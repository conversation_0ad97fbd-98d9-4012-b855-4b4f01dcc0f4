#!/usr/bin/env bash

# 重置工作目录
cd "$(dirname "$0")"

# 在根目录下使用quasar编译打包
echo Y | cordova -v
if [ ! -d "src-cordova/platforms/android" ]
then
  cd src-cordova
  mkdir -p www
  echo "add android platform"
  cordova platform add android@12
  cd -
fi

# 升级为cordova 11以上版本后，必须指定编译结果类型
# cordova build android -- --packageType=apk
# 必须多加"--"，否则"-- --packageType=apk"参数无法传递到cordova工具中
# https://github.com/quasarframework/quasar/issues/9631
quasar build -m cordova -T android -- -- --packageType=apk

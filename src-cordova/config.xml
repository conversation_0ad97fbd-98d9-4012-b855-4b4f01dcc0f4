<?xml version='1.0' encoding='utf-8'?>
<widget id="beifeng.scenic.system" version="2.2.18" xmlns="http://www.w3.org/ns/widgets">
    <name>BF智慧界桩</name>
    <description>Beifeng BF-PMSS01 smart electronic boundary stake system</description>
    <author email="<EMAIL>" href="http://cordova.io">
        Apache Cordova Team
    </author>
    <content src="index.html" />
    <access origin="*" />
    <allow-intent href="http://*/*" />
    <allow-intent href="https://*/*" />
    <allow-intent href="tel:*" />
    <allow-intent href="sms:*" />
    <allow-intent href="mailto:*" />
    <allow-intent href="geo:*" />
    <platform name="android">
        <splash density="land-hdpi" src="res/screen/android/splash-port-hdpi.png" />
        <splash density="land-ldpi" src="res/screen/android/splash-port-ldpi.png" />
        <splash density="land-mdpi" src="res/screen/android/splash-port-mdpi.png" />
        <splash density="land-xhdpi" src="res/screen/android/splash-port-xhdpi.png" />
        <splash density="land-xxhdpi" src="res/screen/android/splash-port-xxhdpi.png" />
        <splash density="land-xxxhdpi" src="res/screen/android/splash-port-xxxhdpi.png" />
        <splash density="port-hdpi" src="res/screen/android/splash-port-hdpi.png" />
        <splash density="port-ldpi" src="res/screen/android/splash-port-ldpi.png" />
        <splash density="port-mdpi" src="res/screen/android/splash-port-mdpi.png" />
        <splash density="port-xhdpi" src="res/screen/android/splash-port-xhdpi.png" />
        <splash density="port-xxhdpi" src="res/screen/android/splash-port-xxhdpi.png" />
        <splash density="port-xxxhdpi" src="res/screen/android/splash-port-xxxhdpi.png" />
        <preference name="AndroidWindowSplashScreenAnimatedIcon" value="res/screen/android/splash-port-xxxhdpi.png" />
        <preference name="android-abi" value="armeabi-v7a,arm64-v8a,x86,x86_64" />
        <hook src="hooks/before_prepare.sh" type="before_prepare" />
        <hook src="hooks/after_prepare.sh" type="after_prepare" />
        <preference name="SplashScreenDelay" value="3000" />
        <preference name="AndroidWindowSplashScreenBackground" value="#ffffff" />
        <preference name="AndroidWindowSplashScreenIconBackgroundColor" value="#4fae53" />
        <allow-intent href="market:*" />
        <preference name="AndroidPersistentFileLocation" value="Compatibility" />
        <preference name="AndroidLaunchMode" value="singleInstance" />
        <icon density="xhdpi" src="res/android/xhdpi.png" />
        <icon density="ldpi" src="res/android/ldpi.png" />
        <icon density="mdpi" src="res/android/mdpi.png" />
        <icon density="hdpi" src="res/android/hdpi.png" />
        <icon density="xhdpi" src="res/android/xhdpi.png" />
        <icon density="xxhdpi" src="res/android/xxhdpi.png" />
        <icon density="xxxhdpi" src="res/android/xxxhdpi.png" />
        <config-file mode="merge" parent="/manifest" target="AndroidManifest.xml" xmlns:android="http://schemas.android.com/apk/res/android">
            <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
            <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
            <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
            <uses-permission android:name="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE" />
            <uses-permission android:name="android.permission.NFC" />
        </config-file>
        <preference name="phonegap-version" value="cli-9.0.0" />
        <plugin name="phonegap-nfc" source="npm" />
    </platform>
    <platform name="ios">
        <splash src="res/screen/ios/Default@2x~ipad~anyany.png" />
        <splash src="res/screen/ios/Default@2x~ipad~comany.png" />
        <splash src="res/screen/ios/Default@2x~iphone~anyany.png" />
        <splash src="res/screen/ios/Default@2x~iphone~comany.png" />
        <splash src="res/screen/ios/Default@2x~iphone~comcom.png" />
        <splash src="res/screen/ios/Default@3x~iphone~anyany.png" />
        <splash src="res/screen/ios/Default@3x~iphone~anycom.png" />
        <splash src="res/screen/ios/Default@3x~iphone~comany.png" />
        <allow-intent href="itms:*" />
        <allow-intent href="itms-apps:*" />
        <icon height="57" src="res/ios/icon.png" width="57" />
        <icon height="114" src="res/ios/<EMAIL>" width="114" />
        <icon height="40" src="res/ios/<EMAIL>" width="40" />
        <icon height="60" src="res/ios/<EMAIL>" width="60" />
        <icon height="29" src="res/ios/icon-29.png" width="29" />
        <icon height="58" src="res/ios/<EMAIL>" width="58" />
        <icon height="87" src="res/ios/<EMAIL>" width="87" />
        <icon height="80" src="res/ios/<EMAIL>" width="80" />
        <icon height="120" src="res/ios/<EMAIL>" width="120" />
        <icon height="180" src="res/ios/<EMAIL>" width="180" />
        <icon height="20" src="res/ios/icon-20.png" width="20" />
        <icon height="40" src="res/ios/icon-40.png" width="40" />
        <icon height="50" src="res/ios/icon-50.png" width="50" />
        <icon height="100" src="res/ios/<EMAIL>" width="100" />
        <icon height="72" src="res/ios/icon-72.png" width="72" />
        <icon height="144" src="res/ios/<EMAIL>" width="144" />
        <icon height="76" src="res/ios/icon-76.png" width="76" />
        <icon height="152" src="res/ios/<EMAIL>" width="152" />
        <icon height="167" src="res/ios/<EMAIL>" width="167" />
        <icon height="1024" src="res/ios/icon-1024.png" width="1024" />
        <icon height="48" src="res/ios/<EMAIL>" width="48" />
        <icon height="55" src="res/ios/<EMAIL>" width="55" />
        <icon height="88" src="res/ios/<EMAIL>" width="88" />
        <icon height="172" src="res/ios/<EMAIL>" width="172" />
        <icon height="196" src="res/ios/<EMAIL>" width="196" />
    </platform>
    <allow-navigation href="about:*" />
    <allow-navigation href="*" />
    <preference name="SplashMaintainAspectRatio" value="true" />
    <preference name="android-minSdkVersion" value="24" />
    <preference name="android-targetSdkVersion" value="34" />
    <preference name="AutoHideSplashScreen" value="true" />
    <preference name="SplashScreenDelay" value="10000" />
</widget>

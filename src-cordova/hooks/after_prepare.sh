#!/usr/bin/env bash

echo "after_prepare hook"

gradle_wrapper_properties="./platforms/android/gradle/wrapper/gradle-wrapper.properties"
if [[ -f "$gradle_wrapper_properties" ]]; then
  # Linux
  sed -i 's|distributionUrl=https://services.gradle.org/distributions|distributionUrl=https\://mirrors.aliyun.com/macports/distfiles/gradle|g' "$gradle_wrapper_properties"
  echo "Gradle wrapper properties updated"
fi

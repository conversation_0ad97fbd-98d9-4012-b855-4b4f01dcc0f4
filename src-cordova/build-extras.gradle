// 加载自定义签名配置文件
def keyStorePropertiesFile = rootProject.file('.release-signing.properties')
def keyStoreProperties = new Properties()
keyStoreProperties.load(new FileInputStream(file(keyStorePropertiesFile)))

// 读取项目的package.json文件数据
def packageJsonFile = rootProject.file('../../../package.json')
def packageJsonContent = new groovy.json.JsonSlurper().parseText(packageJsonFile.text)
//def packageVersionCode = packageJsonContent.versionCode ?: "1"
def packageVersionName = packageJsonContent.version ?:"1.0.0"
def packageAppName = packageJsonContent.appName ?:"bys"
//def packageAppId = packageJsonContent.appId ?:"baiyunmountain.scenic.system"

android {
    defaultConfig {
//        applicationId packageAppId
//        versionCode packageVersionCode.toInteger()
//        versionName packageVersionName

        ndk {
            abiFilters 'x86_64', 'x86', 'arm64-v8a', 'armeabi-v7a'
        }
    }

    // 设置打包格式
    applicationVariants.all { variant ->
        // 只修改release版本的输出名称
        if (variant.buildType.name == 'release') {
            variant.outputs.all { output ->
                def outputFile = output.outputFile
                if (outputFile != null && outputFile.name.endsWith('.apk')) {
                    // 输出apk名称为bys-v1.0.0.apk
                    output.outputFileName = "${packageAppName}-${packageVersionName}.apk"
                }
            }
        }
    }

    // 签名的配置
    signingConfigs {
        release {
            storeFile rootProject.file(keyStoreProperties["storeFile"])
            storePassword keyStoreProperties["storePassword"]
            keyAlias keyStoreProperties["keyAlias"]
            keyPassword keyStoreProperties["keyPassword"]
        }
    }

    buildTypes {
        release {
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.release
            // 开启zipalign优化
            zipAlignEnabled true
            // 混淆
            //minifyEnabled true
            // 移除无用的resource文件，必须在minifyEnabled=true的情况下才能使用
            //shrinkResources true
        }
    }
}

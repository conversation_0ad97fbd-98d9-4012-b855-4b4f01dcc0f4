<?xml version="1.0" encoding="utf-8"?>
<adaptive-icon xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 定义前景图标 -->
    <foreground android:drawable="@mipmap/ic_launcher"/>

    <!-- 定义各个密度下的背景图标 -->
    <background>
        <drawable android:drawable="@mipmap/ic_launcher"/>

        <!-- hdpi -->
        <!-- <drawable android:drawable="@mipmap-hdpi/ic_launcher"/> -->

        <!-- mdpi -->
        <!-- <drawable android:drawable="@mipmap-mdpi/ic_launcher"/> -->

        <!-- xhdpi -->
        <!-- <drawable android:drawable="@mipmap-xhdpi/ic_launcher"/> -->

        <!-- xxhdpi -->
        <!-- <drawable android:drawable="@mipmap-xxhdpi/ic_launcher"/> -->

        <!-- xxxhdpi -->
        <!-- <drawable android:drawable="@mipmap-xxxhdpi/ic_launcher"/> -->

        <!-- 添加更多密度下的背景图标 -->
    </background>
</adaptive-icon>

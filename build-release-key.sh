#生成密钥时，单位、组织、城市等信息默认不填，密码使用.release-signing.properties的storePassword字段
keyStore=.release-key.keystore
keyAlias=release-key

#生成app签名密钥，有效期36500天(100年)
keytool -genkey -v -keystore $keyStore -alias $keyAlias -keyalg RSA -keysize 2048 -validity 36500
#迁移到行业标准格式 PKCS12
keytool -importkeystore -srckeystore $keyStore -destkeystore $keyStore -deststoretype pkcs12
#删除旧文件
rm -rf "${keyStore}.old"

#apk签名命令
# version=$(node -p "require('./package.json').version")
# unsigned_app=dist/cordova-$version/android/apk/release/app-release-unsigned.apk
# jarsigner -verbose -sigalg SHA1withRSA -digestalg SHA1 -keystore $keyStore $unsigned_app $keyAlias
# zipalign -v 4 $unsigned_app path/to/app

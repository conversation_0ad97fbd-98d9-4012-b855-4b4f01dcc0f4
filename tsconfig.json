{"extends": "@quasar/app-vite/tsconfig-preset", "compilerOptions": {"baseUrl": ".", "noImplicitAny": false, "types": ["quasar", "node", "j<PERSON>y"], "paths": {"@app/*": ["./*"], "@src/*": ["./src/*"], "@public/*": ["./public/*"], "@assets/*": ["./src/assets/*"], "@boot/*": ["./src/boot/*"], "@components/*": ["./src/components/*"], "@config/*": ["./src/config/*"], "@css/*": ["./src/css/*"], "@layouts/*": ["./src/layouts/*"], "@localCdn/*": ["./src/localCdn/*"], "@pages/*": ["./src/pages/*"], "@router/*": ["./src/router/*"], "@services/*": ["./src/services/*"], "@store/*": ["./src/store/*"], "@utils/*": ["./src/utils/*"], "@ygen/*": ["./src/ygen/*"]}}, "exclude": ["node_modules", "src/components/fancytree/plain-scrollbar.js", "src/components/fancytree/mobile-scroller.js", "src/localCdn/**/*", "./quasar.config.*.temporary.compiled*"], "include": ["src/**/*.js", "src/**/*.ts", "src/**/*.tsx", "src/**/*.vue"]}
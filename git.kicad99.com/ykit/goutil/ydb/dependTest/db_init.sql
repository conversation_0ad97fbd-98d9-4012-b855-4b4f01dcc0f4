--add default org root
INSERT INTO db_org
(
  rid,
  org_self_id,
  org_short_name,
  org_full_name,
  note,
  dmr_id,
  parent_org_id
)
VALUES (
  '00000000-0000-0000-0000-000000000000',
  'root',
  'root',
  'root',
  'default org root',
  '00000000',
  '00000000-0000-0000-0000-000000000000'
) ON CONFLICT  DO NOTHING;
--add default org image
INSERT INTO  db_image
(
  rid,
  org_id,
  file_name,
  file_content
)
VALUES (
  '11111111-1111-1111-1111-111111111111',
  '00000000-0000-0000-0000-000000000000',
  'default_org.png',
  'data:image/png;base64,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*******************************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'
  
) ON CONFLICT  DO NOTHING;


--add default user image
INSERT INTO  db_image
(
  rid,
  org_id,
  file_name,
  file_content
)
VALUES (
  '22222222-2222-2222-2222-222222222222',
  '00000000-0000-0000-0000-000000000000',
  'default_user.png',
  'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAAFH0lEQVR4nO2d25HiMBBFN4QJwSEQAiEQgkMghM2AEAiBCKgbAiEQgkOY/XBraGvMjGFttcDnVOlna7C07quWuvXwnz8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAr4SkD0lNKtHtgQWRtDmfz3/Vc5HUSfocKVdJJ0kH+/smuu3wJJI+zIiXO8aeWiSpjf7/wANI2lpv9obsTAwnSQdJe0mt/e3OSus8RS6ECx6hcmxcP4z04L2kjyee12ZC6iRtlmg7/Cdm/GPWY2cxlgnIi6CZ47kwI1nP1zM9/pfnN+75xzmfDf+JjeNfxpnb+K6e1tXTLFEHPIi5/jTTvyxlfFdXCiFPS9UDD2Cz99QrtwXqO5asD37BhWxFeqR5gRQZXEvUCXcwY6TeuCtYr59ztKXqhYzM/TeF607zjkPJesFhWbsQV+zCTpWuGww3IbuUrtuJr3jdYKjP64ckZlx2EAFE4SKA4uMwAqiAJIDz+fw3oG4EEA0CWDmVCKArXTcYwQLwOQhCwdL4lGyQAHxKmIxgSezln2p4+WQEA5C08e5XCy4BT2hLWC5itfgJWKTxrS1HBFAY3XbmhC/HOg/AEFAKJ4DwECxyIrpaXAgWKoBsLtJEtmVVaLgRpIlqByuCgbgYfB9Uf2OTUNx/BG72HTIM+PqjI5FVkg0DbeG6v04K0fsDyXphU6jOrW5nA0KTUKsn8wJFFmTM6J82B8H40Wi4Krfo1nAVPoQCE3HZuEUnhGIncJ2owNKwD/tKTzphAj49vMSEkLCvcvyEcG4vYCnf1PtZ9KmVpZJDWe/fzvlsmBEtcH5f7iAoSZ8XQMNbPP4rLMxcPxdCvAoabtbcPvkMf/MIF0O9ElnC5lMPrhhaz/ciahdqKsyJ9dqdvl8S+ameZsLvD/p+lewFEVSMM7y/FrYzo+diOKlfzduoT+58SNra5o7ryO8RQq3odh9wbmTJXRKp4UWPU8qXtzChHBFCRagPzXJXnXrs3dtBTQh5r570e4QQTHLV+n77d2dieOhaWDPo1spGE9O79rcnhFCIH9x8Z/8ekpc34eRCuCKEmbCeNjYjl568/XsJ7gihE2sGz6HxUKyz8bfaq9rvzBE+EcIDqE/j5oavprdP4SchiGzifczQ3asaPkd9jgGPMAUN4/S3+zSLvuchZvuYxcujFW2z1vfP2RS737hKbMInrWibdSb4da8yZq5xG92eUmj4+Zl1HizVcJft6iZGGi5ZhxxwDcXNkFfh+sdQofMM1SG3d2/N++yyoaCNbk8xFPihh9rQbXEr/L6jYjjXt/qNllrbp+g03Lq97jjYUPBtJ0XJQr8muj014HIh7+8RFfiZl1pxGcL3jwac2lcX+99jVZNiN/630W2pBQ1vOtlGt2cxNLxYkQmgocALr4qi4Rc3m+j21ITzjO8bCWQRQBvdnhqw3v+VC3jrzGjmAVI5vPW4N4JuW9zHNr220e1bFFN7vrc/lYuFift3EYUZeyNpZ9vYLyNGT4dSXnob3EOoXwjZ/yCGVCTpaC+vtZ7TRLc/Z8TQumNsb/ST/Z/WYfR72MvzL+4nQfhytZd4SiJxQtnpduonHQRN5WNCSb/b2fP26l32wbyUN/A9I+flYr+ffBJptZihkijSC58qjMjSmaFPZux0EhmDz0EShqTW9fgkEKn3ClN75BRjpnJ1hvXGTcMSRq4R9e48uf5NVkaHheg2AwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPzKP5GJuJDSXhYpAAAAAElFTkSuQmCC'
  
) ON CONFLICT  DO NOTHING;

--add default user bfdx
INSERT INTO db_user
(
  rid,
  org_id,
  self_id,
  user_name,
  user_login_name,
  user_login_pass
)
VALUES (
  '11111111-1111-1111-1111-111111111111',
  '00000000-0000-0000-0000-000000000000',
  'bfdx',
  'bfdx',
  'bfdx',
  '8llBVEV0SnBKp8xRE950ApIQSZnFcU1pXldWp7p3Xno='
) ON CONFLICT  DO NOTHING;
--add priviledge of bfdx
INSERT INTO db_user_privelege
(
  rid,
  user_rid,
  user_org,
  include_children
)
VALUES (
  '11111111-1111-1111-1111-111111111111',
  '11111111-1111-1111-1111-111111111111',
  '00000000-0000-0000-0000-000000000000',
  1
) ON CONFLICT  DO NOTHING;

--add default user ttt
INSERT INTO db_user
(
  rid,
  org_id,
  self_id,
  user_name,
  user_login_name,
  user_login_pass
)
VALUES (
  '22222222-2222-2222-2222-222222222222',
  '00000000-0000-0000-0000-000000000000',
  'ttt',
  'ttt',
  'ttt',
  'UwhpDj0xMGNzZw8sXYZOLGBHmH/q9Wxf7aB6/EbT43Q='
) ON CONFLICT  DO NOTHING;
--add priviledge of ttt
INSERT INTO db_user_privelege
(
  rid,
  user_rid,
  user_org,
  include_children
)
VALUES (
  '22222222-2222-2222-2222-222222222222',
  '22222222-2222-2222-2222-222222222222',
  '00000000-0000-0000-0000-000000000000',
  1
) ON CONFLICT  DO NOTHING;
--add default user yj
INSERT INTO db_user
(
  rid,
  org_id,
  self_id,
  user_name,
  user_login_name,
  user_login_pass
)
VALUES (
  '33333333-3333-3333-3333-333333333333',
  '00000000-0000-0000-0000-000000000000',
  'yj',
  'yj',
  'yj',
  'IjVoSv6bifSknho9cDRjHjFEt77xL05/eNhIoCw/g2M='
) ON CONFLICT  DO NOTHING;
--add priviledge of yj
INSERT INTO db_user_privelege
(
  rid,
  user_rid,
  user_org,
  include_children
)
VALUES (
  '33333333-3333-3333-3333-333333333333',
  '33333333-3333-3333-3333-333333333333',
  '00000000-0000-0000-0000-000000000000',
  1
) ON CONFLICT  DO NOTHING;
--default saler_id 0
INSERT INTO   db_sys_config
(
  rid,
  update_at,
  conf_key,
  conf_value
)
VALUES (
  '33333333-3333-3333-3333-333333333333',
  now_utc(),
  'saler_id',
  '0'
) ON CONFLICT  DO NOTHING;

--default root zone  0
INSERT INTO   db_device_channel_zone
(
  rid,
  zone_level,
  zone_no,
  zone_title

)
VALUES (
  '00000000-0000-0000-0000-000000000000',
  0,
  0,
  'root'
) ON CONFLICT  DO NOTHING;

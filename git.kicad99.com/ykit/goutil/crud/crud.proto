syntax = "proto3";

package crud;

option go_package = "git.kicad99.com/ykit/goutil/crud";

// CRUD DML结果
message DMLResult {
  //影响的行数，如果成功>0,否则=0
  int32 AffectedRow = 1;
  //错误信息，如果有的话
  string errInfo = 2;
  //附加信息,json格式
  string Note = 3;
}

//简单CRUD里面用到的条件和结果字段列表
message DMLParam {
  //条件字段名
  repeated string KeyColumn = 1;
  //结果字段名
  repeated string ResultColumn = 2;
  //条件值
  repeated string KeyValue = 3;
}
// sql where额外条件项
message WhereItem {
  // fieldname
  string Field = 5;
  // field compare operator
  string FieldCompareOperator = 6;
  // Field value
  string FieldValue = 7;
}
//查询条件
message QueryParam {
  //想要的结果字段名，不填写为全要
  repeated string ResultColumn = 1;
  // Query时需要处理时区的字段,单数为字段名，双数为需要加减的的分钟数
  //需要此功能必须把所有的结果列名填写在ResultColumn，否则无法处理
  // TimeColumn = ['ActionTime','480'，‘RecvTime','-60']
  repeated string TimeColumn = 2;
  // where额外条件项,只支持 and
  repeated WhereItem Where = 5;
  //分页，需要的数据，如果Limit=0 and Offset=0,则视为不需要分页
  uint32 Limit = 6;
  //分页，从什么位置开始，如果Limit=0 and
  //Offset!=0,为从Offset的位置开始的所有数据
  uint32 Offset = 7;
  //排序条件，每项为sql的order by 条件
  // select * from a order by col1 asc,col2 desc
  // OrderBy=["col1 asc","col2 desc"]
  repeated string OrderBy = 8;
  // QueryBatch调用时,指定一次返回的数据量，不指定时后端按每次一个返回
  uint32 Batch = 9;
}
//取得用户有权限的数据
message PrivilegeParam {
  //系统名称
  string System = 1;
  // SessionID
  string SessionID = 3;

  //查询条件
  QueryParam QueryCondition = 5;
}
// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: crud.proto

package crud

import (
	fmt "fmt"
	proto "github.com/gogo/protobuf/proto"
	io "io"
	math "math"
	math_bits "math/bits"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

// CRUD DML结果
type DMLResult struct {
	//影响的行数，如果成功>0,否则=0
	AffectedRow int32 `protobuf:"varint,1,opt,name=AffectedRow,proto3" json:"AffectedRow,omitempty"`
	//错误信息，如果有的话
	ErrInfo string `protobuf:"bytes,2,opt,name=errInfo,proto3" json:"errInfo,omitempty"`
	//附加信息,json格式
	Note string `protobuf:"bytes,3,opt,name=Note,proto3" json:"Note,omitempty"`
}

func (m *DMLResult) Reset()         { *m = DMLResult{} }
func (m *DMLResult) String() string { return proto.CompactTextString(m) }
func (*DMLResult) ProtoMessage()    {}
func (*DMLResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_478bbe1b22b2e995, []int{0}
}
func (m *DMLResult) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DMLResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DMLResult.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DMLResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DMLResult.Merge(m, src)
}
func (m *DMLResult) XXX_Size() int {
	return m.Size()
}
func (m *DMLResult) XXX_DiscardUnknown() {
	xxx_messageInfo_DMLResult.DiscardUnknown(m)
}

var xxx_messageInfo_DMLResult proto.InternalMessageInfo

func (m *DMLResult) GetAffectedRow() int32 {
	if m != nil {
		return m.AffectedRow
	}
	return 0
}

func (m *DMLResult) GetErrInfo() string {
	if m != nil {
		return m.ErrInfo
	}
	return ""
}

func (m *DMLResult) GetNote() string {
	if m != nil {
		return m.Note
	}
	return ""
}

//简单CRUD里面用到的条件和结果字段列表
type DMLParam struct {
	//条件字段名
	KeyColumn []string `protobuf:"bytes,1,rep,name=KeyColumn,proto3" json:"KeyColumn,omitempty"`
	//结果字段名
	ResultColumn []string `protobuf:"bytes,2,rep,name=ResultColumn,proto3" json:"ResultColumn,omitempty"`
	//条件值
	KeyValue []string `protobuf:"bytes,3,rep,name=KeyValue,proto3" json:"KeyValue,omitempty"`
}

func (m *DMLParam) Reset()         { *m = DMLParam{} }
func (m *DMLParam) String() string { return proto.CompactTextString(m) }
func (*DMLParam) ProtoMessage()    {}
func (*DMLParam) Descriptor() ([]byte, []int) {
	return fileDescriptor_478bbe1b22b2e995, []int{1}
}
func (m *DMLParam) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DMLParam) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DMLParam.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DMLParam) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DMLParam.Merge(m, src)
}
func (m *DMLParam) XXX_Size() int {
	return m.Size()
}
func (m *DMLParam) XXX_DiscardUnknown() {
	xxx_messageInfo_DMLParam.DiscardUnknown(m)
}

var xxx_messageInfo_DMLParam proto.InternalMessageInfo

func (m *DMLParam) GetKeyColumn() []string {
	if m != nil {
		return m.KeyColumn
	}
	return nil
}

func (m *DMLParam) GetResultColumn() []string {
	if m != nil {
		return m.ResultColumn
	}
	return nil
}

func (m *DMLParam) GetKeyValue() []string {
	if m != nil {
		return m.KeyValue
	}
	return nil
}

// sql where额外条件项
type WhereItem struct {
	// fieldname
	Field string `protobuf:"bytes,5,opt,name=Field,proto3" json:"Field,omitempty"`
	// field compare operator
	FieldCompareOperator string `protobuf:"bytes,6,opt,name=FieldCompareOperator,proto3" json:"FieldCompareOperator,omitempty"`
	// Field value
	FieldValue string `protobuf:"bytes,7,opt,name=FieldValue,proto3" json:"FieldValue,omitempty"`
}

func (m *WhereItem) Reset()         { *m = WhereItem{} }
func (m *WhereItem) String() string { return proto.CompactTextString(m) }
func (*WhereItem) ProtoMessage()    {}
func (*WhereItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_478bbe1b22b2e995, []int{2}
}
func (m *WhereItem) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *WhereItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_WhereItem.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *WhereItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WhereItem.Merge(m, src)
}
func (m *WhereItem) XXX_Size() int {
	return m.Size()
}
func (m *WhereItem) XXX_DiscardUnknown() {
	xxx_messageInfo_WhereItem.DiscardUnknown(m)
}

var xxx_messageInfo_WhereItem proto.InternalMessageInfo

func (m *WhereItem) GetField() string {
	if m != nil {
		return m.Field
	}
	return ""
}

func (m *WhereItem) GetFieldCompareOperator() string {
	if m != nil {
		return m.FieldCompareOperator
	}
	return ""
}

func (m *WhereItem) GetFieldValue() string {
	if m != nil {
		return m.FieldValue
	}
	return ""
}

//查询条件
type QueryParam struct {
	//想要的结果字段名，不填写为全要
	ResultColumn []string `protobuf:"bytes,1,rep,name=ResultColumn,proto3" json:"ResultColumn,omitempty"`
	// Query时需要处理时区的字段,单数为字段名，双数为需要加减的的分钟数
	//需要此功能必须把所有的结果列名填写在ResultColumn，否则无法处理
	// TimeColumn = ['ActionTime','480'，‘RecvTime','-60']
	TimeColumn []string `protobuf:"bytes,2,rep,name=TimeColumn,proto3" json:"TimeColumn,omitempty"`
	// where额外条件项,只支持 and
	Where []*WhereItem `protobuf:"bytes,5,rep,name=Where,proto3" json:"Where,omitempty"`
	//分页，需要的数据，如果Limit=0 and Offset=0,则视为不需要分页
	Limit uint32 `protobuf:"varint,6,opt,name=Limit,proto3" json:"Limit,omitempty"`
	//分页，从什么位置开始，如果Limit=0 and
	//Offset!=0,为从Offset的位置开始的所有数据
	Offset uint32 `protobuf:"varint,7,opt,name=Offset,proto3" json:"Offset,omitempty"`
	//排序条件，每项为sql的order by 条件
	// select * from a order by col1 asc,col2 desc
	// OrderBy=["col1 asc","col2 desc"]
	OrderBy []string `protobuf:"bytes,8,rep,name=OrderBy,proto3" json:"OrderBy,omitempty"`
	// QueryBatch调用时,指定一次返回的数据量，不指定时后端按每次一个返回
	Batch uint32 `protobuf:"varint,9,opt,name=Batch,proto3" json:"Batch,omitempty"`
}

func (m *QueryParam) Reset()         { *m = QueryParam{} }
func (m *QueryParam) String() string { return proto.CompactTextString(m) }
func (*QueryParam) ProtoMessage()    {}
func (*QueryParam) Descriptor() ([]byte, []int) {
	return fileDescriptor_478bbe1b22b2e995, []int{3}
}
func (m *QueryParam) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *QueryParam) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_QueryParam.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *QueryParam) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryParam.Merge(m, src)
}
func (m *QueryParam) XXX_Size() int {
	return m.Size()
}
func (m *QueryParam) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryParam.DiscardUnknown(m)
}

var xxx_messageInfo_QueryParam proto.InternalMessageInfo

func (m *QueryParam) GetResultColumn() []string {
	if m != nil {
		return m.ResultColumn
	}
	return nil
}

func (m *QueryParam) GetTimeColumn() []string {
	if m != nil {
		return m.TimeColumn
	}
	return nil
}

func (m *QueryParam) GetWhere() []*WhereItem {
	if m != nil {
		return m.Where
	}
	return nil
}

func (m *QueryParam) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *QueryParam) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *QueryParam) GetOrderBy() []string {
	if m != nil {
		return m.OrderBy
	}
	return nil
}

func (m *QueryParam) GetBatch() uint32 {
	if m != nil {
		return m.Batch
	}
	return 0
}

//取得用户有权限的数据
type PrivilegeParam struct {
	//系统名称
	System string `protobuf:"bytes,1,opt,name=System,proto3" json:"System,omitempty"`
	// SessionID
	SessionID string `protobuf:"bytes,3,opt,name=SessionID,proto3" json:"SessionID,omitempty"`
	//查询条件
	QueryCondition *QueryParam `protobuf:"bytes,5,opt,name=QueryCondition,proto3" json:"QueryCondition,omitempty"`
}

func (m *PrivilegeParam) Reset()         { *m = PrivilegeParam{} }
func (m *PrivilegeParam) String() string { return proto.CompactTextString(m) }
func (*PrivilegeParam) ProtoMessage()    {}
func (*PrivilegeParam) Descriptor() ([]byte, []int) {
	return fileDescriptor_478bbe1b22b2e995, []int{4}
}
func (m *PrivilegeParam) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *PrivilegeParam) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_PrivilegeParam.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *PrivilegeParam) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PrivilegeParam.Merge(m, src)
}
func (m *PrivilegeParam) XXX_Size() int {
	return m.Size()
}
func (m *PrivilegeParam) XXX_DiscardUnknown() {
	xxx_messageInfo_PrivilegeParam.DiscardUnknown(m)
}

var xxx_messageInfo_PrivilegeParam proto.InternalMessageInfo

func (m *PrivilegeParam) GetSystem() string {
	if m != nil {
		return m.System
	}
	return ""
}

func (m *PrivilegeParam) GetSessionID() string {
	if m != nil {
		return m.SessionID
	}
	return ""
}

func (m *PrivilegeParam) GetQueryCondition() *QueryParam {
	if m != nil {
		return m.QueryCondition
	}
	return nil
}

func init() {
	proto.RegisterType((*DMLResult)(nil), "crud.DMLResult")
	proto.RegisterType((*DMLParam)(nil), "crud.DMLParam")
	proto.RegisterType((*WhereItem)(nil), "crud.WhereItem")
	proto.RegisterType((*QueryParam)(nil), "crud.QueryParam")
	proto.RegisterType((*PrivilegeParam)(nil), "crud.PrivilegeParam")
}

func init() { proto.RegisterFile("crud.proto", fileDescriptor_478bbe1b22b2e995) }

var fileDescriptor_478bbe1b22b2e995 = []byte{
	// 457 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x6c, 0x52, 0xc1, 0x6a, 0xdb, 0x40,
	0x10, 0xf5, 0xc6, 0xb1, 0x63, 0x8d, 0x9b, 0xb4, 0x2c, 0x21, 0x2c, 0xa5, 0x08, 0x21, 0x28, 0xf8,
	0x64, 0x83, 0x7b, 0xc9, 0xb5, 0xb6, 0x29, 0x98, 0x38, 0x75, 0xba, 0x29, 0x2d, 0xb4, 0x27, 0x55,
	0x1a, 0xd9, 0x4b, 0x25, 0xad, 0x59, 0xad, 0xda, 0xea, 0xd6, 0x4f, 0xe8, 0x67, 0xf5, 0x98, 0x4b,
	0xa1, 0xc7, 0x62, 0xff, 0x48, 0xd1, 0xc8, 0x89, 0x9d, 0x90, 0xdb, 0xbc, 0x37, 0x8f, 0x99, 0x79,
	0xfb, 0x16, 0x20, 0x34, 0x45, 0xd4, 0x5f, 0x19, 0x6d, 0x35, 0x3f, 0xac, 0x6a, 0xff, 0x33, 0x38,
	0x93, 0xcb, 0x99, 0xc4, 0xbc, 0x48, 0x2c, 0xf7, 0xa0, 0xfb, 0x3a, 0x8e, 0x31, 0xb4, 0x18, 0x49,
	0xfd, 0x5d, 0x30, 0x8f, 0xf5, 0x5a, 0x72, 0x9f, 0xe2, 0x02, 0x8e, 0xd0, 0x98, 0x69, 0x16, 0x6b,
	0x71, 0xe0, 0xb1, 0x9e, 0x23, 0x6f, 0x21, 0xe7, 0x70, 0xf8, 0x56, 0x5b, 0x14, 0x4d, 0xa2, 0xa9,
	0xf6, 0x97, 0xd0, 0x99, 0x5c, 0xce, 0xae, 0x02, 0x13, 0xa4, 0xfc, 0x05, 0x38, 0x17, 0x58, 0x8e,
	0x75, 0x52, 0xa4, 0x99, 0x60, 0x5e, 0xb3, 0xe7, 0xc8, 0x1d, 0xc1, 0x7d, 0x78, 0x52, 0xdf, 0xb0,
	0x15, 0x1c, 0x90, 0xe0, 0x1e, 0xc7, 0x9f, 0x43, 0xe7, 0x02, 0xcb, 0x0f, 0x41, 0x52, 0x54, 0x5b,
	0xaa, 0xfe, 0x1d, 0xf6, 0x0b, 0x70, 0x3e, 0x2e, 0xd1, 0xe0, 0xd4, 0x62, 0xca, 0x4f, 0xa1, 0xf5,
	0x46, 0x61, 0x12, 0x89, 0x16, 0xdd, 0x52, 0x03, 0x3e, 0x84, 0x53, 0x2a, 0xc6, 0x3a, 0x5d, 0x05,
	0x06, 0xe7, 0x2b, 0x34, 0x81, 0xd5, 0x46, 0xb4, 0x49, 0xf4, 0x68, 0x8f, 0xbb, 0x00, 0xc4, 0xd7,
	0x4b, 0x8f, 0x48, 0xb9, 0xc7, 0xf8, 0x7f, 0x18, 0xc0, 0xbb, 0x02, 0x4d, 0x59, 0x7b, 0x7c, 0xe8,
	0x82, 0x3d, 0xe2, 0xc2, 0x05, 0x78, 0xaf, 0x52, 0xbc, 0xe7, 0x73, 0x8f, 0xe1, 0x2f, 0xa1, 0x45,
	0x4e, 0x44, 0xcb, 0x6b, 0xf6, 0xba, 0xc3, 0xa7, 0x7d, 0x8a, 0xec, 0xce, 0x9c, 0xac, 0xbb, 0x95,
	0xc7, 0x99, 0x4a, 0x95, 0xa5, 0xf3, 0x8f, 0x65, 0x0d, 0xf8, 0x19, 0xb4, 0xe7, 0x71, 0x9c, 0xa3,
	0xa5, 0x5b, 0x8f, 0xe5, 0x16, 0x55, 0xb1, 0xcd, 0x4d, 0x84, 0x66, 0x54, 0x8a, 0x0e, 0x6d, 0xbc,
	0x85, 0xd5, 0x9c, 0x51, 0x60, 0xc3, 0xa5, 0x70, 0xea, 0x39, 0x04, 0xfc, 0x9f, 0x0c, 0x4e, 0xae,
	0x8c, 0xfa, 0xa6, 0x12, 0x5c, 0x60, 0xed, 0xed, 0x0c, 0xda, 0xd7, 0x65, 0x6e, 0x31, 0xa5, 0x6f,
	0xe1, 0xc8, 0x2d, 0xaa, 0x72, 0xbd, 0xc6, 0x3c, 0x57, 0x3a, 0x9b, 0x4e, 0xb6, 0xe1, 0xef, 0x08,
	0x7e, 0x0e, 0x27, 0xf4, 0x3e, 0x63, 0x9d, 0x45, 0xca, 0x2a, 0x9d, 0x51, 0x26, 0xdd, 0xe1, 0xb3,
	0xda, 0xd6, 0xee, 0xed, 0xe4, 0x03, 0xdd, 0xe8, 0xfc, 0xf7, 0xda, 0x65, 0x37, 0x6b, 0x97, 0xfd,
	0x5b, 0xbb, 0xec, 0xd7, 0xc6, 0x6d, 0xdc, 0x6c, 0xdc, 0xc6, 0xdf, 0x8d, 0xdb, 0xf8, 0xe4, 0x2e,
	0x94, 0xed, 0x87, 0xe1, 0x32, 0xfa, 0xd1, 0x0f, 0x75, 0x3a, 0x28, 0xbf, 0x2a, 0x3b, 0x58, 0xe8,
	0xc2, 0xaa, 0x64, 0x50, 0x0d, 0xfd, 0xd2, 0xa6, 0xff, 0xfd, 0xea, 0x7f, 0x00, 0x00, 0x00, 0xff,
	0xff, 0xdb, 0x73, 0x3d, 0x2f, 0xed, 0x02, 0x00, 0x00,
}

func (m *DMLResult) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DMLResult) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DMLResult) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Note) > 0 {
		i -= len(m.Note)
		copy(dAtA[i:], m.Note)
		i = encodeVarintCrud(dAtA, i, uint64(len(m.Note)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.ErrInfo) > 0 {
		i -= len(m.ErrInfo)
		copy(dAtA[i:], m.ErrInfo)
		i = encodeVarintCrud(dAtA, i, uint64(len(m.ErrInfo)))
		i--
		dAtA[i] = 0x12
	}
	if m.AffectedRow != 0 {
		i = encodeVarintCrud(dAtA, i, uint64(m.AffectedRow))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *DMLParam) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DMLParam) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DMLParam) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.KeyValue) > 0 {
		for iNdEx := len(m.KeyValue) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.KeyValue[iNdEx])
			copy(dAtA[i:], m.KeyValue[iNdEx])
			i = encodeVarintCrud(dAtA, i, uint64(len(m.KeyValue[iNdEx])))
			i--
			dAtA[i] = 0x1a
		}
	}
	if len(m.ResultColumn) > 0 {
		for iNdEx := len(m.ResultColumn) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.ResultColumn[iNdEx])
			copy(dAtA[i:], m.ResultColumn[iNdEx])
			i = encodeVarintCrud(dAtA, i, uint64(len(m.ResultColumn[iNdEx])))
			i--
			dAtA[i] = 0x12
		}
	}
	if len(m.KeyColumn) > 0 {
		for iNdEx := len(m.KeyColumn) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.KeyColumn[iNdEx])
			copy(dAtA[i:], m.KeyColumn[iNdEx])
			i = encodeVarintCrud(dAtA, i, uint64(len(m.KeyColumn[iNdEx])))
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *WhereItem) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *WhereItem) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *WhereItem) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.FieldValue) > 0 {
		i -= len(m.FieldValue)
		copy(dAtA[i:], m.FieldValue)
		i = encodeVarintCrud(dAtA, i, uint64(len(m.FieldValue)))
		i--
		dAtA[i] = 0x3a
	}
	if len(m.FieldCompareOperator) > 0 {
		i -= len(m.FieldCompareOperator)
		copy(dAtA[i:], m.FieldCompareOperator)
		i = encodeVarintCrud(dAtA, i, uint64(len(m.FieldCompareOperator)))
		i--
		dAtA[i] = 0x32
	}
	if len(m.Field) > 0 {
		i -= len(m.Field)
		copy(dAtA[i:], m.Field)
		i = encodeVarintCrud(dAtA, i, uint64(len(m.Field)))
		i--
		dAtA[i] = 0x2a
	}
	return len(dAtA) - i, nil
}

func (m *QueryParam) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *QueryParam) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *QueryParam) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Batch != 0 {
		i = encodeVarintCrud(dAtA, i, uint64(m.Batch))
		i--
		dAtA[i] = 0x48
	}
	if len(m.OrderBy) > 0 {
		for iNdEx := len(m.OrderBy) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.OrderBy[iNdEx])
			copy(dAtA[i:], m.OrderBy[iNdEx])
			i = encodeVarintCrud(dAtA, i, uint64(len(m.OrderBy[iNdEx])))
			i--
			dAtA[i] = 0x42
		}
	}
	if m.Offset != 0 {
		i = encodeVarintCrud(dAtA, i, uint64(m.Offset))
		i--
		dAtA[i] = 0x38
	}
	if m.Limit != 0 {
		i = encodeVarintCrud(dAtA, i, uint64(m.Limit))
		i--
		dAtA[i] = 0x30
	}
	if len(m.Where) > 0 {
		for iNdEx := len(m.Where) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Where[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintCrud(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x2a
		}
	}
	if len(m.TimeColumn) > 0 {
		for iNdEx := len(m.TimeColumn) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.TimeColumn[iNdEx])
			copy(dAtA[i:], m.TimeColumn[iNdEx])
			i = encodeVarintCrud(dAtA, i, uint64(len(m.TimeColumn[iNdEx])))
			i--
			dAtA[i] = 0x12
		}
	}
	if len(m.ResultColumn) > 0 {
		for iNdEx := len(m.ResultColumn) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.ResultColumn[iNdEx])
			copy(dAtA[i:], m.ResultColumn[iNdEx])
			i = encodeVarintCrud(dAtA, i, uint64(len(m.ResultColumn[iNdEx])))
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *PrivilegeParam) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PrivilegeParam) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *PrivilegeParam) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.QueryCondition != nil {
		{
			size, err := m.QueryCondition.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintCrud(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x2a
	}
	if len(m.SessionID) > 0 {
		i -= len(m.SessionID)
		copy(dAtA[i:], m.SessionID)
		i = encodeVarintCrud(dAtA, i, uint64(len(m.SessionID)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.System) > 0 {
		i -= len(m.System)
		copy(dAtA[i:], m.System)
		i = encodeVarintCrud(dAtA, i, uint64(len(m.System)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func encodeVarintCrud(dAtA []byte, offset int, v uint64) int {
	offset -= sovCrud(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *DMLResult) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.AffectedRow != 0 {
		n += 1 + sovCrud(uint64(m.AffectedRow))
	}
	l = len(m.ErrInfo)
	if l > 0 {
		n += 1 + l + sovCrud(uint64(l))
	}
	l = len(m.Note)
	if l > 0 {
		n += 1 + l + sovCrud(uint64(l))
	}
	return n
}

func (m *DMLParam) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.KeyColumn) > 0 {
		for _, s := range m.KeyColumn {
			l = len(s)
			n += 1 + l + sovCrud(uint64(l))
		}
	}
	if len(m.ResultColumn) > 0 {
		for _, s := range m.ResultColumn {
			l = len(s)
			n += 1 + l + sovCrud(uint64(l))
		}
	}
	if len(m.KeyValue) > 0 {
		for _, s := range m.KeyValue {
			l = len(s)
			n += 1 + l + sovCrud(uint64(l))
		}
	}
	return n
}

func (m *WhereItem) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Field)
	if l > 0 {
		n += 1 + l + sovCrud(uint64(l))
	}
	l = len(m.FieldCompareOperator)
	if l > 0 {
		n += 1 + l + sovCrud(uint64(l))
	}
	l = len(m.FieldValue)
	if l > 0 {
		n += 1 + l + sovCrud(uint64(l))
	}
	return n
}

func (m *QueryParam) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.ResultColumn) > 0 {
		for _, s := range m.ResultColumn {
			l = len(s)
			n += 1 + l + sovCrud(uint64(l))
		}
	}
	if len(m.TimeColumn) > 0 {
		for _, s := range m.TimeColumn {
			l = len(s)
			n += 1 + l + sovCrud(uint64(l))
		}
	}
	if len(m.Where) > 0 {
		for _, e := range m.Where {
			l = e.Size()
			n += 1 + l + sovCrud(uint64(l))
		}
	}
	if m.Limit != 0 {
		n += 1 + sovCrud(uint64(m.Limit))
	}
	if m.Offset != 0 {
		n += 1 + sovCrud(uint64(m.Offset))
	}
	if len(m.OrderBy) > 0 {
		for _, s := range m.OrderBy {
			l = len(s)
			n += 1 + l + sovCrud(uint64(l))
		}
	}
	if m.Batch != 0 {
		n += 1 + sovCrud(uint64(m.Batch))
	}
	return n
}

func (m *PrivilegeParam) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.System)
	if l > 0 {
		n += 1 + l + sovCrud(uint64(l))
	}
	l = len(m.SessionID)
	if l > 0 {
		n += 1 + l + sovCrud(uint64(l))
	}
	if m.QueryCondition != nil {
		l = m.QueryCondition.Size()
		n += 1 + l + sovCrud(uint64(l))
	}
	return n
}

func sovCrud(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozCrud(x uint64) (n int) {
	return sovCrud(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *DMLResult) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCrud
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DMLResult: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DMLResult: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field AffectedRow", wireType)
			}
			m.AffectedRow = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCrud
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AffectedRow |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ErrInfo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCrud
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCrud
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthCrud
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ErrInfo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Note", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCrud
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCrud
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthCrud
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Note = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCrud(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCrud
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthCrud
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DMLParam) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCrud
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DMLParam: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DMLParam: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field KeyColumn", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCrud
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCrud
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthCrud
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.KeyColumn = append(m.KeyColumn, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ResultColumn", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCrud
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCrud
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthCrud
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ResultColumn = append(m.ResultColumn, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field KeyValue", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCrud
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCrud
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthCrud
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.KeyValue = append(m.KeyValue, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCrud(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCrud
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthCrud
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *WhereItem) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCrud
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: WhereItem: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: WhereItem: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Field", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCrud
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCrud
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthCrud
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Field = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field FieldCompareOperator", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCrud
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCrud
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthCrud
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.FieldCompareOperator = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field FieldValue", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCrud
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCrud
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthCrud
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.FieldValue = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCrud(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCrud
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthCrud
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *QueryParam) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCrud
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: QueryParam: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: QueryParam: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ResultColumn", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCrud
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCrud
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthCrud
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ResultColumn = append(m.ResultColumn, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TimeColumn", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCrud
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCrud
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthCrud
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TimeColumn = append(m.TimeColumn, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Where", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCrud
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCrud
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCrud
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Where = append(m.Where, &WhereItem{})
			if err := m.Where[len(m.Where)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Limit", wireType)
			}
			m.Limit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCrud
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Limit |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Offset", wireType)
			}
			m.Offset = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCrud
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Offset |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OrderBy", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCrud
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCrud
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthCrud
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OrderBy = append(m.OrderBy, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 9:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Batch", wireType)
			}
			m.Batch = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCrud
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Batch |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipCrud(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCrud
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthCrud
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *PrivilegeParam) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCrud
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: PrivilegeParam: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: PrivilegeParam: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field System", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCrud
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCrud
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthCrud
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.System = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SessionID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCrud
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCrud
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthCrud
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SessionID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field QueryCondition", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCrud
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCrud
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCrud
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.QueryCondition == nil {
				m.QueryCondition = &QueryParam{}
			}
			if err := m.QueryCondition.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCrud(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCrud
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthCrud
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipCrud(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowCrud
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowCrud
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowCrud
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthCrud
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupCrud
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthCrud
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthCrud        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowCrud          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupCrud = fmt.Errorf("proto: unexpected end of group")
)

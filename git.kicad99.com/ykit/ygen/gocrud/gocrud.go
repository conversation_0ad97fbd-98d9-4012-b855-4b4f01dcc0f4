package gocrud

import (
	"bytes"
	"log"
	"strings"
	"text/template"

	"git.kicad99.com/ykit/goutil"
	"git.kicad99.com/ykit/ygen"
	"github.com/golang/protobuf/proto"
	"github.com/golang/protobuf/protoc-gen-go/descriptor"
	plugin_go "github.com/golang/protobuf/protoc-gen-go/plugin"
)

func YGenGoCRUD(request *plugin_go.CodeGeneratorRequest) (genFiles []*plugin_go.CodeGeneratorResponse_File) {
	rpcGoItems := YGenGoCRUDRpcgo(request)
	genFiles = append(genFiles, rpcGoItems...)

	crudItems := YGenGoCRUDgo(request)
	genFiles = append(genFiles, crudItems...)

	return
}

func YGenGoCRUDProto(request *plugin_go.CodeGeneratorRequest) (genFiles []*plugin_go.CodeGeneratorResponse_File) {
	//msgItems := YGenGoCRUDMsgItem(request)
	//genFiles = append(genFiles, msgItems...)

	rpcItems := YGenGoCRUDRpc(request)
	genFiles = append(genFiles, rpcItems...)
	return
}

func YGenGoCRUDMsgList(request *plugin_go.CodeGeneratorRequest) (genFiles []*plugin_go.CodeGeneratorResponse_File) {
	//msgItems := YGenGoCRUDMsgItem(request)
	//genFiles = append(genFiles, msgItems...)

	rpcItems := YGenCRUDProtoMsgList(request)
	genFiles = append(genFiles, rpcItems...)
	return
}

func YGenGoCRUDgo(request *plugin_go.CodeGeneratorRequest) (genFiles []*plugin_go.CodeGeneratorResponse_File) {

	var fd *descriptor.FileDescriptorProto

	goCRUDHead := `//Package {{.package}} generated by ygen-gocrud. DO NOT EDIT.
//source: {{.original_file}}
package {{.package}}

import (
	"git.kicad99.com/ykit/goutil/crud"
	{{if .HasRID}}"github.com/jackc/pgx/v4"{{end}}
	"github.com/jackc/pgx/v4/pgxpool"
)
`

	crudItem := `
//Insert
func (this *{{.Msg}}) Insert(db *pgxpool.Conn) (r *crud.DMLResult, err error) {
	return crud.Insert(this, db)
}
{{if .HasRID}}
//Update
func (this *{{.Msg}}) Update(db *pgxpool.Conn) (r *crud.DMLResult, err error) {
	return crud.Update(this, db)
}

//PartialUpdate
func (this *{{.Msg}}) PartialUpdate(updateCols []string, db *pgxpool.Conn) (r *crud.DMLResult, err error) {

	return crud.PartialUpdate(this, updateCols, db)
}

//Delete
func (this *{{.Msg}}) Delete(db *pgxpool.Conn) (r *crud.DMLResult, err error) {
	return crud.Delete(this, db)
}

//SelectOne
func (this *{{.Msg}}) SelectOne(resultCols []string, db *pgxpool.Conn) (selectRow int32, err error) {

	return crud.YkitDbSelectOne(this, resultCols, []string{"RID"}, db)
}

//SelectByRids
func (this *{{.Msg}}) SelectByRids(resultCols []string, rids []string, db *pgxpool.Conn) (rows pgx.Rows, err error) {

	return crud.YkitDbSelectByRIDs(this, resultCols, rids, db)
}{{end}}
`

	for _, fd = range request.GetProtoFile() {
		if !ygen.IsProtoFileNeedGenerate(fd.GetName(), request.FileToGenerate) {
			continue
		}
		needWriteThisFile := false

		rpcFilename := goutil.ExtractFilename(fd.GetName()) + ".crud.go"

		tHead := template.Must(template.New("rpc_head").Parse(goCRUDHead))
		sqlHeadData := map[string]interface{}{
			"original_file": fd.GetName(),
			"package":       fd.GetPackage(),
			"goPkg":         fd.Options.GetGoPackage(),
			"HasRID":        false,
		}

		rpcFileContent := ""

		tMsg := template.Must(template.New("crud_msg").Parse(crudItem))

		//process every msg
		for _, msg := range fd.MessageType {
			msgName := msg.GetName()

			if !strings.HasPrefix(*msg.Name, "Db") {
				//如果不是以Db开头则不处理，规定数据库相关的表message必须以Db开头
				continue
			}

			msgLeadingComments := ygen.GetProtoMsgLeadingComments(fd, msg)

			rpcPos := strings.Index(msgLeadingComments, "@rpc")
			if rpcPos < 0 {
				//no @rpc
				continue
			}

			rpcs := ygen.ExtractMsgRpc(msgLeadingComments)
			if len(rpcs) == 0 {
				//no rpc
				continue
			}

			if goutil.StrSliceIndex(rpcs, "crud") != -1 {
				needWriteThisFile = true
			}
			if goutil.StrSliceIndex(rpcs, "pcrud") != -1 {
				needWriteThisFile = true
			}

			tMsgData := map[string]interface{}{
				"Msg":      msgName,
				"MsgParam": msgName + "Param",
				"HasRID":   false,
			}

			for _, field := range msg.Field {
				if field.GetName() == "RID" {
					tMsgData["HasRID"] = true
					sqlHeadData["HasRID"] = true
				}
			}

			var msgBytes bytes.Buffer
			if err := tMsg.Execute(&msgBytes, tMsgData); err != nil {
				log.Fatal(err)
			}

			rpcFileContent = rpcFileContent + msgBytes.String()
		}

		if !needWriteThisFile {
			continue
		}

		var tplBytes bytes.Buffer
		if err := tHead.Execute(&tplBytes, sqlHeadData); err != nil {
			log.Fatal(err)
		}

		rpcFileHeadContent := tplBytes.String()

		genFile := &plugin_go.CodeGeneratorResponse_File{
			Name:    proto.String(rpcFilename),
			Content: proto.String(rpcFileHeadContent + rpcFileContent),
		}

		genFiles = append(genFiles, genFile)

	}

	return
}

//生成crud的rpc proto 文件
func YGenGoCRUDRpc(request *plugin_go.CodeGeneratorRequest) (genFiles []*plugin_go.CodeGeneratorResponse_File) {

	var fd *descriptor.FileDescriptorProto

	rpcCRUDHead := `//generated by ygen-gocrud-proto. DO NOT EDIT.
//source: {{.original_file}}
syntax = "proto3";
package {{.package}};

import "crud.proto";
import "{{.original_file}}"; 
import "{{.original_file_list}}"; 
{{if .goPkg}}option go_package="{{.goPkg}}";{{end}}
`

	rpcItem := `
{{if .CRUD}}//普通crud，没有权限检查
service   Rpc{{.Msg}} {
//插入一行数据
rpc Insert( {{.Msg}} ) returns (crud.DMLResult);
{{if .HasRID}}
//以RID为条件,全量修改一行数据
rpc Update( {{.Msg}} ) returns (crud.DMLResult);

//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
rpc PartialUpdate( {{.Msg}} ) returns (crud.DMLResult);

//以RID为条件,删除一行数据
rpc Delete( {{.Msg}} ) returns (crud.DMLResult);

//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
rpc SelectOne( crud.DMLParam ) returns ( {{.Msg}} );

//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
rpc SelectMany( crud.DMLParam ) returns ( stream {{.Msg}} );
{{end}}
//根据条件查询数据
rpc Query( crud.QueryParam ) returns ( stream {{.Msg}} );
//根据条件查询数据,批量返回数据,crud.QueryParam.Batch指定一次返回多少条数据
rpc QueryBatch( crud.QueryParam ) returns ( stream {{.Msg}}List );
}{{end}}

{{if .PCRUD}}//带权限检查(有相应的群组权限和编辑权限)的crud
service   Prpc{{.Msg}} {
//插入一行数据
rpc Insert( {{.Msg}} ) returns (crud.DMLResult);
{{if .HasRID}}
//以RID为条件,全量修改一行数据
rpc Update( {{.Msg}} ) returns (crud.DMLResult);

//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
rpc PartialUpdate( {{.Msg}} ) returns (crud.DMLResult);

//以RID为条件,删除一行数据
rpc Delete( {{.Msg}} ) returns (crud.DMLResult);

//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
rpc SelectOne( crud.DMLParam ) returns ( {{.Msg}} );

//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
rpc SelectMany( crud.DMLParam ) returns ( stream {{.Msg}} );
{{end}}
//根据条件取得我有权限的数据
rpc Query( crud.PrivilegeParam ) returns ( stream {{.Msg}} );
//根据条件取得我有权限的数据,批量返回数据,crud.PrivilegeParam.QueryParam.Batch指定一次返回多少条数据
rpc QueryBatch( crud.PrivilegeParam ) returns ( stream {{.Msg}}List );
}{{end}}
`

	for _, fd = range request.GetProtoFile() {
		if !ygen.IsProtoFileNeedGenerate(fd.GetName(), request.FileToGenerate) {
			continue
		}
		needWriteThisFile := false

		original_file := fd.GetName()
		originalFilenameOnly := goutil.ExtractFilename(original_file)
		rpcFilename := originalFilenameOnly + ".rpc.proto"

		tHead := template.Must(template.New("rpc_head").Parse(rpcCRUDHead))
		sqlHeadData := map[string]interface{}{
			"original_file":      original_file,
			"original_file_list": strings.ReplaceAll(original_file, ".proto", ".list.proto"),
			"original_filename":  originalFilenameOnly,
			"package":            fd.GetPackage(),
			"goPkg":              fd.Options.GetGoPackage(),
		}

		var tplBytes bytes.Buffer
		if err := tHead.Execute(&tplBytes, sqlHeadData); err != nil {
			log.Fatal(err)
		}

		rpcFileContent := tplBytes.String()

		tMsg := template.Must(template.New("rpc_msg").Parse(rpcItem))

		//process every msg
		for _, msg := range fd.MessageType {
			msgName := msg.GetName()

			if !strings.HasPrefix(*msg.Name, "Db") {
				//如果不是以Db开头则不处理，规定数据库相关的表message必须以Db开头
				continue
			}

			msgLeadingComments := ygen.GetProtoMsgLeadingComments(fd, msg)

			rpcPos := strings.Index(msgLeadingComments, "@rpc")
			if rpcPos < 0 {
				//no @rpc
				continue
			}

			rpcs := ygen.ExtractMsgRpc(msgLeadingComments)
			if len(rpcs) == 0 {
				//no rpc
				continue
			}

			tMsgData := map[string]interface{}{
				"Msg":             msgName,
				"MsgParam":        msgName + "Param",
				"MsgList":         msgName + "List",
				"HasRID":          false,
				"HasOrgRID":       false,
				"OrgRIDFieldName": "OrgRID",
			}

			for _, field := range msg.Field {
				if field.GetName() == "RID" {
					tMsgData["HasRID"] = true
				}
				if field.GetName() == "OrgRID" {
					tMsgData["HasOrgRID"] = true
				}
			}

			if goutil.StrSliceIndex(rpcs, "crud") != -1 {
				needWriteThisFile = true
				tMsgData["CRUD"] = 1
			}
			if goutil.StrSliceIndex(rpcs, "pcrud") != -1 {
				needWriteThisFile = true
				tMsgData["CRUD"] = 1 //pcrud need crud
				tMsgData["PCRUD"] = 1
			}

			var msgBytes bytes.Buffer
			if err := tMsg.Execute(&msgBytes, tMsgData); err != nil {
				log.Fatal(err)
			}

			rpcFileContent = rpcFileContent + msgBytes.String()
		}

		if !needWriteThisFile {
			continue
		}

		genFile := &plugin_go.CodeGeneratorResponse_File{
			Name:    proto.String(rpcFilename),
			Content: proto.String(rpcFileContent),
		}

		genFiles = append(genFiles, genFile)

	}

	return
}

func YGenCRUDProtoMsgList(request *plugin_go.CodeGeneratorRequest) (genFiles []*plugin_go.CodeGeneratorResponse_File) {

	var fd *descriptor.FileDescriptorProto

	rpcCRUDHead := `//generated by ygen-gocrud-proto. DO NOT EDIT.
//source: {{.original_file}}
syntax = "proto3";
package {{.package}};

import "{{.original_file}}"; 
{{if .goPkg}}option go_package="{{.goPkg}}";{{end}}
`

	rpcItem := `
// {{.Msg}} list
message   {{.Msg}}List {
	//批次号，从0开始
	uint32 BatchNo = 1;

	//rows offset,从0开始
	uint32 RowsOffset = 2;

	//本批次数据
	repeated {{.Msg}} Rows = 3;
}
`

	for _, fd = range request.GetProtoFile() {
		if !ygen.IsProtoFileNeedGenerate(fd.GetName(), request.FileToGenerate) {
			continue
		}
		needWriteThisFile := false

		original_file := fd.GetName()
		originalFilenameOnly := goutil.ExtractFilename(original_file)
		rpcFilename := originalFilenameOnly + ".list.proto"

		tHead := template.Must(template.New("rpc_head").Parse(rpcCRUDHead))
		sqlHeadData := map[string]interface{}{
			"original_file":     original_file,
			"original_filename": originalFilenameOnly,
			"package":           fd.GetPackage(),
			"goPkg":             fd.Options.GetGoPackage(),
		}

		var tplBytes bytes.Buffer
		if err := tHead.Execute(&tplBytes, sqlHeadData); err != nil {
			log.Fatal(err)
		}

		rpcFileContent := tplBytes.String()

		tMsg := template.Must(template.New("rpc_msg").Parse(rpcItem))

		//process every msg
		for _, msg := range fd.MessageType {
			msgName := msg.GetName()

			if !strings.HasPrefix(*msg.Name, "Db") {
				//如果不是以Db开头则不处理，规定数据库相关的表message必须以Db开头
				continue
			}

			msgLeadingComments := ygen.GetProtoMsgLeadingComments(fd, msg)

			rpcPos := strings.Index(msgLeadingComments, "@rpc")
			if rpcPos < 0 {
				//no @rpc
				continue
			}

			rpcs := ygen.ExtractMsgRpc(msgLeadingComments)
			if len(rpcs) == 0 {
				//no rpc
				continue
			}

			tMsgData := map[string]interface{}{
				"Msg": msgName,
			}

			needWriteThisFile = true

			var msgBytes bytes.Buffer
			if err := tMsg.Execute(&msgBytes, tMsgData); err != nil {
				log.Fatal(err)
			}

			rpcFileContent = rpcFileContent + msgBytes.String()
		}

		if !needWriteThisFile {
			continue
		}

		genFile := &plugin_go.CodeGeneratorResponse_File{
			Name:    proto.String(rpcFilename),
			Content: proto.String(rpcFileContent),
		}

		genFiles = append(genFiles, genFile)

	}

	return
}

//生成grpc server端代码
func YGenGoCRUDRpcgo(request *plugin_go.CodeGeneratorRequest) (genFiles []*plugin_go.CodeGeneratorResponse_File) {

	var fd *descriptor.FileDescriptorProto

	rpcCRUDHead := `//Package {{.package}}  generated by ygen-gocrud. DO NOT EDIT.
//source: {{.original_file}}
package {{.package}}

import (
	"context"
	"strconv"

	"git.kicad99.com/ykit/goutil/crud"
	"git.kicad99.com/ykit/goutil/crudhook"
	"git.kicad99.com/ykit/goutil"
	"git.kicad99.com/ykit/goutil/pgxdb"
	{{if .HasRID}}"git.kicad99.com/ykit/goutil/rpc"{{end}}
	"git.kicad99.com/ykit/goutil/yreflect"
	"google.golang.org/grpc/metadata"
)
`

	rpcGoItem := `{{if .CRUD }}
// Crud{{.Service}}Server impl crud Service
type Crud{{.Service}}Server struct {
}

//Insert impl crud insert
func (*Crud{{.Service}}Server) Insert(ctx context.Context, req *{{.Msg}}) (r *crud.DMLResult, err error) {
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	r, err = req.Insert(db)
	if err == nil {
		sys, userRid, sid := pgxdb.GetYkitInfoFromMetadata(ctx)
		ipinfo := goutil.GetIpinfoFromMeta(ctx)
		userOrgrid := goutil.GetUserOrgRIDFromMeta(ctx)
		{{if .HasOrgRID}}goutil.NatsCdc(sys, req.{{.OrgRIDFieldName}}, sid, "{{.Msg}}.Insert", req, nil){{else}}_ = sid{{end}}
		crudhook.CrudAfterHook(sys, userRid, userOrgrid, "{{.Msg}}.Insert", req, nil, ipinfo)
	}
	return
}
{{if .HasRID}}
//Update impl crud update
func (*Crud{{.Service}}Server) Update(ctx context.Context, req *{{.Msg}}) (r *crud.DMLResult, err error) {
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	r, err = req.Update(db)
	if err == nil {
		sys, userRid, sid := pgxdb.GetYkitInfoFromMetadata(ctx)
		ipinfo := goutil.GetIpinfoFromMeta(ctx)
		userOrgrid := goutil.GetUserOrgRIDFromMeta(ctx)
		{{if .HasOrgRID}}goutil.NatsCdc(sys, req.{{.OrgRIDFieldName}}, sid, "{{.Msg}}.Update", req, nil){{else}}_ = sid{{end}}
		crudhook.CrudAfterHook(sys, userRid, userOrgrid, "{{.Msg}}.Update", req, nil, ipinfo)
	}
	return
}

//PartialUpdate impl crud PartialUpdate
func (*Crud{{.Service}}Server) PartialUpdate(ctx context.Context, req *{{.Msg}}) (r *crud.DMLResult, err error) {
	param, err := crud.GetDMLParamFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	r, err = req.PartialUpdate(param.KeyColumn, db)
	if err == nil {
		sys, userRid, sid := pgxdb.GetYkitInfoFromMetadata(ctx)
		ipinfo := goutil.GetIpinfoFromMeta(ctx)
		userOrgrid := goutil.GetUserOrgRIDFromMeta(ctx)
		{{if .HasOrgRID}}goutil.NatsCdc(sys, req.{{.OrgRIDFieldName}}, sid, "{{.Msg}}.PartialUpdate", req, param){{else}}_ = sid{{end}}
		crudhook.CrudAfterHook(sys, userRid, userOrgrid, "{{.Msg}}.PartialUpdate", req, param, ipinfo)
	}
	return
}

//Delete impl crud Delete
func (*Crud{{.Service}}Server) Delete(ctx context.Context, req *{{.Msg}}) (r *crud.DMLResult, err error) {
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	r, err = req.Delete(db)
	if err == nil {
		sys, userRid, sid := pgxdb.GetYkitInfoFromMetadata(ctx)
		ipinfo := goutil.GetIpinfoFromMeta(ctx)
		userOrgrid := goutil.GetUserOrgRIDFromMeta(ctx)
		{{if .HasOrgRID}}goutil.NatsCdc(sys, req.{{.OrgRIDFieldName}}, sid, "{{.Msg}}.Delete", req, nil){{else}}_ = sid{{end}}
		crudhook.CrudAfterHook(sys, userRid, userOrgrid, "{{.Msg}}.Delete", req, nil, ipinfo)
	}
	return
}

//SelectOne impl crud SelectOne
func (*Crud{{.Service}}Server) SelectOne(ctx context.Context, dmlParam *crud.DMLParam) (*{{.Msg}}, error) {
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	if len(dmlParam.KeyValue) != 1 {
		return nil, rpc.EBadProtoParam
	}
	msg := &{{.Msg}}{RID: dmlParam.KeyValue[0]}

	selectRow, err := msg.SelectOne(dmlParam.ResultColumn, db)
	if err != nil {
		return nil, err
	}
	if selectRow == 0 {
		msg.Reset()
	}
	return msg, nil
}

//SelectMany impl crud SelectMany
func (*Crud{{.Service}}Server) SelectMany(dmlParam *crud.DMLParam, srv Rpc{{.Msg}}_SelectManyServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)

	msg := &{{.Msg}}{}

	rows, err := msg.SelectByRids(dmlParam.ResultColumn, dmlParam.KeyValue, db)
	if err != nil {
		return err
	}
	defer rows.Close()
	rowsName := crud.RowsColNames2GoFieldNames(rows, msg)
	rowsCount := 0
	for rows.Next() {
		msg := &{{.Msg}}{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		err = srv.Send(msg)
		if err != nil {
			return err
		}
		rowsCount++
	}
	trailer := metadata.Pairs("rows", strconv.Itoa(rowsCount))
	srv.SetTrailer(trailer)
	return nil
}
{{end}}
//Query impl crud Query
func (*Crud{{.Service}}Server) Query(req *crud.QueryParam, srv Rpc{{.Msg}}_QueryServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)

	rows, err := crud.DbSelectQuery("{{.Msg}}", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	rowsName := crud.RowsColNames2GoFieldNames(rows, &{{.Msg}}{})
	rowsCount := 0
	for rows.Next() {
		msg := &{{.Msg}}{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		err = srv.Send(msg)
		if err != nil {
			return err
		}
		rowsCount++
	}
	trailer := metadata.Pairs("rows", strconv.Itoa(rowsCount))
	srv.SetTrailer(trailer)
	return nil
}
//Query impl crud QueryBatch
func (*Crud{{.Service}}Server) QueryBatch(req *crud.QueryParam, srv Rpc{{.Msg}}_QueryBatchServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)

	rows, err := crud.DbSelectQuery("{{.Msg}}", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	if req.Batch < 1 {
		req.Batch = 1
	}
	if req.Batch > 10000 {
		req.Batch = 10000
	}

	rowsName := crud.RowsColNames2GoFieldNames(rows, &{{.Msg}}{})
	var batchNo uint32 = 0
	var rowsOffset uint32 = 0
	var batchCount uint32 = 0
	list := &{{.Msg}}List{}
	for rows.Next() {
		msg := &{{.Msg}}{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		list.Rows = append(list.Rows, msg)
		batchCount++
		rowsOffset++

		if batchCount >= req.Batch {
			err = srv.Send(list)
			if err != nil {
				return err
			}
			batchNo++
			list = &{{.Msg}}List{
				BatchNo:    batchNo,
				RowsOffset: rowsOffset}
			batchCount = 0
		}
	}
	if batchCount > 0 {
		err = srv.Send(list)
		if err != nil {
			return err
		}
	}
	trailer := metadata.Pairs("rows", strconv.FormatUint(uint64(rowsOffset), 10))
	srv.SetTrailer(trailer)
	return nil
}{{end}}
{{if .PCRUD }}
// Pcrud{{.Service}}Server impl pcrud Service
type Pcrud{{.Service}}Server struct {
}

//Insert impl pcrud Insert
func (*Pcrud{{.Service}}Server) Insert(ctx context.Context, req *{{.Msg}}) (*crud.DMLResult, error) {
	var crudServer *Crud{{.Service}}Server
	_, err := crud.GrpcCheckHasPermission(ctx, "db", "{{.Msg}}.Insert")
	if err != nil {
		return nil, err
	}
	{{.CheckHasPrivilege}}
	return crudServer.Insert(ctx, req)
}
{{if .HasRID}}
//Update impl pcrud Update
func (*Pcrud{{.Service}}Server) Update(ctx context.Context, req *{{.Msg}}) (*crud.DMLResult, error) {
	var crudServer *Crud{{.Service}}Server
	_, err := crud.GrpcCheckHasPermission(ctx, "db", "{{.Msg}}.Update")
	if err != nil {
		return nil, err
	}
	{{.CheckHasPrivilege}}
	return crudServer.Update(ctx, req)
}

//PartialUpdate impl pcrud PartialUpdate
func (*Pcrud{{.Service}}Server) PartialUpdate(ctx context.Context, req *{{.Msg}}) (*crud.DMLResult, error) {
	var crudServer *Crud{{.Service}}Server
	_, err := crud.GrpcCheckHasPermission(ctx, "db", "{{.Msg}}.Update")
	if err != nil {
		return nil, err
	}
	{{.CheckHasPrivilege}}
	return crudServer.PartialUpdate(ctx, req)
}

//Delete impl pcrud Delete
func (*Pcrud{{.Service}}Server) Delete(ctx context.Context, req *{{.Msg}}) (*crud.DMLResult, error) {
	var crudServer *Crud{{.Service}}Server
	_, err := crud.GrpcCheckHasPermission(ctx, "db", "{{.Msg}}.Delete")
	if err != nil {
		return nil, err
	}
	{{.CheckHasPrivilege}}
	return crudServer.Delete(ctx, req)
}

//SelectOne impl pcrud SelectOne
func (*Pcrud{{.Service}}Server) SelectOne(ctx context.Context, dmlParam *crud.DMLParam) (*{{.Msg}}, error) {
	var crudServer *Crud{{.Service}}Server
	return crudServer.SelectOne(ctx, dmlParam)
}

//SelectMany impl pcrud SelectMany
func (*Pcrud{{.Service}}Server) SelectMany(dmlParam *crud.DMLParam, srv Prpc{{.Msg}}_SelectManyServer) error {
	var crudServer *Crud{{.Service}}Server
	return crudServer.SelectMany(dmlParam, srv)
}
{{end}}
//Query impl pcrud Query
func (*Pcrud{{.Service}}Server) Query(req *crud.PrivilegeParam, srv Prpc{{.Msg}}_QueryServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)
	rows, err := crud.DbSelectPrivilege("{{.Msg}}", "{{.OrgRID}}", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	rowsName := crud.RowsColNames2GoFieldNames(rows, &{{.Msg}}{})
	rowsCount := 0
	for rows.Next() {
		msg := &{{.Msg}}{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		err = srv.Send(msg)
		if err != nil {
			return err
		}
		rowsCount++
	}
	trailer := metadata.Pairs("rows", strconv.Itoa(rowsCount))
	srv.SetTrailer(trailer)
	return nil
}
//Query impl pcrud QueryBatch
func (*Pcrud{{.Service}}Server) QueryBatch(req *crud.PrivilegeParam, srv Prpc{{.Msg}}_QueryBatchServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)
	rows, err := crud.DbSelectPrivilege("{{.Msg}}", "{{.OrgRID}}", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	if req.QueryCondition.Batch < 1 {
		req.QueryCondition.Batch = 1
	}
	if req.QueryCondition.Batch > 10000 {
		req.QueryCondition.Batch = 10000
	}

	rowsName := crud.RowsColNames2GoFieldNames(rows, &{{.Msg}}{})

	var batchNo uint32 = 0
	var rowsOffset uint32 = 0
	var batchCount uint32 = 0
	list := &{{.Msg}}List{}
	for rows.Next() {
		msg := &{{.Msg}}{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		list.Rows = append(list.Rows, msg)
		batchCount++
		rowsOffset++

		if batchCount >= req.QueryCondition.Batch {
			err = srv.Send(list)
			if err != nil {
				return err
			}
			batchNo++
			list = &{{.Msg}}List{
				BatchNo:    batchNo,
				RowsOffset: rowsOffset}
			batchCount = 0
		}
	}
	if batchCount > 0 {
		err = srv.Send(list)
		if err != nil {
			return err
		}
	}
	trailer := metadata.Pairs("rows", strconv.FormatUint(uint64(rowsOffset), 10))
	srv.SetTrailer(trailer)
	return nil
}{{end}}`

	for _, fd = range request.GetProtoFile() {
		if !ygen.IsProtoFileNeedGenerate(fd.GetName(), request.FileToGenerate) {
			continue
		}
		needWriteThisFile := false

		rpcFilename := goutil.ExtractFilename(fd.GetName()) + ".grpc.go"

		tHead := template.Must(template.New("rpc_head").Parse(rpcCRUDHead))
		sqlHeadData := map[string]interface{}{
			"original_file": fd.GetName(),
			"package":       fd.GetPackage(),
			"goPkg":         fd.Options.GetGoPackage(),
			"HasRID":        false,
		}

		rpcFileContent := ""

		tMsg := template.Must(template.New("rpc_msg").Parse(rpcGoItem))

		//process every msg
		for _, msg := range fd.MessageType {
			msgName := msg.GetName()

			if !strings.HasPrefix(*msg.Name, "Db") {
				//如果不是以Db开头则不处理，规定数据库相关的表message必须以Db开头
				continue
			}

			msgLeadingComments := ygen.GetProtoMsgLeadingComments(fd, msg)

			rpcPos := strings.Index(msgLeadingComments, "@rpc")
			if rpcPos < 0 {
				//no @rpc
				continue
			}

			rpcs := ygen.ExtractMsgRpc(msgLeadingComments)
			if len(rpcs) == 0 {
				//no rpc
				continue
			}

			tMsgData := map[string]interface{}{
				"Msg":               msgName,
				"MsgParam":          msgName + "Param",
				"Service":           "Rpc" + msgName,
				"OrgRID":            "OrgRID",
				"CheckHasPrivilege": "",
				"HasRID":            false,
				"HasOrgRID":         false,
				"OrgRIDFieldName":   "OrgRID",
			}

			for _, field := range msg.Field {
				if field.GetName() == "RID" {
					tMsgData["HasRID"] = true
					sqlHeadData["HasRID"] = true

				}
				if field.GetName() == "OrgRID" {
					tMsgData["HasOrgRID"] = true
				}
			}

			hasOrgRIDField := false
			privilegeColName := ""
			for _, f := range msg.Field {
				if f.GetName() == "OrgRID" {
					hasOrgRIDField = true
					privilegeColName = "OrgRID"
					break
				}
			}
			if !hasOrgRIDField {
				tMsgData["OrgRID"] = ""
			}

			if msgName == "DbOrg" {
				tMsgData["OrgRID"] = "RID"
				privilegeColName = "OrgRID"
			}

			if len(privilegeColName) > 0 {
				clearUserOrgCache := ""
				if msgName == "DbOrg" || msgName == "DbUserOrgPrivilege" {
					clearUserOrgCache = `defer crud.ResetUserOrgCache()
	`
				}

				tMsgData["CheckHasPrivilege"] = clearUserOrgCache + strings.ReplaceAll(
					`err = crud.GrpcCheckHasOrgridPrivilege(ctx, req.privilegeColName)
	if err != nil {
		return nil, err
	}`, "privilegeColName", privilegeColName)
			}

			if goutil.StrSliceIndex(rpcs, "crud") != -1 {
				needWriteThisFile = true
				tMsgData["CRUD"] = 1
			}

			if goutil.StrSliceIndex(rpcs, "pcrud") != -1 {
				needWriteThisFile = true
				tMsgData["CRUD"] = 1
				tMsgData["PCRUD"] = 1
			}

			var msgBytes bytes.Buffer
			if err := tMsg.Execute(&msgBytes, tMsgData); err != nil {
				log.Fatal(err)
			}

			rpcFileContent = rpcFileContent + msgBytes.String()
		}

		if !needWriteThisFile {
			continue
		}

		var tplBytes bytes.Buffer
		if err := tHead.Execute(&tplBytes, sqlHeadData); err != nil {
			log.Fatal(err)
		}

		rpcFileHeadContent := tplBytes.String()

		if !strings.HasSuffix(rpcFileContent, "\n") {
			rpcFileContent = rpcFileContent + "\n"
		}
		genFile := &plugin_go.CodeGeneratorResponse_File{
			Name:    proto.String(rpcFilename),
			Content: proto.String(rpcFileHeadContent + rpcFileContent),
		}

		genFiles = append(genFiles, genFile)

	}

	return
}

//生成msgList,msgCondition
func YGenGoCRUDMsgItem(request *plugin_go.CodeGeneratorRequest) (genFiles []*plugin_go.CodeGeneratorResponse_File) {

	var fd *descriptor.FileDescriptorProto

	crudMsgListProtoHead := `//generated by ygen-gocrud. DO NOT EDIT.
//source: {{.original_file}}
syntax = "proto3";
package {{.package}};

import "{{.original_file}}";
import "crud.proto";
{{if .goPkg}}option go_package="{{.goPkg}}";{{end}}
`
	msgItem := `
message {{.Msg}}List {
    int32 Code = 1; //一般不用，用到时在具体的api里面说明
    uint32 No = 2; //包序号,从0 开始,类似于分页号码
    string Info = 3; //一般不用，用到时在具体的api里面说明
    repeated {{.Msg}} Rows = 5; //具体的数据
}
message {{.Msg}}Param {
    {{.Msg}} Msg = 1; //{{.Msg}} 数据
    crud.DMLParam Param = 2; //条件数据
}
`

	for _, fd = range request.GetProtoFile() {
		if !ygen.IsProtoFileNeedGenerate(fd.GetName(), request.FileToGenerate) {
			continue
		}
		needWriteThisFile := false

		msgListFilename := goutil.ExtractFilename(fd.GetName()) + ".list.proto"

		tHead := template.Must(template.New("table_head").Parse(crudMsgListProtoHead))
		msgListHeadData := map[string]interface{}{
			"original_file": fd.GetName(),
			"package":       fd.GetPackage(),
			"goPkg":         fd.Options.GetGoPackage(),
		}

		var tplBytes bytes.Buffer
		if err := tHead.Execute(&tplBytes, msgListHeadData); err != nil {
			log.Fatal(err)
		}

		msgListFileContent := tplBytes.String()

		tMsg := template.Must(template.New("msgItem").Parse(msgItem))

		//process every msg
		for _, msg := range fd.MessageType {
			msgName := msg.GetName()
			if !strings.HasPrefix(*msg.Name, "Db") {
				//如果不是以Db开头则不处理，规定数据库相关的表message必须以Db开头
				continue
			}

			msgLeadingComments := ygen.GetProtoMsgLeadingComments(fd, msg)

			rpcPos := strings.Index(msgLeadingComments, "@rpc")
			if rpcPos < 0 {
				//no @rpc
				continue
			}

			rpcs := ygen.ExtractMsgRpc(msgLeadingComments)
			if len(rpcs) == 0 {
				//no rpc
				continue
			}

			if goutil.StrSliceIndex(rpcs, "crud") != -1 {
				needWriteThisFile = true
			}

			tMsgData := map[string]interface{}{
				"Msg": msgName,
			}

			var msgBytes bytes.Buffer
			if err := tMsg.Execute(&msgBytes, tMsgData); err != nil {
				log.Fatal(err)
			}

			msgListFileContent = msgListFileContent + msgBytes.String()
		}

		if !needWriteThisFile {
			continue
		}

		genFile := &plugin_go.CodeGeneratorResponse_File{
			Name:    proto.String(msgListFilename),
			Content: proto.String(msgListFileContent),
		}

		genFiles = append(genFiles, genFile)

	}

	return
}

syntax = "proto3";

package ygen.sql;

//@dbpre --test dbpre
//@dbpre --test dbpre 21
//@dbpost --test dbpost
//@dbpost --test dbpost 233
//@dbpre drop table if exites DbTest
//@db CHECK (Price > DiscountedPrice)
//@db CHECK (Price > DiscountedPrice/0.9)
//@dbpost create index on DbTest (name)
//@dbend PARTITION BY hash (Name)
//这个是测试表
//用来测试ygen-sql
message DbTest{
	//@db text primary key
	//这个是Name的注释
	string Name=1;
	
	//这个是Price的注释
	//@db numeric 
	//@db check(Price>0)
	double Price=2;
	
	//@db numeric
	double DiscountedPrice=8;
	
	//@db jsonb '{}'::jsonb
	string Note =9;
}
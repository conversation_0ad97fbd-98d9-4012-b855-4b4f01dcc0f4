package main

import (
	"io/ioutil"
	"log"
	"os"

	"git.kicad99.com/ykit/ygen/jsyrpc"
	"github.com/golang/protobuf/proto"
	"github.com/golang/protobuf/protoc-gen-go/plugin"
)

var request plugin_go.CodeGeneratorRequest

func main() {

	log.SetPrefix("ygen-jsyrpc: ")
	data, err := ioutil.ReadAll(os.Stdin)
	if err != nil {
		log.Fatalf("error: reading input: %v", err)
	}

	var response plugin_go.CodeGeneratorResponse
	if err := proto.Unmarshal(data, &request); err != nil {
		log.Fatalf("error: parsing input proto: %v", err)
	}

	jsRpcFiles := jsyrpc.YgenJsRpc(&request)

	for _, f := range jsRpcFiles {
		response.File = append(response.File, f)
	}

	if data, err = proto.Marshal(&response); err != nil {
		log.Fatalf("error: failed to marshal output proto: %v", err)
	}
	if _, err := os.Stdout.Write(data); err != nil {
		log.Fatalf("error: failed to write output proto: %v", err)
	}

}

package sql

import (
	"log"
	"unicode"

	"github.com/golang/protobuf/protoc-gen-go/plugin"

	"bytes"
	"strings"
	"text/template"

	"git.kicad99.com/ykit/goutil"
	"git.kicad99.com/ykit/ygen"
	"github.com/golang/protobuf/proto"
	"github.com/golang/protobuf/protoc-gen-go/descriptor"
)

type DBItem struct {
	Name     string
	Db       []string
	Comments []string
}

type MsgItems struct {
	Msg         string
	HasRID      bool
	RIDinsert   string
	RIDupdate   string
	RIDdelete   string
	RIDquery    string
	RIDAllQuery string
	Dbpre       DBItem
	Dbpost      DBItem
	Dbmsg       DBItem
	Dbend       DBItem
	Dbcomment   DBItem
	Fields      []DBItem
}

func (this DBItem) Dbpre() string {
	return this.Dbs(false)
}
func (this DBItem) Dbend() string {
	sb := strings.Builder{}

	for i, db := range this.Db {
		sb.WriteString(db)
		if i < len(this.Db)-1 {
			sb.WriteString("\n")
		}
	}

	return sb.String()
}
func (this DBItem) Dbpost() string {
	return this.Dbs(false)
}
func (this DBItem) Dbs(leadingComma bool) string {
	sb := strings.Builder{}

	for _, db := range this.Db {
		if leadingComma {
			sb.WriteString(",")
		}
		sb.WriteString(db)
		sb.WriteString("\n")
	}

	return sb.String()
}
func (this DBItem) Dbcomment() string {
	sb := strings.Builder{}

	for _, comment := range this.Comments {
		sb.WriteString("--")
		sb.WriteString(comment)
		sb.WriteString("\n")
	}

	return sb.String()
}
func (this DBItem) FieldComments() string {
	return this.Dbcomment()
}
func (this DBItem) FieldDbs(index int) string {
	sb := strings.Builder{}

	if index > 0 {
		sb.WriteString(",")
	}

	for j, db := range this.Db {
		if j == 0 {
			sb.WriteString(this.Name)
		}
		sb.WriteString(db)
		sb.WriteString("\n")
	}

	return sb.String()
}

func YGenSQL(request *plugin_go.CodeGeneratorRequest) (genFiles []*plugin_go.CodeGeneratorResponse_File) {

	var fd *descriptor.FileDescriptorProto

	sqlHeadTmpl := `--generated by ygen-sql DO NOT EDIT.
--source: {{.original_file}}

`
	msgTmpl := `--begin   {{.Msg}}
{{.Dbpre.Dbpre}}
{{.Dbcomment.Dbcomment}}create table if not exists {{.Msg}} (
{{ range $i, $dbitem := .Fields}}{{$dbitem.FieldComments}}{{$dbitem.FieldDbs $i}}{{end}}
{{.Dbmsg.Dbs true}}){{.Dbend.Dbend}};
{{.Dbpost.Dbpost}}
--end {{.Msg}}`

	permTmp := `--depfile role-init.sql
INSERT INTO DbPermission (rid, permissiontype, permissionname, permissionvalue, updatedat, updateddc)
VALUES('{{.RIDinsert}}', 'db', '{{.Msg}}.Insert', '{{.Msg}}.Insert', now_utc(), '') ON CONFLICT  DO NOTHING;
--admin role
INSERT INTO dbrolepermission (rid, rolerid, permissionrid, creator, updatedat, updateddc)
VALUES('{{.RIDinsert}}', '00000000-0000-0000-0000-000000000000', '{{.RIDinsert}}', null, now_utc(), '') ON CONFLICT  DO NOTHING;


INSERT INTO DbPermission (rid, permissiontype, permissionname, permissionvalue, updatedat, updateddc)
VALUES('{{.RIDquery}}', 'db', '{{.Msg}}.Query', '{{.Msg}}.Query', now_utc(), '') ON CONFLICT  DO NOTHING;
--base role
INSERT INTO dbrolepermission (rid, rolerid, permissionrid, creator, updatedat, updateddc)
VALUES('{{.RIDquery}}', '55555555-5555-5555-5555-555555555555', '{{.RIDquery}}', null, now_utc(), '') ON CONFLICT  DO NOTHING;
--admin role
INSERT INTO dbrolepermission (rid, rolerid, permissionrid, creator, updatedat, updateddc)
VALUES('{{.RIDAllQuery}}', '00000000-0000-0000-0000-000000000000', '{{.RIDquery}}', null, now_utc(), '') ON CONFLICT  DO NOTHING;
{{if .HasRID}}
INSERT INTO DbPermission (rid, permissiontype, permissionname, permissionvalue, updatedat, updateddc)
VALUES('{{.RIDupdate}}', 'db', '{{.Msg}}.Update', '{{.Msg}}.Update', now_utc(), '') ON CONFLICT  DO NOTHING;
--admin role
INSERT INTO dbrolepermission (rid, rolerid, permissionrid, creator, updatedat, updateddc)
VALUES('{{.RIDupdate}}', '00000000-0000-0000-0000-000000000000', '{{.RIDupdate}}', null, now_utc(), '') ON CONFLICT  DO NOTHING;


INSERT INTO DbPermission (rid, permissiontype, permissionname, permissionvalue, updatedat, updateddc)
VALUES('{{.RIDdelete}}', 'db', '{{.Msg}}.Delete', '{{.Msg}}.Delete', now_utc(), '') ON CONFLICT  DO NOTHING;
--admin role
INSERT INTO dbrolepermission (rid, rolerid, permissionrid, creator, updatedat, updateddc)
VALUES('{{.RIDdelete}}', '00000000-0000-0000-0000-000000000000', '{{.RIDdelete}}', null, now_utc(), '') ON CONFLICT  DO NOTHING;

{{end}}
`

	for _, fd = range request.GetProtoFile() {
		needWriteThisFile := false

		sqlFilename := goutil.ExtractFilename(fd.GetName()) + ".sql"
		permFilename := goutil.ExtractFilename(fd.GetName()) + ".perm.sql"

		tHead := template.Must(template.New("table_head").Parse(sqlHeadTmpl))
		sqlHeadData := map[string]interface{}{
			"original_file": fd.GetName(),
		}

		var tplBytes bytes.Buffer
		if err := tHead.Execute(&tplBytes, sqlHeadData); err != nil {
			log.Fatal(err)
		}

		sqlFileContent := tplBytes.String()
		permFileConten := ""

		tMsg := template.Must(template.New("table_msg").Parse(msgTmpl))
		tPerm := template.Must(template.New("init perm").Parse(permTmp))

		//process every msg
		for _, msg := range fd.MessageType {

			if !strings.HasPrefix(*msg.Name, "Db") {
				//如果不是以Db开头则不处理，规定数据库相关的表message必须以Db开头
				continue
			}
			if strings.Contains(*msg.Name, "_") {
				log.Fatal("can not use _(underscore) in message name", *msg.Name, fd.GetName())
			}

			tMsgData := MsgItems{}
			tMsgData.Msg = *msg.Name

			msgLeadingComments := ygen.GetProtoMsgLeadingComments(fd, msg)

			if len(msgLeadingComments) == 0 {
				//必须有注释，否则不处理
				log.Println("no msg comment, no gen sql for:", tMsgData.Msg)
				continue
			}

			needWriteThisFile = true

			dbpre := DBItem{}
			dbpost := DBItem{}
			dbmsg := DBItem{}
			dbend := DBItem{}
			dbcomments := DBItem{}

			msgDbPre, msgDb, msgDbend, msgDbPost, msgComments := ygen.ExtractMsgLevelSql(msgLeadingComments)
			dbpre.Db = msgDbPre
			dbmsg.Db = msgDb
			dbend.Db = msgDbend
			dbpost.Db = msgDbPost
			dbcomments.Comments = msgComments

			tMsgData.Dbpre = dbpre
			tMsgData.Dbmsg = dbmsg
			tMsgData.Dbend = dbend
			tMsgData.Dbpost = dbpost
			tMsgData.Dbcomment = dbcomments

			uuidInsert, err := goutil.StrHash2UUID(msg.GetName() + ".insert")
			if err != nil {
				log.Fatal("gen rid for insert err:", msg.GetName(), err)
			}
			tMsgData.RIDinsert = uuidInsert.String()

			uuidUpdate, err := goutil.StrHash2UUID(msg.GetName() + ".update")
			if err != nil {
				log.Fatal("gen rid for update err:", msg.GetName(), err)
			}
			tMsgData.RIDupdate = uuidUpdate.String()

			uuidDelete, err := goutil.StrHash2UUID(msg.GetName() + ".delete")
			if err != nil {
				log.Fatal("gen rid for delete err:", msg.GetName(), err)
			}
			tMsgData.RIDdelete = uuidDelete.String()

			uuidQuery, err := goutil.StrHash2UUID(msg.GetName() + ".query")
			if err != nil {
				log.Fatal("gen rid for query err:", msg.GetName(), err)
			}
			tMsgData.RIDquery = uuidQuery.String()

			uuidQueryAll, err := goutil.StrHash2UUID(msg.GetName() + ".query.all")
			if err != nil {
				log.Fatal("gen rid for query all err:", msg.GetName(), err)
			}
			tMsgData.RIDAllQuery = uuidQueryAll.String()

			fields := []DBItem{}
			for _, field := range msg.Field {
				if strings.Contains(*field.Name, "_") {
					log.Fatal("can not use _(underscore) in field name", *msg.Name, *field.Name, fd.GetName())
				}

				if field.GetName() == "RID" {
					tMsgData.HasRID = true
				}
				_, fieldLeadingComment := ygen.GetProtoMsgFieldLeadingComments(fd, msg, field)

				fieldDb, fieldComments := ygen.ExtractFieldLevelSql(fieldLeadingComment)

				if len(fieldDb) == 0 {
					//这个字段没有@db,不需要在数据库表里面有这个字段
					continue
				}

				if !unicode.IsUpper(rune((*field.Name)[0])) {
					log.Fatal("FieldName must be Capital: ", *msg.Name, " : ", *field.Name)
				}

				fieldDbItem := DBItem{Name: *field.Name, Db: fieldDb, Comments: fieldComments}
				fields = append(fields, fieldDbItem)
			}

			tMsgData.Fields = fields

			var msgBytes bytes.Buffer
			if err := tMsg.Execute(&msgBytes, tMsgData); err != nil {
				log.Fatal(err)
			}

			sqlFileContent = sqlFileContent + msgBytes.String()

			var msgBytesPerm bytes.Buffer
			if err := tPerm.Execute(&msgBytesPerm, tMsgData); err != nil {
				log.Fatal(err)
			}

			permFileConten = permFileConten + msgBytesPerm.String()

		}

		if !needWriteThisFile {
			continue
		}

		sqlFile := &plugin_go.CodeGeneratorResponse_File{
			Name:    proto.String(sqlFilename),
			Content: proto.String(sqlFileContent),
		}

		genFiles = append(genFiles, sqlFile)

		permFile := &plugin_go.CodeGeneratorResponse_File{
			Name:    proto.String(permFilename),
			Content: proto.String(permFileConten),
		}

		genFiles = append(genFiles, permFile)

	}

	return
}

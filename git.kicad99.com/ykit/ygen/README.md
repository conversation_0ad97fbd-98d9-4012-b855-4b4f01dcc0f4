# ygen

ygen 代码生成相关



ygen 可以生成以下方面的代码：

- sql建表
- go操作数据库的crud代码
- crud的前后端实现代码



## ygen 关于proto 生成sql
test.pro
```protobuf
syntax = "proto3";

package ygen.sql;

//@dbpre --test dbpre
//@dbpre --test dbpre 21
//@dbpost --test dbpost
//@dbpost --test dbpost 233
//@dbpre drop table if exites DbTest
//@db CHECK (Price > DiscountedPrice)
//@db CHECK (Price > DiscountedPrice/0.9)
//@dbpost create index on DbTest (name)
//@dbend PARTITION BY hash (Name)
//这个是测试表
//用来测试ygen-sql
message DbTest{
	//@db text primary key
	//这个是Name的注释
	string Name=1;
	
	//这个是Price的注释
	//@db numeric 
	//@db check(Price>0)
	double Price=2;
	
	//@db numeric
	double DiscountedPrice=8;
	
	//@db jsonb '{}'::jsonb
	string Note =9;
}
```
protoc --ygen-sql_out=. test.proto 生成以下的sql 文件

test.sql

```sql

--generated by ygen-sql at 2019-12-08 22:36:51
--proto file: test.proto
--DO NOT EDIT THIS FILE!!!

--begin   DbTest
 --test dbpre
 --test dbpre 21
 drop table if exists DbTest;

--这个是测试表
--用来测试ygen-sql
create table if not exists DbTest (
--这个是Name的注释
Name text primary key
--这个是Price的注释
,Price numeric
 check(Price>0)
,DiscountedPrice numeric
,Note jsonb default '{}'::jsonb

, CHECK (Price > DiscountedPrice)
, CHECK (Price > DiscountedPrice/0.9)
) PARTITION BY hash (Name);
 --test dbpost
 --test dbpost 233
 create index on DbTest (name)

--end DbTest
```

说明：

- 多个@db,@dbpre,@dbpost可以放一起（中间不要有空行），ygen作为一块来处理
- 需要生成表的message必须以Db开头
- 如果message的字段没有@db,不会生成这个数据库字段
- 字段名必须是大写开头,go里面大写的字段才能导出，为了统一方便处理，强制都大写开头
- msg消息名和其中的字段field不能含有下划线_,因为go中需要转换，会导致很多转来转去的工作，简单起见，禁止使用下划线
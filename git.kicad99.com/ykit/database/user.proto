syntax = "proto3";

package user;

option go_package = "git.kicad99.com/ykit/database/user";

//用户表
//@rpc crud pcrud
//@dbpost CREATE INDEX IF NOT EXISTS idxDbUserOrgRID on DbUser USING hash(OrgRID);
//@dbpost INSERT INTO dbuser(rid, orgrid, userid, usertype, username, phone, image, setting, loginname, loginpass, updatedat, updateddc)VALUES('00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', 'ttt', 0, 'ttt', '', NULL, '{}'::jsonb, 'ttt', 'UwhpDj0xMGNzZw8sXYZOLGBHmH/q9Wxf7aB6/EbT43Q=', now_utc(), '') ON CONFLICT  DO NOTHING;
message DbUser {
  //@db uuid primary key
  //行ID
  string RID = 1;

  //@db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
  //用户所属的群组
  string OrgRID = 2;

  //@db varchar(16) not null unique
  //用户编号
  string UserID = 3;

  //@db int
  //用户类型
  int32 UserType = 4;

  //@db varchar(32) not null
  //用户名
  string UserName = 5;

  //@db text
  //备注
  string Note = 6;

  //@db text
  //用户电话
  string Phone = 7;

  //@db uuid REFERENCES DbImage(RID) ON DELETE SET NULL
  //用户图片
  string Image = 8;

  //@db jsonb not null default  '{}'::jsonb
  // 其它设置，可以在里面扩展需要用到的其它信息
  string Setting = 9;

  //@db varchar(32) unique
  //用户登录名,NULL(空)表示不能登录系统
  string LoginName = 10;

  //@db text default ''
  //用户登录密码,为base64(sha256(user_name+user_password)),系统不保存真实的密码
  string LoginPass = 11;

  //@db timestamp not null default now_utc()
  //数据最后修改时间
  string UpdatedAt = 14;

  //@db text
  //数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
  string UpdatedDC = 15;
}

//用户角色数据表
//一个用户可以有多个角色
//@rpc crud pcrud
//@dbpost CREATE INDEX IF NOT EXISTS idxDbUserRoleUserRID on DbUserRole USING hash(UserRID);
message DbUserRole {
  //@db uuid primary key
  //行ID
  string RID = 1;

  //@db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
  //用户所属的群组
  string OrgRID = 2;

  //@db uuid not null REFERENCES DbUser(RID) ON DELETE CASCADE
  //用户rid
  string UserRID = 3;

  //@db uuid not null REFERENCES DbRole(RID) ON DELETE CASCADE
  //role rid
  string RoleRID = 4;

  //@db timestamp not null default now_utc()
  //数据最后修改时间
  string UpdatedAt = 14;

  //@db text
  //数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
  string UpdatedDC = 15;  
}

//Package image generated by ygen-gocrud. DO NOT EDIT.
//source: image.proto
package image

import (
	"git.kicad99.com/ykit/goutil/crud"
	"github.com/jackc/pgx/v4"
	"github.com/jackc/pgx/v4/pgxpool"
)

//Insert
func (this *DbImage) Insert(db *pgxpool.Conn) (r *crud.DMLResult, err error) {
	return crud.Insert(this, db)
}

//Update
func (this *DbImage) Update(db *pgxpool.Conn) (r *crud.DMLResult, err error) {
	return crud.Update(this, db)
}

//PartialUpdate
func (this *DbImage) PartialUpdate(updateCols []string, db *pgxpool.Conn) (r *crud.DMLResult, err error) {

	return crud.PartialUpdate(this, updateCols, db)
}

//Delete
func (this *DbImage) Delete(db *pgxpool.Conn) (r *crud.DMLR<PERSON>ult, err error) {
	return crud.Delete(this, db)
}

//SelectOne
func (this *DbImage) SelectOne(resultCols []string, db *pgxpool.Conn) (selectRow int32, err error) {

	return crud.YkitDbSelectOne(this, resultCols, []string{"RID"}, db)
}

//SelectByRids
func (this *DbImage) SelectByRids(resultCols []string, rids []string, db *pgxpool.Conn) (rows pgx.Rows, err error) {

	return crud.YkitDbSelectByRIDs(this, resultCols, rids, db)
}

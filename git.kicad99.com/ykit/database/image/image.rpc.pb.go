// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: image.rpc.proto

package image

import (
	context "context"
	fmt "fmt"
	crud "git.kicad99.com/ykit/goutil/crud"
	proto "github.com/gogo/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

func init() { proto.RegisterFile("image.rpc.proto", fileDescriptor_c23121688e4d8c61) }

var fileDescriptor_c23121688e4d8c61 = []byte{
	// 280 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x8c, 0xd1, 0xbd, 0x4e, 0xc3, 0x30,
	0x14, 0x05, 0xe0, 0x06, 0xa9, 0x95, 0xb8, 0x08, 0x5a, 0x79, 0xcc, 0xe0, 0x85, 0xa9, 0xaa, 0xe4,
	0x46, 0xc0, 0xd2, 0x81, 0xa5, 0xca, 0x52, 0xa9, 0x15, 0xa5, 0x88, 0x85, 0xed, 0xc6, 0xb1, 0x8a,
	0xd5, 0xfc, 0xc9, 0xb9, 0x19, 0xf2, 0x16, 0x3c, 0x16, 0x63, 0x47, 0x46, 0x94, 0x3c, 0x08, 0x28,
	0x36, 0x0c, 0x20, 0x50, 0x3b, 0xde, 0x73, 0xbe, 0xe3, 0xc5, 0x30, 0xd4, 0x29, 0x6e, 0x95, 0x30,
	0x85, 0x14, 0x85, 0xc9, 0x29, 0x67, 0x7d, 0x1b, 0xf8, 0x20, 0x4d, 0x15, 0xbb, 0xc8, 0x3f, 0x73,
	0xc6, 0x1d, 0x23, 0x77, 0x24, 0xba, 0x24, 0x97, 0x5c, 0x7d, 0x9c, 0x00, 0x6c, 0x0a, 0x19, 0x46,
	0x8b, 0xae, 0x61, 0x63, 0x18, 0x2c, 0xb2, 0x52, 0x19, 0x62, 0x17, 0xc2, 0xd9, 0xaf, 0xc6, 0x1f,
	0x0a, 0xfb, 0x68, 0xb8, 0x5a, 0x6e, 0x54, 0x59, 0x25, 0xd4, 0xd1, 0xc7, 0x22, 0x46, 0x52, 0x87,
	0x69, 0x00, 0xe7, 0x6b, 0x34, 0xa4, 0x31, 0x39, 0x76, 0x31, 0x86, 0x41, 0xa8, 0x12, 0x75, 0x0c,
	0x9d, 0xc0, 0xe9, 0x83, 0x4a, 0x94, 0xa4, 0xbb, 0xac, 0xd3, 0xdf, 0xed, 0x1a, 0x0d, 0xa6, 0xfe,
	0xaf, 0x35, 0x13, 0x00, 0x0e, 0xaf, 0x30, 0xab, 0x0f, 0xe9, 0xc0, 0x63, 0x13, 0xe8, 0xdf, 0x57,
	0xca, 0xd4, 0x6c, 0xe4, 0xa8, 0x3d, 0xfe, 0xc3, 0x37, 0x00, 0xb6, 0x9f, 0x23, 0xc9, 0xe7, 0x3f,
	0x16, 0xec, 0xe7, 0x62, 0xa9, 0x4b, 0x0a, 0xbc, 0xf9, 0xed, 0x6b, 0xc3, 0xbd, 0x7d, 0xc3, 0xbd,
	0xf7, 0x86, 0x7b, 0x2f, 0x2d, 0xef, 0xed, 0x5b, 0xde, 0x7b, 0x6b, 0x79, 0xef, 0xe9, 0x72, 0xab,
	0x49, 0xec, 0xb4, 0xc4, 0x78, 0x36, 0x13, 0x32, 0x4f, 0xa7, 0xf5, 0x4e, 0xd3, 0x34, 0x46, 0xc2,
	0x08, 0x4b, 0x35, 0xb5, 0x8f, 0x45, 0x03, 0xfb, 0x8f, 0xd7, 0x9f, 0x01, 0x00, 0x00, 0xff, 0xff,
	0xc6, 0xf2, 0xcb, 0x5c, 0x0c, 0x02, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// RpcDbImageClient is the client API for RpcDbImage service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type RpcDbImageClient interface {
	//插入一行数据
	Insert(ctx context.Context, in *DbImage, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,全量修改一行数据
	Update(ctx context.Context, in *DbImage, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
	PartialUpdate(ctx context.Context, in *DbImage, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,删除一行数据
	Delete(ctx context.Context, in *DbImage, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
	SelectOne(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (*DbImage, error)
	//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
	SelectMany(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (RpcDbImage_SelectManyClient, error)
	//根据条件查询数据
	Query(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbImage_QueryClient, error)
	//根据条件查询数据,批量返回数据,crud.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbImage_QueryBatchClient, error)
}

type rpcDbImageClient struct {
	cc *grpc.ClientConn
}

func NewRpcDbImageClient(cc *grpc.ClientConn) RpcDbImageClient {
	return &rpcDbImageClient{cc}
}

func (c *rpcDbImageClient) Insert(ctx context.Context, in *DbImage, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/image.RpcDbImage/Insert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbImageClient) Update(ctx context.Context, in *DbImage, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/image.RpcDbImage/Update", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbImageClient) PartialUpdate(ctx context.Context, in *DbImage, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/image.RpcDbImage/PartialUpdate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbImageClient) Delete(ctx context.Context, in *DbImage, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/image.RpcDbImage/Delete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbImageClient) SelectOne(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (*DbImage, error) {
	out := new(DbImage)
	err := c.cc.Invoke(ctx, "/image.RpcDbImage/SelectOne", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbImageClient) SelectMany(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (RpcDbImage_SelectManyClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RpcDbImage_serviceDesc.Streams[0], "/image.RpcDbImage/SelectMany", opts...)
	if err != nil {
		return nil, err
	}
	x := &rpcDbImageSelectManyClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type RpcDbImage_SelectManyClient interface {
	Recv() (*DbImage, error)
	grpc.ClientStream
}

type rpcDbImageSelectManyClient struct {
	grpc.ClientStream
}

func (x *rpcDbImageSelectManyClient) Recv() (*DbImage, error) {
	m := new(DbImage)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *rpcDbImageClient) Query(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbImage_QueryClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RpcDbImage_serviceDesc.Streams[1], "/image.RpcDbImage/Query", opts...)
	if err != nil {
		return nil, err
	}
	x := &rpcDbImageQueryClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type RpcDbImage_QueryClient interface {
	Recv() (*DbImage, error)
	grpc.ClientStream
}

type rpcDbImageQueryClient struct {
	grpc.ClientStream
}

func (x *rpcDbImageQueryClient) Recv() (*DbImage, error) {
	m := new(DbImage)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *rpcDbImageClient) QueryBatch(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbImage_QueryBatchClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RpcDbImage_serviceDesc.Streams[2], "/image.RpcDbImage/QueryBatch", opts...)
	if err != nil {
		return nil, err
	}
	x := &rpcDbImageQueryBatchClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type RpcDbImage_QueryBatchClient interface {
	Recv() (*DbImageList, error)
	grpc.ClientStream
}

type rpcDbImageQueryBatchClient struct {
	grpc.ClientStream
}

func (x *rpcDbImageQueryBatchClient) Recv() (*DbImageList, error) {
	m := new(DbImageList)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// RpcDbImageServer is the server API for RpcDbImage service.
type RpcDbImageServer interface {
	//插入一行数据
	Insert(context.Context, *DbImage) (*crud.DMLResult, error)
	//以RID为条件,全量修改一行数据
	Update(context.Context, *DbImage) (*crud.DMLResult, error)
	//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
	PartialUpdate(context.Context, *DbImage) (*crud.DMLResult, error)
	//以RID为条件,删除一行数据
	Delete(context.Context, *DbImage) (*crud.DMLResult, error)
	//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
	SelectOne(context.Context, *crud.DMLParam) (*DbImage, error)
	//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
	SelectMany(*crud.DMLParam, RpcDbImage_SelectManyServer) error
	//根据条件查询数据
	Query(*crud.QueryParam, RpcDbImage_QueryServer) error
	//根据条件查询数据,批量返回数据,crud.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(*crud.QueryParam, RpcDbImage_QueryBatchServer) error
}

// UnimplementedRpcDbImageServer can be embedded to have forward compatible implementations.
type UnimplementedRpcDbImageServer struct {
}

func (*UnimplementedRpcDbImageServer) Insert(ctx context.Context, req *DbImage) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Insert not implemented")
}
func (*UnimplementedRpcDbImageServer) Update(ctx context.Context, req *DbImage) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Update not implemented")
}
func (*UnimplementedRpcDbImageServer) PartialUpdate(ctx context.Context, req *DbImage) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PartialUpdate not implemented")
}
func (*UnimplementedRpcDbImageServer) Delete(ctx context.Context, req *DbImage) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Delete not implemented")
}
func (*UnimplementedRpcDbImageServer) SelectOne(ctx context.Context, req *crud.DMLParam) (*DbImage, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SelectOne not implemented")
}
func (*UnimplementedRpcDbImageServer) SelectMany(req *crud.DMLParam, srv RpcDbImage_SelectManyServer) error {
	return status.Errorf(codes.Unimplemented, "method SelectMany not implemented")
}
func (*UnimplementedRpcDbImageServer) Query(req *crud.QueryParam, srv RpcDbImage_QueryServer) error {
	return status.Errorf(codes.Unimplemented, "method Query not implemented")
}
func (*UnimplementedRpcDbImageServer) QueryBatch(req *crud.QueryParam, srv RpcDbImage_QueryBatchServer) error {
	return status.Errorf(codes.Unimplemented, "method QueryBatch not implemented")
}

func RegisterRpcDbImageServer(s *grpc.Server, srv RpcDbImageServer) {
	s.RegisterService(&_RpcDbImage_serviceDesc, srv)
}

func _RpcDbImage_Insert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbImage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbImageServer).Insert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/image.RpcDbImage/Insert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbImageServer).Insert(ctx, req.(*DbImage))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbImage_Update_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbImage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbImageServer).Update(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/image.RpcDbImage/Update",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbImageServer).Update(ctx, req.(*DbImage))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbImage_PartialUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbImage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbImageServer).PartialUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/image.RpcDbImage/PartialUpdate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbImageServer).PartialUpdate(ctx, req.(*DbImage))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbImage_Delete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbImage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbImageServer).Delete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/image.RpcDbImage/Delete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbImageServer).Delete(ctx, req.(*DbImage))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbImage_SelectOne_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(crud.DMLParam)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbImageServer).SelectOne(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/image.RpcDbImage/SelectOne",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbImageServer).SelectOne(ctx, req.(*crud.DMLParam))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbImage_SelectMany_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.DMLParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RpcDbImageServer).SelectMany(m, &rpcDbImageSelectManyServer{stream})
}

type RpcDbImage_SelectManyServer interface {
	Send(*DbImage) error
	grpc.ServerStream
}

type rpcDbImageSelectManyServer struct {
	grpc.ServerStream
}

func (x *rpcDbImageSelectManyServer) Send(m *DbImage) error {
	return x.ServerStream.SendMsg(m)
}

func _RpcDbImage_Query_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.QueryParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RpcDbImageServer).Query(m, &rpcDbImageQueryServer{stream})
}

type RpcDbImage_QueryServer interface {
	Send(*DbImage) error
	grpc.ServerStream
}

type rpcDbImageQueryServer struct {
	grpc.ServerStream
}

func (x *rpcDbImageQueryServer) Send(m *DbImage) error {
	return x.ServerStream.SendMsg(m)
}

func _RpcDbImage_QueryBatch_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.QueryParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RpcDbImageServer).QueryBatch(m, &rpcDbImageQueryBatchServer{stream})
}

type RpcDbImage_QueryBatchServer interface {
	Send(*DbImageList) error
	grpc.ServerStream
}

type rpcDbImageQueryBatchServer struct {
	grpc.ServerStream
}

func (x *rpcDbImageQueryBatchServer) Send(m *DbImageList) error {
	return x.ServerStream.SendMsg(m)
}

var _RpcDbImage_serviceDesc = grpc.ServiceDesc{
	ServiceName: "image.RpcDbImage",
	HandlerType: (*RpcDbImageServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Insert",
			Handler:    _RpcDbImage_Insert_Handler,
		},
		{
			MethodName: "Update",
			Handler:    _RpcDbImage_Update_Handler,
		},
		{
			MethodName: "PartialUpdate",
			Handler:    _RpcDbImage_PartialUpdate_Handler,
		},
		{
			MethodName: "Delete",
			Handler:    _RpcDbImage_Delete_Handler,
		},
		{
			MethodName: "SelectOne",
			Handler:    _RpcDbImage_SelectOne_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "SelectMany",
			Handler:       _RpcDbImage_SelectMany_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "Query",
			Handler:       _RpcDbImage_Query_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "QueryBatch",
			Handler:       _RpcDbImage_QueryBatch_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "image.rpc.proto",
}

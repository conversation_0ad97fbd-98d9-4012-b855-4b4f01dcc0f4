// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: image.proto

package image

import (
	fmt "fmt"
	proto "github.com/gogo/protobuf/proto"
	io "io"
	math "math"
	math_bits "math/bits"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

//用户的一些头像图片数据,地图点icon等
//@rpc crud
//@dbpost CREATE INDEX IF NOT EXISTS idxDbImageOrgRID on DbImage USING hash (OrgRID)
type DbImage struct {
	//@db uuid primary key
	//行ID
	RID string `protobuf:"bytes,1,opt,name=RID,proto3" json:"RID,omitempty" ykit:"prk"`
	//@db uuid REFERENCES DbOrg(RID) ON DELETE CASCADE
	//所属的群组
	OrgRID string `protobuf:"bytes,2,opt,name=OrgRID,proto3" json:"OrgRID,omitempty" ykit:"null"`
	//@db text
	//文件名
	FileName string `protobuf:"bytes,4,opt,name=FileName,proto3" json:"FileName,omitempty" ykit:"null"`
	//@db text
	//文件内容,经过base64编码,就是html img的dataurl
	FileContent string `protobuf:"bytes,5,opt,name=FileContent,proto3" json:"FileContent,omitempty" ykit:"null"`
	//@db text
	//文件内容的hash=base64(sha256(file_content_binary)
	Hash string `protobuf:"bytes,6,opt,name=Hash,proto3" json:"Hash,omitempty" ykit:"null"`
	//@db uuid
	//添加的用户RID
	AddUserRID string `protobuf:"bytes,7,opt,name=AddUserRID,proto3" json:"AddUserRID,omitempty" ykit:"null"`
	//@db timestamp not null default now_utc()
	//数据最后修改时间
	UpdatedAt string `protobuf:"bytes,14,opt,name=UpdatedAt,proto3" json:"UpdatedAt,omitempty" ykit:"notnull"`
	//@db text
	//数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
	UpdatedDC string `protobuf:"bytes,15,opt,name=UpdatedDC,proto3" json:"UpdatedDC,omitempty" ykit:"null"`
}

func (m *DbImage) Reset()         { *m = DbImage{} }
func (m *DbImage) String() string { return proto.CompactTextString(m) }
func (*DbImage) ProtoMessage()    {}
func (*DbImage) Descriptor() ([]byte, []int) {
	return fileDescriptor_9624c68e2b547544, []int{0}
}
func (m *DbImage) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbImage) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbImage.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbImage) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbImage.Merge(m, src)
}
func (m *DbImage) XXX_Size() int {
	return m.Size()
}
func (m *DbImage) XXX_DiscardUnknown() {
	xxx_messageInfo_DbImage.DiscardUnknown(m)
}

var xxx_messageInfo_DbImage proto.InternalMessageInfo

func (m *DbImage) GetRID() string {
	if m != nil {
		return m.RID
	}
	return ""
}

func (m *DbImage) GetOrgRID() string {
	if m != nil {
		return m.OrgRID
	}
	return ""
}

func (m *DbImage) GetFileName() string {
	if m != nil {
		return m.FileName
	}
	return ""
}

func (m *DbImage) GetFileContent() string {
	if m != nil {
		return m.FileContent
	}
	return ""
}

func (m *DbImage) GetHash() string {
	if m != nil {
		return m.Hash
	}
	return ""
}

func (m *DbImage) GetAddUserRID() string {
	if m != nil {
		return m.AddUserRID
	}
	return ""
}

func (m *DbImage) GetUpdatedAt() string {
	if m != nil {
		return m.UpdatedAt
	}
	return ""
}

func (m *DbImage) GetUpdatedDC() string {
	if m != nil {
		return m.UpdatedDC
	}
	return ""
}

func init() {
	proto.RegisterType((*DbImage)(nil), "image.DbImage")
}

func init() { proto.RegisterFile("image.proto", fileDescriptor_9624c68e2b547544) }

var fileDescriptor_9624c68e2b547544 = []byte{
	// 244 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe2, 0xe2, 0xce, 0xcc, 0x4d, 0x4c,
	0x4f, 0xd5, 0x2b, 0x28, 0xca, 0x2f, 0xc9, 0x17, 0x62, 0x05, 0x73, 0x94, 0x1e, 0x32, 0x72, 0xb1,
	0xbb, 0x24, 0x79, 0x82, 0xd8, 0x42, 0x02, 0x5c, 0xcc, 0x41, 0x9e, 0x2e, 0x12, 0x8c, 0x0a, 0x8c,
	0x1a, 0x9c, 0x41, 0x20, 0xa6, 0x90, 0x18, 0x17, 0x9b, 0x7f, 0x51, 0x3a, 0x48, 0x90, 0x09, 0x2c,
	0x08, 0xe5, 0x09, 0x49, 0x71, 0x71, 0xb8, 0x65, 0xe6, 0xa4, 0xfa, 0x25, 0xe6, 0xa6, 0x4a, 0xb0,
	0x80, 0x65, 0xe0, 0x7c, 0x21, 0x05, 0x2e, 0x6e, 0x10, 0xdb, 0x39, 0x3f, 0xaf, 0x24, 0x35, 0xaf,
	0x44, 0x82, 0x15, 0x2c, 0x8d, 0x2c, 0x24, 0x24, 0xc4, 0xc5, 0xe2, 0x91, 0x58, 0x9c, 0x21, 0xc1,
	0x06, 0x96, 0x02, 0xb3, 0x85, 0xe4, 0xb8, 0xb8, 0x1c, 0x53, 0x52, 0x42, 0x8b, 0x53, 0x8b, 0x40,
	0xb6, 0xb1, 0x83, 0x65, 0x90, 0x44, 0x84, 0x64, 0xb8, 0x38, 0x43, 0x0b, 0x52, 0x12, 0x4b, 0x52,
	0x53, 0x1c, 0x4b, 0x24, 0xf8, 0xc0, 0xd2, 0x08, 0x01, 0x24, 0x59, 0x17, 0x67, 0x09, 0x7e, 0x14,
	0x59, 0x17, 0x67, 0x27, 0xdb, 0x13, 0x8f, 0xe4, 0x18, 0x2f, 0x3c, 0x92, 0x63, 0x7c, 0xf0, 0x48,
	0x8e, 0x71, 0xc2, 0x63, 0x39, 0x86, 0x0b, 0x8f, 0xe5, 0x18, 0x6e, 0x3c, 0x96, 0x63, 0x88, 0x52,
	0x4e, 0xcf, 0x2c, 0xd1, 0xcb, 0xce, 0x4c, 0x4e, 0x4c, 0xb1, 0xb4, 0xd4, 0x4b, 0xce, 0xcf, 0xd5,
	0xaf, 0xcc, 0xce, 0x2c, 0xd1, 0x4f, 0x49, 0x2c, 0x49, 0x4c, 0x4a, 0x2c, 0x4e, 0xd5, 0x07, 0x07,
	0x51, 0x12, 0x1b, 0x38, 0xc0, 0x8c, 0x01, 0x01, 0x00, 0x00, 0xff, 0xff, 0x46, 0x21, 0xd5, 0x8e,
	0x3f, 0x01, 0x00, 0x00,
}

func (m *DbImage) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbImage) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbImage) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.UpdatedDC) > 0 {
		i -= len(m.UpdatedDC)
		copy(dAtA[i:], m.UpdatedDC)
		i = encodeVarintImage(dAtA, i, uint64(len(m.UpdatedDC)))
		i--
		dAtA[i] = 0x7a
	}
	if len(m.UpdatedAt) > 0 {
		i -= len(m.UpdatedAt)
		copy(dAtA[i:], m.UpdatedAt)
		i = encodeVarintImage(dAtA, i, uint64(len(m.UpdatedAt)))
		i--
		dAtA[i] = 0x72
	}
	if len(m.AddUserRID) > 0 {
		i -= len(m.AddUserRID)
		copy(dAtA[i:], m.AddUserRID)
		i = encodeVarintImage(dAtA, i, uint64(len(m.AddUserRID)))
		i--
		dAtA[i] = 0x3a
	}
	if len(m.Hash) > 0 {
		i -= len(m.Hash)
		copy(dAtA[i:], m.Hash)
		i = encodeVarintImage(dAtA, i, uint64(len(m.Hash)))
		i--
		dAtA[i] = 0x32
	}
	if len(m.FileContent) > 0 {
		i -= len(m.FileContent)
		copy(dAtA[i:], m.FileContent)
		i = encodeVarintImage(dAtA, i, uint64(len(m.FileContent)))
		i--
		dAtA[i] = 0x2a
	}
	if len(m.FileName) > 0 {
		i -= len(m.FileName)
		copy(dAtA[i:], m.FileName)
		i = encodeVarintImage(dAtA, i, uint64(len(m.FileName)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.OrgRID) > 0 {
		i -= len(m.OrgRID)
		copy(dAtA[i:], m.OrgRID)
		i = encodeVarintImage(dAtA, i, uint64(len(m.OrgRID)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.RID) > 0 {
		i -= len(m.RID)
		copy(dAtA[i:], m.RID)
		i = encodeVarintImage(dAtA, i, uint64(len(m.RID)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func encodeVarintImage(dAtA []byte, offset int, v uint64) int {
	offset -= sovImage(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *DbImage) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.RID)
	if l > 0 {
		n += 1 + l + sovImage(uint64(l))
	}
	l = len(m.OrgRID)
	if l > 0 {
		n += 1 + l + sovImage(uint64(l))
	}
	l = len(m.FileName)
	if l > 0 {
		n += 1 + l + sovImage(uint64(l))
	}
	l = len(m.FileContent)
	if l > 0 {
		n += 1 + l + sovImage(uint64(l))
	}
	l = len(m.Hash)
	if l > 0 {
		n += 1 + l + sovImage(uint64(l))
	}
	l = len(m.AddUserRID)
	if l > 0 {
		n += 1 + l + sovImage(uint64(l))
	}
	l = len(m.UpdatedAt)
	if l > 0 {
		n += 1 + l + sovImage(uint64(l))
	}
	l = len(m.UpdatedDC)
	if l > 0 {
		n += 1 + l + sovImage(uint64(l))
	}
	return n
}

func sovImage(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozImage(x uint64) (n int) {
	return sovImage(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *DbImage) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowImage
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DbImage: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DbImage: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImage
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthImage
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthImage
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.RID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OrgRID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImage
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthImage
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthImage
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OrgRID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field FileName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImage
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthImage
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthImage
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.FileName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field FileContent", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImage
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthImage
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthImage
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.FileContent = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Hash", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImage
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthImage
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthImage
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Hash = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AddUserRID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImage
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthImage
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthImage
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AddUserRID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 14:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UpdatedAt", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImage
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthImage
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthImage
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UpdatedAt = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 15:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UpdatedDC", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowImage
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthImage
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthImage
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UpdatedDC = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipImage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthImage
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthImage
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipImage(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowImage
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowImage
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowImage
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthImage
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupImage
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthImage
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthImage        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowImage          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupImage = fmt.Errorf("proto: unexpected end of group")
)

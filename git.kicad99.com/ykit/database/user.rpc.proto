//generated by ygen-gocrud-proto. DO NOT EDIT.
//source: user.proto
syntax = "proto3";
package user;

import "crud.proto";
import "user.proto"; 
import "user.list.proto"; 
option go_package="git.kicad99.com/ykit/database/user";

//普通crud，没有权限检查
service   RpcDbUser {
//插入一行数据
rpc Insert( DbUser ) returns (crud.DMLResult);

//以RID为条件,全量修改一行数据
rpc Update( DbUser ) returns (crud.DMLResult);

//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
rpc PartialUpdate( DbUser ) returns (crud.DMLResult);

//以RID为条件,删除一行数据
rpc Delete( DbUser ) returns (crud.DMLResult);

//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
rpc SelectOne( crud.DMLParam ) returns ( DbUser );

//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
rpc SelectMany( crud.DMLParam ) returns ( stream DbUser );

//根据条件查询数据
rpc Query( crud.QueryParam ) returns ( stream DbUser );
//根据条件查询数据,批量返回数据,crud.QueryParam.Batch指定一次返回多少条数据
rpc QueryBatch( crud.QueryParam ) returns ( stream DbUserList );
}

//带权限检查(有相应的群组权限和编辑权限)的crud
service   PrpcDbUser {
//插入一行数据
rpc Insert( DbUser ) returns (crud.DMLResult);

//以RID为条件,全量修改一行数据
rpc Update( DbUser ) returns (crud.DMLResult);

//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
rpc PartialUpdate( DbUser ) returns (crud.DMLResult);

//以RID为条件,删除一行数据
rpc Delete( DbUser ) returns (crud.DMLResult);

//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
rpc SelectOne( crud.DMLParam ) returns ( DbUser );

//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
rpc SelectMany( crud.DMLParam ) returns ( stream DbUser );

//根据条件取得我有权限的数据
rpc Query( crud.PrivilegeParam ) returns ( stream DbUser );
//根据条件取得我有权限的数据,批量返回数据,crud.PrivilegeParam.QueryParam.Batch指定一次返回多少条数据
rpc QueryBatch( crud.PrivilegeParam ) returns ( stream DbUserList );
}

//普通crud，没有权限检查
service   RpcDbUserRole {
//插入一行数据
rpc Insert( DbUserRole ) returns (crud.DMLResult);

//以RID为条件,全量修改一行数据
rpc Update( DbUserRole ) returns (crud.DMLResult);

//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
rpc PartialUpdate( DbUserRole ) returns (crud.DMLResult);

//以RID为条件,删除一行数据
rpc Delete( DbUserRole ) returns (crud.DMLResult);

//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
rpc SelectOne( crud.DMLParam ) returns ( DbUserRole );

//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
rpc SelectMany( crud.DMLParam ) returns ( stream DbUserRole );

//根据条件查询数据
rpc Query( crud.QueryParam ) returns ( stream DbUserRole );
//根据条件查询数据,批量返回数据,crud.QueryParam.Batch指定一次返回多少条数据
rpc QueryBatch( crud.QueryParam ) returns ( stream DbUserRoleList );
}

//带权限检查(有相应的群组权限和编辑权限)的crud
service   PrpcDbUserRole {
//插入一行数据
rpc Insert( DbUserRole ) returns (crud.DMLResult);

//以RID为条件,全量修改一行数据
rpc Update( DbUserRole ) returns (crud.DMLResult);

//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
rpc PartialUpdate( DbUserRole ) returns (crud.DMLResult);

//以RID为条件,删除一行数据
rpc Delete( DbUserRole ) returns (crud.DMLResult);

//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
rpc SelectOne( crud.DMLParam ) returns ( DbUserRole );

//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
rpc SelectMany( crud.DMLParam ) returns ( stream DbUserRole );

//根据条件取得我有权限的数据
rpc Query( crud.PrivilegeParam ) returns ( stream DbUserRole );
//根据条件取得我有权限的数据,批量返回数据,crud.PrivilegeParam.QueryParam.Batch指定一次返回多少条数据
rpc QueryBatch( crud.PrivilegeParam ) returns ( stream DbUserRoleList );
}

-- root org
--INSERT INTO dborg(rid, orgid, orgtype, sortvalue, shortname, fullname, note, setting, OrgRID, updatedat, updateddc)VALUES('00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', 0, 100, 'root', 'root', '', '{}'::jsonb, NULL, now_utc(), '')  ON CONFLICT  DO NOTHING;

-- user ttt
--INSERT INTO dbuser(rid, orgrid, userid, usertype, username, phone, image, setting, loginname, loginpass, updatedat, updateddc)VALUES('00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', 'ttt', 0, 'ttt', '', NULL, '{}'::jsonb, 'ttt', 'UwhpDj0xMGNzZw8sXYZOLGBHmH/q9Wxf7aB6/EbT43Q=', now_utc(), '') ON CONFLICT  DO NOTHING;

--ttt privilege org root
--INSERT INTO dbuserorgprivilege(rid, userrid, orgrid, includechildren, setting, updatedat, updateddc)VALUES('00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', 1, '{}'::jsonb, now_utc(), '') ON CONFLICT  DO NOTHING;

--depfile role-init.sql
--depfile user.sql
-- user ttt add built-in role
INSERT INTO dbuserrole(rid, orgrid, userrid, rolerid, updatedat, updateddc)
VALUES ('00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000',
        '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', '2020-01-01 00:00:00',
        '')ON CONFLICT  DO NOTHING;
INSERT INTO dbuserrole(rid, orgrid, userrid, rolerid, updatedat, updateddc)
VALUES ('11111111-1111-1111-1111-111111111111', '00000000-0000-0000-0000-000000000000',
        '00000000-0000-0000-0000-000000000000', '11111111-1111-1111-1111-111111111111', '2020-01-01 00:00:00',
        '')ON CONFLICT  DO NOTHING;
INSERT INTO dbuserrole(rid, orgrid, userrid, rolerid, updatedat, updateddc)
VALUES ('22222222-2222-2222-2222-222222222222', '00000000-0000-0000-0000-000000000000',
        '00000000-0000-0000-0000-000000000000', '22222222-2222-2222-2222-222222222222', '2020-01-01 00:00:00',
        '')ON CONFLICT  DO NOTHING;
INSERT INTO dbuserrole(rid, orgrid, userrid, rolerid, updatedat, updateddc)
VALUES ('33333333-3333-3333-3333-333333333333', '00000000-0000-0000-0000-000000000000',
        '00000000-0000-0000-0000-000000000000', '33333333-3333-3333-3333-333333333333', '2020-01-01 00:00:00',
        '')ON CONFLICT  DO NOTHING;
INSERT INTO dbuserrole(rid, orgrid, userrid, rolerid, updatedat, updateddc)
VALUES ('44444444-4444-4444-4444-444444444444', '00000000-0000-0000-0000-000000000000',
        '00000000-0000-0000-0000-000000000000', '44444444-4444-4444-4444-444444444444', '2020-01-01 00:00:00',
        '')ON CONFLICT  DO NOTHING;
INSERT INTO dbuserrole(rid, orgrid, userrid, rolerid, updatedat, updateddc)
VALUES ('55555555-5555-5555-5555-555555555555', '00000000-0000-0000-0000-000000000000',
        '00000000-0000-0000-0000-000000000000', '55555555-5555-5555-5555-555555555555', '2020-01-01 00:00:00',
        '')ON CONFLICT  DO NOTHING;

SELECT * FROM dbrole WHERE ((SELECT rid FROM dbrole WHERE rid='00000000-0000-0000-0000-000000000000' and creator='00000000-0000-0000-0000-000000000000')='00000000-0000-0000-0000-000000000000' OR
                            (SELECT rolerid FROM dbuserrole WHERE rolerid='00000000-0000-0000-0000-000000000000' AND userrid='00000000-0000-0000-0000-000000000000')='00000000-0000-0000-0000-000000000000')
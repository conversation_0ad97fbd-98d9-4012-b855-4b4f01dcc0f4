--取得我有权限的所有群组OrgRID
create or replace function myorg(uuid) returns setof uuid AS $$
with RECURSIVE sub_org as (
select OrgRID as OrgRIDs from DbUserOrgPrivilege where UserRID= $1 and IncludeChildren=1
union
select rid as OrgRIDs from DbOrg
join sub_org d_o_r
on DbOrg.OrgRID=d_o_r.OrgRIDs
)

select OrgRIDs as OrgRID from sub_org
union
select OrgRID as OrgRID from DbUserOrgPrivilege where UserRID=$1 and IncludeChildren=0
union
select rid as OrgRID from dborg where CreatorRID = $1
$$ LANGUAGE SQL STABLE;



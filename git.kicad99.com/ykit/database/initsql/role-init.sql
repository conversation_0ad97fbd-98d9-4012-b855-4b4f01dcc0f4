--depfile org.sql
-- build-in role,do not edit these
INSERT INTO dbrole
(rid, orgrid, rolename, creator, isbuiltin, setting, sortvalue, updatedat, updateddc)
VALUES ('00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', 'admin',
        null, 1,
        '{}', 0, '2020-01-01 00:00:00', '') ON CONFLICT  DO NOTHING;
INSERT INTO dbrole
(rid, orgrid, rolename, creator, isbuiltin, setting, sortvalue, updatedat, updateddc)
VALUES ('11111111-1111-1111-1111-111111111111', '00000000-0000-0000-0000-000000000000', 'data.manager',
        null, 1, '{}',
        0, '2020-01-01 00:00:00', '') ON CONFLICT  DO NOTHING;
INSERT INTO dbrole
(rid, orgrid, rolename, creator, isbuiltin, setting, sortvalue, updatedat, updateddc)
VALUES ('22222222-2222-2222-2222-222222222222', '00000000-0000-0000-0000-000000000000', 'dev.history',
        null, 1, '{}',
        0, '2020-01-01 00:00:00', '') ON CONFLICT  DO NOTHING;

INSERT INTO dbrole
(rid, orgrid, rolename, creator, isbuiltin, setting, sortvalue, updatedat, updateddc)
VALUES ('33333333-3333-3333-3333-333333333333', '00000000-0000-0000-0000-000000000000', 'opt.history',
        null, 1, '{}', 0,
        '2020-01-01 00:00:00', '') ON CONFLICT  DO NOTHING;

INSERT INTO dbrole
(rid, orgrid, rolename, creator, isbuiltin, setting, sortvalue, updatedat, updateddc)
VALUES ('44444444-4444-4444-4444-444444444444', '00000000-0000-0000-0000-000000000000', 'maintain',
        null, 1, '{}', 0,
        '2020-01-01 00:00:00', '') ON CONFLICT  DO NOTHING;

INSERT INTO dbrole
(rid, orgrid, rolename, creator, isbuiltin, setting, sortvalue, updatedat, updateddc)
VALUES ('55555555-5555-5555-5555-555555555555', '00000000-0000-0000-0000-000000000000', 'basic',
        null, 1, '{}', 0,
        '2020-01-01 00:00:00', '') ON CONFLICT  DO NOTHING;



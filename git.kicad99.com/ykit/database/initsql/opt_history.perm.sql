--depfile role-init.sql
--depfile external_perm.sql

-- opt.history
INSERT INTO dbrolepermission(rid, rolerid, permissionrid, creator, updatedat, updateddc)
VALUES ('53a29e5e-d215-4e09-90cc-8e2f5d5982bb', '33333333-3333-3333-3333-333333333333',
        '713ab673-68db-4ca2-6e21-49a5b3bbbacf', null, '2020-01-01 00:00:00',
        '')ON CONFLICT  DO NOTHING;
INSERT INTO dbrolepermission(rid, rolerid, permissionrid, creator, updatedat, updateddc)
VALUES ('f81d318d-4fbc-4784-adc1-f5d705b3a6b9', '33333333-3333-3333-3333-333333333333',
        '728162c8-dad5-f164-2601-fe469ff208df', null, '2020-01-01 00:00:00',
        '')ON CONFLICT  DO NOTHING;
INSERT INTO dbrolepermission(rid, rolerid, permissionrid, creator, updatedat, updateddc)
VALUES ('f64fcf37-4e38-4323-a4e4-1358f904b599', '33333333-3333-3333-3333-333333333333',
        '6b0fbc70-cb4a-f8dc-4a3e-0c40b640231f', null, '2020-01-01 00:00:00',
        '')ON CONFLICT  DO NOTHING;
INSERT INTO dbrolepermission(rid, rolerid, permissionrid, creator, updatedat, updateddc)
VALUES ('849617c6-642e-4f5c-b031-a1d26f228a9f', '33333333-3333-3333-3333-333333333333',
        '713ab673-68db-4ca2-6e21-f9a5b3bbbacc', null, '2020-01-01 00:00:00',
        '')ON CONFLICT  DO NOTHING;
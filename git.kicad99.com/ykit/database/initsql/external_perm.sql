--depfile userPermission.perm.sql
--depfile userOrgPrivilege.perm.sql
--depfile org.perm.sql
--depfile user.perm.sql
--depfile crudlog.perm.sql
--depfile config.perm.sql

-- menu permission
INSERT INTO dbpermission(rid, permissiontype, permissionname, permissionvalue, updatedat, updateddc)
VALUES ('af4acd2f-7864-e6ab-c73d-a857ebe138db', 'menu', 'Data.DbOrg', 'Data.DbOrg', '2020-01-01 00:00:00',
        '')ON CONFLICT  DO NOTHING;
INSERT INTO dbpermission(rid, permissiontype, permissionname, permissionvalue, updatedat, updateddc)
VALUES ('2ef3c38f-576e-93fb-e088-23eb3eb284be', 'menu', 'Data.DbUser', 'Data.DbUser', '2020-01-01 00:00:00',
        '')ON CONFLICT  DO NOTHING;
INSERT INTO dbpermission(rid, permissiontype, permissionname, permissionvalue, updatedat, updateddc)
VALUES ('cdd2ce25-e6bf-bd46-2968-5e5fd90a489a', 'menu', 'Data.DbRole', 'Data.DbRole', '2020-01-01 00:00:00',
        '')ON CONFLICT  DO NOTHING;
INSERT INTO dbpermission(rid, permissiontype, permissionname, permissionvalue, updatedat, updateddc)
VALUES ('8ae5192c-6da3-cc88-c7df-6ba1738fbf74', 'menu', 'Data.DbController', 'Data.DbController',
        '2020-01-01 00:00:00', '')ON CONFLICT  DO NOTHING;
INSERT INTO dbpermission(rid, permissiontype, permissionname, permissionvalue, updatedat, updateddc)
VALUES ('c152bb71-02be-3f3d-ff7f-8845eac8818e', 'menu', 'Data.DbBysMarker', 'Data.DbBysMarker',
        '2020-01-01 00:00:00', '')ON CONFLICT  DO NOTHING;
INSERT INTO dbpermission(rid, permissiontype, permissionname, permissionvalue, updatedat, updateddc)
VALUES ('7b5badfe-4ea5-1ddf-3bce-a8722967af3f', 'menu', 'Data.DbMediaInfo', 'Data.DbMediaInfo',
        '2020-01-01 00:00:00', '')ON CONFLICT  DO NOTHING;
INSERT INTO dbpermission(rid, permissiontype, permissionname, permissionvalue, updatedat, updateddc)
VALUES ('2ab3829e-eee6-ec7f-5180-ce35b5446f7e', 'menu', 'Query.DbControllerOnlineHistory',
        'Query.DbControllerOnlineHistory',
        '2020-01-01 00:00:00', '')ON CONFLICT  DO NOTHING;
INSERT INTO dbpermission(rid, permissiontype, permissionname, permissionvalue, updatedat, updateddc)
VALUES ('fe6b80b7-baef-7caf-f25e-f1876ffbafa9', 'menu', 'Query.DbMarkerHistory', 'Query.DbMarkerHistory',
        '2020-01-01 00:00:00', '')ON CONFLICT  DO NOTHING;
INSERT INTO dbpermission(rid, permissiontype, permissionname, permissionvalue, updatedat, updateddc)
VALUES ('713ab673-68db-4ca2-6e21-49a5b3bbbacf', 'menu', 'Query.crudlog', 'Query.crudlog',
        '2020-01-01 00:00:00', '')ON CONFLICT  DO NOTHING;

-- cmd permission
INSERT INTO dbpermission(rid, permissiontype, permissionname, permissionvalue, updatedat, updateddc)
VALUES ('0e2b33b0-bff0-11ea-b496-bb3aafdcfca4', 'cmd', 'Sys.Maintain', 'Sys.Maintain', '2020-01-01 00:00:00',
        '') ON CONFLICT  DO NOTHING;
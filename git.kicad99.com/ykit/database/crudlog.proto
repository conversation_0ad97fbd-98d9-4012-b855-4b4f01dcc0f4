syntax = "proto3";

package crudlog;

option go_package = "git.kicad99.com/ykit/database/crudlog";

//用户crud log表
//@rpc crud pcrud
//@dbend PARTITION BY RANGE (UpdatedAt)
//@dbpost CREATE INDEX IF NOT EXISTS idxDbCrudLogOrgRID on DbCrudLog USING hash(OrgRID);
//@dbpost CREATE INDEX IF NOT EXISTS idxDbCrudLogUpdatedAt on DbCrudLog USING brin(UpdatedAt);
message DbCrudLog {
  //@db uuid 
  //用户所属的群组
  string OrgRID = 2;

  //@db uuid
  //用户rid
  string UserRID = 3;

  //@db text
  //操作
  string Operation = 4;

  //@db jsonb
  //req proto json
  string Req = 5;

  //@db text
  //req option
  string ReqOption = 6;

  //@db text
  //ipinfo
  string Ipinfo = 7;

  //@db jsonb
  //note
  string Note = 8;

  //@db timestamp not null default now_utc()
  //数据最后修改时间
  string UpdatedAt = 14;

  //@db text
  //数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
  string UpdatedDC = 15;
}

syntax = "proto3";

package user;

option go_package = "git.kicad99.com/ykit/database/user";

//所有的权限信息表,此表信息为预置，一般不给删除
//@rpc crud
//@dbpost CREATE INDEX IF NOT EXISTS idxDbPermissionPermissionValue on DbPermission USING hash(PermissionValue);
message DbPermission {
  //@db uuid primary key
  //行ID,permission rid
  string RID = 1;

  //@db text not null
  //权限类别
  string PermissionType = 3;

  //@db text not null
  //permission 名称
  string PermissionName = 4;

  //@db text not null
  //permission value
  string PermissionValue = 10;

  //@db timestamp not null default now_utc()
  //数据最后修改时间
  string UpdatedAt = 14;

  //@db text
  //数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
  string UpdatedDC = 15;
}

//所有的角色信息表
//@rpc crud pcrud
//@dbpost insert into DbRole (rid, orgrid, rolename, creator, isbuiltin, setting, sortvalue, updatedat, updateddc) VALUES('*************-5555-5555-************', '00000000-0000-0000-0000-000000000000', 'base', null, 1, '{}'::jsonb, 0, now_utc(), '') ON CONFLICT  DO NOTHING;
//@dbpost insert into DbRole (rid, orgrid, rolename, creator, isbuiltin, setting, sortvalue, updatedat, updateddc) VALUES('00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', 'admin', null, 1, '{}'::jsonb, 0, now_utc(), '') ON CONFLICT  DO NOTHING;
message DbRole {
  //@db uuid primary key
  //行ID,role rid
  string RID = 1;

  //@db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
  //用户所属的群组
  string OrgRID = 2;

  //@db text not null
  //role 名称
  string RoleName = 4;

  //@db uuid --REFERENCES DbUser(RID) ON DELETE set null
  //role 创建者 rid
  string Creator = 5;

  //@db int
  //是否是内置的角色，内置角色不能删除
  int32 IsBuiltIn = 6;

  //@db jsonb not null default  '{}'::jsonb
  // 其它设置，可以在里面扩展需要用到的其它信息
  string Setting = 9;

  //@db int default 100
  //sort value
  int32 SortValue = 10;

  //@db timestamp not null default now_utc()
  //数据最后修改时间
  string UpdatedAt = 14;

  //@db text
  //数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
  string UpdatedDC = 15;
}

//角色权限信息表
//@rpc crud pcrud
message DbRolePermission {
  //@db uuid primary key
  //行ID
  string RID = 1;

  //@db uuid not null REFERENCES DbRole(RID) ON DELETE CASCADE
  //role rid
  string RoleRID = 2;

  //@db uuid not null REFERENCES DbPermission(RID) ON DELETE CASCADE
  //permission rid
  string PermissionRID = 4;

  //@db uuid --REFERENCES DbUser(RID) ON DELETE set null
  //role 创建者 rid
  string Creator = 5;

  //@db timestamp not null default now_utc()
  //数据最后修改时间
  string UpdatedAt = 14;

  //@db text
  //数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
  string UpdatedDC = 15;
}
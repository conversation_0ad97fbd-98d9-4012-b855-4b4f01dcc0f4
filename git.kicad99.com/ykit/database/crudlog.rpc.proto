//generated by ygen-gocrud-proto. DO NOT EDIT.
//source: crudlog.proto
syntax = "proto3";
package crudlog;

import "crud.proto";
import "crudlog.proto"; 
import "crudlog.list.proto"; 
option go_package="git.kicad99.com/ykit/database/crudlog";

//普通crud，没有权限检查
service   RpcDbCrudLog {
//插入一行数据
rpc Insert( DbCrudLog ) returns (crud.DMLResult);

//根据条件查询数据
rpc Query( crud.QueryParam ) returns ( stream DbCrudLog );
//根据条件查询数据,批量返回数据,crud.QueryParam.Batch指定一次返回多少条数据
rpc QueryBatch( crud.QueryParam ) returns ( stream DbCrudLogList );
}

//带权限检查(有相应的群组权限和编辑权限)的crud
service   PrpcDbCrudLog {
//插入一行数据
rpc Insert( DbCrudLog ) returns (crud.DMLResult);

//根据条件取得我有权限的数据
rpc Query( crud.PrivilegeParam ) returns ( stream DbCrudLog );
//根据条件取得我有权限的数据,批量返回数据,crud.PrivilegeParam.QueryParam.Batch指定一次返回多少条数据
rpc QueryBatch( crud.PrivilegeParam ) returns ( stream DbCrudLogList );
}

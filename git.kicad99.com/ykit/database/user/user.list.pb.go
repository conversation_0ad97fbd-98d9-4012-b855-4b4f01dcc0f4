// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: user.list.proto

package user

import (
	fmt "fmt"
	proto "github.com/gogo/protobuf/proto"
	io "io"
	math "math"
	math_bits "math/bits"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

// DbUser list
type DbUserList struct {
	//批次号，从0开始
	BatchNo uint32 `protobuf:"varint,1,opt,name=BatchNo,proto3" json:"BatchNo,omitempty" ykit:"nodb"`
	//rows offset,从0开始
	RowsOffset uint32 `protobuf:"varint,2,opt,name=RowsOffset,proto3" json:"RowsOffset,omitempty" ykit:"nodb"`
	//本批次数据
	Rows []*DbUser `protobuf:"bytes,3,rep,name=Rows,proto3" json:"Rows,omitempty" ykit:"nodb"`
}

func (m *DbUserList) Reset()         { *m = DbUserList{} }
func (m *DbUserList) String() string { return proto.CompactTextString(m) }
func (*DbUserList) ProtoMessage()    {}
func (*DbUserList) Descriptor() ([]byte, []int) {
	return fileDescriptor_b49940f0e3d00c0c, []int{0}
}
func (m *DbUserList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbUserList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbUserList.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbUserList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbUserList.Merge(m, src)
}
func (m *DbUserList) XXX_Size() int {
	return m.Size()
}
func (m *DbUserList) XXX_DiscardUnknown() {
	xxx_messageInfo_DbUserList.DiscardUnknown(m)
}

var xxx_messageInfo_DbUserList proto.InternalMessageInfo

func (m *DbUserList) GetBatchNo() uint32 {
	if m != nil {
		return m.BatchNo
	}
	return 0
}

func (m *DbUserList) GetRowsOffset() uint32 {
	if m != nil {
		return m.RowsOffset
	}
	return 0
}

func (m *DbUserList) GetRows() []*DbUser {
	if m != nil {
		return m.Rows
	}
	return nil
}

// DbUserRole list
type DbUserRoleList struct {
	//批次号，从0开始
	BatchNo uint32 `protobuf:"varint,1,opt,name=BatchNo,proto3" json:"BatchNo,omitempty" ykit:"nodb"`
	//rows offset,从0开始
	RowsOffset uint32 `protobuf:"varint,2,opt,name=RowsOffset,proto3" json:"RowsOffset,omitempty" ykit:"nodb"`
	//本批次数据
	Rows []*DbUserRole `protobuf:"bytes,3,rep,name=Rows,proto3" json:"Rows,omitempty" ykit:"nodb"`
}

func (m *DbUserRoleList) Reset()         { *m = DbUserRoleList{} }
func (m *DbUserRoleList) String() string { return proto.CompactTextString(m) }
func (*DbUserRoleList) ProtoMessage()    {}
func (*DbUserRoleList) Descriptor() ([]byte, []int) {
	return fileDescriptor_b49940f0e3d00c0c, []int{1}
}
func (m *DbUserRoleList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbUserRoleList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbUserRoleList.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbUserRoleList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbUserRoleList.Merge(m, src)
}
func (m *DbUserRoleList) XXX_Size() int {
	return m.Size()
}
func (m *DbUserRoleList) XXX_DiscardUnknown() {
	xxx_messageInfo_DbUserRoleList.DiscardUnknown(m)
}

var xxx_messageInfo_DbUserRoleList proto.InternalMessageInfo

func (m *DbUserRoleList) GetBatchNo() uint32 {
	if m != nil {
		return m.BatchNo
	}
	return 0
}

func (m *DbUserRoleList) GetRowsOffset() uint32 {
	if m != nil {
		return m.RowsOffset
	}
	return 0
}

func (m *DbUserRoleList) GetRows() []*DbUserRole {
	if m != nil {
		return m.Rows
	}
	return nil
}

func init() {
	proto.RegisterType((*DbUserList)(nil), "user.DbUserList")
	proto.RegisterType((*DbUserRoleList)(nil), "user.DbUserRoleList")
}

func init() { proto.RegisterFile("user.list.proto", fileDescriptor_b49940f0e3d00c0c) }

var fileDescriptor_b49940f0e3d00c0c = []byte{
	// 216 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe2, 0xe2, 0x2f, 0x2d, 0x4e, 0x2d,
	0xd2, 0xcb, 0xc9, 0x2c, 0x2e, 0xd1, 0x2b, 0x28, 0xca, 0x2f, 0xc9, 0x17, 0x62, 0x01, 0x09, 0x48,
	0x71, 0x81, 0x85, 0xc1, 0x22, 0x4a, 0x19, 0x5c, 0x5c, 0x2e, 0x49, 0xa1, 0xc5, 0xa9, 0x45, 0x3e,
	0x99, 0xc5, 0x25, 0x42, 0x12, 0x5c, 0xec, 0x4e, 0x89, 0x25, 0xc9, 0x19, 0x7e, 0xf9, 0x12, 0x8c,
	0x0a, 0x8c, 0x1a, 0xbc, 0x41, 0x30, 0xae, 0x90, 0x1c, 0x17, 0x57, 0x50, 0x7e, 0x79, 0xb1, 0x7f,
	0x5a, 0x5a, 0x71, 0x6a, 0x89, 0x04, 0x13, 0x58, 0x12, 0x49, 0x44, 0x48, 0x81, 0x8b, 0x05, 0xc4,
	0x93, 0x60, 0x56, 0x60, 0xd6, 0xe0, 0x36, 0xe2, 0xd1, 0x03, 0x5b, 0x01, 0x31, 0x39, 0x08, 0x2c,
	0xa3, 0x54, 0xc0, 0xc5, 0x07, 0xe5, 0xe7, 0xe7, 0xa4, 0x52, 0x68, 0x9b, 0x0a, 0x8a, 0x6d, 0x02,
	0x28, 0xb6, 0xe5, 0xe7, 0xa4, 0x42, 0x6c, 0x74, 0xb2, 0x39, 0xf1, 0x48, 0x8e, 0xf1, 0xc2, 0x23,
	0x39, 0xc6, 0x07, 0x8f, 0xe4, 0x18, 0x27, 0x3c, 0x96, 0x63, 0xb8, 0xf0, 0x58, 0x8e, 0xe1, 0xc6,
	0x63, 0x39, 0x86, 0x28, 0xa5, 0xf4, 0xcc, 0x12, 0xbd, 0xec, 0xcc, 0xe4, 0xc4, 0x14, 0x4b, 0x4b,
	0xbd, 0xe4, 0xfc, 0x5c, 0xfd, 0xca, 0xec, 0xcc, 0x12, 0xfd, 0x94, 0xc4, 0x92, 0xc4, 0xa4, 0xc4,
	0xe2, 0x54, 0x7d, 0x90, 0x71, 0x49, 0x6c, 0xe0, 0x00, 0x32, 0x06, 0x04, 0x00, 0x00, 0xff, 0xff,
	0xa4, 0x25, 0x2f, 0x5f, 0x45, 0x01, 0x00, 0x00,
}

func (m *DbUserList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbUserList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbUserList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for iNdEx := len(m.Rows) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Rows[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintUserList(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x1a
		}
	}
	if m.RowsOffset != 0 {
		i = encodeVarintUserList(dAtA, i, uint64(m.RowsOffset))
		i--
		dAtA[i] = 0x10
	}
	if m.BatchNo != 0 {
		i = encodeVarintUserList(dAtA, i, uint64(m.BatchNo))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *DbUserRoleList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbUserRoleList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbUserRoleList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for iNdEx := len(m.Rows) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Rows[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintUserList(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x1a
		}
	}
	if m.RowsOffset != 0 {
		i = encodeVarintUserList(dAtA, i, uint64(m.RowsOffset))
		i--
		dAtA[i] = 0x10
	}
	if m.BatchNo != 0 {
		i = encodeVarintUserList(dAtA, i, uint64(m.BatchNo))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func encodeVarintUserList(dAtA []byte, offset int, v uint64) int {
	offset -= sovUserList(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *DbUserList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.BatchNo != 0 {
		n += 1 + sovUserList(uint64(m.BatchNo))
	}
	if m.RowsOffset != 0 {
		n += 1 + sovUserList(uint64(m.RowsOffset))
	}
	if len(m.Rows) > 0 {
		for _, e := range m.Rows {
			l = e.Size()
			n += 1 + l + sovUserList(uint64(l))
		}
	}
	return n
}

func (m *DbUserRoleList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.BatchNo != 0 {
		n += 1 + sovUserList(uint64(m.BatchNo))
	}
	if m.RowsOffset != 0 {
		n += 1 + sovUserList(uint64(m.RowsOffset))
	}
	if len(m.Rows) > 0 {
		for _, e := range m.Rows {
			l = e.Size()
			n += 1 + l + sovUserList(uint64(l))
		}
	}
	return n
}

func sovUserList(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozUserList(x uint64) (n int) {
	return sovUserList(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *DbUserList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserList
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DbUserList: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DbUserList: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field BatchNo", wireType)
			}
			m.BatchNo = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BatchNo |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RowsOffset", wireType)
			}
			m.RowsOffset = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RowsOffset |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rows", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserList
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthUserList
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rows = append(m.Rows, &DbUser{})
			if err := m.Rows[len(m.Rows)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUserList(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserList
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthUserList
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DbUserRoleList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserList
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DbUserRoleList: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DbUserRoleList: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field BatchNo", wireType)
			}
			m.BatchNo = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BatchNo |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RowsOffset", wireType)
			}
			m.RowsOffset = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RowsOffset |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rows", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserList
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthUserList
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rows = append(m.Rows, &DbUserRole{})
			if err := m.Rows[len(m.Rows)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUserList(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserList
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthUserList
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipUserList(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowUserList
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowUserList
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowUserList
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthUserList
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupUserList
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthUserList
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthUserList        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowUserList          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupUserList = fmt.Errorf("proto: unexpected end of group")
)

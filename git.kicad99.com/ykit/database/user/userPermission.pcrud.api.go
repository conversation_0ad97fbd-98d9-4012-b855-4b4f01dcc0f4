package user

import (
	"context"
	"strconv"

	"git.kicad99.com/ykit/goutil/crud"
	"git.kicad99.com/ykit/goutil/pgxdb"
	"git.kicad99.com/ykit/goutil/yreflect"
	"google.golang.org/grpc/metadata"
)

//YkitDbRolePermissionPcrud 单独为dbrolepermission实现的 pcrud server
type YkitDbRolePermissionPcrud struct {
}

func (*YkitDbRolePermissionPcrud) Insert(ctx context.Context, req *DbRolePermission) (*crud.DMLResult, error) {
	var s *PcrudRpcDbRolePermissionServer
	return s.Insert(ctx, req)
}
func (*YkitDbRolePermissionPcrud) Update(ctx context.Context, req *DbRolePermission) (*crud.DMLResult, error) {
	var s *PcrudRpcDbRolePermissionServer
	return s.Update(ctx, req)

}
func (*YkitDbRolePermissionPcrud) PartialUpdate(ctx context.Context, req *DbRolePermission) (*crud.DMLResult, error) {
	var s *PcrudRpcDbRolePermissionServer
	return s.PartialUpdate(ctx, req)
}
func (*YkitDbRolePermissionPcrud) Delete(ctx context.Context, req *DbRolePermission) (*crud.DMLResult, error) {
	var s *PcrudRpcDbRolePermissionServer
	return s.Delete(ctx, req)
}
func (*YkitDbRolePermissionPcrud) SelectOne(ctx context.Context, req *crud.DMLParam) (*DbRolePermission, error) {
	var s *PcrudRpcDbRolePermissionServer
	return s.SelectOne(ctx, req)
}
func (*YkitDbRolePermissionPcrud) SelectMany(req *crud.DMLParam, srv PrpcDbRolePermission_SelectManyServer) error {
	var s *PcrudRpcDbRolePermissionServer
	return s.SelectMany(req, srv)
}
func (*YkitDbRolePermissionPcrud) Query(req *crud.PrivilegeParam, srv PrpcDbRolePermission_QueryServer) error {
	queryPara := req.QueryCondition
	if queryPara == nil {
		queryPara = &crud.QueryParam{}
	}

	system := req.System
	db, err := pgxdb.GetDbConn(system)
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)
	sessionid := req.SessionID
	userid, exist, err := crud.GetUserRIDinSession(system, sessionid, true, db)
	if err != nil {
		return err
	}
	if !exist {
		return crud.ENoSuchSessionFound
	}

	rolerids, err := crud.GetUserRoleRIDs(userid, db)

	if err != nil {
		return err
	}

	rows, err := crud.TableSelectColInPrivilege("DbRolePermission", []string{"rolerid"}, [][]string{rolerids}, userid, db, queryPara)
	if err != nil {
		return err
	}

	defer rows.Close()

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbRolePermission{})
	rowsCount := 0
	for rows.Next() {
		msg := &DbRolePermission{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		err = srv.Send(msg)
		if err != nil {
			return err
		}
		rowsCount++
	}
	trailer := metadata.Pairs("rows", strconv.Itoa(rowsCount))
	srv.SetTrailer(trailer)
	return nil

}

func (*YkitDbRolePermissionPcrud) QueryBatch(req *crud.PrivilegeParam, srv PrpcDbRolePermission_QueryBatchServer) error {
	queryPara := req.QueryCondition
	if queryPara == nil {
		queryPara = &crud.QueryParam{}
	}

	if req.QueryCondition.Batch < 1 {
		req.QueryCondition.Batch = 1
	}
	if req.QueryCondition.Batch > 10000 {
		req.QueryCondition.Batch = 10000
	}

	system := req.System
	db, err := pgxdb.GetDbConn(system)
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)
	sessionid := req.SessionID
	userid, exist, err := crud.GetUserRIDinSession(system, sessionid, true, db)
	if err != nil {
		return err
	}
	if !exist {
		return crud.ENoSuchSessionFound
	}

	rolerids, err := crud.GetUserRoleRIDs(userid, db)

	if err != nil {
		return err
	}

	rows, err := crud.TableSelectColInPrivilege("DbRolePermission", []string{"rolerid"}, [][]string{rolerids}, userid, db, queryPara)
	if err != nil {
		return err
	}

	defer rows.Close()

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbRolePermission{})
	var batchNo uint32 = 0
	var rowsOffset uint32 = 0
	var batchCount uint32 = 0
	list := &DbRolePermissionList{}
	for rows.Next() {
		msg := &DbRolePermission{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		list.Rows = append(list.Rows, msg)
		batchCount++
		rowsOffset++

		if batchCount >= req.QueryCondition.Batch {
			err = srv.Send(list)
			if err != nil {
				return err
			}
			batchNo++
			list = &DbRolePermissionList{
				BatchNo:    batchNo,
				RowsOffset: rowsOffset}
			batchCount = 0
		}
	}
	if batchCount > 0 {
		err = srv.Send(list)
		if err != nil {
			return err
		}
	}
	trailer := metadata.Pairs("rows", strconv.FormatUint(uint64(rowsOffset), 10))
	srv.SetTrailer(trailer)
	return nil

}

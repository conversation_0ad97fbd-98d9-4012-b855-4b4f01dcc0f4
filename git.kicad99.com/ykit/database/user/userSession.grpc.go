//Package user  generated by ygen-gocrud. DO NOT EDIT.
//source: userSession.proto
package user

import (
	"context"
	"strconv"

	"git.kicad99.com/ykit/goutil/crud"
	"git.kicad99.com/ykit/goutil/crudhook"
	"git.kicad99.com/ykit/goutil"
	"git.kicad99.com/ykit/goutil/pgxdb"
	"git.kicad99.com/ykit/goutil/rpc"
	"git.kicad99.com/ykit/goutil/yreflect"
	"google.golang.org/grpc/metadata"
)

// CrudRpcDbUserSessionServer impl crud Service
type CrudRpcDbUserSessionServer struct {
}

//Insert impl crud insert
func (*CrudRpcDbUserSessionServer) Insert(ctx context.Context, req *DbUserSession) (r *crud.DMLResult, err error) {
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	r, err = req.Insert(db)
	if err == nil {
		sys, userRid, sid := pgxdb.GetYkitInfoFromMetadata(ctx)
		ipinfo := goutil.GetIpinfoFromMeta(ctx)
		userOrgrid := goutil.GetUserOrgRIDFromMeta(ctx)
		goutil.NatsCdc(sys, req.OrgRID, sid, "DbUserSession.Insert", req, nil)
		crudhook.CrudAfterHook(sys, userRid, userOrgrid, "DbUserSession.Insert", req, nil, ipinfo)
	}
	return
}

//Update impl crud update
func (*CrudRpcDbUserSessionServer) Update(ctx context.Context, req *DbUserSession) (r *crud.DMLResult, err error) {
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	r, err = req.Update(db)
	if err == nil {
		sys, userRid, sid := pgxdb.GetYkitInfoFromMetadata(ctx)
		ipinfo := goutil.GetIpinfoFromMeta(ctx)
		userOrgrid := goutil.GetUserOrgRIDFromMeta(ctx)
		goutil.NatsCdc(sys, req.OrgRID, sid, "DbUserSession.Update", req, nil)
		crudhook.CrudAfterHook(sys, userRid, userOrgrid, "DbUserSession.Update", req, nil, ipinfo)
	}
	return
}

//PartialUpdate impl crud PartialUpdate
func (*CrudRpcDbUserSessionServer) PartialUpdate(ctx context.Context, req *DbUserSession) (r *crud.DMLResult, err error) {
	param, err := crud.GetDMLParamFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	r, err = req.PartialUpdate(param.KeyColumn, db)
	if err == nil {
		sys, userRid, sid := pgxdb.GetYkitInfoFromMetadata(ctx)
		ipinfo := goutil.GetIpinfoFromMeta(ctx)
		userOrgrid := goutil.GetUserOrgRIDFromMeta(ctx)
		goutil.NatsCdc(sys, req.OrgRID, sid, "DbUserSession.PartialUpdate", req, param)
		crudhook.CrudAfterHook(sys, userRid, userOrgrid, "DbUserSession.PartialUpdate", req, param, ipinfo)
	}
	return
}

//Delete impl crud Delete
func (*CrudRpcDbUserSessionServer) Delete(ctx context.Context, req *DbUserSession) (r *crud.DMLResult, err error) {
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	r, err = req.Delete(db)
	if err == nil {
		sys, userRid, sid := pgxdb.GetYkitInfoFromMetadata(ctx)
		ipinfo := goutil.GetIpinfoFromMeta(ctx)
		userOrgrid := goutil.GetUserOrgRIDFromMeta(ctx)
		goutil.NatsCdc(sys, req.OrgRID, sid, "DbUserSession.Delete", req, nil)
		crudhook.CrudAfterHook(sys, userRid, userOrgrid, "DbUserSession.Delete", req, nil, ipinfo)
	}
	return
}

//SelectOne impl crud SelectOne
func (*CrudRpcDbUserSessionServer) SelectOne(ctx context.Context, dmlParam *crud.DMLParam) (*DbUserSession, error) {
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	if len(dmlParam.KeyValue) != 1 {
		return nil, rpc.EBadProtoParam
	}
	msg := &DbUserSession{RID: dmlParam.KeyValue[0]}

	selectRow, err := msg.SelectOne(dmlParam.ResultColumn, db)
	if err != nil {
		return nil, err
	}
	if selectRow == 0 {
		msg.Reset()
	}
	return msg, nil
}

//SelectMany impl crud SelectMany
func (*CrudRpcDbUserSessionServer) SelectMany(dmlParam *crud.DMLParam, srv RpcDbUserSession_SelectManyServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)

	msg := &DbUserSession{}

	rows, err := msg.SelectByRids(dmlParam.ResultColumn, dmlParam.KeyValue, db)
	if err != nil {
		return err
	}
	defer rows.Close()
	rowsName := crud.RowsColNames2GoFieldNames(rows, msg)
	rowsCount := 0
	for rows.Next() {
		msg := &DbUserSession{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		err = srv.Send(msg)
		if err != nil {
			return err
		}
		rowsCount++
	}
	trailer := metadata.Pairs("rows", strconv.Itoa(rowsCount))
	srv.SetTrailer(trailer)
	return nil
}

//Query impl crud Query
func (*CrudRpcDbUserSessionServer) Query(req *crud.QueryParam, srv RpcDbUserSession_QueryServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)

	rows, err := crud.DbSelectQuery("DbUserSession", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbUserSession{})
	rowsCount := 0
	for rows.Next() {
		msg := &DbUserSession{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		err = srv.Send(msg)
		if err != nil {
			return err
		}
		rowsCount++
	}
	trailer := metadata.Pairs("rows", strconv.Itoa(rowsCount))
	srv.SetTrailer(trailer)
	return nil
}
//Query impl crud QueryBatch
func (*CrudRpcDbUserSessionServer) QueryBatch(req *crud.QueryParam, srv RpcDbUserSession_QueryBatchServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)

	rows, err := crud.DbSelectQuery("DbUserSession", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	if req.Batch < 1 {
		req.Batch = 1
	}
	if req.Batch > 10000 {
		req.Batch = 10000
	}

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbUserSession{})
	var batchNo uint32 = 0
	var rowsOffset uint32 = 0
	var batchCount uint32 = 0
	list := &DbUserSessionList{}
	for rows.Next() {
		msg := &DbUserSession{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		list.Rows = append(list.Rows, msg)
		batchCount++
		rowsOffset++

		if batchCount >= req.Batch {
			err = srv.Send(list)
			if err != nil {
				return err
			}
			batchNo++
			list = &DbUserSessionList{
				BatchNo:    batchNo,
				RowsOffset: rowsOffset}
			batchCount = 0
		}
	}
	if batchCount > 0 {
		err = srv.Send(list)
		if err != nil {
			return err
		}
	}
	trailer := metadata.Pairs("rows", strconv.FormatUint(uint64(rowsOffset), 10))
	srv.SetTrailer(trailer)
	return nil
}

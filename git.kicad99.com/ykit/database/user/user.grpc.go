//Package user  generated by ygen-gocrud. DO NOT EDIT.
//source: user.proto
package user

import (
	"context"
	"strconv"

	"git.kicad99.com/ykit/goutil/crud"
	"git.kicad99.com/ykit/goutil/crudhook"
	"git.kicad99.com/ykit/goutil"
	"git.kicad99.com/ykit/goutil/pgxdb"
	"git.kicad99.com/ykit/goutil/rpc"
	"git.kicad99.com/ykit/goutil/yreflect"
	"google.golang.org/grpc/metadata"
)

// CrudRpcDbUserServer impl crud Service
type CrudRpcDbUserServer struct {
}

//Insert impl crud insert
func (*CrudRpcDbUserServer) Insert(ctx context.Context, req *DbUser) (r *crud.DMLResult, err error) {
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	r, err = req.Insert(db)
	if err == nil {
		sys, userRid, sid := pgxdb.GetYkitInfoFromMetadata(ctx)
		ipinfo := goutil.GetIpinfoFromMeta(ctx)
		userOrgrid := goutil.GetUserOrgRIDFromMeta(ctx)
		goutil.NatsCdc(sys, req.OrgRID, sid, "DbUser.Insert", req, nil)
		crudhook.CrudAfterHook(sys, userRid, userOrgrid, "DbUser.Insert", req, nil, ipinfo)
	}
	return
}

//Update impl crud update
func (*CrudRpcDbUserServer) Update(ctx context.Context, req *DbUser) (r *crud.DMLResult, err error) {
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	r, err = req.Update(db)
	if err == nil {
		sys, userRid, sid := pgxdb.GetYkitInfoFromMetadata(ctx)
		ipinfo := goutil.GetIpinfoFromMeta(ctx)
		userOrgrid := goutil.GetUserOrgRIDFromMeta(ctx)
		goutil.NatsCdc(sys, req.OrgRID, sid, "DbUser.Update", req, nil)
		crudhook.CrudAfterHook(sys, userRid, userOrgrid, "DbUser.Update", req, nil, ipinfo)
	}
	return
}

//PartialUpdate impl crud PartialUpdate
func (*CrudRpcDbUserServer) PartialUpdate(ctx context.Context, req *DbUser) (r *crud.DMLResult, err error) {
	param, err := crud.GetDMLParamFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	r, err = req.PartialUpdate(param.KeyColumn, db)
	if err == nil {
		sys, userRid, sid := pgxdb.GetYkitInfoFromMetadata(ctx)
		ipinfo := goutil.GetIpinfoFromMeta(ctx)
		userOrgrid := goutil.GetUserOrgRIDFromMeta(ctx)
		goutil.NatsCdc(sys, req.OrgRID, sid, "DbUser.PartialUpdate", req, param)
		crudhook.CrudAfterHook(sys, userRid, userOrgrid, "DbUser.PartialUpdate", req, param, ipinfo)
	}
	return
}

//Delete impl crud Delete
func (*CrudRpcDbUserServer) Delete(ctx context.Context, req *DbUser) (r *crud.DMLResult, err error) {
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	r, err = req.Delete(db)
	if err == nil {
		sys, userRid, sid := pgxdb.GetYkitInfoFromMetadata(ctx)
		ipinfo := goutil.GetIpinfoFromMeta(ctx)
		userOrgrid := goutil.GetUserOrgRIDFromMeta(ctx)
		goutil.NatsCdc(sys, req.OrgRID, sid, "DbUser.Delete", req, nil)
		crudhook.CrudAfterHook(sys, userRid, userOrgrid, "DbUser.Delete", req, nil, ipinfo)
	}
	return
}

//SelectOne impl crud SelectOne
func (*CrudRpcDbUserServer) SelectOne(ctx context.Context, dmlParam *crud.DMLParam) (*DbUser, error) {
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	if len(dmlParam.KeyValue) != 1 {
		return nil, rpc.EBadProtoParam
	}
	msg := &DbUser{RID: dmlParam.KeyValue[0]}

	selectRow, err := msg.SelectOne(dmlParam.ResultColumn, db)
	if err != nil {
		return nil, err
	}
	if selectRow == 0 {
		msg.Reset()
	}
	return msg, nil
}

//SelectMany impl crud SelectMany
func (*CrudRpcDbUserServer) SelectMany(dmlParam *crud.DMLParam, srv RpcDbUser_SelectManyServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)

	msg := &DbUser{}

	rows, err := msg.SelectByRids(dmlParam.ResultColumn, dmlParam.KeyValue, db)
	if err != nil {
		return err
	}
	defer rows.Close()
	rowsName := crud.RowsColNames2GoFieldNames(rows, msg)
	rowsCount := 0
	for rows.Next() {
		msg := &DbUser{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		err = srv.Send(msg)
		if err != nil {
			return err
		}
		rowsCount++
	}
	trailer := metadata.Pairs("rows", strconv.Itoa(rowsCount))
	srv.SetTrailer(trailer)
	return nil
}

//Query impl crud Query
func (*CrudRpcDbUserServer) Query(req *crud.QueryParam, srv RpcDbUser_QueryServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)

	rows, err := crud.DbSelectQuery("DbUser", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbUser{})
	rowsCount := 0
	for rows.Next() {
		msg := &DbUser{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		err = srv.Send(msg)
		if err != nil {
			return err
		}
		rowsCount++
	}
	trailer := metadata.Pairs("rows", strconv.Itoa(rowsCount))
	srv.SetTrailer(trailer)
	return nil
}
//Query impl crud QueryBatch
func (*CrudRpcDbUserServer) QueryBatch(req *crud.QueryParam, srv RpcDbUser_QueryBatchServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)

	rows, err := crud.DbSelectQuery("DbUser", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	if req.Batch < 1 {
		req.Batch = 1
	}
	if req.Batch > 10000 {
		req.Batch = 10000
	}

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbUser{})
	var batchNo uint32 = 0
	var rowsOffset uint32 = 0
	var batchCount uint32 = 0
	list := &DbUserList{}
	for rows.Next() {
		msg := &DbUser{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		list.Rows = append(list.Rows, msg)
		batchCount++
		rowsOffset++

		if batchCount >= req.Batch {
			err = srv.Send(list)
			if err != nil {
				return err
			}
			batchNo++
			list = &DbUserList{
				BatchNo:    batchNo,
				RowsOffset: rowsOffset}
			batchCount = 0
		}
	}
	if batchCount > 0 {
		err = srv.Send(list)
		if err != nil {
			return err
		}
	}
	trailer := metadata.Pairs("rows", strconv.FormatUint(uint64(rowsOffset), 10))
	srv.SetTrailer(trailer)
	return nil
}

// PcrudRpcDbUserServer impl pcrud Service
type PcrudRpcDbUserServer struct {
}

//Insert impl pcrud Insert
func (*PcrudRpcDbUserServer) Insert(ctx context.Context, req *DbUser) (*crud.DMLResult, error) {
	var crudServer *CrudRpcDbUserServer
	_, err := crud.GrpcCheckHasPermission(ctx, "db", "DbUser.Insert")
	if err != nil {
		return nil, err
	}
	err = crud.GrpcCheckHasOrgridPrivilege(ctx, req.OrgRID)
	if err != nil {
		return nil, err
	}
	return crudServer.Insert(ctx, req)
}

//Update impl pcrud Update
func (*PcrudRpcDbUserServer) Update(ctx context.Context, req *DbUser) (*crud.DMLResult, error) {
	var crudServer *CrudRpcDbUserServer
	_, err := crud.GrpcCheckHasPermission(ctx, "db", "DbUser.Update")
	if err != nil {
		return nil, err
	}
	err = crud.GrpcCheckHasOrgridPrivilege(ctx, req.OrgRID)
	if err != nil {
		return nil, err
	}
	return crudServer.Update(ctx, req)
}

//PartialUpdate impl pcrud PartialUpdate
func (*PcrudRpcDbUserServer) PartialUpdate(ctx context.Context, req *DbUser) (*crud.DMLResult, error) {
	var crudServer *CrudRpcDbUserServer
	_, err := crud.GrpcCheckHasPermission(ctx, "db", "DbUser.Update")
	if err != nil {
		return nil, err
	}
	err = crud.GrpcCheckHasOrgridPrivilege(ctx, req.OrgRID)
	if err != nil {
		return nil, err
	}
	return crudServer.PartialUpdate(ctx, req)
}

//Delete impl pcrud Delete
func (*PcrudRpcDbUserServer) Delete(ctx context.Context, req *DbUser) (*crud.DMLResult, error) {
	var crudServer *CrudRpcDbUserServer
	_, err := crud.GrpcCheckHasPermission(ctx, "db", "DbUser.Delete")
	if err != nil {
		return nil, err
	}
	err = crud.GrpcCheckHasOrgridPrivilege(ctx, req.OrgRID)
	if err != nil {
		return nil, err
	}
	return crudServer.Delete(ctx, req)
}

//SelectOne impl pcrud SelectOne
func (*PcrudRpcDbUserServer) SelectOne(ctx context.Context, dmlParam *crud.DMLParam) (*DbUser, error) {
	var crudServer *CrudRpcDbUserServer
	return crudServer.SelectOne(ctx, dmlParam)
}

//SelectMany impl pcrud SelectMany
func (*PcrudRpcDbUserServer) SelectMany(dmlParam *crud.DMLParam, srv PrpcDbUser_SelectManyServer) error {
	var crudServer *CrudRpcDbUserServer
	return crudServer.SelectMany(dmlParam, srv)
}

//Query impl pcrud Query
func (*PcrudRpcDbUserServer) Query(req *crud.PrivilegeParam, srv PrpcDbUser_QueryServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)
	rows, err := crud.DbSelectPrivilege("DbUser", "OrgRID", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbUser{})
	rowsCount := 0
	for rows.Next() {
		msg := &DbUser{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		err = srv.Send(msg)
		if err != nil {
			return err
		}
		rowsCount++
	}
	trailer := metadata.Pairs("rows", strconv.Itoa(rowsCount))
	srv.SetTrailer(trailer)
	return nil
}
//Query impl pcrud QueryBatch
func (*PcrudRpcDbUserServer) QueryBatch(req *crud.PrivilegeParam, srv PrpcDbUser_QueryBatchServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)
	rows, err := crud.DbSelectPrivilege("DbUser", "OrgRID", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	if req.QueryCondition.Batch < 1 {
		req.QueryCondition.Batch = 1
	}
	if req.QueryCondition.Batch > 10000 {
		req.QueryCondition.Batch = 10000
	}

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbUser{})

	var batchNo uint32 = 0
	var rowsOffset uint32 = 0
	var batchCount uint32 = 0
	list := &DbUserList{}
	for rows.Next() {
		msg := &DbUser{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		list.Rows = append(list.Rows, msg)
		batchCount++
		rowsOffset++

		if batchCount >= req.QueryCondition.Batch {
			err = srv.Send(list)
			if err != nil {
				return err
			}
			batchNo++
			list = &DbUserList{
				BatchNo:    batchNo,
				RowsOffset: rowsOffset}
			batchCount = 0
		}
	}
	if batchCount > 0 {
		err = srv.Send(list)
		if err != nil {
			return err
		}
	}
	trailer := metadata.Pairs("rows", strconv.FormatUint(uint64(rowsOffset), 10))
	srv.SetTrailer(trailer)
	return nil
}
// CrudRpcDbUserRoleServer impl crud Service
type CrudRpcDbUserRoleServer struct {
}

//Insert impl crud insert
func (*CrudRpcDbUserRoleServer) Insert(ctx context.Context, req *DbUserRole) (r *crud.DMLResult, err error) {
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	r, err = req.Insert(db)
	if err == nil {
		sys, userRid, sid := pgxdb.GetYkitInfoFromMetadata(ctx)
		ipinfo := goutil.GetIpinfoFromMeta(ctx)
		userOrgrid := goutil.GetUserOrgRIDFromMeta(ctx)
		goutil.NatsCdc(sys, req.OrgRID, sid, "DbUserRole.Insert", req, nil)
		crudhook.CrudAfterHook(sys, userRid, userOrgrid, "DbUserRole.Insert", req, nil, ipinfo)
	}
	return
}

//Update impl crud update
func (*CrudRpcDbUserRoleServer) Update(ctx context.Context, req *DbUserRole) (r *crud.DMLResult, err error) {
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	r, err = req.Update(db)
	if err == nil {
		sys, userRid, sid := pgxdb.GetYkitInfoFromMetadata(ctx)
		ipinfo := goutil.GetIpinfoFromMeta(ctx)
		userOrgrid := goutil.GetUserOrgRIDFromMeta(ctx)
		goutil.NatsCdc(sys, req.OrgRID, sid, "DbUserRole.Update", req, nil)
		crudhook.CrudAfterHook(sys, userRid, userOrgrid, "DbUserRole.Update", req, nil, ipinfo)
	}
	return
}

//PartialUpdate impl crud PartialUpdate
func (*CrudRpcDbUserRoleServer) PartialUpdate(ctx context.Context, req *DbUserRole) (r *crud.DMLResult, err error) {
	param, err := crud.GetDMLParamFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	r, err = req.PartialUpdate(param.KeyColumn, db)
	if err == nil {
		sys, userRid, sid := pgxdb.GetYkitInfoFromMetadata(ctx)
		ipinfo := goutil.GetIpinfoFromMeta(ctx)
		userOrgrid := goutil.GetUserOrgRIDFromMeta(ctx)
		goutil.NatsCdc(sys, req.OrgRID, sid, "DbUserRole.PartialUpdate", req, param)
		crudhook.CrudAfterHook(sys, userRid, userOrgrid, "DbUserRole.PartialUpdate", req, param, ipinfo)
	}
	return
}

//Delete impl crud Delete
func (*CrudRpcDbUserRoleServer) Delete(ctx context.Context, req *DbUserRole) (r *crud.DMLResult, err error) {
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	r, err = req.Delete(db)
	if err == nil {
		sys, userRid, sid := pgxdb.GetYkitInfoFromMetadata(ctx)
		ipinfo := goutil.GetIpinfoFromMeta(ctx)
		userOrgrid := goutil.GetUserOrgRIDFromMeta(ctx)
		goutil.NatsCdc(sys, req.OrgRID, sid, "DbUserRole.Delete", req, nil)
		crudhook.CrudAfterHook(sys, userRid, userOrgrid, "DbUserRole.Delete", req, nil, ipinfo)
	}
	return
}

//SelectOne impl crud SelectOne
func (*CrudRpcDbUserRoleServer) SelectOne(ctx context.Context, dmlParam *crud.DMLParam) (*DbUserRole, error) {
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	if len(dmlParam.KeyValue) != 1 {
		return nil, rpc.EBadProtoParam
	}
	msg := &DbUserRole{RID: dmlParam.KeyValue[0]}

	selectRow, err := msg.SelectOne(dmlParam.ResultColumn, db)
	if err != nil {
		return nil, err
	}
	if selectRow == 0 {
		msg.Reset()
	}
	return msg, nil
}

//SelectMany impl crud SelectMany
func (*CrudRpcDbUserRoleServer) SelectMany(dmlParam *crud.DMLParam, srv RpcDbUserRole_SelectManyServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)

	msg := &DbUserRole{}

	rows, err := msg.SelectByRids(dmlParam.ResultColumn, dmlParam.KeyValue, db)
	if err != nil {
		return err
	}
	defer rows.Close()
	rowsName := crud.RowsColNames2GoFieldNames(rows, msg)
	rowsCount := 0
	for rows.Next() {
		msg := &DbUserRole{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		err = srv.Send(msg)
		if err != nil {
			return err
		}
		rowsCount++
	}
	trailer := metadata.Pairs("rows", strconv.Itoa(rowsCount))
	srv.SetTrailer(trailer)
	return nil
}

//Query impl crud Query
func (*CrudRpcDbUserRoleServer) Query(req *crud.QueryParam, srv RpcDbUserRole_QueryServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)

	rows, err := crud.DbSelectQuery("DbUserRole", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbUserRole{})
	rowsCount := 0
	for rows.Next() {
		msg := &DbUserRole{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		err = srv.Send(msg)
		if err != nil {
			return err
		}
		rowsCount++
	}
	trailer := metadata.Pairs("rows", strconv.Itoa(rowsCount))
	srv.SetTrailer(trailer)
	return nil
}
//Query impl crud QueryBatch
func (*CrudRpcDbUserRoleServer) QueryBatch(req *crud.QueryParam, srv RpcDbUserRole_QueryBatchServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)

	rows, err := crud.DbSelectQuery("DbUserRole", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	if req.Batch < 1 {
		req.Batch = 1
	}
	if req.Batch > 10000 {
		req.Batch = 10000
	}

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbUserRole{})
	var batchNo uint32 = 0
	var rowsOffset uint32 = 0
	var batchCount uint32 = 0
	list := &DbUserRoleList{}
	for rows.Next() {
		msg := &DbUserRole{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		list.Rows = append(list.Rows, msg)
		batchCount++
		rowsOffset++

		if batchCount >= req.Batch {
			err = srv.Send(list)
			if err != nil {
				return err
			}
			batchNo++
			list = &DbUserRoleList{
				BatchNo:    batchNo,
				RowsOffset: rowsOffset}
			batchCount = 0
		}
	}
	if batchCount > 0 {
		err = srv.Send(list)
		if err != nil {
			return err
		}
	}
	trailer := metadata.Pairs("rows", strconv.FormatUint(uint64(rowsOffset), 10))
	srv.SetTrailer(trailer)
	return nil
}

// PcrudRpcDbUserRoleServer impl pcrud Service
type PcrudRpcDbUserRoleServer struct {
}

//Insert impl pcrud Insert
func (*PcrudRpcDbUserRoleServer) Insert(ctx context.Context, req *DbUserRole) (*crud.DMLResult, error) {
	var crudServer *CrudRpcDbUserRoleServer
	_, err := crud.GrpcCheckHasPermission(ctx, "db", "DbUserRole.Insert")
	if err != nil {
		return nil, err
	}
	err = crud.GrpcCheckHasOrgridPrivilege(ctx, req.OrgRID)
	if err != nil {
		return nil, err
	}
	return crudServer.Insert(ctx, req)
}

//Update impl pcrud Update
func (*PcrudRpcDbUserRoleServer) Update(ctx context.Context, req *DbUserRole) (*crud.DMLResult, error) {
	var crudServer *CrudRpcDbUserRoleServer
	_, err := crud.GrpcCheckHasPermission(ctx, "db", "DbUserRole.Update")
	if err != nil {
		return nil, err
	}
	err = crud.GrpcCheckHasOrgridPrivilege(ctx, req.OrgRID)
	if err != nil {
		return nil, err
	}
	return crudServer.Update(ctx, req)
}

//PartialUpdate impl pcrud PartialUpdate
func (*PcrudRpcDbUserRoleServer) PartialUpdate(ctx context.Context, req *DbUserRole) (*crud.DMLResult, error) {
	var crudServer *CrudRpcDbUserRoleServer
	_, err := crud.GrpcCheckHasPermission(ctx, "db", "DbUserRole.Update")
	if err != nil {
		return nil, err
	}
	err = crud.GrpcCheckHasOrgridPrivilege(ctx, req.OrgRID)
	if err != nil {
		return nil, err
	}
	return crudServer.PartialUpdate(ctx, req)
}

//Delete impl pcrud Delete
func (*PcrudRpcDbUserRoleServer) Delete(ctx context.Context, req *DbUserRole) (*crud.DMLResult, error) {
	var crudServer *CrudRpcDbUserRoleServer
	_, err := crud.GrpcCheckHasPermission(ctx, "db", "DbUserRole.Delete")
	if err != nil {
		return nil, err
	}
	err = crud.GrpcCheckHasOrgridPrivilege(ctx, req.OrgRID)
	if err != nil {
		return nil, err
	}
	return crudServer.Delete(ctx, req)
}

//SelectOne impl pcrud SelectOne
func (*PcrudRpcDbUserRoleServer) SelectOne(ctx context.Context, dmlParam *crud.DMLParam) (*DbUserRole, error) {
	var crudServer *CrudRpcDbUserRoleServer
	return crudServer.SelectOne(ctx, dmlParam)
}

//SelectMany impl pcrud SelectMany
func (*PcrudRpcDbUserRoleServer) SelectMany(dmlParam *crud.DMLParam, srv PrpcDbUserRole_SelectManyServer) error {
	var crudServer *CrudRpcDbUserRoleServer
	return crudServer.SelectMany(dmlParam, srv)
}

//Query impl pcrud Query
func (*PcrudRpcDbUserRoleServer) Query(req *crud.PrivilegeParam, srv PrpcDbUserRole_QueryServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)
	rows, err := crud.DbSelectPrivilege("DbUserRole", "OrgRID", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbUserRole{})
	rowsCount := 0
	for rows.Next() {
		msg := &DbUserRole{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		err = srv.Send(msg)
		if err != nil {
			return err
		}
		rowsCount++
	}
	trailer := metadata.Pairs("rows", strconv.Itoa(rowsCount))
	srv.SetTrailer(trailer)
	return nil
}
//Query impl pcrud QueryBatch
func (*PcrudRpcDbUserRoleServer) QueryBatch(req *crud.PrivilegeParam, srv PrpcDbUserRole_QueryBatchServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)
	rows, err := crud.DbSelectPrivilege("DbUserRole", "OrgRID", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	if req.QueryCondition.Batch < 1 {
		req.QueryCondition.Batch = 1
	}
	if req.QueryCondition.Batch > 10000 {
		req.QueryCondition.Batch = 10000
	}

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbUserRole{})

	var batchNo uint32 = 0
	var rowsOffset uint32 = 0
	var batchCount uint32 = 0
	list := &DbUserRoleList{}
	for rows.Next() {
		msg := &DbUserRole{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		list.Rows = append(list.Rows, msg)
		batchCount++
		rowsOffset++

		if batchCount >= req.QueryCondition.Batch {
			err = srv.Send(list)
			if err != nil {
				return err
			}
			batchNo++
			list = &DbUserRoleList{
				BatchNo:    batchNo,
				RowsOffset: rowsOffset}
			batchCount = 0
		}
	}
	if batchCount > 0 {
		err = srv.Send(list)
		if err != nil {
			return err
		}
	}
	trailer := metadata.Pairs("rows", strconv.FormatUint(uint64(rowsOffset), 10))
	srv.SetTrailer(trailer)
	return nil
}

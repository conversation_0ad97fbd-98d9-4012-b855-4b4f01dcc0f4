// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: userSession.proto

package user

import (
	fmt "fmt"
	proto "github.com/gogo/protobuf/proto"
	io "io"
	math "math"
	math_bits "math/bits"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

//用户session表
//@rpc crud
//@dbpost CREATE INDEX IF NOT EXISTS idxDbUserSessionOrgRID on DbUserSession USING hash(OrgRID)
type DbUserSession struct {
	//@db uuid primary key
	//行ID,session rid
	RID string `protobuf:"bytes,1,opt,name=RID,proto3" json:"RID,omitempty" ykit:"prk"`
	//@db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
	//用户所属的群组
	OrgRID string `protobuf:"bytes,2,opt,name=OrgRID,proto3" json:"OrgRID,omitempty" ykit:"notnull"`
	//@db uuid not null REFERENCES DbUser(RID) ON DELETE CASCADE
	//用户rid
	UserRID string `protobuf:"bytes,3,opt,name=UserRID,proto3" json:"UserRID,omitempty" ykit:"notnull"`
	//@db int
	//session类型 0:web client
	SessionType int32 `protobuf:"varint,4,opt,name=SessionType,proto3" json:"SessionType,omitempty" ykit:"null"`
	//@db timestamp not null default now_utc()
	//用户登录的时间
	LoginTime string `protobuf:"bytes,10,opt,name=LoginTime,proto3" json:"LoginTime,omitempty" ykit:"notnull"`
	//@db jsonb not null default  '{}'::jsonb
	//用户登录信息
	LoginInfo string `protobuf:"bytes,11,opt,name=LoginInfo,proto3" json:"LoginInfo,omitempty" ykit:"notnull"`
	//@db timestamp not null default now_utc()
	//数据最后修改时间
	UpdatedAt string `protobuf:"bytes,14,opt,name=UpdatedAt,proto3" json:"UpdatedAt,omitempty" ykit:"notnull"`
	//@db text
	//数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
	UpdatedDC string `protobuf:"bytes,15,opt,name=UpdatedDC,proto3" json:"UpdatedDC,omitempty" ykit:"null"`
}

func (m *DbUserSession) Reset()         { *m = DbUserSession{} }
func (m *DbUserSession) String() string { return proto.CompactTextString(m) }
func (*DbUserSession) ProtoMessage()    {}
func (*DbUserSession) Descriptor() ([]byte, []int) {
	return fileDescriptor_06dd5c82e37ab736, []int{0}
}
func (m *DbUserSession) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbUserSession) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbUserSession.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbUserSession) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbUserSession.Merge(m, src)
}
func (m *DbUserSession) XXX_Size() int {
	return m.Size()
}
func (m *DbUserSession) XXX_DiscardUnknown() {
	xxx_messageInfo_DbUserSession.DiscardUnknown(m)
}

var xxx_messageInfo_DbUserSession proto.InternalMessageInfo

func (m *DbUserSession) GetRID() string {
	if m != nil {
		return m.RID
	}
	return ""
}

func (m *DbUserSession) GetOrgRID() string {
	if m != nil {
		return m.OrgRID
	}
	return ""
}

func (m *DbUserSession) GetUserRID() string {
	if m != nil {
		return m.UserRID
	}
	return ""
}

func (m *DbUserSession) GetSessionType() int32 {
	if m != nil {
		return m.SessionType
	}
	return 0
}

func (m *DbUserSession) GetLoginTime() string {
	if m != nil {
		return m.LoginTime
	}
	return ""
}

func (m *DbUserSession) GetLoginInfo() string {
	if m != nil {
		return m.LoginInfo
	}
	return ""
}

func (m *DbUserSession) GetUpdatedAt() string {
	if m != nil {
		return m.UpdatedAt
	}
	return ""
}

func (m *DbUserSession) GetUpdatedDC() string {
	if m != nil {
		return m.UpdatedDC
	}
	return ""
}

func init() {
	proto.RegisterType((*DbUserSession)(nil), "user.DbUserSession")
}

func init() { proto.RegisterFile("userSession.proto", fileDescriptor_06dd5c82e37ab736) }

var fileDescriptor_06dd5c82e37ab736 = []byte{
	// 242 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe2, 0x12, 0x2c, 0x2d, 0x4e, 0x2d,
	0x0a, 0x4e, 0x2d, 0x2e, 0xce, 0xcc, 0xcf, 0xd3, 0x2b, 0x28, 0xca, 0x2f, 0xc9, 0x17, 0x62, 0x01,
	0x09, 0x29, 0xbd, 0x65, 0xe4, 0xe2, 0x75, 0x49, 0x0a, 0x45, 0xc8, 0x0a, 0x09, 0x70, 0x31, 0x07,
	0x79, 0xba, 0x48, 0x30, 0x2a, 0x30, 0x6a, 0x70, 0x06, 0x81, 0x98, 0x42, 0x62, 0x5c, 0x6c, 0xfe,
	0x45, 0xe9, 0x20, 0x41, 0x26, 0xb0, 0x20, 0x94, 0x27, 0x24, 0xc1, 0xc5, 0x0e, 0xd2, 0x08, 0x92,
	0x60, 0x06, 0x4b, 0xc0, 0xb8, 0x42, 0x0a, 0x5c, 0xdc, 0x50, 0xe3, 0x42, 0x2a, 0x0b, 0x52, 0x25,
	0x58, 0x14, 0x18, 0x35, 0x58, 0x83, 0x90, 0x85, 0x84, 0x64, 0xb8, 0x38, 0x7d, 0xf2, 0xd3, 0x33,
	0xf3, 0x42, 0x32, 0x73, 0x53, 0x25, 0xb8, 0xc0, 0xba, 0x11, 0x02, 0x70, 0x59, 0xcf, 0xbc, 0xb4,
	0x7c, 0x09, 0x6e, 0x24, 0x59, 0x90, 0x00, 0x48, 0x36, 0xb4, 0x20, 0x25, 0xb1, 0x24, 0x35, 0xc5,
	0xb1, 0x44, 0x82, 0x0f, 0x22, 0x0b, 0x17, 0x40, 0x92, 0x75, 0x71, 0x96, 0xe0, 0x47, 0x91, 0x75,
	0x71, 0x76, 0xb2, 0x39, 0xf1, 0x48, 0x8e, 0xf1, 0xc2, 0x23, 0x39, 0xc6, 0x07, 0x8f, 0xe4, 0x18,
	0x27, 0x3c, 0x96, 0x63, 0xb8, 0xf0, 0x58, 0x8e, 0xe1, 0xc6, 0x63, 0x39, 0x86, 0x28, 0xa5, 0xf4,
	0xcc, 0x12, 0xbd, 0xec, 0xcc, 0xe4, 0xc4, 0x14, 0x4b, 0x4b, 0xbd, 0xe4, 0xfc, 0x5c, 0xfd, 0xca,
	0xec, 0xcc, 0x12, 0xfd, 0x94, 0xc4, 0x92, 0xc4, 0xa4, 0xc4, 0xe2, 0x54, 0x7d, 0x50, 0x68, 0x25,
	0xb1, 0x81, 0x83, 0xce, 0x18, 0x10, 0x00, 0x00, 0xff, 0xff, 0xb2, 0x8d, 0xd6, 0x7d, 0x4f, 0x01,
	0x00, 0x00,
}

func (m *DbUserSession) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbUserSession) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbUserSession) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.UpdatedDC) > 0 {
		i -= len(m.UpdatedDC)
		copy(dAtA[i:], m.UpdatedDC)
		i = encodeVarintUserSession(dAtA, i, uint64(len(m.UpdatedDC)))
		i--
		dAtA[i] = 0x7a
	}
	if len(m.UpdatedAt) > 0 {
		i -= len(m.UpdatedAt)
		copy(dAtA[i:], m.UpdatedAt)
		i = encodeVarintUserSession(dAtA, i, uint64(len(m.UpdatedAt)))
		i--
		dAtA[i] = 0x72
	}
	if len(m.LoginInfo) > 0 {
		i -= len(m.LoginInfo)
		copy(dAtA[i:], m.LoginInfo)
		i = encodeVarintUserSession(dAtA, i, uint64(len(m.LoginInfo)))
		i--
		dAtA[i] = 0x5a
	}
	if len(m.LoginTime) > 0 {
		i -= len(m.LoginTime)
		copy(dAtA[i:], m.LoginTime)
		i = encodeVarintUserSession(dAtA, i, uint64(len(m.LoginTime)))
		i--
		dAtA[i] = 0x52
	}
	if m.SessionType != 0 {
		i = encodeVarintUserSession(dAtA, i, uint64(m.SessionType))
		i--
		dAtA[i] = 0x20
	}
	if len(m.UserRID) > 0 {
		i -= len(m.UserRID)
		copy(dAtA[i:], m.UserRID)
		i = encodeVarintUserSession(dAtA, i, uint64(len(m.UserRID)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.OrgRID) > 0 {
		i -= len(m.OrgRID)
		copy(dAtA[i:], m.OrgRID)
		i = encodeVarintUserSession(dAtA, i, uint64(len(m.OrgRID)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.RID) > 0 {
		i -= len(m.RID)
		copy(dAtA[i:], m.RID)
		i = encodeVarintUserSession(dAtA, i, uint64(len(m.RID)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func encodeVarintUserSession(dAtA []byte, offset int, v uint64) int {
	offset -= sovUserSession(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *DbUserSession) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.RID)
	if l > 0 {
		n += 1 + l + sovUserSession(uint64(l))
	}
	l = len(m.OrgRID)
	if l > 0 {
		n += 1 + l + sovUserSession(uint64(l))
	}
	l = len(m.UserRID)
	if l > 0 {
		n += 1 + l + sovUserSession(uint64(l))
	}
	if m.SessionType != 0 {
		n += 1 + sovUserSession(uint64(m.SessionType))
	}
	l = len(m.LoginTime)
	if l > 0 {
		n += 1 + l + sovUserSession(uint64(l))
	}
	l = len(m.LoginInfo)
	if l > 0 {
		n += 1 + l + sovUserSession(uint64(l))
	}
	l = len(m.UpdatedAt)
	if l > 0 {
		n += 1 + l + sovUserSession(uint64(l))
	}
	l = len(m.UpdatedDC)
	if l > 0 {
		n += 1 + l + sovUserSession(uint64(l))
	}
	return n
}

func sovUserSession(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozUserSession(x uint64) (n int) {
	return sovUserSession(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *DbUserSession) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserSession
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DbUserSession: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DbUserSession: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserSession
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserSession
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthUserSession
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.RID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OrgRID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserSession
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserSession
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthUserSession
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OrgRID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UserRID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserSession
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserSession
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthUserSession
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UserRID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SessionType", wireType)
			}
			m.SessionType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserSession
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SessionType |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LoginTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserSession
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserSession
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthUserSession
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.LoginTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LoginInfo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserSession
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserSession
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthUserSession
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.LoginInfo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 14:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UpdatedAt", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserSession
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserSession
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthUserSession
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UpdatedAt = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 15:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UpdatedDC", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserSession
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserSession
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthUserSession
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UpdatedDC = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUserSession(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserSession
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthUserSession
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipUserSession(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowUserSession
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowUserSession
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowUserSession
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthUserSession
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupUserSession
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthUserSession
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthUserSession        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowUserSession          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupUserSession = fmt.Errorf("proto: unexpected end of group")
)

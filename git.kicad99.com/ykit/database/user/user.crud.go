//Package user generated by ygen-gocrud. DO NOT EDIT.
//source: user.proto
package user

import (
	"git.kicad99.com/ykit/goutil/crud"
	"github.com/jackc/pgx/v4"
	"github.com/jackc/pgx/v4/pgxpool"
)

//Insert
func (this *DbUser) Insert(db *pgxpool.Conn) (r *crud.DMLResult, err error) {
	return crud.Insert(this, db)
}

//Update
func (this *DbUser) Update(db *pgxpool.Conn) (r *crud.DMLResult, err error) {
	return crud.Update(this, db)
}

//PartialUpdate
func (this *DbUser) PartialUpdate(updateCols []string, db *pgxpool.Conn) (r *crud.DMLResult, err error) {

	return crud.PartialUpdate(this, updateCols, db)
}

//Delete
func (this *DbUser) Delete(db *pgxpool.Conn) (r *crud.DMLR<PERSON>ult, err error) {
	return crud.Delete(this, db)
}

//SelectOne
func (this *DbUser) SelectOne(resultCols []string, db *pgxpool.Conn) (selectRow int32, err error) {

	return crud.YkitDbSelectOne(this, resultCols, []string{"RID"}, db)
}

//SelectByRids
func (this *DbUser) SelectByRids(resultCols []string, rids []string, db *pgxpool.Conn) (rows pgx.Rows, err error) {

	return crud.YkitDbSelectByRIDs(this, resultCols, rids, db)
}

//Insert
func (this *DbUserRole) Insert(db *pgxpool.Conn) (r *crud.DMLResult, err error) {
	return crud.Insert(this, db)
}

//Update
func (this *DbUserRole) Update(db *pgxpool.Conn) (r *crud.DMLResult, err error) {
	return crud.Update(this, db)
}

//PartialUpdate
func (this *DbUserRole) PartialUpdate(updateCols []string, db *pgxpool.Conn) (r *crud.DMLResult, err error) {

	return crud.PartialUpdate(this, updateCols, db)
}

//Delete
func (this *DbUserRole) Delete(db *pgxpool.Conn) (r *crud.DMLResult, err error) {
	return crud.Delete(this, db)
}

//SelectOne
func (this *DbUserRole) SelectOne(resultCols []string, db *pgxpool.Conn) (selectRow int32, err error) {

	return crud.YkitDbSelectOne(this, resultCols, []string{"RID"}, db)
}

//SelectByRids
func (this *DbUserRole) SelectByRids(resultCols []string, rids []string, db *pgxpool.Conn) (rows pgx.Rows, err error) {

	return crud.YkitDbSelectByRIDs(this, resultCols, rids, db)
}

// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: user.proto

package user

import (
	fmt "fmt"
	proto "github.com/gogo/protobuf/proto"
	io "io"
	math "math"
	math_bits "math/bits"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

//用户表
//@rpc crud pcrud
//@dbpost CREATE INDEX IF NOT EXISTS idxDbUserOrgRID on DbUser USING hash(OrgRID);
//@dbpost INSERT INTO dbuser(rid, orgrid, userid, usertype, username, phone, image, setting, loginname, loginpass, updatedat, updateddc)VALUES('00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', 'ttt', 0, 'ttt', '', NULL, '{}'::jsonb, 'ttt', 'UwhpDj0xMGNzZw8sXYZOLGBHmH/q9Wxf7aB6/EbT43Q=', now_utc(), '') ON CONFLICT  DO NOTHING;
type DbUser struct {
	//@db uuid primary key
	//行ID
	RID string `protobuf:"bytes,1,opt,name=RID,proto3" json:"RID,omitempty" ykit:"prk"`
	//@db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
	//用户所属的群组
	OrgRID string `protobuf:"bytes,2,opt,name=OrgRID,proto3" json:"OrgRID,omitempty" ykit:"notnull"`
	//@db varchar(16) not null unique
	//用户编号
	UserID string `protobuf:"bytes,3,opt,name=UserID,proto3" json:"UserID,omitempty" ykit:"unique,notnull"`
	//@db int
	//用户类型
	UserType int32 `protobuf:"varint,4,opt,name=UserType,proto3" json:"UserType,omitempty" ykit:"null"`
	//@db varchar(32) not null
	//用户名
	UserName string `protobuf:"bytes,5,opt,name=UserName,proto3" json:"UserName,omitempty" ykit:"notnull"`
	//@db text
	//备注
	Note string `protobuf:"bytes,6,opt,name=Note,proto3" json:"Note,omitempty" ykit:"null"`
	//@db text
	//用户电话
	Phone string `protobuf:"bytes,7,opt,name=Phone,proto3" json:"Phone,omitempty" ykit:"null"`
	//@db uuid REFERENCES DbImage(RID) ON DELETE SET NULL
	//用户图片
	Image string `protobuf:"bytes,8,opt,name=Image,proto3" json:"Image,omitempty" ykit:"null"`
	//@db jsonb not null default  '{}'::jsonb
	// 其它设置，可以在里面扩展需要用到的其它信息
	Setting string `protobuf:"bytes,9,opt,name=Setting,proto3" json:"Setting,omitempty" ykit:"notnull"`
	//@db varchar(32) unique
	//用户登录名,NULL(空)表示不能登录系统
	LoginName string `protobuf:"bytes,10,opt,name=LoginName,proto3" json:"LoginName,omitempty" ykit:"unique,null"`
	//@db text default ''
	//用户登录密码,为base64(sha256(user_name+user_password)),系统不保存真实的密码
	LoginPass string `protobuf:"bytes,11,opt,name=LoginPass,proto3" json:"LoginPass,omitempty"`
	//@db timestamp not null default now_utc()
	//数据最后修改时间
	UpdatedAt string `protobuf:"bytes,14,opt,name=UpdatedAt,proto3" json:"UpdatedAt,omitempty" ykit:"notnull"`
	//@db text
	//数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
	UpdatedDC string `protobuf:"bytes,15,opt,name=UpdatedDC,proto3" json:"UpdatedDC,omitempty" ykit:"null"`
}

func (m *DbUser) Reset()         { *m = DbUser{} }
func (m *DbUser) String() string { return proto.CompactTextString(m) }
func (*DbUser) ProtoMessage()    {}
func (*DbUser) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{0}
}
func (m *DbUser) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbUser) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbUser.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbUser) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbUser.Merge(m, src)
}
func (m *DbUser) XXX_Size() int {
	return m.Size()
}
func (m *DbUser) XXX_DiscardUnknown() {
	xxx_messageInfo_DbUser.DiscardUnknown(m)
}

var xxx_messageInfo_DbUser proto.InternalMessageInfo

func (m *DbUser) GetRID() string {
	if m != nil {
		return m.RID
	}
	return ""
}

func (m *DbUser) GetOrgRID() string {
	if m != nil {
		return m.OrgRID
	}
	return ""
}

func (m *DbUser) GetUserID() string {
	if m != nil {
		return m.UserID
	}
	return ""
}

func (m *DbUser) GetUserType() int32 {
	if m != nil {
		return m.UserType
	}
	return 0
}

func (m *DbUser) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

func (m *DbUser) GetNote() string {
	if m != nil {
		return m.Note
	}
	return ""
}

func (m *DbUser) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *DbUser) GetImage() string {
	if m != nil {
		return m.Image
	}
	return ""
}

func (m *DbUser) GetSetting() string {
	if m != nil {
		return m.Setting
	}
	return ""
}

func (m *DbUser) GetLoginName() string {
	if m != nil {
		return m.LoginName
	}
	return ""
}

func (m *DbUser) GetLoginPass() string {
	if m != nil {
		return m.LoginPass
	}
	return ""
}

func (m *DbUser) GetUpdatedAt() string {
	if m != nil {
		return m.UpdatedAt
	}
	return ""
}

func (m *DbUser) GetUpdatedDC() string {
	if m != nil {
		return m.UpdatedDC
	}
	return ""
}

//用户角色数据表
//一个用户可以有多个角色
//@rpc crud pcrud
//@dbpost CREATE INDEX IF NOT EXISTS idxDbUserRoleUserRID on DbUserRole USING hash(UserRID);
type DbUserRole struct {
	//@db uuid primary key
	//行ID
	RID string `protobuf:"bytes,1,opt,name=RID,proto3" json:"RID,omitempty" ykit:"prk"`
	//@db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
	//用户所属的群组
	OrgRID string `protobuf:"bytes,2,opt,name=OrgRID,proto3" json:"OrgRID,omitempty" ykit:"notnull"`
	//@db uuid not null REFERENCES DbUser(RID) ON DELETE CASCADE
	//用户rid
	UserRID string `protobuf:"bytes,3,opt,name=UserRID,proto3" json:"UserRID,omitempty" ykit:"notnull"`
	//@db uuid not null REFERENCES DbRole(RID) ON DELETE CASCADE
	//role rid
	RoleRID string `protobuf:"bytes,4,opt,name=RoleRID,proto3" json:"RoleRID,omitempty" ykit:"notnull"`
	//@db timestamp not null default now_utc()
	//数据最后修改时间
	UpdatedAt string `protobuf:"bytes,14,opt,name=UpdatedAt,proto3" json:"UpdatedAt,omitempty" ykit:"notnull"`
	//@db text
	//数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
	UpdatedDC string `protobuf:"bytes,15,opt,name=UpdatedDC,proto3" json:"UpdatedDC,omitempty" ykit:"null"`
}

func (m *DbUserRole) Reset()         { *m = DbUserRole{} }
func (m *DbUserRole) String() string { return proto.CompactTextString(m) }
func (*DbUserRole) ProtoMessage()    {}
func (*DbUserRole) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{1}
}
func (m *DbUserRole) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbUserRole) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbUserRole.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbUserRole) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbUserRole.Merge(m, src)
}
func (m *DbUserRole) XXX_Size() int {
	return m.Size()
}
func (m *DbUserRole) XXX_DiscardUnknown() {
	xxx_messageInfo_DbUserRole.DiscardUnknown(m)
}

var xxx_messageInfo_DbUserRole proto.InternalMessageInfo

func (m *DbUserRole) GetRID() string {
	if m != nil {
		return m.RID
	}
	return ""
}

func (m *DbUserRole) GetOrgRID() string {
	if m != nil {
		return m.OrgRID
	}
	return ""
}

func (m *DbUserRole) GetUserRID() string {
	if m != nil {
		return m.UserRID
	}
	return ""
}

func (m *DbUserRole) GetRoleRID() string {
	if m != nil {
		return m.RoleRID
	}
	return ""
}

func (m *DbUserRole) GetUpdatedAt() string {
	if m != nil {
		return m.UpdatedAt
	}
	return ""
}

func (m *DbUserRole) GetUpdatedDC() string {
	if m != nil {
		return m.UpdatedDC
	}
	return ""
}

func init() {
	proto.RegisterType((*DbUser)(nil), "user.DbUser")
	proto.RegisterType((*DbUserRole)(nil), "user.DbUserRole")
}

func init() { proto.RegisterFile("user.proto", fileDescriptor_116e343673f7ffaf) }

var fileDescriptor_116e343673f7ffaf = []byte{
	// 328 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xa4, 0x92, 0xb1, 0x4e, 0x32, 0x41,
	0x10, 0x80, 0x59, 0x38, 0x0e, 0x98, 0x3f, 0xf9, 0x35, 0x1b, 0x63, 0x26, 0xc6, 0x6c, 0x08, 0x15,
	0x15, 0x14, 0x56, 0x24, 0x36, 0xca, 0x35, 0x24, 0x06, 0xc9, 0x29, 0x8d, 0xdd, 0xc2, 0x6d, 0xce,
	0x0b, 0xc2, 0x92, 0xbb, 0xb5, 0xe0, 0x2d, 0x7c, 0x0a, 0x9f, 0xc5, 0x92, 0xc2, 0xc2, 0xd2, 0x70,
	0x2f, 0x62, 0x66, 0x97, 0x3b, 0xb1, 0x34, 0x76, 0xf3, 0x7d, 0x5f, 0x06, 0x2e, 0x93, 0x05, 0x78,
	0xce, 0x54, 0xda, 0x5b, 0xa7, 0xda, 0x68, 0xee, 0xd1, 0xdc, 0x79, 0xaf, 0x82, 0x1f, 0xcc, 0xa6,
	0x99, 0x4a, 0xf9, 0x31, 0xd4, 0xc2, 0x51, 0x80, 0xac, 0xcd, 0xba, 0xad, 0x90, 0x46, 0x7e, 0x0a,
	0xfe, 0x6d, 0x1a, 0x93, 0xac, 0x5a, 0xb9, 0x27, 0xf2, 0xb4, 0x31, 0x0a, 0xb0, 0xe6, 0xbc, 0x23,
	0x7e, 0x06, 0x4d, 0x9a, 0xee, 0x37, 0x6b, 0x85, 0x5e, 0x9b, 0x75, 0xeb, 0x61, 0xc9, 0x45, 0x1b,
	0xcb, 0xa5, 0xc2, 0xba, 0xdd, 0x2a, 0x99, 0x73, 0xf0, 0xc6, 0xda, 0x28, 0xf4, 0xad, 0xb7, 0x33,
	0x3f, 0x81, 0xfa, 0xe4, 0x51, 0xaf, 0x14, 0x36, 0xac, 0x74, 0x40, 0x76, 0xb4, 0x94, 0xb1, 0xc2,
	0xa6, 0xb3, 0x16, 0x38, 0x42, 0xe3, 0x4e, 0x19, 0x93, 0xac, 0x62, 0x6c, 0x59, 0x5f, 0x20, 0x3f,
	0x87, 0xd6, 0x8d, 0x8e, 0x93, 0x95, 0xfd, 0x5b, 0xb0, 0xed, 0x5b, 0x94, 0x75, 0x22, 0xb3, 0x0c,
	0xff, 0x1d, 0x54, 0x12, 0x54, 0xa7, 0xeb, 0x48, 0x1a, 0x15, 0x5d, 0x19, 0xfc, 0xef, 0x6a, 0x29,
	0x0e, 0x6a, 0x30, 0xc4, 0xa3, 0x1f, 0x35, 0x18, 0x76, 0x5e, 0x19, 0x80, 0x3b, 0x6b, 0xa8, 0x9f,
	0xd4, 0x2f, 0x4e, 0x8b, 0xd0, 0xb0, 0x5b, 0xe5, 0x6d, 0x0b, 0xa4, 0x42, 0xbf, 0x45, 0xc5, 0x73,
	0x65, 0x8f, 0x7f, 0xf9, 0xd0, 0xeb, 0xcb, 0xb7, 0x9d, 0x60, 0xdb, 0x9d, 0x60, 0x9f, 0x3b, 0xc1,
	0x5e, 0x72, 0x51, 0xd9, 0xe6, 0xa2, 0xf2, 0x91, 0x8b, 0xca, 0x43, 0x27, 0x4e, 0x4c, 0x6f, 0x91,
	0xcc, 0x65, 0x34, 0x18, 0xf4, 0xe6, 0x7a, 0xd9, 0xdf, 0x2c, 0x12, 0xd3, 0x8f, 0xa4, 0x91, 0x33,
	0x99, 0xa9, 0x3e, 0xbd, 0x9e, 0x99, 0x6f, 0x9f, 0xd2, 0xc5, 0x57, 0x00, 0x00, 0x00, 0xff, 0xff,
	0xa3, 0xa5, 0x2a, 0x15, 0x58, 0x02, 0x00, 0x00,
}

func (m *DbUser) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbUser) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbUser) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.UpdatedDC) > 0 {
		i -= len(m.UpdatedDC)
		copy(dAtA[i:], m.UpdatedDC)
		i = encodeVarintUser(dAtA, i, uint64(len(m.UpdatedDC)))
		i--
		dAtA[i] = 0x7a
	}
	if len(m.UpdatedAt) > 0 {
		i -= len(m.UpdatedAt)
		copy(dAtA[i:], m.UpdatedAt)
		i = encodeVarintUser(dAtA, i, uint64(len(m.UpdatedAt)))
		i--
		dAtA[i] = 0x72
	}
	if len(m.LoginPass) > 0 {
		i -= len(m.LoginPass)
		copy(dAtA[i:], m.LoginPass)
		i = encodeVarintUser(dAtA, i, uint64(len(m.LoginPass)))
		i--
		dAtA[i] = 0x5a
	}
	if len(m.LoginName) > 0 {
		i -= len(m.LoginName)
		copy(dAtA[i:], m.LoginName)
		i = encodeVarintUser(dAtA, i, uint64(len(m.LoginName)))
		i--
		dAtA[i] = 0x52
	}
	if len(m.Setting) > 0 {
		i -= len(m.Setting)
		copy(dAtA[i:], m.Setting)
		i = encodeVarintUser(dAtA, i, uint64(len(m.Setting)))
		i--
		dAtA[i] = 0x4a
	}
	if len(m.Image) > 0 {
		i -= len(m.Image)
		copy(dAtA[i:], m.Image)
		i = encodeVarintUser(dAtA, i, uint64(len(m.Image)))
		i--
		dAtA[i] = 0x42
	}
	if len(m.Phone) > 0 {
		i -= len(m.Phone)
		copy(dAtA[i:], m.Phone)
		i = encodeVarintUser(dAtA, i, uint64(len(m.Phone)))
		i--
		dAtA[i] = 0x3a
	}
	if len(m.Note) > 0 {
		i -= len(m.Note)
		copy(dAtA[i:], m.Note)
		i = encodeVarintUser(dAtA, i, uint64(len(m.Note)))
		i--
		dAtA[i] = 0x32
	}
	if len(m.UserName) > 0 {
		i -= len(m.UserName)
		copy(dAtA[i:], m.UserName)
		i = encodeVarintUser(dAtA, i, uint64(len(m.UserName)))
		i--
		dAtA[i] = 0x2a
	}
	if m.UserType != 0 {
		i = encodeVarintUser(dAtA, i, uint64(m.UserType))
		i--
		dAtA[i] = 0x20
	}
	if len(m.UserID) > 0 {
		i -= len(m.UserID)
		copy(dAtA[i:], m.UserID)
		i = encodeVarintUser(dAtA, i, uint64(len(m.UserID)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.OrgRID) > 0 {
		i -= len(m.OrgRID)
		copy(dAtA[i:], m.OrgRID)
		i = encodeVarintUser(dAtA, i, uint64(len(m.OrgRID)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.RID) > 0 {
		i -= len(m.RID)
		copy(dAtA[i:], m.RID)
		i = encodeVarintUser(dAtA, i, uint64(len(m.RID)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *DbUserRole) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbUserRole) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbUserRole) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.UpdatedDC) > 0 {
		i -= len(m.UpdatedDC)
		copy(dAtA[i:], m.UpdatedDC)
		i = encodeVarintUser(dAtA, i, uint64(len(m.UpdatedDC)))
		i--
		dAtA[i] = 0x7a
	}
	if len(m.UpdatedAt) > 0 {
		i -= len(m.UpdatedAt)
		copy(dAtA[i:], m.UpdatedAt)
		i = encodeVarintUser(dAtA, i, uint64(len(m.UpdatedAt)))
		i--
		dAtA[i] = 0x72
	}
	if len(m.RoleRID) > 0 {
		i -= len(m.RoleRID)
		copy(dAtA[i:], m.RoleRID)
		i = encodeVarintUser(dAtA, i, uint64(len(m.RoleRID)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.UserRID) > 0 {
		i -= len(m.UserRID)
		copy(dAtA[i:], m.UserRID)
		i = encodeVarintUser(dAtA, i, uint64(len(m.UserRID)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.OrgRID) > 0 {
		i -= len(m.OrgRID)
		copy(dAtA[i:], m.OrgRID)
		i = encodeVarintUser(dAtA, i, uint64(len(m.OrgRID)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.RID) > 0 {
		i -= len(m.RID)
		copy(dAtA[i:], m.RID)
		i = encodeVarintUser(dAtA, i, uint64(len(m.RID)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func encodeVarintUser(dAtA []byte, offset int, v uint64) int {
	offset -= sovUser(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *DbUser) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.RID)
	if l > 0 {
		n += 1 + l + sovUser(uint64(l))
	}
	l = len(m.OrgRID)
	if l > 0 {
		n += 1 + l + sovUser(uint64(l))
	}
	l = len(m.UserID)
	if l > 0 {
		n += 1 + l + sovUser(uint64(l))
	}
	if m.UserType != 0 {
		n += 1 + sovUser(uint64(m.UserType))
	}
	l = len(m.UserName)
	if l > 0 {
		n += 1 + l + sovUser(uint64(l))
	}
	l = len(m.Note)
	if l > 0 {
		n += 1 + l + sovUser(uint64(l))
	}
	l = len(m.Phone)
	if l > 0 {
		n += 1 + l + sovUser(uint64(l))
	}
	l = len(m.Image)
	if l > 0 {
		n += 1 + l + sovUser(uint64(l))
	}
	l = len(m.Setting)
	if l > 0 {
		n += 1 + l + sovUser(uint64(l))
	}
	l = len(m.LoginName)
	if l > 0 {
		n += 1 + l + sovUser(uint64(l))
	}
	l = len(m.LoginPass)
	if l > 0 {
		n += 1 + l + sovUser(uint64(l))
	}
	l = len(m.UpdatedAt)
	if l > 0 {
		n += 1 + l + sovUser(uint64(l))
	}
	l = len(m.UpdatedDC)
	if l > 0 {
		n += 1 + l + sovUser(uint64(l))
	}
	return n
}

func (m *DbUserRole) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.RID)
	if l > 0 {
		n += 1 + l + sovUser(uint64(l))
	}
	l = len(m.OrgRID)
	if l > 0 {
		n += 1 + l + sovUser(uint64(l))
	}
	l = len(m.UserRID)
	if l > 0 {
		n += 1 + l + sovUser(uint64(l))
	}
	l = len(m.RoleRID)
	if l > 0 {
		n += 1 + l + sovUser(uint64(l))
	}
	l = len(m.UpdatedAt)
	if l > 0 {
		n += 1 + l + sovUser(uint64(l))
	}
	l = len(m.UpdatedDC)
	if l > 0 {
		n += 1 + l + sovUser(uint64(l))
	}
	return n
}

func sovUser(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozUser(x uint64) (n int) {
	return sovUser(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *DbUser) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUser
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DbUser: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DbUser: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUser
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUser
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthUser
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.RID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OrgRID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUser
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUser
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthUser
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OrgRID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UserID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUser
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUser
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthUser
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UserID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field UserType", wireType)
			}
			m.UserType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUser
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UserType |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UserName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUser
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUser
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthUser
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UserName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Note", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUser
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUser
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthUser
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Note = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Phone", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUser
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUser
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthUser
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Phone = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Image", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUser
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUser
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthUser
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Image = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Setting", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUser
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUser
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthUser
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Setting = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LoginName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUser
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUser
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthUser
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.LoginName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LoginPass", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUser
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUser
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthUser
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.LoginPass = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 14:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UpdatedAt", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUser
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUser
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthUser
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UpdatedAt = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 15:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UpdatedDC", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUser
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUser
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthUser
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UpdatedDC = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUser(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUser
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthUser
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DbUserRole) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUser
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DbUserRole: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DbUserRole: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUser
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUser
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthUser
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.RID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OrgRID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUser
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUser
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthUser
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OrgRID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UserRID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUser
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUser
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthUser
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UserRID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RoleRID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUser
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUser
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthUser
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.RoleRID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 14:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UpdatedAt", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUser
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUser
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthUser
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UpdatedAt = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 15:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UpdatedDC", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUser
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUser
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthUser
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UpdatedDC = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUser(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUser
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthUser
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipUser(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowUser
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowUser
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowUser
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthUser
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupUser
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthUser
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthUser        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowUser          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupUser = fmt.Errorf("proto: unexpected end of group")
)

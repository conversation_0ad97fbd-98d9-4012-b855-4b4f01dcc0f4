// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: user.rpc.proto

package user

import (
	context "context"
	fmt "fmt"
	crud "git.kicad99.com/ykit/goutil/crud"
	proto "github.com/gogo/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

func init() { proto.RegisterFile("user.rpc.proto", fileDescriptor_edfd371b7eecadb7) }

var fileDescriptor_edfd371b7eecadb7 = []byte{
	// 412 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xec, 0x95, 0xbd, 0x6e, 0xe2, 0x40,
	0x14, 0x85, 0x31, 0xec, 0x22, 0x71, 0xb5, 0xfc, 0x68, 0x44, 0xe5, 0xc2, 0xc5, 0x36, 0x08, 0xb1,
	0x18, 0xd6, 0x2b, 0x21, 0xb1, 0xda, 0x0a, 0xd1, 0xac, 0x04, 0x5a, 0x2f, 0x11, 0x4d, 0xba, 0xc1,
	0xbe, 0x22, 0x23, 0x0c, 0xb6, 0xc6, 0xe3, 0x48, 0xbc, 0x45, 0x94, 0x36, 0x2f, 0x94, 0x92, 0x32,
	0x65, 0x04, 0x7d, 0xda, 0xb4, 0x91, 0x6d, 0xe2, 0xf0, 0xe3, 0xd8, 0x7e, 0x00, 0xba, 0x99, 0x7b,
	0xbf, 0x33, 0x96, 0xcf, 0x99, 0xab, 0x81, 0x8a, 0xe7, 0x22, 0x57, 0xb9, 0x63, 0xa8, 0x0e, 0xb7,
	0x85, 0x4d, 0xbe, 0xf8, 0x7b, 0x19, 0x0c, 0xee, 0x99, 0x61, 0x45, 0x86, 0x80, 0x08, 0xd7, 0xd5,
	0x60, 0x6d, 0x31, 0x57, 0x84, 0x05, 0xed, 0x25, 0x0f, 0xa5, 0x89, 0x63, 0x0c, 0x67, 0x53, 0x17,
	0x39, 0x69, 0x40, 0xf1, 0xef, 0xca, 0x45, 0x2e, 0xc8, 0x37, 0x35, 0x20, 0xc3, 0xba, 0x5c, 0x55,
	0x83, 0xf3, 0x86, 0xe3, 0xd1, 0x04, 0x5d, 0xcf, 0x12, 0x3e, 0x38, 0x75, 0x4c, 0x2a, 0x30, 0x0d,
	0x54, 0xa1, 0xac, 0x53, 0x2e, 0x18, 0xb5, 0xb2, 0xf1, 0x0d, 0x28, 0x0e, 0xd1, 0xc2, 0x74, 0xb0,
	0x09, 0xa5, 0x2b, 0xb4, 0xd0, 0x10, 0xff, 0x56, 0x48, 0x2a, 0x51, 0x57, 0xa7, 0x9c, 0x2e, 0xe5,
	0x23, 0x2d, 0xf9, 0x01, 0x10, 0xa2, 0x63, 0xba, 0x5a, 0x27, 0xb3, 0x5d, 0x89, 0x34, 0xe1, 0xeb,
	0x7f, 0x0f, 0xf9, 0x9a, 0xd4, 0x42, 0x30, 0xd8, 0xc4, 0xa3, 0x1a, 0x40, 0xd0, 0x1d, 0x50, 0x61,
	0xdc, 0xc4, 0xf0, 0xb5, 0x43, 0x7e, 0xc4, 0x5c, 0xd1, 0x95, 0xb4, 0xd7, 0x3c, 0x80, 0xce, 0x2f,
	0x8e, 0x1f, 0xd8, 0xd8, 0x7e, 0x77, 0xbc, 0x1e, 0x82, 0x3a, 0x67, 0xb7, 0xcc, 0xc2, 0x39, 0xc6,
	0xe3, 0xbd, 0x23, 0xd7, 0xe3, 0x35, 0x71, 0xce, 0xdf, 0x17, 0xa0, 0x1c, 0x5d, 0xf5, 0x89, 0x6d,
	0x21, 0x69, 0x45, 0xe6, 0x1f, 0xf1, 0x7e, 0xef, 0xfc, 0xf7, 0x5b, 0x51, 0x00, 0x19, 0x60, 0xed,
	0x34, 0x84, 0x6c, 0x1f, 0xd8, 0x07, 0x91, 0x01, 0x6e, 0x27, 0x85, 0x71, 0xa6, 0x27, 0xdd, 0xc4,
	0x40, 0xce, 0xf8, 0xc3, 0x50, 0x52, 0xae, 0xf5, 0x1e, 0xef, 0xa5, 0x8c, 0x42, 0xfd, 0x54, 0xb3,
	0x0f, 0xe5, 0xa1, 0x00, 0x95, 0x8f, 0x71, 0xb8, 0xa4, 0x92, 0x92, 0xca, 0xcf, 0xe4, 0x51, 0x89,
	0x93, 0xfc, 0xce, 0x30, 0x2e, 0x9f, 0xa4, 0x33, 0xf8, 0xf3, 0xb8, 0x55, 0xa4, 0xcd, 0x56, 0x91,
	0x9e, 0xb7, 0x8a, 0x74, 0xb7, 0x53, 0x72, 0x9b, 0x9d, 0x92, 0x7b, 0xda, 0x29, 0xb9, 0xeb, 0xef,
	0x73, 0x26, 0xd4, 0x05, 0x33, 0xa8, 0xd9, 0xef, 0xab, 0x86, 0xbd, 0xec, 0xac, 0x17, 0x4c, 0x74,
	0x4c, 0x2a, 0xe8, 0x8c, 0xba, 0xd8, 0xf1, 0x8f, 0x9b, 0x15, 0x83, 0x27, 0xe6, 0xd7, 0x5b, 0x00,
	0x00, 0x00, 0xff, 0xff, 0x31, 0x98, 0xc9, 0x5e, 0xa3, 0x06, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// RpcDbUserClient is the client API for RpcDbUser service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type RpcDbUserClient interface {
	//插入一行数据
	Insert(ctx context.Context, in *DbUser, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,全量修改一行数据
	Update(ctx context.Context, in *DbUser, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
	PartialUpdate(ctx context.Context, in *DbUser, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,删除一行数据
	Delete(ctx context.Context, in *DbUser, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
	SelectOne(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (*DbUser, error)
	//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
	SelectMany(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (RpcDbUser_SelectManyClient, error)
	//根据条件查询数据
	Query(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbUser_QueryClient, error)
	//根据条件查询数据,批量返回数据,crud.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbUser_QueryBatchClient, error)
}

type rpcDbUserClient struct {
	cc *grpc.ClientConn
}

func NewRpcDbUserClient(cc *grpc.ClientConn) RpcDbUserClient {
	return &rpcDbUserClient{cc}
}

func (c *rpcDbUserClient) Insert(ctx context.Context, in *DbUser, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/user.RpcDbUser/Insert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbUserClient) Update(ctx context.Context, in *DbUser, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/user.RpcDbUser/Update", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbUserClient) PartialUpdate(ctx context.Context, in *DbUser, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/user.RpcDbUser/PartialUpdate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbUserClient) Delete(ctx context.Context, in *DbUser, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/user.RpcDbUser/Delete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbUserClient) SelectOne(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (*DbUser, error) {
	out := new(DbUser)
	err := c.cc.Invoke(ctx, "/user.RpcDbUser/SelectOne", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbUserClient) SelectMany(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (RpcDbUser_SelectManyClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RpcDbUser_serviceDesc.Streams[0], "/user.RpcDbUser/SelectMany", opts...)
	if err != nil {
		return nil, err
	}
	x := &rpcDbUserSelectManyClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type RpcDbUser_SelectManyClient interface {
	Recv() (*DbUser, error)
	grpc.ClientStream
}

type rpcDbUserSelectManyClient struct {
	grpc.ClientStream
}

func (x *rpcDbUserSelectManyClient) Recv() (*DbUser, error) {
	m := new(DbUser)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *rpcDbUserClient) Query(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbUser_QueryClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RpcDbUser_serviceDesc.Streams[1], "/user.RpcDbUser/Query", opts...)
	if err != nil {
		return nil, err
	}
	x := &rpcDbUserQueryClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type RpcDbUser_QueryClient interface {
	Recv() (*DbUser, error)
	grpc.ClientStream
}

type rpcDbUserQueryClient struct {
	grpc.ClientStream
}

func (x *rpcDbUserQueryClient) Recv() (*DbUser, error) {
	m := new(DbUser)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *rpcDbUserClient) QueryBatch(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbUser_QueryBatchClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RpcDbUser_serviceDesc.Streams[2], "/user.RpcDbUser/QueryBatch", opts...)
	if err != nil {
		return nil, err
	}
	x := &rpcDbUserQueryBatchClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type RpcDbUser_QueryBatchClient interface {
	Recv() (*DbUserList, error)
	grpc.ClientStream
}

type rpcDbUserQueryBatchClient struct {
	grpc.ClientStream
}

func (x *rpcDbUserQueryBatchClient) Recv() (*DbUserList, error) {
	m := new(DbUserList)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// RpcDbUserServer is the server API for RpcDbUser service.
type RpcDbUserServer interface {
	//插入一行数据
	Insert(context.Context, *DbUser) (*crud.DMLResult, error)
	//以RID为条件,全量修改一行数据
	Update(context.Context, *DbUser) (*crud.DMLResult, error)
	//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
	PartialUpdate(context.Context, *DbUser) (*crud.DMLResult, error)
	//以RID为条件,删除一行数据
	Delete(context.Context, *DbUser) (*crud.DMLResult, error)
	//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
	SelectOne(context.Context, *crud.DMLParam) (*DbUser, error)
	//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
	SelectMany(*crud.DMLParam, RpcDbUser_SelectManyServer) error
	//根据条件查询数据
	Query(*crud.QueryParam, RpcDbUser_QueryServer) error
	//根据条件查询数据,批量返回数据,crud.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(*crud.QueryParam, RpcDbUser_QueryBatchServer) error
}

// UnimplementedRpcDbUserServer can be embedded to have forward compatible implementations.
type UnimplementedRpcDbUserServer struct {
}

func (*UnimplementedRpcDbUserServer) Insert(ctx context.Context, req *DbUser) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Insert not implemented")
}
func (*UnimplementedRpcDbUserServer) Update(ctx context.Context, req *DbUser) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Update not implemented")
}
func (*UnimplementedRpcDbUserServer) PartialUpdate(ctx context.Context, req *DbUser) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PartialUpdate not implemented")
}
func (*UnimplementedRpcDbUserServer) Delete(ctx context.Context, req *DbUser) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Delete not implemented")
}
func (*UnimplementedRpcDbUserServer) SelectOne(ctx context.Context, req *crud.DMLParam) (*DbUser, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SelectOne not implemented")
}
func (*UnimplementedRpcDbUserServer) SelectMany(req *crud.DMLParam, srv RpcDbUser_SelectManyServer) error {
	return status.Errorf(codes.Unimplemented, "method SelectMany not implemented")
}
func (*UnimplementedRpcDbUserServer) Query(req *crud.QueryParam, srv RpcDbUser_QueryServer) error {
	return status.Errorf(codes.Unimplemented, "method Query not implemented")
}
func (*UnimplementedRpcDbUserServer) QueryBatch(req *crud.QueryParam, srv RpcDbUser_QueryBatchServer) error {
	return status.Errorf(codes.Unimplemented, "method QueryBatch not implemented")
}

func RegisterRpcDbUserServer(s *grpc.Server, srv RpcDbUserServer) {
	s.RegisterService(&_RpcDbUser_serviceDesc, srv)
}

func _RpcDbUser_Insert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbUser)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbUserServer).Insert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.RpcDbUser/Insert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbUserServer).Insert(ctx, req.(*DbUser))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbUser_Update_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbUser)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbUserServer).Update(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.RpcDbUser/Update",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbUserServer).Update(ctx, req.(*DbUser))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbUser_PartialUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbUser)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbUserServer).PartialUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.RpcDbUser/PartialUpdate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbUserServer).PartialUpdate(ctx, req.(*DbUser))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbUser_Delete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbUser)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbUserServer).Delete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.RpcDbUser/Delete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbUserServer).Delete(ctx, req.(*DbUser))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbUser_SelectOne_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(crud.DMLParam)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbUserServer).SelectOne(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.RpcDbUser/SelectOne",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbUserServer).SelectOne(ctx, req.(*crud.DMLParam))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbUser_SelectMany_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.DMLParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RpcDbUserServer).SelectMany(m, &rpcDbUserSelectManyServer{stream})
}

type RpcDbUser_SelectManyServer interface {
	Send(*DbUser) error
	grpc.ServerStream
}

type rpcDbUserSelectManyServer struct {
	grpc.ServerStream
}

func (x *rpcDbUserSelectManyServer) Send(m *DbUser) error {
	return x.ServerStream.SendMsg(m)
}

func _RpcDbUser_Query_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.QueryParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RpcDbUserServer).Query(m, &rpcDbUserQueryServer{stream})
}

type RpcDbUser_QueryServer interface {
	Send(*DbUser) error
	grpc.ServerStream
}

type rpcDbUserQueryServer struct {
	grpc.ServerStream
}

func (x *rpcDbUserQueryServer) Send(m *DbUser) error {
	return x.ServerStream.SendMsg(m)
}

func _RpcDbUser_QueryBatch_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.QueryParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RpcDbUserServer).QueryBatch(m, &rpcDbUserQueryBatchServer{stream})
}

type RpcDbUser_QueryBatchServer interface {
	Send(*DbUserList) error
	grpc.ServerStream
}

type rpcDbUserQueryBatchServer struct {
	grpc.ServerStream
}

func (x *rpcDbUserQueryBatchServer) Send(m *DbUserList) error {
	return x.ServerStream.SendMsg(m)
}

var _RpcDbUser_serviceDesc = grpc.ServiceDesc{
	ServiceName: "user.RpcDbUser",
	HandlerType: (*RpcDbUserServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Insert",
			Handler:    _RpcDbUser_Insert_Handler,
		},
		{
			MethodName: "Update",
			Handler:    _RpcDbUser_Update_Handler,
		},
		{
			MethodName: "PartialUpdate",
			Handler:    _RpcDbUser_PartialUpdate_Handler,
		},
		{
			MethodName: "Delete",
			Handler:    _RpcDbUser_Delete_Handler,
		},
		{
			MethodName: "SelectOne",
			Handler:    _RpcDbUser_SelectOne_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "SelectMany",
			Handler:       _RpcDbUser_SelectMany_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "Query",
			Handler:       _RpcDbUser_Query_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "QueryBatch",
			Handler:       _RpcDbUser_QueryBatch_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "user.rpc.proto",
}

// PrpcDbUserClient is the client API for PrpcDbUser service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type PrpcDbUserClient interface {
	//插入一行数据
	Insert(ctx context.Context, in *DbUser, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,全量修改一行数据
	Update(ctx context.Context, in *DbUser, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
	PartialUpdate(ctx context.Context, in *DbUser, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,删除一行数据
	Delete(ctx context.Context, in *DbUser, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
	SelectOne(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (*DbUser, error)
	//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
	SelectMany(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (PrpcDbUser_SelectManyClient, error)
	//根据条件取得我有权限的数据
	Query(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbUser_QueryClient, error)
	//根据条件取得我有权限的数据,批量返回数据,crud.PrivilegeParam.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbUser_QueryBatchClient, error)
}

type prpcDbUserClient struct {
	cc *grpc.ClientConn
}

func NewPrpcDbUserClient(cc *grpc.ClientConn) PrpcDbUserClient {
	return &prpcDbUserClient{cc}
}

func (c *prpcDbUserClient) Insert(ctx context.Context, in *DbUser, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/user.PrpcDbUser/Insert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prpcDbUserClient) Update(ctx context.Context, in *DbUser, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/user.PrpcDbUser/Update", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prpcDbUserClient) PartialUpdate(ctx context.Context, in *DbUser, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/user.PrpcDbUser/PartialUpdate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prpcDbUserClient) Delete(ctx context.Context, in *DbUser, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/user.PrpcDbUser/Delete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prpcDbUserClient) SelectOne(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (*DbUser, error) {
	out := new(DbUser)
	err := c.cc.Invoke(ctx, "/user.PrpcDbUser/SelectOne", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prpcDbUserClient) SelectMany(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (PrpcDbUser_SelectManyClient, error) {
	stream, err := c.cc.NewStream(ctx, &_PrpcDbUser_serviceDesc.Streams[0], "/user.PrpcDbUser/SelectMany", opts...)
	if err != nil {
		return nil, err
	}
	x := &prpcDbUserSelectManyClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type PrpcDbUser_SelectManyClient interface {
	Recv() (*DbUser, error)
	grpc.ClientStream
}

type prpcDbUserSelectManyClient struct {
	grpc.ClientStream
}

func (x *prpcDbUserSelectManyClient) Recv() (*DbUser, error) {
	m := new(DbUser)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *prpcDbUserClient) Query(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbUser_QueryClient, error) {
	stream, err := c.cc.NewStream(ctx, &_PrpcDbUser_serviceDesc.Streams[1], "/user.PrpcDbUser/Query", opts...)
	if err != nil {
		return nil, err
	}
	x := &prpcDbUserQueryClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type PrpcDbUser_QueryClient interface {
	Recv() (*DbUser, error)
	grpc.ClientStream
}

type prpcDbUserQueryClient struct {
	grpc.ClientStream
}

func (x *prpcDbUserQueryClient) Recv() (*DbUser, error) {
	m := new(DbUser)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *prpcDbUserClient) QueryBatch(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbUser_QueryBatchClient, error) {
	stream, err := c.cc.NewStream(ctx, &_PrpcDbUser_serviceDesc.Streams[2], "/user.PrpcDbUser/QueryBatch", opts...)
	if err != nil {
		return nil, err
	}
	x := &prpcDbUserQueryBatchClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type PrpcDbUser_QueryBatchClient interface {
	Recv() (*DbUserList, error)
	grpc.ClientStream
}

type prpcDbUserQueryBatchClient struct {
	grpc.ClientStream
}

func (x *prpcDbUserQueryBatchClient) Recv() (*DbUserList, error) {
	m := new(DbUserList)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// PrpcDbUserServer is the server API for PrpcDbUser service.
type PrpcDbUserServer interface {
	//插入一行数据
	Insert(context.Context, *DbUser) (*crud.DMLResult, error)
	//以RID为条件,全量修改一行数据
	Update(context.Context, *DbUser) (*crud.DMLResult, error)
	//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
	PartialUpdate(context.Context, *DbUser) (*crud.DMLResult, error)
	//以RID为条件,删除一行数据
	Delete(context.Context, *DbUser) (*crud.DMLResult, error)
	//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
	SelectOne(context.Context, *crud.DMLParam) (*DbUser, error)
	//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
	SelectMany(*crud.DMLParam, PrpcDbUser_SelectManyServer) error
	//根据条件取得我有权限的数据
	Query(*crud.PrivilegeParam, PrpcDbUser_QueryServer) error
	//根据条件取得我有权限的数据,批量返回数据,crud.PrivilegeParam.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(*crud.PrivilegeParam, PrpcDbUser_QueryBatchServer) error
}

// UnimplementedPrpcDbUserServer can be embedded to have forward compatible implementations.
type UnimplementedPrpcDbUserServer struct {
}

func (*UnimplementedPrpcDbUserServer) Insert(ctx context.Context, req *DbUser) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Insert not implemented")
}
func (*UnimplementedPrpcDbUserServer) Update(ctx context.Context, req *DbUser) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Update not implemented")
}
func (*UnimplementedPrpcDbUserServer) PartialUpdate(ctx context.Context, req *DbUser) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PartialUpdate not implemented")
}
func (*UnimplementedPrpcDbUserServer) Delete(ctx context.Context, req *DbUser) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Delete not implemented")
}
func (*UnimplementedPrpcDbUserServer) SelectOne(ctx context.Context, req *crud.DMLParam) (*DbUser, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SelectOne not implemented")
}
func (*UnimplementedPrpcDbUserServer) SelectMany(req *crud.DMLParam, srv PrpcDbUser_SelectManyServer) error {
	return status.Errorf(codes.Unimplemented, "method SelectMany not implemented")
}
func (*UnimplementedPrpcDbUserServer) Query(req *crud.PrivilegeParam, srv PrpcDbUser_QueryServer) error {
	return status.Errorf(codes.Unimplemented, "method Query not implemented")
}
func (*UnimplementedPrpcDbUserServer) QueryBatch(req *crud.PrivilegeParam, srv PrpcDbUser_QueryBatchServer) error {
	return status.Errorf(codes.Unimplemented, "method QueryBatch not implemented")
}

func RegisterPrpcDbUserServer(s *grpc.Server, srv PrpcDbUserServer) {
	s.RegisterService(&_PrpcDbUser_serviceDesc, srv)
}

func _PrpcDbUser_Insert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbUser)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrpcDbUserServer).Insert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.PrpcDbUser/Insert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrpcDbUserServer).Insert(ctx, req.(*DbUser))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrpcDbUser_Update_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbUser)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrpcDbUserServer).Update(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.PrpcDbUser/Update",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrpcDbUserServer).Update(ctx, req.(*DbUser))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrpcDbUser_PartialUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbUser)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrpcDbUserServer).PartialUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.PrpcDbUser/PartialUpdate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrpcDbUserServer).PartialUpdate(ctx, req.(*DbUser))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrpcDbUser_Delete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbUser)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrpcDbUserServer).Delete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.PrpcDbUser/Delete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrpcDbUserServer).Delete(ctx, req.(*DbUser))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrpcDbUser_SelectOne_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(crud.DMLParam)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrpcDbUserServer).SelectOne(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.PrpcDbUser/SelectOne",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrpcDbUserServer).SelectOne(ctx, req.(*crud.DMLParam))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrpcDbUser_SelectMany_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.DMLParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(PrpcDbUserServer).SelectMany(m, &prpcDbUserSelectManyServer{stream})
}

type PrpcDbUser_SelectManyServer interface {
	Send(*DbUser) error
	grpc.ServerStream
}

type prpcDbUserSelectManyServer struct {
	grpc.ServerStream
}

func (x *prpcDbUserSelectManyServer) Send(m *DbUser) error {
	return x.ServerStream.SendMsg(m)
}

func _PrpcDbUser_Query_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.PrivilegeParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(PrpcDbUserServer).Query(m, &prpcDbUserQueryServer{stream})
}

type PrpcDbUser_QueryServer interface {
	Send(*DbUser) error
	grpc.ServerStream
}

type prpcDbUserQueryServer struct {
	grpc.ServerStream
}

func (x *prpcDbUserQueryServer) Send(m *DbUser) error {
	return x.ServerStream.SendMsg(m)
}

func _PrpcDbUser_QueryBatch_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.PrivilegeParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(PrpcDbUserServer).QueryBatch(m, &prpcDbUserQueryBatchServer{stream})
}

type PrpcDbUser_QueryBatchServer interface {
	Send(*DbUserList) error
	grpc.ServerStream
}

type prpcDbUserQueryBatchServer struct {
	grpc.ServerStream
}

func (x *prpcDbUserQueryBatchServer) Send(m *DbUserList) error {
	return x.ServerStream.SendMsg(m)
}

var _PrpcDbUser_serviceDesc = grpc.ServiceDesc{
	ServiceName: "user.PrpcDbUser",
	HandlerType: (*PrpcDbUserServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Insert",
			Handler:    _PrpcDbUser_Insert_Handler,
		},
		{
			MethodName: "Update",
			Handler:    _PrpcDbUser_Update_Handler,
		},
		{
			MethodName: "PartialUpdate",
			Handler:    _PrpcDbUser_PartialUpdate_Handler,
		},
		{
			MethodName: "Delete",
			Handler:    _PrpcDbUser_Delete_Handler,
		},
		{
			MethodName: "SelectOne",
			Handler:    _PrpcDbUser_SelectOne_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "SelectMany",
			Handler:       _PrpcDbUser_SelectMany_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "Query",
			Handler:       _PrpcDbUser_Query_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "QueryBatch",
			Handler:       _PrpcDbUser_QueryBatch_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "user.rpc.proto",
}

// RpcDbUserRoleClient is the client API for RpcDbUserRole service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type RpcDbUserRoleClient interface {
	//插入一行数据
	Insert(ctx context.Context, in *DbUserRole, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,全量修改一行数据
	Update(ctx context.Context, in *DbUserRole, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
	PartialUpdate(ctx context.Context, in *DbUserRole, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,删除一行数据
	Delete(ctx context.Context, in *DbUserRole, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
	SelectOne(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (*DbUserRole, error)
	//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
	SelectMany(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (RpcDbUserRole_SelectManyClient, error)
	//根据条件查询数据
	Query(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbUserRole_QueryClient, error)
	//根据条件查询数据,批量返回数据,crud.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbUserRole_QueryBatchClient, error)
}

type rpcDbUserRoleClient struct {
	cc *grpc.ClientConn
}

func NewRpcDbUserRoleClient(cc *grpc.ClientConn) RpcDbUserRoleClient {
	return &rpcDbUserRoleClient{cc}
}

func (c *rpcDbUserRoleClient) Insert(ctx context.Context, in *DbUserRole, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/user.RpcDbUserRole/Insert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbUserRoleClient) Update(ctx context.Context, in *DbUserRole, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/user.RpcDbUserRole/Update", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbUserRoleClient) PartialUpdate(ctx context.Context, in *DbUserRole, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/user.RpcDbUserRole/PartialUpdate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbUserRoleClient) Delete(ctx context.Context, in *DbUserRole, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/user.RpcDbUserRole/Delete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbUserRoleClient) SelectOne(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (*DbUserRole, error) {
	out := new(DbUserRole)
	err := c.cc.Invoke(ctx, "/user.RpcDbUserRole/SelectOne", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbUserRoleClient) SelectMany(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (RpcDbUserRole_SelectManyClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RpcDbUserRole_serviceDesc.Streams[0], "/user.RpcDbUserRole/SelectMany", opts...)
	if err != nil {
		return nil, err
	}
	x := &rpcDbUserRoleSelectManyClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type RpcDbUserRole_SelectManyClient interface {
	Recv() (*DbUserRole, error)
	grpc.ClientStream
}

type rpcDbUserRoleSelectManyClient struct {
	grpc.ClientStream
}

func (x *rpcDbUserRoleSelectManyClient) Recv() (*DbUserRole, error) {
	m := new(DbUserRole)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *rpcDbUserRoleClient) Query(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbUserRole_QueryClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RpcDbUserRole_serviceDesc.Streams[1], "/user.RpcDbUserRole/Query", opts...)
	if err != nil {
		return nil, err
	}
	x := &rpcDbUserRoleQueryClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type RpcDbUserRole_QueryClient interface {
	Recv() (*DbUserRole, error)
	grpc.ClientStream
}

type rpcDbUserRoleQueryClient struct {
	grpc.ClientStream
}

func (x *rpcDbUserRoleQueryClient) Recv() (*DbUserRole, error) {
	m := new(DbUserRole)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *rpcDbUserRoleClient) QueryBatch(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbUserRole_QueryBatchClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RpcDbUserRole_serviceDesc.Streams[2], "/user.RpcDbUserRole/QueryBatch", opts...)
	if err != nil {
		return nil, err
	}
	x := &rpcDbUserRoleQueryBatchClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type RpcDbUserRole_QueryBatchClient interface {
	Recv() (*DbUserRoleList, error)
	grpc.ClientStream
}

type rpcDbUserRoleQueryBatchClient struct {
	grpc.ClientStream
}

func (x *rpcDbUserRoleQueryBatchClient) Recv() (*DbUserRoleList, error) {
	m := new(DbUserRoleList)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// RpcDbUserRoleServer is the server API for RpcDbUserRole service.
type RpcDbUserRoleServer interface {
	//插入一行数据
	Insert(context.Context, *DbUserRole) (*crud.DMLResult, error)
	//以RID为条件,全量修改一行数据
	Update(context.Context, *DbUserRole) (*crud.DMLResult, error)
	//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
	PartialUpdate(context.Context, *DbUserRole) (*crud.DMLResult, error)
	//以RID为条件,删除一行数据
	Delete(context.Context, *DbUserRole) (*crud.DMLResult, error)
	//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
	SelectOne(context.Context, *crud.DMLParam) (*DbUserRole, error)
	//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
	SelectMany(*crud.DMLParam, RpcDbUserRole_SelectManyServer) error
	//根据条件查询数据
	Query(*crud.QueryParam, RpcDbUserRole_QueryServer) error
	//根据条件查询数据,批量返回数据,crud.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(*crud.QueryParam, RpcDbUserRole_QueryBatchServer) error
}

// UnimplementedRpcDbUserRoleServer can be embedded to have forward compatible implementations.
type UnimplementedRpcDbUserRoleServer struct {
}

func (*UnimplementedRpcDbUserRoleServer) Insert(ctx context.Context, req *DbUserRole) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Insert not implemented")
}
func (*UnimplementedRpcDbUserRoleServer) Update(ctx context.Context, req *DbUserRole) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Update not implemented")
}
func (*UnimplementedRpcDbUserRoleServer) PartialUpdate(ctx context.Context, req *DbUserRole) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PartialUpdate not implemented")
}
func (*UnimplementedRpcDbUserRoleServer) Delete(ctx context.Context, req *DbUserRole) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Delete not implemented")
}
func (*UnimplementedRpcDbUserRoleServer) SelectOne(ctx context.Context, req *crud.DMLParam) (*DbUserRole, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SelectOne not implemented")
}
func (*UnimplementedRpcDbUserRoleServer) SelectMany(req *crud.DMLParam, srv RpcDbUserRole_SelectManyServer) error {
	return status.Errorf(codes.Unimplemented, "method SelectMany not implemented")
}
func (*UnimplementedRpcDbUserRoleServer) Query(req *crud.QueryParam, srv RpcDbUserRole_QueryServer) error {
	return status.Errorf(codes.Unimplemented, "method Query not implemented")
}
func (*UnimplementedRpcDbUserRoleServer) QueryBatch(req *crud.QueryParam, srv RpcDbUserRole_QueryBatchServer) error {
	return status.Errorf(codes.Unimplemented, "method QueryBatch not implemented")
}

func RegisterRpcDbUserRoleServer(s *grpc.Server, srv RpcDbUserRoleServer) {
	s.RegisterService(&_RpcDbUserRole_serviceDesc, srv)
}

func _RpcDbUserRole_Insert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbUserRole)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbUserRoleServer).Insert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.RpcDbUserRole/Insert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbUserRoleServer).Insert(ctx, req.(*DbUserRole))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbUserRole_Update_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbUserRole)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbUserRoleServer).Update(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.RpcDbUserRole/Update",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbUserRoleServer).Update(ctx, req.(*DbUserRole))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbUserRole_PartialUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbUserRole)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbUserRoleServer).PartialUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.RpcDbUserRole/PartialUpdate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbUserRoleServer).PartialUpdate(ctx, req.(*DbUserRole))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbUserRole_Delete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbUserRole)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbUserRoleServer).Delete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.RpcDbUserRole/Delete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbUserRoleServer).Delete(ctx, req.(*DbUserRole))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbUserRole_SelectOne_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(crud.DMLParam)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbUserRoleServer).SelectOne(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.RpcDbUserRole/SelectOne",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbUserRoleServer).SelectOne(ctx, req.(*crud.DMLParam))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbUserRole_SelectMany_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.DMLParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RpcDbUserRoleServer).SelectMany(m, &rpcDbUserRoleSelectManyServer{stream})
}

type RpcDbUserRole_SelectManyServer interface {
	Send(*DbUserRole) error
	grpc.ServerStream
}

type rpcDbUserRoleSelectManyServer struct {
	grpc.ServerStream
}

func (x *rpcDbUserRoleSelectManyServer) Send(m *DbUserRole) error {
	return x.ServerStream.SendMsg(m)
}

func _RpcDbUserRole_Query_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.QueryParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RpcDbUserRoleServer).Query(m, &rpcDbUserRoleQueryServer{stream})
}

type RpcDbUserRole_QueryServer interface {
	Send(*DbUserRole) error
	grpc.ServerStream
}

type rpcDbUserRoleQueryServer struct {
	grpc.ServerStream
}

func (x *rpcDbUserRoleQueryServer) Send(m *DbUserRole) error {
	return x.ServerStream.SendMsg(m)
}

func _RpcDbUserRole_QueryBatch_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.QueryParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RpcDbUserRoleServer).QueryBatch(m, &rpcDbUserRoleQueryBatchServer{stream})
}

type RpcDbUserRole_QueryBatchServer interface {
	Send(*DbUserRoleList) error
	grpc.ServerStream
}

type rpcDbUserRoleQueryBatchServer struct {
	grpc.ServerStream
}

func (x *rpcDbUserRoleQueryBatchServer) Send(m *DbUserRoleList) error {
	return x.ServerStream.SendMsg(m)
}

var _RpcDbUserRole_serviceDesc = grpc.ServiceDesc{
	ServiceName: "user.RpcDbUserRole",
	HandlerType: (*RpcDbUserRoleServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Insert",
			Handler:    _RpcDbUserRole_Insert_Handler,
		},
		{
			MethodName: "Update",
			Handler:    _RpcDbUserRole_Update_Handler,
		},
		{
			MethodName: "PartialUpdate",
			Handler:    _RpcDbUserRole_PartialUpdate_Handler,
		},
		{
			MethodName: "Delete",
			Handler:    _RpcDbUserRole_Delete_Handler,
		},
		{
			MethodName: "SelectOne",
			Handler:    _RpcDbUserRole_SelectOne_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "SelectMany",
			Handler:       _RpcDbUserRole_SelectMany_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "Query",
			Handler:       _RpcDbUserRole_Query_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "QueryBatch",
			Handler:       _RpcDbUserRole_QueryBatch_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "user.rpc.proto",
}

// PrpcDbUserRoleClient is the client API for PrpcDbUserRole service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type PrpcDbUserRoleClient interface {
	//插入一行数据
	Insert(ctx context.Context, in *DbUserRole, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,全量修改一行数据
	Update(ctx context.Context, in *DbUserRole, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
	PartialUpdate(ctx context.Context, in *DbUserRole, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,删除一行数据
	Delete(ctx context.Context, in *DbUserRole, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
	SelectOne(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (*DbUserRole, error)
	//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
	SelectMany(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (PrpcDbUserRole_SelectManyClient, error)
	//根据条件取得我有权限的数据
	Query(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbUserRole_QueryClient, error)
	//根据条件取得我有权限的数据,批量返回数据,crud.PrivilegeParam.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbUserRole_QueryBatchClient, error)
}

type prpcDbUserRoleClient struct {
	cc *grpc.ClientConn
}

func NewPrpcDbUserRoleClient(cc *grpc.ClientConn) PrpcDbUserRoleClient {
	return &prpcDbUserRoleClient{cc}
}

func (c *prpcDbUserRoleClient) Insert(ctx context.Context, in *DbUserRole, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/user.PrpcDbUserRole/Insert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prpcDbUserRoleClient) Update(ctx context.Context, in *DbUserRole, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/user.PrpcDbUserRole/Update", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prpcDbUserRoleClient) PartialUpdate(ctx context.Context, in *DbUserRole, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/user.PrpcDbUserRole/PartialUpdate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prpcDbUserRoleClient) Delete(ctx context.Context, in *DbUserRole, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/user.PrpcDbUserRole/Delete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prpcDbUserRoleClient) SelectOne(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (*DbUserRole, error) {
	out := new(DbUserRole)
	err := c.cc.Invoke(ctx, "/user.PrpcDbUserRole/SelectOne", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prpcDbUserRoleClient) SelectMany(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (PrpcDbUserRole_SelectManyClient, error) {
	stream, err := c.cc.NewStream(ctx, &_PrpcDbUserRole_serviceDesc.Streams[0], "/user.PrpcDbUserRole/SelectMany", opts...)
	if err != nil {
		return nil, err
	}
	x := &prpcDbUserRoleSelectManyClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type PrpcDbUserRole_SelectManyClient interface {
	Recv() (*DbUserRole, error)
	grpc.ClientStream
}

type prpcDbUserRoleSelectManyClient struct {
	grpc.ClientStream
}

func (x *prpcDbUserRoleSelectManyClient) Recv() (*DbUserRole, error) {
	m := new(DbUserRole)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *prpcDbUserRoleClient) Query(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbUserRole_QueryClient, error) {
	stream, err := c.cc.NewStream(ctx, &_PrpcDbUserRole_serviceDesc.Streams[1], "/user.PrpcDbUserRole/Query", opts...)
	if err != nil {
		return nil, err
	}
	x := &prpcDbUserRoleQueryClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type PrpcDbUserRole_QueryClient interface {
	Recv() (*DbUserRole, error)
	grpc.ClientStream
}

type prpcDbUserRoleQueryClient struct {
	grpc.ClientStream
}

func (x *prpcDbUserRoleQueryClient) Recv() (*DbUserRole, error) {
	m := new(DbUserRole)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *prpcDbUserRoleClient) QueryBatch(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbUserRole_QueryBatchClient, error) {
	stream, err := c.cc.NewStream(ctx, &_PrpcDbUserRole_serviceDesc.Streams[2], "/user.PrpcDbUserRole/QueryBatch", opts...)
	if err != nil {
		return nil, err
	}
	x := &prpcDbUserRoleQueryBatchClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type PrpcDbUserRole_QueryBatchClient interface {
	Recv() (*DbUserRoleList, error)
	grpc.ClientStream
}

type prpcDbUserRoleQueryBatchClient struct {
	grpc.ClientStream
}

func (x *prpcDbUserRoleQueryBatchClient) Recv() (*DbUserRoleList, error) {
	m := new(DbUserRoleList)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// PrpcDbUserRoleServer is the server API for PrpcDbUserRole service.
type PrpcDbUserRoleServer interface {
	//插入一行数据
	Insert(context.Context, *DbUserRole) (*crud.DMLResult, error)
	//以RID为条件,全量修改一行数据
	Update(context.Context, *DbUserRole) (*crud.DMLResult, error)
	//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
	PartialUpdate(context.Context, *DbUserRole) (*crud.DMLResult, error)
	//以RID为条件,删除一行数据
	Delete(context.Context, *DbUserRole) (*crud.DMLResult, error)
	//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
	SelectOne(context.Context, *crud.DMLParam) (*DbUserRole, error)
	//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
	SelectMany(*crud.DMLParam, PrpcDbUserRole_SelectManyServer) error
	//根据条件取得我有权限的数据
	Query(*crud.PrivilegeParam, PrpcDbUserRole_QueryServer) error
	//根据条件取得我有权限的数据,批量返回数据,crud.PrivilegeParam.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(*crud.PrivilegeParam, PrpcDbUserRole_QueryBatchServer) error
}

// UnimplementedPrpcDbUserRoleServer can be embedded to have forward compatible implementations.
type UnimplementedPrpcDbUserRoleServer struct {
}

func (*UnimplementedPrpcDbUserRoleServer) Insert(ctx context.Context, req *DbUserRole) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Insert not implemented")
}
func (*UnimplementedPrpcDbUserRoleServer) Update(ctx context.Context, req *DbUserRole) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Update not implemented")
}
func (*UnimplementedPrpcDbUserRoleServer) PartialUpdate(ctx context.Context, req *DbUserRole) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PartialUpdate not implemented")
}
func (*UnimplementedPrpcDbUserRoleServer) Delete(ctx context.Context, req *DbUserRole) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Delete not implemented")
}
func (*UnimplementedPrpcDbUserRoleServer) SelectOne(ctx context.Context, req *crud.DMLParam) (*DbUserRole, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SelectOne not implemented")
}
func (*UnimplementedPrpcDbUserRoleServer) SelectMany(req *crud.DMLParam, srv PrpcDbUserRole_SelectManyServer) error {
	return status.Errorf(codes.Unimplemented, "method SelectMany not implemented")
}
func (*UnimplementedPrpcDbUserRoleServer) Query(req *crud.PrivilegeParam, srv PrpcDbUserRole_QueryServer) error {
	return status.Errorf(codes.Unimplemented, "method Query not implemented")
}
func (*UnimplementedPrpcDbUserRoleServer) QueryBatch(req *crud.PrivilegeParam, srv PrpcDbUserRole_QueryBatchServer) error {
	return status.Errorf(codes.Unimplemented, "method QueryBatch not implemented")
}

func RegisterPrpcDbUserRoleServer(s *grpc.Server, srv PrpcDbUserRoleServer) {
	s.RegisterService(&_PrpcDbUserRole_serviceDesc, srv)
}

func _PrpcDbUserRole_Insert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbUserRole)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrpcDbUserRoleServer).Insert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.PrpcDbUserRole/Insert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrpcDbUserRoleServer).Insert(ctx, req.(*DbUserRole))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrpcDbUserRole_Update_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbUserRole)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrpcDbUserRoleServer).Update(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.PrpcDbUserRole/Update",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrpcDbUserRoleServer).Update(ctx, req.(*DbUserRole))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrpcDbUserRole_PartialUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbUserRole)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrpcDbUserRoleServer).PartialUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.PrpcDbUserRole/PartialUpdate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrpcDbUserRoleServer).PartialUpdate(ctx, req.(*DbUserRole))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrpcDbUserRole_Delete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbUserRole)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrpcDbUserRoleServer).Delete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.PrpcDbUserRole/Delete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrpcDbUserRoleServer).Delete(ctx, req.(*DbUserRole))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrpcDbUserRole_SelectOne_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(crud.DMLParam)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrpcDbUserRoleServer).SelectOne(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.PrpcDbUserRole/SelectOne",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrpcDbUserRoleServer).SelectOne(ctx, req.(*crud.DMLParam))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrpcDbUserRole_SelectMany_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.DMLParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(PrpcDbUserRoleServer).SelectMany(m, &prpcDbUserRoleSelectManyServer{stream})
}

type PrpcDbUserRole_SelectManyServer interface {
	Send(*DbUserRole) error
	grpc.ServerStream
}

type prpcDbUserRoleSelectManyServer struct {
	grpc.ServerStream
}

func (x *prpcDbUserRoleSelectManyServer) Send(m *DbUserRole) error {
	return x.ServerStream.SendMsg(m)
}

func _PrpcDbUserRole_Query_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.PrivilegeParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(PrpcDbUserRoleServer).Query(m, &prpcDbUserRoleQueryServer{stream})
}

type PrpcDbUserRole_QueryServer interface {
	Send(*DbUserRole) error
	grpc.ServerStream
}

type prpcDbUserRoleQueryServer struct {
	grpc.ServerStream
}

func (x *prpcDbUserRoleQueryServer) Send(m *DbUserRole) error {
	return x.ServerStream.SendMsg(m)
}

func _PrpcDbUserRole_QueryBatch_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.PrivilegeParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(PrpcDbUserRoleServer).QueryBatch(m, &prpcDbUserRoleQueryBatchServer{stream})
}

type PrpcDbUserRole_QueryBatchServer interface {
	Send(*DbUserRoleList) error
	grpc.ServerStream
}

type prpcDbUserRoleQueryBatchServer struct {
	grpc.ServerStream
}

func (x *prpcDbUserRoleQueryBatchServer) Send(m *DbUserRoleList) error {
	return x.ServerStream.SendMsg(m)
}

var _PrpcDbUserRole_serviceDesc = grpc.ServiceDesc{
	ServiceName: "user.PrpcDbUserRole",
	HandlerType: (*PrpcDbUserRoleServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Insert",
			Handler:    _PrpcDbUserRole_Insert_Handler,
		},
		{
			MethodName: "Update",
			Handler:    _PrpcDbUserRole_Update_Handler,
		},
		{
			MethodName: "PartialUpdate",
			Handler:    _PrpcDbUserRole_PartialUpdate_Handler,
		},
		{
			MethodName: "Delete",
			Handler:    _PrpcDbUserRole_Delete_Handler,
		},
		{
			MethodName: "SelectOne",
			Handler:    _PrpcDbUserRole_SelectOne_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "SelectMany",
			Handler:       _PrpcDbUserRole_SelectMany_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "Query",
			Handler:       _PrpcDbUserRole_Query_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "QueryBatch",
			Handler:       _PrpcDbUserRole_QueryBatch_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "user.rpc.proto",
}

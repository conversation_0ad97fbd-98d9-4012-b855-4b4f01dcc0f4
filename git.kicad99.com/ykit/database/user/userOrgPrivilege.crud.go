//Package user generated by ygen-gocrud. DO NOT EDIT.
//source: userOrgPrivilege.proto
package user

import (
	"git.kicad99.com/ykit/goutil/crud"
	"github.com/jackc/pgx/v4"
	"github.com/jackc/pgx/v4/pgxpool"
)

//Insert
func (this *DbUserOrgPrivilege) Insert(db *pgxpool.Conn) (r *crud.DMLResult, err error) {
	return crud.Insert(this, db)
}

//Update
func (this *DbUserOrgPrivilege) Update(db *pgxpool.Conn) (r *crud.DMLResult, err error) {
	return crud.Update(this, db)
}

//PartialUpdate
func (this *DbUserOrgPrivilege) PartialUpdate(updateCols []string, db *pgxpool.Conn) (r *crud.DMLResult, err error) {

	return crud.PartialUpdate(this, updateCols, db)
}

//Delete
func (this *DbUserOrgPrivilege) Delete(db *pgxpool.Conn) (r *crud.DMLResult, err error) {
	return crud.Delete(this, db)
}

//SelectOne
func (this *DbUserOrgPrivilege) SelectOne(resultCols []string, db *pgxpool.Conn) (selectRow int32, err error) {

	return crud.YkitDbSelectOne(this, resultCols, []string{"RID"}, db)
}

//SelectByRids
func (this *DbUserOrgPrivilege) SelectByRids(resultCols []string, rids []string, db *pgxpool.Conn) (rows pgx.Rows, err error) {

	return crud.YkitDbSelectByRIDs(this, resultCols, rids, db)
}

// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: userOrgPrivilege.list.proto

package user

import (
	fmt "fmt"
	proto "github.com/gogo/protobuf/proto"
	io "io"
	math "math"
	math_bits "math/bits"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

// DbUserOrgPrivilege list
type DbUserOrgPrivilegeList struct {
	//批次号，从0开始
	BatchNo uint32 `protobuf:"varint,1,opt,name=BatchNo,proto3" json:"BatchNo,omitempty" ykit:"nodb"`
	//rows offset,从0开始
	RowsOffset uint32 `protobuf:"varint,2,opt,name=RowsOffset,proto3" json:"RowsOffset,omitempty" ykit:"nodb"`
	//本批次数据
	Rows []*DbUserOrgPrivilege `protobuf:"bytes,3,rep,name=Rows,proto3" json:"Rows,omitempty" ykit:"nodb"`
}

func (m *DbUserOrgPrivilegeList) Reset()         { *m = DbUserOrgPrivilegeList{} }
func (m *DbUserOrgPrivilegeList) String() string { return proto.CompactTextString(m) }
func (*DbUserOrgPrivilegeList) ProtoMessage()    {}
func (*DbUserOrgPrivilegeList) Descriptor() ([]byte, []int) {
	return fileDescriptor_e36855d898511c5c, []int{0}
}
func (m *DbUserOrgPrivilegeList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbUserOrgPrivilegeList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbUserOrgPrivilegeList.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbUserOrgPrivilegeList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbUserOrgPrivilegeList.Merge(m, src)
}
func (m *DbUserOrgPrivilegeList) XXX_Size() int {
	return m.Size()
}
func (m *DbUserOrgPrivilegeList) XXX_DiscardUnknown() {
	xxx_messageInfo_DbUserOrgPrivilegeList.DiscardUnknown(m)
}

var xxx_messageInfo_DbUserOrgPrivilegeList proto.InternalMessageInfo

func (m *DbUserOrgPrivilegeList) GetBatchNo() uint32 {
	if m != nil {
		return m.BatchNo
	}
	return 0
}

func (m *DbUserOrgPrivilegeList) GetRowsOffset() uint32 {
	if m != nil {
		return m.RowsOffset
	}
	return 0
}

func (m *DbUserOrgPrivilegeList) GetRows() []*DbUserOrgPrivilege {
	if m != nil {
		return m.Rows
	}
	return nil
}

func init() {
	proto.RegisterType((*DbUserOrgPrivilegeList)(nil), "user.DbUserOrgPrivilegeList")
}

func init() { proto.RegisterFile("userOrgPrivilege.list.proto", fileDescriptor_e36855d898511c5c) }

var fileDescriptor_e36855d898511c5c = []byte{
	// 207 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe2, 0x92, 0x2e, 0x2d, 0x4e, 0x2d,
	0xf2, 0x2f, 0x4a, 0x0f, 0x28, 0xca, 0x2c, 0xcb, 0xcc, 0x49, 0x4d, 0x4f, 0xd5, 0xcb, 0xc9, 0x2c,
	0x2e, 0xd1, 0x2b, 0x28, 0xca, 0x2f, 0xc9, 0x17, 0x62, 0x01, 0x49, 0x4a, 0x89, 0x61, 0x28, 0x01,
	0xcb, 0x2a, 0x35, 0x30, 0x72, 0x89, 0xb9, 0x24, 0x85, 0xa2, 0x49, 0xfa, 0x64, 0x16, 0x97, 0x08,
	0x49, 0x70, 0xb1, 0x3b, 0x25, 0x96, 0x24, 0x67, 0xf8, 0xe5, 0x4b, 0x30, 0x2a, 0x30, 0x6a, 0xf0,
	0x06, 0xc1, 0xb8, 0x42, 0x72, 0x5c, 0x5c, 0x41, 0xf9, 0xe5, 0xc5, 0xfe, 0x69, 0x69, 0xc5, 0xa9,
	0x25, 0x12, 0x4c, 0x60, 0x49, 0x24, 0x11, 0x21, 0x1d, 0x2e, 0x16, 0x10, 0x4f, 0x82, 0x59, 0x81,
	0x59, 0x83, 0xdb, 0x48, 0x42, 0x0f, 0x64, 0xb7, 0x1e, 0xa6, 0x2d, 0x41, 0x60, 0x55, 0x4e, 0x36,
	0x27, 0x1e, 0xc9, 0x31, 0x5e, 0x78, 0x24, 0xc7, 0xf8, 0xe0, 0x91, 0x1c, 0xe3, 0x84, 0xc7, 0x72,
	0x0c, 0x17, 0x1e, 0xcb, 0x31, 0xdc, 0x78, 0x2c, 0xc7, 0x10, 0xa5, 0x94, 0x9e, 0x59, 0xa2, 0x97,
	0x9d, 0x99, 0x9c, 0x98, 0x62, 0x69, 0xa9, 0x97, 0x9c, 0x9f, 0xab, 0x5f, 0x99, 0x9d, 0x59, 0xa2,
	0x9f, 0x92, 0x58, 0x92, 0x98, 0x94, 0x58, 0x9c, 0xaa, 0x0f, 0x32, 0x36, 0x89, 0x0d, 0xec, 0x0f,
	0x63, 0x40, 0x00, 0x00, 0x00, 0xff, 0xff, 0x4f, 0xe2, 0x16, 0x21, 0x04, 0x01, 0x00, 0x00,
}

func (m *DbUserOrgPrivilegeList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbUserOrgPrivilegeList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbUserOrgPrivilegeList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for iNdEx := len(m.Rows) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Rows[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintUserOrgPrivilegeList(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x1a
		}
	}
	if m.RowsOffset != 0 {
		i = encodeVarintUserOrgPrivilegeList(dAtA, i, uint64(m.RowsOffset))
		i--
		dAtA[i] = 0x10
	}
	if m.BatchNo != 0 {
		i = encodeVarintUserOrgPrivilegeList(dAtA, i, uint64(m.BatchNo))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func encodeVarintUserOrgPrivilegeList(dAtA []byte, offset int, v uint64) int {
	offset -= sovUserOrgPrivilegeList(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *DbUserOrgPrivilegeList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.BatchNo != 0 {
		n += 1 + sovUserOrgPrivilegeList(uint64(m.BatchNo))
	}
	if m.RowsOffset != 0 {
		n += 1 + sovUserOrgPrivilegeList(uint64(m.RowsOffset))
	}
	if len(m.Rows) > 0 {
		for _, e := range m.Rows {
			l = e.Size()
			n += 1 + l + sovUserOrgPrivilegeList(uint64(l))
		}
	}
	return n
}

func sovUserOrgPrivilegeList(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozUserOrgPrivilegeList(x uint64) (n int) {
	return sovUserOrgPrivilegeList(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *DbUserOrgPrivilegeList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserOrgPrivilegeList
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DbUserOrgPrivilegeList: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DbUserOrgPrivilegeList: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field BatchNo", wireType)
			}
			m.BatchNo = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserOrgPrivilegeList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BatchNo |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RowsOffset", wireType)
			}
			m.RowsOffset = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserOrgPrivilegeList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RowsOffset |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rows", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserOrgPrivilegeList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserOrgPrivilegeList
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthUserOrgPrivilegeList
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rows = append(m.Rows, &DbUserOrgPrivilege{})
			if err := m.Rows[len(m.Rows)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUserOrgPrivilegeList(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserOrgPrivilegeList
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthUserOrgPrivilegeList
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipUserOrgPrivilegeList(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowUserOrgPrivilegeList
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowUserOrgPrivilegeList
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowUserOrgPrivilegeList
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthUserOrgPrivilegeList
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupUserOrgPrivilegeList
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthUserOrgPrivilegeList
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthUserOrgPrivilegeList        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowUserOrgPrivilegeList          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupUserOrgPrivilegeList = fmt.Errorf("proto: unexpected end of group")
)

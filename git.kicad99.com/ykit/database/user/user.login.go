package user

import (
	"context"
	"crypto/sha256"
	"database/sql"
	"encoding/base64"
	"errors"
	"fmt"
	"strconv"
	"time"

	"git.kicad99.com/ykit/database/org"
	"git.kicad99.com/ykit/goutil"
	"git.kicad99.com/ykit/goutil/crud"
	"git.kicad99.com/ykit/goutil/pgxdb"
	"git.kicad99.com/ykit/goutil/rpc"
	"git.kicad99.com/ykit/goutil/yreflect"
	"github.com/jackc/pgx/v4"
	"github.com/jackc/pgx/v4/pgxpool"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
)

type YkitUserLogin struct {
	UnimplementedRpcUserServer
}

func (*YkitUserLogin) Login(ctx context.Context, login *ReqUserLogin) (res *ResUserLogin, err error) {
	var badLoginPara = errors.New("bad login param")
	if len(login.LoginName) == 0 || len(login.LoginTimeStr) == 0 {
		return nil, badLoginPara
	}
	loginTime, err := time.ParseInLocation(goutil.ISOTimeFormat, login.LoginTimeStr, time.UTC)
	if err != nil {
		return nil, err
	}
	diffTime := time.Since(loginTime)
	if diffTime.Minutes() > 3 {
		return nil, errors.New("time need update:" + goutil.NowTimeStrInUtc())
	}
	db, err := pgxdb.GetDbConn(login.System)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	switch login.LoginType {
	case 0:
		//username+pw login
		dbuser := &DbUser{LoginName: login.LoginName}
		_, err := crud.YkitDbSelectOne(dbuser, nil, []string{"LoginName"}, db)
		if err != nil {
			if err == pgx.ErrNoRows || err == sql.ErrNoRows {
				//用户不存在
				res = &ResUserLogin{
					Code: 4,
				}
				return res, nil
			}
			return nil, err
		}
		h := sha256.Sum256([]byte(dbuser.LoginPass + login.LoginTimeStr))
		pwHash := base64.StdEncoding.EncodeToString(h[:])
		if pwHash == login.LoginPass {
			newSessionRid := goutil.NewUUIDv1()
			nowTimeStr := goutil.NowTimeStrInUtc()
			userSession := &DbUserSession{
				RID:         newSessionRid.String(),
				OrgRID:      dbuser.OrgRID,
				UserRID:     dbuser.RID,
				SessionType: 0,
				LoginTime:   nowTimeStr,
				LoginInfo:   "{}",
				UpdatedAt:   nowTimeStr,
				UpdatedDC:   "bys",
			}

			_, err = userSession.Insert(db)

			if err != nil {
				return nil, err
			}

			crud.PutUserInSessionCache(newSessionRid.String(), dbuser.RID)
			crud.DelUserOrgsCache(dbuser.RID)

			res = &ResUserLogin{
				Code:       100,
				Err:        "",
				UserRID:    dbuser.RID,
				UserOrgRID: dbuser.OrgRID,
				SessionRID: newSessionRid.String(),
				SysUTCTime: goutil.NowTimeStrInUtc(),
			}
			trailer := metadata.Pairs("ykit-sys", login.System, "ykit-userrid", res.UserRID,
				"ykit-sid", newSessionRid.String(), "ykit-userorgrid", dbuser.OrgRID)
			_ = grpc.SetTrailer(ctx, trailer)
		} else {
			res = &ResUserLogin{
				Code: 1,
			}
		}
		return res, nil

	case 1:
		//sid login
		if len(login.LoginName) < 12 {
			//sid 为uuid,长度至少12
			return nil, badLoginPara
		}

		res = &ResUserLogin{
			Code: 2,
		}

		userRID, exist, err := crud.GetUserRIDinSession(login.System, login.LoginName, true, db)
		if err == pgx.ErrNoRows || err == sql.ErrNoRows {
			return res, nil
		}
		if err != nil {
			return nil, err
		}
		if !exist {
			return res, nil
		}

		dbuser := &DbUser{
			RID: userRID,
		}
		selectRow, err := dbuser.SelectOne(nil, db)
		if err != nil {
			return nil, err
		}
		if selectRow == 0 {
			res.Code = 3
			return res, nil
		}

		res.Code = 100
		res.UserRID = dbuser.RID
		res.UserOrgRID = dbuser.OrgRID
		res.SessionRID = login.LoginName
		res.SysUTCTime = goutil.NowTimeStrInUtc()
		crud.PutUserInSessionCache(login.LoginName, dbuser.RID)
		crud.DelUserOrgsCache(dbuser.RID)

		trailer := metadata.Pairs("ykit-sys", login.System, "ykit-userrid", res.UserRID,
			"ykit-sid", res.SessionRID, "ykit-userorgrid", dbuser.OrgRID)
		_ = grpc.SetTrailer(ctx, trailer)
		return res, nil

	default:
		return nil, errors.New("bad login type")

	}
}

func (*YkitUserLogin) IsUserHasPermission(ctx context.Context, req *ReqUserHasPermission) (*rpc.RpcCommon, error) {
	hasPermission, err := crud.IsUserHasPermission(req.System, req.UserRID, req.PermissionType, req.PermissionValue)
	if err != nil && err != crud.EnoPermission {
		return nil, err
	}
	r := &rpc.RpcCommon{
		Code: -1,
	}
	if !hasPermission {
		return r, nil
	}

	r.Code = 100

	return r, nil
}

func (*YkitUserLogin) IsUserHasOrgPrivilege(ctx context.Context, privilege *ReqUserHasOrgPrivilege) (*rpc.RpcCommon, error) {
	db, err := pgxdb.GetDbConn(privilege.System)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	r := &rpc.RpcCommon{}

	var tmpstr string
	//直接有一行
	qsql := `select OrgRID from DbUserOrgPrivilege where UserRID=$1 and OrgRID =$2 ;`
	err = db.QueryRow(context.Background(), qsql, privilege.UserRID, privilege.OrgRID).Scan(&tmpstr)
	if err == nil {
		r.Code = 100
		return r, nil
	}

	//通过检查下级是否包含
	qsql = `with RECURSIVE sub_org as (
select OrgRID as OrgRID from DbUserOrgPrivilege where UserRID= $1 and IncludeChildren=1
union
select rid as OrgRID from DbOrg
join sub_org d_o_r
on DbOrg.ParentRID=d_o_r.OrgRID
)

select OrgRID from sub_org;`

	rows, err := db.Query(context.Background(), qsql, privilege.UserRID)
	if err != nil {
		r.Err = err.Error()
		r.Code = 1
		return r, nil
	}

	defer rows.Close()

	for rows.Next() {
		err = rows.Scan(&tmpstr)
		if err != nil {
			r.Err = err.Error()
			r.Code = 2
			return r, nil
		}

		if tmpstr == privilege.OrgRID {
			r.Code = 100
			return r, nil
		}
	}

	r.Code = 3

	return r, nil
}

func (*YkitUserLogin) MyOrgRID(req *rpc.RpcCommon, srv RpcUser_MyOrgRIDServer) error {
	db, err := CheckReqIsOK(req)
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)

	sqlStr := `select * from myorg($1);`
	rows, err := db.Query(context.Background(), sqlStr, req.Err)
	if err != nil {
		return err
	}
	defer rows.Close()

	var tmpOrgRID string
	rowsCount := 0
	dborg := &org.DbOrg{}
	for rows.Next() {
		err = rows.Scan(&tmpOrgRID)
		if err != nil {
			return err
		}
		dborg.RID = tmpOrgRID
		rowsCount++
		err = srv.Send(dborg)
		if err != nil {
			return err
		}
	}

	trailer := metadata.Pairs("rows", strconv.Itoa(rowsCount))
	srv.SetTrailer(trailer)

	return nil
}

//func (y YkitUserLogin) MyOrgData(req *rpc.RpcCommon, srv RpcUser_MyOrgDataServer) error {
//	userRID, exist, err := crud.GetUserRIDinSession(req.System, req.SessionRID, true)
//	if err != nil {
//		return err
//	}
//	if !exist {
//		return crud.ENoSuchSessionFound
//	}
//	if userRID != req.Err {
//		return errors.New("userrid not matched with sessionrid")
//	}
//
//	dmlParam := &crud.DMLParam{}
//	err = dmlParam.Unmarshal(req.Body)
//	if err != nil {
//		return err
//	}
//
//	db, err := pgxdb.GetDbConn(req.System)
//	if err != nil {
//		return err
//	}
//	defer pgxdb.ReleaseDbConn(db)
//
//	resultColsStr := crud.ResultCols2SQLString(dmlParam.ResultColumn)
//	sqlStr := "select " + resultColsStr + " from dborg where rid in(select * from myorg($1));"
//	rows, err := db.Query(context.Background(), sqlStr, req.Err)
//	if err != nil {
//		return err
//	}
//	defer rows.Close()
//
//	rowsCount := 0
//	rowsName := crud.RowsColNames2GoFieldNames(rows, &org.DbOrg{})
//	for rows.Next() {
//		dborg := &org.DbOrg{}
//		if len(rowsName) < 1 {
//			rowsName = crud.RowsColNames2GoFieldNames(rows, dborg)
//
//		}
//		vals, err := rows.Values()
//		if err == nil {
//			_ = yreflect.SetFields(dborg, rowsName, vals)
//		} else {
//			return err
//		}
//		rowsCount++
//		err = srv.Send(dborg)
//		if err != nil {
//			return err
//		}
//	}
//
//	trailer := metadata.Pairs("rows", strconv.Itoa(rowsCount))
//	srv.SetTrailer(trailer)
//
//	return nil
//}

func CheckReqIsOK(req *rpc.RpcCommon) (*pgxpool.Conn, error) {
	db, err := pgxdb.GetDbConn(req.System)
	if err != nil {
		return nil, err
	}

	userRID, exist, err := crud.GetUserRIDinSession(req.System, req.SessionRID, true, db)
	if err != nil {
		pgxdb.ReleaseDbConn(db)
		return nil, err
	}
	if !exist {
		pgxdb.ReleaseDbConn(db)
		return nil, crud.ENoSuchSessionFound
	}
	if userRID != req.Err {
		pgxdb.ReleaseDbConn(db)
		return nil, errors.New("userrid not matched with sessionrid")
	}

	return db, nil
}

func (*YkitUserLogin) MyRole(req *rpc.RpcCommon, srv RpcUser_MyRoleServer) error {
	sqlStr := `select * from DbRole where RID in (select RoleRID from DbUserRole where UserRID = $1) ;
`

	db, err := CheckReqIsOK(req)
	if err != nil {
		return err
	}

	defer pgxdb.ReleaseDbConn(db)

	rows, err := db.Query(context.Background(), sqlStr, req.Err)
	if err != nil {
		return err
	}
	defer rows.Close()

	rowsCount := 0
	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbRole{})
	for rows.Next() {
		role := &DbRole{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, role)

		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(role, rowsName, vals)
		} else {
			return err
		}
		rowsCount++
		err = srv.Send(role)
		if err != nil {
			return err
		}
	}

	trailer := metadata.Pairs("rows", strconv.Itoa(rowsCount))
	srv.SetTrailer(trailer)

	return nil
}

func (*YkitUserLogin) MyPermission(req *rpc.RpcCommon, srv RpcUser_MyPermissionServer) error {
	sqlStr := `select RID, PermissionType,PermissionName,PermissionValue from DbPermission 
where rid in (select PermissionRID from DbRolePermission 
where RoleRID in (select RoleRID from DbUserRole where UserRID = $1) );
`

	db, err := CheckReqIsOK(req)
	if err != nil {
		return err
	}

	defer pgxdb.ReleaseDbConn(db)

	rows, err := db.Query(context.Background(), sqlStr, req.Err)
	if err != nil {
		return err
	}
	defer rows.Close()

	rowsCount := 0
	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbPermission{})
	for rows.Next() {
		permission := &DbPermission{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, permission)

		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(permission, rowsName, vals)
		} else {
			return err
		}
		rowsCount++
		err = srv.Send(permission)
		if err != nil {
			return err
		}
	}

	trailer := metadata.Pairs("rows", strconv.Itoa(rowsCount))
	srv.SetTrailer(trailer)

	return nil

}

func (*YkitUserLogin) UpdateMySetting(ctx context.Context, req *DbUser) (*crud.DMLResult, error) {
	sys, userRid, _ := pgxdb.GetYkitInfoFromMetadata(ctx)
	if userRid != req.RID {
		//userrid is not equal
		return nil, fmt.Errorf("bad login: sys:%s, req:%s", userRid, req.RID)
	}

	db, err := pgxdb.GetDbConn(sys)
	if err != nil {
		return nil, err
	}

	defer pgxdb.ReleaseDbConn(db)

	param, err := crud.GetDMLParamFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}

	dmlResult, err := crud.PartialUpdate(req, param.KeyColumn, db)
	return dmlResult, err
}

func (*YkitUserLogin) SysUTCTime(ctx context.Context, req *rpc.RpcCommon) (*rpc.RpcCommon, error) {
	req.System = goutil.NowTimeStrInUtc()
	return req, nil
}

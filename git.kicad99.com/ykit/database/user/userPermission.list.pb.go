// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: userPermission.list.proto

package user

import (
	fmt "fmt"
	proto "github.com/gogo/protobuf/proto"
	io "io"
	math "math"
	math_bits "math/bits"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

// DbPermission list
type DbPermissionList struct {
	//批次号，从0开始
	BatchNo uint32 `protobuf:"varint,1,opt,name=BatchNo,proto3" json:"BatchNo,omitempty" ykit:"nodb"`
	//rows offset,从0开始
	RowsOffset uint32 `protobuf:"varint,2,opt,name=RowsOffset,proto3" json:"RowsOffset,omitempty" ykit:"nodb"`
	//本批次数据
	Rows []*DbPermission `protobuf:"bytes,3,rep,name=Rows,proto3" json:"Rows,omitempty" ykit:"nodb"`
}

func (m *DbPermissionList) Reset()         { *m = DbPermissionList{} }
func (m *DbPermissionList) String() string { return proto.CompactTextString(m) }
func (*DbPermissionList) ProtoMessage()    {}
func (*DbPermissionList) Descriptor() ([]byte, []int) {
	return fileDescriptor_72c58d995b73ca44, []int{0}
}
func (m *DbPermissionList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbPermissionList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbPermissionList.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbPermissionList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbPermissionList.Merge(m, src)
}
func (m *DbPermissionList) XXX_Size() int {
	return m.Size()
}
func (m *DbPermissionList) XXX_DiscardUnknown() {
	xxx_messageInfo_DbPermissionList.DiscardUnknown(m)
}

var xxx_messageInfo_DbPermissionList proto.InternalMessageInfo

func (m *DbPermissionList) GetBatchNo() uint32 {
	if m != nil {
		return m.BatchNo
	}
	return 0
}

func (m *DbPermissionList) GetRowsOffset() uint32 {
	if m != nil {
		return m.RowsOffset
	}
	return 0
}

func (m *DbPermissionList) GetRows() []*DbPermission {
	if m != nil {
		return m.Rows
	}
	return nil
}

// DbRole list
type DbRoleList struct {
	//批次号，从0开始
	BatchNo uint32 `protobuf:"varint,1,opt,name=BatchNo,proto3" json:"BatchNo,omitempty" ykit:"nodb"`
	//rows offset,从0开始
	RowsOffset uint32 `protobuf:"varint,2,opt,name=RowsOffset,proto3" json:"RowsOffset,omitempty" ykit:"nodb"`
	//本批次数据
	Rows []*DbRole `protobuf:"bytes,3,rep,name=Rows,proto3" json:"Rows,omitempty" ykit:"nodb"`
}

func (m *DbRoleList) Reset()         { *m = DbRoleList{} }
func (m *DbRoleList) String() string { return proto.CompactTextString(m) }
func (*DbRoleList) ProtoMessage()    {}
func (*DbRoleList) Descriptor() ([]byte, []int) {
	return fileDescriptor_72c58d995b73ca44, []int{1}
}
func (m *DbRoleList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbRoleList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbRoleList.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbRoleList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbRoleList.Merge(m, src)
}
func (m *DbRoleList) XXX_Size() int {
	return m.Size()
}
func (m *DbRoleList) XXX_DiscardUnknown() {
	xxx_messageInfo_DbRoleList.DiscardUnknown(m)
}

var xxx_messageInfo_DbRoleList proto.InternalMessageInfo

func (m *DbRoleList) GetBatchNo() uint32 {
	if m != nil {
		return m.BatchNo
	}
	return 0
}

func (m *DbRoleList) GetRowsOffset() uint32 {
	if m != nil {
		return m.RowsOffset
	}
	return 0
}

func (m *DbRoleList) GetRows() []*DbRole {
	if m != nil {
		return m.Rows
	}
	return nil
}

// DbRolePermission list
type DbRolePermissionList struct {
	//批次号，从0开始
	BatchNo uint32 `protobuf:"varint,1,opt,name=BatchNo,proto3" json:"BatchNo,omitempty" ykit:"nodb"`
	//rows offset,从0开始
	RowsOffset uint32 `protobuf:"varint,2,opt,name=RowsOffset,proto3" json:"RowsOffset,omitempty" ykit:"nodb"`
	//本批次数据
	Rows []*DbRolePermission `protobuf:"bytes,3,rep,name=Rows,proto3" json:"Rows,omitempty" ykit:"nodb"`
}

func (m *DbRolePermissionList) Reset()         { *m = DbRolePermissionList{} }
func (m *DbRolePermissionList) String() string { return proto.CompactTextString(m) }
func (*DbRolePermissionList) ProtoMessage()    {}
func (*DbRolePermissionList) Descriptor() ([]byte, []int) {
	return fileDescriptor_72c58d995b73ca44, []int{2}
}
func (m *DbRolePermissionList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbRolePermissionList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbRolePermissionList.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbRolePermissionList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbRolePermissionList.Merge(m, src)
}
func (m *DbRolePermissionList) XXX_Size() int {
	return m.Size()
}
func (m *DbRolePermissionList) XXX_DiscardUnknown() {
	xxx_messageInfo_DbRolePermissionList.DiscardUnknown(m)
}

var xxx_messageInfo_DbRolePermissionList proto.InternalMessageInfo

func (m *DbRolePermissionList) GetBatchNo() uint32 {
	if m != nil {
		return m.BatchNo
	}
	return 0
}

func (m *DbRolePermissionList) GetRowsOffset() uint32 {
	if m != nil {
		return m.RowsOffset
	}
	return 0
}

func (m *DbRolePermissionList) GetRows() []*DbRolePermission {
	if m != nil {
		return m.Rows
	}
	return nil
}

func init() {
	proto.RegisterType((*DbPermissionList)(nil), "user.DbPermissionList")
	proto.RegisterType((*DbRoleList)(nil), "user.DbRoleList")
	proto.RegisterType((*DbRolePermissionList)(nil), "user.DbRolePermissionList")
}

func init() { proto.RegisterFile("userPermission.list.proto", fileDescriptor_72c58d995b73ca44) }

var fileDescriptor_72c58d995b73ca44 = []byte{
	// 242 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe2, 0x92, 0x2c, 0x2d, 0x4e, 0x2d,
	0x0a, 0x48, 0x2d, 0xca, 0xcd, 0x2c, 0x2e, 0xce, 0xcc, 0xcf, 0xd3, 0xcb, 0xc9, 0x2c, 0x2e, 0xd1,
	0x2b, 0x28, 0xca, 0x2f, 0xc9, 0x17, 0x62, 0x01, 0x49, 0x49, 0x89, 0xa0, 0x29, 0x00, 0xcb, 0x29,
	0x95, 0x70, 0x09, 0xb8, 0x24, 0x21, 0x44, 0x7d, 0x32, 0x8b, 0x4b, 0x84, 0x24, 0xb8, 0xd8, 0x9d,
	0x12, 0x4b, 0x92, 0x33, 0xfc, 0xf2, 0x25, 0x18, 0x15, 0x18, 0x35, 0x78, 0x83, 0x60, 0x5c, 0x21,
	0x39, 0x2e, 0xae, 0xa0, 0xfc, 0xf2, 0x62, 0xff, 0xb4, 0xb4, 0xe2, 0xd4, 0x12, 0x09, 0x26, 0xb0,
	0x24, 0x92, 0x88, 0x90, 0x1a, 0x17, 0x0b, 0x88, 0x27, 0xc1, 0xac, 0xc0, 0xac, 0xc1, 0x6d, 0x24,
	0xa4, 0x07, 0xb2, 0x52, 0x0f, 0xd9, 0xfc, 0x20, 0xb0, 0xbc, 0x52, 0x06, 0x17, 0x97, 0x4b, 0x52,
	0x50, 0x7e, 0x4e, 0x2a, 0x85, 0xf6, 0x29, 0xa0, 0xd8, 0xc7, 0x03, 0xb3, 0x0f, 0x64, 0x32, 0xd4,
	0xa6, 0x1a, 0x2e, 0x11, 0x08, 0x9f, 0x6a, 0x7e, 0xd4, 0x42, 0xb1, 0x53, 0x0c, 0xd9, 0x4e, 0x74,
	0x7f, 0x3a, 0xd9, 0x9c, 0x78, 0x24, 0xc7, 0x78, 0xe1, 0x91, 0x1c, 0xe3, 0x83, 0x47, 0x72, 0x8c,
	0x13, 0x1e, 0xcb, 0x31, 0x5c, 0x78, 0x2c, 0xc7, 0x70, 0xe3, 0xb1, 0x1c, 0x43, 0x94, 0x52, 0x7a,
	0x66, 0x89, 0x5e, 0x76, 0x66, 0x72, 0x62, 0x8a, 0xa5, 0xa5, 0x5e, 0x72, 0x7e, 0xae, 0x7e, 0x65,
	0x76, 0x66, 0x89, 0x7e, 0x4a, 0x62, 0x49, 0x62, 0x52, 0x62, 0x71, 0xaa, 0x3e, 0xc8, 0xd0, 0x24,
	0x36, 0x70, 0x14, 0x19, 0x03, 0x02, 0x00, 0x00, 0xff, 0xff, 0x0c, 0xb6, 0xc8, 0xb9, 0xdb, 0x01,
	0x00, 0x00,
}

func (m *DbPermissionList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbPermissionList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbPermissionList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for iNdEx := len(m.Rows) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Rows[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintUserPermissionList(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x1a
		}
	}
	if m.RowsOffset != 0 {
		i = encodeVarintUserPermissionList(dAtA, i, uint64(m.RowsOffset))
		i--
		dAtA[i] = 0x10
	}
	if m.BatchNo != 0 {
		i = encodeVarintUserPermissionList(dAtA, i, uint64(m.BatchNo))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *DbRoleList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbRoleList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbRoleList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for iNdEx := len(m.Rows) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Rows[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintUserPermissionList(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x1a
		}
	}
	if m.RowsOffset != 0 {
		i = encodeVarintUserPermissionList(dAtA, i, uint64(m.RowsOffset))
		i--
		dAtA[i] = 0x10
	}
	if m.BatchNo != 0 {
		i = encodeVarintUserPermissionList(dAtA, i, uint64(m.BatchNo))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *DbRolePermissionList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbRolePermissionList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbRolePermissionList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for iNdEx := len(m.Rows) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Rows[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintUserPermissionList(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x1a
		}
	}
	if m.RowsOffset != 0 {
		i = encodeVarintUserPermissionList(dAtA, i, uint64(m.RowsOffset))
		i--
		dAtA[i] = 0x10
	}
	if m.BatchNo != 0 {
		i = encodeVarintUserPermissionList(dAtA, i, uint64(m.BatchNo))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func encodeVarintUserPermissionList(dAtA []byte, offset int, v uint64) int {
	offset -= sovUserPermissionList(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *DbPermissionList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.BatchNo != 0 {
		n += 1 + sovUserPermissionList(uint64(m.BatchNo))
	}
	if m.RowsOffset != 0 {
		n += 1 + sovUserPermissionList(uint64(m.RowsOffset))
	}
	if len(m.Rows) > 0 {
		for _, e := range m.Rows {
			l = e.Size()
			n += 1 + l + sovUserPermissionList(uint64(l))
		}
	}
	return n
}

func (m *DbRoleList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.BatchNo != 0 {
		n += 1 + sovUserPermissionList(uint64(m.BatchNo))
	}
	if m.RowsOffset != 0 {
		n += 1 + sovUserPermissionList(uint64(m.RowsOffset))
	}
	if len(m.Rows) > 0 {
		for _, e := range m.Rows {
			l = e.Size()
			n += 1 + l + sovUserPermissionList(uint64(l))
		}
	}
	return n
}

func (m *DbRolePermissionList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.BatchNo != 0 {
		n += 1 + sovUserPermissionList(uint64(m.BatchNo))
	}
	if m.RowsOffset != 0 {
		n += 1 + sovUserPermissionList(uint64(m.RowsOffset))
	}
	if len(m.Rows) > 0 {
		for _, e := range m.Rows {
			l = e.Size()
			n += 1 + l + sovUserPermissionList(uint64(l))
		}
	}
	return n
}

func sovUserPermissionList(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozUserPermissionList(x uint64) (n int) {
	return sovUserPermissionList(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *DbPermissionList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserPermissionList
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DbPermissionList: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DbPermissionList: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field BatchNo", wireType)
			}
			m.BatchNo = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserPermissionList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BatchNo |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RowsOffset", wireType)
			}
			m.RowsOffset = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserPermissionList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RowsOffset |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rows", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserPermissionList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserPermissionList
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthUserPermissionList
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rows = append(m.Rows, &DbPermission{})
			if err := m.Rows[len(m.Rows)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUserPermissionList(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserPermissionList
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthUserPermissionList
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DbRoleList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserPermissionList
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DbRoleList: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DbRoleList: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field BatchNo", wireType)
			}
			m.BatchNo = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserPermissionList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BatchNo |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RowsOffset", wireType)
			}
			m.RowsOffset = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserPermissionList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RowsOffset |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rows", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserPermissionList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserPermissionList
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthUserPermissionList
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rows = append(m.Rows, &DbRole{})
			if err := m.Rows[len(m.Rows)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUserPermissionList(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserPermissionList
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthUserPermissionList
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DbRolePermissionList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserPermissionList
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DbRolePermissionList: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DbRolePermissionList: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field BatchNo", wireType)
			}
			m.BatchNo = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserPermissionList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BatchNo |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RowsOffset", wireType)
			}
			m.RowsOffset = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserPermissionList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RowsOffset |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rows", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserPermissionList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserPermissionList
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthUserPermissionList
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rows = append(m.Rows, &DbRolePermission{})
			if err := m.Rows[len(m.Rows)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUserPermissionList(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserPermissionList
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthUserPermissionList
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipUserPermissionList(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowUserPermissionList
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowUserPermissionList
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowUserPermissionList
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthUserPermissionList
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupUserPermissionList
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthUserPermissionList
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthUserPermissionList        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowUserPermissionList          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupUserPermissionList = fmt.Errorf("proto: unexpected end of group")
)

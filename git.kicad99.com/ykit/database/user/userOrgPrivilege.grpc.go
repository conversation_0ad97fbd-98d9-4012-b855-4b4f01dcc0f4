//Package user  generated by ygen-gocrud. DO NOT EDIT.
//source: userOrgPrivilege.proto
package user

import (
	"context"
	"strconv"

	"git.kicad99.com/ykit/goutil/crud"
	"git.kicad99.com/ykit/goutil/crudhook"
	"git.kicad99.com/ykit/goutil"
	"git.kicad99.com/ykit/goutil/pgxdb"
	"git.kicad99.com/ykit/goutil/rpc"
	"git.kicad99.com/ykit/goutil/yreflect"
	"google.golang.org/grpc/metadata"
)

// CrudRpcDbUserOrgPrivilegeServer impl crud Service
type CrudRpcDbUserOrgPrivilegeServer struct {
}

//Insert impl crud insert
func (*CrudRpcDbUserOrgPrivilegeServer) Insert(ctx context.Context, req *DbUserOrgPrivilege) (r *crud.DMLResult, err error) {
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	r, err = req.Insert(db)
	if err == nil {
		sys, userRid, sid := pgxdb.GetYkitInfoFromMetadata(ctx)
		ipinfo := goutil.GetIpinfoFromMeta(ctx)
		userOrgrid := goutil.GetUserOrgRIDFromMeta(ctx)
		goutil.NatsCdc(sys, req.OrgRID, sid, "DbUserOrgPrivilege.Insert", req, nil)
		crudhook.CrudAfterHook(sys, userRid, userOrgrid, "DbUserOrgPrivilege.Insert", req, nil, ipinfo)
	}
	return
}

//Update impl crud update
func (*CrudRpcDbUserOrgPrivilegeServer) Update(ctx context.Context, req *DbUserOrgPrivilege) (r *crud.DMLResult, err error) {
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	r, err = req.Update(db)
	if err == nil {
		sys, userRid, sid := pgxdb.GetYkitInfoFromMetadata(ctx)
		ipinfo := goutil.GetIpinfoFromMeta(ctx)
		userOrgrid := goutil.GetUserOrgRIDFromMeta(ctx)
		goutil.NatsCdc(sys, req.OrgRID, sid, "DbUserOrgPrivilege.Update", req, nil)
		crudhook.CrudAfterHook(sys, userRid, userOrgrid, "DbUserOrgPrivilege.Update", req, nil, ipinfo)
	}
	return
}

//PartialUpdate impl crud PartialUpdate
func (*CrudRpcDbUserOrgPrivilegeServer) PartialUpdate(ctx context.Context, req *DbUserOrgPrivilege) (r *crud.DMLResult, err error) {
	param, err := crud.GetDMLParamFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	r, err = req.PartialUpdate(param.KeyColumn, db)
	if err == nil {
		sys, userRid, sid := pgxdb.GetYkitInfoFromMetadata(ctx)
		ipinfo := goutil.GetIpinfoFromMeta(ctx)
		userOrgrid := goutil.GetUserOrgRIDFromMeta(ctx)
		goutil.NatsCdc(sys, req.OrgRID, sid, "DbUserOrgPrivilege.PartialUpdate", req, param)
		crudhook.CrudAfterHook(sys, userRid, userOrgrid, "DbUserOrgPrivilege.PartialUpdate", req, param, ipinfo)
	}
	return
}

//Delete impl crud Delete
func (*CrudRpcDbUserOrgPrivilegeServer) Delete(ctx context.Context, req *DbUserOrgPrivilege) (r *crud.DMLResult, err error) {
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	r, err = req.Delete(db)
	if err == nil {
		sys, userRid, sid := pgxdb.GetYkitInfoFromMetadata(ctx)
		ipinfo := goutil.GetIpinfoFromMeta(ctx)
		userOrgrid := goutil.GetUserOrgRIDFromMeta(ctx)
		goutil.NatsCdc(sys, req.OrgRID, sid, "DbUserOrgPrivilege.Delete", req, nil)
		crudhook.CrudAfterHook(sys, userRid, userOrgrid, "DbUserOrgPrivilege.Delete", req, nil, ipinfo)
	}
	return
}

//SelectOne impl crud SelectOne
func (*CrudRpcDbUserOrgPrivilegeServer) SelectOne(ctx context.Context, dmlParam *crud.DMLParam) (*DbUserOrgPrivilege, error) {
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	if len(dmlParam.KeyValue) != 1 {
		return nil, rpc.EBadProtoParam
	}
	msg := &DbUserOrgPrivilege{RID: dmlParam.KeyValue[0]}

	selectRow, err := msg.SelectOne(dmlParam.ResultColumn, db)
	if err != nil {
		return nil, err
	}
	if selectRow == 0 {
		msg.Reset()
	}
	return msg, nil
}

//SelectMany impl crud SelectMany
func (*CrudRpcDbUserOrgPrivilegeServer) SelectMany(dmlParam *crud.DMLParam, srv RpcDbUserOrgPrivilege_SelectManyServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)

	msg := &DbUserOrgPrivilege{}

	rows, err := msg.SelectByRids(dmlParam.ResultColumn, dmlParam.KeyValue, db)
	if err != nil {
		return err
	}
	defer rows.Close()
	rowsName := crud.RowsColNames2GoFieldNames(rows, msg)
	rowsCount := 0
	for rows.Next() {
		msg := &DbUserOrgPrivilege{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		err = srv.Send(msg)
		if err != nil {
			return err
		}
		rowsCount++
	}
	trailer := metadata.Pairs("rows", strconv.Itoa(rowsCount))
	srv.SetTrailer(trailer)
	return nil
}

//Query impl crud Query
func (*CrudRpcDbUserOrgPrivilegeServer) Query(req *crud.QueryParam, srv RpcDbUserOrgPrivilege_QueryServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)

	rows, err := crud.DbSelectQuery("DbUserOrgPrivilege", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbUserOrgPrivilege{})
	rowsCount := 0
	for rows.Next() {
		msg := &DbUserOrgPrivilege{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		err = srv.Send(msg)
		if err != nil {
			return err
		}
		rowsCount++
	}
	trailer := metadata.Pairs("rows", strconv.Itoa(rowsCount))
	srv.SetTrailer(trailer)
	return nil
}
//Query impl crud QueryBatch
func (*CrudRpcDbUserOrgPrivilegeServer) QueryBatch(req *crud.QueryParam, srv RpcDbUserOrgPrivilege_QueryBatchServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)

	rows, err := crud.DbSelectQuery("DbUserOrgPrivilege", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	if req.Batch < 1 {
		req.Batch = 1
	}
	if req.Batch > 10000 {
		req.Batch = 10000
	}

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbUserOrgPrivilege{})
	var batchNo uint32 = 0
	var rowsOffset uint32 = 0
	var batchCount uint32 = 0
	list := &DbUserOrgPrivilegeList{}
	for rows.Next() {
		msg := &DbUserOrgPrivilege{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		list.Rows = append(list.Rows, msg)
		batchCount++
		rowsOffset++

		if batchCount >= req.Batch {
			err = srv.Send(list)
			if err != nil {
				return err
			}
			batchNo++
			list = &DbUserOrgPrivilegeList{
				BatchNo:    batchNo,
				RowsOffset: rowsOffset}
			batchCount = 0
		}
	}
	if batchCount > 0 {
		err = srv.Send(list)
		if err != nil {
			return err
		}
	}
	trailer := metadata.Pairs("rows", strconv.FormatUint(uint64(rowsOffset), 10))
	srv.SetTrailer(trailer)
	return nil
}

// PcrudRpcDbUserOrgPrivilegeServer impl pcrud Service
type PcrudRpcDbUserOrgPrivilegeServer struct {
}

//Insert impl pcrud Insert
func (*PcrudRpcDbUserOrgPrivilegeServer) Insert(ctx context.Context, req *DbUserOrgPrivilege) (*crud.DMLResult, error) {
	var crudServer *CrudRpcDbUserOrgPrivilegeServer
	_, err := crud.GrpcCheckHasPermission(ctx, "db", "DbUserOrgPrivilege.Insert")
	if err != nil {
		return nil, err
	}
	defer crud.ResetUserOrgCache()
	err = crud.GrpcCheckHasOrgridPrivilege(ctx, req.OrgRID)
	if err != nil {
		return nil, err
	}
	return crudServer.Insert(ctx, req)
}

//Update impl pcrud Update
func (*PcrudRpcDbUserOrgPrivilegeServer) Update(ctx context.Context, req *DbUserOrgPrivilege) (*crud.DMLResult, error) {
	var crudServer *CrudRpcDbUserOrgPrivilegeServer
	_, err := crud.GrpcCheckHasPermission(ctx, "db", "DbUserOrgPrivilege.Update")
	if err != nil {
		return nil, err
	}
	defer crud.ResetUserOrgCache()
	err = crud.GrpcCheckHasOrgridPrivilege(ctx, req.OrgRID)
	if err != nil {
		return nil, err
	}
	return crudServer.Update(ctx, req)
}

//PartialUpdate impl pcrud PartialUpdate
func (*PcrudRpcDbUserOrgPrivilegeServer) PartialUpdate(ctx context.Context, req *DbUserOrgPrivilege) (*crud.DMLResult, error) {
	var crudServer *CrudRpcDbUserOrgPrivilegeServer
	_, err := crud.GrpcCheckHasPermission(ctx, "db", "DbUserOrgPrivilege.Update")
	if err != nil {
		return nil, err
	}
	defer crud.ResetUserOrgCache()
	err = crud.GrpcCheckHasOrgridPrivilege(ctx, req.OrgRID)
	if err != nil {
		return nil, err
	}
	return crudServer.PartialUpdate(ctx, req)
}

//Delete impl pcrud Delete
func (*PcrudRpcDbUserOrgPrivilegeServer) Delete(ctx context.Context, req *DbUserOrgPrivilege) (*crud.DMLResult, error) {
	var crudServer *CrudRpcDbUserOrgPrivilegeServer
	_, err := crud.GrpcCheckHasPermission(ctx, "db", "DbUserOrgPrivilege.Delete")
	if err != nil {
		return nil, err
	}
	defer crud.ResetUserOrgCache()
	err = crud.GrpcCheckHasOrgridPrivilege(ctx, req.OrgRID)
	if err != nil {
		return nil, err
	}
	return crudServer.Delete(ctx, req)
}

//SelectOne impl pcrud SelectOne
func (*PcrudRpcDbUserOrgPrivilegeServer) SelectOne(ctx context.Context, dmlParam *crud.DMLParam) (*DbUserOrgPrivilege, error) {
	var crudServer *CrudRpcDbUserOrgPrivilegeServer
	return crudServer.SelectOne(ctx, dmlParam)
}

//SelectMany impl pcrud SelectMany
func (*PcrudRpcDbUserOrgPrivilegeServer) SelectMany(dmlParam *crud.DMLParam, srv PrpcDbUserOrgPrivilege_SelectManyServer) error {
	var crudServer *CrudRpcDbUserOrgPrivilegeServer
	return crudServer.SelectMany(dmlParam, srv)
}

//Query impl pcrud Query
func (*PcrudRpcDbUserOrgPrivilegeServer) Query(req *crud.PrivilegeParam, srv PrpcDbUserOrgPrivilege_QueryServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)
	rows, err := crud.DbSelectPrivilege("DbUserOrgPrivilege", "OrgRID", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbUserOrgPrivilege{})
	rowsCount := 0
	for rows.Next() {
		msg := &DbUserOrgPrivilege{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		err = srv.Send(msg)
		if err != nil {
			return err
		}
		rowsCount++
	}
	trailer := metadata.Pairs("rows", strconv.Itoa(rowsCount))
	srv.SetTrailer(trailer)
	return nil
}
//Query impl pcrud QueryBatch
func (*PcrudRpcDbUserOrgPrivilegeServer) QueryBatch(req *crud.PrivilegeParam, srv PrpcDbUserOrgPrivilege_QueryBatchServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)
	rows, err := crud.DbSelectPrivilege("DbUserOrgPrivilege", "OrgRID", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	if req.QueryCondition.Batch < 1 {
		req.QueryCondition.Batch = 1
	}
	if req.QueryCondition.Batch > 10000 {
		req.QueryCondition.Batch = 10000
	}

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbUserOrgPrivilege{})

	var batchNo uint32 = 0
	var rowsOffset uint32 = 0
	var batchCount uint32 = 0
	list := &DbUserOrgPrivilegeList{}
	for rows.Next() {
		msg := &DbUserOrgPrivilege{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		list.Rows = append(list.Rows, msg)
		batchCount++
		rowsOffset++

		if batchCount >= req.QueryCondition.Batch {
			err = srv.Send(list)
			if err != nil {
				return err
			}
			batchNo++
			list = &DbUserOrgPrivilegeList{
				BatchNo:    batchNo,
				RowsOffset: rowsOffset}
			batchCount = 0
		}
	}
	if batchCount > 0 {
		err = srv.Send(list)
		if err != nil {
			return err
		}
	}
	trailer := metadata.Pairs("rows", strconv.FormatUint(uint64(rowsOffset), 10))
	srv.SetTrailer(trailer)
	return nil
}

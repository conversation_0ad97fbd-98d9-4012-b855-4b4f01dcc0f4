// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: userPermission.proto

package user

import (
	fmt "fmt"
	proto "github.com/gogo/protobuf/proto"
	io "io"
	math "math"
	math_bits "math/bits"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

//所有的权限信息表,此表信息为预置，一般不给删除
//@rpc crud
//@dbpost CREATE INDEX IF NOT EXISTS idxDbPermissionPermissionValue on DbPermission USING hash(PermissionValue);
type DbPermission struct {
	//@db uuid primary key
	//行ID,permission rid
	RID string `protobuf:"bytes,1,opt,name=RID,proto3" json:"RID,omitempty" ykit:"prk"`
	//@db text not null
	//权限类别
	PermissionType string `protobuf:"bytes,3,opt,name=PermissionType,proto3" json:"PermissionType,omitempty" ykit:"notnull"`
	//@db text not null
	//permission 名称
	PermissionName string `protobuf:"bytes,4,opt,name=PermissionName,proto3" json:"PermissionName,omitempty" ykit:"notnull"`
	//@db text not null
	//permission value
	PermissionValue string `protobuf:"bytes,10,opt,name=PermissionValue,proto3" json:"PermissionValue,omitempty" ykit:"notnull"`
	//@db timestamp not null default now_utc()
	//数据最后修改时间
	UpdatedAt string `protobuf:"bytes,14,opt,name=UpdatedAt,proto3" json:"UpdatedAt,omitempty" ykit:"notnull"`
	//@db text
	//数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
	UpdatedDC string `protobuf:"bytes,15,opt,name=UpdatedDC,proto3" json:"UpdatedDC,omitempty" ykit:"null"`
}

func (m *DbPermission) Reset()         { *m = DbPermission{} }
func (m *DbPermission) String() string { return proto.CompactTextString(m) }
func (*DbPermission) ProtoMessage()    {}
func (*DbPermission) Descriptor() ([]byte, []int) {
	return fileDescriptor_9c23d31d7c5a4f51, []int{0}
}
func (m *DbPermission) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbPermission) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbPermission.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbPermission) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbPermission.Merge(m, src)
}
func (m *DbPermission) XXX_Size() int {
	return m.Size()
}
func (m *DbPermission) XXX_DiscardUnknown() {
	xxx_messageInfo_DbPermission.DiscardUnknown(m)
}

var xxx_messageInfo_DbPermission proto.InternalMessageInfo

func (m *DbPermission) GetRID() string {
	if m != nil {
		return m.RID
	}
	return ""
}

func (m *DbPermission) GetPermissionType() string {
	if m != nil {
		return m.PermissionType
	}
	return ""
}

func (m *DbPermission) GetPermissionName() string {
	if m != nil {
		return m.PermissionName
	}
	return ""
}

func (m *DbPermission) GetPermissionValue() string {
	if m != nil {
		return m.PermissionValue
	}
	return ""
}

func (m *DbPermission) GetUpdatedAt() string {
	if m != nil {
		return m.UpdatedAt
	}
	return ""
}

func (m *DbPermission) GetUpdatedDC() string {
	if m != nil {
		return m.UpdatedDC
	}
	return ""
}

//所有的角色信息表
//@rpc crud pcrud
//@dbpost insert into DbRole (rid, orgrid, rolename, creator, isbuiltin, setting, sortvalue, updatedat, updateddc) VALUES('*************-5555-5555-************', '00000000-0000-0000-0000-000000000000', 'base', null, 1, '{}'::jsonb, 0, now_utc(), '') ON CONFLICT  DO NOTHING;
//@dbpost insert into DbRole (rid, orgrid, rolename, creator, isbuiltin, setting, sortvalue, updatedat, updateddc) VALUES('00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', 'admin', null, 1, '{}'::jsonb, 0, now_utc(), '') ON CONFLICT  DO NOTHING;
type DbRole struct {
	//@db uuid primary key
	//行ID,role rid
	RID string `protobuf:"bytes,1,opt,name=RID,proto3" json:"RID,omitempty" ykit:"prk"`
	//@db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
	//用户所属的群组
	OrgRID string `protobuf:"bytes,2,opt,name=OrgRID,proto3" json:"OrgRID,omitempty" ykit:"notnull"`
	//@db text not null
	//role 名称
	RoleName string `protobuf:"bytes,4,opt,name=RoleName,proto3" json:"RoleName,omitempty" ykit:"notnull"`
	//@db uuid --REFERENCES DbUser(RID) ON DELETE set null
	//role 创建者 rid
	Creator string `protobuf:"bytes,5,opt,name=Creator,proto3" json:"Creator,omitempty" ykit:"null"`
	//@db int
	//是否是内置的角色，内置角色不能删除
	IsBuiltIn int32 `protobuf:"varint,6,opt,name=IsBuiltIn,proto3" json:"IsBuiltIn,omitempty" ykit:"null"`
	//@db jsonb not null default  '{}'::jsonb
	// 其它设置，可以在里面扩展需要用到的其它信息
	Setting string `protobuf:"bytes,9,opt,name=Setting,proto3" json:"Setting,omitempty" ykit:"notnull"`
	//@db int default 100
	//sort value
	SortValue int32 `protobuf:"varint,10,opt,name=SortValue,proto3" json:"SortValue,omitempty"`
	//@db timestamp not null default now_utc()
	//数据最后修改时间
	UpdatedAt string `protobuf:"bytes,14,opt,name=UpdatedAt,proto3" json:"UpdatedAt,omitempty" ykit:"notnull"`
	//@db text
	//数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
	UpdatedDC string `protobuf:"bytes,15,opt,name=UpdatedDC,proto3" json:"UpdatedDC,omitempty" ykit:"null"`
}

func (m *DbRole) Reset()         { *m = DbRole{} }
func (m *DbRole) String() string { return proto.CompactTextString(m) }
func (*DbRole) ProtoMessage()    {}
func (*DbRole) Descriptor() ([]byte, []int) {
	return fileDescriptor_9c23d31d7c5a4f51, []int{1}
}
func (m *DbRole) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbRole) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbRole.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbRole) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbRole.Merge(m, src)
}
func (m *DbRole) XXX_Size() int {
	return m.Size()
}
func (m *DbRole) XXX_DiscardUnknown() {
	xxx_messageInfo_DbRole.DiscardUnknown(m)
}

var xxx_messageInfo_DbRole proto.InternalMessageInfo

func (m *DbRole) GetRID() string {
	if m != nil {
		return m.RID
	}
	return ""
}

func (m *DbRole) GetOrgRID() string {
	if m != nil {
		return m.OrgRID
	}
	return ""
}

func (m *DbRole) GetRoleName() string {
	if m != nil {
		return m.RoleName
	}
	return ""
}

func (m *DbRole) GetCreator() string {
	if m != nil {
		return m.Creator
	}
	return ""
}

func (m *DbRole) GetIsBuiltIn() int32 {
	if m != nil {
		return m.IsBuiltIn
	}
	return 0
}

func (m *DbRole) GetSetting() string {
	if m != nil {
		return m.Setting
	}
	return ""
}

func (m *DbRole) GetSortValue() int32 {
	if m != nil {
		return m.SortValue
	}
	return 0
}

func (m *DbRole) GetUpdatedAt() string {
	if m != nil {
		return m.UpdatedAt
	}
	return ""
}

func (m *DbRole) GetUpdatedDC() string {
	if m != nil {
		return m.UpdatedDC
	}
	return ""
}

//角色权限信息表
//@rpc crud pcrud
type DbRolePermission struct {
	//@db uuid primary key
	//行ID
	RID string `protobuf:"bytes,1,opt,name=RID,proto3" json:"RID,omitempty" ykit:"prk"`
	//@db uuid not null REFERENCES DbRole(RID) ON DELETE CASCADE
	//role rid
	RoleRID string `protobuf:"bytes,2,opt,name=RoleRID,proto3" json:"RoleRID,omitempty" ykit:"notnull"`
	//@db uuid not null REFERENCES DbPermission(RID) ON DELETE CASCADE
	//permission rid
	PermissionRID string `protobuf:"bytes,4,opt,name=PermissionRID,proto3" json:"PermissionRID,omitempty" ykit:"notnull"`
	//@db uuid --REFERENCES DbUser(RID) ON DELETE set null
	//role 创建者 rid
	Creator string `protobuf:"bytes,5,opt,name=Creator,proto3" json:"Creator,omitempty" ykit:"null"`
	//@db timestamp not null default now_utc()
	//数据最后修改时间
	UpdatedAt string `protobuf:"bytes,14,opt,name=UpdatedAt,proto3" json:"UpdatedAt,omitempty" ykit:"notnull"`
	//@db text
	//数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
	UpdatedDC string `protobuf:"bytes,15,opt,name=UpdatedDC,proto3" json:"UpdatedDC,omitempty" ykit:"null"`
}

func (m *DbRolePermission) Reset()         { *m = DbRolePermission{} }
func (m *DbRolePermission) String() string { return proto.CompactTextString(m) }
func (*DbRolePermission) ProtoMessage()    {}
func (*DbRolePermission) Descriptor() ([]byte, []int) {
	return fileDescriptor_9c23d31d7c5a4f51, []int{2}
}
func (m *DbRolePermission) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbRolePermission) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbRolePermission.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbRolePermission) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbRolePermission.Merge(m, src)
}
func (m *DbRolePermission) XXX_Size() int {
	return m.Size()
}
func (m *DbRolePermission) XXX_DiscardUnknown() {
	xxx_messageInfo_DbRolePermission.DiscardUnknown(m)
}

var xxx_messageInfo_DbRolePermission proto.InternalMessageInfo

func (m *DbRolePermission) GetRID() string {
	if m != nil {
		return m.RID
	}
	return ""
}

func (m *DbRolePermission) GetRoleRID() string {
	if m != nil {
		return m.RoleRID
	}
	return ""
}

func (m *DbRolePermission) GetPermissionRID() string {
	if m != nil {
		return m.PermissionRID
	}
	return ""
}

func (m *DbRolePermission) GetCreator() string {
	if m != nil {
		return m.Creator
	}
	return ""
}

func (m *DbRolePermission) GetUpdatedAt() string {
	if m != nil {
		return m.UpdatedAt
	}
	return ""
}

func (m *DbRolePermission) GetUpdatedDC() string {
	if m != nil {
		return m.UpdatedDC
	}
	return ""
}

func init() {
	proto.RegisterType((*DbPermission)(nil), "user.DbPermission")
	proto.RegisterType((*DbRole)(nil), "user.DbRole")
	proto.RegisterType((*DbRolePermission)(nil), "user.DbRolePermission")
}

func init() { proto.RegisterFile("userPermission.proto", fileDescriptor_9c23d31d7c5a4f51) }

var fileDescriptor_9c23d31d7c5a4f51 = []byte{
	// 350 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xa4, 0x92, 0x3f, 0x4f, 0x02, 0x31,
	0x18, 0xc6, 0xa9, 0xc0, 0x21, 0x6f, 0x14, 0x48, 0x63, 0x4c, 0x63, 0x4c, 0x43, 0x88, 0x31, 0x4c,
	0x30, 0x38, 0x91, 0xb8, 0x08, 0xb7, 0xb0, 0xa8, 0x39, 0xd4, 0xc1, 0xad, 0xc7, 0x35, 0xa4, 0xe1,
	0x4f, 0x49, 0xaf, 0x0c, 0x7c, 0x0b, 0x3f, 0x8f, 0x9f, 0xc0, 0x91, 0xc9, 0x38, 0x1a, 0xf8, 0x16,
	0x4e, 0xa6, 0x05, 0x2c, 0x77, 0x31, 0x0c, 0xba, 0xdd, 0xf3, 0x7b, 0x9e, 0x27, 0x79, 0xdf, 0xf7,
	0x0a, 0x27, 0xb3, 0x98, 0xab, 0x7b, 0xae, 0xc6, 0x22, 0x8e, 0x85, 0x9c, 0x34, 0xa6, 0x4a, 0x6a,
	0x89, 0x73, 0x86, 0xd6, 0xde, 0x11, 0x1c, 0xf9, 0xa1, 0x33, 0x71, 0x05, 0xb2, 0x41, 0xd7, 0x27,
	0xa8, 0x8a, 0xea, 0xc5, 0xc0, 0x7c, 0xe2, 0x4b, 0x28, 0x39, 0xff, 0x61, 0x3e, 0xe5, 0x24, 0x6b,
	0xcd, 0x14, 0x4d, 0xe6, 0x6e, 0xd9, 0x98, 0x93, 0x5c, 0x3a, 0x67, 0x28, 0xae, 0x43, 0xd9, 0x91,
	0x27, 0x36, 0x9a, 0x71, 0x02, 0x36, 0x98, 0xc6, 0xf8, 0x1c, 0x8a, 0x8f, 0xd3, 0x88, 0x69, 0x1e,
	0xdd, 0x68, 0x52, 0xb2, 0x19, 0x07, 0x76, 0x5c, 0xbf, 0x43, 0xca, 0x09, 0xd7, 0xef, 0xd4, 0xbe,
	0x10, 0x78, 0x7e, 0x18, 0xc8, 0x11, 0xff, 0x65, 0xa5, 0x53, 0xf0, 0xee, 0xd4, 0xc0, 0xc0, 0x03,
	0x0b, 0x37, 0x0a, 0x9f, 0xc1, 0xa1, 0x69, 0xec, 0x0c, 0xff, 0xa3, 0x31, 0x81, 0x42, 0x47, 0x71,
	0xa6, 0xa5, 0x22, 0x79, 0x6b, 0x6d, 0xa5, 0x19, 0xa4, 0x1b, 0xb7, 0x67, 0x62, 0xa4, 0xbb, 0x13,
	0xe2, 0x55, 0x51, 0x3d, 0x1f, 0x38, 0x60, 0x7a, 0x3d, 0xae, 0xb5, 0x98, 0x0c, 0x48, 0x71, 0xdd,
	0xdb, 0x48, 0xd3, 0xeb, 0x49, 0xa5, 0xdd, 0x09, 0xf2, 0x81, 0x03, 0xff, 0x5a, 0xfe, 0x15, 0x41,
	0x65, 0xbd, 0xfc, 0xde, 0x3f, 0x4b, 0xa0, 0x60, 0x32, 0xee, 0x0e, 0x5b, 0x89, 0x2f, 0xe0, 0xd8,
	0x35, 0x8d, 0xbf, 0xbe, 0x46, 0x12, 0xee, 0x3f, 0xc9, 0x5f, 0x87, 0x6f, 0x5f, 0xbf, 0x2d, 0x29,
	0x5a, 0x2c, 0x29, 0xfa, 0x5c, 0x52, 0xf4, 0xb2, 0xa2, 0x99, 0xc5, 0x8a, 0x66, 0x3e, 0x56, 0x34,
	0xf3, 0x5c, 0x1b, 0x08, 0xdd, 0x18, 0x8a, 0x3e, 0x8b, 0x5a, 0xad, 0x46, 0x5f, 0x8e, 0x9b, 0xf3,
	0xa1, 0xd0, 0xcd, 0x88, 0x69, 0x16, 0xb2, 0x98, 0x37, 0xcd, 0x83, 0x0e, 0x3d, 0xfb, 0xba, 0xaf,
	0xbe, 0x03, 0x00, 0x00, 0xff, 0xff, 0xfa, 0xe2, 0x98, 0xeb, 0xf5, 0x02, 0x00, 0x00,
}

func (m *DbPermission) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbPermission) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbPermission) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.UpdatedDC) > 0 {
		i -= len(m.UpdatedDC)
		copy(dAtA[i:], m.UpdatedDC)
		i = encodeVarintUserPermission(dAtA, i, uint64(len(m.UpdatedDC)))
		i--
		dAtA[i] = 0x7a
	}
	if len(m.UpdatedAt) > 0 {
		i -= len(m.UpdatedAt)
		copy(dAtA[i:], m.UpdatedAt)
		i = encodeVarintUserPermission(dAtA, i, uint64(len(m.UpdatedAt)))
		i--
		dAtA[i] = 0x72
	}
	if len(m.PermissionValue) > 0 {
		i -= len(m.PermissionValue)
		copy(dAtA[i:], m.PermissionValue)
		i = encodeVarintUserPermission(dAtA, i, uint64(len(m.PermissionValue)))
		i--
		dAtA[i] = 0x52
	}
	if len(m.PermissionName) > 0 {
		i -= len(m.PermissionName)
		copy(dAtA[i:], m.PermissionName)
		i = encodeVarintUserPermission(dAtA, i, uint64(len(m.PermissionName)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.PermissionType) > 0 {
		i -= len(m.PermissionType)
		copy(dAtA[i:], m.PermissionType)
		i = encodeVarintUserPermission(dAtA, i, uint64(len(m.PermissionType)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.RID) > 0 {
		i -= len(m.RID)
		copy(dAtA[i:], m.RID)
		i = encodeVarintUserPermission(dAtA, i, uint64(len(m.RID)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *DbRole) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbRole) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbRole) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.UpdatedDC) > 0 {
		i -= len(m.UpdatedDC)
		copy(dAtA[i:], m.UpdatedDC)
		i = encodeVarintUserPermission(dAtA, i, uint64(len(m.UpdatedDC)))
		i--
		dAtA[i] = 0x7a
	}
	if len(m.UpdatedAt) > 0 {
		i -= len(m.UpdatedAt)
		copy(dAtA[i:], m.UpdatedAt)
		i = encodeVarintUserPermission(dAtA, i, uint64(len(m.UpdatedAt)))
		i--
		dAtA[i] = 0x72
	}
	if m.SortValue != 0 {
		i = encodeVarintUserPermission(dAtA, i, uint64(m.SortValue))
		i--
		dAtA[i] = 0x50
	}
	if len(m.Setting) > 0 {
		i -= len(m.Setting)
		copy(dAtA[i:], m.Setting)
		i = encodeVarintUserPermission(dAtA, i, uint64(len(m.Setting)))
		i--
		dAtA[i] = 0x4a
	}
	if m.IsBuiltIn != 0 {
		i = encodeVarintUserPermission(dAtA, i, uint64(m.IsBuiltIn))
		i--
		dAtA[i] = 0x30
	}
	if len(m.Creator) > 0 {
		i -= len(m.Creator)
		copy(dAtA[i:], m.Creator)
		i = encodeVarintUserPermission(dAtA, i, uint64(len(m.Creator)))
		i--
		dAtA[i] = 0x2a
	}
	if len(m.RoleName) > 0 {
		i -= len(m.RoleName)
		copy(dAtA[i:], m.RoleName)
		i = encodeVarintUserPermission(dAtA, i, uint64(len(m.RoleName)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.OrgRID) > 0 {
		i -= len(m.OrgRID)
		copy(dAtA[i:], m.OrgRID)
		i = encodeVarintUserPermission(dAtA, i, uint64(len(m.OrgRID)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.RID) > 0 {
		i -= len(m.RID)
		copy(dAtA[i:], m.RID)
		i = encodeVarintUserPermission(dAtA, i, uint64(len(m.RID)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *DbRolePermission) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbRolePermission) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbRolePermission) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.UpdatedDC) > 0 {
		i -= len(m.UpdatedDC)
		copy(dAtA[i:], m.UpdatedDC)
		i = encodeVarintUserPermission(dAtA, i, uint64(len(m.UpdatedDC)))
		i--
		dAtA[i] = 0x7a
	}
	if len(m.UpdatedAt) > 0 {
		i -= len(m.UpdatedAt)
		copy(dAtA[i:], m.UpdatedAt)
		i = encodeVarintUserPermission(dAtA, i, uint64(len(m.UpdatedAt)))
		i--
		dAtA[i] = 0x72
	}
	if len(m.Creator) > 0 {
		i -= len(m.Creator)
		copy(dAtA[i:], m.Creator)
		i = encodeVarintUserPermission(dAtA, i, uint64(len(m.Creator)))
		i--
		dAtA[i] = 0x2a
	}
	if len(m.PermissionRID) > 0 {
		i -= len(m.PermissionRID)
		copy(dAtA[i:], m.PermissionRID)
		i = encodeVarintUserPermission(dAtA, i, uint64(len(m.PermissionRID)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.RoleRID) > 0 {
		i -= len(m.RoleRID)
		copy(dAtA[i:], m.RoleRID)
		i = encodeVarintUserPermission(dAtA, i, uint64(len(m.RoleRID)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.RID) > 0 {
		i -= len(m.RID)
		copy(dAtA[i:], m.RID)
		i = encodeVarintUserPermission(dAtA, i, uint64(len(m.RID)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func encodeVarintUserPermission(dAtA []byte, offset int, v uint64) int {
	offset -= sovUserPermission(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *DbPermission) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.RID)
	if l > 0 {
		n += 1 + l + sovUserPermission(uint64(l))
	}
	l = len(m.PermissionType)
	if l > 0 {
		n += 1 + l + sovUserPermission(uint64(l))
	}
	l = len(m.PermissionName)
	if l > 0 {
		n += 1 + l + sovUserPermission(uint64(l))
	}
	l = len(m.PermissionValue)
	if l > 0 {
		n += 1 + l + sovUserPermission(uint64(l))
	}
	l = len(m.UpdatedAt)
	if l > 0 {
		n += 1 + l + sovUserPermission(uint64(l))
	}
	l = len(m.UpdatedDC)
	if l > 0 {
		n += 1 + l + sovUserPermission(uint64(l))
	}
	return n
}

func (m *DbRole) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.RID)
	if l > 0 {
		n += 1 + l + sovUserPermission(uint64(l))
	}
	l = len(m.OrgRID)
	if l > 0 {
		n += 1 + l + sovUserPermission(uint64(l))
	}
	l = len(m.RoleName)
	if l > 0 {
		n += 1 + l + sovUserPermission(uint64(l))
	}
	l = len(m.Creator)
	if l > 0 {
		n += 1 + l + sovUserPermission(uint64(l))
	}
	if m.IsBuiltIn != 0 {
		n += 1 + sovUserPermission(uint64(m.IsBuiltIn))
	}
	l = len(m.Setting)
	if l > 0 {
		n += 1 + l + sovUserPermission(uint64(l))
	}
	if m.SortValue != 0 {
		n += 1 + sovUserPermission(uint64(m.SortValue))
	}
	l = len(m.UpdatedAt)
	if l > 0 {
		n += 1 + l + sovUserPermission(uint64(l))
	}
	l = len(m.UpdatedDC)
	if l > 0 {
		n += 1 + l + sovUserPermission(uint64(l))
	}
	return n
}

func (m *DbRolePermission) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.RID)
	if l > 0 {
		n += 1 + l + sovUserPermission(uint64(l))
	}
	l = len(m.RoleRID)
	if l > 0 {
		n += 1 + l + sovUserPermission(uint64(l))
	}
	l = len(m.PermissionRID)
	if l > 0 {
		n += 1 + l + sovUserPermission(uint64(l))
	}
	l = len(m.Creator)
	if l > 0 {
		n += 1 + l + sovUserPermission(uint64(l))
	}
	l = len(m.UpdatedAt)
	if l > 0 {
		n += 1 + l + sovUserPermission(uint64(l))
	}
	l = len(m.UpdatedDC)
	if l > 0 {
		n += 1 + l + sovUserPermission(uint64(l))
	}
	return n
}

func sovUserPermission(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozUserPermission(x uint64) (n int) {
	return sovUserPermission(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *DbPermission) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserPermission
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DbPermission: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DbPermission: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserPermission
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserPermission
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthUserPermission
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.RID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PermissionType", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserPermission
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserPermission
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthUserPermission
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.PermissionType = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PermissionName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserPermission
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserPermission
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthUserPermission
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.PermissionName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PermissionValue", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserPermission
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserPermission
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthUserPermission
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.PermissionValue = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 14:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UpdatedAt", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserPermission
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserPermission
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthUserPermission
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UpdatedAt = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 15:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UpdatedDC", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserPermission
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserPermission
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthUserPermission
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UpdatedDC = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUserPermission(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserPermission
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthUserPermission
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DbRole) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserPermission
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DbRole: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DbRole: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserPermission
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserPermission
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthUserPermission
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.RID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OrgRID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserPermission
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserPermission
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthUserPermission
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OrgRID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RoleName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserPermission
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserPermission
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthUserPermission
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.RoleName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Creator", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserPermission
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserPermission
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthUserPermission
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Creator = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field IsBuiltIn", wireType)
			}
			m.IsBuiltIn = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserPermission
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.IsBuiltIn |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Setting", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserPermission
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserPermission
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthUserPermission
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Setting = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 10:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SortValue", wireType)
			}
			m.SortValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserPermission
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SortValue |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 14:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UpdatedAt", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserPermission
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserPermission
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthUserPermission
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UpdatedAt = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 15:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UpdatedDC", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserPermission
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserPermission
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthUserPermission
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UpdatedDC = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUserPermission(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserPermission
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthUserPermission
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DbRolePermission) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserPermission
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DbRolePermission: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DbRolePermission: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserPermission
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserPermission
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthUserPermission
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.RID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RoleRID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserPermission
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserPermission
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthUserPermission
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.RoleRID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PermissionRID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserPermission
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserPermission
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthUserPermission
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.PermissionRID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Creator", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserPermission
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserPermission
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthUserPermission
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Creator = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 14:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UpdatedAt", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserPermission
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserPermission
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthUserPermission
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UpdatedAt = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 15:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UpdatedDC", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserPermission
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserPermission
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthUserPermission
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UpdatedDC = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUserPermission(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserPermission
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthUserPermission
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipUserPermission(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowUserPermission
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowUserPermission
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowUserPermission
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthUserPermission
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupUserPermission
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthUserPermission
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthUserPermission        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowUserPermission          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupUserPermission = fmt.Errorf("proto: unexpected end of group")
)

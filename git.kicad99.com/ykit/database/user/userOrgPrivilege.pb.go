// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: userOrgPrivilege.proto

package user

import (
	fmt "fmt"
	proto "github.com/gogo/protobuf/proto"
	io "io"
	math "math"
	math_bits "math/bits"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

//用户群组权限表
//@rpc crud pcrud
//@dbpost CREATE INDEX IF NOT EXISTS idxDbUserOrgPrivilegeUserRID on DbUserOrgPrivilege USING hash(UserRID);
//@dbpost INSERT INTO dbuserorgprivilege(rid, userrid, orgrid, includechildren, setting, updatedat, updateddc)VALUES('00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', 1, '{}'::jsonb, now_utc(), '') ON CONFLICT  DO NOTHING;
type DbUserOrgPrivilege struct {
	//@db uuid primary key
	//行ID
	RID string `protobuf:"bytes,1,opt,name=RID,proto3" json:"RID,omitempty" ykit:"prk"`
	//@db uuid not null REFERENCES DbUser(RID) ON DELETE CASCADE
	//用户rid
	UserRID string `protobuf:"bytes,3,opt,name=UserRID,proto3" json:"UserRID,omitempty" ykit:"notnull"`
	//@db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
	//有权限的群组
	OrgRID string `protobuf:"bytes,4,opt,name=OrgRID,proto3" json:"OrgRID,omitempty" ykit:"notnull"`
	//@db int default 0
	//是否包含下级群组
	IncludeChildren int32 `protobuf:"varint,5,opt,name=IncludeChildren,proto3" json:"IncludeChildren,omitempty"`
	//@db jsonb not null default  '{}'::jsonb
	// 其它设置，可以在里面扩展需要用到的其它信息
	Setting string `protobuf:"bytes,8,opt,name=Setting,proto3" json:"Setting,omitempty" ykit:"notnull"`
	//@db timestamp not null default now_utc()
	//数据最后修改时间
	UpdatedAt string `protobuf:"bytes,14,opt,name=UpdatedAt,proto3" json:"UpdatedAt,omitempty" ykit:"notnull"`
	//@db text
	//数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
	UpdatedDC string `protobuf:"bytes,15,opt,name=UpdatedDC,proto3" json:"UpdatedDC,omitempty" ykit:"null"`
}

func (m *DbUserOrgPrivilege) Reset()         { *m = DbUserOrgPrivilege{} }
func (m *DbUserOrgPrivilege) String() string { return proto.CompactTextString(m) }
func (*DbUserOrgPrivilege) ProtoMessage()    {}
func (*DbUserOrgPrivilege) Descriptor() ([]byte, []int) {
	return fileDescriptor_ddd2e5c5e833325b, []int{0}
}
func (m *DbUserOrgPrivilege) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbUserOrgPrivilege) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbUserOrgPrivilege.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbUserOrgPrivilege) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbUserOrgPrivilege.Merge(m, src)
}
func (m *DbUserOrgPrivilege) XXX_Size() int {
	return m.Size()
}
func (m *DbUserOrgPrivilege) XXX_DiscardUnknown() {
	xxx_messageInfo_DbUserOrgPrivilege.DiscardUnknown(m)
}

var xxx_messageInfo_DbUserOrgPrivilege proto.InternalMessageInfo

func (m *DbUserOrgPrivilege) GetRID() string {
	if m != nil {
		return m.RID
	}
	return ""
}

func (m *DbUserOrgPrivilege) GetUserRID() string {
	if m != nil {
		return m.UserRID
	}
	return ""
}

func (m *DbUserOrgPrivilege) GetOrgRID() string {
	if m != nil {
		return m.OrgRID
	}
	return ""
}

func (m *DbUserOrgPrivilege) GetIncludeChildren() int32 {
	if m != nil {
		return m.IncludeChildren
	}
	return 0
}

func (m *DbUserOrgPrivilege) GetSetting() string {
	if m != nil {
		return m.Setting
	}
	return ""
}

func (m *DbUserOrgPrivilege) GetUpdatedAt() string {
	if m != nil {
		return m.UpdatedAt
	}
	return ""
}

func (m *DbUserOrgPrivilege) GetUpdatedDC() string {
	if m != nil {
		return m.UpdatedDC
	}
	return ""
}

func init() {
	proto.RegisterType((*DbUserOrgPrivilege)(nil), "user.DbUserOrgPrivilege")
}

func init() { proto.RegisterFile("userOrgPrivilege.proto", fileDescriptor_ddd2e5c5e833325b) }

var fileDescriptor_ddd2e5c5e833325b = []byte{
	// 243 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe2, 0x12, 0x2b, 0x2d, 0x4e, 0x2d,
	0xf2, 0x2f, 0x4a, 0x0f, 0x28, 0xca, 0x2c, 0xcb, 0xcc, 0x49, 0x4d, 0x4f, 0xd5, 0x2b, 0x28, 0xca,
	0x2f, 0xc9, 0x17, 0x62, 0x01, 0x89, 0x2b, 0xdd, 0x60, 0xe4, 0x12, 0x72, 0x49, 0x0a, 0x45, 0x53,
	0x22, 0x24, 0xc0, 0xc5, 0x1c, 0xe4, 0xe9, 0x22, 0xc1, 0xa8, 0xc0, 0xa8, 0xc1, 0x19, 0x04, 0x62,
	0x0a, 0x49, 0x70, 0xb1, 0x83, 0x54, 0x81, 0x44, 0x99, 0xc1, 0xa2, 0x30, 0xae, 0x90, 0x18, 0x17,
	0x9b, 0x7f, 0x51, 0x3a, 0x48, 0x82, 0x05, 0x2c, 0x01, 0xe5, 0x09, 0x69, 0x70, 0xf1, 0x7b, 0xe6,
	0x25, 0xe7, 0x94, 0xa6, 0xa4, 0x3a, 0x67, 0x64, 0xe6, 0xa4, 0x14, 0xa5, 0xe6, 0x49, 0xb0, 0x2a,
	0x30, 0x6a, 0xb0, 0x06, 0xa1, 0x0b, 0x83, 0xcc, 0x0e, 0x4e, 0x2d, 0x29, 0xc9, 0xcc, 0x4b, 0x97,
	0xe0, 0x80, 0x98, 0x0d, 0xe5, 0x0a, 0xc9, 0x70, 0x71, 0x86, 0x16, 0xa4, 0x24, 0x96, 0xa4, 0xa6,
	0x38, 0x96, 0x48, 0xf0, 0x81, 0xe5, 0x10, 0x02, 0x48, 0xb2, 0x2e, 0xce, 0x12, 0xfc, 0x28, 0xb2,
	0x2e, 0xce, 0x4e, 0x36, 0x27, 0x1e, 0xc9, 0x31, 0x5e, 0x78, 0x24, 0xc7, 0xf8, 0xe0, 0x91, 0x1c,
	0xe3, 0x84, 0xc7, 0x72, 0x0c, 0x17, 0x1e, 0xcb, 0x31, 0xdc, 0x78, 0x2c, 0xc7, 0x10, 0xa5, 0x94,
	0x9e, 0x59, 0xa2, 0x97, 0x9d, 0x99, 0x9c, 0x98, 0x62, 0x69, 0xa9, 0x97, 0x9c, 0x9f, 0xab, 0x5f,
	0x99, 0x9d, 0x59, 0xa2, 0x9f, 0x92, 0x58, 0x92, 0x98, 0x94, 0x58, 0x9c, 0xaa, 0x0f, 0x0a, 0x98,
	0x24, 0x36, 0x70, 0x28, 0x19, 0x03, 0x02, 0x00, 0x00, 0xff, 0xff, 0x2a, 0x73, 0x9c, 0x33, 0x3f,
	0x01, 0x00, 0x00,
}

func (m *DbUserOrgPrivilege) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbUserOrgPrivilege) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbUserOrgPrivilege) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.UpdatedDC) > 0 {
		i -= len(m.UpdatedDC)
		copy(dAtA[i:], m.UpdatedDC)
		i = encodeVarintUserOrgPrivilege(dAtA, i, uint64(len(m.UpdatedDC)))
		i--
		dAtA[i] = 0x7a
	}
	if len(m.UpdatedAt) > 0 {
		i -= len(m.UpdatedAt)
		copy(dAtA[i:], m.UpdatedAt)
		i = encodeVarintUserOrgPrivilege(dAtA, i, uint64(len(m.UpdatedAt)))
		i--
		dAtA[i] = 0x72
	}
	if len(m.Setting) > 0 {
		i -= len(m.Setting)
		copy(dAtA[i:], m.Setting)
		i = encodeVarintUserOrgPrivilege(dAtA, i, uint64(len(m.Setting)))
		i--
		dAtA[i] = 0x42
	}
	if m.IncludeChildren != 0 {
		i = encodeVarintUserOrgPrivilege(dAtA, i, uint64(m.IncludeChildren))
		i--
		dAtA[i] = 0x28
	}
	if len(m.OrgRID) > 0 {
		i -= len(m.OrgRID)
		copy(dAtA[i:], m.OrgRID)
		i = encodeVarintUserOrgPrivilege(dAtA, i, uint64(len(m.OrgRID)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.UserRID) > 0 {
		i -= len(m.UserRID)
		copy(dAtA[i:], m.UserRID)
		i = encodeVarintUserOrgPrivilege(dAtA, i, uint64(len(m.UserRID)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.RID) > 0 {
		i -= len(m.RID)
		copy(dAtA[i:], m.RID)
		i = encodeVarintUserOrgPrivilege(dAtA, i, uint64(len(m.RID)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func encodeVarintUserOrgPrivilege(dAtA []byte, offset int, v uint64) int {
	offset -= sovUserOrgPrivilege(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *DbUserOrgPrivilege) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.RID)
	if l > 0 {
		n += 1 + l + sovUserOrgPrivilege(uint64(l))
	}
	l = len(m.UserRID)
	if l > 0 {
		n += 1 + l + sovUserOrgPrivilege(uint64(l))
	}
	l = len(m.OrgRID)
	if l > 0 {
		n += 1 + l + sovUserOrgPrivilege(uint64(l))
	}
	if m.IncludeChildren != 0 {
		n += 1 + sovUserOrgPrivilege(uint64(m.IncludeChildren))
	}
	l = len(m.Setting)
	if l > 0 {
		n += 1 + l + sovUserOrgPrivilege(uint64(l))
	}
	l = len(m.UpdatedAt)
	if l > 0 {
		n += 1 + l + sovUserOrgPrivilege(uint64(l))
	}
	l = len(m.UpdatedDC)
	if l > 0 {
		n += 1 + l + sovUserOrgPrivilege(uint64(l))
	}
	return n
}

func sovUserOrgPrivilege(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozUserOrgPrivilege(x uint64) (n int) {
	return sovUserOrgPrivilege(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *DbUserOrgPrivilege) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserOrgPrivilege
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DbUserOrgPrivilege: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DbUserOrgPrivilege: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserOrgPrivilege
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserOrgPrivilege
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthUserOrgPrivilege
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.RID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UserRID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserOrgPrivilege
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserOrgPrivilege
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthUserOrgPrivilege
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UserRID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OrgRID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserOrgPrivilege
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserOrgPrivilege
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthUserOrgPrivilege
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OrgRID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field IncludeChildren", wireType)
			}
			m.IncludeChildren = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserOrgPrivilege
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.IncludeChildren |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Setting", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserOrgPrivilege
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserOrgPrivilege
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthUserOrgPrivilege
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Setting = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 14:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UpdatedAt", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserOrgPrivilege
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserOrgPrivilege
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthUserOrgPrivilege
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UpdatedAt = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 15:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UpdatedDC", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserOrgPrivilege
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserOrgPrivilege
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthUserOrgPrivilege
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UpdatedDC = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUserOrgPrivilege(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserOrgPrivilege
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthUserOrgPrivilege
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipUserOrgPrivilege(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowUserOrgPrivilege
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowUserOrgPrivilege
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowUserOrgPrivilege
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthUserOrgPrivilege
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupUserOrgPrivilege
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthUserOrgPrivilege
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthUserOrgPrivilege        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowUserOrgPrivilege          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupUserOrgPrivilege = fmt.Errorf("proto: unexpected end of group")
)

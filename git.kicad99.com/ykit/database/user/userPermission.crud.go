//Package user generated by ygen-gocrud. DO NOT EDIT.
//source: userPermission.proto
package user

import (
	"git.kicad99.com/ykit/goutil/crud"
	"github.com/jackc/pgx/v4"
	"github.com/jackc/pgx/v4/pgxpool"
)

//Insert
func (this *DbPermission) Insert(db *pgxpool.Conn) (r *crud.DMLResult, err error) {
	return crud.Insert(this, db)
}

//Update
func (this *DbPermission) Update(db *pgxpool.Conn) (r *crud.DMLResult, err error) {
	return crud.Update(this, db)
}

//PartialUpdate
func (this *DbPermission) PartialUpdate(updateCols []string, db *pgxpool.Conn) (r *crud.DMLResult, err error) {

	return crud.PartialUpdate(this, updateCols, db)
}

//Delete
func (this *DbPermission) Delete(db *pgxpool.Conn) (r *crud.DMLResult, err error) {
	return crud.Delete(this, db)
}

//SelectOne
func (this *DbPermission) SelectOne(resultCols []string, db *pgxpool.Conn) (selectRow int32, err error) {

	return crud.YkitDbSelectOne(this, resultCols, []string{"RID"}, db)
}

//SelectByRids
func (this *DbPermission) SelectByRids(resultCols []string, rids []string, db *pgxpool.Conn) (rows pgx.Rows, err error) {

	return crud.YkitDbSelectByRIDs(this, resultCols, rids, db)
}

//Insert
func (this *DbRole) Insert(db *pgxpool.Conn) (r *crud.DMLResult, err error) {
	return crud.Insert(this, db)
}

//Update
func (this *DbRole) Update(db *pgxpool.Conn) (r *crud.DMLResult, err error) {
	return crud.Update(this, db)
}

//PartialUpdate
func (this *DbRole) PartialUpdate(updateCols []string, db *pgxpool.Conn) (r *crud.DMLResult, err error) {

	return crud.PartialUpdate(this, updateCols, db)
}

//Delete
func (this *DbRole) Delete(db *pgxpool.Conn) (r *crud.DMLResult, err error) {
	return crud.Delete(this, db)
}

//SelectOne
func (this *DbRole) SelectOne(resultCols []string, db *pgxpool.Conn) (selectRow int32, err error) {

	return crud.YkitDbSelectOne(this, resultCols, []string{"RID"}, db)
}

//SelectByRids
func (this *DbRole) SelectByRids(resultCols []string, rids []string, db *pgxpool.Conn) (rows pgx.Rows, err error) {

	return crud.YkitDbSelectByRIDs(this, resultCols, rids, db)
}

//Insert
func (this *DbRolePermission) Insert(db *pgxpool.Conn) (r *crud.DMLResult, err error) {
	return crud.Insert(this, db)
}

//Update
func (this *DbRolePermission) Update(db *pgxpool.Conn) (r *crud.DMLResult, err error) {
	return crud.Update(this, db)
}

//PartialUpdate
func (this *DbRolePermission) PartialUpdate(updateCols []string, db *pgxpool.Conn) (r *crud.DMLResult, err error) {

	return crud.PartialUpdate(this, updateCols, db)
}

//Delete
func (this *DbRolePermission) Delete(db *pgxpool.Conn) (r *crud.DMLResult, err error) {
	return crud.Delete(this, db)
}

//SelectOne
func (this *DbRolePermission) SelectOne(resultCols []string, db *pgxpool.Conn) (selectRow int32, err error) {

	return crud.YkitDbSelectOne(this, resultCols, []string{"RID"}, db)
}

//SelectByRids
func (this *DbRolePermission) SelectByRids(resultCols []string, rids []string, db *pgxpool.Conn) (rows pgx.Rows, err error) {

	return crud.YkitDbSelectByRIDs(this, resultCols, rids, db)
}

//Package user  generated by ygen-gocrud. DO NOT EDIT.
//source: userPermission.proto
package user

import (
	"context"
	"strconv"

	"git.kicad99.com/ykit/goutil/crud"
	"git.kicad99.com/ykit/goutil/crudhook"
	"git.kicad99.com/ykit/goutil"
	"git.kicad99.com/ykit/goutil/pgxdb"
	"git.kicad99.com/ykit/goutil/rpc"
	"git.kicad99.com/ykit/goutil/yreflect"
	"google.golang.org/grpc/metadata"
)

// CrudRpcDbPermissionServer impl crud Service
type CrudRpcDbPermissionServer struct {
}

//Insert impl crud insert
func (*CrudRpcDbPermissionServer) Insert(ctx context.Context, req *DbPermission) (r *crud.DMLResult, err error) {
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	r, err = req.Insert(db)
	if err == nil {
		sys, userRid, sid := pgxdb.GetYkitInfoFromMetadata(ctx)
		ipinfo := goutil.GetIpinfoFromMeta(ctx)
		userOrgrid := goutil.GetUserOrgRIDFromMeta(ctx)
		_ = sid
		crudhook.CrudAfterHook(sys, userRid, userOrgrid, "DbPermission.Insert", req, nil, ipinfo)
	}
	return
}

//Update impl crud update
func (*CrudRpcDbPermissionServer) Update(ctx context.Context, req *DbPermission) (r *crud.DMLResult, err error) {
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	r, err = req.Update(db)
	if err == nil {
		sys, userRid, sid := pgxdb.GetYkitInfoFromMetadata(ctx)
		ipinfo := goutil.GetIpinfoFromMeta(ctx)
		userOrgrid := goutil.GetUserOrgRIDFromMeta(ctx)
		_ = sid
		crudhook.CrudAfterHook(sys, userRid, userOrgrid, "DbPermission.Update", req, nil, ipinfo)
	}
	return
}

//PartialUpdate impl crud PartialUpdate
func (*CrudRpcDbPermissionServer) PartialUpdate(ctx context.Context, req *DbPermission) (r *crud.DMLResult, err error) {
	param, err := crud.GetDMLParamFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	r, err = req.PartialUpdate(param.KeyColumn, db)
	if err == nil {
		sys, userRid, sid := pgxdb.GetYkitInfoFromMetadata(ctx)
		ipinfo := goutil.GetIpinfoFromMeta(ctx)
		userOrgrid := goutil.GetUserOrgRIDFromMeta(ctx)
		_ = sid
		crudhook.CrudAfterHook(sys, userRid, userOrgrid, "DbPermission.PartialUpdate", req, param, ipinfo)
	}
	return
}

//Delete impl crud Delete
func (*CrudRpcDbPermissionServer) Delete(ctx context.Context, req *DbPermission) (r *crud.DMLResult, err error) {
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	r, err = req.Delete(db)
	if err == nil {
		sys, userRid, sid := pgxdb.GetYkitInfoFromMetadata(ctx)
		ipinfo := goutil.GetIpinfoFromMeta(ctx)
		userOrgrid := goutil.GetUserOrgRIDFromMeta(ctx)
		_ = sid
		crudhook.CrudAfterHook(sys, userRid, userOrgrid, "DbPermission.Delete", req, nil, ipinfo)
	}
	return
}

//SelectOne impl crud SelectOne
func (*CrudRpcDbPermissionServer) SelectOne(ctx context.Context, dmlParam *crud.DMLParam) (*DbPermission, error) {
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	if len(dmlParam.KeyValue) != 1 {
		return nil, rpc.EBadProtoParam
	}
	msg := &DbPermission{RID: dmlParam.KeyValue[0]}

	selectRow, err := msg.SelectOne(dmlParam.ResultColumn, db)
	if err != nil {
		return nil, err
	}
	if selectRow == 0 {
		msg.Reset()
	}
	return msg, nil
}

//SelectMany impl crud SelectMany
func (*CrudRpcDbPermissionServer) SelectMany(dmlParam *crud.DMLParam, srv RpcDbPermission_SelectManyServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)

	msg := &DbPermission{}

	rows, err := msg.SelectByRids(dmlParam.ResultColumn, dmlParam.KeyValue, db)
	if err != nil {
		return err
	}
	defer rows.Close()
	rowsName := crud.RowsColNames2GoFieldNames(rows, msg)
	rowsCount := 0
	for rows.Next() {
		msg := &DbPermission{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		err = srv.Send(msg)
		if err != nil {
			return err
		}
		rowsCount++
	}
	trailer := metadata.Pairs("rows", strconv.Itoa(rowsCount))
	srv.SetTrailer(trailer)
	return nil
}

//Query impl crud Query
func (*CrudRpcDbPermissionServer) Query(req *crud.QueryParam, srv RpcDbPermission_QueryServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)

	rows, err := crud.DbSelectQuery("DbPermission", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbPermission{})
	rowsCount := 0
	for rows.Next() {
		msg := &DbPermission{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		err = srv.Send(msg)
		if err != nil {
			return err
		}
		rowsCount++
	}
	trailer := metadata.Pairs("rows", strconv.Itoa(rowsCount))
	srv.SetTrailer(trailer)
	return nil
}
//Query impl crud QueryBatch
func (*CrudRpcDbPermissionServer) QueryBatch(req *crud.QueryParam, srv RpcDbPermission_QueryBatchServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)

	rows, err := crud.DbSelectQuery("DbPermission", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	if req.Batch < 1 {
		req.Batch = 1
	}
	if req.Batch > 10000 {
		req.Batch = 10000
	}

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbPermission{})
	var batchNo uint32 = 0
	var rowsOffset uint32 = 0
	var batchCount uint32 = 0
	list := &DbPermissionList{}
	for rows.Next() {
		msg := &DbPermission{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		list.Rows = append(list.Rows, msg)
		batchCount++
		rowsOffset++

		if batchCount >= req.Batch {
			err = srv.Send(list)
			if err != nil {
				return err
			}
			batchNo++
			list = &DbPermissionList{
				BatchNo:    batchNo,
				RowsOffset: rowsOffset}
			batchCount = 0
		}
	}
	if batchCount > 0 {
		err = srv.Send(list)
		if err != nil {
			return err
		}
	}
	trailer := metadata.Pairs("rows", strconv.FormatUint(uint64(rowsOffset), 10))
	srv.SetTrailer(trailer)
	return nil
}

// CrudRpcDbRoleServer impl crud Service
type CrudRpcDbRoleServer struct {
}

//Insert impl crud insert
func (*CrudRpcDbRoleServer) Insert(ctx context.Context, req *DbRole) (r *crud.DMLResult, err error) {
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	r, err = req.Insert(db)
	if err == nil {
		sys, userRid, sid := pgxdb.GetYkitInfoFromMetadata(ctx)
		ipinfo := goutil.GetIpinfoFromMeta(ctx)
		userOrgrid := goutil.GetUserOrgRIDFromMeta(ctx)
		goutil.NatsCdc(sys, req.OrgRID, sid, "DbRole.Insert", req, nil)
		crudhook.CrudAfterHook(sys, userRid, userOrgrid, "DbRole.Insert", req, nil, ipinfo)
	}
	return
}

//Update impl crud update
func (*CrudRpcDbRoleServer) Update(ctx context.Context, req *DbRole) (r *crud.DMLResult, err error) {
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	r, err = req.Update(db)
	if err == nil {
		sys, userRid, sid := pgxdb.GetYkitInfoFromMetadata(ctx)
		ipinfo := goutil.GetIpinfoFromMeta(ctx)
		userOrgrid := goutil.GetUserOrgRIDFromMeta(ctx)
		goutil.NatsCdc(sys, req.OrgRID, sid, "DbRole.Update", req, nil)
		crudhook.CrudAfterHook(sys, userRid, userOrgrid, "DbRole.Update", req, nil, ipinfo)
	}
	return
}

//PartialUpdate impl crud PartialUpdate
func (*CrudRpcDbRoleServer) PartialUpdate(ctx context.Context, req *DbRole) (r *crud.DMLResult, err error) {
	param, err := crud.GetDMLParamFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	r, err = req.PartialUpdate(param.KeyColumn, db)
	if err == nil {
		sys, userRid, sid := pgxdb.GetYkitInfoFromMetadata(ctx)
		ipinfo := goutil.GetIpinfoFromMeta(ctx)
		userOrgrid := goutil.GetUserOrgRIDFromMeta(ctx)
		goutil.NatsCdc(sys, req.OrgRID, sid, "DbRole.PartialUpdate", req, param)
		crudhook.CrudAfterHook(sys, userRid, userOrgrid, "DbRole.PartialUpdate", req, param, ipinfo)
	}
	return
}

//Delete impl crud Delete
func (*CrudRpcDbRoleServer) Delete(ctx context.Context, req *DbRole) (r *crud.DMLResult, err error) {
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	r, err = req.Delete(db)
	if err == nil {
		sys, userRid, sid := pgxdb.GetYkitInfoFromMetadata(ctx)
		ipinfo := goutil.GetIpinfoFromMeta(ctx)
		userOrgrid := goutil.GetUserOrgRIDFromMeta(ctx)
		goutil.NatsCdc(sys, req.OrgRID, sid, "DbRole.Delete", req, nil)
		crudhook.CrudAfterHook(sys, userRid, userOrgrid, "DbRole.Delete", req, nil, ipinfo)
	}
	return
}

//SelectOne impl crud SelectOne
func (*CrudRpcDbRoleServer) SelectOne(ctx context.Context, dmlParam *crud.DMLParam) (*DbRole, error) {
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	if len(dmlParam.KeyValue) != 1 {
		return nil, rpc.EBadProtoParam
	}
	msg := &DbRole{RID: dmlParam.KeyValue[0]}

	selectRow, err := msg.SelectOne(dmlParam.ResultColumn, db)
	if err != nil {
		return nil, err
	}
	if selectRow == 0 {
		msg.Reset()
	}
	return msg, nil
}

//SelectMany impl crud SelectMany
func (*CrudRpcDbRoleServer) SelectMany(dmlParam *crud.DMLParam, srv RpcDbRole_SelectManyServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)

	msg := &DbRole{}

	rows, err := msg.SelectByRids(dmlParam.ResultColumn, dmlParam.KeyValue, db)
	if err != nil {
		return err
	}
	defer rows.Close()
	rowsName := crud.RowsColNames2GoFieldNames(rows, msg)
	rowsCount := 0
	for rows.Next() {
		msg := &DbRole{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		err = srv.Send(msg)
		if err != nil {
			return err
		}
		rowsCount++
	}
	trailer := metadata.Pairs("rows", strconv.Itoa(rowsCount))
	srv.SetTrailer(trailer)
	return nil
}

//Query impl crud Query
func (*CrudRpcDbRoleServer) Query(req *crud.QueryParam, srv RpcDbRole_QueryServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)

	rows, err := crud.DbSelectQuery("DbRole", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbRole{})
	rowsCount := 0
	for rows.Next() {
		msg := &DbRole{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		err = srv.Send(msg)
		if err != nil {
			return err
		}
		rowsCount++
	}
	trailer := metadata.Pairs("rows", strconv.Itoa(rowsCount))
	srv.SetTrailer(trailer)
	return nil
}
//Query impl crud QueryBatch
func (*CrudRpcDbRoleServer) QueryBatch(req *crud.QueryParam, srv RpcDbRole_QueryBatchServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)

	rows, err := crud.DbSelectQuery("DbRole", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	if req.Batch < 1 {
		req.Batch = 1
	}
	if req.Batch > 10000 {
		req.Batch = 10000
	}

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbRole{})
	var batchNo uint32 = 0
	var rowsOffset uint32 = 0
	var batchCount uint32 = 0
	list := &DbRoleList{}
	for rows.Next() {
		msg := &DbRole{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		list.Rows = append(list.Rows, msg)
		batchCount++
		rowsOffset++

		if batchCount >= req.Batch {
			err = srv.Send(list)
			if err != nil {
				return err
			}
			batchNo++
			list = &DbRoleList{
				BatchNo:    batchNo,
				RowsOffset: rowsOffset}
			batchCount = 0
		}
	}
	if batchCount > 0 {
		err = srv.Send(list)
		if err != nil {
			return err
		}
	}
	trailer := metadata.Pairs("rows", strconv.FormatUint(uint64(rowsOffset), 10))
	srv.SetTrailer(trailer)
	return nil
}

// PcrudRpcDbRoleServer impl pcrud Service
type PcrudRpcDbRoleServer struct {
}

//Insert impl pcrud Insert
func (*PcrudRpcDbRoleServer) Insert(ctx context.Context, req *DbRole) (*crud.DMLResult, error) {
	var crudServer *CrudRpcDbRoleServer
	_, err := crud.GrpcCheckHasPermission(ctx, "db", "DbRole.Insert")
	if err != nil {
		return nil, err
	}
	err = crud.GrpcCheckHasOrgridPrivilege(ctx, req.OrgRID)
	if err != nil {
		return nil, err
	}
	return crudServer.Insert(ctx, req)
}

//Update impl pcrud Update
func (*PcrudRpcDbRoleServer) Update(ctx context.Context, req *DbRole) (*crud.DMLResult, error) {
	var crudServer *CrudRpcDbRoleServer
	_, err := crud.GrpcCheckHasPermission(ctx, "db", "DbRole.Update")
	if err != nil {
		return nil, err
	}
	err = crud.GrpcCheckHasOrgridPrivilege(ctx, req.OrgRID)
	if err != nil {
		return nil, err
	}
	return crudServer.Update(ctx, req)
}

//PartialUpdate impl pcrud PartialUpdate
func (*PcrudRpcDbRoleServer) PartialUpdate(ctx context.Context, req *DbRole) (*crud.DMLResult, error) {
	var crudServer *CrudRpcDbRoleServer
	_, err := crud.GrpcCheckHasPermission(ctx, "db", "DbRole.Update")
	if err != nil {
		return nil, err
	}
	err = crud.GrpcCheckHasOrgridPrivilege(ctx, req.OrgRID)
	if err != nil {
		return nil, err
	}
	return crudServer.PartialUpdate(ctx, req)
}

//Delete impl pcrud Delete
func (*PcrudRpcDbRoleServer) Delete(ctx context.Context, req *DbRole) (*crud.DMLResult, error) {
	var crudServer *CrudRpcDbRoleServer
	_, err := crud.GrpcCheckHasPermission(ctx, "db", "DbRole.Delete")
	if err != nil {
		return nil, err
	}
	err = crud.GrpcCheckHasOrgridPrivilege(ctx, req.OrgRID)
	if err != nil {
		return nil, err
	}
	return crudServer.Delete(ctx, req)
}

//SelectOne impl pcrud SelectOne
func (*PcrudRpcDbRoleServer) SelectOne(ctx context.Context, dmlParam *crud.DMLParam) (*DbRole, error) {
	var crudServer *CrudRpcDbRoleServer
	return crudServer.SelectOne(ctx, dmlParam)
}

//SelectMany impl pcrud SelectMany
func (*PcrudRpcDbRoleServer) SelectMany(dmlParam *crud.DMLParam, srv PrpcDbRole_SelectManyServer) error {
	var crudServer *CrudRpcDbRoleServer
	return crudServer.SelectMany(dmlParam, srv)
}

//Query impl pcrud Query
func (*PcrudRpcDbRoleServer) Query(req *crud.PrivilegeParam, srv PrpcDbRole_QueryServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)
	rows, err := crud.DbSelectPrivilege("DbRole", "OrgRID", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbRole{})
	rowsCount := 0
	for rows.Next() {
		msg := &DbRole{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		err = srv.Send(msg)
		if err != nil {
			return err
		}
		rowsCount++
	}
	trailer := metadata.Pairs("rows", strconv.Itoa(rowsCount))
	srv.SetTrailer(trailer)
	return nil
}
//Query impl pcrud QueryBatch
func (*PcrudRpcDbRoleServer) QueryBatch(req *crud.PrivilegeParam, srv PrpcDbRole_QueryBatchServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)
	rows, err := crud.DbSelectPrivilege("DbRole", "OrgRID", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	if req.QueryCondition.Batch < 1 {
		req.QueryCondition.Batch = 1
	}
	if req.QueryCondition.Batch > 10000 {
		req.QueryCondition.Batch = 10000
	}

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbRole{})

	var batchNo uint32 = 0
	var rowsOffset uint32 = 0
	var batchCount uint32 = 0
	list := &DbRoleList{}
	for rows.Next() {
		msg := &DbRole{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		list.Rows = append(list.Rows, msg)
		batchCount++
		rowsOffset++

		if batchCount >= req.QueryCondition.Batch {
			err = srv.Send(list)
			if err != nil {
				return err
			}
			batchNo++
			list = &DbRoleList{
				BatchNo:    batchNo,
				RowsOffset: rowsOffset}
			batchCount = 0
		}
	}
	if batchCount > 0 {
		err = srv.Send(list)
		if err != nil {
			return err
		}
	}
	trailer := metadata.Pairs("rows", strconv.FormatUint(uint64(rowsOffset), 10))
	srv.SetTrailer(trailer)
	return nil
}
// CrudRpcDbRolePermissionServer impl crud Service
type CrudRpcDbRolePermissionServer struct {
}

//Insert impl crud insert
func (*CrudRpcDbRolePermissionServer) Insert(ctx context.Context, req *DbRolePermission) (r *crud.DMLResult, err error) {
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	r, err = req.Insert(db)
	if err == nil {
		sys, userRid, sid := pgxdb.GetYkitInfoFromMetadata(ctx)
		ipinfo := goutil.GetIpinfoFromMeta(ctx)
		userOrgrid := goutil.GetUserOrgRIDFromMeta(ctx)
		_ = sid
		crudhook.CrudAfterHook(sys, userRid, userOrgrid, "DbRolePermission.Insert", req, nil, ipinfo)
	}
	return
}

//Update impl crud update
func (*CrudRpcDbRolePermissionServer) Update(ctx context.Context, req *DbRolePermission) (r *crud.DMLResult, err error) {
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	r, err = req.Update(db)
	if err == nil {
		sys, userRid, sid := pgxdb.GetYkitInfoFromMetadata(ctx)
		ipinfo := goutil.GetIpinfoFromMeta(ctx)
		userOrgrid := goutil.GetUserOrgRIDFromMeta(ctx)
		_ = sid
		crudhook.CrudAfterHook(sys, userRid, userOrgrid, "DbRolePermission.Update", req, nil, ipinfo)
	}
	return
}

//PartialUpdate impl crud PartialUpdate
func (*CrudRpcDbRolePermissionServer) PartialUpdate(ctx context.Context, req *DbRolePermission) (r *crud.DMLResult, err error) {
	param, err := crud.GetDMLParamFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	r, err = req.PartialUpdate(param.KeyColumn, db)
	if err == nil {
		sys, userRid, sid := pgxdb.GetYkitInfoFromMetadata(ctx)
		ipinfo := goutil.GetIpinfoFromMeta(ctx)
		userOrgrid := goutil.GetUserOrgRIDFromMeta(ctx)
		_ = sid
		crudhook.CrudAfterHook(sys, userRid, userOrgrid, "DbRolePermission.PartialUpdate", req, param, ipinfo)
	}
	return
}

//Delete impl crud Delete
func (*CrudRpcDbRolePermissionServer) Delete(ctx context.Context, req *DbRolePermission) (r *crud.DMLResult, err error) {
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	r, err = req.Delete(db)
	if err == nil {
		sys, userRid, sid := pgxdb.GetYkitInfoFromMetadata(ctx)
		ipinfo := goutil.GetIpinfoFromMeta(ctx)
		userOrgrid := goutil.GetUserOrgRIDFromMeta(ctx)
		_ = sid
		crudhook.CrudAfterHook(sys, userRid, userOrgrid, "DbRolePermission.Delete", req, nil, ipinfo)
	}
	return
}

//SelectOne impl crud SelectOne
func (*CrudRpcDbRolePermissionServer) SelectOne(ctx context.Context, dmlParam *crud.DMLParam) (*DbRolePermission, error) {
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	if len(dmlParam.KeyValue) != 1 {
		return nil, rpc.EBadProtoParam
	}
	msg := &DbRolePermission{RID: dmlParam.KeyValue[0]}

	selectRow, err := msg.SelectOne(dmlParam.ResultColumn, db)
	if err != nil {
		return nil, err
	}
	if selectRow == 0 {
		msg.Reset()
	}
	return msg, nil
}

//SelectMany impl crud SelectMany
func (*CrudRpcDbRolePermissionServer) SelectMany(dmlParam *crud.DMLParam, srv RpcDbRolePermission_SelectManyServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)

	msg := &DbRolePermission{}

	rows, err := msg.SelectByRids(dmlParam.ResultColumn, dmlParam.KeyValue, db)
	if err != nil {
		return err
	}
	defer rows.Close()
	rowsName := crud.RowsColNames2GoFieldNames(rows, msg)
	rowsCount := 0
	for rows.Next() {
		msg := &DbRolePermission{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		err = srv.Send(msg)
		if err != nil {
			return err
		}
		rowsCount++
	}
	trailer := metadata.Pairs("rows", strconv.Itoa(rowsCount))
	srv.SetTrailer(trailer)
	return nil
}

//Query impl crud Query
func (*CrudRpcDbRolePermissionServer) Query(req *crud.QueryParam, srv RpcDbRolePermission_QueryServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)

	rows, err := crud.DbSelectQuery("DbRolePermission", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbRolePermission{})
	rowsCount := 0
	for rows.Next() {
		msg := &DbRolePermission{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		err = srv.Send(msg)
		if err != nil {
			return err
		}
		rowsCount++
	}
	trailer := metadata.Pairs("rows", strconv.Itoa(rowsCount))
	srv.SetTrailer(trailer)
	return nil
}
//Query impl crud QueryBatch
func (*CrudRpcDbRolePermissionServer) QueryBatch(req *crud.QueryParam, srv RpcDbRolePermission_QueryBatchServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)

	rows, err := crud.DbSelectQuery("DbRolePermission", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	if req.Batch < 1 {
		req.Batch = 1
	}
	if req.Batch > 10000 {
		req.Batch = 10000
	}

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbRolePermission{})
	var batchNo uint32 = 0
	var rowsOffset uint32 = 0
	var batchCount uint32 = 0
	list := &DbRolePermissionList{}
	for rows.Next() {
		msg := &DbRolePermission{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		list.Rows = append(list.Rows, msg)
		batchCount++
		rowsOffset++

		if batchCount >= req.Batch {
			err = srv.Send(list)
			if err != nil {
				return err
			}
			batchNo++
			list = &DbRolePermissionList{
				BatchNo:    batchNo,
				RowsOffset: rowsOffset}
			batchCount = 0
		}
	}
	if batchCount > 0 {
		err = srv.Send(list)
		if err != nil {
			return err
		}
	}
	trailer := metadata.Pairs("rows", strconv.FormatUint(uint64(rowsOffset), 10))
	srv.SetTrailer(trailer)
	return nil
}

// PcrudRpcDbRolePermissionServer impl pcrud Service
type PcrudRpcDbRolePermissionServer struct {
}

//Insert impl pcrud Insert
func (*PcrudRpcDbRolePermissionServer) Insert(ctx context.Context, req *DbRolePermission) (*crud.DMLResult, error) {
	var crudServer *CrudRpcDbRolePermissionServer
	_, err := crud.GrpcCheckHasPermission(ctx, "db", "DbRolePermission.Insert")
	if err != nil {
		return nil, err
	}
	
	return crudServer.Insert(ctx, req)
}

//Update impl pcrud Update
func (*PcrudRpcDbRolePermissionServer) Update(ctx context.Context, req *DbRolePermission) (*crud.DMLResult, error) {
	var crudServer *CrudRpcDbRolePermissionServer
	_, err := crud.GrpcCheckHasPermission(ctx, "db", "DbRolePermission.Update")
	if err != nil {
		return nil, err
	}
	
	return crudServer.Update(ctx, req)
}

//PartialUpdate impl pcrud PartialUpdate
func (*PcrudRpcDbRolePermissionServer) PartialUpdate(ctx context.Context, req *DbRolePermission) (*crud.DMLResult, error) {
	var crudServer *CrudRpcDbRolePermissionServer
	_, err := crud.GrpcCheckHasPermission(ctx, "db", "DbRolePermission.Update")
	if err != nil {
		return nil, err
	}
	
	return crudServer.PartialUpdate(ctx, req)
}

//Delete impl pcrud Delete
func (*PcrudRpcDbRolePermissionServer) Delete(ctx context.Context, req *DbRolePermission) (*crud.DMLResult, error) {
	var crudServer *CrudRpcDbRolePermissionServer
	_, err := crud.GrpcCheckHasPermission(ctx, "db", "DbRolePermission.Delete")
	if err != nil {
		return nil, err
	}
	
	return crudServer.Delete(ctx, req)
}

//SelectOne impl pcrud SelectOne
func (*PcrudRpcDbRolePermissionServer) SelectOne(ctx context.Context, dmlParam *crud.DMLParam) (*DbRolePermission, error) {
	var crudServer *CrudRpcDbRolePermissionServer
	return crudServer.SelectOne(ctx, dmlParam)
}

//SelectMany impl pcrud SelectMany
func (*PcrudRpcDbRolePermissionServer) SelectMany(dmlParam *crud.DMLParam, srv PrpcDbRolePermission_SelectManyServer) error {
	var crudServer *CrudRpcDbRolePermissionServer
	return crudServer.SelectMany(dmlParam, srv)
}

//Query impl pcrud Query
func (*PcrudRpcDbRolePermissionServer) Query(req *crud.PrivilegeParam, srv PrpcDbRolePermission_QueryServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)
	rows, err := crud.DbSelectPrivilege("DbRolePermission", "", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbRolePermission{})
	rowsCount := 0
	for rows.Next() {
		msg := &DbRolePermission{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		err = srv.Send(msg)
		if err != nil {
			return err
		}
		rowsCount++
	}
	trailer := metadata.Pairs("rows", strconv.Itoa(rowsCount))
	srv.SetTrailer(trailer)
	return nil
}
//Query impl pcrud QueryBatch
func (*PcrudRpcDbRolePermissionServer) QueryBatch(req *crud.PrivilegeParam, srv PrpcDbRolePermission_QueryBatchServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)
	rows, err := crud.DbSelectPrivilege("DbRolePermission", "", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	if req.QueryCondition.Batch < 1 {
		req.QueryCondition.Batch = 1
	}
	if req.QueryCondition.Batch > 10000 {
		req.QueryCondition.Batch = 10000
	}

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbRolePermission{})

	var batchNo uint32 = 0
	var rowsOffset uint32 = 0
	var batchCount uint32 = 0
	list := &DbRolePermissionList{}
	for rows.Next() {
		msg := &DbRolePermission{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		list.Rows = append(list.Rows, msg)
		batchCount++
		rowsOffset++

		if batchCount >= req.QueryCondition.Batch {
			err = srv.Send(list)
			if err != nil {
				return err
			}
			batchNo++
			list = &DbRolePermissionList{
				BatchNo:    batchNo,
				RowsOffset: rowsOffset}
			batchCount = 0
		}
	}
	if batchCount > 0 {
		err = srv.Send(list)
		if err != nil {
			return err
		}
	}
	trailer := metadata.Pairs("rows", strconv.FormatUint(uint64(rowsOffset), 10))
	srv.SetTrailer(trailer)
	return nil
}

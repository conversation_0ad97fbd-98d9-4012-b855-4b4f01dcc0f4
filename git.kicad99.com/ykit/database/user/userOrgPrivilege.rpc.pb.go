// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: userOrgPrivilege.rpc.proto

package user

import (
	context "context"
	fmt "fmt"
	crud "git.kicad99.com/ykit/goutil/crud"
	proto "github.com/gogo/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

func init() { proto.RegisterFile("userOrgPrivilege.rpc.proto", fileDescriptor_1c9487426d407095) }

var fileDescriptor_1c9487426d407095 = []byte{
	// 331 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xec, 0xd4, 0xcf, 0x4a, 0x3a, 0x51,
	0x14, 0x07, 0x70, 0x07, 0x7f, 0x3f, 0xa1, 0x03, 0xfd, 0xe1, 0x52, 0x12, 0x53, 0xcc, 0xa2, 0x07,
	0xb8, 0x8a, 0x22, 0x64, 0x44, 0x0b, 0x71, 0x13, 0x28, 0x4e, 0x86, 0x9b, 0x76, 0xd7, 0x3b, 0x07,
	0xbb, 0x38, 0xea, 0x70, 0xef, 0x99, 0xc0, 0xb7, 0xe8, 0xb1, 0x5a, 0xba, 0x8c, 0x56, 0xe1, 0xbc,
	0x48, 0xcc, 0x5c, 0x12, 0x4a, 0xc5, 0x66, 0xd1, 0xae, 0xe5, 0x9c, 0xf3, 0xfd, 0x9c, 0x59, 0x7c,
	0xe1, 0x82, 0x1b, 0x1b, 0xd4, 0x3d, 0x3d, 0xf2, 0xb5, 0x7a, 0x52, 0x21, 0x8e, 0x90, 0xeb, 0x48,
	0xf2, 0x48, 0xcf, 0x68, 0xc6, 0xfe, 0xa5, 0x3b, 0x17, 0xa4, 0x8e, 0x03, 0x3b, 0x71, 0xcb, 0x6b,
	0x69, 0x3b, 0x3f, 0x5b, 0x9b, 0x87, 0xca, 0x90, 0x5d, 0xd6, 0xde, 0x8a, 0x70, 0xd2, 0x8f, 0x64,
	0x7b, 0x38, 0xf8, 0x16, 0x62, 0x75, 0x28, 0xdd, 0x4e, 0x0d, 0x6a, 0x62, 0xa7, 0x3c, 0xbd, 0xc0,
	0xd7, 0x33, 0xee, 0x21, 0xcf, 0xfe, 0xdf, 0xee, 0x76, 0xfa, 0x68, 0xe2, 0x90, 0x52, 0x34, 0x88,
	0x02, 0x41, 0x98, 0x07, 0x5d, 0xc1, 0xbe, 0x2f, 0x34, 0x29, 0x11, 0xe6, 0xb7, 0x75, 0x28, 0xb5,
	0x31, 0xc4, 0x7c, 0xa8, 0x01, 0x7b, 0xf7, 0x18, 0xa2, 0xa4, 0xde, 0x14, 0xd9, 0xc1, 0x6a, 0xeb,
	0x0b, 0x2d, 0x26, 0xee, 0xd6, 0x3b, 0xec, 0x12, 0xc0, 0xb2, 0xae, 0x98, 0xce, 0x7f, 0xee, 0xaa,
	0x0e, 0x6b, 0xc0, 0xff, 0xbb, 0x18, 0xf5, 0x9c, 0x1d, 0x59, 0x94, 0x7d, 0xec, 0x66, 0x37, 0x00,
	0x59, 0xb2, 0x25, 0x48, 0x3e, 0x6e, 0xb0, 0xe7, 0xdb, 0x6c, 0x47, 0x19, 0xaa, 0x3a, 0xb5, 0xa4,
	0x08, 0x65, 0x5f, 0xff, 0xb5, 0xfb, 0xcb, 0xed, 0x36, 0x3f, 0xdb, 0x3d, 0xb6, 0x68, 0xb5, 0xdb,
	0x4d, 0x5b, 0x5f, 0x1a, 0xde, 0xec, 0x77, 0xb4, 0xdc, 0xba, 0x7e, 0x59, 0x7a, 0xce, 0x62, 0xe9,
	0x39, 0xef, 0x4b, 0xcf, 0x79, 0x4e, 0xbc, 0xc2, 0x22, 0xf1, 0x0a, 0xaf, 0x89, 0x57, 0x78, 0xb8,
	0x18, 0x29, 0xe2, 0x63, 0x25, 0x45, 0xd0, 0x6c, 0x72, 0x39, 0x9b, 0x54, 0xe6, 0x63, 0x45, 0x95,
	0x40, 0x90, 0x18, 0x0a, 0x83, 0x95, 0xf4, 0xec, 0xb0, 0x94, 0xbd, 0x03, 0xf5, 0x8f, 0x00, 0x00,
	0x00, 0xff, 0xff, 0x22, 0xfe, 0x12, 0x47, 0x6c, 0x04, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// RpcDbUserOrgPrivilegeClient is the client API for RpcDbUserOrgPrivilege service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type RpcDbUserOrgPrivilegeClient interface {
	//插入一行数据
	Insert(ctx context.Context, in *DbUserOrgPrivilege, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,全量修改一行数据
	Update(ctx context.Context, in *DbUserOrgPrivilege, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
	PartialUpdate(ctx context.Context, in *DbUserOrgPrivilege, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,删除一行数据
	Delete(ctx context.Context, in *DbUserOrgPrivilege, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
	SelectOne(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (*DbUserOrgPrivilege, error)
	//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
	SelectMany(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (RpcDbUserOrgPrivilege_SelectManyClient, error)
	//根据条件查询数据
	Query(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbUserOrgPrivilege_QueryClient, error)
	//根据条件查询数据,批量返回数据,crud.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbUserOrgPrivilege_QueryBatchClient, error)
}

type rpcDbUserOrgPrivilegeClient struct {
	cc *grpc.ClientConn
}

func NewRpcDbUserOrgPrivilegeClient(cc *grpc.ClientConn) RpcDbUserOrgPrivilegeClient {
	return &rpcDbUserOrgPrivilegeClient{cc}
}

func (c *rpcDbUserOrgPrivilegeClient) Insert(ctx context.Context, in *DbUserOrgPrivilege, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/user.RpcDbUserOrgPrivilege/Insert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbUserOrgPrivilegeClient) Update(ctx context.Context, in *DbUserOrgPrivilege, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/user.RpcDbUserOrgPrivilege/Update", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbUserOrgPrivilegeClient) PartialUpdate(ctx context.Context, in *DbUserOrgPrivilege, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/user.RpcDbUserOrgPrivilege/PartialUpdate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbUserOrgPrivilegeClient) Delete(ctx context.Context, in *DbUserOrgPrivilege, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/user.RpcDbUserOrgPrivilege/Delete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbUserOrgPrivilegeClient) SelectOne(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (*DbUserOrgPrivilege, error) {
	out := new(DbUserOrgPrivilege)
	err := c.cc.Invoke(ctx, "/user.RpcDbUserOrgPrivilege/SelectOne", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbUserOrgPrivilegeClient) SelectMany(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (RpcDbUserOrgPrivilege_SelectManyClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RpcDbUserOrgPrivilege_serviceDesc.Streams[0], "/user.RpcDbUserOrgPrivilege/SelectMany", opts...)
	if err != nil {
		return nil, err
	}
	x := &rpcDbUserOrgPrivilegeSelectManyClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type RpcDbUserOrgPrivilege_SelectManyClient interface {
	Recv() (*DbUserOrgPrivilege, error)
	grpc.ClientStream
}

type rpcDbUserOrgPrivilegeSelectManyClient struct {
	grpc.ClientStream
}

func (x *rpcDbUserOrgPrivilegeSelectManyClient) Recv() (*DbUserOrgPrivilege, error) {
	m := new(DbUserOrgPrivilege)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *rpcDbUserOrgPrivilegeClient) Query(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbUserOrgPrivilege_QueryClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RpcDbUserOrgPrivilege_serviceDesc.Streams[1], "/user.RpcDbUserOrgPrivilege/Query", opts...)
	if err != nil {
		return nil, err
	}
	x := &rpcDbUserOrgPrivilegeQueryClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type RpcDbUserOrgPrivilege_QueryClient interface {
	Recv() (*DbUserOrgPrivilege, error)
	grpc.ClientStream
}

type rpcDbUserOrgPrivilegeQueryClient struct {
	grpc.ClientStream
}

func (x *rpcDbUserOrgPrivilegeQueryClient) Recv() (*DbUserOrgPrivilege, error) {
	m := new(DbUserOrgPrivilege)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *rpcDbUserOrgPrivilegeClient) QueryBatch(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbUserOrgPrivilege_QueryBatchClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RpcDbUserOrgPrivilege_serviceDesc.Streams[2], "/user.RpcDbUserOrgPrivilege/QueryBatch", opts...)
	if err != nil {
		return nil, err
	}
	x := &rpcDbUserOrgPrivilegeQueryBatchClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type RpcDbUserOrgPrivilege_QueryBatchClient interface {
	Recv() (*DbUserOrgPrivilegeList, error)
	grpc.ClientStream
}

type rpcDbUserOrgPrivilegeQueryBatchClient struct {
	grpc.ClientStream
}

func (x *rpcDbUserOrgPrivilegeQueryBatchClient) Recv() (*DbUserOrgPrivilegeList, error) {
	m := new(DbUserOrgPrivilegeList)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// RpcDbUserOrgPrivilegeServer is the server API for RpcDbUserOrgPrivilege service.
type RpcDbUserOrgPrivilegeServer interface {
	//插入一行数据
	Insert(context.Context, *DbUserOrgPrivilege) (*crud.DMLResult, error)
	//以RID为条件,全量修改一行数据
	Update(context.Context, *DbUserOrgPrivilege) (*crud.DMLResult, error)
	//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
	PartialUpdate(context.Context, *DbUserOrgPrivilege) (*crud.DMLResult, error)
	//以RID为条件,删除一行数据
	Delete(context.Context, *DbUserOrgPrivilege) (*crud.DMLResult, error)
	//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
	SelectOne(context.Context, *crud.DMLParam) (*DbUserOrgPrivilege, error)
	//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
	SelectMany(*crud.DMLParam, RpcDbUserOrgPrivilege_SelectManyServer) error
	//根据条件查询数据
	Query(*crud.QueryParam, RpcDbUserOrgPrivilege_QueryServer) error
	//根据条件查询数据,批量返回数据,crud.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(*crud.QueryParam, RpcDbUserOrgPrivilege_QueryBatchServer) error
}

// UnimplementedRpcDbUserOrgPrivilegeServer can be embedded to have forward compatible implementations.
type UnimplementedRpcDbUserOrgPrivilegeServer struct {
}

func (*UnimplementedRpcDbUserOrgPrivilegeServer) Insert(ctx context.Context, req *DbUserOrgPrivilege) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Insert not implemented")
}
func (*UnimplementedRpcDbUserOrgPrivilegeServer) Update(ctx context.Context, req *DbUserOrgPrivilege) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Update not implemented")
}
func (*UnimplementedRpcDbUserOrgPrivilegeServer) PartialUpdate(ctx context.Context, req *DbUserOrgPrivilege) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PartialUpdate not implemented")
}
func (*UnimplementedRpcDbUserOrgPrivilegeServer) Delete(ctx context.Context, req *DbUserOrgPrivilege) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Delete not implemented")
}
func (*UnimplementedRpcDbUserOrgPrivilegeServer) SelectOne(ctx context.Context, req *crud.DMLParam) (*DbUserOrgPrivilege, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SelectOne not implemented")
}
func (*UnimplementedRpcDbUserOrgPrivilegeServer) SelectMany(req *crud.DMLParam, srv RpcDbUserOrgPrivilege_SelectManyServer) error {
	return status.Errorf(codes.Unimplemented, "method SelectMany not implemented")
}
func (*UnimplementedRpcDbUserOrgPrivilegeServer) Query(req *crud.QueryParam, srv RpcDbUserOrgPrivilege_QueryServer) error {
	return status.Errorf(codes.Unimplemented, "method Query not implemented")
}
func (*UnimplementedRpcDbUserOrgPrivilegeServer) QueryBatch(req *crud.QueryParam, srv RpcDbUserOrgPrivilege_QueryBatchServer) error {
	return status.Errorf(codes.Unimplemented, "method QueryBatch not implemented")
}

func RegisterRpcDbUserOrgPrivilegeServer(s *grpc.Server, srv RpcDbUserOrgPrivilegeServer) {
	s.RegisterService(&_RpcDbUserOrgPrivilege_serviceDesc, srv)
}

func _RpcDbUserOrgPrivilege_Insert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbUserOrgPrivilege)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbUserOrgPrivilegeServer).Insert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.RpcDbUserOrgPrivilege/Insert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbUserOrgPrivilegeServer).Insert(ctx, req.(*DbUserOrgPrivilege))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbUserOrgPrivilege_Update_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbUserOrgPrivilege)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbUserOrgPrivilegeServer).Update(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.RpcDbUserOrgPrivilege/Update",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbUserOrgPrivilegeServer).Update(ctx, req.(*DbUserOrgPrivilege))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbUserOrgPrivilege_PartialUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbUserOrgPrivilege)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbUserOrgPrivilegeServer).PartialUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.RpcDbUserOrgPrivilege/PartialUpdate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbUserOrgPrivilegeServer).PartialUpdate(ctx, req.(*DbUserOrgPrivilege))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbUserOrgPrivilege_Delete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbUserOrgPrivilege)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbUserOrgPrivilegeServer).Delete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.RpcDbUserOrgPrivilege/Delete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbUserOrgPrivilegeServer).Delete(ctx, req.(*DbUserOrgPrivilege))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbUserOrgPrivilege_SelectOne_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(crud.DMLParam)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbUserOrgPrivilegeServer).SelectOne(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.RpcDbUserOrgPrivilege/SelectOne",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbUserOrgPrivilegeServer).SelectOne(ctx, req.(*crud.DMLParam))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbUserOrgPrivilege_SelectMany_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.DMLParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RpcDbUserOrgPrivilegeServer).SelectMany(m, &rpcDbUserOrgPrivilegeSelectManyServer{stream})
}

type RpcDbUserOrgPrivilege_SelectManyServer interface {
	Send(*DbUserOrgPrivilege) error
	grpc.ServerStream
}

type rpcDbUserOrgPrivilegeSelectManyServer struct {
	grpc.ServerStream
}

func (x *rpcDbUserOrgPrivilegeSelectManyServer) Send(m *DbUserOrgPrivilege) error {
	return x.ServerStream.SendMsg(m)
}

func _RpcDbUserOrgPrivilege_Query_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.QueryParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RpcDbUserOrgPrivilegeServer).Query(m, &rpcDbUserOrgPrivilegeQueryServer{stream})
}

type RpcDbUserOrgPrivilege_QueryServer interface {
	Send(*DbUserOrgPrivilege) error
	grpc.ServerStream
}

type rpcDbUserOrgPrivilegeQueryServer struct {
	grpc.ServerStream
}

func (x *rpcDbUserOrgPrivilegeQueryServer) Send(m *DbUserOrgPrivilege) error {
	return x.ServerStream.SendMsg(m)
}

func _RpcDbUserOrgPrivilege_QueryBatch_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.QueryParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RpcDbUserOrgPrivilegeServer).QueryBatch(m, &rpcDbUserOrgPrivilegeQueryBatchServer{stream})
}

type RpcDbUserOrgPrivilege_QueryBatchServer interface {
	Send(*DbUserOrgPrivilegeList) error
	grpc.ServerStream
}

type rpcDbUserOrgPrivilegeQueryBatchServer struct {
	grpc.ServerStream
}

func (x *rpcDbUserOrgPrivilegeQueryBatchServer) Send(m *DbUserOrgPrivilegeList) error {
	return x.ServerStream.SendMsg(m)
}

var _RpcDbUserOrgPrivilege_serviceDesc = grpc.ServiceDesc{
	ServiceName: "user.RpcDbUserOrgPrivilege",
	HandlerType: (*RpcDbUserOrgPrivilegeServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Insert",
			Handler:    _RpcDbUserOrgPrivilege_Insert_Handler,
		},
		{
			MethodName: "Update",
			Handler:    _RpcDbUserOrgPrivilege_Update_Handler,
		},
		{
			MethodName: "PartialUpdate",
			Handler:    _RpcDbUserOrgPrivilege_PartialUpdate_Handler,
		},
		{
			MethodName: "Delete",
			Handler:    _RpcDbUserOrgPrivilege_Delete_Handler,
		},
		{
			MethodName: "SelectOne",
			Handler:    _RpcDbUserOrgPrivilege_SelectOne_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "SelectMany",
			Handler:       _RpcDbUserOrgPrivilege_SelectMany_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "Query",
			Handler:       _RpcDbUserOrgPrivilege_Query_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "QueryBatch",
			Handler:       _RpcDbUserOrgPrivilege_QueryBatch_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "userOrgPrivilege.rpc.proto",
}

// PrpcDbUserOrgPrivilegeClient is the client API for PrpcDbUserOrgPrivilege service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type PrpcDbUserOrgPrivilegeClient interface {
	//插入一行数据
	Insert(ctx context.Context, in *DbUserOrgPrivilege, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,全量修改一行数据
	Update(ctx context.Context, in *DbUserOrgPrivilege, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
	PartialUpdate(ctx context.Context, in *DbUserOrgPrivilege, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,删除一行数据
	Delete(ctx context.Context, in *DbUserOrgPrivilege, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
	SelectOne(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (*DbUserOrgPrivilege, error)
	//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
	SelectMany(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (PrpcDbUserOrgPrivilege_SelectManyClient, error)
	//根据条件取得我有权限的数据
	Query(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbUserOrgPrivilege_QueryClient, error)
	//根据条件取得我有权限的数据,批量返回数据,crud.PrivilegeParam.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbUserOrgPrivilege_QueryBatchClient, error)
}

type prpcDbUserOrgPrivilegeClient struct {
	cc *grpc.ClientConn
}

func NewPrpcDbUserOrgPrivilegeClient(cc *grpc.ClientConn) PrpcDbUserOrgPrivilegeClient {
	return &prpcDbUserOrgPrivilegeClient{cc}
}

func (c *prpcDbUserOrgPrivilegeClient) Insert(ctx context.Context, in *DbUserOrgPrivilege, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/user.PrpcDbUserOrgPrivilege/Insert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prpcDbUserOrgPrivilegeClient) Update(ctx context.Context, in *DbUserOrgPrivilege, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/user.PrpcDbUserOrgPrivilege/Update", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prpcDbUserOrgPrivilegeClient) PartialUpdate(ctx context.Context, in *DbUserOrgPrivilege, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/user.PrpcDbUserOrgPrivilege/PartialUpdate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prpcDbUserOrgPrivilegeClient) Delete(ctx context.Context, in *DbUserOrgPrivilege, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/user.PrpcDbUserOrgPrivilege/Delete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prpcDbUserOrgPrivilegeClient) SelectOne(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (*DbUserOrgPrivilege, error) {
	out := new(DbUserOrgPrivilege)
	err := c.cc.Invoke(ctx, "/user.PrpcDbUserOrgPrivilege/SelectOne", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prpcDbUserOrgPrivilegeClient) SelectMany(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (PrpcDbUserOrgPrivilege_SelectManyClient, error) {
	stream, err := c.cc.NewStream(ctx, &_PrpcDbUserOrgPrivilege_serviceDesc.Streams[0], "/user.PrpcDbUserOrgPrivilege/SelectMany", opts...)
	if err != nil {
		return nil, err
	}
	x := &prpcDbUserOrgPrivilegeSelectManyClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type PrpcDbUserOrgPrivilege_SelectManyClient interface {
	Recv() (*DbUserOrgPrivilege, error)
	grpc.ClientStream
}

type prpcDbUserOrgPrivilegeSelectManyClient struct {
	grpc.ClientStream
}

func (x *prpcDbUserOrgPrivilegeSelectManyClient) Recv() (*DbUserOrgPrivilege, error) {
	m := new(DbUserOrgPrivilege)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *prpcDbUserOrgPrivilegeClient) Query(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbUserOrgPrivilege_QueryClient, error) {
	stream, err := c.cc.NewStream(ctx, &_PrpcDbUserOrgPrivilege_serviceDesc.Streams[1], "/user.PrpcDbUserOrgPrivilege/Query", opts...)
	if err != nil {
		return nil, err
	}
	x := &prpcDbUserOrgPrivilegeQueryClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type PrpcDbUserOrgPrivilege_QueryClient interface {
	Recv() (*DbUserOrgPrivilege, error)
	grpc.ClientStream
}

type prpcDbUserOrgPrivilegeQueryClient struct {
	grpc.ClientStream
}

func (x *prpcDbUserOrgPrivilegeQueryClient) Recv() (*DbUserOrgPrivilege, error) {
	m := new(DbUserOrgPrivilege)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *prpcDbUserOrgPrivilegeClient) QueryBatch(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbUserOrgPrivilege_QueryBatchClient, error) {
	stream, err := c.cc.NewStream(ctx, &_PrpcDbUserOrgPrivilege_serviceDesc.Streams[2], "/user.PrpcDbUserOrgPrivilege/QueryBatch", opts...)
	if err != nil {
		return nil, err
	}
	x := &prpcDbUserOrgPrivilegeQueryBatchClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type PrpcDbUserOrgPrivilege_QueryBatchClient interface {
	Recv() (*DbUserOrgPrivilegeList, error)
	grpc.ClientStream
}

type prpcDbUserOrgPrivilegeQueryBatchClient struct {
	grpc.ClientStream
}

func (x *prpcDbUserOrgPrivilegeQueryBatchClient) Recv() (*DbUserOrgPrivilegeList, error) {
	m := new(DbUserOrgPrivilegeList)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// PrpcDbUserOrgPrivilegeServer is the server API for PrpcDbUserOrgPrivilege service.
type PrpcDbUserOrgPrivilegeServer interface {
	//插入一行数据
	Insert(context.Context, *DbUserOrgPrivilege) (*crud.DMLResult, error)
	//以RID为条件,全量修改一行数据
	Update(context.Context, *DbUserOrgPrivilege) (*crud.DMLResult, error)
	//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
	PartialUpdate(context.Context, *DbUserOrgPrivilege) (*crud.DMLResult, error)
	//以RID为条件,删除一行数据
	Delete(context.Context, *DbUserOrgPrivilege) (*crud.DMLResult, error)
	//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
	SelectOne(context.Context, *crud.DMLParam) (*DbUserOrgPrivilege, error)
	//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
	SelectMany(*crud.DMLParam, PrpcDbUserOrgPrivilege_SelectManyServer) error
	//根据条件取得我有权限的数据
	Query(*crud.PrivilegeParam, PrpcDbUserOrgPrivilege_QueryServer) error
	//根据条件取得我有权限的数据,批量返回数据,crud.PrivilegeParam.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(*crud.PrivilegeParam, PrpcDbUserOrgPrivilege_QueryBatchServer) error
}

// UnimplementedPrpcDbUserOrgPrivilegeServer can be embedded to have forward compatible implementations.
type UnimplementedPrpcDbUserOrgPrivilegeServer struct {
}

func (*UnimplementedPrpcDbUserOrgPrivilegeServer) Insert(ctx context.Context, req *DbUserOrgPrivilege) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Insert not implemented")
}
func (*UnimplementedPrpcDbUserOrgPrivilegeServer) Update(ctx context.Context, req *DbUserOrgPrivilege) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Update not implemented")
}
func (*UnimplementedPrpcDbUserOrgPrivilegeServer) PartialUpdate(ctx context.Context, req *DbUserOrgPrivilege) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PartialUpdate not implemented")
}
func (*UnimplementedPrpcDbUserOrgPrivilegeServer) Delete(ctx context.Context, req *DbUserOrgPrivilege) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Delete not implemented")
}
func (*UnimplementedPrpcDbUserOrgPrivilegeServer) SelectOne(ctx context.Context, req *crud.DMLParam) (*DbUserOrgPrivilege, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SelectOne not implemented")
}
func (*UnimplementedPrpcDbUserOrgPrivilegeServer) SelectMany(req *crud.DMLParam, srv PrpcDbUserOrgPrivilege_SelectManyServer) error {
	return status.Errorf(codes.Unimplemented, "method SelectMany not implemented")
}
func (*UnimplementedPrpcDbUserOrgPrivilegeServer) Query(req *crud.PrivilegeParam, srv PrpcDbUserOrgPrivilege_QueryServer) error {
	return status.Errorf(codes.Unimplemented, "method Query not implemented")
}
func (*UnimplementedPrpcDbUserOrgPrivilegeServer) QueryBatch(req *crud.PrivilegeParam, srv PrpcDbUserOrgPrivilege_QueryBatchServer) error {
	return status.Errorf(codes.Unimplemented, "method QueryBatch not implemented")
}

func RegisterPrpcDbUserOrgPrivilegeServer(s *grpc.Server, srv PrpcDbUserOrgPrivilegeServer) {
	s.RegisterService(&_PrpcDbUserOrgPrivilege_serviceDesc, srv)
}

func _PrpcDbUserOrgPrivilege_Insert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbUserOrgPrivilege)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrpcDbUserOrgPrivilegeServer).Insert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.PrpcDbUserOrgPrivilege/Insert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrpcDbUserOrgPrivilegeServer).Insert(ctx, req.(*DbUserOrgPrivilege))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrpcDbUserOrgPrivilege_Update_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbUserOrgPrivilege)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrpcDbUserOrgPrivilegeServer).Update(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.PrpcDbUserOrgPrivilege/Update",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrpcDbUserOrgPrivilegeServer).Update(ctx, req.(*DbUserOrgPrivilege))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrpcDbUserOrgPrivilege_PartialUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbUserOrgPrivilege)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrpcDbUserOrgPrivilegeServer).PartialUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.PrpcDbUserOrgPrivilege/PartialUpdate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrpcDbUserOrgPrivilegeServer).PartialUpdate(ctx, req.(*DbUserOrgPrivilege))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrpcDbUserOrgPrivilege_Delete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbUserOrgPrivilege)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrpcDbUserOrgPrivilegeServer).Delete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.PrpcDbUserOrgPrivilege/Delete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrpcDbUserOrgPrivilegeServer).Delete(ctx, req.(*DbUserOrgPrivilege))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrpcDbUserOrgPrivilege_SelectOne_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(crud.DMLParam)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrpcDbUserOrgPrivilegeServer).SelectOne(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.PrpcDbUserOrgPrivilege/SelectOne",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrpcDbUserOrgPrivilegeServer).SelectOne(ctx, req.(*crud.DMLParam))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrpcDbUserOrgPrivilege_SelectMany_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.DMLParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(PrpcDbUserOrgPrivilegeServer).SelectMany(m, &prpcDbUserOrgPrivilegeSelectManyServer{stream})
}

type PrpcDbUserOrgPrivilege_SelectManyServer interface {
	Send(*DbUserOrgPrivilege) error
	grpc.ServerStream
}

type prpcDbUserOrgPrivilegeSelectManyServer struct {
	grpc.ServerStream
}

func (x *prpcDbUserOrgPrivilegeSelectManyServer) Send(m *DbUserOrgPrivilege) error {
	return x.ServerStream.SendMsg(m)
}

func _PrpcDbUserOrgPrivilege_Query_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.PrivilegeParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(PrpcDbUserOrgPrivilegeServer).Query(m, &prpcDbUserOrgPrivilegeQueryServer{stream})
}

type PrpcDbUserOrgPrivilege_QueryServer interface {
	Send(*DbUserOrgPrivilege) error
	grpc.ServerStream
}

type prpcDbUserOrgPrivilegeQueryServer struct {
	grpc.ServerStream
}

func (x *prpcDbUserOrgPrivilegeQueryServer) Send(m *DbUserOrgPrivilege) error {
	return x.ServerStream.SendMsg(m)
}

func _PrpcDbUserOrgPrivilege_QueryBatch_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.PrivilegeParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(PrpcDbUserOrgPrivilegeServer).QueryBatch(m, &prpcDbUserOrgPrivilegeQueryBatchServer{stream})
}

type PrpcDbUserOrgPrivilege_QueryBatchServer interface {
	Send(*DbUserOrgPrivilegeList) error
	grpc.ServerStream
}

type prpcDbUserOrgPrivilegeQueryBatchServer struct {
	grpc.ServerStream
}

func (x *prpcDbUserOrgPrivilegeQueryBatchServer) Send(m *DbUserOrgPrivilegeList) error {
	return x.ServerStream.SendMsg(m)
}

var _PrpcDbUserOrgPrivilege_serviceDesc = grpc.ServiceDesc{
	ServiceName: "user.PrpcDbUserOrgPrivilege",
	HandlerType: (*PrpcDbUserOrgPrivilegeServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Insert",
			Handler:    _PrpcDbUserOrgPrivilege_Insert_Handler,
		},
		{
			MethodName: "Update",
			Handler:    _PrpcDbUserOrgPrivilege_Update_Handler,
		},
		{
			MethodName: "PartialUpdate",
			Handler:    _PrpcDbUserOrgPrivilege_PartialUpdate_Handler,
		},
		{
			MethodName: "Delete",
			Handler:    _PrpcDbUserOrgPrivilege_Delete_Handler,
		},
		{
			MethodName: "SelectOne",
			Handler:    _PrpcDbUserOrgPrivilege_SelectOne_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "SelectMany",
			Handler:       _PrpcDbUserOrgPrivilege_SelectMany_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "Query",
			Handler:       _PrpcDbUserOrgPrivilege_Query_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "QueryBatch",
			Handler:       _PrpcDbUserOrgPrivilege_QueryBatch_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "userOrgPrivilege.rpc.proto",
}

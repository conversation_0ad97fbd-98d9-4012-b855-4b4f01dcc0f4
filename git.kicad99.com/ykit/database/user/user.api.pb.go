// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: user.api.proto

package user

import (
	context "context"
	fmt "fmt"
	org "git.kicad99.com/ykit/database/org"
	crud "git.kicad99.com/ykit/goutil/crud"
	rpc "git.kicad99.com/ykit/goutil/rpc"
	proto "github.com/gogo/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	io "io"
	math "math"
	math_bits "math/bits"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

type ReqUserLogin struct {
	//登录方式
	//0：username+userpass
	//1: session rid
	LoginType int32 `protobuf:"varint,1,opt,name=LoginType,proto3" json:"LoginType,omitempty"`
	//login name or session rid
	LoginName string `protobuf:"bytes,2,opt,name=LoginName,proto3" json:"LoginName,omitempty"`
	//login pass base64(sha256(base64(sha256(username+userpass))+LoginTimeStr))
	LoginPass string `protobuf:"bytes,3,opt,name=LoginPass,proto3" json:"LoginPass,omitempty"`
	//时间字符串，必须是3分钟内的时间(utc)，格式 yyyy-mm-dd HH:MM:SS
	LoginTimeStr string `protobuf:"bytes,4,opt,name=LoginTimeStr,proto3" json:"LoginTimeStr,omitempty"`
	//系统号
	System string `protobuf:"bytes,5,opt,name=System,proto3" json:"System,omitempty"`
}

func (m *ReqUserLogin) Reset()         { *m = ReqUserLogin{} }
func (m *ReqUserLogin) String() string { return proto.CompactTextString(m) }
func (*ReqUserLogin) ProtoMessage()    {}
func (*ReqUserLogin) Descriptor() ([]byte, []int) {
	return fileDescriptor_3fed89fb56fbe3ef, []int{0}
}
func (m *ReqUserLogin) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ReqUserLogin) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ReqUserLogin.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ReqUserLogin) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReqUserLogin.Merge(m, src)
}
func (m *ReqUserLogin) XXX_Size() int {
	return m.Size()
}
func (m *ReqUserLogin) XXX_DiscardUnknown() {
	xxx_messageInfo_ReqUserLogin.DiscardUnknown(m)
}

var xxx_messageInfo_ReqUserLogin proto.InternalMessageInfo

func (m *ReqUserLogin) GetLoginType() int32 {
	if m != nil {
		return m.LoginType
	}
	return 0
}

func (m *ReqUserLogin) GetLoginName() string {
	if m != nil {
		return m.LoginName
	}
	return ""
}

func (m *ReqUserLogin) GetLoginPass() string {
	if m != nil {
		return m.LoginPass
	}
	return ""
}

func (m *ReqUserLogin) GetLoginTimeStr() string {
	if m != nil {
		return m.LoginTimeStr
	}
	return ""
}

func (m *ReqUserLogin) GetSystem() string {
	if m != nil {
		return m.System
	}
	return ""
}

type ResUserLogin struct {
	//回应码
	//100:ok,其它为错误
	//1:密码错误 2: 无此sid 3:此sessionn用户已经删除 4:无此用户
	Code int32 `protobuf:"varint,1,opt,name=Code,proto3" json:"Code,omitempty"`
	//错误信息,如果有的话
	Err string `protobuf:"bytes,2,opt,name=Err,proto3" json:"Err,omitempty"`
	//user rid
	UserRID string `protobuf:"bytes,3,opt,name=UserRID,proto3" json:"UserRID,omitempty"`
	//user org rid
	UserOrgRID string `protobuf:"bytes,4,opt,name=UserOrgRID,proto3" json:"UserOrgRID,omitempty"`
	//session rid
	SessionRID string `protobuf:"bytes,5,opt,name=SessionRID,proto3" json:"SessionRID,omitempty"`
	//sys utc time yyyy-mm-dd HH:MM:SS
	SysUTCTime string `protobuf:"bytes,6,opt,name=SysUTCTime,proto3" json:"SysUTCTime,omitempty"`
}

func (m *ResUserLogin) Reset()         { *m = ResUserLogin{} }
func (m *ResUserLogin) String() string { return proto.CompactTextString(m) }
func (*ResUserLogin) ProtoMessage()    {}
func (*ResUserLogin) Descriptor() ([]byte, []int) {
	return fileDescriptor_3fed89fb56fbe3ef, []int{1}
}
func (m *ResUserLogin) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ResUserLogin) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ResUserLogin.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ResUserLogin) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ResUserLogin.Merge(m, src)
}
func (m *ResUserLogin) XXX_Size() int {
	return m.Size()
}
func (m *ResUserLogin) XXX_DiscardUnknown() {
	xxx_messageInfo_ResUserLogin.DiscardUnknown(m)
}

var xxx_messageInfo_ResUserLogin proto.InternalMessageInfo

func (m *ResUserLogin) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *ResUserLogin) GetErr() string {
	if m != nil {
		return m.Err
	}
	return ""
}

func (m *ResUserLogin) GetUserRID() string {
	if m != nil {
		return m.UserRID
	}
	return ""
}

func (m *ResUserLogin) GetUserOrgRID() string {
	if m != nil {
		return m.UserOrgRID
	}
	return ""
}

func (m *ResUserLogin) GetSessionRID() string {
	if m != nil {
		return m.SessionRID
	}
	return ""
}

func (m *ResUserLogin) GetSysUTCTime() string {
	if m != nil {
		return m.SysUTCTime
	}
	return ""
}

type ReqUserHasOrgPrivilege struct {
	//系统号
	System string `protobuf:"bytes,1,opt,name=System,proto3" json:"System,omitempty"`
	//要查询的用户RID
	UserRID string `protobuf:"bytes,2,opt,name=UserRID,proto3" json:"UserRID,omitempty"`
	//是不是对此群组拥有权限
	OrgRID string `protobuf:"bytes,3,opt,name=OrgRID,proto3" json:"OrgRID,omitempty"`
	//发起者的session id
	SessionID string `protobuf:"bytes,4,opt,name=SessionID,proto3" json:"SessionID,omitempty"`
}

func (m *ReqUserHasOrgPrivilege) Reset()         { *m = ReqUserHasOrgPrivilege{} }
func (m *ReqUserHasOrgPrivilege) String() string { return proto.CompactTextString(m) }
func (*ReqUserHasOrgPrivilege) ProtoMessage()    {}
func (*ReqUserHasOrgPrivilege) Descriptor() ([]byte, []int) {
	return fileDescriptor_3fed89fb56fbe3ef, []int{2}
}
func (m *ReqUserHasOrgPrivilege) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ReqUserHasOrgPrivilege) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ReqUserHasOrgPrivilege.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ReqUserHasOrgPrivilege) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReqUserHasOrgPrivilege.Merge(m, src)
}
func (m *ReqUserHasOrgPrivilege) XXX_Size() int {
	return m.Size()
}
func (m *ReqUserHasOrgPrivilege) XXX_DiscardUnknown() {
	xxx_messageInfo_ReqUserHasOrgPrivilege.DiscardUnknown(m)
}

var xxx_messageInfo_ReqUserHasOrgPrivilege proto.InternalMessageInfo

func (m *ReqUserHasOrgPrivilege) GetSystem() string {
	if m != nil {
		return m.System
	}
	return ""
}

func (m *ReqUserHasOrgPrivilege) GetUserRID() string {
	if m != nil {
		return m.UserRID
	}
	return ""
}

func (m *ReqUserHasOrgPrivilege) GetOrgRID() string {
	if m != nil {
		return m.OrgRID
	}
	return ""
}

func (m *ReqUserHasOrgPrivilege) GetSessionID() string {
	if m != nil {
		return m.SessionID
	}
	return ""
}

type ReqUserHasPermission struct {
	//系统号
	System string `protobuf:"bytes,1,opt,name=System,proto3" json:"System,omitempty"`
	//要查询的用户RID
	UserRID         string `protobuf:"bytes,2,opt,name=UserRID,proto3" json:"UserRID,omitempty"`
	PermissionType  string `protobuf:"bytes,3,opt,name=PermissionType,proto3" json:"PermissionType,omitempty"`
	PermissionValue string `protobuf:"bytes,4,opt,name=PermissionValue,proto3" json:"PermissionValue,omitempty"`
	//发起者的session id
	SessionID string `protobuf:"bytes,6,opt,name=SessionID,proto3" json:"SessionID,omitempty"`
}

func (m *ReqUserHasPermission) Reset()         { *m = ReqUserHasPermission{} }
func (m *ReqUserHasPermission) String() string { return proto.CompactTextString(m) }
func (*ReqUserHasPermission) ProtoMessage()    {}
func (*ReqUserHasPermission) Descriptor() ([]byte, []int) {
	return fileDescriptor_3fed89fb56fbe3ef, []int{3}
}
func (m *ReqUserHasPermission) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ReqUserHasPermission) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ReqUserHasPermission.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ReqUserHasPermission) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReqUserHasPermission.Merge(m, src)
}
func (m *ReqUserHasPermission) XXX_Size() int {
	return m.Size()
}
func (m *ReqUserHasPermission) XXX_DiscardUnknown() {
	xxx_messageInfo_ReqUserHasPermission.DiscardUnknown(m)
}

var xxx_messageInfo_ReqUserHasPermission proto.InternalMessageInfo

func (m *ReqUserHasPermission) GetSystem() string {
	if m != nil {
		return m.System
	}
	return ""
}

func (m *ReqUserHasPermission) GetUserRID() string {
	if m != nil {
		return m.UserRID
	}
	return ""
}

func (m *ReqUserHasPermission) GetPermissionType() string {
	if m != nil {
		return m.PermissionType
	}
	return ""
}

func (m *ReqUserHasPermission) GetPermissionValue() string {
	if m != nil {
		return m.PermissionValue
	}
	return ""
}

func (m *ReqUserHasPermission) GetSessionID() string {
	if m != nil {
		return m.SessionID
	}
	return ""
}

func init() {
	proto.RegisterType((*ReqUserLogin)(nil), "user.ReqUserLogin")
	proto.RegisterType((*ResUserLogin)(nil), "user.ResUserLogin")
	proto.RegisterType((*ReqUserHasOrgPrivilege)(nil), "user.ReqUserHasOrgPrivilege")
	proto.RegisterType((*ReqUserHasPermission)(nil), "user.ReqUserHasPermission")
}

func init() { proto.RegisterFile("user.api.proto", fileDescriptor_3fed89fb56fbe3ef) }

var fileDescriptor_3fed89fb56fbe3ef = []byte{
	// 567 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x9c, 0x94, 0x41, 0x8f, 0xd2, 0x40,
	0x14, 0xc7, 0x77, 0x16, 0xe8, 0xca, 0x0b, 0x01, 0x33, 0xae, 0xa4, 0x69, 0x36, 0xcd, 0xa6, 0x07,
	0xc3, 0xc1, 0x14, 0xa2, 0x5e, 0x36, 0xf1, 0xa2, 0xb0, 0x89, 0x9b, 0x2c, 0x42, 0xca, 0xe2, 0xc1,
	0x5b, 0x29, 0x93, 0x66, 0xb2, 0x94, 0xd6, 0x99, 0x62, 0xd2, 0x9b, 0x1f, 0xc1, 0x0f, 0xe1, 0x79,
	0xcf, 0x7e, 0x04, 0x8f, 0x7b, 0xf4, 0x68, 0xe0, 0x8b, 0x98, 0x99, 0x0e, 0xed, 0x50, 0x3c, 0x79,
	0x7b, 0xef, 0xff, 0x7f, 0x33, 0xf3, 0x7b, 0x8f, 0x47, 0xa1, 0xbd, 0xe1, 0x84, 0xb9, 0x7e, 0x42,
	0xdd, 0x84, 0xc5, 0x69, 0x8c, 0xeb, 0x22, 0xb7, 0x9a, 0x31, 0x0b, 0x73, 0xc1, 0x3a, 0x17, 0xc2,
	0x94, 0xb0, 0x88, 0x72, 0x4e, 0xe3, 0xb5, 0x52, 0x9b, 0x2c, 0x09, 0x54, 0x08, 0x01, 0xdb, 0x2c,
	0xf7, 0xb1, 0xbc, 0x4d, 0xc6, 0xce, 0x0f, 0x04, 0x2d, 0x8f, 0x7c, 0x99, 0x73, 0xc2, 0x6e, 0xe3,
	0x90, 0xae, 0xf1, 0x05, 0x34, 0x65, 0x70, 0x97, 0x25, 0xc4, 0x44, 0x97, 0xa8, 0xd7, 0xf0, 0x4a,
	0xa1, 0x70, 0x3f, 0xfa, 0x11, 0x31, 0x4f, 0x2f, 0x51, 0xaf, 0xe9, 0x95, 0x42, 0xe1, 0x4e, 0x7d,
	0xce, 0xcd, 0x9a, 0xe6, 0x0a, 0x01, 0x3b, 0xd0, 0xca, 0x2f, 0xa2, 0x11, 0x99, 0xa5, 0xcc, 0xac,
	0xcb, 0x82, 0x03, 0x0d, 0x77, 0xc1, 0x98, 0x65, 0x3c, 0x25, 0x91, 0xd9, 0x90, 0xae, 0xca, 0x9c,
	0x07, 0x89, 0xc9, 0x4b, 0x4c, 0x0c, 0xf5, 0x61, 0xbc, 0xdc, 0x13, 0xca, 0x18, 0x3f, 0x85, 0xda,
	0x35, 0x63, 0x0a, 0x4b, 0x84, 0xd8, 0x84, 0x33, 0x71, 0xc4, 0xbb, 0x19, 0x29, 0x9c, 0x7d, 0x8a,
	0x6d, 0x00, 0x11, 0x4e, 0x58, 0x28, 0xcc, 0x1c, 0x45, 0x53, 0x84, 0x3f, 0x23, 0x72, 0x96, 0xc2,
	0xcf, 0x61, 0x34, 0x45, 0xfa, 0x19, 0x9f, 0xdf, 0x0d, 0x05, 0xb9, 0x69, 0x28, 0xbf, 0x50, 0x9c,
	0x6f, 0x08, 0xba, 0x6a, 0xae, 0x1f, 0x7c, 0x3e, 0x61, 0xe1, 0x94, 0xd1, 0xaf, 0x74, 0x45, 0x42,
	0xa2, 0xf5, 0x88, 0xf4, 0x1e, 0x75, 0xd8, 0xd3, 0x43, 0xd8, 0x2e, 0x18, 0x0a, 0x34, 0xef, 0x42,
	0x65, 0x62, 0xde, 0x0a, 0xa9, 0xe8, 0xa1, 0x14, 0x9c, 0x9f, 0x08, 0xce, 0x4b, 0x84, 0x72, 0x39,
	0xfe, 0x03, 0xe0, 0x05, 0xb4, 0xcb, 0xf3, 0x72, 0x33, 0x72, 0x90, 0x8a, 0x8a, 0x7b, 0xd0, 0x29,
	0x95, 0x4f, 0xfe, 0x6a, 0x43, 0x14, 0x56, 0x55, 0x3e, 0x44, 0x37, 0x2a, 0xe8, 0xaf, 0x1e, 0x6a,
	0x70, 0xe6, 0x25, 0x81, 0x78, 0x1e, 0xf7, 0xa1, 0xa1, 0x7e, 0x72, 0x57, 0xee, 0xad, 0xbe, 0xad,
	0x56, 0xa1, 0x69, 0xab, 0x71, 0x0d, 0xcf, 0x6f, 0xf8, 0xbf, 0x06, 0x7f, 0x71, 0x70, 0x41, 0xc5,
	0xb5, 0xda, 0xae, 0xf8, 0xb7, 0x78, 0x49, 0x30, 0x8c, 0xa3, 0x28, 0x5e, 0xe3, 0x1e, 0x3c, 0x19,
	0x67, 0x6a, 0xd0, 0x15, 0xcf, 0x02, 0x57, 0xfc, 0xf5, 0x46, 0x8b, 0x09, 0x0b, 0x07, 0x08, 0xbf,
	0x81, 0xd6, 0x38, 0xd3, 0xe6, 0x5b, 0xad, 0x56, 0x90, 0xa3, 0x45, 0x59, 0x33, 0x40, 0xb8, 0x07,
	0xc6, 0x38, 0xf3, 0xe2, 0x15, 0x39, 0xaa, 0x6f, 0xed, 0xeb, 0x85, 0x3b, 0x40, 0xf8, 0x1d, 0x3c,
	0x2b, 0x1a, 0xd2, 0x9e, 0xb1, 0xaa, 0xed, 0x94, 0xde, 0x51, 0x33, 0x03, 0xe8, 0xcc, 0x93, 0xa5,
	0x9f, 0x92, 0x71, 0x36, 0x23, 0x69, 0x4a, 0xd7, 0x21, 0x2e, 0x5e, 0x11, 0xa7, 0xad, 0x8e, 0x2b,
	0x3f, 0x10, 0xa3, 0xf1, 0xad, 0x47, 0xf8, 0x66, 0x95, 0xe2, 0x97, 0xfa, 0x82, 0x1f, 0x21, 0x56,
	0xf2, 0xf7, 0x6f, 0x7f, 0x6d, 0x6d, 0xf4, 0xb8, 0xb5, 0xd1, 0x9f, 0xad, 0x8d, 0xbe, 0xef, 0xec,
	0x93, 0xc7, 0x9d, 0x7d, 0xf2, 0x7b, 0x67, 0x9f, 0x7c, 0x76, 0x42, 0x9a, 0xba, 0xf7, 0x34, 0xf0,
	0x97, 0x57, 0x57, 0x6e, 0x10, 0x47, 0xfd, 0xec, 0x9e, 0xa6, 0xfd, 0xa5, 0x9f, 0xfa, 0x0b, 0x9f,
	0x93, 0xbe, 0x60, 0x58, 0x18, 0xf2, 0x5b, 0xf4, 0xfa, 0x6f, 0x00, 0x00, 0x00, 0xff, 0xff, 0xcd,
	0x02, 0x43, 0x56, 0xe7, 0x04, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// RpcUserClient is the client API for RpcUser service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type RpcUserClient interface {
	//登录系统
	Login(ctx context.Context, in *ReqUserLogin, opts ...grpc.CallOption) (*ResUserLogin, error)
	//查询用户对相应的群组是否有权限
	//返回值RpcCommon.Code=100为拥有权限，其它为不拥有权限
	IsUserHasOrgPrivilege(ctx context.Context, in *ReqUserHasOrgPrivilege, opts ...grpc.CallOption) (*rpc.RpcCommon, error)
	//取得有权限的org rid列表
	//RpcCommon.Err = userrid
	MyOrgRID(ctx context.Context, in *rpc.RpcCommon, opts ...grpc.CallOption) (RpcUser_MyOrgRIDClient, error)
	//取得用户的权限列表
	//RpcCommon.Err = userrid
	MyPermission(ctx context.Context, in *rpc.RpcCommon, opts ...grpc.CallOption) (RpcUser_MyPermissionClient, error)
	//取得用户的角色列表
	//RpcCommon.Err = userrid
	MyRole(ctx context.Context, in *rpc.RpcCommon, opts ...grpc.CallOption) (RpcUser_MyRoleClient, error)
	//查询用户是否有特定的权限
	//返回值RpcCommon.Code=100为拥有权限，其它为不拥有权限
	IsUserHasPermission(ctx context.Context, in *ReqUserHasPermission, opts ...grpc.CallOption) (*rpc.RpcCommon, error)
	//修改用户自己的配置信息
	//以metadata.DMLParam.keyColumn为要修改的目标列
	UpdateMySetting(ctx context.Context, in *DbUser, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//获取系统当前utc时间
	//系统时间在RpcCommon.System=yyyy-mm-dd HH:MM:SS
	SysUTCTime(ctx context.Context, in *rpc.RpcCommon, opts ...grpc.CallOption) (*rpc.RpcCommon, error)
}

type rpcUserClient struct {
	cc *grpc.ClientConn
}

func NewRpcUserClient(cc *grpc.ClientConn) RpcUserClient {
	return &rpcUserClient{cc}
}

func (c *rpcUserClient) Login(ctx context.Context, in *ReqUserLogin, opts ...grpc.CallOption) (*ResUserLogin, error) {
	out := new(ResUserLogin)
	err := c.cc.Invoke(ctx, "/user.RpcUser/Login", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcUserClient) IsUserHasOrgPrivilege(ctx context.Context, in *ReqUserHasOrgPrivilege, opts ...grpc.CallOption) (*rpc.RpcCommon, error) {
	out := new(rpc.RpcCommon)
	err := c.cc.Invoke(ctx, "/user.RpcUser/IsUserHasOrgPrivilege", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcUserClient) MyOrgRID(ctx context.Context, in *rpc.RpcCommon, opts ...grpc.CallOption) (RpcUser_MyOrgRIDClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RpcUser_serviceDesc.Streams[0], "/user.RpcUser/MyOrgRID", opts...)
	if err != nil {
		return nil, err
	}
	x := &rpcUserMyOrgRIDClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type RpcUser_MyOrgRIDClient interface {
	Recv() (*org.DbOrg, error)
	grpc.ClientStream
}

type rpcUserMyOrgRIDClient struct {
	grpc.ClientStream
}

func (x *rpcUserMyOrgRIDClient) Recv() (*org.DbOrg, error) {
	m := new(org.DbOrg)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *rpcUserClient) MyPermission(ctx context.Context, in *rpc.RpcCommon, opts ...grpc.CallOption) (RpcUser_MyPermissionClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RpcUser_serviceDesc.Streams[1], "/user.RpcUser/MyPermission", opts...)
	if err != nil {
		return nil, err
	}
	x := &rpcUserMyPermissionClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type RpcUser_MyPermissionClient interface {
	Recv() (*DbPermission, error)
	grpc.ClientStream
}

type rpcUserMyPermissionClient struct {
	grpc.ClientStream
}

func (x *rpcUserMyPermissionClient) Recv() (*DbPermission, error) {
	m := new(DbPermission)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *rpcUserClient) MyRole(ctx context.Context, in *rpc.RpcCommon, opts ...grpc.CallOption) (RpcUser_MyRoleClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RpcUser_serviceDesc.Streams[2], "/user.RpcUser/MyRole", opts...)
	if err != nil {
		return nil, err
	}
	x := &rpcUserMyRoleClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type RpcUser_MyRoleClient interface {
	Recv() (*DbRole, error)
	grpc.ClientStream
}

type rpcUserMyRoleClient struct {
	grpc.ClientStream
}

func (x *rpcUserMyRoleClient) Recv() (*DbRole, error) {
	m := new(DbRole)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *rpcUserClient) IsUserHasPermission(ctx context.Context, in *ReqUserHasPermission, opts ...grpc.CallOption) (*rpc.RpcCommon, error) {
	out := new(rpc.RpcCommon)
	err := c.cc.Invoke(ctx, "/user.RpcUser/IsUserHasPermission", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcUserClient) UpdateMySetting(ctx context.Context, in *DbUser, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/user.RpcUser/UpdateMySetting", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcUserClient) SysUTCTime(ctx context.Context, in *rpc.RpcCommon, opts ...grpc.CallOption) (*rpc.RpcCommon, error) {
	out := new(rpc.RpcCommon)
	err := c.cc.Invoke(ctx, "/user.RpcUser/SysUTCTime", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RpcUserServer is the server API for RpcUser service.
type RpcUserServer interface {
	//登录系统
	Login(context.Context, *ReqUserLogin) (*ResUserLogin, error)
	//查询用户对相应的群组是否有权限
	//返回值RpcCommon.Code=100为拥有权限，其它为不拥有权限
	IsUserHasOrgPrivilege(context.Context, *ReqUserHasOrgPrivilege) (*rpc.RpcCommon, error)
	//取得有权限的org rid列表
	//RpcCommon.Err = userrid
	MyOrgRID(*rpc.RpcCommon, RpcUser_MyOrgRIDServer) error
	//取得用户的权限列表
	//RpcCommon.Err = userrid
	MyPermission(*rpc.RpcCommon, RpcUser_MyPermissionServer) error
	//取得用户的角色列表
	//RpcCommon.Err = userrid
	MyRole(*rpc.RpcCommon, RpcUser_MyRoleServer) error
	//查询用户是否有特定的权限
	//返回值RpcCommon.Code=100为拥有权限，其它为不拥有权限
	IsUserHasPermission(context.Context, *ReqUserHasPermission) (*rpc.RpcCommon, error)
	//修改用户自己的配置信息
	//以metadata.DMLParam.keyColumn为要修改的目标列
	UpdateMySetting(context.Context, *DbUser) (*crud.DMLResult, error)
	//获取系统当前utc时间
	//系统时间在RpcCommon.System=yyyy-mm-dd HH:MM:SS
	SysUTCTime(context.Context, *rpc.RpcCommon) (*rpc.RpcCommon, error)
}

// UnimplementedRpcUserServer can be embedded to have forward compatible implementations.
type UnimplementedRpcUserServer struct {
}

func (*UnimplementedRpcUserServer) Login(ctx context.Context, req *ReqUserLogin) (*ResUserLogin, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Login not implemented")
}
func (*UnimplementedRpcUserServer) IsUserHasOrgPrivilege(ctx context.Context, req *ReqUserHasOrgPrivilege) (*rpc.RpcCommon, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IsUserHasOrgPrivilege not implemented")
}
func (*UnimplementedRpcUserServer) MyOrgRID(req *rpc.RpcCommon, srv RpcUser_MyOrgRIDServer) error {
	return status.Errorf(codes.Unimplemented, "method MyOrgRID not implemented")
}
func (*UnimplementedRpcUserServer) MyPermission(req *rpc.RpcCommon, srv RpcUser_MyPermissionServer) error {
	return status.Errorf(codes.Unimplemented, "method MyPermission not implemented")
}
func (*UnimplementedRpcUserServer) MyRole(req *rpc.RpcCommon, srv RpcUser_MyRoleServer) error {
	return status.Errorf(codes.Unimplemented, "method MyRole not implemented")
}
func (*UnimplementedRpcUserServer) IsUserHasPermission(ctx context.Context, req *ReqUserHasPermission) (*rpc.RpcCommon, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IsUserHasPermission not implemented")
}
func (*UnimplementedRpcUserServer) UpdateMySetting(ctx context.Context, req *DbUser) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateMySetting not implemented")
}
func (*UnimplementedRpcUserServer) SysUTCTime(ctx context.Context, req *rpc.RpcCommon) (*rpc.RpcCommon, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SysUTCTime not implemented")
}

func RegisterRpcUserServer(s *grpc.Server, srv RpcUserServer) {
	s.RegisterService(&_RpcUser_serviceDesc, srv)
}

func _RpcUser_Login_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReqUserLogin)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcUserServer).Login(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.RpcUser/Login",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcUserServer).Login(ctx, req.(*ReqUserLogin))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcUser_IsUserHasOrgPrivilege_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReqUserHasOrgPrivilege)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcUserServer).IsUserHasOrgPrivilege(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.RpcUser/IsUserHasOrgPrivilege",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcUserServer).IsUserHasOrgPrivilege(ctx, req.(*ReqUserHasOrgPrivilege))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcUser_MyOrgRID_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(rpc.RpcCommon)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RpcUserServer).MyOrgRID(m, &rpcUserMyOrgRIDServer{stream})
}

type RpcUser_MyOrgRIDServer interface {
	Send(*org.DbOrg) error
	grpc.ServerStream
}

type rpcUserMyOrgRIDServer struct {
	grpc.ServerStream
}

func (x *rpcUserMyOrgRIDServer) Send(m *org.DbOrg) error {
	return x.ServerStream.SendMsg(m)
}

func _RpcUser_MyPermission_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(rpc.RpcCommon)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RpcUserServer).MyPermission(m, &rpcUserMyPermissionServer{stream})
}

type RpcUser_MyPermissionServer interface {
	Send(*DbPermission) error
	grpc.ServerStream
}

type rpcUserMyPermissionServer struct {
	grpc.ServerStream
}

func (x *rpcUserMyPermissionServer) Send(m *DbPermission) error {
	return x.ServerStream.SendMsg(m)
}

func _RpcUser_MyRole_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(rpc.RpcCommon)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RpcUserServer).MyRole(m, &rpcUserMyRoleServer{stream})
}

type RpcUser_MyRoleServer interface {
	Send(*DbRole) error
	grpc.ServerStream
}

type rpcUserMyRoleServer struct {
	grpc.ServerStream
}

func (x *rpcUserMyRoleServer) Send(m *DbRole) error {
	return x.ServerStream.SendMsg(m)
}

func _RpcUser_IsUserHasPermission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReqUserHasPermission)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcUserServer).IsUserHasPermission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.RpcUser/IsUserHasPermission",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcUserServer).IsUserHasPermission(ctx, req.(*ReqUserHasPermission))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcUser_UpdateMySetting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbUser)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcUserServer).UpdateMySetting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.RpcUser/UpdateMySetting",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcUserServer).UpdateMySetting(ctx, req.(*DbUser))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcUser_SysUTCTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(rpc.RpcCommon)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcUserServer).SysUTCTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.RpcUser/SysUTCTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcUserServer).SysUTCTime(ctx, req.(*rpc.RpcCommon))
	}
	return interceptor(ctx, in, info, handler)
}

var _RpcUser_serviceDesc = grpc.ServiceDesc{
	ServiceName: "user.RpcUser",
	HandlerType: (*RpcUserServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Login",
			Handler:    _RpcUser_Login_Handler,
		},
		{
			MethodName: "IsUserHasOrgPrivilege",
			Handler:    _RpcUser_IsUserHasOrgPrivilege_Handler,
		},
		{
			MethodName: "IsUserHasPermission",
			Handler:    _RpcUser_IsUserHasPermission_Handler,
		},
		{
			MethodName: "UpdateMySetting",
			Handler:    _RpcUser_UpdateMySetting_Handler,
		},
		{
			MethodName: "SysUTCTime",
			Handler:    _RpcUser_SysUTCTime_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "MyOrgRID",
			Handler:       _RpcUser_MyOrgRID_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "MyPermission",
			Handler:       _RpcUser_MyPermission_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "MyRole",
			Handler:       _RpcUser_MyRole_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "user.api.proto",
}

func (m *ReqUserLogin) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ReqUserLogin) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ReqUserLogin) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.System) > 0 {
		i -= len(m.System)
		copy(dAtA[i:], m.System)
		i = encodeVarintUserApi(dAtA, i, uint64(len(m.System)))
		i--
		dAtA[i] = 0x2a
	}
	if len(m.LoginTimeStr) > 0 {
		i -= len(m.LoginTimeStr)
		copy(dAtA[i:], m.LoginTimeStr)
		i = encodeVarintUserApi(dAtA, i, uint64(len(m.LoginTimeStr)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.LoginPass) > 0 {
		i -= len(m.LoginPass)
		copy(dAtA[i:], m.LoginPass)
		i = encodeVarintUserApi(dAtA, i, uint64(len(m.LoginPass)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.LoginName) > 0 {
		i -= len(m.LoginName)
		copy(dAtA[i:], m.LoginName)
		i = encodeVarintUserApi(dAtA, i, uint64(len(m.LoginName)))
		i--
		dAtA[i] = 0x12
	}
	if m.LoginType != 0 {
		i = encodeVarintUserApi(dAtA, i, uint64(m.LoginType))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *ResUserLogin) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ResUserLogin) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ResUserLogin) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.SysUTCTime) > 0 {
		i -= len(m.SysUTCTime)
		copy(dAtA[i:], m.SysUTCTime)
		i = encodeVarintUserApi(dAtA, i, uint64(len(m.SysUTCTime)))
		i--
		dAtA[i] = 0x32
	}
	if len(m.SessionRID) > 0 {
		i -= len(m.SessionRID)
		copy(dAtA[i:], m.SessionRID)
		i = encodeVarintUserApi(dAtA, i, uint64(len(m.SessionRID)))
		i--
		dAtA[i] = 0x2a
	}
	if len(m.UserOrgRID) > 0 {
		i -= len(m.UserOrgRID)
		copy(dAtA[i:], m.UserOrgRID)
		i = encodeVarintUserApi(dAtA, i, uint64(len(m.UserOrgRID)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.UserRID) > 0 {
		i -= len(m.UserRID)
		copy(dAtA[i:], m.UserRID)
		i = encodeVarintUserApi(dAtA, i, uint64(len(m.UserRID)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.Err) > 0 {
		i -= len(m.Err)
		copy(dAtA[i:], m.Err)
		i = encodeVarintUserApi(dAtA, i, uint64(len(m.Err)))
		i--
		dAtA[i] = 0x12
	}
	if m.Code != 0 {
		i = encodeVarintUserApi(dAtA, i, uint64(m.Code))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *ReqUserHasOrgPrivilege) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ReqUserHasOrgPrivilege) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ReqUserHasOrgPrivilege) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.SessionID) > 0 {
		i -= len(m.SessionID)
		copy(dAtA[i:], m.SessionID)
		i = encodeVarintUserApi(dAtA, i, uint64(len(m.SessionID)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.OrgRID) > 0 {
		i -= len(m.OrgRID)
		copy(dAtA[i:], m.OrgRID)
		i = encodeVarintUserApi(dAtA, i, uint64(len(m.OrgRID)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.UserRID) > 0 {
		i -= len(m.UserRID)
		copy(dAtA[i:], m.UserRID)
		i = encodeVarintUserApi(dAtA, i, uint64(len(m.UserRID)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.System) > 0 {
		i -= len(m.System)
		copy(dAtA[i:], m.System)
		i = encodeVarintUserApi(dAtA, i, uint64(len(m.System)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *ReqUserHasPermission) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ReqUserHasPermission) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ReqUserHasPermission) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.SessionID) > 0 {
		i -= len(m.SessionID)
		copy(dAtA[i:], m.SessionID)
		i = encodeVarintUserApi(dAtA, i, uint64(len(m.SessionID)))
		i--
		dAtA[i] = 0x32
	}
	if len(m.PermissionValue) > 0 {
		i -= len(m.PermissionValue)
		copy(dAtA[i:], m.PermissionValue)
		i = encodeVarintUserApi(dAtA, i, uint64(len(m.PermissionValue)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.PermissionType) > 0 {
		i -= len(m.PermissionType)
		copy(dAtA[i:], m.PermissionType)
		i = encodeVarintUserApi(dAtA, i, uint64(len(m.PermissionType)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.UserRID) > 0 {
		i -= len(m.UserRID)
		copy(dAtA[i:], m.UserRID)
		i = encodeVarintUserApi(dAtA, i, uint64(len(m.UserRID)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.System) > 0 {
		i -= len(m.System)
		copy(dAtA[i:], m.System)
		i = encodeVarintUserApi(dAtA, i, uint64(len(m.System)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func encodeVarintUserApi(dAtA []byte, offset int, v uint64) int {
	offset -= sovUserApi(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *ReqUserLogin) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.LoginType != 0 {
		n += 1 + sovUserApi(uint64(m.LoginType))
	}
	l = len(m.LoginName)
	if l > 0 {
		n += 1 + l + sovUserApi(uint64(l))
	}
	l = len(m.LoginPass)
	if l > 0 {
		n += 1 + l + sovUserApi(uint64(l))
	}
	l = len(m.LoginTimeStr)
	if l > 0 {
		n += 1 + l + sovUserApi(uint64(l))
	}
	l = len(m.System)
	if l > 0 {
		n += 1 + l + sovUserApi(uint64(l))
	}
	return n
}

func (m *ResUserLogin) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Code != 0 {
		n += 1 + sovUserApi(uint64(m.Code))
	}
	l = len(m.Err)
	if l > 0 {
		n += 1 + l + sovUserApi(uint64(l))
	}
	l = len(m.UserRID)
	if l > 0 {
		n += 1 + l + sovUserApi(uint64(l))
	}
	l = len(m.UserOrgRID)
	if l > 0 {
		n += 1 + l + sovUserApi(uint64(l))
	}
	l = len(m.SessionRID)
	if l > 0 {
		n += 1 + l + sovUserApi(uint64(l))
	}
	l = len(m.SysUTCTime)
	if l > 0 {
		n += 1 + l + sovUserApi(uint64(l))
	}
	return n
}

func (m *ReqUserHasOrgPrivilege) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.System)
	if l > 0 {
		n += 1 + l + sovUserApi(uint64(l))
	}
	l = len(m.UserRID)
	if l > 0 {
		n += 1 + l + sovUserApi(uint64(l))
	}
	l = len(m.OrgRID)
	if l > 0 {
		n += 1 + l + sovUserApi(uint64(l))
	}
	l = len(m.SessionID)
	if l > 0 {
		n += 1 + l + sovUserApi(uint64(l))
	}
	return n
}

func (m *ReqUserHasPermission) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.System)
	if l > 0 {
		n += 1 + l + sovUserApi(uint64(l))
	}
	l = len(m.UserRID)
	if l > 0 {
		n += 1 + l + sovUserApi(uint64(l))
	}
	l = len(m.PermissionType)
	if l > 0 {
		n += 1 + l + sovUserApi(uint64(l))
	}
	l = len(m.PermissionValue)
	if l > 0 {
		n += 1 + l + sovUserApi(uint64(l))
	}
	l = len(m.SessionID)
	if l > 0 {
		n += 1 + l + sovUserApi(uint64(l))
	}
	return n
}

func sovUserApi(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozUserApi(x uint64) (n int) {
	return sovUserApi(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *ReqUserLogin) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserApi
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ReqUserLogin: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ReqUserLogin: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field LoginType", wireType)
			}
			m.LoginType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LoginType |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LoginName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserApi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthUserApi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.LoginName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LoginPass", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserApi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthUserApi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.LoginPass = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LoginTimeStr", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserApi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthUserApi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.LoginTimeStr = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field System", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserApi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthUserApi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.System = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUserApi(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserApi
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthUserApi
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ResUserLogin) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserApi
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ResUserLogin: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ResUserLogin: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Code", wireType)
			}
			m.Code = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Code |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Err", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserApi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthUserApi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Err = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UserRID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserApi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthUserApi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UserRID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UserOrgRID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserApi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthUserApi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UserOrgRID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SessionRID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserApi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthUserApi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SessionRID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SysUTCTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserApi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthUserApi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SysUTCTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUserApi(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserApi
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthUserApi
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ReqUserHasOrgPrivilege) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserApi
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ReqUserHasOrgPrivilege: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ReqUserHasOrgPrivilege: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field System", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserApi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthUserApi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.System = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UserRID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserApi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthUserApi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UserRID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OrgRID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserApi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthUserApi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OrgRID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SessionID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserApi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthUserApi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SessionID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUserApi(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserApi
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthUserApi
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ReqUserHasPermission) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserApi
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ReqUserHasPermission: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ReqUserHasPermission: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field System", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserApi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthUserApi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.System = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UserRID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserApi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthUserApi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UserRID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PermissionType", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserApi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthUserApi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.PermissionType = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PermissionValue", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserApi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthUserApi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.PermissionValue = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SessionID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserApi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthUserApi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SessionID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUserApi(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserApi
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthUserApi
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipUserApi(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowUserApi
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowUserApi
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowUserApi
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthUserApi
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupUserApi
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthUserApi
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthUserApi        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowUserApi          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupUserApi = fmt.Errorf("proto: unexpected end of group")
)

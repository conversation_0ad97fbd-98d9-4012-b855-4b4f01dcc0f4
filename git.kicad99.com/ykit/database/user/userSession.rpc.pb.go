// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: userSession.rpc.proto

package user

import (
	context "context"
	fmt "fmt"
	crud "git.kicad99.com/ykit/goutil/crud"
	proto "github.com/gogo/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

func init() { proto.RegisterFile("userSession.rpc.proto", fileDescriptor_ff1ae1a4b339c44b) }

var fileDescriptor_ff1ae1a4b339c44b = []byte{
	// 292 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x94, 0x91, 0xc1, 0x4a, 0xf3, 0x40,
	0x14, 0x46, 0x1b, 0xfe, 0xdf, 0x82, 0x17, 0xd4, 0x3a, 0xa2, 0x42, 0x16, 0xb3, 0xf0, 0x01, 0xa6,
	0xc1, 0xe2, 0x22, 0xe0, 0xaa, 0x64, 0x23, 0xb4, 0x58, 0x5b, 0xba, 0x71, 0x37, 0x99, 0x5c, 0x74,
	0x68, 0x9a, 0x84, 0x99, 0x9b, 0x45, 0xde, 0xc2, 0x17, 0xf1, 0x3d, 0x5c, 0x76, 0xe9, 0x52, 0x92,
	0x17, 0x91, 0x24, 0xa0, 0x15, 0x82, 0xc4, 0x65, 0xce, 0x77, 0xce, 0xcd, 0x62, 0xe0, 0x3c, 0xb7,
	0x68, 0x56, 0x68, 0xad, 0x4e, 0x13, 0x61, 0x32, 0x25, 0x32, 0x93, 0x52, 0xca, 0xfe, 0xd7, 0xd8,
	0x05, 0x65, 0xf2, 0xa8, 0x25, 0xee, 0xe9, 0xbe, 0xd8, 0xa2, 0x8b, 0x7d, 0x14, 0x6b, 0x4b, 0x2d,
	0xbf, 0x7e, 0xfd, 0x07, 0xa3, 0x65, 0xa6, 0x82, 0x70, 0xfd, 0xbd, 0x33, 0x01, 0xc3, 0xbb, 0xc4,
	0xa2, 0x21, 0x76, 0x26, 0xea, 0x4e, 0xfc, 0x98, 0xdd, 0x13, 0xd1, 0xfc, 0x2b, 0x98, 0xcf, 0x96,
	0x68, 0xf3, 0x98, 0x6a, 0x7f, 0x9d, 0x45, 0x92, 0xb0, 0xa7, 0x7f, 0x03, 0x47, 0x0b, 0x69, 0x48,
	0xcb, 0xf8, 0x4f, 0x99, 0x80, 0x61, 0x80, 0x31, 0xf6, 0xf6, 0x3d, 0x38, 0x5c, 0x61, 0x8c, 0x8a,
	0xee, 0x13, 0x64, 0xc7, 0x5f, 0xeb, 0x42, 0x1a, 0xb9, 0x75, 0xbb, 0x4e, 0xb0, 0x09, 0x40, 0x5b,
	0xcc, 0x65, 0x52, 0xf4, 0x4a, 0x3c, 0x87, 0x79, 0x70, 0xf0, 0x90, 0xa3, 0x29, 0xd8, 0xa8, 0xf5,
	0x9b, 0x8f, 0x5f, 0x0b, 0x1f, 0xa0, 0x91, 0xa6, 0x92, 0xd4, 0x73, 0x47, 0x76, 0xd9, 0x91, 0xcd,
	0xb4, 0x25, 0xcf, 0x99, 0xde, 0xbe, 0x95, 0xdc, 0xd9, 0x95, 0xdc, 0xf9, 0x28, 0xb9, 0xf3, 0x52,
	0xf1, 0xc1, 0xae, 0xe2, 0x83, 0xf7, 0x8a, 0x0f, 0x1e, 0xaf, 0x9e, 0x34, 0x89, 0x8d, 0x56, 0x32,
	0xf2, 0x7d, 0xa1, 0xd2, 0xed, 0xb8, 0xd8, 0x68, 0x1a, 0x47, 0x92, 0x64, 0x28, 0x2d, 0x8e, 0xeb,
	0x8b, 0xe1, 0xb0, 0x79, 0xf4, 0xc9, 0x67, 0x00, 0x00, 0x00, 0xff, 0xff, 0x50, 0x47, 0x8f, 0x9e,
	0x4a, 0x02, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// RpcDbUserSessionClient is the client API for RpcDbUserSession service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type RpcDbUserSessionClient interface {
	//插入一行数据
	Insert(ctx context.Context, in *DbUserSession, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,全量修改一行数据
	Update(ctx context.Context, in *DbUserSession, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
	PartialUpdate(ctx context.Context, in *DbUserSession, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,删除一行数据
	Delete(ctx context.Context, in *DbUserSession, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
	SelectOne(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (*DbUserSession, error)
	//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
	SelectMany(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (RpcDbUserSession_SelectManyClient, error)
	//根据条件查询数据
	Query(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbUserSession_QueryClient, error)
	//根据条件查询数据,批量返回数据,crud.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbUserSession_QueryBatchClient, error)
}

type rpcDbUserSessionClient struct {
	cc *grpc.ClientConn
}

func NewRpcDbUserSessionClient(cc *grpc.ClientConn) RpcDbUserSessionClient {
	return &rpcDbUserSessionClient{cc}
}

func (c *rpcDbUserSessionClient) Insert(ctx context.Context, in *DbUserSession, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/user.RpcDbUserSession/Insert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbUserSessionClient) Update(ctx context.Context, in *DbUserSession, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/user.RpcDbUserSession/Update", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbUserSessionClient) PartialUpdate(ctx context.Context, in *DbUserSession, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/user.RpcDbUserSession/PartialUpdate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbUserSessionClient) Delete(ctx context.Context, in *DbUserSession, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/user.RpcDbUserSession/Delete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbUserSessionClient) SelectOne(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (*DbUserSession, error) {
	out := new(DbUserSession)
	err := c.cc.Invoke(ctx, "/user.RpcDbUserSession/SelectOne", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbUserSessionClient) SelectMany(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (RpcDbUserSession_SelectManyClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RpcDbUserSession_serviceDesc.Streams[0], "/user.RpcDbUserSession/SelectMany", opts...)
	if err != nil {
		return nil, err
	}
	x := &rpcDbUserSessionSelectManyClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type RpcDbUserSession_SelectManyClient interface {
	Recv() (*DbUserSession, error)
	grpc.ClientStream
}

type rpcDbUserSessionSelectManyClient struct {
	grpc.ClientStream
}

func (x *rpcDbUserSessionSelectManyClient) Recv() (*DbUserSession, error) {
	m := new(DbUserSession)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *rpcDbUserSessionClient) Query(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbUserSession_QueryClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RpcDbUserSession_serviceDesc.Streams[1], "/user.RpcDbUserSession/Query", opts...)
	if err != nil {
		return nil, err
	}
	x := &rpcDbUserSessionQueryClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type RpcDbUserSession_QueryClient interface {
	Recv() (*DbUserSession, error)
	grpc.ClientStream
}

type rpcDbUserSessionQueryClient struct {
	grpc.ClientStream
}

func (x *rpcDbUserSessionQueryClient) Recv() (*DbUserSession, error) {
	m := new(DbUserSession)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *rpcDbUserSessionClient) QueryBatch(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbUserSession_QueryBatchClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RpcDbUserSession_serviceDesc.Streams[2], "/user.RpcDbUserSession/QueryBatch", opts...)
	if err != nil {
		return nil, err
	}
	x := &rpcDbUserSessionQueryBatchClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type RpcDbUserSession_QueryBatchClient interface {
	Recv() (*DbUserSessionList, error)
	grpc.ClientStream
}

type rpcDbUserSessionQueryBatchClient struct {
	grpc.ClientStream
}

func (x *rpcDbUserSessionQueryBatchClient) Recv() (*DbUserSessionList, error) {
	m := new(DbUserSessionList)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// RpcDbUserSessionServer is the server API for RpcDbUserSession service.
type RpcDbUserSessionServer interface {
	//插入一行数据
	Insert(context.Context, *DbUserSession) (*crud.DMLResult, error)
	//以RID为条件,全量修改一行数据
	Update(context.Context, *DbUserSession) (*crud.DMLResult, error)
	//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
	PartialUpdate(context.Context, *DbUserSession) (*crud.DMLResult, error)
	//以RID为条件,删除一行数据
	Delete(context.Context, *DbUserSession) (*crud.DMLResult, error)
	//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
	SelectOne(context.Context, *crud.DMLParam) (*DbUserSession, error)
	//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
	SelectMany(*crud.DMLParam, RpcDbUserSession_SelectManyServer) error
	//根据条件查询数据
	Query(*crud.QueryParam, RpcDbUserSession_QueryServer) error
	//根据条件查询数据,批量返回数据,crud.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(*crud.QueryParam, RpcDbUserSession_QueryBatchServer) error
}

// UnimplementedRpcDbUserSessionServer can be embedded to have forward compatible implementations.
type UnimplementedRpcDbUserSessionServer struct {
}

func (*UnimplementedRpcDbUserSessionServer) Insert(ctx context.Context, req *DbUserSession) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Insert not implemented")
}
func (*UnimplementedRpcDbUserSessionServer) Update(ctx context.Context, req *DbUserSession) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Update not implemented")
}
func (*UnimplementedRpcDbUserSessionServer) PartialUpdate(ctx context.Context, req *DbUserSession) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PartialUpdate not implemented")
}
func (*UnimplementedRpcDbUserSessionServer) Delete(ctx context.Context, req *DbUserSession) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Delete not implemented")
}
func (*UnimplementedRpcDbUserSessionServer) SelectOne(ctx context.Context, req *crud.DMLParam) (*DbUserSession, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SelectOne not implemented")
}
func (*UnimplementedRpcDbUserSessionServer) SelectMany(req *crud.DMLParam, srv RpcDbUserSession_SelectManyServer) error {
	return status.Errorf(codes.Unimplemented, "method SelectMany not implemented")
}
func (*UnimplementedRpcDbUserSessionServer) Query(req *crud.QueryParam, srv RpcDbUserSession_QueryServer) error {
	return status.Errorf(codes.Unimplemented, "method Query not implemented")
}
func (*UnimplementedRpcDbUserSessionServer) QueryBatch(req *crud.QueryParam, srv RpcDbUserSession_QueryBatchServer) error {
	return status.Errorf(codes.Unimplemented, "method QueryBatch not implemented")
}

func RegisterRpcDbUserSessionServer(s *grpc.Server, srv RpcDbUserSessionServer) {
	s.RegisterService(&_RpcDbUserSession_serviceDesc, srv)
}

func _RpcDbUserSession_Insert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbUserSession)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbUserSessionServer).Insert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.RpcDbUserSession/Insert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbUserSessionServer).Insert(ctx, req.(*DbUserSession))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbUserSession_Update_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbUserSession)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbUserSessionServer).Update(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.RpcDbUserSession/Update",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbUserSessionServer).Update(ctx, req.(*DbUserSession))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbUserSession_PartialUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbUserSession)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbUserSessionServer).PartialUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.RpcDbUserSession/PartialUpdate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbUserSessionServer).PartialUpdate(ctx, req.(*DbUserSession))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbUserSession_Delete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbUserSession)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbUserSessionServer).Delete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.RpcDbUserSession/Delete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbUserSessionServer).Delete(ctx, req.(*DbUserSession))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbUserSession_SelectOne_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(crud.DMLParam)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbUserSessionServer).SelectOne(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.RpcDbUserSession/SelectOne",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbUserSessionServer).SelectOne(ctx, req.(*crud.DMLParam))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbUserSession_SelectMany_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.DMLParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RpcDbUserSessionServer).SelectMany(m, &rpcDbUserSessionSelectManyServer{stream})
}

type RpcDbUserSession_SelectManyServer interface {
	Send(*DbUserSession) error
	grpc.ServerStream
}

type rpcDbUserSessionSelectManyServer struct {
	grpc.ServerStream
}

func (x *rpcDbUserSessionSelectManyServer) Send(m *DbUserSession) error {
	return x.ServerStream.SendMsg(m)
}

func _RpcDbUserSession_Query_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.QueryParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RpcDbUserSessionServer).Query(m, &rpcDbUserSessionQueryServer{stream})
}

type RpcDbUserSession_QueryServer interface {
	Send(*DbUserSession) error
	grpc.ServerStream
}

type rpcDbUserSessionQueryServer struct {
	grpc.ServerStream
}

func (x *rpcDbUserSessionQueryServer) Send(m *DbUserSession) error {
	return x.ServerStream.SendMsg(m)
}

func _RpcDbUserSession_QueryBatch_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.QueryParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RpcDbUserSessionServer).QueryBatch(m, &rpcDbUserSessionQueryBatchServer{stream})
}

type RpcDbUserSession_QueryBatchServer interface {
	Send(*DbUserSessionList) error
	grpc.ServerStream
}

type rpcDbUserSessionQueryBatchServer struct {
	grpc.ServerStream
}

func (x *rpcDbUserSessionQueryBatchServer) Send(m *DbUserSessionList) error {
	return x.ServerStream.SendMsg(m)
}

var _RpcDbUserSession_serviceDesc = grpc.ServiceDesc{
	ServiceName: "user.RpcDbUserSession",
	HandlerType: (*RpcDbUserSessionServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Insert",
			Handler:    _RpcDbUserSession_Insert_Handler,
		},
		{
			MethodName: "Update",
			Handler:    _RpcDbUserSession_Update_Handler,
		},
		{
			MethodName: "PartialUpdate",
			Handler:    _RpcDbUserSession_PartialUpdate_Handler,
		},
		{
			MethodName: "Delete",
			Handler:    _RpcDbUserSession_Delete_Handler,
		},
		{
			MethodName: "SelectOne",
			Handler:    _RpcDbUserSession_SelectOne_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "SelectMany",
			Handler:       _RpcDbUserSession_SelectMany_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "Query",
			Handler:       _RpcDbUserSession_Query_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "QueryBatch",
			Handler:       _RpcDbUserSession_QueryBatch_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "userSession.rpc.proto",
}

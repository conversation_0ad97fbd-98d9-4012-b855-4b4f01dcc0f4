// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: userSession.list.proto

package user

import (
	fmt "fmt"
	proto "github.com/gogo/protobuf/proto"
	io "io"
	math "math"
	math_bits "math/bits"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

// DbUserSession list
type DbUserSessionList struct {
	//批次号，从0开始
	BatchNo uint32 `protobuf:"varint,1,opt,name=BatchNo,proto3" json:"BatchNo,omitempty" ykit:"nodb"`
	//rows offset,从0开始
	RowsOffset uint32 `protobuf:"varint,2,opt,name=RowsOffset,proto3" json:"RowsOffset,omitempty" ykit:"nodb"`
	//本批次数据
	Rows []*DbUserSession `protobuf:"bytes,3,rep,name=Rows,proto3" json:"Rows,omitempty" ykit:"nodb"`
}

func (m *DbUserSessionList) Reset()         { *m = DbUserSessionList{} }
func (m *DbUserSessionList) String() string { return proto.CompactTextString(m) }
func (*DbUserSessionList) ProtoMessage()    {}
func (*DbUserSessionList) Descriptor() ([]byte, []int) {
	return fileDescriptor_c64a83bcdcd179c7, []int{0}
}
func (m *DbUserSessionList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbUserSessionList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbUserSessionList.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbUserSessionList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbUserSessionList.Merge(m, src)
}
func (m *DbUserSessionList) XXX_Size() int {
	return m.Size()
}
func (m *DbUserSessionList) XXX_DiscardUnknown() {
	xxx_messageInfo_DbUserSessionList.DiscardUnknown(m)
}

var xxx_messageInfo_DbUserSessionList proto.InternalMessageInfo

func (m *DbUserSessionList) GetBatchNo() uint32 {
	if m != nil {
		return m.BatchNo
	}
	return 0
}

func (m *DbUserSessionList) GetRowsOffset() uint32 {
	if m != nil {
		return m.RowsOffset
	}
	return 0
}

func (m *DbUserSessionList) GetRows() []*DbUserSession {
	if m != nil {
		return m.Rows
	}
	return nil
}

func init() {
	proto.RegisterType((*DbUserSessionList)(nil), "user.DbUserSessionList")
}

func init() { proto.RegisterFile("userSession.list.proto", fileDescriptor_c64a83bcdcd179c7) }

var fileDescriptor_c64a83bcdcd179c7 = []byte{
	// 200 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe2, 0x12, 0x2b, 0x2d, 0x4e, 0x2d,
	0x0a, 0x4e, 0x2d, 0x2e, 0xce, 0xcc, 0xcf, 0xd3, 0xcb, 0xc9, 0x2c, 0x2e, 0xd1, 0x2b, 0x28, 0xca,
	0x2f, 0xc9, 0x17, 0x62, 0x01, 0x89, 0x4b, 0x09, 0x22, 0xcb, 0x82, 0x25, 0x94, 0xca, 0xb8, 0x04,
	0x5d, 0x92, 0x42, 0x11, 0xc2, 0x3e, 0x99, 0xc5, 0x25, 0x42, 0x12, 0x5c, 0xec, 0x4e, 0x89, 0x25,
	0xc9, 0x19, 0x7e, 0xf9, 0x12, 0x8c, 0x0a, 0x8c, 0x1a, 0xbc, 0x41, 0x30, 0xae, 0x90, 0x1c, 0x17,
	0x57, 0x50, 0x7e, 0x79, 0xb1, 0x7f, 0x5a, 0x5a, 0x71, 0x6a, 0x89, 0x04, 0x13, 0x58, 0x12, 0x49,
	0x44, 0x48, 0x9d, 0x8b, 0x05, 0xc4, 0x93, 0x60, 0x56, 0x60, 0xd6, 0xe0, 0x36, 0x12, 0xd6, 0x03,
	0x59, 0xa8, 0x87, 0x62, 0x41, 0x10, 0x58, 0x81, 0x93, 0xcd, 0x89, 0x47, 0x72, 0x8c, 0x17, 0x1e,
	0xc9, 0x31, 0x3e, 0x78, 0x24, 0xc7, 0x38, 0xe1, 0xb1, 0x1c, 0xc3, 0x85, 0xc7, 0x72, 0x0c, 0x37,
	0x1e, 0xcb, 0x31, 0x44, 0x29, 0xa5, 0x67, 0x96, 0xe8, 0x65, 0x67, 0x26, 0x27, 0xa6, 0x58, 0x5a,
	0xea, 0x25, 0xe7, 0xe7, 0xea, 0x57, 0x66, 0x67, 0x96, 0xe8, 0xa7, 0x24, 0x96, 0x24, 0x26, 0x25,
	0x16, 0xa7, 0xea, 0x83, 0x4c, 0x4c, 0x62, 0x03, 0x3b, 0xde, 0x18, 0x10, 0x00, 0x00, 0xff, 0xff,
	0x57, 0xa3, 0x7f, 0x55, 0xef, 0x00, 0x00, 0x00,
}

func (m *DbUserSessionList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbUserSessionList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbUserSessionList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for iNdEx := len(m.Rows) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Rows[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintUserSessionList(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x1a
		}
	}
	if m.RowsOffset != 0 {
		i = encodeVarintUserSessionList(dAtA, i, uint64(m.RowsOffset))
		i--
		dAtA[i] = 0x10
	}
	if m.BatchNo != 0 {
		i = encodeVarintUserSessionList(dAtA, i, uint64(m.BatchNo))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func encodeVarintUserSessionList(dAtA []byte, offset int, v uint64) int {
	offset -= sovUserSessionList(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *DbUserSessionList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.BatchNo != 0 {
		n += 1 + sovUserSessionList(uint64(m.BatchNo))
	}
	if m.RowsOffset != 0 {
		n += 1 + sovUserSessionList(uint64(m.RowsOffset))
	}
	if len(m.Rows) > 0 {
		for _, e := range m.Rows {
			l = e.Size()
			n += 1 + l + sovUserSessionList(uint64(l))
		}
	}
	return n
}

func sovUserSessionList(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozUserSessionList(x uint64) (n int) {
	return sovUserSessionList(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *DbUserSessionList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserSessionList
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DbUserSessionList: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DbUserSessionList: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field BatchNo", wireType)
			}
			m.BatchNo = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserSessionList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BatchNo |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RowsOffset", wireType)
			}
			m.RowsOffset = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserSessionList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RowsOffset |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rows", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserSessionList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserSessionList
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthUserSessionList
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rows = append(m.Rows, &DbUserSession{})
			if err := m.Rows[len(m.Rows)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUserSessionList(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserSessionList
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthUserSessionList
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipUserSessionList(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowUserSessionList
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowUserSessionList
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowUserSessionList
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthUserSessionList
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupUserSessionList
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthUserSessionList
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthUserSessionList        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowUserSessionList          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupUserSessionList = fmt.Errorf("proto: unexpected end of group")
)

// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: userPermission.rpc.proto

package user

import (
	context "context"
	fmt "fmt"
	crud "git.kicad99.com/ykit/goutil/crud"
	proto "github.com/gogo/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

func init() { proto.RegisterFile("userPermission.rpc.proto", fileDescriptor_617da9db7f3bdb80) }

var fileDescriptor_617da9db7f3bdb80 = []byte{
	// 485 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xec, 0x96, 0x3f, 0x6f, 0xd3, 0x40,
	0x18, 0xc6, 0xe3, 0x06, 0x22, 0xf5, 0x15, 0xd0, 0xea, 0x88, 0x2a, 0xf0, 0xe0, 0x81, 0xa5, 0xaa,
	0x44, 0xce, 0xae, 0x83, 0xa0, 0x95, 0x3a, 0xa0, 0x2a, 0x0b, 0x52, 0x2b, 0x4c, 0x10, 0x0b, 0xdb,
	0xc5, 0x3e, 0x95, 0x53, 0x1d, 0xdb, 0xba, 0x3b, 0x23, 0xe5, 0x5b, 0xf0, 0x21, 0xf8, 0x30, 0x1d,
	0xcb, 0xc6, 0x88, 0x92, 0x9d, 0x95, 0x15, 0xf9, 0x4f, 0x5d, 0xc7, 0xb5, 0xcf, 0xae, 0xd4, 0x31,
	0x63, 0xee, 0x7d, 0x7e, 0x77, 0xd2, 0xf3, 0xcb, 0x1b, 0x05, 0x5e, 0xc4, 0x82, 0x72, 0x87, 0xf2,
	0x39, 0x13, 0x82, 0x85, 0x01, 0xe6, 0x91, 0x8b, 0x23, 0x1e, 0xca, 0x10, 0x3d, 0x4a, 0x26, 0x3a,
	0xb8, 0x3c, 0xf6, 0xb2, 0x13, 0x7d, 0x58, 0xc9, 0x66, 0xa7, 0x2f, 0x2b, 0xa7, 0x3e, 0x13, 0x32,
	0x1b, 0xd9, 0x3f, 0xfb, 0xb0, 0x33, 0x8d, 0xdc, 0xc9, 0xec, 0x76, 0x8c, 0x46, 0x30, 0xf8, 0x10,
	0x08, 0xca, 0x25, 0x42, 0x38, 0x21, 0x71, 0x79, 0xaa, 0xef, 0xe0, 0xf4, 0xbd, 0xc9, 0xf9, 0xd9,
	0x94, 0x8a, 0xd8, 0x97, 0x49, 0xfc, 0x4b, 0xe4, 0x11, 0x49, 0xbb, 0xc5, 0xdf, 0xc0, 0x53, 0x87,
	0x70, 0xc9, 0x88, 0x7f, 0x1f, 0x6a, 0x04, 0x83, 0x09, 0xf5, 0x69, 0xd7, 0xb8, 0x09, 0xdb, 0x9f,
	0xa9, 0x4f, 0x5d, 0xf9, 0x31, 0xa0, 0xe8, 0x59, 0x31, 0x75, 0x08, 0x27, 0x73, 0xbd, 0xe6, 0x06,
	0x64, 0x03, 0x64, 0xc0, 0x39, 0x09, 0x16, 0x5d, 0x08, 0x4b, 0x43, 0x26, 0x3c, 0xfe, 0x14, 0x53,
	0xbe, 0x40, 0xbb, 0x59, 0x3c, 0xfd, 0xa0, 0x02, 0x8e, 0x00, 0xd2, 0xcc, 0x29, 0x91, 0xee, 0xb7,
	0x1a, 0x6a, 0xef, 0x2e, 0x75, 0xc6, 0x84, 0xb4, 0x34, 0xfb, 0xef, 0x16, 0x6c, 0xa7, 0x9a, 0xa6,
	0xa1, 0x4f, 0xd1, 0x7e, 0x21, 0xe8, 0xc9, 0x0d, 0x91, 0x9c, 0xdf, 0xad, 0x61, 0xbf, 0x50, 0xd3,
	0x12, 0xc4, 0x55, 0x29, 0xed, 0x17, 0xe7, 0x3a, 0x5a, 0x82, 0x07, 0x2a, 0x11, 0x6b, 0x2c, 0x7a,
	0xad, 0x54, 0xb0, 0x96, 0xb5, 0x34, 0x74, 0xd0, 0x5c, 0x7e, 0x35, 0x6a, 0xb7, 0xd4, 0xbe, 0x5b,
	0xce, 0xe7, 0x85, 0xff, 0xdb, 0x02, 0x70, 0xf8, 0xa6, 0xf1, 0x52, 0x8d, 0xa3, 0x9b, 0xc6, 0x87,
	0x59, 0xd0, 0xe1, 0xec, 0x3b, 0xf3, 0xe9, 0x05, 0xad, 0x8f, 0xbf, 0x5d, 0x6b, 0xbd, 0x9e, 0xa9,
	0x6b, 0xfe, 0xaa, 0x0f, 0xcf, 0x8b, 0xaf, 0x7a, 0x69, 0x43, 0x0f, 0x0b, 0x05, 0x7b, 0x65, 0x4a,
	0xf5, 0x2b, 0x70, 0x58, 0xc8, 0xe8, 0x8c, 0x1c, 0x55, 0xb5, 0xdc, 0xe7, 0xb1, 0x5c, 0x50, 0x67,
	0x64, 0xac, 0x52, 0xd5, 0x70, 0x4b, 0xd2, 0xab, 0x42, 0x5a, 0x03, 0x65, 0x69, 0x68, 0xdc, 0xbc,
	0x30, 0xcd, 0xd0, 0x49, 0xcb, 0xea, 0xe8, 0xf5, 0x64, 0xae, 0xf2, 0x57, 0x1f, 0x86, 0xb7, 0x4b,
	0xb4, 0x71, 0xf9, 0x60, 0x2e, 0xdf, 0xa9, 0x57, 0xb1, 0x19, 0x7c, 0xdf, 0x61, 0x29, 0x95, 0x4e,
	0x4f, 0x4f, 0xae, 0x96, 0x86, 0x76, 0xbd, 0x34, 0xb4, 0x3f, 0x4b, 0x43, 0xfb, 0xb1, 0x32, 0x7a,
	0xd7, 0x2b, 0xa3, 0xf7, 0x7b, 0x65, 0xf4, 0xbe, 0xbe, 0xba, 0x60, 0x12, 0x5f, 0x32, 0x97, 0x78,
	0xc7, 0xc7, 0xd8, 0x0d, 0xe7, 0xe6, 0xe2, 0x92, 0x49, 0xd3, 0x23, 0x92, 0xcc, 0x88, 0xa0, 0x66,
	0x72, 0xe9, 0x6c, 0x90, 0xfe, 0xeb, 0x18, 0xff, 0x0f, 0x00, 0x00, 0xff, 0xff, 0xbf, 0x81, 0x46,
	0x63, 0xd4, 0x08, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// RpcDbPermissionClient is the client API for RpcDbPermission service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type RpcDbPermissionClient interface {
	//插入一行数据
	Insert(ctx context.Context, in *DbPermission, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,全量修改一行数据
	Update(ctx context.Context, in *DbPermission, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
	PartialUpdate(ctx context.Context, in *DbPermission, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,删除一行数据
	Delete(ctx context.Context, in *DbPermission, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
	SelectOne(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (*DbPermission, error)
	//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
	SelectMany(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (RpcDbPermission_SelectManyClient, error)
	//根据条件查询数据
	Query(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbPermission_QueryClient, error)
	//根据条件查询数据,批量返回数据,crud.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbPermission_QueryBatchClient, error)
}

type rpcDbPermissionClient struct {
	cc *grpc.ClientConn
}

func NewRpcDbPermissionClient(cc *grpc.ClientConn) RpcDbPermissionClient {
	return &rpcDbPermissionClient{cc}
}

func (c *rpcDbPermissionClient) Insert(ctx context.Context, in *DbPermission, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/user.RpcDbPermission/Insert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbPermissionClient) Update(ctx context.Context, in *DbPermission, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/user.RpcDbPermission/Update", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbPermissionClient) PartialUpdate(ctx context.Context, in *DbPermission, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/user.RpcDbPermission/PartialUpdate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbPermissionClient) Delete(ctx context.Context, in *DbPermission, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/user.RpcDbPermission/Delete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbPermissionClient) SelectOne(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (*DbPermission, error) {
	out := new(DbPermission)
	err := c.cc.Invoke(ctx, "/user.RpcDbPermission/SelectOne", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbPermissionClient) SelectMany(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (RpcDbPermission_SelectManyClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RpcDbPermission_serviceDesc.Streams[0], "/user.RpcDbPermission/SelectMany", opts...)
	if err != nil {
		return nil, err
	}
	x := &rpcDbPermissionSelectManyClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type RpcDbPermission_SelectManyClient interface {
	Recv() (*DbPermission, error)
	grpc.ClientStream
}

type rpcDbPermissionSelectManyClient struct {
	grpc.ClientStream
}

func (x *rpcDbPermissionSelectManyClient) Recv() (*DbPermission, error) {
	m := new(DbPermission)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *rpcDbPermissionClient) Query(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbPermission_QueryClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RpcDbPermission_serviceDesc.Streams[1], "/user.RpcDbPermission/Query", opts...)
	if err != nil {
		return nil, err
	}
	x := &rpcDbPermissionQueryClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type RpcDbPermission_QueryClient interface {
	Recv() (*DbPermission, error)
	grpc.ClientStream
}

type rpcDbPermissionQueryClient struct {
	grpc.ClientStream
}

func (x *rpcDbPermissionQueryClient) Recv() (*DbPermission, error) {
	m := new(DbPermission)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *rpcDbPermissionClient) QueryBatch(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbPermission_QueryBatchClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RpcDbPermission_serviceDesc.Streams[2], "/user.RpcDbPermission/QueryBatch", opts...)
	if err != nil {
		return nil, err
	}
	x := &rpcDbPermissionQueryBatchClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type RpcDbPermission_QueryBatchClient interface {
	Recv() (*DbPermissionList, error)
	grpc.ClientStream
}

type rpcDbPermissionQueryBatchClient struct {
	grpc.ClientStream
}

func (x *rpcDbPermissionQueryBatchClient) Recv() (*DbPermissionList, error) {
	m := new(DbPermissionList)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// RpcDbPermissionServer is the server API for RpcDbPermission service.
type RpcDbPermissionServer interface {
	//插入一行数据
	Insert(context.Context, *DbPermission) (*crud.DMLResult, error)
	//以RID为条件,全量修改一行数据
	Update(context.Context, *DbPermission) (*crud.DMLResult, error)
	//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
	PartialUpdate(context.Context, *DbPermission) (*crud.DMLResult, error)
	//以RID为条件,删除一行数据
	Delete(context.Context, *DbPermission) (*crud.DMLResult, error)
	//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
	SelectOne(context.Context, *crud.DMLParam) (*DbPermission, error)
	//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
	SelectMany(*crud.DMLParam, RpcDbPermission_SelectManyServer) error
	//根据条件查询数据
	Query(*crud.QueryParam, RpcDbPermission_QueryServer) error
	//根据条件查询数据,批量返回数据,crud.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(*crud.QueryParam, RpcDbPermission_QueryBatchServer) error
}

// UnimplementedRpcDbPermissionServer can be embedded to have forward compatible implementations.
type UnimplementedRpcDbPermissionServer struct {
}

func (*UnimplementedRpcDbPermissionServer) Insert(ctx context.Context, req *DbPermission) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Insert not implemented")
}
func (*UnimplementedRpcDbPermissionServer) Update(ctx context.Context, req *DbPermission) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Update not implemented")
}
func (*UnimplementedRpcDbPermissionServer) PartialUpdate(ctx context.Context, req *DbPermission) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PartialUpdate not implemented")
}
func (*UnimplementedRpcDbPermissionServer) Delete(ctx context.Context, req *DbPermission) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Delete not implemented")
}
func (*UnimplementedRpcDbPermissionServer) SelectOne(ctx context.Context, req *crud.DMLParam) (*DbPermission, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SelectOne not implemented")
}
func (*UnimplementedRpcDbPermissionServer) SelectMany(req *crud.DMLParam, srv RpcDbPermission_SelectManyServer) error {
	return status.Errorf(codes.Unimplemented, "method SelectMany not implemented")
}
func (*UnimplementedRpcDbPermissionServer) Query(req *crud.QueryParam, srv RpcDbPermission_QueryServer) error {
	return status.Errorf(codes.Unimplemented, "method Query not implemented")
}
func (*UnimplementedRpcDbPermissionServer) QueryBatch(req *crud.QueryParam, srv RpcDbPermission_QueryBatchServer) error {
	return status.Errorf(codes.Unimplemented, "method QueryBatch not implemented")
}

func RegisterRpcDbPermissionServer(s *grpc.Server, srv RpcDbPermissionServer) {
	s.RegisterService(&_RpcDbPermission_serviceDesc, srv)
}

func _RpcDbPermission_Insert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbPermission)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbPermissionServer).Insert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.RpcDbPermission/Insert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbPermissionServer).Insert(ctx, req.(*DbPermission))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbPermission_Update_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbPermission)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbPermissionServer).Update(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.RpcDbPermission/Update",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbPermissionServer).Update(ctx, req.(*DbPermission))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbPermission_PartialUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbPermission)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbPermissionServer).PartialUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.RpcDbPermission/PartialUpdate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbPermissionServer).PartialUpdate(ctx, req.(*DbPermission))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbPermission_Delete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbPermission)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbPermissionServer).Delete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.RpcDbPermission/Delete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbPermissionServer).Delete(ctx, req.(*DbPermission))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbPermission_SelectOne_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(crud.DMLParam)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbPermissionServer).SelectOne(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.RpcDbPermission/SelectOne",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbPermissionServer).SelectOne(ctx, req.(*crud.DMLParam))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbPermission_SelectMany_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.DMLParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RpcDbPermissionServer).SelectMany(m, &rpcDbPermissionSelectManyServer{stream})
}

type RpcDbPermission_SelectManyServer interface {
	Send(*DbPermission) error
	grpc.ServerStream
}

type rpcDbPermissionSelectManyServer struct {
	grpc.ServerStream
}

func (x *rpcDbPermissionSelectManyServer) Send(m *DbPermission) error {
	return x.ServerStream.SendMsg(m)
}

func _RpcDbPermission_Query_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.QueryParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RpcDbPermissionServer).Query(m, &rpcDbPermissionQueryServer{stream})
}

type RpcDbPermission_QueryServer interface {
	Send(*DbPermission) error
	grpc.ServerStream
}

type rpcDbPermissionQueryServer struct {
	grpc.ServerStream
}

func (x *rpcDbPermissionQueryServer) Send(m *DbPermission) error {
	return x.ServerStream.SendMsg(m)
}

func _RpcDbPermission_QueryBatch_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.QueryParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RpcDbPermissionServer).QueryBatch(m, &rpcDbPermissionQueryBatchServer{stream})
}

type RpcDbPermission_QueryBatchServer interface {
	Send(*DbPermissionList) error
	grpc.ServerStream
}

type rpcDbPermissionQueryBatchServer struct {
	grpc.ServerStream
}

func (x *rpcDbPermissionQueryBatchServer) Send(m *DbPermissionList) error {
	return x.ServerStream.SendMsg(m)
}

var _RpcDbPermission_serviceDesc = grpc.ServiceDesc{
	ServiceName: "user.RpcDbPermission",
	HandlerType: (*RpcDbPermissionServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Insert",
			Handler:    _RpcDbPermission_Insert_Handler,
		},
		{
			MethodName: "Update",
			Handler:    _RpcDbPermission_Update_Handler,
		},
		{
			MethodName: "PartialUpdate",
			Handler:    _RpcDbPermission_PartialUpdate_Handler,
		},
		{
			MethodName: "Delete",
			Handler:    _RpcDbPermission_Delete_Handler,
		},
		{
			MethodName: "SelectOne",
			Handler:    _RpcDbPermission_SelectOne_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "SelectMany",
			Handler:       _RpcDbPermission_SelectMany_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "Query",
			Handler:       _RpcDbPermission_Query_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "QueryBatch",
			Handler:       _RpcDbPermission_QueryBatch_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "userPermission.rpc.proto",
}

// RpcDbRoleClient is the client API for RpcDbRole service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type RpcDbRoleClient interface {
	//插入一行数据
	Insert(ctx context.Context, in *DbRole, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,全量修改一行数据
	Update(ctx context.Context, in *DbRole, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
	PartialUpdate(ctx context.Context, in *DbRole, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,删除一行数据
	Delete(ctx context.Context, in *DbRole, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
	SelectOne(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (*DbRole, error)
	//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
	SelectMany(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (RpcDbRole_SelectManyClient, error)
	//根据条件查询数据
	Query(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbRole_QueryClient, error)
	//根据条件查询数据,批量返回数据,crud.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbRole_QueryBatchClient, error)
}

type rpcDbRoleClient struct {
	cc *grpc.ClientConn
}

func NewRpcDbRoleClient(cc *grpc.ClientConn) RpcDbRoleClient {
	return &rpcDbRoleClient{cc}
}

func (c *rpcDbRoleClient) Insert(ctx context.Context, in *DbRole, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/user.RpcDbRole/Insert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbRoleClient) Update(ctx context.Context, in *DbRole, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/user.RpcDbRole/Update", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbRoleClient) PartialUpdate(ctx context.Context, in *DbRole, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/user.RpcDbRole/PartialUpdate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbRoleClient) Delete(ctx context.Context, in *DbRole, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/user.RpcDbRole/Delete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbRoleClient) SelectOne(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (*DbRole, error) {
	out := new(DbRole)
	err := c.cc.Invoke(ctx, "/user.RpcDbRole/SelectOne", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbRoleClient) SelectMany(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (RpcDbRole_SelectManyClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RpcDbRole_serviceDesc.Streams[0], "/user.RpcDbRole/SelectMany", opts...)
	if err != nil {
		return nil, err
	}
	x := &rpcDbRoleSelectManyClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type RpcDbRole_SelectManyClient interface {
	Recv() (*DbRole, error)
	grpc.ClientStream
}

type rpcDbRoleSelectManyClient struct {
	grpc.ClientStream
}

func (x *rpcDbRoleSelectManyClient) Recv() (*DbRole, error) {
	m := new(DbRole)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *rpcDbRoleClient) Query(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbRole_QueryClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RpcDbRole_serviceDesc.Streams[1], "/user.RpcDbRole/Query", opts...)
	if err != nil {
		return nil, err
	}
	x := &rpcDbRoleQueryClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type RpcDbRole_QueryClient interface {
	Recv() (*DbRole, error)
	grpc.ClientStream
}

type rpcDbRoleQueryClient struct {
	grpc.ClientStream
}

func (x *rpcDbRoleQueryClient) Recv() (*DbRole, error) {
	m := new(DbRole)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *rpcDbRoleClient) QueryBatch(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbRole_QueryBatchClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RpcDbRole_serviceDesc.Streams[2], "/user.RpcDbRole/QueryBatch", opts...)
	if err != nil {
		return nil, err
	}
	x := &rpcDbRoleQueryBatchClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type RpcDbRole_QueryBatchClient interface {
	Recv() (*DbRoleList, error)
	grpc.ClientStream
}

type rpcDbRoleQueryBatchClient struct {
	grpc.ClientStream
}

func (x *rpcDbRoleQueryBatchClient) Recv() (*DbRoleList, error) {
	m := new(DbRoleList)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// RpcDbRoleServer is the server API for RpcDbRole service.
type RpcDbRoleServer interface {
	//插入一行数据
	Insert(context.Context, *DbRole) (*crud.DMLResult, error)
	//以RID为条件,全量修改一行数据
	Update(context.Context, *DbRole) (*crud.DMLResult, error)
	//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
	PartialUpdate(context.Context, *DbRole) (*crud.DMLResult, error)
	//以RID为条件,删除一行数据
	Delete(context.Context, *DbRole) (*crud.DMLResult, error)
	//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
	SelectOne(context.Context, *crud.DMLParam) (*DbRole, error)
	//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
	SelectMany(*crud.DMLParam, RpcDbRole_SelectManyServer) error
	//根据条件查询数据
	Query(*crud.QueryParam, RpcDbRole_QueryServer) error
	//根据条件查询数据,批量返回数据,crud.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(*crud.QueryParam, RpcDbRole_QueryBatchServer) error
}

// UnimplementedRpcDbRoleServer can be embedded to have forward compatible implementations.
type UnimplementedRpcDbRoleServer struct {
}

func (*UnimplementedRpcDbRoleServer) Insert(ctx context.Context, req *DbRole) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Insert not implemented")
}
func (*UnimplementedRpcDbRoleServer) Update(ctx context.Context, req *DbRole) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Update not implemented")
}
func (*UnimplementedRpcDbRoleServer) PartialUpdate(ctx context.Context, req *DbRole) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PartialUpdate not implemented")
}
func (*UnimplementedRpcDbRoleServer) Delete(ctx context.Context, req *DbRole) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Delete not implemented")
}
func (*UnimplementedRpcDbRoleServer) SelectOne(ctx context.Context, req *crud.DMLParam) (*DbRole, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SelectOne not implemented")
}
func (*UnimplementedRpcDbRoleServer) SelectMany(req *crud.DMLParam, srv RpcDbRole_SelectManyServer) error {
	return status.Errorf(codes.Unimplemented, "method SelectMany not implemented")
}
func (*UnimplementedRpcDbRoleServer) Query(req *crud.QueryParam, srv RpcDbRole_QueryServer) error {
	return status.Errorf(codes.Unimplemented, "method Query not implemented")
}
func (*UnimplementedRpcDbRoleServer) QueryBatch(req *crud.QueryParam, srv RpcDbRole_QueryBatchServer) error {
	return status.Errorf(codes.Unimplemented, "method QueryBatch not implemented")
}

func RegisterRpcDbRoleServer(s *grpc.Server, srv RpcDbRoleServer) {
	s.RegisterService(&_RpcDbRole_serviceDesc, srv)
}

func _RpcDbRole_Insert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbRole)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbRoleServer).Insert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.RpcDbRole/Insert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbRoleServer).Insert(ctx, req.(*DbRole))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbRole_Update_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbRole)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbRoleServer).Update(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.RpcDbRole/Update",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbRoleServer).Update(ctx, req.(*DbRole))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbRole_PartialUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbRole)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbRoleServer).PartialUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.RpcDbRole/PartialUpdate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbRoleServer).PartialUpdate(ctx, req.(*DbRole))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbRole_Delete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbRole)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbRoleServer).Delete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.RpcDbRole/Delete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbRoleServer).Delete(ctx, req.(*DbRole))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbRole_SelectOne_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(crud.DMLParam)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbRoleServer).SelectOne(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.RpcDbRole/SelectOne",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbRoleServer).SelectOne(ctx, req.(*crud.DMLParam))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbRole_SelectMany_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.DMLParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RpcDbRoleServer).SelectMany(m, &rpcDbRoleSelectManyServer{stream})
}

type RpcDbRole_SelectManyServer interface {
	Send(*DbRole) error
	grpc.ServerStream
}

type rpcDbRoleSelectManyServer struct {
	grpc.ServerStream
}

func (x *rpcDbRoleSelectManyServer) Send(m *DbRole) error {
	return x.ServerStream.SendMsg(m)
}

func _RpcDbRole_Query_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.QueryParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RpcDbRoleServer).Query(m, &rpcDbRoleQueryServer{stream})
}

type RpcDbRole_QueryServer interface {
	Send(*DbRole) error
	grpc.ServerStream
}

type rpcDbRoleQueryServer struct {
	grpc.ServerStream
}

func (x *rpcDbRoleQueryServer) Send(m *DbRole) error {
	return x.ServerStream.SendMsg(m)
}

func _RpcDbRole_QueryBatch_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.QueryParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RpcDbRoleServer).QueryBatch(m, &rpcDbRoleQueryBatchServer{stream})
}

type RpcDbRole_QueryBatchServer interface {
	Send(*DbRoleList) error
	grpc.ServerStream
}

type rpcDbRoleQueryBatchServer struct {
	grpc.ServerStream
}

func (x *rpcDbRoleQueryBatchServer) Send(m *DbRoleList) error {
	return x.ServerStream.SendMsg(m)
}

var _RpcDbRole_serviceDesc = grpc.ServiceDesc{
	ServiceName: "user.RpcDbRole",
	HandlerType: (*RpcDbRoleServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Insert",
			Handler:    _RpcDbRole_Insert_Handler,
		},
		{
			MethodName: "Update",
			Handler:    _RpcDbRole_Update_Handler,
		},
		{
			MethodName: "PartialUpdate",
			Handler:    _RpcDbRole_PartialUpdate_Handler,
		},
		{
			MethodName: "Delete",
			Handler:    _RpcDbRole_Delete_Handler,
		},
		{
			MethodName: "SelectOne",
			Handler:    _RpcDbRole_SelectOne_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "SelectMany",
			Handler:       _RpcDbRole_SelectMany_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "Query",
			Handler:       _RpcDbRole_Query_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "QueryBatch",
			Handler:       _RpcDbRole_QueryBatch_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "userPermission.rpc.proto",
}

// PrpcDbRoleClient is the client API for PrpcDbRole service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type PrpcDbRoleClient interface {
	//插入一行数据
	Insert(ctx context.Context, in *DbRole, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,全量修改一行数据
	Update(ctx context.Context, in *DbRole, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
	PartialUpdate(ctx context.Context, in *DbRole, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,删除一行数据
	Delete(ctx context.Context, in *DbRole, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
	SelectOne(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (*DbRole, error)
	//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
	SelectMany(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (PrpcDbRole_SelectManyClient, error)
	//根据条件取得我有权限的数据
	Query(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbRole_QueryClient, error)
	//根据条件取得我有权限的数据,批量返回数据,crud.PrivilegeParam.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbRole_QueryBatchClient, error)
}

type prpcDbRoleClient struct {
	cc *grpc.ClientConn
}

func NewPrpcDbRoleClient(cc *grpc.ClientConn) PrpcDbRoleClient {
	return &prpcDbRoleClient{cc}
}

func (c *prpcDbRoleClient) Insert(ctx context.Context, in *DbRole, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/user.PrpcDbRole/Insert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prpcDbRoleClient) Update(ctx context.Context, in *DbRole, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/user.PrpcDbRole/Update", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prpcDbRoleClient) PartialUpdate(ctx context.Context, in *DbRole, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/user.PrpcDbRole/PartialUpdate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prpcDbRoleClient) Delete(ctx context.Context, in *DbRole, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/user.PrpcDbRole/Delete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prpcDbRoleClient) SelectOne(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (*DbRole, error) {
	out := new(DbRole)
	err := c.cc.Invoke(ctx, "/user.PrpcDbRole/SelectOne", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prpcDbRoleClient) SelectMany(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (PrpcDbRole_SelectManyClient, error) {
	stream, err := c.cc.NewStream(ctx, &_PrpcDbRole_serviceDesc.Streams[0], "/user.PrpcDbRole/SelectMany", opts...)
	if err != nil {
		return nil, err
	}
	x := &prpcDbRoleSelectManyClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type PrpcDbRole_SelectManyClient interface {
	Recv() (*DbRole, error)
	grpc.ClientStream
}

type prpcDbRoleSelectManyClient struct {
	grpc.ClientStream
}

func (x *prpcDbRoleSelectManyClient) Recv() (*DbRole, error) {
	m := new(DbRole)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *prpcDbRoleClient) Query(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbRole_QueryClient, error) {
	stream, err := c.cc.NewStream(ctx, &_PrpcDbRole_serviceDesc.Streams[1], "/user.PrpcDbRole/Query", opts...)
	if err != nil {
		return nil, err
	}
	x := &prpcDbRoleQueryClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type PrpcDbRole_QueryClient interface {
	Recv() (*DbRole, error)
	grpc.ClientStream
}

type prpcDbRoleQueryClient struct {
	grpc.ClientStream
}

func (x *prpcDbRoleQueryClient) Recv() (*DbRole, error) {
	m := new(DbRole)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *prpcDbRoleClient) QueryBatch(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbRole_QueryBatchClient, error) {
	stream, err := c.cc.NewStream(ctx, &_PrpcDbRole_serviceDesc.Streams[2], "/user.PrpcDbRole/QueryBatch", opts...)
	if err != nil {
		return nil, err
	}
	x := &prpcDbRoleQueryBatchClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type PrpcDbRole_QueryBatchClient interface {
	Recv() (*DbRoleList, error)
	grpc.ClientStream
}

type prpcDbRoleQueryBatchClient struct {
	grpc.ClientStream
}

func (x *prpcDbRoleQueryBatchClient) Recv() (*DbRoleList, error) {
	m := new(DbRoleList)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// PrpcDbRoleServer is the server API for PrpcDbRole service.
type PrpcDbRoleServer interface {
	//插入一行数据
	Insert(context.Context, *DbRole) (*crud.DMLResult, error)
	//以RID为条件,全量修改一行数据
	Update(context.Context, *DbRole) (*crud.DMLResult, error)
	//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
	PartialUpdate(context.Context, *DbRole) (*crud.DMLResult, error)
	//以RID为条件,删除一行数据
	Delete(context.Context, *DbRole) (*crud.DMLResult, error)
	//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
	SelectOne(context.Context, *crud.DMLParam) (*DbRole, error)
	//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
	SelectMany(*crud.DMLParam, PrpcDbRole_SelectManyServer) error
	//根据条件取得我有权限的数据
	Query(*crud.PrivilegeParam, PrpcDbRole_QueryServer) error
	//根据条件取得我有权限的数据,批量返回数据,crud.PrivilegeParam.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(*crud.PrivilegeParam, PrpcDbRole_QueryBatchServer) error
}

// UnimplementedPrpcDbRoleServer can be embedded to have forward compatible implementations.
type UnimplementedPrpcDbRoleServer struct {
}

func (*UnimplementedPrpcDbRoleServer) Insert(ctx context.Context, req *DbRole) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Insert not implemented")
}
func (*UnimplementedPrpcDbRoleServer) Update(ctx context.Context, req *DbRole) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Update not implemented")
}
func (*UnimplementedPrpcDbRoleServer) PartialUpdate(ctx context.Context, req *DbRole) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PartialUpdate not implemented")
}
func (*UnimplementedPrpcDbRoleServer) Delete(ctx context.Context, req *DbRole) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Delete not implemented")
}
func (*UnimplementedPrpcDbRoleServer) SelectOne(ctx context.Context, req *crud.DMLParam) (*DbRole, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SelectOne not implemented")
}
func (*UnimplementedPrpcDbRoleServer) SelectMany(req *crud.DMLParam, srv PrpcDbRole_SelectManyServer) error {
	return status.Errorf(codes.Unimplemented, "method SelectMany not implemented")
}
func (*UnimplementedPrpcDbRoleServer) Query(req *crud.PrivilegeParam, srv PrpcDbRole_QueryServer) error {
	return status.Errorf(codes.Unimplemented, "method Query not implemented")
}
func (*UnimplementedPrpcDbRoleServer) QueryBatch(req *crud.PrivilegeParam, srv PrpcDbRole_QueryBatchServer) error {
	return status.Errorf(codes.Unimplemented, "method QueryBatch not implemented")
}

func RegisterPrpcDbRoleServer(s *grpc.Server, srv PrpcDbRoleServer) {
	s.RegisterService(&_PrpcDbRole_serviceDesc, srv)
}

func _PrpcDbRole_Insert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbRole)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrpcDbRoleServer).Insert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.PrpcDbRole/Insert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrpcDbRoleServer).Insert(ctx, req.(*DbRole))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrpcDbRole_Update_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbRole)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrpcDbRoleServer).Update(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.PrpcDbRole/Update",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrpcDbRoleServer).Update(ctx, req.(*DbRole))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrpcDbRole_PartialUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbRole)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrpcDbRoleServer).PartialUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.PrpcDbRole/PartialUpdate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrpcDbRoleServer).PartialUpdate(ctx, req.(*DbRole))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrpcDbRole_Delete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbRole)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrpcDbRoleServer).Delete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.PrpcDbRole/Delete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrpcDbRoleServer).Delete(ctx, req.(*DbRole))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrpcDbRole_SelectOne_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(crud.DMLParam)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrpcDbRoleServer).SelectOne(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.PrpcDbRole/SelectOne",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrpcDbRoleServer).SelectOne(ctx, req.(*crud.DMLParam))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrpcDbRole_SelectMany_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.DMLParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(PrpcDbRoleServer).SelectMany(m, &prpcDbRoleSelectManyServer{stream})
}

type PrpcDbRole_SelectManyServer interface {
	Send(*DbRole) error
	grpc.ServerStream
}

type prpcDbRoleSelectManyServer struct {
	grpc.ServerStream
}

func (x *prpcDbRoleSelectManyServer) Send(m *DbRole) error {
	return x.ServerStream.SendMsg(m)
}

func _PrpcDbRole_Query_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.PrivilegeParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(PrpcDbRoleServer).Query(m, &prpcDbRoleQueryServer{stream})
}

type PrpcDbRole_QueryServer interface {
	Send(*DbRole) error
	grpc.ServerStream
}

type prpcDbRoleQueryServer struct {
	grpc.ServerStream
}

func (x *prpcDbRoleQueryServer) Send(m *DbRole) error {
	return x.ServerStream.SendMsg(m)
}

func _PrpcDbRole_QueryBatch_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.PrivilegeParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(PrpcDbRoleServer).QueryBatch(m, &prpcDbRoleQueryBatchServer{stream})
}

type PrpcDbRole_QueryBatchServer interface {
	Send(*DbRoleList) error
	grpc.ServerStream
}

type prpcDbRoleQueryBatchServer struct {
	grpc.ServerStream
}

func (x *prpcDbRoleQueryBatchServer) Send(m *DbRoleList) error {
	return x.ServerStream.SendMsg(m)
}

var _PrpcDbRole_serviceDesc = grpc.ServiceDesc{
	ServiceName: "user.PrpcDbRole",
	HandlerType: (*PrpcDbRoleServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Insert",
			Handler:    _PrpcDbRole_Insert_Handler,
		},
		{
			MethodName: "Update",
			Handler:    _PrpcDbRole_Update_Handler,
		},
		{
			MethodName: "PartialUpdate",
			Handler:    _PrpcDbRole_PartialUpdate_Handler,
		},
		{
			MethodName: "Delete",
			Handler:    _PrpcDbRole_Delete_Handler,
		},
		{
			MethodName: "SelectOne",
			Handler:    _PrpcDbRole_SelectOne_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "SelectMany",
			Handler:       _PrpcDbRole_SelectMany_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "Query",
			Handler:       _PrpcDbRole_Query_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "QueryBatch",
			Handler:       _PrpcDbRole_QueryBatch_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "userPermission.rpc.proto",
}

// RpcDbRolePermissionClient is the client API for RpcDbRolePermission service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type RpcDbRolePermissionClient interface {
	//插入一行数据
	Insert(ctx context.Context, in *DbRolePermission, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,全量修改一行数据
	Update(ctx context.Context, in *DbRolePermission, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
	PartialUpdate(ctx context.Context, in *DbRolePermission, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,删除一行数据
	Delete(ctx context.Context, in *DbRolePermission, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
	SelectOne(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (*DbRolePermission, error)
	//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
	SelectMany(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (RpcDbRolePermission_SelectManyClient, error)
	//根据条件查询数据
	Query(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbRolePermission_QueryClient, error)
	//根据条件查询数据,批量返回数据,crud.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbRolePermission_QueryBatchClient, error)
}

type rpcDbRolePermissionClient struct {
	cc *grpc.ClientConn
}

func NewRpcDbRolePermissionClient(cc *grpc.ClientConn) RpcDbRolePermissionClient {
	return &rpcDbRolePermissionClient{cc}
}

func (c *rpcDbRolePermissionClient) Insert(ctx context.Context, in *DbRolePermission, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/user.RpcDbRolePermission/Insert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbRolePermissionClient) Update(ctx context.Context, in *DbRolePermission, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/user.RpcDbRolePermission/Update", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbRolePermissionClient) PartialUpdate(ctx context.Context, in *DbRolePermission, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/user.RpcDbRolePermission/PartialUpdate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbRolePermissionClient) Delete(ctx context.Context, in *DbRolePermission, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/user.RpcDbRolePermission/Delete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbRolePermissionClient) SelectOne(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (*DbRolePermission, error) {
	out := new(DbRolePermission)
	err := c.cc.Invoke(ctx, "/user.RpcDbRolePermission/SelectOne", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbRolePermissionClient) SelectMany(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (RpcDbRolePermission_SelectManyClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RpcDbRolePermission_serviceDesc.Streams[0], "/user.RpcDbRolePermission/SelectMany", opts...)
	if err != nil {
		return nil, err
	}
	x := &rpcDbRolePermissionSelectManyClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type RpcDbRolePermission_SelectManyClient interface {
	Recv() (*DbRolePermission, error)
	grpc.ClientStream
}

type rpcDbRolePermissionSelectManyClient struct {
	grpc.ClientStream
}

func (x *rpcDbRolePermissionSelectManyClient) Recv() (*DbRolePermission, error) {
	m := new(DbRolePermission)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *rpcDbRolePermissionClient) Query(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbRolePermission_QueryClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RpcDbRolePermission_serviceDesc.Streams[1], "/user.RpcDbRolePermission/Query", opts...)
	if err != nil {
		return nil, err
	}
	x := &rpcDbRolePermissionQueryClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type RpcDbRolePermission_QueryClient interface {
	Recv() (*DbRolePermission, error)
	grpc.ClientStream
}

type rpcDbRolePermissionQueryClient struct {
	grpc.ClientStream
}

func (x *rpcDbRolePermissionQueryClient) Recv() (*DbRolePermission, error) {
	m := new(DbRolePermission)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *rpcDbRolePermissionClient) QueryBatch(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbRolePermission_QueryBatchClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RpcDbRolePermission_serviceDesc.Streams[2], "/user.RpcDbRolePermission/QueryBatch", opts...)
	if err != nil {
		return nil, err
	}
	x := &rpcDbRolePermissionQueryBatchClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type RpcDbRolePermission_QueryBatchClient interface {
	Recv() (*DbRolePermissionList, error)
	grpc.ClientStream
}

type rpcDbRolePermissionQueryBatchClient struct {
	grpc.ClientStream
}

func (x *rpcDbRolePermissionQueryBatchClient) Recv() (*DbRolePermissionList, error) {
	m := new(DbRolePermissionList)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// RpcDbRolePermissionServer is the server API for RpcDbRolePermission service.
type RpcDbRolePermissionServer interface {
	//插入一行数据
	Insert(context.Context, *DbRolePermission) (*crud.DMLResult, error)
	//以RID为条件,全量修改一行数据
	Update(context.Context, *DbRolePermission) (*crud.DMLResult, error)
	//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
	PartialUpdate(context.Context, *DbRolePermission) (*crud.DMLResult, error)
	//以RID为条件,删除一行数据
	Delete(context.Context, *DbRolePermission) (*crud.DMLResult, error)
	//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
	SelectOne(context.Context, *crud.DMLParam) (*DbRolePermission, error)
	//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
	SelectMany(*crud.DMLParam, RpcDbRolePermission_SelectManyServer) error
	//根据条件查询数据
	Query(*crud.QueryParam, RpcDbRolePermission_QueryServer) error
	//根据条件查询数据,批量返回数据,crud.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(*crud.QueryParam, RpcDbRolePermission_QueryBatchServer) error
}

// UnimplementedRpcDbRolePermissionServer can be embedded to have forward compatible implementations.
type UnimplementedRpcDbRolePermissionServer struct {
}

func (*UnimplementedRpcDbRolePermissionServer) Insert(ctx context.Context, req *DbRolePermission) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Insert not implemented")
}
func (*UnimplementedRpcDbRolePermissionServer) Update(ctx context.Context, req *DbRolePermission) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Update not implemented")
}
func (*UnimplementedRpcDbRolePermissionServer) PartialUpdate(ctx context.Context, req *DbRolePermission) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PartialUpdate not implemented")
}
func (*UnimplementedRpcDbRolePermissionServer) Delete(ctx context.Context, req *DbRolePermission) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Delete not implemented")
}
func (*UnimplementedRpcDbRolePermissionServer) SelectOne(ctx context.Context, req *crud.DMLParam) (*DbRolePermission, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SelectOne not implemented")
}
func (*UnimplementedRpcDbRolePermissionServer) SelectMany(req *crud.DMLParam, srv RpcDbRolePermission_SelectManyServer) error {
	return status.Errorf(codes.Unimplemented, "method SelectMany not implemented")
}
func (*UnimplementedRpcDbRolePermissionServer) Query(req *crud.QueryParam, srv RpcDbRolePermission_QueryServer) error {
	return status.Errorf(codes.Unimplemented, "method Query not implemented")
}
func (*UnimplementedRpcDbRolePermissionServer) QueryBatch(req *crud.QueryParam, srv RpcDbRolePermission_QueryBatchServer) error {
	return status.Errorf(codes.Unimplemented, "method QueryBatch not implemented")
}

func RegisterRpcDbRolePermissionServer(s *grpc.Server, srv RpcDbRolePermissionServer) {
	s.RegisterService(&_RpcDbRolePermission_serviceDesc, srv)
}

func _RpcDbRolePermission_Insert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbRolePermission)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbRolePermissionServer).Insert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.RpcDbRolePermission/Insert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbRolePermissionServer).Insert(ctx, req.(*DbRolePermission))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbRolePermission_Update_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbRolePermission)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbRolePermissionServer).Update(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.RpcDbRolePermission/Update",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbRolePermissionServer).Update(ctx, req.(*DbRolePermission))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbRolePermission_PartialUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbRolePermission)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbRolePermissionServer).PartialUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.RpcDbRolePermission/PartialUpdate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbRolePermissionServer).PartialUpdate(ctx, req.(*DbRolePermission))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbRolePermission_Delete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbRolePermission)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbRolePermissionServer).Delete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.RpcDbRolePermission/Delete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbRolePermissionServer).Delete(ctx, req.(*DbRolePermission))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbRolePermission_SelectOne_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(crud.DMLParam)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbRolePermissionServer).SelectOne(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.RpcDbRolePermission/SelectOne",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbRolePermissionServer).SelectOne(ctx, req.(*crud.DMLParam))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbRolePermission_SelectMany_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.DMLParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RpcDbRolePermissionServer).SelectMany(m, &rpcDbRolePermissionSelectManyServer{stream})
}

type RpcDbRolePermission_SelectManyServer interface {
	Send(*DbRolePermission) error
	grpc.ServerStream
}

type rpcDbRolePermissionSelectManyServer struct {
	grpc.ServerStream
}

func (x *rpcDbRolePermissionSelectManyServer) Send(m *DbRolePermission) error {
	return x.ServerStream.SendMsg(m)
}

func _RpcDbRolePermission_Query_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.QueryParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RpcDbRolePermissionServer).Query(m, &rpcDbRolePermissionQueryServer{stream})
}

type RpcDbRolePermission_QueryServer interface {
	Send(*DbRolePermission) error
	grpc.ServerStream
}

type rpcDbRolePermissionQueryServer struct {
	grpc.ServerStream
}

func (x *rpcDbRolePermissionQueryServer) Send(m *DbRolePermission) error {
	return x.ServerStream.SendMsg(m)
}

func _RpcDbRolePermission_QueryBatch_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.QueryParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RpcDbRolePermissionServer).QueryBatch(m, &rpcDbRolePermissionQueryBatchServer{stream})
}

type RpcDbRolePermission_QueryBatchServer interface {
	Send(*DbRolePermissionList) error
	grpc.ServerStream
}

type rpcDbRolePermissionQueryBatchServer struct {
	grpc.ServerStream
}

func (x *rpcDbRolePermissionQueryBatchServer) Send(m *DbRolePermissionList) error {
	return x.ServerStream.SendMsg(m)
}

var _RpcDbRolePermission_serviceDesc = grpc.ServiceDesc{
	ServiceName: "user.RpcDbRolePermission",
	HandlerType: (*RpcDbRolePermissionServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Insert",
			Handler:    _RpcDbRolePermission_Insert_Handler,
		},
		{
			MethodName: "Update",
			Handler:    _RpcDbRolePermission_Update_Handler,
		},
		{
			MethodName: "PartialUpdate",
			Handler:    _RpcDbRolePermission_PartialUpdate_Handler,
		},
		{
			MethodName: "Delete",
			Handler:    _RpcDbRolePermission_Delete_Handler,
		},
		{
			MethodName: "SelectOne",
			Handler:    _RpcDbRolePermission_SelectOne_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "SelectMany",
			Handler:       _RpcDbRolePermission_SelectMany_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "Query",
			Handler:       _RpcDbRolePermission_Query_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "QueryBatch",
			Handler:       _RpcDbRolePermission_QueryBatch_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "userPermission.rpc.proto",
}

// PrpcDbRolePermissionClient is the client API for PrpcDbRolePermission service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type PrpcDbRolePermissionClient interface {
	//插入一行数据
	Insert(ctx context.Context, in *DbRolePermission, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,全量修改一行数据
	Update(ctx context.Context, in *DbRolePermission, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
	PartialUpdate(ctx context.Context, in *DbRolePermission, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,删除一行数据
	Delete(ctx context.Context, in *DbRolePermission, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
	SelectOne(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (*DbRolePermission, error)
	//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
	SelectMany(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (PrpcDbRolePermission_SelectManyClient, error)
	//根据条件取得我有权限的数据
	Query(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbRolePermission_QueryClient, error)
	//根据条件取得我有权限的数据,批量返回数据,crud.PrivilegeParam.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbRolePermission_QueryBatchClient, error)
}

type prpcDbRolePermissionClient struct {
	cc *grpc.ClientConn
}

func NewPrpcDbRolePermissionClient(cc *grpc.ClientConn) PrpcDbRolePermissionClient {
	return &prpcDbRolePermissionClient{cc}
}

func (c *prpcDbRolePermissionClient) Insert(ctx context.Context, in *DbRolePermission, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/user.PrpcDbRolePermission/Insert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prpcDbRolePermissionClient) Update(ctx context.Context, in *DbRolePermission, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/user.PrpcDbRolePermission/Update", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prpcDbRolePermissionClient) PartialUpdate(ctx context.Context, in *DbRolePermission, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/user.PrpcDbRolePermission/PartialUpdate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prpcDbRolePermissionClient) Delete(ctx context.Context, in *DbRolePermission, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/user.PrpcDbRolePermission/Delete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prpcDbRolePermissionClient) SelectOne(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (*DbRolePermission, error) {
	out := new(DbRolePermission)
	err := c.cc.Invoke(ctx, "/user.PrpcDbRolePermission/SelectOne", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prpcDbRolePermissionClient) SelectMany(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (PrpcDbRolePermission_SelectManyClient, error) {
	stream, err := c.cc.NewStream(ctx, &_PrpcDbRolePermission_serviceDesc.Streams[0], "/user.PrpcDbRolePermission/SelectMany", opts...)
	if err != nil {
		return nil, err
	}
	x := &prpcDbRolePermissionSelectManyClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type PrpcDbRolePermission_SelectManyClient interface {
	Recv() (*DbRolePermission, error)
	grpc.ClientStream
}

type prpcDbRolePermissionSelectManyClient struct {
	grpc.ClientStream
}

func (x *prpcDbRolePermissionSelectManyClient) Recv() (*DbRolePermission, error) {
	m := new(DbRolePermission)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *prpcDbRolePermissionClient) Query(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbRolePermission_QueryClient, error) {
	stream, err := c.cc.NewStream(ctx, &_PrpcDbRolePermission_serviceDesc.Streams[1], "/user.PrpcDbRolePermission/Query", opts...)
	if err != nil {
		return nil, err
	}
	x := &prpcDbRolePermissionQueryClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type PrpcDbRolePermission_QueryClient interface {
	Recv() (*DbRolePermission, error)
	grpc.ClientStream
}

type prpcDbRolePermissionQueryClient struct {
	grpc.ClientStream
}

func (x *prpcDbRolePermissionQueryClient) Recv() (*DbRolePermission, error) {
	m := new(DbRolePermission)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *prpcDbRolePermissionClient) QueryBatch(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbRolePermission_QueryBatchClient, error) {
	stream, err := c.cc.NewStream(ctx, &_PrpcDbRolePermission_serviceDesc.Streams[2], "/user.PrpcDbRolePermission/QueryBatch", opts...)
	if err != nil {
		return nil, err
	}
	x := &prpcDbRolePermissionQueryBatchClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type PrpcDbRolePermission_QueryBatchClient interface {
	Recv() (*DbRolePermissionList, error)
	grpc.ClientStream
}

type prpcDbRolePermissionQueryBatchClient struct {
	grpc.ClientStream
}

func (x *prpcDbRolePermissionQueryBatchClient) Recv() (*DbRolePermissionList, error) {
	m := new(DbRolePermissionList)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// PrpcDbRolePermissionServer is the server API for PrpcDbRolePermission service.
type PrpcDbRolePermissionServer interface {
	//插入一行数据
	Insert(context.Context, *DbRolePermission) (*crud.DMLResult, error)
	//以RID为条件,全量修改一行数据
	Update(context.Context, *DbRolePermission) (*crud.DMLResult, error)
	//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
	PartialUpdate(context.Context, *DbRolePermission) (*crud.DMLResult, error)
	//以RID为条件,删除一行数据
	Delete(context.Context, *DbRolePermission) (*crud.DMLResult, error)
	//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
	SelectOne(context.Context, *crud.DMLParam) (*DbRolePermission, error)
	//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
	SelectMany(*crud.DMLParam, PrpcDbRolePermission_SelectManyServer) error
	//根据条件取得我有权限的数据
	Query(*crud.PrivilegeParam, PrpcDbRolePermission_QueryServer) error
	//根据条件取得我有权限的数据,批量返回数据,crud.PrivilegeParam.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(*crud.PrivilegeParam, PrpcDbRolePermission_QueryBatchServer) error
}

// UnimplementedPrpcDbRolePermissionServer can be embedded to have forward compatible implementations.
type UnimplementedPrpcDbRolePermissionServer struct {
}

func (*UnimplementedPrpcDbRolePermissionServer) Insert(ctx context.Context, req *DbRolePermission) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Insert not implemented")
}
func (*UnimplementedPrpcDbRolePermissionServer) Update(ctx context.Context, req *DbRolePermission) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Update not implemented")
}
func (*UnimplementedPrpcDbRolePermissionServer) PartialUpdate(ctx context.Context, req *DbRolePermission) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PartialUpdate not implemented")
}
func (*UnimplementedPrpcDbRolePermissionServer) Delete(ctx context.Context, req *DbRolePermission) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Delete not implemented")
}
func (*UnimplementedPrpcDbRolePermissionServer) SelectOne(ctx context.Context, req *crud.DMLParam) (*DbRolePermission, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SelectOne not implemented")
}
func (*UnimplementedPrpcDbRolePermissionServer) SelectMany(req *crud.DMLParam, srv PrpcDbRolePermission_SelectManyServer) error {
	return status.Errorf(codes.Unimplemented, "method SelectMany not implemented")
}
func (*UnimplementedPrpcDbRolePermissionServer) Query(req *crud.PrivilegeParam, srv PrpcDbRolePermission_QueryServer) error {
	return status.Errorf(codes.Unimplemented, "method Query not implemented")
}
func (*UnimplementedPrpcDbRolePermissionServer) QueryBatch(req *crud.PrivilegeParam, srv PrpcDbRolePermission_QueryBatchServer) error {
	return status.Errorf(codes.Unimplemented, "method QueryBatch not implemented")
}

func RegisterPrpcDbRolePermissionServer(s *grpc.Server, srv PrpcDbRolePermissionServer) {
	s.RegisterService(&_PrpcDbRolePermission_serviceDesc, srv)
}

func _PrpcDbRolePermission_Insert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbRolePermission)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrpcDbRolePermissionServer).Insert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.PrpcDbRolePermission/Insert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrpcDbRolePermissionServer).Insert(ctx, req.(*DbRolePermission))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrpcDbRolePermission_Update_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbRolePermission)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrpcDbRolePermissionServer).Update(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.PrpcDbRolePermission/Update",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrpcDbRolePermissionServer).Update(ctx, req.(*DbRolePermission))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrpcDbRolePermission_PartialUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbRolePermission)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrpcDbRolePermissionServer).PartialUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.PrpcDbRolePermission/PartialUpdate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrpcDbRolePermissionServer).PartialUpdate(ctx, req.(*DbRolePermission))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrpcDbRolePermission_Delete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbRolePermission)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrpcDbRolePermissionServer).Delete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.PrpcDbRolePermission/Delete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrpcDbRolePermissionServer).Delete(ctx, req.(*DbRolePermission))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrpcDbRolePermission_SelectOne_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(crud.DMLParam)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrpcDbRolePermissionServer).SelectOne(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.PrpcDbRolePermission/SelectOne",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrpcDbRolePermissionServer).SelectOne(ctx, req.(*crud.DMLParam))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrpcDbRolePermission_SelectMany_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.DMLParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(PrpcDbRolePermissionServer).SelectMany(m, &prpcDbRolePermissionSelectManyServer{stream})
}

type PrpcDbRolePermission_SelectManyServer interface {
	Send(*DbRolePermission) error
	grpc.ServerStream
}

type prpcDbRolePermissionSelectManyServer struct {
	grpc.ServerStream
}

func (x *prpcDbRolePermissionSelectManyServer) Send(m *DbRolePermission) error {
	return x.ServerStream.SendMsg(m)
}

func _PrpcDbRolePermission_Query_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.PrivilegeParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(PrpcDbRolePermissionServer).Query(m, &prpcDbRolePermissionQueryServer{stream})
}

type PrpcDbRolePermission_QueryServer interface {
	Send(*DbRolePermission) error
	grpc.ServerStream
}

type prpcDbRolePermissionQueryServer struct {
	grpc.ServerStream
}

func (x *prpcDbRolePermissionQueryServer) Send(m *DbRolePermission) error {
	return x.ServerStream.SendMsg(m)
}

func _PrpcDbRolePermission_QueryBatch_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.PrivilegeParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(PrpcDbRolePermissionServer).QueryBatch(m, &prpcDbRolePermissionQueryBatchServer{stream})
}

type PrpcDbRolePermission_QueryBatchServer interface {
	Send(*DbRolePermissionList) error
	grpc.ServerStream
}

type prpcDbRolePermissionQueryBatchServer struct {
	grpc.ServerStream
}

func (x *prpcDbRolePermissionQueryBatchServer) Send(m *DbRolePermissionList) error {
	return x.ServerStream.SendMsg(m)
}

var _PrpcDbRolePermission_serviceDesc = grpc.ServiceDesc{
	ServiceName: "user.PrpcDbRolePermission",
	HandlerType: (*PrpcDbRolePermissionServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Insert",
			Handler:    _PrpcDbRolePermission_Insert_Handler,
		},
		{
			MethodName: "Update",
			Handler:    _PrpcDbRolePermission_Update_Handler,
		},
		{
			MethodName: "PartialUpdate",
			Handler:    _PrpcDbRolePermission_PartialUpdate_Handler,
		},
		{
			MethodName: "Delete",
			Handler:    _PrpcDbRolePermission_Delete_Handler,
		},
		{
			MethodName: "SelectOne",
			Handler:    _PrpcDbRolePermission_SelectOne_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "SelectMany",
			Handler:       _PrpcDbRolePermission_SelectMany_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "Query",
			Handler:       _PrpcDbRolePermission_Query_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "QueryBatch",
			Handler:       _PrpcDbRolePermission_QueryBatch_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "userPermission.rpc.proto",
}

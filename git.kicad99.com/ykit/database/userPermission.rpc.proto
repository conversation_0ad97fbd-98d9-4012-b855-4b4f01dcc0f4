//generated by ygen-gocrud-proto. DO NOT EDIT.
//source: userPermission.proto
syntax = "proto3";
package user;

import "crud.proto";
import "userPermission.proto"; 
import "userPermission.list.proto"; 
option go_package="git.kicad99.com/ykit/database/user";

//普通crud，没有权限检查
service   RpcDbPermission {
//插入一行数据
rpc Insert( DbPermission ) returns (crud.DMLResult);

//以RID为条件,全量修改一行数据
rpc Update( DbPermission ) returns (crud.DMLResult);

//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
rpc PartialUpdate( DbPermission ) returns (crud.DMLResult);

//以RID为条件,删除一行数据
rpc Delete( DbPermission ) returns (crud.DMLResult);

//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
rpc SelectOne( crud.DMLParam ) returns ( DbPermission );

//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
rpc SelectMany( crud.DMLParam ) returns ( stream DbPermission );

//根据条件查询数据
rpc Query( crud.QueryParam ) returns ( stream DbPermission );
//根据条件查询数据,批量返回数据,crud.QueryParam.Batch指定一次返回多少条数据
rpc QueryBatch( crud.QueryParam ) returns ( stream DbPermissionList );
}



//普通crud，没有权限检查
service   RpcDbRole {
//插入一行数据
rpc Insert( DbRole ) returns (crud.DMLResult);

//以RID为条件,全量修改一行数据
rpc Update( DbRole ) returns (crud.DMLResult);

//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
rpc PartialUpdate( DbRole ) returns (crud.DMLResult);

//以RID为条件,删除一行数据
rpc Delete( DbRole ) returns (crud.DMLResult);

//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
rpc SelectOne( crud.DMLParam ) returns ( DbRole );

//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
rpc SelectMany( crud.DMLParam ) returns ( stream DbRole );

//根据条件查询数据
rpc Query( crud.QueryParam ) returns ( stream DbRole );
//根据条件查询数据,批量返回数据,crud.QueryParam.Batch指定一次返回多少条数据
rpc QueryBatch( crud.QueryParam ) returns ( stream DbRoleList );
}

//带权限检查(有相应的群组权限和编辑权限)的crud
service   PrpcDbRole {
//插入一行数据
rpc Insert( DbRole ) returns (crud.DMLResult);

//以RID为条件,全量修改一行数据
rpc Update( DbRole ) returns (crud.DMLResult);

//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
rpc PartialUpdate( DbRole ) returns (crud.DMLResult);

//以RID为条件,删除一行数据
rpc Delete( DbRole ) returns (crud.DMLResult);

//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
rpc SelectOne( crud.DMLParam ) returns ( DbRole );

//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
rpc SelectMany( crud.DMLParam ) returns ( stream DbRole );

//根据条件取得我有权限的数据
rpc Query( crud.PrivilegeParam ) returns ( stream DbRole );
//根据条件取得我有权限的数据,批量返回数据,crud.PrivilegeParam.QueryParam.Batch指定一次返回多少条数据
rpc QueryBatch( crud.PrivilegeParam ) returns ( stream DbRoleList );
}

//普通crud，没有权限检查
service   RpcDbRolePermission {
//插入一行数据
rpc Insert( DbRolePermission ) returns (crud.DMLResult);

//以RID为条件,全量修改一行数据
rpc Update( DbRolePermission ) returns (crud.DMLResult);

//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
rpc PartialUpdate( DbRolePermission ) returns (crud.DMLResult);

//以RID为条件,删除一行数据
rpc Delete( DbRolePermission ) returns (crud.DMLResult);

//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
rpc SelectOne( crud.DMLParam ) returns ( DbRolePermission );

//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
rpc SelectMany( crud.DMLParam ) returns ( stream DbRolePermission );

//根据条件查询数据
rpc Query( crud.QueryParam ) returns ( stream DbRolePermission );
//根据条件查询数据,批量返回数据,crud.QueryParam.Batch指定一次返回多少条数据
rpc QueryBatch( crud.QueryParam ) returns ( stream DbRolePermissionList );
}

//带权限检查(有相应的群组权限和编辑权限)的crud
service   PrpcDbRolePermission {
//插入一行数据
rpc Insert( DbRolePermission ) returns (crud.DMLResult);

//以RID为条件,全量修改一行数据
rpc Update( DbRolePermission ) returns (crud.DMLResult);

//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
rpc PartialUpdate( DbRolePermission ) returns (crud.DMLResult);

//以RID为条件,删除一行数据
rpc Delete( DbRolePermission ) returns (crud.DMLResult);

//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
rpc SelectOne( crud.DMLParam ) returns ( DbRolePermission );

//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
rpc SelectMany( crud.DMLParam ) returns ( stream DbRolePermission );

//根据条件取得我有权限的数据
rpc Query( crud.PrivilegeParam ) returns ( stream DbRolePermission );
//根据条件取得我有权限的数据,批量返回数据,crud.PrivilegeParam.QueryParam.Batch指定一次返回多少条数据
rpc QueryBatch( crud.PrivilegeParam ) returns ( stream DbRolePermissionList );
}

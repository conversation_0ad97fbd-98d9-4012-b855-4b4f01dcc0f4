// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: config.rpc.proto

package config

import (
	context "context"
	fmt "fmt"
	crud "git.kicad99.com/ykit/goutil/crud"
	proto "github.com/gogo/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

func init() { proto.RegisterFile("config.rpc.proto", fileDescriptor_7bdefbde354702dd) }

var fileDescriptor_7bdefbde354702dd = []byte{
	// 286 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x8c, 0xd1, 0xbd, 0x4e, 0xc3, 0x30,
	0x10, 0x07, 0xf0, 0x44, 0x88, 0x48, 0x1c, 0x5f, 0xc5, 0x62, 0xca, 0xe0, 0x89, 0xad, 0xaa, 0x13,
	0x15, 0x09, 0xa9, 0x0b, 0x43, 0xc9, 0x82, 0xd4, 0x8a, 0x52, 0xc4, 0xc2, 0xe6, 0x38, 0xa6, 0x58,
	0x4d, 0x93, 0xc8, 0xb9, 0x0c, 0x79, 0x8b, 0x3e, 0x16, 0x63, 0x47, 0x46, 0x94, 0xbc, 0x08, 0x4a,
	0x1c, 0x18, 0xf8, 0x10, 0x19, 0xef, 0xfe, 0xbf, 0xff, 0xc9, 0x92, 0x61, 0x20, 0xd2, 0xe4, 0x59,
	0xad, 0x98, 0xce, 0x04, 0xcb, 0x74, 0x8a, 0x29, 0x71, 0xcc, 0xc6, 0x05, 0xa1, 0x8b, 0xc8, 0xec,
	0xdc, 0xa3, 0x4e, 0x99, 0xe9, 0xac, 0x9b, 0x62, 0x95, 0xa3, 0x59, 0x8d, 0xb7, 0x7b, 0x70, 0xb8,
	0xcc, 0x44, 0x10, 0xde, 0xb4, 0x11, 0x19, 0x82, 0x73, 0x9b, 0xe4, 0x52, 0x23, 0x19, 0xb0, 0x4e,
	0x7f, 0x66, 0xee, 0x29, 0x6b, 0x2f, 0x07, 0xf3, 0xd9, 0x52, 0xe6, 0x45, 0x8c, 0x0d, 0x7e, 0xcc,
	0x22, 0x8e, 0xb2, 0x0f, 0x1e, 0xc3, 0xf1, 0x82, 0x6b, 0x54, 0x3c, 0xee, 0xdf, 0x19, 0x82, 0x13,
	0xc8, 0x58, 0xf6, 0xc3, 0x23, 0x38, 0x78, 0x90, 0xb1, 0x14, 0x78, 0x97, 0x48, 0x72, 0xf2, 0x95,
	0x2e, 0xb8, 0xe6, 0x1b, 0xf7, 0x47, 0x9f, 0xf8, 0x00, 0x86, 0xcf, 0x79, 0x52, 0xfe, 0xef, 0x7d,
	0x9b, 0x8c, 0x60, 0xff, 0xbe, 0x90, 0xba, 0x6c, 0x1e, 0xd3, 0xe0, 0x76, 0xf8, 0x9b, 0x5f, 0x01,
	0xb4, 0x62, 0xca, 0x51, 0xbc, 0xfc, 0xd2, 0x39, 0xff, 0xde, 0x99, 0xa9, 0x1c, 0x7d, 0x7b, 0x7a,
	0xfd, 0x5a, 0x51, 0x7b, 0x57, 0x51, 0xfb, 0xbd, 0xa2, 0xf6, 0xb6, 0xa6, 0xd6, 0xae, 0xa6, 0xd6,
	0x5b, 0x4d, 0xad, 0xa7, 0x8b, 0x95, 0x42, 0xb6, 0x56, 0x82, 0x47, 0x93, 0x09, 0x13, 0xe9, 0xc6,
	0x2b, 0xd7, 0x0a, 0xbd, 0x88, 0x23, 0x0f, 0x79, 0x2e, 0x3d, 0x73, 0x2e, 0x74, 0xda, 0x9f, 0xbd,
	0xfc, 0x08, 0x00, 0x00, 0xff, 0xff, 0x2c, 0x91, 0x0c, 0xe9, 0x22, 0x02, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// RpcDbConfigClient is the client API for RpcDbConfig service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type RpcDbConfigClient interface {
	//插入一行数据
	Insert(ctx context.Context, in *DbConfig, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,全量修改一行数据
	Update(ctx context.Context, in *DbConfig, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
	PartialUpdate(ctx context.Context, in *DbConfig, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,删除一行数据
	Delete(ctx context.Context, in *DbConfig, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
	SelectOne(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (*DbConfig, error)
	//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
	SelectMany(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (RpcDbConfig_SelectManyClient, error)
	//根据条件查询数据
	Query(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbConfig_QueryClient, error)
	//根据条件查询数据,批量返回数据,crud.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbConfig_QueryBatchClient, error)
}

type rpcDbConfigClient struct {
	cc *grpc.ClientConn
}

func NewRpcDbConfigClient(cc *grpc.ClientConn) RpcDbConfigClient {
	return &rpcDbConfigClient{cc}
}

func (c *rpcDbConfigClient) Insert(ctx context.Context, in *DbConfig, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/config.RpcDbConfig/Insert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbConfigClient) Update(ctx context.Context, in *DbConfig, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/config.RpcDbConfig/Update", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbConfigClient) PartialUpdate(ctx context.Context, in *DbConfig, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/config.RpcDbConfig/PartialUpdate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbConfigClient) Delete(ctx context.Context, in *DbConfig, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/config.RpcDbConfig/Delete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbConfigClient) SelectOne(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (*DbConfig, error) {
	out := new(DbConfig)
	err := c.cc.Invoke(ctx, "/config.RpcDbConfig/SelectOne", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbConfigClient) SelectMany(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (RpcDbConfig_SelectManyClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RpcDbConfig_serviceDesc.Streams[0], "/config.RpcDbConfig/SelectMany", opts...)
	if err != nil {
		return nil, err
	}
	x := &rpcDbConfigSelectManyClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type RpcDbConfig_SelectManyClient interface {
	Recv() (*DbConfig, error)
	grpc.ClientStream
}

type rpcDbConfigSelectManyClient struct {
	grpc.ClientStream
}

func (x *rpcDbConfigSelectManyClient) Recv() (*DbConfig, error) {
	m := new(DbConfig)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *rpcDbConfigClient) Query(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbConfig_QueryClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RpcDbConfig_serviceDesc.Streams[1], "/config.RpcDbConfig/Query", opts...)
	if err != nil {
		return nil, err
	}
	x := &rpcDbConfigQueryClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type RpcDbConfig_QueryClient interface {
	Recv() (*DbConfig, error)
	grpc.ClientStream
}

type rpcDbConfigQueryClient struct {
	grpc.ClientStream
}

func (x *rpcDbConfigQueryClient) Recv() (*DbConfig, error) {
	m := new(DbConfig)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *rpcDbConfigClient) QueryBatch(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbConfig_QueryBatchClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RpcDbConfig_serviceDesc.Streams[2], "/config.RpcDbConfig/QueryBatch", opts...)
	if err != nil {
		return nil, err
	}
	x := &rpcDbConfigQueryBatchClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type RpcDbConfig_QueryBatchClient interface {
	Recv() (*DbConfigList, error)
	grpc.ClientStream
}

type rpcDbConfigQueryBatchClient struct {
	grpc.ClientStream
}

func (x *rpcDbConfigQueryBatchClient) Recv() (*DbConfigList, error) {
	m := new(DbConfigList)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// RpcDbConfigServer is the server API for RpcDbConfig service.
type RpcDbConfigServer interface {
	//插入一行数据
	Insert(context.Context, *DbConfig) (*crud.DMLResult, error)
	//以RID为条件,全量修改一行数据
	Update(context.Context, *DbConfig) (*crud.DMLResult, error)
	//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
	PartialUpdate(context.Context, *DbConfig) (*crud.DMLResult, error)
	//以RID为条件,删除一行数据
	Delete(context.Context, *DbConfig) (*crud.DMLResult, error)
	//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
	SelectOne(context.Context, *crud.DMLParam) (*DbConfig, error)
	//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
	SelectMany(*crud.DMLParam, RpcDbConfig_SelectManyServer) error
	//根据条件查询数据
	Query(*crud.QueryParam, RpcDbConfig_QueryServer) error
	//根据条件查询数据,批量返回数据,crud.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(*crud.QueryParam, RpcDbConfig_QueryBatchServer) error
}

// UnimplementedRpcDbConfigServer can be embedded to have forward compatible implementations.
type UnimplementedRpcDbConfigServer struct {
}

func (*UnimplementedRpcDbConfigServer) Insert(ctx context.Context, req *DbConfig) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Insert not implemented")
}
func (*UnimplementedRpcDbConfigServer) Update(ctx context.Context, req *DbConfig) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Update not implemented")
}
func (*UnimplementedRpcDbConfigServer) PartialUpdate(ctx context.Context, req *DbConfig) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PartialUpdate not implemented")
}
func (*UnimplementedRpcDbConfigServer) Delete(ctx context.Context, req *DbConfig) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Delete not implemented")
}
func (*UnimplementedRpcDbConfigServer) SelectOne(ctx context.Context, req *crud.DMLParam) (*DbConfig, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SelectOne not implemented")
}
func (*UnimplementedRpcDbConfigServer) SelectMany(req *crud.DMLParam, srv RpcDbConfig_SelectManyServer) error {
	return status.Errorf(codes.Unimplemented, "method SelectMany not implemented")
}
func (*UnimplementedRpcDbConfigServer) Query(req *crud.QueryParam, srv RpcDbConfig_QueryServer) error {
	return status.Errorf(codes.Unimplemented, "method Query not implemented")
}
func (*UnimplementedRpcDbConfigServer) QueryBatch(req *crud.QueryParam, srv RpcDbConfig_QueryBatchServer) error {
	return status.Errorf(codes.Unimplemented, "method QueryBatch not implemented")
}

func RegisterRpcDbConfigServer(s *grpc.Server, srv RpcDbConfigServer) {
	s.RegisterService(&_RpcDbConfig_serviceDesc, srv)
}

func _RpcDbConfig_Insert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbConfig)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbConfigServer).Insert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/config.RpcDbConfig/Insert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbConfigServer).Insert(ctx, req.(*DbConfig))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbConfig_Update_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbConfig)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbConfigServer).Update(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/config.RpcDbConfig/Update",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbConfigServer).Update(ctx, req.(*DbConfig))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbConfig_PartialUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbConfig)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbConfigServer).PartialUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/config.RpcDbConfig/PartialUpdate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbConfigServer).PartialUpdate(ctx, req.(*DbConfig))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbConfig_Delete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbConfig)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbConfigServer).Delete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/config.RpcDbConfig/Delete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbConfigServer).Delete(ctx, req.(*DbConfig))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbConfig_SelectOne_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(crud.DMLParam)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbConfigServer).SelectOne(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/config.RpcDbConfig/SelectOne",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbConfigServer).SelectOne(ctx, req.(*crud.DMLParam))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbConfig_SelectMany_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.DMLParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RpcDbConfigServer).SelectMany(m, &rpcDbConfigSelectManyServer{stream})
}

type RpcDbConfig_SelectManyServer interface {
	Send(*DbConfig) error
	grpc.ServerStream
}

type rpcDbConfigSelectManyServer struct {
	grpc.ServerStream
}

func (x *rpcDbConfigSelectManyServer) Send(m *DbConfig) error {
	return x.ServerStream.SendMsg(m)
}

func _RpcDbConfig_Query_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.QueryParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RpcDbConfigServer).Query(m, &rpcDbConfigQueryServer{stream})
}

type RpcDbConfig_QueryServer interface {
	Send(*DbConfig) error
	grpc.ServerStream
}

type rpcDbConfigQueryServer struct {
	grpc.ServerStream
}

func (x *rpcDbConfigQueryServer) Send(m *DbConfig) error {
	return x.ServerStream.SendMsg(m)
}

func _RpcDbConfig_QueryBatch_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.QueryParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RpcDbConfigServer).QueryBatch(m, &rpcDbConfigQueryBatchServer{stream})
}

type RpcDbConfig_QueryBatchServer interface {
	Send(*DbConfigList) error
	grpc.ServerStream
}

type rpcDbConfigQueryBatchServer struct {
	grpc.ServerStream
}

func (x *rpcDbConfigQueryBatchServer) Send(m *DbConfigList) error {
	return x.ServerStream.SendMsg(m)
}

var _RpcDbConfig_serviceDesc = grpc.ServiceDesc{
	ServiceName: "config.RpcDbConfig",
	HandlerType: (*RpcDbConfigServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Insert",
			Handler:    _RpcDbConfig_Insert_Handler,
		},
		{
			MethodName: "Update",
			Handler:    _RpcDbConfig_Update_Handler,
		},
		{
			MethodName: "PartialUpdate",
			Handler:    _RpcDbConfig_PartialUpdate_Handler,
		},
		{
			MethodName: "Delete",
			Handler:    _RpcDbConfig_Delete_Handler,
		},
		{
			MethodName: "SelectOne",
			Handler:    _RpcDbConfig_SelectOne_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "SelectMany",
			Handler:       _RpcDbConfig_SelectMany_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "Query",
			Handler:       _RpcDbConfig_Query_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "QueryBatch",
			Handler:       _RpcDbConfig_QueryBatch_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "config.rpc.proto",
}

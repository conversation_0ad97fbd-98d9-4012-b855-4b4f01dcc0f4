#!/bin/sh

#ygen for org
protoc --ygen-gogofaster_out=../../../ --ygen-gocrud_out=./org/ --ygen-gocrud-proto_out=. --ygen-sql_out=generatedsql org.proto
protoc -I=. -I=../goutil/crud --ygen-gogofaster_out=plugins=grpc:../../../ org.rpc.proto
protoc -I=. -I=../goutil/crud --ygen-gogofaster_out=../../../ org.list.proto

#ygen for image
protoc --ygen-gogofaster_out=../../../ --ygen-gocrud_out=./image/ --ygen-gocrud-proto_out=. --ygen-sql_out=generatedsql image.proto
protoc -I=. -I=../goutil/crud --ygen-gogofaster_out=plugins=grpc:../../../ image.rpc.proto
protoc -I=. -I=../goutil/crud --ygen-gogofaster_out=../../../ image.list.proto

#ygen for config
protoc --ygen-gogofaster_out=../../../ --ygen-gocrud_out=./config/ --ygen-gocrud-proto_out=. --ygen-sql_out=generatedsql config.proto
protoc -I=. -I=../goutil/crud --ygen-gogofaster_out=plugins=grpc:../../../ config.rpc.proto
protoc -I=. -I=../goutil/crud --ygen-gogofaster_out=../../../ config.list.proto

#ygen for user
protoc --ygen-gogofaster_out=../../../ --ygen-gocrud_out=./user/ --ygen-gocrud-proto_out=. --ygen-sql_out=generatedsql user.proto
protoc -I=. -I=../goutil/crud --ygen-gogofaster_out=plugins=grpc:../../../ user.rpc.proto
protoc -I=. -I=../goutil/crud --ygen-gogofaster_out=../../../ user.list.proto

#ygen for userPermission
protoc --ygen-gogofaster_out=../../../ --ygen-gocrud_out=./user/ --ygen-gocrud-proto_out=. --ygen-sql_out=generatedsql userPermission.proto
protoc -I=. -I=../goutil/crud --ygen-gogofaster_out=plugins=grpc:../../../ userPermission.rpc.proto
protoc -I=. -I=../goutil/crud --ygen-gogofaster_out=../../../ userPermission.list.proto

#ygen for userOrgPrivilege
protoc --ygen-gogofaster_out=../../../ --ygen-gocrud_out=./user/ --ygen-gocrud-proto_out=. --ygen-sql_out=generatedsql userOrgPrivilege.proto
protoc -I=. -I=../goutil/crud --ygen-gogofaster_out=plugins=grpc:../../../ userOrgPrivilege.rpc.proto
protoc -I=. -I=../goutil/crud --ygen-gogofaster_out=../../../ userOrgPrivilege.list.proto

#ygen for userOrgPrivilege
protoc --ygen-gogofaster_out=../../../ --ygen-gocrud_out=./user/ --ygen-gocrud-proto_out=. --ygen-sql_out=generatedsql userSession.proto
protoc -I=. -I=../goutil/crud --ygen-gogofaster_out=plugins=grpc:../../../ userSession.rpc.proto
protoc -I=. -I=../goutil/crud --ygen-gogofaster_out=../../../ userSession.list.proto

#ygen for user rpc api
protoc -I=. -I=../goutil/rpc -I=../goutil/crud --ygen-gogofaster_out=plugins=grpc:../../../ --ygen-gocrud_out=./user/ --ygen-gocrud-proto_out=. user.api.proto

#ygen for crudlog
protoc --ygen-gogofaster_out=../../../ --ygen-gocrud_out=./crudlog/ --ygen-gocrud-proto_out=. --ygen-sql_out=generatedsql crudlog.proto
protoc -I=. -I=../goutil/crud --ygen-gogofaster_out=plugins=grpc:../../../ crudlog.rpc.proto
protoc -I=. -I=../goutil/crud --ygen-gogofaster_out=../../../ crudlog.list.proto

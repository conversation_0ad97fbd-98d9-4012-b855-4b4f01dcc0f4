// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: crudlog.rpc.proto

package crudlog

import (
	context "context"
	fmt "fmt"
	crud "git.kicad99.com/ykit/goutil/crud"
	proto "github.com/gogo/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

func init() { proto.RegisterFile("crudlog.rpc.proto", fileDescriptor_d3fc62938dd1d354) }

var fileDescriptor_d3fc62938dd1d354 = []byte{
	// 265 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe2, 0x12, 0x4c, 0x2e, 0x2a, 0x4d,
	0xc9, 0xc9, 0x4f, 0xd7, 0x2b, 0x2a, 0x48, 0xd6, 0x2b, 0x28, 0xca, 0x2f, 0xc9, 0x17, 0x62, 0x87,
	0x0a, 0x49, 0x71, 0x81, 0x18, 0x10, 0x41, 0x29, 0x5e, 0x98, 0x3a, 0x08, 0x57, 0x08, 0xc6, 0xcd,
	0xc9, 0x2c, 0x2e, 0x81, 0x88, 0x19, 0xad, 0x60, 0xe4, 0xe2, 0x09, 0x2a, 0x48, 0x76, 0x49, 0x72,
	0x2e, 0x2a, 0x4d, 0xf1, 0xc9, 0x4f, 0x17, 0xd2, 0xe5, 0x62, 0xf3, 0xcc, 0x2b, 0x4e, 0x2d, 0x2a,
	0x11, 0x12, 0xd2, 0x83, 0xa9, 0x87, 0xcb, 0x4a, 0xf1, 0x83, 0xc5, 0xf4, 0x5c, 0x7c, 0x7d, 0x82,
	0x52, 0x8b, 0x4b, 0x73, 0x4a, 0x84, 0xf4, 0xb9, 0x58, 0x03, 0x4b, 0x53, 0x8b, 0x2a, 0x85, 0x04,
	0x20, 0x32, 0x60, 0x4e, 0x40, 0x62, 0x51, 0x62, 0xae, 0x14, 0x16, 0xfd, 0x06, 0x8c, 0x42, 0x16,
	0x5c, 0x5c, 0x60, 0x35, 0x4e, 0x89, 0x25, 0xc9, 0x19, 0x58, 0x74, 0x89, 0x61, 0xea, 0xf2, 0xc9,
	0x2c, 0x2e, 0x31, 0x60, 0x34, 0xda, 0xc8, 0xc8, 0xc5, 0x1b, 0x50, 0x44, 0x81, 0x5b, 0x8d, 0x61,
	0x6e, 0x15, 0x81, 0xc8, 0x04, 0x14, 0x65, 0x96, 0x65, 0xe6, 0xa4, 0xa6, 0xa7, 0xe2, 0x73, 0xaf,
	0x0d, 0x8a, 0x7b, 0xb1, 0xeb, 0xc4, 0xe9, 0x66, 0x27, 0xfb, 0x13, 0x8f, 0xe4, 0x18, 0x2f, 0x3c,
	0x92, 0x63, 0x7c, 0xf0, 0x48, 0x8e, 0x71, 0xc2, 0x63, 0x39, 0x86, 0x0b, 0x8f, 0xe5, 0x18, 0x6e,
	0x3c, 0x96, 0x63, 0x88, 0x52, 0x4d, 0xcf, 0x2c, 0xd1, 0xcb, 0xce, 0x4c, 0x4e, 0x4c, 0xb1, 0xb4,
	0xd4, 0x4b, 0xce, 0xcf, 0xd5, 0xaf, 0xcc, 0xce, 0x2c, 0xd1, 0x4f, 0x49, 0x2c, 0x49, 0x4c, 0x4a,
	0x2c, 0x4e, 0xd5, 0x87, 0x1a, 0x98, 0xc4, 0x06, 0x8e, 0x26, 0x63, 0x40, 0x00, 0x00, 0x00, 0xff,
	0xff, 0x41, 0x18, 0x52, 0x65, 0xf3, 0x01, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// RpcDbCrudLogClient is the client API for RpcDbCrudLog service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type RpcDbCrudLogClient interface {
	//插入一行数据
	Insert(ctx context.Context, in *DbCrudLog, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//根据条件查询数据
	Query(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbCrudLog_QueryClient, error)
	//根据条件查询数据,批量返回数据,crud.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbCrudLog_QueryBatchClient, error)
}

type rpcDbCrudLogClient struct {
	cc *grpc.ClientConn
}

func NewRpcDbCrudLogClient(cc *grpc.ClientConn) RpcDbCrudLogClient {
	return &rpcDbCrudLogClient{cc}
}

func (c *rpcDbCrudLogClient) Insert(ctx context.Context, in *DbCrudLog, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/crudlog.RpcDbCrudLog/Insert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbCrudLogClient) Query(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbCrudLog_QueryClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RpcDbCrudLog_serviceDesc.Streams[0], "/crudlog.RpcDbCrudLog/Query", opts...)
	if err != nil {
		return nil, err
	}
	x := &rpcDbCrudLogQueryClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type RpcDbCrudLog_QueryClient interface {
	Recv() (*DbCrudLog, error)
	grpc.ClientStream
}

type rpcDbCrudLogQueryClient struct {
	grpc.ClientStream
}

func (x *rpcDbCrudLogQueryClient) Recv() (*DbCrudLog, error) {
	m := new(DbCrudLog)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *rpcDbCrudLogClient) QueryBatch(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbCrudLog_QueryBatchClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RpcDbCrudLog_serviceDesc.Streams[1], "/crudlog.RpcDbCrudLog/QueryBatch", opts...)
	if err != nil {
		return nil, err
	}
	x := &rpcDbCrudLogQueryBatchClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type RpcDbCrudLog_QueryBatchClient interface {
	Recv() (*DbCrudLogList, error)
	grpc.ClientStream
}

type rpcDbCrudLogQueryBatchClient struct {
	grpc.ClientStream
}

func (x *rpcDbCrudLogQueryBatchClient) Recv() (*DbCrudLogList, error) {
	m := new(DbCrudLogList)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// RpcDbCrudLogServer is the server API for RpcDbCrudLog service.
type RpcDbCrudLogServer interface {
	//插入一行数据
	Insert(context.Context, *DbCrudLog) (*crud.DMLResult, error)
	//根据条件查询数据
	Query(*crud.QueryParam, RpcDbCrudLog_QueryServer) error
	//根据条件查询数据,批量返回数据,crud.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(*crud.QueryParam, RpcDbCrudLog_QueryBatchServer) error
}

// UnimplementedRpcDbCrudLogServer can be embedded to have forward compatible implementations.
type UnimplementedRpcDbCrudLogServer struct {
}

func (*UnimplementedRpcDbCrudLogServer) Insert(ctx context.Context, req *DbCrudLog) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Insert not implemented")
}
func (*UnimplementedRpcDbCrudLogServer) Query(req *crud.QueryParam, srv RpcDbCrudLog_QueryServer) error {
	return status.Errorf(codes.Unimplemented, "method Query not implemented")
}
func (*UnimplementedRpcDbCrudLogServer) QueryBatch(req *crud.QueryParam, srv RpcDbCrudLog_QueryBatchServer) error {
	return status.Errorf(codes.Unimplemented, "method QueryBatch not implemented")
}

func RegisterRpcDbCrudLogServer(s *grpc.Server, srv RpcDbCrudLogServer) {
	s.RegisterService(&_RpcDbCrudLog_serviceDesc, srv)
}

func _RpcDbCrudLog_Insert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbCrudLog)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbCrudLogServer).Insert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/crudlog.RpcDbCrudLog/Insert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbCrudLogServer).Insert(ctx, req.(*DbCrudLog))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbCrudLog_Query_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.QueryParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RpcDbCrudLogServer).Query(m, &rpcDbCrudLogQueryServer{stream})
}

type RpcDbCrudLog_QueryServer interface {
	Send(*DbCrudLog) error
	grpc.ServerStream
}

type rpcDbCrudLogQueryServer struct {
	grpc.ServerStream
}

func (x *rpcDbCrudLogQueryServer) Send(m *DbCrudLog) error {
	return x.ServerStream.SendMsg(m)
}

func _RpcDbCrudLog_QueryBatch_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.QueryParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RpcDbCrudLogServer).QueryBatch(m, &rpcDbCrudLogQueryBatchServer{stream})
}

type RpcDbCrudLog_QueryBatchServer interface {
	Send(*DbCrudLogList) error
	grpc.ServerStream
}

type rpcDbCrudLogQueryBatchServer struct {
	grpc.ServerStream
}

func (x *rpcDbCrudLogQueryBatchServer) Send(m *DbCrudLogList) error {
	return x.ServerStream.SendMsg(m)
}

var _RpcDbCrudLog_serviceDesc = grpc.ServiceDesc{
	ServiceName: "crudlog.RpcDbCrudLog",
	HandlerType: (*RpcDbCrudLogServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Insert",
			Handler:    _RpcDbCrudLog_Insert_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "Query",
			Handler:       _RpcDbCrudLog_Query_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "QueryBatch",
			Handler:       _RpcDbCrudLog_QueryBatch_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "crudlog.rpc.proto",
}

// PrpcDbCrudLogClient is the client API for PrpcDbCrudLog service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type PrpcDbCrudLogClient interface {
	//插入一行数据
	Insert(ctx context.Context, in *DbCrudLog, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//根据条件取得我有权限的数据
	Query(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbCrudLog_QueryClient, error)
	//根据条件取得我有权限的数据,批量返回数据,crud.PrivilegeParam.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbCrudLog_QueryBatchClient, error)
}

type prpcDbCrudLogClient struct {
	cc *grpc.ClientConn
}

func NewPrpcDbCrudLogClient(cc *grpc.ClientConn) PrpcDbCrudLogClient {
	return &prpcDbCrudLogClient{cc}
}

func (c *prpcDbCrudLogClient) Insert(ctx context.Context, in *DbCrudLog, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/crudlog.PrpcDbCrudLog/Insert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prpcDbCrudLogClient) Query(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbCrudLog_QueryClient, error) {
	stream, err := c.cc.NewStream(ctx, &_PrpcDbCrudLog_serviceDesc.Streams[0], "/crudlog.PrpcDbCrudLog/Query", opts...)
	if err != nil {
		return nil, err
	}
	x := &prpcDbCrudLogQueryClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type PrpcDbCrudLog_QueryClient interface {
	Recv() (*DbCrudLog, error)
	grpc.ClientStream
}

type prpcDbCrudLogQueryClient struct {
	grpc.ClientStream
}

func (x *prpcDbCrudLogQueryClient) Recv() (*DbCrudLog, error) {
	m := new(DbCrudLog)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *prpcDbCrudLogClient) QueryBatch(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbCrudLog_QueryBatchClient, error) {
	stream, err := c.cc.NewStream(ctx, &_PrpcDbCrudLog_serviceDesc.Streams[1], "/crudlog.PrpcDbCrudLog/QueryBatch", opts...)
	if err != nil {
		return nil, err
	}
	x := &prpcDbCrudLogQueryBatchClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type PrpcDbCrudLog_QueryBatchClient interface {
	Recv() (*DbCrudLogList, error)
	grpc.ClientStream
}

type prpcDbCrudLogQueryBatchClient struct {
	grpc.ClientStream
}

func (x *prpcDbCrudLogQueryBatchClient) Recv() (*DbCrudLogList, error) {
	m := new(DbCrudLogList)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// PrpcDbCrudLogServer is the server API for PrpcDbCrudLog service.
type PrpcDbCrudLogServer interface {
	//插入一行数据
	Insert(context.Context, *DbCrudLog) (*crud.DMLResult, error)
	//根据条件取得我有权限的数据
	Query(*crud.PrivilegeParam, PrpcDbCrudLog_QueryServer) error
	//根据条件取得我有权限的数据,批量返回数据,crud.PrivilegeParam.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(*crud.PrivilegeParam, PrpcDbCrudLog_QueryBatchServer) error
}

// UnimplementedPrpcDbCrudLogServer can be embedded to have forward compatible implementations.
type UnimplementedPrpcDbCrudLogServer struct {
}

func (*UnimplementedPrpcDbCrudLogServer) Insert(ctx context.Context, req *DbCrudLog) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Insert not implemented")
}
func (*UnimplementedPrpcDbCrudLogServer) Query(req *crud.PrivilegeParam, srv PrpcDbCrudLog_QueryServer) error {
	return status.Errorf(codes.Unimplemented, "method Query not implemented")
}
func (*UnimplementedPrpcDbCrudLogServer) QueryBatch(req *crud.PrivilegeParam, srv PrpcDbCrudLog_QueryBatchServer) error {
	return status.Errorf(codes.Unimplemented, "method QueryBatch not implemented")
}

func RegisterPrpcDbCrudLogServer(s *grpc.Server, srv PrpcDbCrudLogServer) {
	s.RegisterService(&_PrpcDbCrudLog_serviceDesc, srv)
}

func _PrpcDbCrudLog_Insert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbCrudLog)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrpcDbCrudLogServer).Insert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/crudlog.PrpcDbCrudLog/Insert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrpcDbCrudLogServer).Insert(ctx, req.(*DbCrudLog))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrpcDbCrudLog_Query_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.PrivilegeParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(PrpcDbCrudLogServer).Query(m, &prpcDbCrudLogQueryServer{stream})
}

type PrpcDbCrudLog_QueryServer interface {
	Send(*DbCrudLog) error
	grpc.ServerStream
}

type prpcDbCrudLogQueryServer struct {
	grpc.ServerStream
}

func (x *prpcDbCrudLogQueryServer) Send(m *DbCrudLog) error {
	return x.ServerStream.SendMsg(m)
}

func _PrpcDbCrudLog_QueryBatch_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.PrivilegeParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(PrpcDbCrudLogServer).QueryBatch(m, &prpcDbCrudLogQueryBatchServer{stream})
}

type PrpcDbCrudLog_QueryBatchServer interface {
	Send(*DbCrudLogList) error
	grpc.ServerStream
}

type prpcDbCrudLogQueryBatchServer struct {
	grpc.ServerStream
}

func (x *prpcDbCrudLogQueryBatchServer) Send(m *DbCrudLogList) error {
	return x.ServerStream.SendMsg(m)
}

var _PrpcDbCrudLog_serviceDesc = grpc.ServiceDesc{
	ServiceName: "crudlog.PrpcDbCrudLog",
	HandlerType: (*PrpcDbCrudLogServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Insert",
			Handler:    _PrpcDbCrudLog_Insert_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "Query",
			Handler:       _PrpcDbCrudLog_Query_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "QueryBatch",
			Handler:       _PrpcDbCrudLog_QueryBatch_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "crudlog.rpc.proto",
}

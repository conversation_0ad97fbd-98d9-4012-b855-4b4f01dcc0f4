// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: crudlog.list.proto

package crudlog

import (
	fmt "fmt"
	proto "github.com/gogo/protobuf/proto"
	io "io"
	math "math"
	math_bits "math/bits"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

// DbCrudLog list
type DbCrudLogList struct {
	//批次号，从0开始
	BatchNo uint32 `protobuf:"varint,1,opt,name=BatchNo,proto3" json:"BatchNo,omitempty" ykit:"nodb"`
	//rows offset,从0开始
	RowsOffset uint32 `protobuf:"varint,2,opt,name=RowsOffset,proto3" json:"RowsOffset,omitempty" ykit:"nodb"`
	//本批次数据
	Rows []*DbCrudLog `protobuf:"bytes,3,rep,name=Rows,proto3" json:"Rows,omitempty" ykit:"nodb"`
}

func (m *DbCrudLogList) Reset()         { *m = DbCrudLogList{} }
func (m *DbCrudLogList) String() string { return proto.CompactTextString(m) }
func (*DbCrudLogList) ProtoMessage()    {}
func (*DbCrudLogList) Descriptor() ([]byte, []int) {
	return fileDescriptor_92bfb6ee92a4ebe0, []int{0}
}
func (m *DbCrudLogList) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbCrudLogList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbCrudLogList.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbCrudLogList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbCrudLogList.Merge(m, src)
}
func (m *DbCrudLogList) XXX_Size() int {
	return m.Size()
}
func (m *DbCrudLogList) XXX_DiscardUnknown() {
	xxx_messageInfo_DbCrudLogList.DiscardUnknown(m)
}

var xxx_messageInfo_DbCrudLogList proto.InternalMessageInfo

func (m *DbCrudLogList) GetBatchNo() uint32 {
	if m != nil {
		return m.BatchNo
	}
	return 0
}

func (m *DbCrudLogList) GetRowsOffset() uint32 {
	if m != nil {
		return m.RowsOffset
	}
	return 0
}

func (m *DbCrudLogList) GetRows() []*DbCrudLog {
	if m != nil {
		return m.Rows
	}
	return nil
}

func init() {
	proto.RegisterType((*DbCrudLogList)(nil), "crudlog.DbCrudLogList")
}

func init() { proto.RegisterFile("crudlog.list.proto", fileDescriptor_92bfb6ee92a4ebe0) }

var fileDescriptor_92bfb6ee92a4ebe0 = []byte{
	// 199 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe2, 0x12, 0x4a, 0x2e, 0x2a, 0x4d,
	0xc9, 0xc9, 0x4f, 0xd7, 0xcb, 0xc9, 0x2c, 0x2e, 0xd1, 0x2b, 0x28, 0xca, 0x2f, 0xc9, 0x17, 0x62,
	0x87, 0x8a, 0x49, 0xf1, 0xc2, 0x24, 0xc1, 0xe2, 0x4a, 0x85, 0x5c, 0xbc, 0x2e, 0x49, 0xce, 0x45,
	0xa5, 0x29, 0x3e, 0xf9, 0xe9, 0x3e, 0x99, 0xc5, 0x25, 0x42, 0x12, 0x5c, 0xec, 0x4e, 0x89, 0x25,
	0xc9, 0x19, 0x7e, 0xf9, 0x12, 0x8c, 0x0a, 0x8c, 0x1a, 0xbc, 0x41, 0x30, 0xae, 0x90, 0x1c, 0x17,
	0x57, 0x50, 0x7e, 0x79, 0xb1, 0x7f, 0x5a, 0x5a, 0x71, 0x6a, 0x89, 0x04, 0x13, 0x58, 0x12, 0x49,
	0x44, 0x48, 0x8d, 0x8b, 0x05, 0xc4, 0x93, 0x60, 0x56, 0x60, 0xd6, 0xe0, 0x36, 0x12, 0xd2, 0x83,
	0x59, 0x04, 0x37, 0x3f, 0x08, 0x2c, 0xef, 0x64, 0x7f, 0xe2, 0x91, 0x1c, 0xe3, 0x85, 0x47, 0x72,
	0x8c, 0x0f, 0x1e, 0xc9, 0x31, 0x4e, 0x78, 0x2c, 0xc7, 0x70, 0xe1, 0xb1, 0x1c, 0xc3, 0x8d, 0xc7,
	0x72, 0x0c, 0x51, 0xaa, 0xe9, 0x99, 0x25, 0x7a, 0xd9, 0x99, 0xc9, 0x89, 0x29, 0x96, 0x96, 0x7a,
	0xc9, 0xf9, 0xb9, 0xfa, 0x95, 0xd9, 0x99, 0x25, 0xfa, 0x29, 0x89, 0x25, 0x89, 0x49, 0x89, 0xc5,
	0xa9, 0xfa, 0x50, 0x03, 0x93, 0xd8, 0xc0, 0x4e, 0x37, 0x06, 0x04, 0x00, 0x00, 0xff, 0xff, 0xbb,
	0x19, 0xc7, 0x17, 0xe8, 0x00, 0x00, 0x00,
}

func (m *DbCrudLogList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbCrudLogList) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbCrudLogList) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Rows) > 0 {
		for iNdEx := len(m.Rows) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Rows[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintCrudlogList(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x1a
		}
	}
	if m.RowsOffset != 0 {
		i = encodeVarintCrudlogList(dAtA, i, uint64(m.RowsOffset))
		i--
		dAtA[i] = 0x10
	}
	if m.BatchNo != 0 {
		i = encodeVarintCrudlogList(dAtA, i, uint64(m.BatchNo))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func encodeVarintCrudlogList(dAtA []byte, offset int, v uint64) int {
	offset -= sovCrudlogList(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *DbCrudLogList) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.BatchNo != 0 {
		n += 1 + sovCrudlogList(uint64(m.BatchNo))
	}
	if m.RowsOffset != 0 {
		n += 1 + sovCrudlogList(uint64(m.RowsOffset))
	}
	if len(m.Rows) > 0 {
		for _, e := range m.Rows {
			l = e.Size()
			n += 1 + l + sovCrudlogList(uint64(l))
		}
	}
	return n
}

func sovCrudlogList(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozCrudlogList(x uint64) (n int) {
	return sovCrudlogList(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *DbCrudLogList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCrudlogList
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DbCrudLogList: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DbCrudLogList: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field BatchNo", wireType)
			}
			m.BatchNo = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCrudlogList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BatchNo |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RowsOffset", wireType)
			}
			m.RowsOffset = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCrudlogList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RowsOffset |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rows", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCrudlogList
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCrudlogList
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCrudlogList
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rows = append(m.Rows, &DbCrudLog{})
			if err := m.Rows[len(m.Rows)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCrudlogList(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCrudlogList
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthCrudlogList
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipCrudlogList(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowCrudlogList
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowCrudlogList
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowCrudlogList
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthCrudlogList
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupCrudlogList
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthCrudlogList
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthCrudlogList        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowCrudlogList          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupCrudlogList = fmt.Errorf("proto: unexpected end of group")
)

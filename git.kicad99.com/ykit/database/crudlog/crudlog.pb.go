// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: crudlog.proto

package crudlog

import (
	fmt "fmt"
	proto "github.com/gogo/protobuf/proto"
	io "io"
	math "math"
	math_bits "math/bits"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

//用户crud log表
//@rpc crud pcrud
//@dbend PARTITION BY RANGE (UpdatedAt)
//@dbpost CREATE INDEX IF NOT EXISTS idxDbCrudLogOrgRID on DbCrudLog USING hash(OrgRID);
//@dbpost CREATE INDEX IF NOT EXISTS idxDbCrudLogUpdatedAt on DbCrudLog USING brin(UpdatedAt);
type DbCrudLog struct {
	//@db uuid
	//用户所属的群组
	OrgRID string `protobuf:"bytes,2,opt,name=OrgRID,proto3" json:"OrgRID,omitempty" ykit:"null"`
	//@db uuid
	//用户rid
	UserRID string `protobuf:"bytes,3,opt,name=UserRID,proto3" json:"UserRID,omitempty" ykit:"null"`
	//@db text
	//操作
	Operation string `protobuf:"bytes,4,opt,name=Operation,proto3" json:"Operation,omitempty" ykit:"null"`
	//@db jsonb
	//req proto json
	Req string `protobuf:"bytes,5,opt,name=Req,proto3" json:"Req,omitempty" ykit:"null"`
	//@db text
	//req option
	ReqOption string `protobuf:"bytes,6,opt,name=ReqOption,proto3" json:"ReqOption,omitempty" ykit:"null"`
	//@db text
	//ipinfo
	Ipinfo string `protobuf:"bytes,7,opt,name=Ipinfo,proto3" json:"Ipinfo,omitempty" ykit:"null"`
	//@db jsonb
	//note
	Note string `protobuf:"bytes,8,opt,name=Note,proto3" json:"Note,omitempty" ykit:"null"`
	//@db timestamp not null default now_utc()
	//数据最后修改时间
	UpdatedAt string `protobuf:"bytes,14,opt,name=UpdatedAt,proto3" json:"UpdatedAt,omitempty" ykit:"notnull"`
	//@db text
	//数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
	UpdatedDC string `protobuf:"bytes,15,opt,name=UpdatedDC,proto3" json:"UpdatedDC,omitempty" ykit:"null"`
}

func (m *DbCrudLog) Reset()         { *m = DbCrudLog{} }
func (m *DbCrudLog) String() string { return proto.CompactTextString(m) }
func (*DbCrudLog) ProtoMessage()    {}
func (*DbCrudLog) Descriptor() ([]byte, []int) {
	return fileDescriptor_d7e7f45a4c1f0c4f, []int{0}
}
func (m *DbCrudLog) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbCrudLog) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbCrudLog.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbCrudLog) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbCrudLog.Merge(m, src)
}
func (m *DbCrudLog) XXX_Size() int {
	return m.Size()
}
func (m *DbCrudLog) XXX_DiscardUnknown() {
	xxx_messageInfo_DbCrudLog.DiscardUnknown(m)
}

var xxx_messageInfo_DbCrudLog proto.InternalMessageInfo

func (m *DbCrudLog) GetOrgRID() string {
	if m != nil {
		return m.OrgRID
	}
	return ""
}

func (m *DbCrudLog) GetUserRID() string {
	if m != nil {
		return m.UserRID
	}
	return ""
}

func (m *DbCrudLog) GetOperation() string {
	if m != nil {
		return m.Operation
	}
	return ""
}

func (m *DbCrudLog) GetReq() string {
	if m != nil {
		return m.Req
	}
	return ""
}

func (m *DbCrudLog) GetReqOption() string {
	if m != nil {
		return m.ReqOption
	}
	return ""
}

func (m *DbCrudLog) GetIpinfo() string {
	if m != nil {
		return m.Ipinfo
	}
	return ""
}

func (m *DbCrudLog) GetNote() string {
	if m != nil {
		return m.Note
	}
	return ""
}

func (m *DbCrudLog) GetUpdatedAt() string {
	if m != nil {
		return m.UpdatedAt
	}
	return ""
}

func (m *DbCrudLog) GetUpdatedDC() string {
	if m != nil {
		return m.UpdatedDC
	}
	return ""
}

func init() {
	proto.RegisterType((*DbCrudLog)(nil), "crudlog.DbCrudLog")
}

func init() { proto.RegisterFile("crudlog.proto", fileDescriptor_d7e7f45a4c1f0c4f) }

var fileDescriptor_d7e7f45a4c1f0c4f = []byte{
	// 257 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe2, 0xe2, 0x4d, 0x2e, 0x2a, 0x4d,
	0xc9, 0xc9, 0x4f, 0xd7, 0x2b, 0x28, 0xca, 0x2f, 0xc9, 0x17, 0x62, 0x87, 0x72, 0x95, 0x3e, 0x33,
	0x72, 0x71, 0xba, 0x24, 0x39, 0x17, 0x95, 0xa6, 0xf8, 0xe4, 0xa7, 0x0b, 0x89, 0x71, 0xb1, 0xf9,
	0x17, 0xa5, 0x07, 0x79, 0xba, 0x48, 0x30, 0x29, 0x30, 0x6a, 0x70, 0x06, 0x41, 0x79, 0x42, 0x12,
	0x5c, 0xec, 0xa1, 0xc5, 0xa9, 0x45, 0x20, 0x09, 0x66, 0xb0, 0x04, 0x8c, 0x2b, 0x24, 0xc3, 0xc5,
	0xe9, 0x5f, 0x90, 0x5a, 0x94, 0x58, 0x92, 0x99, 0x9f, 0x27, 0xc1, 0x02, 0x96, 0x43, 0x08, 0x08,
	0x09, 0x70, 0x31, 0x07, 0xa5, 0x16, 0x4a, 0xb0, 0x82, 0xc5, 0x41, 0x4c, 0x90, 0xfa, 0xa0, 0xd4,
	0x42, 0xff, 0x02, 0xb0, 0x7a, 0x36, 0x88, 0x7a, 0xb8, 0x00, 0xc8, 0x7e, 0xcf, 0x82, 0xcc, 0xbc,
	0xb4, 0x7c, 0x09, 0x76, 0x88, 0xfd, 0x10, 0x9e, 0x90, 0x10, 0x17, 0x8b, 0x5f, 0x7e, 0x49, 0xaa,
	0x04, 0x07, 0x58, 0x14, 0xcc, 0x06, 0x99, 0x14, 0x5a, 0x90, 0x92, 0x58, 0x92, 0x9a, 0xe2, 0x58,
	0x22, 0xc1, 0x07, 0x31, 0x09, 0x2e, 0x80, 0x24, 0xeb, 0xe2, 0x2c, 0xc1, 0x8f, 0x22, 0xeb, 0xe2,
	0xec, 0x64, 0x7f, 0xe2, 0x91, 0x1c, 0xe3, 0x85, 0x47, 0x72, 0x8c, 0x0f, 0x1e, 0xc9, 0x31, 0x4e,
	0x78, 0x2c, 0xc7, 0x70, 0xe1, 0xb1, 0x1c, 0xc3, 0x8d, 0xc7, 0x72, 0x0c, 0x51, 0xaa, 0xe9, 0x99,
	0x25, 0x7a, 0xd9, 0x99, 0xc9, 0x89, 0x29, 0x96, 0x96, 0x7a, 0xc9, 0xf9, 0xb9, 0xfa, 0x95, 0xd9,
	0x99, 0x25, 0xfa, 0x29, 0x89, 0x25, 0x89, 0x49, 0x89, 0xc5, 0xa9, 0xfa, 0xd0, 0x60, 0x4b, 0x62,
	0x03, 0x07, 0xa3, 0x31, 0x20, 0x00, 0x00, 0xff, 0xff, 0xa5, 0xf3, 0x71, 0x5d, 0x57, 0x01, 0x00,
	0x00,
}

func (m *DbCrudLog) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbCrudLog) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbCrudLog) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.UpdatedDC) > 0 {
		i -= len(m.UpdatedDC)
		copy(dAtA[i:], m.UpdatedDC)
		i = encodeVarintCrudlog(dAtA, i, uint64(len(m.UpdatedDC)))
		i--
		dAtA[i] = 0x7a
	}
	if len(m.UpdatedAt) > 0 {
		i -= len(m.UpdatedAt)
		copy(dAtA[i:], m.UpdatedAt)
		i = encodeVarintCrudlog(dAtA, i, uint64(len(m.UpdatedAt)))
		i--
		dAtA[i] = 0x72
	}
	if len(m.Note) > 0 {
		i -= len(m.Note)
		copy(dAtA[i:], m.Note)
		i = encodeVarintCrudlog(dAtA, i, uint64(len(m.Note)))
		i--
		dAtA[i] = 0x42
	}
	if len(m.Ipinfo) > 0 {
		i -= len(m.Ipinfo)
		copy(dAtA[i:], m.Ipinfo)
		i = encodeVarintCrudlog(dAtA, i, uint64(len(m.Ipinfo)))
		i--
		dAtA[i] = 0x3a
	}
	if len(m.ReqOption) > 0 {
		i -= len(m.ReqOption)
		copy(dAtA[i:], m.ReqOption)
		i = encodeVarintCrudlog(dAtA, i, uint64(len(m.ReqOption)))
		i--
		dAtA[i] = 0x32
	}
	if len(m.Req) > 0 {
		i -= len(m.Req)
		copy(dAtA[i:], m.Req)
		i = encodeVarintCrudlog(dAtA, i, uint64(len(m.Req)))
		i--
		dAtA[i] = 0x2a
	}
	if len(m.Operation) > 0 {
		i -= len(m.Operation)
		copy(dAtA[i:], m.Operation)
		i = encodeVarintCrudlog(dAtA, i, uint64(len(m.Operation)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.UserRID) > 0 {
		i -= len(m.UserRID)
		copy(dAtA[i:], m.UserRID)
		i = encodeVarintCrudlog(dAtA, i, uint64(len(m.UserRID)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.OrgRID) > 0 {
		i -= len(m.OrgRID)
		copy(dAtA[i:], m.OrgRID)
		i = encodeVarintCrudlog(dAtA, i, uint64(len(m.OrgRID)))
		i--
		dAtA[i] = 0x12
	}
	return len(dAtA) - i, nil
}

func encodeVarintCrudlog(dAtA []byte, offset int, v uint64) int {
	offset -= sovCrudlog(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *DbCrudLog) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.OrgRID)
	if l > 0 {
		n += 1 + l + sovCrudlog(uint64(l))
	}
	l = len(m.UserRID)
	if l > 0 {
		n += 1 + l + sovCrudlog(uint64(l))
	}
	l = len(m.Operation)
	if l > 0 {
		n += 1 + l + sovCrudlog(uint64(l))
	}
	l = len(m.Req)
	if l > 0 {
		n += 1 + l + sovCrudlog(uint64(l))
	}
	l = len(m.ReqOption)
	if l > 0 {
		n += 1 + l + sovCrudlog(uint64(l))
	}
	l = len(m.Ipinfo)
	if l > 0 {
		n += 1 + l + sovCrudlog(uint64(l))
	}
	l = len(m.Note)
	if l > 0 {
		n += 1 + l + sovCrudlog(uint64(l))
	}
	l = len(m.UpdatedAt)
	if l > 0 {
		n += 1 + l + sovCrudlog(uint64(l))
	}
	l = len(m.UpdatedDC)
	if l > 0 {
		n += 1 + l + sovCrudlog(uint64(l))
	}
	return n
}

func sovCrudlog(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozCrudlog(x uint64) (n int) {
	return sovCrudlog(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *DbCrudLog) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCrudlog
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DbCrudLog: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DbCrudLog: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OrgRID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCrudlog
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCrudlog
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthCrudlog
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OrgRID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UserRID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCrudlog
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCrudlog
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthCrudlog
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UserRID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Operation", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCrudlog
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCrudlog
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthCrudlog
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Operation = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Req", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCrudlog
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCrudlog
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthCrudlog
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Req = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ReqOption", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCrudlog
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCrudlog
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthCrudlog
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ReqOption = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Ipinfo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCrudlog
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCrudlog
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthCrudlog
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Ipinfo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Note", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCrudlog
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCrudlog
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthCrudlog
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Note = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 14:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UpdatedAt", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCrudlog
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCrudlog
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthCrudlog
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UpdatedAt = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 15:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UpdatedDC", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCrudlog
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCrudlog
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthCrudlog
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UpdatedDC = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCrudlog(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCrudlog
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthCrudlog
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipCrudlog(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowCrudlog
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowCrudlog
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowCrudlog
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthCrudlog
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupCrudlog
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthCrudlog
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthCrudlog        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowCrudlog          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupCrudlog = fmt.Errorf("proto: unexpected end of group")
)

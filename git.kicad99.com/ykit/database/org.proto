syntax = "proto3";

package org;

option go_package = "git.kicad99.com/ykit/database/org";

//群组/组织结构表
//@rpc crud pcrud
//@dbpost DO $$ BEGIN BEGIN
//@dbpost   ALTER TABLE DbOrg ADD CONSTRAINT fkDbOrgParentRID  FOREIGN KEY (OrgRID) REFERENCES DbOrg(RID);
//@dbpost   EXCEPTION WHEN duplicate_object THEN --do nothing
//@dbpost END; END $$;
//@dbpost CREATE INDEX IF NOT EXISTS idxDbOrgCreatorRID on DbOrg USING hash(CreatorRID);
//@dbpost INSERT INTO dborg(rid, orgid, orgtype, sortvalue, shortname, fullname, note, setting, OrgRID, updatedat, updateddc)VALUES('00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', 0, 100, 'root', 'root', '', '{}'::jsonb, NULL, now_utc(), '')  ON CONFLICT  DO NOTHING;
message DbOrg {
  //@db uuid primary key
  //行ID
  string RID = 1;

  //@db varchar(64) unique not null
  //组织机构自编号
  string OrgID = 2;

  //@db int default 0
  int32 OrgType = 3;

  //@db int default 100
  //排序值,由用户指定,不用管拼音或者其它语种的排序
  int32 SortValue = 4;

  //@db varchar(32) not null
  //机构名称,缩写
  string ShortName = 5;

  //@db varchar(256)
  //机构名称,全称
  string FullName = 6;

  //@db text
  //机构描述/备注信息
  string Note = 7;

  //@db jsonb default  '{}'::jsonb
  // 其它设置，可以在里面扩展需要用到的其它信息
  string Setting = 8;
  
  //@db uuid
  //创建者的rid
  string CreatorRID = 9;

  //@db uuid 
  //此组织的上级机构
  string OrgRID = 11;

  //@db timestamp not null default now_utc()
  //数据最后修改时间
  string UpdatedAt = 14;

  //@db text
  //数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
  string UpdatedDC = 15;
}

syntax = "proto3";

package user;

option go_package = "git.kicad99.com/ykit/database/user";

//用户群组权限表
//@rpc crud pcrud
//@dbpost CREATE INDEX IF NOT EXISTS idxDbUserOrgPrivilegeUserRID on DbUserOrgPrivilege USING hash(UserRID);
//@dbpost INSERT INTO dbuserorgprivilege(rid, userrid, orgrid, includechildren, setting, updatedat, updateddc)VALUES('00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', 1, '{}'::jsonb, now_utc(), '') ON CONFLICT  DO NOTHING;
message DbUserOrgPrivilege {
  //@db uuid primary key
  //行ID
  string RID = 1;

  //@db uuid not null REFERENCES DbUser(RID) ON DELETE CASCADE
  //用户rid
  string UserRID = 3;

  //@db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
  //有权限的群组
  string OrgRID = 4;

  //@db int default 0
  //是否包含下级群组
  int32 IncludeChildren = 5;

  //@db jsonb not null default  '{}'::jsonb
  // 其它设置，可以在里面扩展需要用到的其它信息
  string Setting = 8;

  //@db timestamp not null default now_utc()
  //数据最后修改时间
  string UpdatedAt = 14;

  //@db text
  //数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
  string UpdatedDC = 15;
}

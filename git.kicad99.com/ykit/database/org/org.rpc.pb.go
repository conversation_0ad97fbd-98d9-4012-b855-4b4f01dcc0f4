// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: org.rpc.proto

package org

import (
	context "context"
	fmt "fmt"
	crud "git.kicad99.com/ykit/goutil/crud"
	proto "github.com/gogo/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

func init() { proto.RegisterFile("org.rpc.proto", fileDescriptor_e2f768ee292cf115) }

var fileDescriptor_e2f768ee292cf115 = []byte{
	// 317 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe4, 0xd3, 0xcf, 0x4b, 0x02, 0x41,
	0x14, 0x07, 0x70, 0xd7, 0x48, 0xf2, 0x81, 0x16, 0x43, 0xa7, 0x3d, 0x2c, 0x74, 0x10, 0xa1, 0x62,
	0x15, 0xeb, 0x22, 0xdd, 0xc4, 0x4b, 0xa0, 0xb8, 0x19, 0x5d, 0xba, 0x8d, 0xb3, 0x8f, 0x6d, 0x70,
	0x75, 0x96, 0xb7, 0xcf, 0xc0, 0xff, 0xa2, 0x3f, 0xab, 0xa3, 0xc7, 0x2e, 0x41, 0x28, 0xf4, 0x77,
	0xc4, 0xee, 0xd0, 0x2f, 0x5a, 0xac, 0x7b, 0xb7, 0xf7, 0xe3, 0xf3, 0xe6, 0xf0, 0x85, 0x81, 0x9a,
	0xa1, 0xc8, 0xa7, 0x44, 0xf9, 0x09, 0x19, 0x36, 0x62, 0xc7, 0x50, 0xe4, 0x82, 0xa2, 0x45, 0x68,
	0x07, 0x6e, 0x35, 0xdb, 0xdb, 0xb2, 0x9e, 0x95, 0xb1, 0x4e, 0xd9, 0xf6, 0x9d, 0xe7, 0x32, 0xec,
	0x8d, 0x13, 0xd5, 0x9f, 0x8c, 0x28, 0x12, 0x0d, 0xa8, 0x5c, 0xce, 0x53, 0x24, 0x16, 0xe0, 0x67,
	0x2e, 0x9f, 0xba, 0xfb, 0x7e, 0xfe, 0x54, 0x7f, 0x38, 0x18, 0x63, 0xba, 0x88, 0x39, 0x63, 0x37,
	0x49, 0x28, 0x19, 0xb7, 0xb3, 0x53, 0xa8, 0x05, 0x92, 0x58, 0xcb, 0xf8, 0x2f, 0xba, 0x01, 0x95,
	0x3e, 0xc6, 0xf8, 0x1b, 0x6b, 0x42, 0xf5, 0x1a, 0x63, 0x54, 0x3c, 0x9a, 0xa3, 0xa8, 0x7f, 0x6c,
	0x03, 0x49, 0x72, 0xe6, 0x7e, 0xb9, 0x14, 0xc7, 0x00, 0x16, 0x0e, 0xe5, 0x7c, 0xb9, 0x4d, 0xb6,
	0x1d, 0xd1, 0x84, 0xdd, 0xab, 0x05, 0xd2, 0x52, 0x1c, 0x58, 0x96, 0x37, 0x45, 0xb0, 0x0d, 0x90,
	0xef, 0x7a, 0x92, 0xd5, 0x5d, 0x81, 0xae, 0x7f, 0xea, 0x81, 0x4e, 0xb9, 0xed, 0x74, 0x5e, 0xcb,
	0x50, 0x0d, 0xe8, 0xbf, 0x07, 0x7c, 0xf2, 0x1e, 0xf0, 0xa1, 0x65, 0x01, 0xe9, 0x7b, 0x1d, 0x63,
	0x84, 0x45, 0xf8, 0xfc, 0x5b, 0xc8, 0xc5, 0x17, 0x3f, 0x82, 0xee, 0x5d, 0x3c, 0xae, 0x3d, 0x67,
	0xb5, 0xf6, 0x9c, 0x97, 0xb5, 0xe7, 0x3c, 0x6c, 0xbc, 0xd2, 0x6a, 0xe3, 0x95, 0x9e, 0x36, 0x5e,
	0xe9, 0xf6, 0x28, 0xd2, 0xec, 0x4f, 0xb5, 0x92, 0x61, 0xb7, 0xeb, 0x2b, 0x33, 0x6b, 0x2d, 0xa7,
	0x9a, 0x5b, 0xa1, 0x64, 0x39, 0x91, 0x29, 0xb6, 0x0c, 0x45, 0x93, 0x4a, 0xfe, 0x19, 0xce, 0xde,
	0x02, 0x00, 0x00, 0xff, 0xff, 0xd3, 0x5d, 0xf3, 0x5e, 0x49, 0x03, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// RpcDbOrgClient is the client API for RpcDbOrg service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type RpcDbOrgClient interface {
	//插入一行数据
	Insert(ctx context.Context, in *DbOrg, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,全量修改一行数据
	Update(ctx context.Context, in *DbOrg, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
	PartialUpdate(ctx context.Context, in *DbOrg, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,删除一行数据
	Delete(ctx context.Context, in *DbOrg, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
	SelectOne(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (*DbOrg, error)
	//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
	SelectMany(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (RpcDbOrg_SelectManyClient, error)
	//根据条件查询数据
	Query(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbOrg_QueryClient, error)
	//根据条件查询数据,批量返回数据,crud.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbOrg_QueryBatchClient, error)
}

type rpcDbOrgClient struct {
	cc *grpc.ClientConn
}

func NewRpcDbOrgClient(cc *grpc.ClientConn) RpcDbOrgClient {
	return &rpcDbOrgClient{cc}
}

func (c *rpcDbOrgClient) Insert(ctx context.Context, in *DbOrg, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/org.RpcDbOrg/Insert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbOrgClient) Update(ctx context.Context, in *DbOrg, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/org.RpcDbOrg/Update", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbOrgClient) PartialUpdate(ctx context.Context, in *DbOrg, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/org.RpcDbOrg/PartialUpdate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbOrgClient) Delete(ctx context.Context, in *DbOrg, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/org.RpcDbOrg/Delete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbOrgClient) SelectOne(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (*DbOrg, error) {
	out := new(DbOrg)
	err := c.cc.Invoke(ctx, "/org.RpcDbOrg/SelectOne", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbOrgClient) SelectMany(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (RpcDbOrg_SelectManyClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RpcDbOrg_serviceDesc.Streams[0], "/org.RpcDbOrg/SelectMany", opts...)
	if err != nil {
		return nil, err
	}
	x := &rpcDbOrgSelectManyClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type RpcDbOrg_SelectManyClient interface {
	Recv() (*DbOrg, error)
	grpc.ClientStream
}

type rpcDbOrgSelectManyClient struct {
	grpc.ClientStream
}

func (x *rpcDbOrgSelectManyClient) Recv() (*DbOrg, error) {
	m := new(DbOrg)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *rpcDbOrgClient) Query(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbOrg_QueryClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RpcDbOrg_serviceDesc.Streams[1], "/org.RpcDbOrg/Query", opts...)
	if err != nil {
		return nil, err
	}
	x := &rpcDbOrgQueryClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type RpcDbOrg_QueryClient interface {
	Recv() (*DbOrg, error)
	grpc.ClientStream
}

type rpcDbOrgQueryClient struct {
	grpc.ClientStream
}

func (x *rpcDbOrgQueryClient) Recv() (*DbOrg, error) {
	m := new(DbOrg)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *rpcDbOrgClient) QueryBatch(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbOrg_QueryBatchClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RpcDbOrg_serviceDesc.Streams[2], "/org.RpcDbOrg/QueryBatch", opts...)
	if err != nil {
		return nil, err
	}
	x := &rpcDbOrgQueryBatchClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type RpcDbOrg_QueryBatchClient interface {
	Recv() (*DbOrgList, error)
	grpc.ClientStream
}

type rpcDbOrgQueryBatchClient struct {
	grpc.ClientStream
}

func (x *rpcDbOrgQueryBatchClient) Recv() (*DbOrgList, error) {
	m := new(DbOrgList)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// RpcDbOrgServer is the server API for RpcDbOrg service.
type RpcDbOrgServer interface {
	//插入一行数据
	Insert(context.Context, *DbOrg) (*crud.DMLResult, error)
	//以RID为条件,全量修改一行数据
	Update(context.Context, *DbOrg) (*crud.DMLResult, error)
	//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
	PartialUpdate(context.Context, *DbOrg) (*crud.DMLResult, error)
	//以RID为条件,删除一行数据
	Delete(context.Context, *DbOrg) (*crud.DMLResult, error)
	//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
	SelectOne(context.Context, *crud.DMLParam) (*DbOrg, error)
	//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
	SelectMany(*crud.DMLParam, RpcDbOrg_SelectManyServer) error
	//根据条件查询数据
	Query(*crud.QueryParam, RpcDbOrg_QueryServer) error
	//根据条件查询数据,批量返回数据,crud.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(*crud.QueryParam, RpcDbOrg_QueryBatchServer) error
}

// UnimplementedRpcDbOrgServer can be embedded to have forward compatible implementations.
type UnimplementedRpcDbOrgServer struct {
}

func (*UnimplementedRpcDbOrgServer) Insert(ctx context.Context, req *DbOrg) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Insert not implemented")
}
func (*UnimplementedRpcDbOrgServer) Update(ctx context.Context, req *DbOrg) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Update not implemented")
}
func (*UnimplementedRpcDbOrgServer) PartialUpdate(ctx context.Context, req *DbOrg) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PartialUpdate not implemented")
}
func (*UnimplementedRpcDbOrgServer) Delete(ctx context.Context, req *DbOrg) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Delete not implemented")
}
func (*UnimplementedRpcDbOrgServer) SelectOne(ctx context.Context, req *crud.DMLParam) (*DbOrg, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SelectOne not implemented")
}
func (*UnimplementedRpcDbOrgServer) SelectMany(req *crud.DMLParam, srv RpcDbOrg_SelectManyServer) error {
	return status.Errorf(codes.Unimplemented, "method SelectMany not implemented")
}
func (*UnimplementedRpcDbOrgServer) Query(req *crud.QueryParam, srv RpcDbOrg_QueryServer) error {
	return status.Errorf(codes.Unimplemented, "method Query not implemented")
}
func (*UnimplementedRpcDbOrgServer) QueryBatch(req *crud.QueryParam, srv RpcDbOrg_QueryBatchServer) error {
	return status.Errorf(codes.Unimplemented, "method QueryBatch not implemented")
}

func RegisterRpcDbOrgServer(s *grpc.Server, srv RpcDbOrgServer) {
	s.RegisterService(&_RpcDbOrg_serviceDesc, srv)
}

func _RpcDbOrg_Insert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbOrg)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbOrgServer).Insert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/org.RpcDbOrg/Insert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbOrgServer).Insert(ctx, req.(*DbOrg))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbOrg_Update_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbOrg)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbOrgServer).Update(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/org.RpcDbOrg/Update",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbOrgServer).Update(ctx, req.(*DbOrg))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbOrg_PartialUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbOrg)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbOrgServer).PartialUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/org.RpcDbOrg/PartialUpdate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbOrgServer).PartialUpdate(ctx, req.(*DbOrg))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbOrg_Delete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbOrg)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbOrgServer).Delete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/org.RpcDbOrg/Delete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbOrgServer).Delete(ctx, req.(*DbOrg))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbOrg_SelectOne_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(crud.DMLParam)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbOrgServer).SelectOne(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/org.RpcDbOrg/SelectOne",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbOrgServer).SelectOne(ctx, req.(*crud.DMLParam))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbOrg_SelectMany_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.DMLParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RpcDbOrgServer).SelectMany(m, &rpcDbOrgSelectManyServer{stream})
}

type RpcDbOrg_SelectManyServer interface {
	Send(*DbOrg) error
	grpc.ServerStream
}

type rpcDbOrgSelectManyServer struct {
	grpc.ServerStream
}

func (x *rpcDbOrgSelectManyServer) Send(m *DbOrg) error {
	return x.ServerStream.SendMsg(m)
}

func _RpcDbOrg_Query_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.QueryParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RpcDbOrgServer).Query(m, &rpcDbOrgQueryServer{stream})
}

type RpcDbOrg_QueryServer interface {
	Send(*DbOrg) error
	grpc.ServerStream
}

type rpcDbOrgQueryServer struct {
	grpc.ServerStream
}

func (x *rpcDbOrgQueryServer) Send(m *DbOrg) error {
	return x.ServerStream.SendMsg(m)
}

func _RpcDbOrg_QueryBatch_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.QueryParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RpcDbOrgServer).QueryBatch(m, &rpcDbOrgQueryBatchServer{stream})
}

type RpcDbOrg_QueryBatchServer interface {
	Send(*DbOrgList) error
	grpc.ServerStream
}

type rpcDbOrgQueryBatchServer struct {
	grpc.ServerStream
}

func (x *rpcDbOrgQueryBatchServer) Send(m *DbOrgList) error {
	return x.ServerStream.SendMsg(m)
}

var _RpcDbOrg_serviceDesc = grpc.ServiceDesc{
	ServiceName: "org.RpcDbOrg",
	HandlerType: (*RpcDbOrgServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Insert",
			Handler:    _RpcDbOrg_Insert_Handler,
		},
		{
			MethodName: "Update",
			Handler:    _RpcDbOrg_Update_Handler,
		},
		{
			MethodName: "PartialUpdate",
			Handler:    _RpcDbOrg_PartialUpdate_Handler,
		},
		{
			MethodName: "Delete",
			Handler:    _RpcDbOrg_Delete_Handler,
		},
		{
			MethodName: "SelectOne",
			Handler:    _RpcDbOrg_SelectOne_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "SelectMany",
			Handler:       _RpcDbOrg_SelectMany_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "Query",
			Handler:       _RpcDbOrg_Query_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "QueryBatch",
			Handler:       _RpcDbOrg_QueryBatch_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "org.rpc.proto",
}

// PrpcDbOrgClient is the client API for PrpcDbOrg service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type PrpcDbOrgClient interface {
	//插入一行数据
	Insert(ctx context.Context, in *DbOrg, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,全量修改一行数据
	Update(ctx context.Context, in *DbOrg, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
	PartialUpdate(ctx context.Context, in *DbOrg, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,删除一行数据
	Delete(ctx context.Context, in *DbOrg, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
	SelectOne(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (*DbOrg, error)
	//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
	SelectMany(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (PrpcDbOrg_SelectManyClient, error)
	//根据条件取得我有权限的数据
	Query(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbOrg_QueryClient, error)
	//根据条件取得我有权限的数据,批量返回数据,crud.PrivilegeParam.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbOrg_QueryBatchClient, error)
}

type prpcDbOrgClient struct {
	cc *grpc.ClientConn
}

func NewPrpcDbOrgClient(cc *grpc.ClientConn) PrpcDbOrgClient {
	return &prpcDbOrgClient{cc}
}

func (c *prpcDbOrgClient) Insert(ctx context.Context, in *DbOrg, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/org.PrpcDbOrg/Insert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prpcDbOrgClient) Update(ctx context.Context, in *DbOrg, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/org.PrpcDbOrg/Update", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prpcDbOrgClient) PartialUpdate(ctx context.Context, in *DbOrg, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/org.PrpcDbOrg/PartialUpdate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prpcDbOrgClient) Delete(ctx context.Context, in *DbOrg, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/org.PrpcDbOrg/Delete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prpcDbOrgClient) SelectOne(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (*DbOrg, error) {
	out := new(DbOrg)
	err := c.cc.Invoke(ctx, "/org.PrpcDbOrg/SelectOne", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prpcDbOrgClient) SelectMany(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (PrpcDbOrg_SelectManyClient, error) {
	stream, err := c.cc.NewStream(ctx, &_PrpcDbOrg_serviceDesc.Streams[0], "/org.PrpcDbOrg/SelectMany", opts...)
	if err != nil {
		return nil, err
	}
	x := &prpcDbOrgSelectManyClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type PrpcDbOrg_SelectManyClient interface {
	Recv() (*DbOrg, error)
	grpc.ClientStream
}

type prpcDbOrgSelectManyClient struct {
	grpc.ClientStream
}

func (x *prpcDbOrgSelectManyClient) Recv() (*DbOrg, error) {
	m := new(DbOrg)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *prpcDbOrgClient) Query(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbOrg_QueryClient, error) {
	stream, err := c.cc.NewStream(ctx, &_PrpcDbOrg_serviceDesc.Streams[1], "/org.PrpcDbOrg/Query", opts...)
	if err != nil {
		return nil, err
	}
	x := &prpcDbOrgQueryClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type PrpcDbOrg_QueryClient interface {
	Recv() (*DbOrg, error)
	grpc.ClientStream
}

type prpcDbOrgQueryClient struct {
	grpc.ClientStream
}

func (x *prpcDbOrgQueryClient) Recv() (*DbOrg, error) {
	m := new(DbOrg)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *prpcDbOrgClient) QueryBatch(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbOrg_QueryBatchClient, error) {
	stream, err := c.cc.NewStream(ctx, &_PrpcDbOrg_serviceDesc.Streams[2], "/org.PrpcDbOrg/QueryBatch", opts...)
	if err != nil {
		return nil, err
	}
	x := &prpcDbOrgQueryBatchClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type PrpcDbOrg_QueryBatchClient interface {
	Recv() (*DbOrgList, error)
	grpc.ClientStream
}

type prpcDbOrgQueryBatchClient struct {
	grpc.ClientStream
}

func (x *prpcDbOrgQueryBatchClient) Recv() (*DbOrgList, error) {
	m := new(DbOrgList)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// PrpcDbOrgServer is the server API for PrpcDbOrg service.
type PrpcDbOrgServer interface {
	//插入一行数据
	Insert(context.Context, *DbOrg) (*crud.DMLResult, error)
	//以RID为条件,全量修改一行数据
	Update(context.Context, *DbOrg) (*crud.DMLResult, error)
	//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
	PartialUpdate(context.Context, *DbOrg) (*crud.DMLResult, error)
	//以RID为条件,删除一行数据
	Delete(context.Context, *DbOrg) (*crud.DMLResult, error)
	//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
	SelectOne(context.Context, *crud.DMLParam) (*DbOrg, error)
	//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
	SelectMany(*crud.DMLParam, PrpcDbOrg_SelectManyServer) error
	//根据条件取得我有权限的数据
	Query(*crud.PrivilegeParam, PrpcDbOrg_QueryServer) error
	//根据条件取得我有权限的数据,批量返回数据,crud.PrivilegeParam.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(*crud.PrivilegeParam, PrpcDbOrg_QueryBatchServer) error
}

// UnimplementedPrpcDbOrgServer can be embedded to have forward compatible implementations.
type UnimplementedPrpcDbOrgServer struct {
}

func (*UnimplementedPrpcDbOrgServer) Insert(ctx context.Context, req *DbOrg) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Insert not implemented")
}
func (*UnimplementedPrpcDbOrgServer) Update(ctx context.Context, req *DbOrg) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Update not implemented")
}
func (*UnimplementedPrpcDbOrgServer) PartialUpdate(ctx context.Context, req *DbOrg) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PartialUpdate not implemented")
}
func (*UnimplementedPrpcDbOrgServer) Delete(ctx context.Context, req *DbOrg) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Delete not implemented")
}
func (*UnimplementedPrpcDbOrgServer) SelectOne(ctx context.Context, req *crud.DMLParam) (*DbOrg, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SelectOne not implemented")
}
func (*UnimplementedPrpcDbOrgServer) SelectMany(req *crud.DMLParam, srv PrpcDbOrg_SelectManyServer) error {
	return status.Errorf(codes.Unimplemented, "method SelectMany not implemented")
}
func (*UnimplementedPrpcDbOrgServer) Query(req *crud.PrivilegeParam, srv PrpcDbOrg_QueryServer) error {
	return status.Errorf(codes.Unimplemented, "method Query not implemented")
}
func (*UnimplementedPrpcDbOrgServer) QueryBatch(req *crud.PrivilegeParam, srv PrpcDbOrg_QueryBatchServer) error {
	return status.Errorf(codes.Unimplemented, "method QueryBatch not implemented")
}

func RegisterPrpcDbOrgServer(s *grpc.Server, srv PrpcDbOrgServer) {
	s.RegisterService(&_PrpcDbOrg_serviceDesc, srv)
}

func _PrpcDbOrg_Insert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbOrg)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrpcDbOrgServer).Insert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/org.PrpcDbOrg/Insert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrpcDbOrgServer).Insert(ctx, req.(*DbOrg))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrpcDbOrg_Update_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbOrg)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrpcDbOrgServer).Update(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/org.PrpcDbOrg/Update",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrpcDbOrgServer).Update(ctx, req.(*DbOrg))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrpcDbOrg_PartialUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbOrg)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrpcDbOrgServer).PartialUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/org.PrpcDbOrg/PartialUpdate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrpcDbOrgServer).PartialUpdate(ctx, req.(*DbOrg))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrpcDbOrg_Delete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbOrg)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrpcDbOrgServer).Delete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/org.PrpcDbOrg/Delete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrpcDbOrgServer).Delete(ctx, req.(*DbOrg))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrpcDbOrg_SelectOne_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(crud.DMLParam)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrpcDbOrgServer).SelectOne(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/org.PrpcDbOrg/SelectOne",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrpcDbOrgServer).SelectOne(ctx, req.(*crud.DMLParam))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrpcDbOrg_SelectMany_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.DMLParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(PrpcDbOrgServer).SelectMany(m, &prpcDbOrgSelectManyServer{stream})
}

type PrpcDbOrg_SelectManyServer interface {
	Send(*DbOrg) error
	grpc.ServerStream
}

type prpcDbOrgSelectManyServer struct {
	grpc.ServerStream
}

func (x *prpcDbOrgSelectManyServer) Send(m *DbOrg) error {
	return x.ServerStream.SendMsg(m)
}

func _PrpcDbOrg_Query_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.PrivilegeParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(PrpcDbOrgServer).Query(m, &prpcDbOrgQueryServer{stream})
}

type PrpcDbOrg_QueryServer interface {
	Send(*DbOrg) error
	grpc.ServerStream
}

type prpcDbOrgQueryServer struct {
	grpc.ServerStream
}

func (x *prpcDbOrgQueryServer) Send(m *DbOrg) error {
	return x.ServerStream.SendMsg(m)
}

func _PrpcDbOrg_QueryBatch_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.PrivilegeParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(PrpcDbOrgServer).QueryBatch(m, &prpcDbOrgQueryBatchServer{stream})
}

type PrpcDbOrg_QueryBatchServer interface {
	Send(*DbOrgList) error
	grpc.ServerStream
}

type prpcDbOrgQueryBatchServer struct {
	grpc.ServerStream
}

func (x *prpcDbOrgQueryBatchServer) Send(m *DbOrgList) error {
	return x.ServerStream.SendMsg(m)
}

var _PrpcDbOrg_serviceDesc = grpc.ServiceDesc{
	ServiceName: "org.PrpcDbOrg",
	HandlerType: (*PrpcDbOrgServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Insert",
			Handler:    _PrpcDbOrg_Insert_Handler,
		},
		{
			MethodName: "Update",
			Handler:    _PrpcDbOrg_Update_Handler,
		},
		{
			MethodName: "PartialUpdate",
			Handler:    _PrpcDbOrg_PartialUpdate_Handler,
		},
		{
			MethodName: "Delete",
			Handler:    _PrpcDbOrg_Delete_Handler,
		},
		{
			MethodName: "SelectOne",
			Handler:    _PrpcDbOrg_SelectOne_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "SelectMany",
			Handler:       _PrpcDbOrg_SelectMany_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "Query",
			Handler:       _PrpcDbOrg_Query_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "QueryBatch",
			Handler:       _PrpcDbOrg_QueryBatch_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "org.rpc.proto",
}

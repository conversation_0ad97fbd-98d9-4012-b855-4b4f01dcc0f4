// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: org.proto

package org

import (
	fmt "fmt"
	proto "github.com/gogo/protobuf/proto"
	io "io"
	math "math"
	math_bits "math/bits"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

//群组/组织结构表
//@rpc crud pcrud
//@dbpost DO $$ BEGIN BEGIN
//@dbpost   ALTER TABLE DbOrg ADD CONSTRAINT fkDbOrgParentRID  FOREIGN KEY (OrgRID) REFERENCES DbOrg(RID);
//@dbpost   EXCEPTION WHEN duplicate_object THEN --do nothing
//@dbpost END; END $$;
//@dbpost CREATE INDEX IF NOT EXISTS idxDbOrgCreatorRID on DbOrg USING hash(CreatorRID);
//@dbpost INSERT INTO dborg(rid, orgid, orgtype, sortvalue, shortname, fullname, note, setting, OrgRID, updatedat, updateddc)VALUES('00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', 0, 100, 'root', 'root', '', '{}'::jsonb, NULL, now_utc(), '')  ON CONFLICT  DO NOTHING;
type DbOrg struct {
	//@db uuid primary key
	//行ID
	RID string `protobuf:"bytes,1,opt,name=RID,proto3" json:"RID,omitempty" ykit:"prk"`
	//@db varchar(64) unique not null
	//组织机构自编号
	OrgID string `protobuf:"bytes,2,opt,name=OrgID,proto3" json:"OrgID,omitempty" ykit:"unique,notnull"`
	//@db int default 0
	OrgType int32 `protobuf:"varint,3,opt,name=OrgType,proto3" json:"OrgType,omitempty"`
	//@db int default 100
	//排序值,由用户指定,不用管拼音或者其它语种的排序
	SortValue int32 `protobuf:"varint,4,opt,name=SortValue,proto3" json:"SortValue,omitempty"`
	//@db varchar(32) not null
	//机构名称,缩写
	ShortName string `protobuf:"bytes,5,opt,name=ShortName,proto3" json:"ShortName,omitempty" ykit:"notnull"`
	//@db varchar(256)
	//机构名称,全称
	FullName string `protobuf:"bytes,6,opt,name=FullName,proto3" json:"FullName,omitempty" ykit:"null"`
	//@db text
	//机构描述/备注信息
	Note string `protobuf:"bytes,7,opt,name=Note,proto3" json:"Note,omitempty" ykit:"null"`
	//@db jsonb default  '{}'::jsonb
	// 其它设置，可以在里面扩展需要用到的其它信息
	Setting string `protobuf:"bytes,8,opt,name=Setting,proto3" json:"Setting,omitempty"`
	//@db uuid
	//创建者的rid
	CreatorRID string `protobuf:"bytes,9,opt,name=CreatorRID,proto3" json:"CreatorRID,omitempty" ykit:"null"`
	//@db uuid
	//此组织的上级机构
	OrgRID string `protobuf:"bytes,11,opt,name=OrgRID,proto3" json:"OrgRID,omitempty" ykit:"null"`
	//@db timestamp not null default now_utc()
	//数据最后修改时间
	UpdatedAt string `protobuf:"bytes,14,opt,name=UpdatedAt,proto3" json:"UpdatedAt,omitempty" ykit:"notnull"`
	//@db text
	//数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
	UpdatedDC string `protobuf:"bytes,15,opt,name=UpdatedDC,proto3" json:"UpdatedDC,omitempty" ykit:"null"`
}

func (m *DbOrg) Reset()         { *m = DbOrg{} }
func (m *DbOrg) String() string { return proto.CompactTextString(m) }
func (*DbOrg) ProtoMessage()    {}
func (*DbOrg) Descriptor() ([]byte, []int) {
	return fileDescriptor_ccb462779e28924f, []int{0}
}
func (m *DbOrg) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbOrg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbOrg.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbOrg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbOrg.Merge(m, src)
}
func (m *DbOrg) XXX_Size() int {
	return m.Size()
}
func (m *DbOrg) XXX_DiscardUnknown() {
	xxx_messageInfo_DbOrg.DiscardUnknown(m)
}

var xxx_messageInfo_DbOrg proto.InternalMessageInfo

func (m *DbOrg) GetRID() string {
	if m != nil {
		return m.RID
	}
	return ""
}

func (m *DbOrg) GetOrgID() string {
	if m != nil {
		return m.OrgID
	}
	return ""
}

func (m *DbOrg) GetOrgType() int32 {
	if m != nil {
		return m.OrgType
	}
	return 0
}

func (m *DbOrg) GetSortValue() int32 {
	if m != nil {
		return m.SortValue
	}
	return 0
}

func (m *DbOrg) GetShortName() string {
	if m != nil {
		return m.ShortName
	}
	return ""
}

func (m *DbOrg) GetFullName() string {
	if m != nil {
		return m.FullName
	}
	return ""
}

func (m *DbOrg) GetNote() string {
	if m != nil {
		return m.Note
	}
	return ""
}

func (m *DbOrg) GetSetting() string {
	if m != nil {
		return m.Setting
	}
	return ""
}

func (m *DbOrg) GetCreatorRID() string {
	if m != nil {
		return m.CreatorRID
	}
	return ""
}

func (m *DbOrg) GetOrgRID() string {
	if m != nil {
		return m.OrgRID
	}
	return ""
}

func (m *DbOrg) GetUpdatedAt() string {
	if m != nil {
		return m.UpdatedAt
	}
	return ""
}

func (m *DbOrg) GetUpdatedDC() string {
	if m != nil {
		return m.UpdatedDC
	}
	return ""
}

func init() {
	proto.RegisterType((*DbOrg)(nil), "org.DbOrg")
}

func init() { proto.RegisterFile("org.proto", fileDescriptor_ccb462779e28924f) }

var fileDescriptor_ccb462779e28924f = []byte{
	// 295 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x54, 0x91, 0x3f, 0x4b, 0xc3, 0x40,
	0x18, 0x87, 0x7b, 0x6d, 0xd3, 0x36, 0x27, 0xa8, 0x1c, 0x22, 0x87, 0xc8, 0x51, 0x9d, 0x3a, 0xb5,
	0x83, 0x53, 0x71, 0xd2, 0x06, 0xa1, 0x4b, 0x03, 0xa9, 0x3a, 0xb8, 0x5d, 0x9a, 0xe3, 0x0c, 0x4d,
	0xbd, 0xf0, 0xfa, 0x76, 0xe8, 0xb7, 0xf0, 0x3b, 0xb9, 0x38, 0x76, 0x74, 0x94, 0xe4, 0x8b, 0x48,
	0xde, 0xda, 0x3f, 0x6e, 0xef, 0xf3, 0x3c, 0x10, 0x7e, 0xe1, 0xb8, 0xef, 0xc0, 0xf6, 0x73, 0x70,
	0xe8, 0x44, 0xc3, 0x81, 0xbd, 0xfe, 0xac, 0x73, 0x2f, 0x88, 0x43, 0xb0, 0xe2, 0x94, 0x37, 0xa2,
	0x71, 0x20, 0x59, 0x97, 0xf5, 0xfc, 0xa8, 0x3a, 0xc5, 0x19, 0xf7, 0x42, 0xb0, 0xe3, 0x40, 0xd6,
	0xc9, 0x6d, 0x40, 0x48, 0xde, 0x0e, 0xc1, 0x3e, 0xae, 0x72, 0x23, 0x1b, 0x5d, 0xd6, 0xf3, 0xa2,
	0x2d, 0x8a, 0x4b, 0xee, 0x4f, 0x1d, 0xe0, 0xb3, 0xce, 0x96, 0x46, 0x36, 0xa9, 0xed, 0x05, 0xd5,
	0x57, 0x07, 0x38, 0xd1, 0x0b, 0x23, 0x3d, 0xfa, 0xe2, 0x5e, 0x88, 0x0b, 0xde, 0x79, 0x58, 0x66,
	0x19, 0xc5, 0x16, 0xc5, 0x1d, 0x0b, 0xc1, 0x9b, 0x13, 0x87, 0x46, 0xb6, 0xc9, 0xd3, 0x5d, 0xad,
	0x98, 0x1a, 0xc4, 0xf4, 0xcd, 0xca, 0x0e, 0xe9, 0x2d, 0x0a, 0xc5, 0xf9, 0x08, 0x8c, 0x46, 0x07,
	0xd5, 0xef, 0xf8, 0x14, 0x0f, 0x8c, 0x38, 0xe7, 0xad, 0x10, 0x6c, 0xd5, 0x8e, 0xa8, 0xfd, 0x51,
	0xb5, 0xef, 0x29, 0x4f, 0x34, 0x9a, 0xe4, 0x0e, 0xe5, 0xf1, 0x66, 0xdf, 0x4e, 0x1c, 0xd4, 0x60,
	0x24, 0x4f, 0xfe, 0xd5, 0x60, 0x74, 0x7f, 0xfb, 0x55, 0x28, 0xb6, 0x2e, 0x14, 0xfb, 0x29, 0x14,
	0xfb, 0x28, 0x55, 0x6d, 0x5d, 0xaa, 0xda, 0x77, 0xa9, 0x6a, 0x2f, 0x57, 0x36, 0xc5, 0xfe, 0x3c,
	0x9d, 0xe9, 0x64, 0x38, 0xec, 0xcf, 0xdc, 0x62, 0xb0, 0x9a, 0xa7, 0x38, 0x48, 0x34, 0xea, 0x58,
	0xbf, 0x9b, 0x81, 0x03, 0x1b, 0xb7, 0xe8, 0x39, 0x6e, 0x7e, 0x03, 0x00, 0x00, 0xff, 0xff, 0x8b,
	0xe4, 0x53, 0x0e, 0x9b, 0x01, 0x00, 0x00,
}

func (m *DbOrg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbOrg) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbOrg) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.UpdatedDC) > 0 {
		i -= len(m.UpdatedDC)
		copy(dAtA[i:], m.UpdatedDC)
		i = encodeVarintOrg(dAtA, i, uint64(len(m.UpdatedDC)))
		i--
		dAtA[i] = 0x7a
	}
	if len(m.UpdatedAt) > 0 {
		i -= len(m.UpdatedAt)
		copy(dAtA[i:], m.UpdatedAt)
		i = encodeVarintOrg(dAtA, i, uint64(len(m.UpdatedAt)))
		i--
		dAtA[i] = 0x72
	}
	if len(m.OrgRID) > 0 {
		i -= len(m.OrgRID)
		copy(dAtA[i:], m.OrgRID)
		i = encodeVarintOrg(dAtA, i, uint64(len(m.OrgRID)))
		i--
		dAtA[i] = 0x5a
	}
	if len(m.CreatorRID) > 0 {
		i -= len(m.CreatorRID)
		copy(dAtA[i:], m.CreatorRID)
		i = encodeVarintOrg(dAtA, i, uint64(len(m.CreatorRID)))
		i--
		dAtA[i] = 0x4a
	}
	if len(m.Setting) > 0 {
		i -= len(m.Setting)
		copy(dAtA[i:], m.Setting)
		i = encodeVarintOrg(dAtA, i, uint64(len(m.Setting)))
		i--
		dAtA[i] = 0x42
	}
	if len(m.Note) > 0 {
		i -= len(m.Note)
		copy(dAtA[i:], m.Note)
		i = encodeVarintOrg(dAtA, i, uint64(len(m.Note)))
		i--
		dAtA[i] = 0x3a
	}
	if len(m.FullName) > 0 {
		i -= len(m.FullName)
		copy(dAtA[i:], m.FullName)
		i = encodeVarintOrg(dAtA, i, uint64(len(m.FullName)))
		i--
		dAtA[i] = 0x32
	}
	if len(m.ShortName) > 0 {
		i -= len(m.ShortName)
		copy(dAtA[i:], m.ShortName)
		i = encodeVarintOrg(dAtA, i, uint64(len(m.ShortName)))
		i--
		dAtA[i] = 0x2a
	}
	if m.SortValue != 0 {
		i = encodeVarintOrg(dAtA, i, uint64(m.SortValue))
		i--
		dAtA[i] = 0x20
	}
	if m.OrgType != 0 {
		i = encodeVarintOrg(dAtA, i, uint64(m.OrgType))
		i--
		dAtA[i] = 0x18
	}
	if len(m.OrgID) > 0 {
		i -= len(m.OrgID)
		copy(dAtA[i:], m.OrgID)
		i = encodeVarintOrg(dAtA, i, uint64(len(m.OrgID)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.RID) > 0 {
		i -= len(m.RID)
		copy(dAtA[i:], m.RID)
		i = encodeVarintOrg(dAtA, i, uint64(len(m.RID)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func encodeVarintOrg(dAtA []byte, offset int, v uint64) int {
	offset -= sovOrg(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *DbOrg) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.RID)
	if l > 0 {
		n += 1 + l + sovOrg(uint64(l))
	}
	l = len(m.OrgID)
	if l > 0 {
		n += 1 + l + sovOrg(uint64(l))
	}
	if m.OrgType != 0 {
		n += 1 + sovOrg(uint64(m.OrgType))
	}
	if m.SortValue != 0 {
		n += 1 + sovOrg(uint64(m.SortValue))
	}
	l = len(m.ShortName)
	if l > 0 {
		n += 1 + l + sovOrg(uint64(l))
	}
	l = len(m.FullName)
	if l > 0 {
		n += 1 + l + sovOrg(uint64(l))
	}
	l = len(m.Note)
	if l > 0 {
		n += 1 + l + sovOrg(uint64(l))
	}
	l = len(m.Setting)
	if l > 0 {
		n += 1 + l + sovOrg(uint64(l))
	}
	l = len(m.CreatorRID)
	if l > 0 {
		n += 1 + l + sovOrg(uint64(l))
	}
	l = len(m.OrgRID)
	if l > 0 {
		n += 1 + l + sovOrg(uint64(l))
	}
	l = len(m.UpdatedAt)
	if l > 0 {
		n += 1 + l + sovOrg(uint64(l))
	}
	l = len(m.UpdatedDC)
	if l > 0 {
		n += 1 + l + sovOrg(uint64(l))
	}
	return n
}

func sovOrg(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozOrg(x uint64) (n int) {
	return sovOrg(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *DbOrg) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowOrg
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DbOrg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DbOrg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOrg
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthOrg
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthOrg
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.RID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OrgID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOrg
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthOrg
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthOrg
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OrgID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field OrgType", wireType)
			}
			m.OrgType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOrg
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OrgType |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SortValue", wireType)
			}
			m.SortValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOrg
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SortValue |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ShortName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOrg
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthOrg
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthOrg
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ShortName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field FullName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOrg
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthOrg
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthOrg
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.FullName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Note", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOrg
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthOrg
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthOrg
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Note = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Setting", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOrg
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthOrg
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthOrg
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Setting = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CreatorRID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOrg
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthOrg
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthOrg
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CreatorRID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OrgRID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOrg
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthOrg
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthOrg
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OrgRID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 14:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UpdatedAt", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOrg
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthOrg
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthOrg
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UpdatedAt = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 15:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UpdatedDC", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOrg
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthOrg
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthOrg
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UpdatedDC = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipOrg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthOrg
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthOrg
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipOrg(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowOrg
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowOrg
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowOrg
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthOrg
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupOrg
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthOrg
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthOrg        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowOrg          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupOrg = fmt.Errorf("proto: unexpected end of group")
)

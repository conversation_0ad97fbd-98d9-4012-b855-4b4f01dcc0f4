//Package org  generated by ygen-gocrud. DO NOT EDIT.
//source: org.proto
package org

import (
	"context"
	"strconv"

	"git.kicad99.com/ykit/goutil/crud"
	"git.kicad99.com/ykit/goutil/crudhook"
	"git.kicad99.com/ykit/goutil"
	"git.kicad99.com/ykit/goutil/pgxdb"
	"git.kicad99.com/ykit/goutil/rpc"
	"git.kicad99.com/ykit/goutil/yreflect"
	"google.golang.org/grpc/metadata"
)

// CrudRpcDbOrgServer impl crud Service
type CrudRpcDbOrgServer struct {
}

//Insert impl crud insert
func (*CrudRpcDbOrgServer) Insert(ctx context.Context, req *DbOrg) (r *crud.DMLResult, err error) {
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	r, err = req.Insert(db)
	if err == nil {
		sys, userRid, sid := pgxdb.GetYkitInfoFromMetadata(ctx)
		ipinfo := goutil.GetIpinfoFromMeta(ctx)
		userOrgrid := goutil.GetUserOrgRIDFromMeta(ctx)
		goutil.NatsCdc(sys, req.OrgRID, sid, "DbOrg.Insert", req, nil)
		crudhook.CrudAfterHook(sys, userRid, userOrgrid, "DbOrg.Insert", req, nil, ipinfo)
	}
	return
}

//Update impl crud update
func (*CrudRpcDbOrgServer) Update(ctx context.Context, req *DbOrg) (r *crud.DMLResult, err error) {
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	r, err = req.Update(db)
	if err == nil {
		sys, userRid, sid := pgxdb.GetYkitInfoFromMetadata(ctx)
		ipinfo := goutil.GetIpinfoFromMeta(ctx)
		userOrgrid := goutil.GetUserOrgRIDFromMeta(ctx)
		goutil.NatsCdc(sys, req.OrgRID, sid, "DbOrg.Update", req, nil)
		crudhook.CrudAfterHook(sys, userRid, userOrgrid, "DbOrg.Update", req, nil, ipinfo)
	}
	return
}

//PartialUpdate impl crud PartialUpdate
func (*CrudRpcDbOrgServer) PartialUpdate(ctx context.Context, req *DbOrg) (r *crud.DMLResult, err error) {
	param, err := crud.GetDMLParamFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	r, err = req.PartialUpdate(param.KeyColumn, db)
	if err == nil {
		sys, userRid, sid := pgxdb.GetYkitInfoFromMetadata(ctx)
		ipinfo := goutil.GetIpinfoFromMeta(ctx)
		userOrgrid := goutil.GetUserOrgRIDFromMeta(ctx)
		goutil.NatsCdc(sys, req.OrgRID, sid, "DbOrg.PartialUpdate", req, param)
		crudhook.CrudAfterHook(sys, userRid, userOrgrid, "DbOrg.PartialUpdate", req, param, ipinfo)
	}
	return
}

//Delete impl crud Delete
func (*CrudRpcDbOrgServer) Delete(ctx context.Context, req *DbOrg) (r *crud.DMLResult, err error) {
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	r, err = req.Delete(db)
	if err == nil {
		sys, userRid, sid := pgxdb.GetYkitInfoFromMetadata(ctx)
		ipinfo := goutil.GetIpinfoFromMeta(ctx)
		userOrgrid := goutil.GetUserOrgRIDFromMeta(ctx)
		goutil.NatsCdc(sys, req.OrgRID, sid, "DbOrg.Delete", req, nil)
		crudhook.CrudAfterHook(sys, userRid, userOrgrid, "DbOrg.Delete", req, nil, ipinfo)
	}
	return
}

//SelectOne impl crud SelectOne
func (*CrudRpcDbOrgServer) SelectOne(ctx context.Context, dmlParam *crud.DMLParam) (*DbOrg, error) {
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	if len(dmlParam.KeyValue) != 1 {
		return nil, rpc.EBadProtoParam
	}
	msg := &DbOrg{RID: dmlParam.KeyValue[0]}

	selectRow, err := msg.SelectOne(dmlParam.ResultColumn, db)
	if err != nil {
		return nil, err
	}
	if selectRow == 0 {
		msg.Reset()
	}
	return msg, nil
}

//SelectMany impl crud SelectMany
func (*CrudRpcDbOrgServer) SelectMany(dmlParam *crud.DMLParam, srv RpcDbOrg_SelectManyServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)

	msg := &DbOrg{}

	rows, err := msg.SelectByRids(dmlParam.ResultColumn, dmlParam.KeyValue, db)
	if err != nil {
		return err
	}
	defer rows.Close()
	rowsName := crud.RowsColNames2GoFieldNames(rows, msg)
	rowsCount := 0
	for rows.Next() {
		msg := &DbOrg{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		err = srv.Send(msg)
		if err != nil {
			return err
		}
		rowsCount++
	}
	trailer := metadata.Pairs("rows", strconv.Itoa(rowsCount))
	srv.SetTrailer(trailer)
	return nil
}

//Query impl crud Query
func (*CrudRpcDbOrgServer) Query(req *crud.QueryParam, srv RpcDbOrg_QueryServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)

	rows, err := crud.DbSelectQuery("DbOrg", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbOrg{})
	rowsCount := 0
	for rows.Next() {
		msg := &DbOrg{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		err = srv.Send(msg)
		if err != nil {
			return err
		}
		rowsCount++
	}
	trailer := metadata.Pairs("rows", strconv.Itoa(rowsCount))
	srv.SetTrailer(trailer)
	return nil
}
//Query impl crud QueryBatch
func (*CrudRpcDbOrgServer) QueryBatch(req *crud.QueryParam, srv RpcDbOrg_QueryBatchServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)

	rows, err := crud.DbSelectQuery("DbOrg", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	if req.Batch < 1 {
		req.Batch = 1
	}
	if req.Batch > 10000 {
		req.Batch = 10000
	}

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbOrg{})
	var batchNo uint32 = 0
	var rowsOffset uint32 = 0
	var batchCount uint32 = 0
	list := &DbOrgList{}
	for rows.Next() {
		msg := &DbOrg{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		list.Rows = append(list.Rows, msg)
		batchCount++
		rowsOffset++

		if batchCount >= req.Batch {
			err = srv.Send(list)
			if err != nil {
				return err
			}
			batchNo++
			list = &DbOrgList{
				BatchNo:    batchNo,
				RowsOffset: rowsOffset}
			batchCount = 0
		}
	}
	if batchCount > 0 {
		err = srv.Send(list)
		if err != nil {
			return err
		}
	}
	trailer := metadata.Pairs("rows", strconv.FormatUint(uint64(rowsOffset), 10))
	srv.SetTrailer(trailer)
	return nil
}

// PcrudRpcDbOrgServer impl pcrud Service
type PcrudRpcDbOrgServer struct {
}

//Insert impl pcrud Insert
func (*PcrudRpcDbOrgServer) Insert(ctx context.Context, req *DbOrg) (*crud.DMLResult, error) {
	var crudServer *CrudRpcDbOrgServer
	_, err := crud.GrpcCheckHasPermission(ctx, "db", "DbOrg.Insert")
	if err != nil {
		return nil, err
	}
	defer crud.ResetUserOrgCache()
	err = crud.GrpcCheckHasOrgridPrivilege(ctx, req.OrgRID)
	if err != nil {
		return nil, err
	}
	return crudServer.Insert(ctx, req)
}

//Update impl pcrud Update
func (*PcrudRpcDbOrgServer) Update(ctx context.Context, req *DbOrg) (*crud.DMLResult, error) {
	var crudServer *CrudRpcDbOrgServer
	_, err := crud.GrpcCheckHasPermission(ctx, "db", "DbOrg.Update")
	if err != nil {
		return nil, err
	}
	defer crud.ResetUserOrgCache()
	err = crud.GrpcCheckHasOrgridPrivilege(ctx, req.OrgRID)
	if err != nil {
		return nil, err
	}
	return crudServer.Update(ctx, req)
}

//PartialUpdate impl pcrud PartialUpdate
func (*PcrudRpcDbOrgServer) PartialUpdate(ctx context.Context, req *DbOrg) (*crud.DMLResult, error) {
	var crudServer *CrudRpcDbOrgServer
	_, err := crud.GrpcCheckHasPermission(ctx, "db", "DbOrg.Update")
	if err != nil {
		return nil, err
	}
	defer crud.ResetUserOrgCache()
	err = crud.GrpcCheckHasOrgridPrivilege(ctx, req.OrgRID)
	if err != nil {
		return nil, err
	}
	return crudServer.PartialUpdate(ctx, req)
}

//Delete impl pcrud Delete
func (*PcrudRpcDbOrgServer) Delete(ctx context.Context, req *DbOrg) (*crud.DMLResult, error) {
	var crudServer *CrudRpcDbOrgServer
	_, err := crud.GrpcCheckHasPermission(ctx, "db", "DbOrg.Delete")
	if err != nil {
		return nil, err
	}
	defer crud.ResetUserOrgCache()
	err = crud.GrpcCheckHasOrgridPrivilege(ctx, req.OrgRID)
	if err != nil {
		return nil, err
	}
	return crudServer.Delete(ctx, req)
}

//SelectOne impl pcrud SelectOne
func (*PcrudRpcDbOrgServer) SelectOne(ctx context.Context, dmlParam *crud.DMLParam) (*DbOrg, error) {
	var crudServer *CrudRpcDbOrgServer
	return crudServer.SelectOne(ctx, dmlParam)
}

//SelectMany impl pcrud SelectMany
func (*PcrudRpcDbOrgServer) SelectMany(dmlParam *crud.DMLParam, srv PrpcDbOrg_SelectManyServer) error {
	var crudServer *CrudRpcDbOrgServer
	return crudServer.SelectMany(dmlParam, srv)
}

//Query impl pcrud Query
func (*PcrudRpcDbOrgServer) Query(req *crud.PrivilegeParam, srv PrpcDbOrg_QueryServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)
	rows, err := crud.DbSelectPrivilege("DbOrg", "RID", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbOrg{})
	rowsCount := 0
	for rows.Next() {
		msg := &DbOrg{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		err = srv.Send(msg)
		if err != nil {
			return err
		}
		rowsCount++
	}
	trailer := metadata.Pairs("rows", strconv.Itoa(rowsCount))
	srv.SetTrailer(trailer)
	return nil
}
//Query impl pcrud QueryBatch
func (*PcrudRpcDbOrgServer) QueryBatch(req *crud.PrivilegeParam, srv PrpcDbOrg_QueryBatchServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)
	rows, err := crud.DbSelectPrivilege("DbOrg", "RID", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	if req.QueryCondition.Batch < 1 {
		req.QueryCondition.Batch = 1
	}
	if req.QueryCondition.Batch > 10000 {
		req.QueryCondition.Batch = 10000
	}

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbOrg{})

	var batchNo uint32 = 0
	var rowsOffset uint32 = 0
	var batchCount uint32 = 0
	list := &DbOrgList{}
	for rows.Next() {
		msg := &DbOrg{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		list.Rows = append(list.Rows, msg)
		batchCount++
		rowsOffset++

		if batchCount >= req.QueryCondition.Batch {
			err = srv.Send(list)
			if err != nil {
				return err
			}
			batchNo++
			list = &DbOrgList{
				BatchNo:    batchNo,
				RowsOffset: rowsOffset}
			batchCount = 0
		}
	}
	if batchCount > 0 {
		err = srv.Send(list)
		if err != nil {
			return err
		}
	}
	trailer := metadata.Pairs("rows", strconv.FormatUint(uint64(rowsOffset), 10))
	srv.SetTrailer(trailer)
	return nil
}

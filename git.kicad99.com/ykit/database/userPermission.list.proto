//generated by ygen-gocrud-proto. DO NOT EDIT.
//source: userPermission.proto
syntax = "proto3";
package user;

import "userPermission.proto"; 
option go_package="git.kicad99.com/ykit/database/user";

// DbPermission list
message   DbPermissionList {
	//批次号，从0开始
	uint32 BatchNo = 1;

	//rows offset,从0开始
	uint32 RowsOffset = 2;

	//本批次数据
	repeated DbPermission Rows = 3;
}

// DbRole list
message   DbRoleList {
	//批次号，从0开始
	uint32 BatchNo = 1;

	//rows offset,从0开始
	uint32 RowsOffset = 2;

	//本批次数据
	repeated DbRole Rows = 3;
}

// DbRolePermission list
message   DbRolePermissionList {
	//批次号，从0开始
	uint32 BatchNo = 1;

	//rows offset,从0开始
	uint32 RowsOffset = 2;

	//本批次数据
	repeated DbRolePermission Rows = 3;
}

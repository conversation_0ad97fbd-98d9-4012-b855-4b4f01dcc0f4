//generated by ygen-gocrud-proto. DO NOT EDIT.
//source: config.proto
syntax = "proto3";
package config;

import "crud.proto";
import "config.proto"; 
import "config.list.proto"; 
option go_package="git.kicad99.com/ykit/database/config";

//普通crud，没有权限检查
service   RpcDbConfig {
//插入一行数据
rpc Insert( DbConfig ) returns (crud.DMLResult);

//以RID为条件,全量修改一行数据
rpc Update( DbConfig ) returns (crud.DMLResult);

//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
rpc PartialUpdate( DbConfig ) returns (crud.DMLResult);

//以RID为条件,删除一行数据
rpc Delete( DbConfig ) returns (crud.DMLResult);

//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
rpc SelectOne( crud.DMLParam ) returns ( DbConfig );

//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
rpc SelectMany( crud.DMLParam ) returns ( stream DbConfig );

//根据条件查询数据
rpc Query( crud.QueryParam ) returns ( stream DbConfig );
//根据条件查询数据,批量返回数据,crud.QueryParam.Batch指定一次返回多少条数据
rpc QueryBatch( crud.QueryParam ) returns ( stream DbConfigList );
}



syntax = "proto3";

package user;

option go_package = "git.kicad99.com/ykit/database/user";

//用户session表
//@rpc crud
//@dbpost CREATE INDEX IF NOT EXISTS idxDbUserSessionOrgRID on DbUserSession USING hash(OrgRID)
message DbUserSession {
  //@db uuid primary key
  //行ID,session rid
  string RID = 1;

  //@db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
  //用户所属的群组
  string OrgRID = 2;

  //@db uuid not null REFERENCES DbUser(RID) ON DELETE CASCADE
  //用户rid
  string UserRID = 3;

  //@db int
  //session类型 0:web client
  int32 SessionType = 4;

  //@db timestamp not null default now_utc()
  //用户登录的时间
  string LoginTime = 10;

  //@db jsonb not null default  '{}'::jsonb
  //用户登录信息
  string LoginInfo = 11;

  //@db timestamp not null default now_utc()
  //数据最后修改时间
  string UpdatedAt = 14;

  //@db text
  //数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
  string UpdatedDC = 15;
}
syntax = "proto3";

package user;

import "org.proto";
import "userPermission.proto";
import "rpc.proto";
import "crud.proto";
import "user.proto";

option go_package = "git.kicad99.com/ykit/database/user";

message ReqUserLogin {
  //登录方式
  //0：username+userpass
  //1: session rid
  int32 LoginType = 1;
  //login name or session rid
  string LoginName = 2;
  //login pass base64(sha256(base64(sha256(username+userpass))+LoginTimeStr))
  string LoginPass = 3;
  //时间字符串，必须是3分钟内的时间(utc)，格式 yyyy-mm-dd HH:MM:SS
  string LoginTimeStr = 4;
  //系统号
  string System = 5;
}

message ResUserLogin {
  //回应码
  //100:ok,其它为错误
  //1:密码错误 2: 无此sid 3:此sessionn用户已经删除 4:无此用户
  int32 Code = 1;
  //错误信息,如果有的话
  string Err = 2;
  //user rid
  string UserRID = 3;
  //user org rid
  string UserOrgRID = 4;
  //session rid
  string SessionRID = 5;
  //sys utc time yyyy-mm-dd HH:MM:SS
  string SysUTCTime = 6;
}

message ReqUserHasOrgPrivilege {
  //系统号
  string System = 1;
  //要查询的用户RID
  string UserRID = 2;
  //是不是对此群组拥有权限
  string OrgRID = 3;
  //发起者的session id
  string SessionID = 4;
}

message ReqUserHasPermission {
  //系统号
  string System = 1;
  //要查询的用户RID
  string UserRID = 2;
  string PermissionType = 3;
  string PermissionValue = 4;
  //发起者的session id
  string SessionID = 6;
}

service RpcUser {
  //登录系统
  rpc Login(ReqUserLogin) returns (ResUserLogin);

  //查询用户对相应的群组是否有权限
  //返回值RpcCommon.Code=100为拥有权限，其它为不拥有权限
  rpc IsUserHasOrgPrivilege(ReqUserHasOrgPrivilege) returns (rpc.RpcCommon);

  //取得有权限的org rid列表
  //RpcCommon.Err = userrid
  rpc MyOrgRID(rpc.RpcCommon) returns (stream org.DbOrg);

  //取得用户的权限列表
  //RpcCommon.Err = userrid
  rpc MyPermission(rpc.RpcCommon) returns (stream DbPermission);

  //取得用户的角色列表
  //RpcCommon.Err = userrid
  rpc MyRole(rpc.RpcCommon) returns (stream DbRole);

  //查询用户是否有特定的权限
  //返回值RpcCommon.Code=100为拥有权限，其它为不拥有权限
  rpc IsUserHasPermission(ReqUserHasPermission) returns (rpc.RpcCommon);

  //修改用户自己的配置信息
  //以metadata.DMLParam.keyColumn为要修改的目标列
  rpc UpdateMySetting(DbUser) returns (crud.DMLResult);

  //获取系统当前utc时间
  //系统时间在RpcCommon.System=yyyy-mm-dd HH:MM:SS
  rpc SysUTCTime(rpc.RpcCommon) returns (rpc.RpcCommon);
}
syntax = "proto3";

package config;

option go_package = "git.kicad99.com/ykit/database/config";

//系统配置信息表
//@rpc crud
//@dbpost CREATE INDEX IF NOT EXISTS idxDbConfigOrgRID on DbConfig USING hash(OrgRID)
message DbConfig {
  //@db uuid primary key
  //行ID
  string RID = 1;

  //@db uuid  REFERENCES DbOrg(RID) ON DELETE CASCADE
  //所属的群组，如果是全系统通用的配置，此项为NULL
  string OrgRID = 2;

  //@db text not null
  //配置名
  string ConfKey = 3;

  //@db text
  //配置的值
  string ConfValue = 4;

  //@db timestamp not null default now_utc()
  //数据最后修改时间
  string UpdatedAt = 14;

  //@db text
  //数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
  string UpdatedDC = 15;
}
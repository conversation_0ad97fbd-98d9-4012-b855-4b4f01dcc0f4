syntax = "proto3";

package image;

option go_package = "git.kicad99.com/ykit/database/image";

//用户的一些头像图片数据,地图点icon等
//@rpc crud
//@dbpost CREATE INDEX IF NOT EXISTS idxDbImageOrgRID on DbImage USING hash (OrgRID)
message DbImage {
  //@db uuid primary key
  //行ID
  string RID = 1;

  //@db uuid REFERENCES DbOrg(RID) ON DELETE CASCADE
  //所属的群组
  string OrgRID = 2;

  //@db text
  //文件名
  string FileName = 4;

  //@db text
  //文件内容,经过base64编码,就是html img的dataurl
  string FileContent = 5;

  //@db text
  //文件内容的hash=base64(sha256(file_content_binary)
  string Hash = 6;

  //@db uuid
  //添加的用户RID
  string AddUserRID = 7;

  //@db timestamp not null default now_utc()
  //数据最后修改时间
  string UpdatedAt = 14;

  //@db text
  //数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
  string UpdatedDC = 15;
}
package database

import (
	"context"
	"fmt"
	"git.kicad99.com/ykit/database/user"
	"git.kicad99.com/ykit/goutil"
	"git.kicad99.com/ykit/goutil/crud"
	"git.kicad99.com/ykit/goutil/pgxdb"
	"github.com/golang/glog"
)

type ExPcrudRpcDbUserServer struct {
}

//Insert impl crud insert
func (*ExPcrudRpcDbUserServer) Insert(ctx context.Context, req *user.DbUser) (r *crud.DMLResult, err error) {
	u := &user.PcrudRpcDbUserServer{}
	r, err = u.Insert(ctx, req)
	if err != nil {
		return r, err
	}

	sys, _, _ := pgxdb.GetYkitInfoFromMetadata(ctx)
	db, getDbErr := pgxdb.GetDbConn(sys)
	if getDbErr != nil {
		//Insert DBUser-Role failed.Rollback DBUser Err
		glog.Warningln("Insert DBUser-Role failed.GetDBErr.userRid:", req.RID, " ,username:", req.UserName)
		return r, err
	}
	defer pgxdb.ReleaseDbConn(db)

	//need insert user-role
	userRole := &user.DbUserRole{
		RID:       goutil.NewUUIDv1().String(),
		OrgRID:    req.OrgRID,
		UserRID:   req.RID,
		RoleRID:   "*************-5555-5555-************",
		UpdatedAt: req.UpdatedAt,
		UpdatedDC: req.UpdatedDC,
	}
	_, insert_user_role_err := userRole.Insert(db)
	if insert_user_role_err != nil {
		_, err := req.Delete(db)
		if err != nil {
			//Insert DBUser-Role failed.Rollback DBUser Err
			glog.Warningln("Insert DBUser-Role failed.Rollback DBUser Err.userRid:", req.RID, " ,username:", req.UserName)
			return r, err
		}
		return nil, fmt.Errorf("insert DBUser-Role failed.Rollback DBUser")
	}

	return r, err
}

//Update impl crud update
func (*ExPcrudRpcDbUserServer) Update(ctx context.Context, req *user.DbUser) (r *crud.DMLResult, err error) {
	u := &user.PcrudRpcDbUserServer{}
	return u.Update(ctx, req)
}

//PartialUpdate impl crud PartialUpdate
func (*ExPcrudRpcDbUserServer) PartialUpdate(ctx context.Context, req *user.DbUser) (r *crud.DMLResult, err error) {
	u := &user.PcrudRpcDbUserServer{}
	return u.PartialUpdate(ctx, req)
}

//Delete impl crud Delete
func (*ExPcrudRpcDbUserServer) Delete(ctx context.Context, req *user.DbUser) (r *crud.DMLResult, err error) {
	u := &user.PcrudRpcDbUserServer{}
	return u.Delete(ctx, req)
}

//SelectOne impl crud SelectOne
func (*ExPcrudRpcDbUserServer) SelectOne(ctx context.Context, dmlParam *crud.DMLParam) (*user.DbUser, error) {
	u := &user.PcrudRpcDbUserServer{}
	return u.SelectOne(ctx, dmlParam)
}

//SelectMany impl crud SelectMany
func (*ExPcrudRpcDbUserServer) SelectMany(dmlParam *crud.DMLParam, srv user.PrpcDbUser_SelectManyServer) error {
	u := &user.PcrudRpcDbUserServer{}
	return u.SelectMany(dmlParam, srv)
}

//Query impl crud Query
func (*ExPcrudRpcDbUserServer) Query(req *crud.PrivilegeParam, srv user.PrpcDbUser_QueryServer) error {
	u := &user.PcrudRpcDbUserServer{}
	return u.Query(req, srv)
}

//Query impl crud QueryBatch
func (*ExPcrudRpcDbUserServer) QueryBatch(req *crud.PrivilegeParam, srv user.PrpcDbUser_QueryBatchServer) error {
	u := &user.PcrudRpcDbUserServer{}
	return u.QueryBatch(req, srv)
}

type ExPcrudRpcDbUserRoleServer struct {
}

func isUserHaveRole(sys, userRid, roleRid string) bool {
	db, err := pgxdb.GetDbConn(sys)
	if err != nil {
		glog.Warningln("check is user have role,Err:", err)
		return false
	}
	defer pgxdb.ReleaseDbConn(db)

	SqlStr := `SELECT true as rid WHERE 
(SELECT true::boolean FROM dbrole WHERE rid=$1 and creator=$2) OR
(SELECT true::boolean FROM dbuserrole WHERE rolerid=$1 AND userrid=$2)`

	rows, err := db.Query(context.Background(), SqlStr, roleRid, userRid)
	if err != nil {
		glog.Warningln("check is user have role,Err:", err)
		return false
	}
	defer rows.Close()
	return rows.Next()
}

func IsUserHaveRole(ctx context.Context, roleRid string) error {
	// check this role is opt user'role
	sys, userRid, _ := pgxdb.GetYkitInfoFromMetadata(ctx)
	if !isUserHaveRole(sys, userRid, roleRid) {
		return fmt.Errorf("operation user not hava this role:%s", roleRid)
	}

	return nil
}

//Insert impl crud insert
func (*ExPcrudRpcDbUserRoleServer) Insert(ctx context.Context, req *user.DbUserRole) (r *crud.DMLResult, err error) {
	ur := &user.PcrudRpcDbUserRoleServer{}
	err = IsUserHaveRole(ctx, req.RoleRID)
	if err != nil {
		return nil, err
	}
	return ur.Insert(ctx, req)
}

//Update impl crud update
func (*ExPcrudRpcDbUserRoleServer) Update(ctx context.Context, req *user.DbUserRole) (r *crud.DMLResult, err error) {
	ur := &user.PcrudRpcDbUserRoleServer{}
	err = IsUserHaveRole(ctx, req.RoleRID)
	if err != nil {
		return nil, err
	}
	return ur.Update(ctx, req)
}

//PartialUpdate impl crud PartialUpdate
func (*ExPcrudRpcDbUserRoleServer) PartialUpdate(ctx context.Context, req *user.DbUserRole) (r *crud.DMLResult, err error) {
	ur := &user.PcrudRpcDbUserRoleServer{}
	err = IsUserHaveRole(ctx, req.RoleRID)
	if err != nil {
		return nil, err
	}
	return ur.PartialUpdate(ctx, req)
}

//Delete impl crud Delete
func (*ExPcrudRpcDbUserRoleServer) Delete(ctx context.Context, req *user.DbUserRole) (r *crud.DMLResult, err error) {
	ur := &user.PcrudRpcDbUserRoleServer{}
	err = IsUserHaveRole(ctx, req.RoleRID)
	if err != nil {
		return nil, err
	}
	return ur.Delete(ctx, req)
}

//SelectOne impl crud SelectOne
func (*ExPcrudRpcDbUserRoleServer) SelectOne(ctx context.Context, dmlParam *crud.DMLParam) (*user.DbUserRole, error) {
	ur := &user.PcrudRpcDbUserRoleServer{}
	return ur.SelectOne(ctx, dmlParam)
}

//SelectMany impl crud SelectMany
func (*ExPcrudRpcDbUserRoleServer) SelectMany(dmlParam *crud.DMLParam, srv user.PrpcDbUserRole_SelectManyServer) error {
	ur := &user.PcrudRpcDbUserRoleServer{}
	return ur.SelectMany(dmlParam, srv)
}

//Query impl crud Query
func (*ExPcrudRpcDbUserRoleServer) Query(req *crud.PrivilegeParam, srv user.PrpcDbUserRole_QueryServer) error {
	ur := &user.PcrudRpcDbUserRoleServer{}
	return ur.Query(req, srv)
}

//Query impl crud QueryBatch
func (*ExPcrudRpcDbUserRoleServer) QueryBatch(req *crud.PrivilegeParam, srv user.PrpcDbUserRole_QueryBatchServer) error {
	ur := &user.PcrudRpcDbUserRoleServer{}
	return ur.QueryBatch(req, srv)
}

type ExPcrudRpcDbRoleServer struct {
}

//Insert impl pcrud Insert
func (*ExPcrudRpcDbRoleServer) Insert(ctx context.Context, req *user.DbRole) (*crud.DMLResult, error) {
	sys, _, _ := pgxdb.GetYkitInfoFromMetadata(ctx)
	db, getDbErr := pgxdb.GetDbConn(sys)
	if getDbErr != nil {
		//Insert DBUser-Role failed.Rollback DBUser Err
		glog.Warningln("In DbRoleServer Insert Err. Got DbConn Error:", getDbErr.Error())
		return nil, getDbErr
	}

	defer pgxdb.ReleaseDbConn(db)

	SQLStr := "SELECT RID FROM dbrole WHERE orgrid=$1 AND rolename=$2"

	rows, queryError := db.Query(context.Background(), SQLStr, req.OrgRID, req.RoleName)
	if queryError != nil {
		glog.Warningln("In DbRoleServer Insert Err. Query Role under uint error.Error:", getDbErr.Error())
		return nil, queryError
	}

	defer rows.Close()

	if rows.Next() {
		return nil, fmt.Errorf("role name already exists under the org")
	}
	r := &user.PcrudRpcDbRoleServer{}
	return r.Insert(ctx, req)
}

//Update impl pcrud Update
func (*ExPcrudRpcDbRoleServer) Update(ctx context.Context, req *user.DbRole) (*crud.DMLResult, error) {
	r := &user.PcrudRpcDbRoleServer{}
	// check this role is opt user'role
	err := IsUserHaveRole(ctx, req.RID)
	if err != nil {
		return nil, err
	}
	return r.Update(ctx, req)
}

//PartialUpdate impl pcrud PartialUpdate
func (*ExPcrudRpcDbRoleServer) PartialUpdate(ctx context.Context, req *user.DbRole) (*crud.DMLResult, error) {
	r := &user.PcrudRpcDbRoleServer{}
	// check this role is opt user'role
	err := IsUserHaveRole(ctx, req.RID)
	if err != nil {
		return nil, err
	}
	return r.PartialUpdate(ctx, req)
}

//Delete impl pcrud Delete
func (*ExPcrudRpcDbRoleServer) Delete(ctx context.Context, req *user.DbRole) (*crud.DMLResult, error) {
	r := &user.PcrudRpcDbRoleServer{}
	// check this role is opt user'role
	err := IsUserHaveRole(ctx, req.RID)
	if err != nil {
		return nil, err
	}
	return r.Delete(ctx, req)
}

//SelectOne impl pcrud SelectOne
func (*ExPcrudRpcDbRoleServer) SelectOne(ctx context.Context, dmlParam *crud.DMLParam) (*user.DbRole, error) {
	r := &user.PcrudRpcDbRoleServer{}
	return r.SelectOne(ctx, dmlParam)
}

//SelectMany impl pcrud SelectMany
func (*ExPcrudRpcDbRoleServer) SelectMany(dmlParam *crud.DMLParam, srv user.PrpcDbRole_SelectManyServer) error {
	r := &user.PcrudRpcDbRoleServer{}
	return r.SelectMany(dmlParam, srv)
}

//Query impl pcrud Query
func (*ExPcrudRpcDbRoleServer) Query(req *crud.PrivilegeParam, srv user.PrpcDbRole_QueryServer) error {
	r := &user.PcrudRpcDbRoleServer{}
	return r.Query(req, srv)
}

//Query impl pcrud QueryBatch
func (*ExPcrudRpcDbRoleServer) QueryBatch(req *crud.PrivilegeParam, srv user.PrpcDbRole_QueryBatchServer) error {
	r := &user.PcrudRpcDbRoleServer{}
	return r.QueryBatch(req, srv)
}

type ExDbRolePermissionPcrud struct {
}

func (*ExDbRolePermissionPcrud) Insert(ctx context.Context, req *user.DbRolePermission) (*crud.DMLResult, error) {
	q := &user.YkitDbRolePermissionPcrud{}
	err := IsUserHaveRole(ctx, req.RoleRID)
	if err != nil {
		return nil, err
	}
	return q.Insert(ctx, req)
}
func (*ExDbRolePermissionPcrud) Update(ctx context.Context, req *user.DbRolePermission) (*crud.DMLResult, error) {
	q := &user.YkitDbRolePermissionPcrud{}
	err := IsUserHaveRole(ctx, req.RoleRID)
	if err != nil {
		return nil, err
	}
	return q.Update(ctx, req)
}
func (*ExDbRolePermissionPcrud) PartialUpdate(ctx context.Context, req *user.DbRolePermission) (*crud.DMLResult, error) {
	q := &user.YkitDbRolePermissionPcrud{}
	err := IsUserHaveRole(ctx, req.RoleRID)
	if err != nil {
		return nil, err
	}
	return q.PartialUpdate(ctx, req)
}
func (*ExDbRolePermissionPcrud) Delete(ctx context.Context, req *user.DbRolePermission) (*crud.DMLResult, error) {
	q := &user.YkitDbRolePermissionPcrud{}
	err := IsUserHaveRole(ctx, req.RoleRID)
	if err != nil {
		return nil, err
	}
	return q.Delete(ctx, req)
}
func (*ExDbRolePermissionPcrud) SelectOne(ctx context.Context, req *crud.DMLParam) (*user.DbRolePermission, error) {
	q := &user.YkitDbRolePermissionPcrud{}
	return q.SelectOne(ctx, req)
}
func (*ExDbRolePermissionPcrud) SelectMany(req *crud.DMLParam, srv user.PrpcDbRolePermission_SelectManyServer) error {
	q := &user.YkitDbRolePermissionPcrud{}
	return q.SelectMany(req, srv)
}
func (*ExDbRolePermissionPcrud) Query(req *crud.PrivilegeParam, srv user.PrpcDbRolePermission_QueryServer) error {
	q := &user.YkitDbRolePermissionPcrud{}
	return q.Query(req, srv)
}

func (*ExDbRolePermissionPcrud) QueryBatch(req *crud.PrivilegeParam, srv user.PrpcDbRolePermission_QueryBatchServer) error {
	q := &user.YkitDbRolePermissionPcrud{}
	return q.QueryBatch(req, srv)
}

# options for analysis running
run:
  # timeout for analysis, e.g. 30s, 5m, default is 1m
  timeout: 10m


# all available settings of specific linters
linters-settings:
  maligned:
    # print struct with more effective memory layout or not, false by default
    suggest-new: true

  prealloc:
    # XXX: we don't recommend using this linter before doing performance profiling.
    # For most programs usage of prealloc will be a premature optimization.

    # Report preallocation suggestions only on simple loops that have no returns/breaks/continues/gotos in them.
    # True by default.
    simple: true
    range-loops: true # Report preallocation suggestions on range loops, true by default
    for-loops: true # Report preallocation suggestions on for loops, false by default

before_script:
  - export PATH=$PATH:/home/<USER>/go/bin

stages:
  - build
  - deploy

compile:
  stage: build
  tags:
    - **********
  rules:
    - if: $CI_MERGE_REQUEST_ID
  cache:
    key: $CI_PROJECT_NAME.ykit
    paths:
      - node_modules/

  script:
    - yarn
    - yarn build


deploy:
  stage: deploy
  tags:
    - **********
  rules:
    - if: '$CI_COMMIT_REF_NAME == "master"'
      changes:
        - package.json
  cache:
    key: $CI_PROJECT_NAME.ykit
    paths:
      - node_modules/
  script:
    - echo '//registry.npmjs.org/:_authToken=${NPM_TOKEN}'>.npmrc
    - export myversion=$(node -p "require('./package.json').version")
    - export npmversion=$(npm show yrpcmsg version)
    - if [ "$myversion" != "$npmversion" ]; then yarn ; yarn build; npm publish --access public; fi

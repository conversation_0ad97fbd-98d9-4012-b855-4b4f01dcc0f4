// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: yrpcmsg.proto

package yrpcmsg

import (
	encoding_binary "encoding/binary"
	fmt "fmt"
	proto "github.com/gogo/protobuf/proto"
	io "io"
	math "math"
	math_bits "math/bits"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

///系统中所有的消息交互底层都以此为包装
///ymsg multiline comment
type Ymsg struct {
	///整个rpc msg的长度，不包含此字段
	///虽然这个长度可以很长，但是为了避免大包阻塞其它操作，通常都要限制长度,采用分包多发机制
	///当使用基于包的传输通道时(udp/kcp/websocket)，此值可能为0(此时长度为收到的整个包的长度)
	Len uint32 `protobuf:"fixed32,1,opt,name=Len,proto3" json:"Len,omitempty"`
	// rpc command,rpc的命令和option
	// b15-b0(uint16):低16为rpc命令
	// b19-b16:body压缩方式 0:无压缩 1:lz4 2:zlib inflate/deflate
	// b23-b20:optbin压缩方式 0:无压缩 1:lz4 2:zlib inflate/deflate
	// b31-b24: not used
	Cmd uint32 `protobuf:"fixed32,2,opt,name=Cmd,proto3" json:"Cmd,omitempty"`
	/// session id，登录后一定会有,用于后台区分不同的用户请求
	Sid []byte `protobuf:"bytes,3,opt,name=Sid,proto3" json:"Sid,omitempty"`
	/// rpc call id,给分辨不同的rpc调用使用,调用方增1循环使用
	Cid uint32 `protobuf:"varint,4,opt,name=Cid,proto3" json:"Cid,omitempty"`
	// rpc no,从0开始增1使用,用于区分收到重复的包,特别是udp的情况下
	No uint32 `protobuf:"varint,5,opt,name=No,proto3" json:"No,omitempty"`
	// response code
	Res int32 `protobuf:"zigzag32,9,opt,name=Res,proto3" json:"Res,omitempty"`
	// msg body
	Body []byte `protobuf:"bytes,10,opt,name=Body,proto3" json:"Body,omitempty"`
	// optional str parameter,额外的信息,一般不会有,有些特殊命令里面可能用到
	Optstr string `protobuf:"bytes,11,opt,name=Optstr,proto3" json:"Optstr,omitempty"`
	// optional binary parameter,额外的信息,一般不会有,有些特殊命令里面可能用到
	Optbin []byte `protobuf:"bytes,12,opt,name=Optbin,proto3" json:"Optbin,omitempty"`
	// optional grpc meta
	MetaInfo *Meta `protobuf:"bytes,13,opt,name=MetaInfo,proto3" json:"MetaInfo,omitempty"`
}

func (m *Ymsg) Reset()         { *m = Ymsg{} }
func (m *Ymsg) String() string { return proto.CompactTextString(m) }
func (*Ymsg) ProtoMessage()    {}
func (*Ymsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_1c508808291297d6, []int{0}
}
func (m *Ymsg) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Ymsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Ymsg.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Ymsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Ymsg.Merge(m, src)
}
func (m *Ymsg) XXX_Size() int {
	return m.Size()
}
func (m *Ymsg) XXX_DiscardUnknown() {
	xxx_messageInfo_Ymsg.DiscardUnknown(m)
}

var xxx_messageInfo_Ymsg proto.InternalMessageInfo

func (m *Ymsg) GetLen() uint32 {
	if m != nil {
		return m.Len
	}
	return 0
}

func (m *Ymsg) GetCmd() uint32 {
	if m != nil {
		return m.Cmd
	}
	return 0
}

func (m *Ymsg) GetSid() []byte {
	if m != nil {
		return m.Sid
	}
	return nil
}

func (m *Ymsg) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *Ymsg) GetNo() uint32 {
	if m != nil {
		return m.No
	}
	return 0
}

func (m *Ymsg) GetRes() int32 {
	if m != nil {
		return m.Res
	}
	return 0
}

func (m *Ymsg) GetBody() []byte {
	if m != nil {
		return m.Body
	}
	return nil
}

func (m *Ymsg) GetOptstr() string {
	if m != nil {
		return m.Optstr
	}
	return ""
}

func (m *Ymsg) GetOptbin() []byte {
	if m != nil {
		return m.Optbin
	}
	return nil
}

func (m *Ymsg) GetMetaInfo() *Meta {
	if m != nil {
		return m.MetaInfo
	}
	return nil
}

// grpc meta data item
type MetaItem struct {
	Key  string   `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Vals []string `protobuf:"bytes,2,rep,name=vals,proto3" json:"vals,omitempty"`
}

func (m *MetaItem) Reset()         { *m = MetaItem{} }
func (m *MetaItem) String() string { return proto.CompactTextString(m) }
func (*MetaItem) ProtoMessage()    {}
func (*MetaItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_1c508808291297d6, []int{1}
}
func (m *MetaItem) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *MetaItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_MetaItem.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *MetaItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MetaItem.Merge(m, src)
}
func (m *MetaItem) XXX_Size() int {
	return m.Size()
}
func (m *MetaItem) XXX_DiscardUnknown() {
	xxx_messageInfo_MetaItem.DiscardUnknown(m)
}

var xxx_messageInfo_MetaItem proto.InternalMessageInfo

func (m *MetaItem) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *MetaItem) GetVals() []string {
	if m != nil {
		return m.Vals
	}
	return nil
}

// grpc meta
type Meta struct {
	Val []*MetaItem `protobuf:"bytes,1,rep,name=val,proto3" json:"val,omitempty"`
}

func (m *Meta) Reset()         { *m = Meta{} }
func (m *Meta) String() string { return proto.CompactTextString(m) }
func (*Meta) ProtoMessage()    {}
func (*Meta) Descriptor() ([]byte, []int) {
	return fileDescriptor_1c508808291297d6, []int{2}
}
func (m *Meta) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Meta) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Meta.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Meta) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Meta.Merge(m, src)
}
func (m *Meta) XXX_Size() int {
	return m.Size()
}
func (m *Meta) XXX_DiscardUnknown() {
	xxx_messageInfo_Meta.DiscardUnknown(m)
}

var xxx_messageInfo_Meta proto.InternalMessageInfo

func (m *Meta) GetVal() []*MetaItem {
	if m != nil {
		return m.Val
	}
	return nil
}

// grpc Header Trailer meta
type GrpcMeta struct {
	Header  *Meta `protobuf:"bytes,1,opt,name=Header,proto3" json:"Header,omitempty"`
	Trailer *Meta `protobuf:"bytes,2,opt,name=Trailer,proto3" json:"Trailer,omitempty"`
}

func (m *GrpcMeta) Reset()         { *m = GrpcMeta{} }
func (m *GrpcMeta) String() string { return proto.CompactTextString(m) }
func (*GrpcMeta) ProtoMessage()    {}
func (*GrpcMeta) Descriptor() ([]byte, []int) {
	return fileDescriptor_1c508808291297d6, []int{3}
}
func (m *GrpcMeta) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GrpcMeta) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GrpcMeta.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GrpcMeta) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GrpcMeta.Merge(m, src)
}
func (m *GrpcMeta) XXX_Size() int {
	return m.Size()
}
func (m *GrpcMeta) XXX_DiscardUnknown() {
	xxx_messageInfo_GrpcMeta.DiscardUnknown(m)
}

var xxx_messageInfo_GrpcMeta proto.InternalMessageInfo

func (m *GrpcMeta) GetHeader() *Meta {
	if m != nil {
		return m.Header
	}
	return nil
}

func (m *GrpcMeta) GetTrailer() *Meta {
	if m != nil {
		return m.Trailer
	}
	return nil
}

type Yempty struct {
}

func (m *Yempty) Reset()         { *m = Yempty{} }
func (m *Yempty) String() string { return proto.CompactTextString(m) }
func (*Yempty) ProtoMessage()    {}
func (*Yempty) Descriptor() ([]byte, []int) {
	return fileDescriptor_1c508808291297d6, []int{4}
}
func (m *Yempty) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Yempty) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Yempty.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Yempty) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Yempty.Merge(m, src)
}
func (m *Yempty) XXX_Size() int {
	return m.Size()
}
func (m *Yempty) XXX_DiscardUnknown() {
	xxx_messageInfo_Yempty.DiscardUnknown(m)
}

var xxx_messageInfo_Yempty proto.InternalMessageInfo

// A generic nocare message that you can use to info the call is not important
// and no care the result. A typical example is to use it in report log/trace.
// For instance:
//
//     service Log {
//       rpc Log(infos) returns (yrpc.Ynocare);
//     }
//
type Ynocare struct {
}

func (m *Ynocare) Reset()         { *m = Ynocare{} }
func (m *Ynocare) String() string { return proto.CompactTextString(m) }
func (*Ynocare) ProtoMessage()    {}
func (*Ynocare) Descriptor() ([]byte, []int) {
	return fileDescriptor_1c508808291297d6, []int{5}
}
func (m *Ynocare) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Ynocare) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Ynocare.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Ynocare) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Ynocare.Merge(m, src)
}
func (m *Ynocare) XXX_Size() int {
	return m.Size()
}
func (m *Ynocare) XXX_DiscardUnknown() {
	xxx_messageInfo_Ynocare.DiscardUnknown(m)
}

var xxx_messageInfo_Ynocare proto.InternalMessageInfo

type UnixTime struct {
	// Unix time, the number of miliseconds elapsed since January 1, 1970 UTC
	TimeUnix int64 `protobuf:"zigzag64,1,opt,name=TimeUnix,proto3" json:"TimeUnix,omitempty"`
	// utc time yyyy-MM-dd hh:mm:ss.zzz
	TimeStr string `protobuf:"bytes,2,opt,name=TimeStr,proto3" json:"TimeStr,omitempty"`
}

func (m *UnixTime) Reset()         { *m = UnixTime{} }
func (m *UnixTime) String() string { return proto.CompactTextString(m) }
func (*UnixTime) ProtoMessage()    {}
func (*UnixTime) Descriptor() ([]byte, []int) {
	return fileDescriptor_1c508808291297d6, []int{6}
}
func (m *UnixTime) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *UnixTime) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_UnixTime.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *UnixTime) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnixTime.Merge(m, src)
}
func (m *UnixTime) XXX_Size() int {
	return m.Size()
}
func (m *UnixTime) XXX_DiscardUnknown() {
	xxx_messageInfo_UnixTime.DiscardUnknown(m)
}

var xxx_messageInfo_UnixTime proto.InternalMessageInfo

func (m *UnixTime) GetTimeUnix() int64 {
	if m != nil {
		return m.TimeUnix
	}
	return 0
}

func (m *UnixTime) GetTimeStr() string {
	if m != nil {
		return m.TimeStr
	}
	return ""
}

func init() {
	proto.RegisterType((*Ymsg)(nil), "yrpcmsg.Ymsg")
	proto.RegisterType((*MetaItem)(nil), "yrpcmsg.MetaItem")
	proto.RegisterType((*Meta)(nil), "yrpcmsg.Meta")
	proto.RegisterType((*GrpcMeta)(nil), "yrpcmsg.GrpcMeta")
	proto.RegisterType((*Yempty)(nil), "yrpcmsg.Yempty")
	proto.RegisterType((*Ynocare)(nil), "yrpcmsg.Ynocare")
	proto.RegisterType((*UnixTime)(nil), "yrpcmsg.UnixTime")
}

func init() { proto.RegisterFile("yrpcmsg.proto", fileDescriptor_1c508808291297d6) }

var fileDescriptor_1c508808291297d6 = []byte{
	// 403 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x64, 0x52, 0x4f, 0x6f, 0xd3, 0x30,
	0x14, 0xaf, 0x9b, 0x92, 0x3f, 0xaf, 0x0b, 0x62, 0x3e, 0x20, 0x8b, 0x43, 0x64, 0x05, 0x21, 0x8c,
	0x90, 0x3a, 0x34, 0x6e, 0x9c, 0xd0, 0x76, 0x60, 0x48, 0x30, 0x24, 0x6f, 0x1c, 0xba, 0x5b, 0x9a,
	0x98, 0x10, 0xa8, 0xe3, 0xc8, 0xf1, 0x26, 0xf2, 0x2d, 0xf8, 0x58, 0x1c, 0x77, 0xe4, 0x06, 0x6a,
	0xbf, 0x08, 0x7a, 0x5e, 0x3a, 0x09, 0xf5, 0x94, 0xdf, 0x3f, 0xbf, 0xf7, 0x73, 0x64, 0x48, 0x07,
	0xdb, 0x95, 0xba, 0xaf, 0x17, 0x9d, 0x35, 0xce, 0xd0, 0x68, 0xa4, 0xf9, 0x1f, 0x02, 0xb3, 0xa5,
	0xee, 0x6b, 0xfa, 0x08, 0x82, 0x0f, 0xaa, 0x65, 0x84, 0x13, 0x11, 0x49, 0x84, 0xa8, 0x9c, 0xea,
	0x8a, 0x4d, 0xef, 0x94, 0x53, 0x5d, 0xa1, 0x72, 0xd1, 0x54, 0x2c, 0xe0, 0x44, 0x1c, 0x48, 0x84,
	0x3e, 0xd3, 0x54, 0x6c, 0xc6, 0x89, 0x48, 0x25, 0x42, 0xfa, 0x10, 0xa6, 0xe7, 0x86, 0x3d, 0xf0,
	0xc2, 0xf4, 0xdc, 0x60, 0x42, 0xaa, 0x9e, 0x25, 0x9c, 0x88, 0x43, 0x89, 0x90, 0x52, 0x98, 0x9d,
	0x98, 0x6a, 0x60, 0xe0, 0xc7, 0x78, 0x4c, 0x1f, 0x43, 0xf8, 0xa9, 0x73, 0xbd, 0xb3, 0x6c, 0xce,
	0x89, 0x48, 0xe4, 0xc8, 0x46, 0x7d, 0xd5, 0xb4, 0xec, 0xc0, 0xa7, 0x47, 0x46, 0x5f, 0x40, 0xfc,
	0x51, 0xb9, 0xe2, 0x7d, 0xfb, 0xc5, 0xb0, 0x94, 0x13, 0x31, 0x3f, 0x4e, 0x17, 0xbb, 0x1b, 0xa2,
	0x21, 0xef, 0xed, 0xfc, 0xd5, 0x18, 0x75, 0x4a, 0x63, 0x99, 0xef, 0x6a, 0xf0, 0x97, 0x4c, 0x24,
	0x42, 0x2c, 0x73, 0x53, 0xac, 0x7b, 0x36, 0xe5, 0x81, 0x48, 0xa4, 0xc7, 0xf9, 0x4b, 0x98, 0xe1,
	0x09, 0xfa, 0x14, 0x82, 0x9b, 0x62, 0xcd, 0x08, 0x0f, 0xc4, 0xfc, 0xf8, 0xf0, 0xbf, 0xf9, 0x38,
	0x4d, 0xa2, 0x9b, 0x5f, 0x41, 0xfc, 0xce, 0x76, 0xa5, 0x3f, 0xf0, 0x0c, 0xc2, 0x33, 0x55, 0x54,
	0xca, 0xfa, 0x0d, 0x7b, 0x9d, 0x46, 0x93, 0x3e, 0x87, 0xe8, 0xd2, 0x16, 0xcd, 0x5a, 0x59, 0xff,
	0x73, 0xf7, 0x72, 0x3b, 0x37, 0x8f, 0x21, 0x5c, 0x2a, 0xdd, 0xb9, 0x21, 0x4f, 0x20, 0x5a, 0xb6,
	0xa6, 0x2c, 0xac, 0xca, 0xdf, 0x42, 0xfc, 0xb9, 0x6d, 0x7e, 0x5c, 0x36, 0x5a, 0xd1, 0x27, 0x10,
	0xe3, 0x17, 0xb9, 0x5f, 0x49, 0xe5, 0x3d, 0xa7, 0x0c, 0x22, 0xc4, 0x17, 0xee, 0x6e, 0x4b, 0x22,
	0x77, 0xf4, 0xe4, 0xcd, 0xaf, 0x4d, 0x46, 0x6e, 0x37, 0x19, 0xf9, 0xbb, 0xc9, 0xc8, 0xcf, 0x6d,
	0x36, 0xb9, 0xdd, 0x66, 0x93, 0xdf, 0xdb, 0x6c, 0x72, 0x16, 0x5c, 0x65, 0x75, 0xe3, 0xbe, 0x5e,
	0xaf, 0x16, 0xa5, 0xd1, 0x47, 0x43, 0xd1, 0xd6, 0xdf, 0xae, 0xdb, 0xd2, 0x54, 0xea, 0x68, 0x2c,
	0xb8, 0x0a, 0xfd, 0xfb, 0x79, 0xfd, 0x2f, 0x00, 0x00, 0xff, 0xff, 0x6e, 0x49, 0xbb, 0x6b, 0x50,
	0x02, 0x00, 0x00,
}

func (m *Ymsg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Ymsg) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Ymsg) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.MetaInfo != nil {
		{
			size, err := m.MetaInfo.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintYrpcmsg(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x6a
	}
	if len(m.Optbin) > 0 {
		i -= len(m.Optbin)
		copy(dAtA[i:], m.Optbin)
		i = encodeVarintYrpcmsg(dAtA, i, uint64(len(m.Optbin)))
		i--
		dAtA[i] = 0x62
	}
	if len(m.Optstr) > 0 {
		i -= len(m.Optstr)
		copy(dAtA[i:], m.Optstr)
		i = encodeVarintYrpcmsg(dAtA, i, uint64(len(m.Optstr)))
		i--
		dAtA[i] = 0x5a
	}
	if len(m.Body) > 0 {
		i -= len(m.Body)
		copy(dAtA[i:], m.Body)
		i = encodeVarintYrpcmsg(dAtA, i, uint64(len(m.Body)))
		i--
		dAtA[i] = 0x52
	}
	if m.Res != 0 {
		i = encodeVarintYrpcmsg(dAtA, i, uint64((uint32(m.Res)<<1)^uint32((m.Res>>31))))
		i--
		dAtA[i] = 0x48
	}
	if m.No != 0 {
		i = encodeVarintYrpcmsg(dAtA, i, uint64(m.No))
		i--
		dAtA[i] = 0x28
	}
	if m.Cid != 0 {
		i = encodeVarintYrpcmsg(dAtA, i, uint64(m.Cid))
		i--
		dAtA[i] = 0x20
	}
	if len(m.Sid) > 0 {
		i -= len(m.Sid)
		copy(dAtA[i:], m.Sid)
		i = encodeVarintYrpcmsg(dAtA, i, uint64(len(m.Sid)))
		i--
		dAtA[i] = 0x1a
	}
	if m.Cmd != 0 {
		i -= 4
		encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(m.Cmd))
		i--
		dAtA[i] = 0x15
	}
	if m.Len != 0 {
		i -= 4
		encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(m.Len))
		i--
		dAtA[i] = 0xd
	}
	return len(dAtA) - i, nil
}

func (m *MetaItem) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MetaItem) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *MetaItem) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Vals) > 0 {
		for iNdEx := len(m.Vals) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Vals[iNdEx])
			copy(dAtA[i:], m.Vals[iNdEx])
			i = encodeVarintYrpcmsg(dAtA, i, uint64(len(m.Vals[iNdEx])))
			i--
			dAtA[i] = 0x12
		}
	}
	if len(m.Key) > 0 {
		i -= len(m.Key)
		copy(dAtA[i:], m.Key)
		i = encodeVarintYrpcmsg(dAtA, i, uint64(len(m.Key)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *Meta) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Meta) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Meta) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Val) > 0 {
		for iNdEx := len(m.Val) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Val[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintYrpcmsg(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *GrpcMeta) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GrpcMeta) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GrpcMeta) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Trailer != nil {
		{
			size, err := m.Trailer.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintYrpcmsg(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if m.Header != nil {
		{
			size, err := m.Header.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintYrpcmsg(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *Yempty) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Yempty) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Yempty) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *Ynocare) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Ynocare) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Ynocare) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *UnixTime) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UnixTime) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *UnixTime) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.TimeStr) > 0 {
		i -= len(m.TimeStr)
		copy(dAtA[i:], m.TimeStr)
		i = encodeVarintYrpcmsg(dAtA, i, uint64(len(m.TimeStr)))
		i--
		dAtA[i] = 0x12
	}
	if m.TimeUnix != 0 {
		i = encodeVarintYrpcmsg(dAtA, i, uint64((uint64(m.TimeUnix)<<1)^uint64((m.TimeUnix>>63))))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func encodeVarintYrpcmsg(dAtA []byte, offset int, v uint64) int {
	offset -= sovYrpcmsg(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *Ymsg) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Len != 0 {
		n += 5
	}
	if m.Cmd != 0 {
		n += 5
	}
	l = len(m.Sid)
	if l > 0 {
		n += 1 + l + sovYrpcmsg(uint64(l))
	}
	if m.Cid != 0 {
		n += 1 + sovYrpcmsg(uint64(m.Cid))
	}
	if m.No != 0 {
		n += 1 + sovYrpcmsg(uint64(m.No))
	}
	if m.Res != 0 {
		n += 1 + sozYrpcmsg(uint64(m.Res))
	}
	l = len(m.Body)
	if l > 0 {
		n += 1 + l + sovYrpcmsg(uint64(l))
	}
	l = len(m.Optstr)
	if l > 0 {
		n += 1 + l + sovYrpcmsg(uint64(l))
	}
	l = len(m.Optbin)
	if l > 0 {
		n += 1 + l + sovYrpcmsg(uint64(l))
	}
	if m.MetaInfo != nil {
		l = m.MetaInfo.Size()
		n += 1 + l + sovYrpcmsg(uint64(l))
	}
	return n
}

func (m *MetaItem) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Key)
	if l > 0 {
		n += 1 + l + sovYrpcmsg(uint64(l))
	}
	if len(m.Vals) > 0 {
		for _, s := range m.Vals {
			l = len(s)
			n += 1 + l + sovYrpcmsg(uint64(l))
		}
	}
	return n
}

func (m *Meta) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Val) > 0 {
		for _, e := range m.Val {
			l = e.Size()
			n += 1 + l + sovYrpcmsg(uint64(l))
		}
	}
	return n
}

func (m *GrpcMeta) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Header != nil {
		l = m.Header.Size()
		n += 1 + l + sovYrpcmsg(uint64(l))
	}
	if m.Trailer != nil {
		l = m.Trailer.Size()
		n += 1 + l + sovYrpcmsg(uint64(l))
	}
	return n
}

func (m *Yempty) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *Ynocare) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *UnixTime) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.TimeUnix != 0 {
		n += 1 + sozYrpcmsg(uint64(m.TimeUnix))
	}
	l = len(m.TimeStr)
	if l > 0 {
		n += 1 + l + sovYrpcmsg(uint64(l))
	}
	return n
}

func sovYrpcmsg(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozYrpcmsg(x uint64) (n int) {
	return sovYrpcmsg(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *Ymsg) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowYrpcmsg
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Ymsg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Ymsg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field Len", wireType)
			}
			m.Len = 0
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			m.Len = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
		case 2:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field Cmd", wireType)
			}
			m.Cmd = 0
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			m.Cmd = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Sid", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowYrpcmsg
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthYrpcmsg
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthYrpcmsg
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Sid = append(m.Sid[:0], dAtA[iNdEx:postIndex]...)
			if m.Sid == nil {
				m.Sid = []byte{}
			}
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Cid", wireType)
			}
			m.Cid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowYrpcmsg
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Cid |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field No", wireType)
			}
			m.No = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowYrpcmsg
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.No |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Res", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowYrpcmsg
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.Res = v
		case 10:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Body", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowYrpcmsg
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthYrpcmsg
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthYrpcmsg
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Body = append(m.Body[:0], dAtA[iNdEx:postIndex]...)
			if m.Body == nil {
				m.Body = []byte{}
			}
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Optstr", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowYrpcmsg
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthYrpcmsg
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthYrpcmsg
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Optstr = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 12:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Optbin", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowYrpcmsg
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthYrpcmsg
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthYrpcmsg
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Optbin = append(m.Optbin[:0], dAtA[iNdEx:postIndex]...)
			if m.Optbin == nil {
				m.Optbin = []byte{}
			}
			iNdEx = postIndex
		case 13:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field MetaInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowYrpcmsg
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthYrpcmsg
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthYrpcmsg
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.MetaInfo == nil {
				m.MetaInfo = &Meta{}
			}
			if err := m.MetaInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipYrpcmsg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthYrpcmsg
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *MetaItem) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowYrpcmsg
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: MetaItem: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: MetaItem: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Key", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowYrpcmsg
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthYrpcmsg
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthYrpcmsg
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Key = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Vals", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowYrpcmsg
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthYrpcmsg
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthYrpcmsg
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Vals = append(m.Vals, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipYrpcmsg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthYrpcmsg
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Meta) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowYrpcmsg
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Meta: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Meta: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Val", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowYrpcmsg
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthYrpcmsg
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthYrpcmsg
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Val = append(m.Val, &MetaItem{})
			if err := m.Val[len(m.Val)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipYrpcmsg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthYrpcmsg
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GrpcMeta) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowYrpcmsg
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GrpcMeta: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GrpcMeta: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Header", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowYrpcmsg
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthYrpcmsg
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthYrpcmsg
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Header == nil {
				m.Header = &Meta{}
			}
			if err := m.Header.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Trailer", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowYrpcmsg
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthYrpcmsg
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthYrpcmsg
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Trailer == nil {
				m.Trailer = &Meta{}
			}
			if err := m.Trailer.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipYrpcmsg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthYrpcmsg
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Yempty) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowYrpcmsg
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Yempty: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Yempty: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipYrpcmsg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthYrpcmsg
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Ynocare) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowYrpcmsg
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Ynocare: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Ynocare: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipYrpcmsg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthYrpcmsg
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *UnixTime) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowYrpcmsg
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: UnixTime: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: UnixTime: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TimeUnix", wireType)
			}
			var v uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowYrpcmsg
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = (v >> 1) ^ uint64((int64(v&1)<<63)>>63)
			m.TimeUnix = int64(v)
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TimeStr", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowYrpcmsg
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthYrpcmsg
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthYrpcmsg
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TimeStr = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipYrpcmsg(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthYrpcmsg
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipYrpcmsg(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowYrpcmsg
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowYrpcmsg
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowYrpcmsg
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthYrpcmsg
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupYrpcmsg
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthYrpcmsg
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthYrpcmsg        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowYrpcmsg          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupYrpcmsg = fmt.Errorf("proto: unexpected end of group")
)

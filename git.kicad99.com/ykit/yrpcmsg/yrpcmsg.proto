syntax = "proto3";
package yrpcmsg;

option optimize_for = LITE_RUNTIME;

option go_package = "github.com/yangjuncode/yrpcmsg";

///系统中所有的消息交互底层都以此为包装
///ymsg multiline comment
message Ymsg {

  ///整个rpc msg的长度，不包含此字段
  ///虽然这个长度可以很长，但是为了避免大包阻塞其它操作，通常都要限制长度,采用分包多发机制
  ///当使用基于包的传输通道时(udp/kcp/websocket)，此值可能为0(此时长度为收到的整个包的长度)
  fixed32 Len = 1;

  // rpc command,rpc的命令和option
  // b15-b0(uint16):低16为rpc命令
  // b19-b16:body压缩方式 0:无压缩 1:lz4 2:zlib inflate/deflate
  // b23-b20:optbin压缩方式 0:无压缩 1:lz4 2:zlib inflate/deflate
  // b31-b24: not used
  fixed32 Cmd = 2;

  /// session id，登录后一定会有,用于后台区分不同的用户请求
  bytes Sid = 3;

  /// rpc call id,给分辨不同的rpc调用使用,调用方增1循环使用
  uint32 Cid = 4;

  // rpc no,从0开始增1使用,用于区分收到重复的包,特别是udp的情况下
  uint32 No = 5;

  // response code
  sint32 Res = 9;

  // msg body
  bytes Body = 10;

  // optional str parameter,额外的信息,一般不会有,有些特殊命令里面可能用到
  string Optstr = 11;

  // optional binary parameter,额外的信息,一般不会有,有些特殊命令里面可能用到
  bytes Optbin = 12;

  // optional grpc meta
  Meta MetaInfo = 13;
};

// grpc meta data item
message MetaItem {
  string key = 1;
  repeated string vals = 2;
}

// grpc meta
message Meta { repeated MetaItem val = 1; }

// grpc Header Trailer meta
message GrpcMeta {
  Meta Header = 1;
  Meta Trailer = 2;
}

// A generic empty message that you can re-use to avoid defining duplicated
// empty messages in your APIs. A typical example is to use it as the request
// or the response type of an API method. For instance:
//
//     service Foo {
//       rpc Bar(yrpc.Yempty) returns (yrpc.Yempty);
//     }
//

message Yempty {}

// A generic nocare message that you can use to info the call is not important
// and no care the result. A typical example is to use it in report log/trace.
// For instance:
//
//     service Log {
//       rpc Log(infos) returns (yrpc.Ynocare);
//     }
//
message Ynocare {}

message UnixTime {
  // Unix time, the number of miliseconds elapsed since January 1, 1970 UTC
  sint64 TimeUnix = 1;
  // utc time yyyy-MM-dd hh:mm:ss.zzz
  string TimeStr = 2;
}

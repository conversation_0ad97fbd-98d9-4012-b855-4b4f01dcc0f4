**福建北峰智慧电子界桩**

**基础型1.0版本**

**功能开发需求书**

**广州研发中心：洪天贵 搞**

**2023年5月21号**

**智慧电子界桩以及今后系统物联网拓展平台, 并结合原有的专网系统**

**系统**

数据库+后台服务器端平台 BF-PMSS01

客户端调度软件BF-PMSBS01

APP客户端软件 BF-PMSAP01

**终端**

标准型：无摄像红外：BF-DTD002

精典型：升级带摄像红外：BF-DTD002 Pro

专网型：DMR UHF频段：  BF-DTD003

专网基站控制器型号：BF-TR917

DMR UHF频段专网基站模块：BF-DTR908

**以上是2024.1.15重新与品牌小张核对型号**

50mm

**1、电子界桩4G基础型初步结构**

主

板

正

面

⑥

背

①

②

③

④

GPS天线T形直接焊板上

⑨

外接传感器

卡槽2.0mm

板尺寸暂定50x80mm器件摆放根据实具电路板摆放。

⑧

⑤

⑦

Type-C口

5mm空间电池焊点

- GPS天线：主板上凹缺口，GPS天线T形直接焊在主板上，
- 信号灯：根据当前信号强弱分为三档高中低，在一个灯区别秒闪低、连闪二下为中、连闪三下为高，注：60秒自动关灯
- 上网灯：开始连接服务器为闪闪闪直到连到为秒闪，注：60秒自动关灯
- 4G天线：内置与外接二种模式，SMA母座，弯头焊主板，另一种是内置FPC天线
- 电源开关：需要控制在电池进入端，因为出厂电池已装好避免长期时没有及时安装漏电。 **(90度拨动开关)**
- 报警键：主要作于安装前与后台连通测试；( **90度轻触开关** ) 注：主要作于安装前与后台连通测试所有上传数据是否正确；
- 复位键：恢复出厂值
- USB口：Type-C不分方向，USB带供电， **注** ：1、串口打印、2远程和现场升级设置参数和修改，
- 外接传感器：如、压力、酸碱度、异味等，串口综合市场终端而定，如用于界桩是不需要的，但是否能采共用一个口根据设备来对应？
- GPS模块：18x18 mm
- 4G卡座：采用小卡座
- 卡槽：塑料卡槽深度2mm，

内置俯视图

顶盖面图

侧面50mm

电池

正面

40mm

顶盖设计二种模式

- 内置FPC天线：是根据现场信号效强的场地使用，
- 外置天线：是根据现场信号效差的场地使用

外接传感器Type-C口不分方向

外

- 外壳为更好防水采塑料罐体，罐体内部左右卡卡槽固主板。
- 顶盖做防水圈4个螺丝固定，注：塑料罐体4个螺丝采用3mm螺母埋注
- 顶盖只有4G天线座孔，同时做防水圈处理
- 顶盖指示灯不外露，指示灯位置塑料做透明感光。
- 外接传感器口：位置由结构与硬件商定，
- 4G天线内置外置由结构与硬件商定，
- 在主机在安装前完打开顶盖，调试完毕在放入塑料罐体。

**硬件功能需求(标准版)**

| 智慧电子界桩系统 硬件部分        (标准型) BF-DTD002   | 智慧电子界桩系统 硬件部分        (标准型) BF-DTD002   | 智慧电子界桩系统 硬件部分        (标准型) BF-DTD002                                                                                                                                                                                                | 智慧电子界桩系统 硬件部分        (标准型) BF-DTD002                                                                                                                                                                                                | 智慧电子界桩系统 硬件部分        (标准型) BF-DTD002                                                                                                                                                                                                |
|----------------------------------------|----------------------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| 序号                                     | 功能                                     | 功能描述                                                                                                                                                                                                                                | 参 数                                                                                                                                                                                                                                 | 备注                                                                                                                                                                                                                                  |
| 1                                      | 公网数传模块                                 | 动态、静态预警预报监测                                                                                                                                                                                                                         |                                                                                                                                                                                                                                     |                                                                                                                                                                                                                                     |
| 2                                      | 定位                                     | 北斗/GPS双模跟踪定位                                                                                                                                                                                                                        | 定位精度≤-10m                                                                                                                                                                                                                           | 加纽扣电池存储最后定位数，型号MS621FE                                                                                                                                                                                                              |
| 3                                      | 北斗天线                                   | 北斗有源陶瓷天线                                                                                                                                                                                                                            | 3.3V、18x18x5mm                                                                                                                                                                                                                      |                                                                                                                                                                                                                                     |
| 4                                      | 4G模块                                   | 全网通模块                                                                                                                                                                                                                               | 待选型，最重是功耗低又能符合的数据传输需求，对于传输速率在测试环境下能保证99%                                                                                                                                                                                            | 待选型，最重是功耗低又能符合的数据传输需求，对于传输速率在测试环境下能保证99%                                                                                                                                                                                            |
| 5                                      | 三轴芯片                                   | 三轴芯片位移、倾斜、振动报警                                                                                                                                                                                                                      | 位移速度、倾斜45度、连读振动30秒报警                                                                                                                                                                                                                | 采用原U段界桩芯片                                                                                                                                                                                                                           |
| 6                                      | PCB主板指示灯                               | 1、信号场强灯：强弱分为三档（高中低）                                                                                                                                                                                                                 | 区别秒闪为低、连闪二下为中、连闪三下为高                                                                                                                                                                                                                | 注：60秒自动关                                                                                                                                                                                                                            |
| 6                                      | PCB主板指示灯                               | 2、上网灯：初始化连接服务器                                                                                                                                                                                                                      | 为闪闪闪直到连到系统服务器转为秒闪                                                                                                                                                                                                                   | 注：60秒自动关                                                                                                                                                                                                                            |
| 7                                      | 功能键                                    | 1、电源总开关、2测试键、3复位键                                                                                                                                                                                                                   |                                                                                                                                                                                                                                     | 注：测试键、复位键，长按3秒起用                                                                                                                                                                                                                    |
| 8                                      | 电池                                     | 要求三年使用，另加1520电容放电                                                                                                                                                                                                                   | 34615锂亚3.6v、19A一次性电池  (2粒并连总和38Ah)                                                                                                                                                                                                  | 注：电池最低2v模块NV不工作是否需做断电处理                                                                                                                                                                                                             |
| 9                                      | 外接传感器                                  | 可支待其它的外接传感器，                                                                                                                                                                                                                        | 如温显度、压力、等环境监测；参数根据所需产品而定                                                                                                                                                                                                            | 接口根据市面传感器通用接口定议，持定                                                                                                                                                                                                                  |
| 10                                     | 开机初始化上传                                | 开机初始化全功能打开，包括4G模块、北斗模块，超过60秒进入休眠状态。如不能运行上传正常数据，关机重新开机照延时60秒。                                                                                                                                                                        | 开机初始化全功能打开，包括4G模块、北斗模块，超过60秒进入休眠状态。如不能运行上传正常数据，关机重新开机照延时60秒。                                                                                                                                                                        | 开机初始化全功能打开，包括4G模块、北斗模块，超过60秒进入休眠状态。如不能运行上传正常数据，关机重新开机照延时60秒。                                                                                                                                                                        |
| 11                                     | 手动测试上传                                 | 主要作于安装前与后台连调测试所有上传数据是否正确； 包括：GPS位置、NB-IMEI 、4G-ICCID、时间、电量、场强、温度、湿度 注：手动测试即时发送，不等有效定位才发送信息                                                                                                                                          | 主要作于安装前与后台连调测试所有上传数据是否正确； 包括：GPS位置、NB-IMEI 、4G-ICCID、时间、电量、场强、温度、湿度 注：手动测试即时发送，不等有效定位才发送信息                                                                                                                                          | 主要作于安装前与后台连调测试所有上传数据是否正确； 包括：GPS位置、NB-IMEI 、4G-ICCID、时间、电量、场强、温度、湿度 注：手动测试即时发送，不等有效定位才发送信息                                                                                                                                          |
| 12                                     | 定时上传打卡                                 | 包括：设备自编码ID、4G卡ICCID、时间、电量、场强、温度、湿度。第一次等待3秒后台握手回应，收到后台握手回应即时进入休眠。如第一次没有收到握手回应，重发第二次在等待3秒握手回应后自动进入休眠。在等待时如有新的指令参数修改应即可更新。注：不需要打开定位，                                                                                                   | 包括：设备自编码ID、4G卡ICCID、时间、电量、场强、温度、湿度。第一次等待3秒后台握手回应，收到后台握手回应即时进入休眠。如第一次没有收到握手回应，重发第二次在等待3秒握手回应后自动进入休眠。在等待时如有新的指令参数修改应即可更新。注：不需要打开定位，                                                                                                   | 包括：设备自编码ID、4G卡ICCID、时间、电量、场强、温度、湿度。第一次等待3秒后台握手回应，收到后台握手回应即时进入休眠。如第一次没有收到握手回应，重发第二次在等待3秒握手回应后自动进入休眠。在等待时如有新的指令参数修改应即可更新。注：不需要打开定位，                                                                                                   |
| 13                                     | 主动上传报警                                 | 1、被动上传数据是指终端：位移、倾斜、振动报警，此时全功能打开，包括4G模块、北斗模块，间隔10秒报一次、连读报10次，如后台没有解除报警，自动解除进入休眠状态。 注：处于报警状态下：定位模块常开直到报警解除    2、上传数据包括设备自编码ID、4G卡ICCID、时间、电量、场强、温度、湿度、位置。                                                                             | 1、被动上传数据是指终端：位移、倾斜、振动报警，此时全功能打开，包括4G模块、北斗模块，间隔10秒报一次、连读报10次，如后台没有解除报警，自动解除进入休眠状态。 注：处于报警状态下：定位模块常开直到报警解除    2、上传数据包括设备自编码ID、4G卡ICCID、时间、电量、场强、温度、湿度、位置。                                                                             | 1、被动上传数据是指终端：位移、倾斜、振动报警，此时全功能打开，包括4G模块、北斗模块，间隔10秒报一次、连读报10次，如后台没有解除报警，自动解除进入休眠状态。 注：处于报警状态下：定位模块常开直到报警解除    2、上传数据包括设备自编码ID、4G卡ICCID、时间、电量、场强、温度、湿度、位置。                                                                             |
| 14                                     | 接收后台指令                                 | 界桩平时处于休眠状态，后台下发修改参数指令只能等待界桩自动苏醒上传打卡给予更新修改； 参数修改内容：定时上传时间间隔、位移、倾斜、振动报警参数值； 遥控指令内容：遥毙锁机、2遥晕遥开、解除报警； 注：参数修改或指令都需要握手回应 注：1、遥毙：主要是针对被盗或严重故障乱发数据的给予遥毙，(遥毙后只能现场重新刷机才能重新使用)  2、遥晕：主要是针对乱发数据的给予遥晕，(遥晕后取消发送开机其它动态报警，但每天照常按时苏醒上传打卡，等待接收新指令解除)； | 界桩平时处于休眠状态，后台下发修改参数指令只能等待界桩自动苏醒上传打卡给予更新修改； 参数修改内容：定时上传时间间隔、位移、倾斜、振动报警参数值； 遥控指令内容：遥毙锁机、2遥晕遥开、解除报警； 注：参数修改或指令都需要握手回应 注：1、遥毙：主要是针对被盗或严重故障乱发数据的给予遥毙，(遥毙后只能现场重新刷机才能重新使用)  2、遥晕：主要是针对乱发数据的给予遥晕，(遥晕后取消发送开机其它动态报警，但每天照常按时苏醒上传打卡，等待接收新指令解除)； | 界桩平时处于休眠状态，后台下发修改参数指令只能等待界桩自动苏醒上传打卡给予更新修改； 参数修改内容：定时上传时间间隔、位移、倾斜、振动报警参数值； 遥控指令内容：遥毙锁机、2遥晕遥开、解除报警； 注：参数修改或指令都需要握手回应 注：1、遥毙：主要是针对被盗或严重故障乱发数据的给予遥毙，(遥毙后只能现场重新刷机才能重新使用)  2、遥晕：主要是针对乱发数据的给予遥晕，(遥晕后取消发送开机其它动态报警，但每天照常按时苏醒上传打卡，等待接收新指令解除)； |
| 15                                     | 曾加演示版                                  | 解析：演示版本主要是用于给客户演示，演示功能：位移、倾斜、振动报警，室外报警定位跟踪；注：做法开机上电初始化全功能打开至关机为止中途不休眠                                                                                                                                                               | 解析：演示版本主要是用于给客户演示，演示功能：位移、倾斜、振动报警，室外报警定位跟踪；注：做法开机上电初始化全功能打开至关机为止中途不休眠                                                                                                                                                               | 解析：演示版本主要是用于给客户演示，演示功能：位移、倾斜、振动报警，室外报警定位跟踪；注：做法开机上电初始化全功能打开至关机为止中途不休眠                                                                                                                                                               |
| 16                                     | 省电处理                                   | 平时单片机、三轴芯片、常待机状态，4G模块、北斗模块处于休眠状态；                                                                                                                                                                                                   | 平时单片机、三轴芯片、常待机状态，4G模块、北斗模块处于休眠状态；                                                                                                                                                                                                   | 平时单片机、三轴芯片、常待机状态，4G模块、北斗模块处于休眠状态；                                                                                                                                                                                                   |
| 17                                     | 定时重起                                   | 终端定时重起：每天定时打卡前断电重起上传打一次或N天重起可设定，为避免终端死机；注：根据测试情况在定                                                                                                                                                                                  | 终端定时重起：每天定时打卡前断电重起上传打一次或N天重起可设定，为避免终端死机；注：根据测试情况在定                                                                                                                                                                                  | 终端定时重起：每天定时打卡前断电重起上传打一次或N天重起可设定，为避免终端死机；注：根据测试情况在定                                                                                                                                                                                  |

**硬件功能需求(精典版)**

| 智慧电子界桩系统 硬件部分        (精典型 BF-DTD002 Pro)   | 智慧电子界桩系统 硬件部分        (精典型 BF-DTD002 Pro)   | 智慧电子界桩系统 硬件部分        (精典型 BF-DTD002 Pro)                                                                                                                                                                                                                                   | 智慧电子界桩系统 硬件部分        (精典型 BF-DTD002 Pro)                                                                                                                                                                                                                                   | 智慧电子界桩系统 硬件部分        (精典型 BF-DTD002 Pro)                                                                                                                                                                                                                                   |
|--------------------------------------------|--------------------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| 序号                                         | 功能                                         | 功能描述                                                                                                                                                                                                                                                                       | 参 数                                                                                                                                                                                                                                                                        | 备注                                                                                                                                                                                                                                                                         |
| 1                                          | 公网数传模块                                     | 动态、静态预警预报监测                                                                                                                                                                                                                                                                |                                                                                                                                                                                                                                                                            |                                                                                                                                                                                                                                                                            |
| 2                                          | 定位                                         | 北斗、GPS双模跟踪定位                                                                                                                                                                                                                                                               | 定位精度≤-10m、                                                                                                                                                                                                                                                                 | 采用深圳合方圆开发成品，                                                                                                                                                                                                                                                               |
| 3                                          | 北斗天线                                       | 北斗有源陶瓷天线                                                                                                                                                                                                                                                                   | 3.3V、18x18x5mm                                                                                                                                                                                                                                                             | 采用深圳合方圆开发成品，                                                                                                                                                                                                                                                               |
| 4                                          | 4G模块                                       | 全网通模块                                                                                                                                                                                                                                                                      |                                                                                                                                                                                                                                                                            | 采用深圳合方圆开发成品，                                                                                                                                                                                                                                                               |
| 5                                          | 摄像头                                        | 摄像头图片抓拍通过4G上传；                                                                                                                                                                                                                                                             | 3.3V低功耗                                                                                                                                                                                                                                                                    | 采用深圳合方圆开发成品，注：增加二个夜补光                                                                                                                                                                                                                                                      |
| 6                                          | 红外探头                                       | 低功耗红外人体及动物感应                                                                                                                                                                                                                                                               | 探测距离6M                                                                                                                                                                                                                                                                     | 持选合适探头                                                                                                                                                                                                                                                                     |
| 7                                          | 三轴芯片                                       | 三轴芯片具备位移、倾斜、振动报警                                                                                                                                                                                                                                                           | 默认值：位移10、倾斜45度、连读振动30秒报警                                                                                                                                                                                                                                                   | 注：振动值等测试持定                                                                                                                                                                                                                                                                 |
| 8                                          | PCB主板指示灯                                   | 1、信号场强灯：强弱分为三档（高中低）                                                                                                                                                                                                                                                        | 区别秒闪为低、连闪二下为中、连闪三下为高                                                                                                                                                                                                                                                       | 注：60秒自动关                                                                                                                                                                                                                                                                   |
| 8                                          | PCB主板指示灯                                   | 2、上网灯：初始化连接服务器                                                                                                                                                                                                                                                             | 为闪闪闪直到连到系统服务器转为秒闪                                                                                                                                                                                                                                                          | 注：60秒自动关                                                                                                                                                                                                                                                                   |
| 9                                          | 功能键                                        | 1、电源总开关、2测试键、3复位键                                                                                                                                                                                                                                                          |                                                                                                                                                                                                                                                                            | 注：测试键、复位键，长按3秒起用                                                                                                                                                                                                                                                           |
| 10                                         | 电池                                         | 要求三年使用，另加1520电容放电                                                                                                                                                                                                                                                          | 34615锂亚3.6v、19A一次性电池  (4粒并连总和76Ah)                                                                                                                                                                                                                                         | 注：电池最低2v模块NV不工作是否需做断电处理                                                                                                                                                                                                                                                    |
| 11                                         | 外接传感器                                      | 可支待其它的外接传感器，                                                                                                                                                                                                                                                               | 如温湿度、压力、等环境监测；参数根据所需产品而定                                                                                                                                                                                                                                                   | 接口根据市面传感器通用接口定议，持定                                                                                                                                                                                                                                                         |
| 11                                         | 开机初始化上传                                    | 开机初始化全功能打开，包括4G模块、北斗模块，超过60秒进入休眠状态。如不能运行上传正常数据，关机重新开机照延时60秒。                                                                                                                                                                                                               | 开机初始化全功能打开，包括4G模块、北斗模块，超过60秒进入休眠状态。如不能运行上传正常数据，关机重新开机照延时60秒。                                                                                                                                                                                                               | 开机初始化全功能打开，包括4G模块、北斗模块，超过60秒进入休眠状态。如不能运行上传正常数据，关机重新开机照延时60秒。                                                                                                                                                                                                               |
| 13                                         | 手动测试上传                                     | 主要作于安装前与后台连调测试所有上传数据是否正确； 包括：GPS位置、 NB-IMEI 、4G-ICCID、时间、电量、场强、温度、湿度、图片抓拍；                                                                                                                                                                                                 | 主要作于安装前与后台连调测试所有上传数据是否正确； 包括：GPS位置、 NB-IMEI 、4G-ICCID、时间、电量、场强、温度、湿度、图片抓拍；                                                                                                                                                                                                 | 主要作于安装前与后台连调测试所有上传数据是否正确； 包括：GPS位置、 NB-IMEI 、4G-ICCID、时间、电量、场强、温度、湿度、图片抓拍；                                                                                                                                                                                                 |
| 14                                         | 定时上传打卡                                     | 包括：设备自编码ID、4G卡ICCID、时间、电量、场强、温度、湿度。第一次等待3秒后台握手回应，收到后台握手回应即时进入休眠。如第一次没有收到握手回应，重发第二次在等待3秒握手回应后自动进入休眠。在等待时如有新的指令参数修改应即可更新。注：不需要打开定位，                                                                                                                                          | 包括：设备自编码ID、4G卡ICCID、时间、电量、场强、温度、湿度。第一次等待3秒后台握手回应，收到后台握手回应即时进入休眠。如第一次没有收到握手回应，重发第二次在等待3秒握手回应后自动进入休眠。在等待时如有新的指令参数修改应即可更新。注：不需要打开定位，                                                                                                                                          | 包括：设备自编码ID、4G卡ICCID、时间、电量、场强、温度、湿度。第一次等待3秒后台握手回应，收到后台握手回应即时进入休眠。如第一次没有收到握手回应，重发第二次在等待3秒握手回应后自动进入休眠。在等待时如有新的指令参数修改应即可更新。注：不需要打开定位，                                                                                                                                          |
| 15                                         | 主动上传报警                                     | 1、主动上传位移、倾斜、振动报警，同时起动红外、摄像头图片抓拍，间隔10秒报一次、连续报10次，如后台没有解除报警，自动解除进入休眠状态。 2、上传数据包括设备ID、4G卡ICCID、时间、电量、场强、温度、湿度、位置、图片。注：每次报警前抓拍图片同时发送；  3、 红外感应人与动物，起动拍照上传，注：根据调试情况，按间隔时间或按图片张数上传                                                                                               | 1、主动上传位移、倾斜、振动报警，同时起动红外、摄像头图片抓拍，间隔10秒报一次、连续报10次，如后台没有解除报警，自动解除进入休眠状态。 2、上传数据包括设备ID、4G卡ICCID、时间、电量、场强、温度、湿度、位置、图片。注：每次报警前抓拍图片同时发送；  3、 红外感应人与动物，起动拍照上传，注：根据调试情况，按间隔时间或按图片张数上传                                                                                               | 1、主动上传位移、倾斜、振动报警，同时起动红外、摄像头图片抓拍，间隔10秒报一次、连续报10次，如后台没有解除报警，自动解除进入休眠状态。 2、上传数据包括设备ID、4G卡ICCID、时间、电量、场强、温度、湿度、位置、图片。注：每次报警前抓拍图片同时发送；  3、 红外感应人与动物，起动拍照上传，注：根据调试情况，按间隔时间或按图片张数上传                                                                                               |
| 16                                         | 接收后台指令                                     | 界桩平时处于休眠状态，后台下发修改参数指令只能等待界桩自动苏醒上传打卡给予更新修改； 参数修改内容：定时上传时间间隔、位移、倾斜、振动报警参数值； 遥控指令内容：1、遥毙锁机2、遥晕遥开3、解除报警4、摄像头开关；注：参数修改或指令都需要握手回应； 处于报警待机状态下，支持远程实时图片抓拍回传、摄像头开或关  注：1、遥毙：主要是针对被盗或严重故障乱发数据的给予遥毙，(遥毙后只能现场重新刷机方可重新使用)  2、遥晕：主要是针对乱发数据的给予遥晕，(遥晕后取消发送所有信息，只保留每天照常按时苏醒上传打卡，等待接收新指令解除)； | 界桩平时处于休眠状态，后台下发修改参数指令只能等待界桩自动苏醒上传打卡给予更新修改； 参数修改内容：定时上传时间间隔、位移、倾斜、振动报警参数值； 遥控指令内容：1、遥毙锁机2、遥晕遥开3、解除报警4、摄像头开关；注：参数修改或指令都需要握手回应； 处于报警待机状态下，支持远程实时图片抓拍回传、摄像头开或关  注：1、遥毙：主要是针对被盗或严重故障乱发数据的给予遥毙，(遥毙后只能现场重新刷机方可重新使用)  2、遥晕：主要是针对乱发数据的给予遥晕，(遥晕后取消发送所有信息，只保留每天照常按时苏醒上传打卡，等待接收新指令解除)； | 界桩平时处于休眠状态，后台下发修改参数指令只能等待界桩自动苏醒上传打卡给予更新修改； 参数修改内容：定时上传时间间隔、位移、倾斜、振动报警参数值； 遥控指令内容：1、遥毙锁机2、遥晕遥开3、解除报警4、摄像头开关；注：参数修改或指令都需要握手回应； 处于报警待机状态下，支持远程实时图片抓拍回传、摄像头开或关  注：1、遥毙：主要是针对被盗或严重故障乱发数据的给予遥毙，(遥毙后只能现场重新刷机方可重新使用)  2、遥晕：主要是针对乱发数据的给予遥晕，(遥晕后取消发送所有信息，只保留每天照常按时苏醒上传打卡，等待接收新指令解除)； |
| 17                                         | 抓拍图片                                       | 1、红外感应抓拍：2、报警抓拍：3、定时抓拍：4、手动抓拍；注：手动抓拍是报警状态未解除报警下抓拍                                                                                                                                                                                                                          | 1、红外感应抓拍：2、报警抓拍：3、定时抓拍：4、手动抓拍；注：手动抓拍是报警状态未解除报警下抓拍                                                                                                                                                                                                                          | 1、红外感应抓拍：2、报警抓拍：3、定时抓拍：4、手动抓拍；注：手动抓拍是报警状态未解除报警下抓拍                                                                                                                                                                                                                          |
| 18                                         | 省电处理                                       | 平时单片机、三轴芯片、常待机状态，4G模块、北斗模块处于休眠状态；                                                                                                                                                                                                                                          | 平时单片机、三轴芯片、常待机状态，4G模块、北斗模块处于休眠状态；                                                                                                                                                                                                                                          | 平时单片机、三轴芯片、常待机状态，4G模块、北斗模块处于休眠状态；                                                                                                                                                                                                                                          |
| 19                                         | 定时重起                                       | 终端定时重起：每天定时打卡前断电重起上传打一次或N天重起可设定，为避免终端死机；                                                                                                                                                                                                                                   | 终端定时重起：每天定时打卡前断电重起上传打一次或N天重起可设定，为避免终端死机；                                                                                                                                                                                                                                   | 终端定时重起：每天定时打卡前断电重起上传打一次或N天重起可设定，为避免终端死机；                                                                                                                                                                                                                                   |

**硬件功能需求(专网版)**

| 智慧电子界桩系统 硬件部分        (专网型) BF-DTD003   | 智慧电子界桩系统 硬件部分        (专网型) BF-DTD003   | 智慧电子界桩系统 硬件部分        (专网型) BF-DTD003                                                                                                                                                                                                | 智慧电子界桩系统 硬件部分        (专网型) BF-DTD003                                                                                                                                                                                                | 智慧电子界桩系统 硬件部分        (专网型) BF-DTD003                                                                                                                                                                                                |
|----------------------------------------|----------------------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| 序号                                     | 功能                                     | 功能描述                                                                                                                                                                                                                                | 参 数                                                                                                                                                                                                                                 | 备注                                                                                                                                                                                                                                  |
| 1                                      | 专网数传模块                                 | 动态、静态预警预报监测                                                                                                                                                                                                                         | 通讯模拟：数字DMR、频段：403-470MHz、功率：2-5W、工作电压：7.2V                                                                                                                                                                                          | 通讯模拟：数字DMR、频段：403-470MHz、功率：2-5W、工作电压：7.2V                                                                                                                                                                                          |
| 2                                      | 定位                                     | 北斗/GPS双模跟踪定位                                                                                                                                                                                                                        | 定位精度≤-10m                                                                                                                                                                                                                           | 现改为电池直接备份电                                                                                                                                                                                                                          |
| 3                                      | 北斗天线                                   | 北斗有源陶瓷天线                                                                                                                                                                                                                            | 3.3V、18x18x5mm                                                                                                                                                                                                                      |                                                                                                                                                                                                                                     |
| 5                                      | 三轴芯片                                   | 三轴芯片位移、倾斜、振动报警                                                                                                                                                                                                                      | 位移速度、倾斜45度、连续振动30秒报警                                                                                                                                                                                                                | 采用原U段界桩芯片                                                                                                                                                                                                                           |
| 6                                      | PCB主板指示灯                               | 1、接收信号场强灯：强弱分为三档（高中低）                                                                                                                                                                                                               | 区别秒闪为低、连闪二下为中、连闪三下为高                                                                                                                                                                                                                | 注：指示灯终端进入休眠态自动关闭                                                                                                                                                                                                                    |
|                                        | 电池                                     | 要求三年使用，另加1520电容放电                                                                                                                                                                                                                   | 34615锂亚3.6v、19A一次性电池  (2粒串联 7.2V)                                                                                                                                                                                                   | 注：电池低压保护预防电池爆炸：根据电池厂家最低值做断电处理，等最终测试再商定                                                                                                                                                                                              |
| 7                                      | 功能键                                    | 1、电源总开关、2测试键、3复位键                                                                                                                                                                                                                   |                                                                                                                                                                                                                                     | 注：测试键、复位键，长按3秒起用                                                                                                                                                                                                                    |
| 8                                      | Type-C串口                               | 程序下载、写频、串口打印、预留外接传感器                                                                                                                                                                                                                |                                                                                                                                                                                                                                     |                                                                                                                                                                                                                                     |
| 9                                      | 外接传感器                                  | 可支持其它的外接传感器，                                                                                                                                                                                                                        | 如温湿度、压力、等环境监测；参数根据所需产品而定                                                                                                                                                                                                            | 接口根据市面传感器通用接口定议，持定                                                                                                                                                                                                                  |
| 10                                     | 手动测试上传                                 | 主要作于安装前与后台连调测试所有上传数据是否正确； 包括：GPS位置、设备芯片ID 、时间、电量、场强、温度、湿度。 后台回复延时默认120可调进入休眠状态。注：指示灯终端进入休眠态自动关闭。                                                                                                                                    | 主要作于安装前与后台连调测试所有上传数据是否正确； 包括：GPS位置、设备芯片ID 、时间、电量、场强、温度、湿度。 后台回复延时默认120可调进入休眠状态。注：指示灯终端进入休眠态自动关闭。                                                                                                                                    | 主要作于安装前与后台连调测试所有上传数据是否正确； 包括：GPS位置、设备芯片ID 、时间、电量、场强、温度、湿度。 后台回复延时默认120可调进入休眠状态。注：指示灯终端进入休眠态自动关闭。                                                                                                                                    |
| 11                                     | 定时上传打卡                                 | 包括：设备自编码ID、时间、电量、场强、温度、湿度。第一次等待3秒后台握手回应，收到后台握手回应即时进入休眠。如第一次没有收到握手回应，重发第二次在等待3秒握手回应后自动进入休眠。在等待时如有新的指令参数修改应即可更新。注：不需要打开定位，接收基站同时给予终端时间受时。                                                                                             | 包括：设备自编码ID、时间、电量、场强、温度、湿度。第一次等待3秒后台握手回应，收到后台握手回应即时进入休眠。如第一次没有收到握手回应，重发第二次在等待3秒握手回应后自动进入休眠。在等待时如有新的指令参数修改应即可更新。注：不需要打开定位，接收基站同时给予终端时间受时。                                                                                             | 包括：设备自编码ID、时间、电量、场强、温度、湿度。第一次等待3秒后台握手回应，收到后台握手回应即时进入休眠。如第一次没有收到握手回应，重发第二次在等待3秒握手回应后自动进入休眠。在等待时如有新的指令参数修改应即可更新。注：不需要打开定位，接收基站同时给予终端时间受时。                                                                                             |
| 12                                     | 主动上传报警                                 | 1、报警上传数据是指终端：位移、倾斜、振动报警，此时全功能打开，包括北斗模块，默认间隔10秒报一次可调、默认连读报10次可调，如后台没有解除报警，自动解除进入休眠状态。  2、上传数据包括设备自编码ID、时间、电量、场强、温度、湿度、位置。                                                                                                            | 1、报警上传数据是指终端：位移、倾斜、振动报警，此时全功能打开，包括北斗模块，默认间隔10秒报一次可调、默认连读报10次可调，如后台没有解除报警，自动解除进入休眠状态。  2、上传数据包括设备自编码ID、时间、电量、场强、温度、湿度、位置。                                                                                                            | 1、报警上传数据是指终端：位移、倾斜、振动报警，此时全功能打开，包括北斗模块，默认间隔10秒报一次可调、默认连读报10次可调，如后台没有解除报警，自动解除进入休眠状态。  2、上传数据包括设备自编码ID、时间、电量、场强、温度、湿度、位置。                                                                                                            |
| 13                                     | 接收后台指令                                 | 界桩平时处于休眠状态，后台下发修改参数指令只能等待界桩自动苏醒上传打卡给予更新修改； 参数修改内容：定时上传时间间隔、位移、倾斜、振动报警参数值； 遥控指令内容：遥毙锁机、2遥晕遥开、解除报警；注：参数修改或指令都需要握手回应 注：1、遥毙：主要是针对被盗或严重故障乱发数据的给予遥毙，(遥毙后只能现场重新刷机才能重新使用)  2、遥晕：主要是针对乱发数据的给予遥晕，(遥晕后取消发送开机及其它动态报警，但每天照常按时苏醒上传打卡，等待接收新指令解除)； | 界桩平时处于休眠状态，后台下发修改参数指令只能等待界桩自动苏醒上传打卡给予更新修改； 参数修改内容：定时上传时间间隔、位移、倾斜、振动报警参数值； 遥控指令内容：遥毙锁机、2遥晕遥开、解除报警；注：参数修改或指令都需要握手回应 注：1、遥毙：主要是针对被盗或严重故障乱发数据的给予遥毙，(遥毙后只能现场重新刷机才能重新使用)  2、遥晕：主要是针对乱发数据的给予遥晕，(遥晕后取消发送开机及其它动态报警，但每天照常按时苏醒上传打卡，等待接收新指令解除)； | 界桩平时处于休眠状态，后台下发修改参数指令只能等待界桩自动苏醒上传打卡给予更新修改； 参数修改内容：定时上传时间间隔、位移、倾斜、振动报警参数值； 遥控指令内容：遥毙锁机、2遥晕遥开、解除报警；注：参数修改或指令都需要握手回应 注：1、遥毙：主要是针对被盗或严重故障乱发数据的给予遥毙，(遥毙后只能现场重新刷机才能重新使用)  2、遥晕：主要是针对乱发数据的给予遥晕，(遥晕后取消发送开机及其它动态报警，但每天照常按时苏醒上传打卡，等待接收新指令解除)； |
| 14                                     | 设置软件                                   | 1、参数设置：参照原有的BFP-AH141(DT100)设置软件；注：具体功能实际惰况调整，报警间隔时间与报警次数默认及可调 2、电台设置：常规DMR电台设置：终端ID、频点、信道、时隙、色码、发射功率可调高5W低2W、归属群组；                                                                                                                 | 1、参数设置：参照原有的BFP-AH141(DT100)设置软件；注：具体功能实际惰况调整，报警间隔时间与报警次数默认及可调 2、电台设置：常规DMR电台设置：终端ID、频点、信道、时隙、色码、发射功率可调高5W低2W、归属群组；                                                                                                                 | 1、参数设置：参照原有的BFP-AH141(DT100)设置软件；注：具体功能实际惰况调整，报警间隔时间与报警次数默认及可调 2、电台设置：常规DMR电台设置：终端ID、频点、信道、时隙、色码、发射功率可调高5W低2W、归属群组；                                                                                                                 |
| 15                                     | 省电处理                                   | 平时单片机、三轴芯片、常待机状态，北斗模块处于休眠状态；                                                                                                                                                                                                        | 平时单片机、三轴芯片、常待机状态，北斗模块处于休眠状态；                                                                                                                                                                                                        | 平时单片机、三轴芯片、常待机状态，北斗模块处于休眠状态；                                                                                                                                                                                                        |

**硬件功能需求(专网基站)**

|   序号 | 名称       | 功能描述与解释                                                                                                                                                                                                                                                                                                                                                                                                                       | 备注                                                                                                                                   |
|------|----------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------------------------------------------------------------------------------------------------------------------------------|
|    1 | 通讯模式     | 基站控制器必须具备三个通道：1、公网4G全网通 2、专网电台 3、以太网三网融合，根据现场环境灵活组网；                                                                                                                                                                                                                                                                                                                                                                          |                                                                                                                                      |
|    2 | 组网模式应用场景 | 1、窄带专网电台是基站标配，主要负责接收界桩上传数据，以及下达各项指令，同时负责上一级基站链路中继通道；                                                                                                                                                                                                                                                                                                                                                                          |                                                                                                                                      |
|    2 | 组网模式应用场景 | 2、以太网、4G网主要负责基站与服务器数据交互，根据现场通讯环境二选一，作为公网数据通道；                                                                                                                                                                                                                                                                                                                                                                                 |                                                                                                                                      |
|    2 | 组网模式应用场景 | 3、为确保数据空中传输采用双网通讯，如系统设置默认某个通道、另一个为备份通道，当默认通道故障自动切换备份通道。                                                                                                                                                                                                                                                                                                                                                                       |                                                                                                                                      |
|    2 | 组网模式应用场景 | 4、根据不同场地环境进行组网，详见系统架构图1-4图，                                                                                                                                                                                                                                                                                                                                                                                                   |                                                                                                                                      |
|    3 | 北斗、GPS双模 | 1、防被盗 2、跟踪定位，注：基站时间以服务器时为准，每次与服务器握手同时较准，在给界桩较时；       ，                                                                                                                                                                                                                                                                                                                                                                       | 标配：加纽扣电池存储最后数据                                                                                                                       |
|    4 | 三轴芯片     | 位移、倾斜报警，防止遇人为破坏或自然灾害倾斜倒地能够及时现场维护；                                                                                                                                                                                                                                                                                                                                                                                             | 标配：                                                                                                                                  |
|    5 | 温度芯片     | 监测基站控制器的温度值；因为所有基站都是安装在野外，受高低温超过器件承受值，随时了解便于处理；                                                                                                                                                                                                                                                                                                                                                                               | 不需要加湿度                                                                                                                               |
|    5 | 4G信号灯    | 根据当前信号强弱分为三档高中低，秒闪为低、连闪二下为中、连闪三下为高；三档三个标置位区别发送                                                                                                                                                                                                                                                                                                                                                                                |                                                                                                                                      |
|    6 | 调试串口     | 程序下载、串口打印、预留外接传感器                                                                                                                                                                                                                                                                                                                                                                                                             | 串口打印：包括数据接收发打印                                                                                                                       |
|    7 | 控制器终端功能  | 开机运行：开机初始化全功能运行 发送数据包括：基站ID、4GICCID卡号、定位信息、时间、电量值、场强值、驻波值、温度值、倾斜报警。现使用通道名称(窄带专网、4G、以太网)，是有功能数据参数是否符合设置要求；  数据接收：控制器接收到界桩上传数据调制解调，在本站即时回应界桩终端，同时将数据压缩转发到服务器。 数据存储：当后台发送各项数据指令到控制器备份存储，等待界桩上传打卡即时下发指令，如界桩第一次没有即时回应的，在补发第二次，如2次都没有收到回应，控制器停止发送执行其它任务。注：利用心跳握手将更新的指令存储。 心跳握手：由控制器主动与服务器心跳握手，默认每300秒1次，服务器超过600秒未收到控制器心跳握手，服务 器主动向控制器请求握手，握手不到当故障处理。支持手动远程查询状态。 6、心跳握手发送数据包括：基站ID、时间、定位信息、当前电台信道、当前网络通道、专网场强、公网场强、电台天线驻波比、电压值、温度值； | 1、为什么要对基站监测：该系统称”智慧监控系统” 2、当然也需要对各硬件模块有效监测，场强、驻波比、电压、是否定位、温度，随时了解基站各模块运行状态，如有故障便于即时维护 3、省电处理：根据实具各模块应用，该做省电都做得省电处理： 4定位信息、电台天线驻波 待讨论 |
|    8 | 设置软件     | 1、IP地址：服务器IP地址、端口号； 2、通道选择：使用通道选择：窄带专网、以太网、4G网，默认通道、备份通道； 3、心跳握手：默认为300秒1次可调(支持远程修改) 注：当界桩排队打卡应停止心跳，等60秒无打卡继续心跳 4、终端重起：默认时间为00.00.00秒可调； (支持远程重起：包括4G路由、控制器) 5、基站中继：选择基站或中继，  6、倾斜角度：默认为45度，倾斜角度可调；  7、移位：报警起动定位跟踪在20米内还是按原位，超过20米按新的位置刷新。(报警跟踪间隔--N秒可调) 8、电台设置：常规DMR电台设置：终端ID、频点、信道、时隙、色码、发射功率可调高中低W、归属群组； 9、 (支持远程修改：切换信道、切换发射功率)                                                                                           |                                                                                                                                      |
|    9 | 省电处理     | 平时单片机、三轴芯片、常待机状态，4G模块、北斗模块处于休眠状态等..该省电都得做省电处理；                                                                                                                                                                                                                                                                                                                                                                                | 平时单片机、三轴芯片、常待机状态，4G模块、北斗模块处于休眠状态等..该省电都得做省电处理；                                                                                       |

**基站控制器前后面板指示灯与接口**

测试键

4G灯

以太网

GPS灯

电台2灯

电台1灯

电源开关

背面

4G无线口

GPS天线口

网口

调试口

电台2

电台1

接地

复位

DC电源口

**3、基站控制器前后面板灯接口用途**

| 序号   | 正面    | 功能需求描述                                     | 备注   |
|------|-------|--------------------------------------------|------|
| 1    | 电台1灯  | 电台数据收发是否正常工作：采用双色灯RX绿色、TX红色，               | 双色灯  |
| 2    | 电台2灯  | 电台数据收发是否正常工作：采用双色灯RX绿色、TX红色，               | 双色灯  |
| 3    | GPS灯  | GPS是否正常工作：终端受时、防盗跟踪定位，采用双色灯红色秒闪未定位、绿秒闪为己定位 | 双色灯  |
| 4    | 以太网   | 观察以太网是否正常工作：采用双色灯红色秒闪为未连接、绿色秒闪为己连接         | 双色灯  |
| 5    | 4G信号灯 | 根据当前信号强弱分为三档高中低，秒闪为低、连闪二下为中、连闪三下为高         | 单色灯  |
| 6    | 电源开关  |                                            |      |
| 7    | 测试键   | 开机初始化所有功能模块起动测试，在后台查看功能模块运行数据状态是否能达到安装标准   | 规格持定 |
|      | 背面    | 功能需求描述                                     |      |
| 1    | 电源口   | DC5-14V输入                                  | 规格持定 |
| 2    | 电台接口  | 电台1、电台2串口接入                                | 规格持定 |
| 4    | 调试口   | 程序下载、串口打印包括：数据接收、数据上传                      | 规格持定 |
| 5    | 网口    | 考虑是否支持双网口：                                 | 规格持定 |
| 6    | 接地    | 4mm供螺纹接地线                                  |      |
| 7    | GPS天线 | SMA-KWE 外螺内孔/针 加长 射频座 RF天线座 弯头卧式 天线接头      |      |
| 8    | 4G天线  | SMA-KWE 外螺内孔/针 加长 射频座 RF天线座 弯头卧式 天线接头      |      |

**系统软件功能需求**

| 序号   | 功能           | 功能描述                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  | 原有√              | 新增★   |
|------|--------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|------------------|-------|
| 1    | 权限管理         | 系统可按客户端权限级别登入管理，如：高级管理员、片区管理员、普通操作管理员、维护管理员等；                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         | √                |       |
| 2    | 地图管理         | 可在地图上对所有设备终端安装位置，按不同类型不同图标标注，如：基站、各类传感器设备终端、监控中心、管理站等管理；                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              | √                |       |
| 3    | 设备管理         | 可按不同类型设备终端ID编号、物联卡ID、物联卡绑定流量、物联卡起止时间、相对应类型图标、设置不同参数、所属部门单位等管理；                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        | √需在优化            | ★     |
| 4    | 人员管理         | 人员名称角色如：高级管理员、值班员、系统维护员、联系方式等管理；                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      | √需在优化            |       |
| 5    | 图像管理         | 对所有终端设备实地安装位置图影像管理，如：360全景影像、图片、视频等管理；                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                | √需在优化            |       |
| 6    | 报警管理         | 系统可根据各类型设备终端传感器报警，即在电子地图视野弹框居中、报警处理、处理结果存档等管理；报警                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      | √需在优化            |       |
| 7    | 空中指令         | 可通过系统空中指令下发，包括：1、报警解除、2遥毙锁机、3遥晕遥开、4修改设置参数等； 注：1、遥毙：遥毙后只能是重新刷机才能重新使用；(主要是针对被盗或严重故障乱发数据) 2、遥晕：取消发送其它动态报警，但每天照常按时苏醒上传打卡，等待接收新指令解除；                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       | √需在优化            |       |
| 8    | 数据管理         | 对所有终端设备添加、删除、更改，管理人员、地图标注点等，数据导入导出报表生成管理；                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             | √需在优化            |       |
| 9    | 巡查管理         | 可根据系排班巡查、也可自由巡查，巡查员通过APP手机到设备终端点位打卡，打卡成功上传系统存储，上传数据包括：打卡员ID、打卡点ID、时间；注：如打卡当地无网络上传，处理方式：1应保存本机等待下一个点有网络一起发送，2、本机存储2000条信息，起过存储量打卡将会提示存储己满请上传系统；3、报警历吏查询：需要在地图轨迹回放；                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     |                  | ★     |
| 10   | 巡查轨迹管理       | 按时间段总查询或单人巡查历吏轨迹数据统计，导出报表生成管理，也可通过系统GIS地图回放历吏轨迹；                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      |                  | ★     |
| 11   | 数据查询         | 对所有设备终端、专网基站、人员、巡查点、地图标注点、终端设备运行状态，可按单设备终端或时间段调用查询等管理；                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                | √                | ★     |
| 12   | 统计管理         | 1、统计终端设备总数、各类型、管理人员、巡查人员统计总数，数据导出报表生成等管理； 2、针对设备终端故障未按时收到信息的、报警、终端低电压、遥毙、遥晕、4G流量卡等数据统计管理；                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             | √需在优化            |       |
| 13   | 终端提示         | 设备终端提示指：未按时收到信息提示、低电压提示、遥毙、遥晕、4G卡到期等提示；注：分4类存储、查询 1、未按时收到信息提示：指的是按系统设置对设备终端定时上传打卡信息，如连读3天末收到信息的示为设备故障； 2、终端低电压提示：按不同类型终端设置最低参数值，低于参数值提示；注：预留15天右左更换电池（电压值等测试） 3、遥毙、遥晕提示：指的是系统空中遥毙、遥晕记录，便于今后重新开起使用； 4、4G卡到期提示：按购卡开通之日起到之日让，以天为计算提前10天提示。避免停机影响设备正常运行； 提示方式及解释：在系统安装完毕等待验收期间，可能有部分设备存在问题来不及调整，会导致验收无法通过，因此该功能做半公开模式。在系统界面铃铛标记可选择关或开信息，在验收期间先选择关闭，信息暂时屏蔽显示无记录，打开屏蔽开关：正常提示各项信息提示，统计导出报表便于维护员现场处理；                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         | √需在优化 √需在优化√需在优化 | ★ ★   |
| 11   | 系统界面         | 由各模块组成如下：系统登入、数据录入、数据查询、帮助，系统连接状态、终端设备异常、设置、列表屏蔽、默认高德地图 系统登入：系统编号、登入名称、密码； 数据录入： 使用单位管理：包括所属单位、下属部门名称信息，排序值如：市级、县级、乡镇； 系统管理员：包括密码及权限、用户ID编号、用户名称、所属单位、联系方式； 专网基站管理：安装地点名称及自编号ID、单位、类型(中继、基站)、网络类型(专网、4G网、有线)上级基站、基站通道、经纬度、地图放大级别、4G卡ID、4G卡起止时间、备注栏； 终端设备管理： 专网：安装地点名称及自编号ID、单位、设备ID、(桩体材质或其它用途)、上级基站、基站通道、排队号、唤醒基准时间、打卡间隔时间、报警间隔时间、经纬度、地图放大级别、备注栏； 公网：安装地点名称及自编号ID、单位、设备ID、(桩体材质或其它用途)、唤醒基准时间、打卡间隔时间、报警间隔时间、经纬度、地图放大级别、4G卡ID、4G卡起止时间、备注栏；   图影像管理：对所有终端设备实地安装位置图影像管理，如：360全景影像、图片、视频等管理； 巡查管理：NFC自编号ID、地点名称、APP终端ID、所属人姓名、所属单位、排班时间、巡查线路、巡查规则； 3、历史数据查询： 专网基站查询：运行状态历史数据查询，可按基站自编号ID时间段查询(包括：上线时间、心跳、离线时间、) 注：系统保存三个月  终端设备查询：运行状态历史数据系统保存三个月，可按单位名称或设备终端自编号ID，按时间段查询(包括：安装位置、影像、上下线、打卡时间、4G卡ID、4G卡起止时间、备注) 系统操作历史查询：为更有效管理系统数据库正常运行，对所有操作员在权限范围内所操作各项内容系统自动保存，防止误操作或恶意破坏系统数据库有据可查； 内容包括(系统登入密码、人员权限密码、地图标注、各设备终端，新增、修改、删除、空中各项指令等数据) 快捷查询：在地图界面快捷查询设备终端自动居中，同时列表自动展开； 异常查询：在界面铃铛点击查询设备终端异常信息：未按时打卡、低电压、遥毙、遥晕、4G卡到期； 4、系统设置：   1) 屏幕切换：可切换全屏或关闭全屏； 2) 滚动标题：在设置栏选择滚动标题，进行编辑使用单位名称，可开或停止标题滚动，单位图标、系统界面登入低景图更换； 3) 单位树排序：指的是在屏幕右边列表上级单位、下属部门及人员树形排序如：局机关为最顶一二三处顺序往下； 4) 设备终端树排序：局机关为最顶一二三处顺序往下； 5) 选择地图显示：已安装未安装设备； 6) 报警弹窗：选择报警是否弹窗； 7) 巡查打卡弹窗：选择是否打卡弹窗； 8) 报警响铃：选择报警是否响铃； 8) 个人设置：修改登入密码：可在个人设置框修改登入密码； 9) 系统设置：设置使用单位标题、单位图标； 10) 服务器设置：系统名称、服务器地让、服务器端口 11) 安全设置：名称、原密码、新密码； 5、帮助：            1) 系统操作文档明书：随系统一起安装便于操作随时调用查看；            2) 系统版本信息：客户端版本号、创建时间、服务器版本号、运行时间、运行时长；            3) 指令日志：所有设备终端上传数据及下发指令打印，主要限制于系统调试、安装及维护人员查看；            4) 终端安装：终端安装情况统计，末安装设备桩体、末安装设备、完整安装；此项需移到系统查询里            5) APP程序下载：下载程序给APP手机使用，可以PC客户端同样帐号、同样有权限；？？  6、列表动态展示：            1) 列表展示：展示所有的人员及设备终端，可选择打开收起或展开； 2) 列表动态展示：以四种颜色区分，注：以下颜色区分列表与地图标注点同步 A) 绿色：终端设备为正常在线； B) 灰色：终端设备超过三天未上传打卡的视为故障离线； C) 红色：终端设备处于位移、倾斜、振动报警；  D) 橙色：终端设备异常分为五种状态，末按时上打卡、电压低、遥毙、遥晕、4G卡即将到期异常； 7、动态弹窗提示：           1)初始化开栈弹窗：主要是核对设备安装前上传的数据信息是否正确 2)报警弹窗：当设备处于紧急报警，在电子地图视野放大居中, 并弹窗展示该设备位置及相关信息； 3)巡查打卡弹窗：当巡查人员到达打卡点打卡，在电子地图视野弹窗展示该设备位置及相关信息； 4) 空中指令弹窗：报警解除、遥毙停机、修改参数指令发送弹窗，设备终端收到回应弹窗； 8、地图操作：地图放大缩小、全屏切换、测距、终端标注、红线图连线、地图卫星图切换、导航； | √                | ★     |
|      | 服务器与基站心跳握手   | 心跳握手：由控制器主动与服务器心跳握手，默认每300秒1次，服务器超过600秒未收到控制器心跳握手，服务器主动向控制器请求握手，握手不到当故障处理； 心跳握手数据包括：基站ID、时间、定位信息、当前电台信道、当前网络通道、专网场强、公网场强、电台天线驻波比、电压值、温度值； 注：利用心跳握手将更新的指令发送基站存储本站，等待界桩上打卡即给下发，更新成功即时回应后台记录存档。                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          |                  |       |
| 14   | 手机APP考勤签到打卡： | 1、NFC打卡：注：为更加人性化增加语音及振动提示：当巡查人员进入设定半径范围内，提示(您己进打卡区域请打卡) 2、定位打卡：注：为更加人性化增加语音及振动提示：当巡查人员进入设定半径范围内，提示(您己进打卡区域请打卡) 3、终端异常：通过手机APP拍照、视频、文字说明。                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              |                  | ★     |
| 15   | 二维码          | 可通过公众手机微信APP扫码，获取终端设备信息；                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              |                  | ★     |

台系统居于之前针对广州白云山智慧界桩为基础，定制的终端产品是UHF段专网，根据市场需求界桩传感器大部分采用4G/5G公网智慧界桩，为能适应市场需求本次专为4G公网基础型1.0版本新产品开发，主要用于智慧界桩、名贵古树、山体滑坡等振动、位移、倾斜、定位跟踪等监控管理，同时也考虑今后接入第三方的4G公网多元化物联网传感的应用，应用前景以遥控、监测、感应为主，对后台系统也相应升级优化。

**目前计划分为三步：** 1、4G公网基础型，2、4G公网基加强型(带线红外、摄像抓拍)，第一项基本完成、第二项进行中。

**2024.11.28之起：安排** 3、UHF段专网界桩终端   4、基站数传25W电台以及基站控制器终端。

**专网基站箱内部模块组合图**

其它传感器

系统服务器

4G路由器

(购买现成产品)

太阳能控制器

(购买现成产品)

网关控制器

TC717还得预留一个口接其它传感器

![Image](北峰电子界桩项目功能需求开发文挡_artifacts/image_000000_7d05bd09314dded2cb45598d358149111147e1752c3a01296587da2f92f6b891.png)

局域网

串口2

太阳能板

数传电台2

负责下发指令

数传电台1

负责接收数据

串口1

蓄电池输出12V，网关控制器、数传电台做宽压10-15V。

、

注：基站网关控制器与电台需求方案如下

1、按原规划1台电负责收发，采用2个信道扫描跳频方式，1信道接收跳2信道发送、 2信道接收跳1信道发送。

2、现根据敬寿意建；采用2台电台，1台电负责接收界桩上传数据，同时下发握手指令，另1台电负责上一级中继的收发。

3、根据以上的需求；TC717网关控制器就得预留3个接口，1口负责界桩、2口台电负责上一级中继、3口负责外接传感器。

4、综合以上二种方案需求；根据实际场景在设置程序软件选择单电台或双电台模式，满足二种方案需求。

5、根据实际场景电台采用二款选择； DTR908电台25W模块、另一款BF-DTD003 **界桩** 5W模块。

6、TC717网关控制器；每天定时断电重起1次，也可支持远程重起。

![Image](北峰电子界桩项目功能需求开发文挡_artifacts/image_000001_fa32316cc15602d2c6340ba844d7b67809b9963d7caaf3e22da9b9511ac0fbf0.png)

![Image](北峰电子界桩项目功能需求开发文挡_artifacts/image_000002_ad51925e398ee311c9d7386b08e52b041b4e2a11c39fbdc04185e65fa294e630.png)
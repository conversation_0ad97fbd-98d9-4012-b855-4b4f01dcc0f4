//generated by ygen-gocrud-proto. DO NOT EDIT.
//source: bysdb.proto
syntax = "proto3";
package bysdb;

import "crud.proto";
import "bysdb.proto"; 
import "bysdb.list.proto"; 
option go_package="git.kicad99.com/gzdev/bys/doc/bysdb";

//普通crud，没有权限检查
service   RpcDbController {
//插入一行数据
rpc Insert( DbController ) returns (crud.DMLResult);

//以RID为条件,全量修改一行数据
rpc Update( DbController ) returns (crud.DMLResult);

//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
rpc PartialUpdate( DbController ) returns (crud.DMLResult);

//以RID为条件,删除一行数据
rpc Delete( DbController ) returns (crud.DMLResult);

//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
rpc SelectOne( crud.DMLParam ) returns ( DbController );

//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
rpc SelectMany( crud.DMLParam ) returns ( stream DbController );

//根据条件查询数据
rpc Query( crud.QueryParam ) returns ( stream DbController );
//根据条件查询数据,批量返回数据,crud.QueryParam.Batch指定一次返回多少条数据
rpc QueryBatch( crud.QueryParam ) returns ( stream DbControllerList );
}

//带权限检查(有相应的群组权限和编辑权限)的crud
service   PrpcDbController {
//插入一行数据
rpc Insert( DbController ) returns (crud.DMLResult);

//以RID为条件,全量修改一行数据
rpc Update( DbController ) returns (crud.DMLResult);

//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
rpc PartialUpdate( DbController ) returns (crud.DMLResult);

//以RID为条件,删除一行数据
rpc Delete( DbController ) returns (crud.DMLResult);

//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
rpc SelectOne( crud.DMLParam ) returns ( DbController );

//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
rpc SelectMany( crud.DMLParam ) returns ( stream DbController );

//根据条件取得我有权限的数据
rpc Query( crud.PrivilegeParam ) returns ( stream DbController );
//根据条件取得我有权限的数据,批量返回数据,crud.PrivilegeParam.QueryParam.Batch指定一次返回多少条数据
rpc QueryBatch( crud.PrivilegeParam ) returns ( stream DbControllerList );
}

//普通crud，没有权限检查
service   RpcDbBysMarker {
//插入一行数据
rpc Insert( DbBysMarker ) returns (crud.DMLResult);

//以RID为条件,全量修改一行数据
rpc Update( DbBysMarker ) returns (crud.DMLResult);

//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
rpc PartialUpdate( DbBysMarker ) returns (crud.DMLResult);

//以RID为条件,删除一行数据
rpc Delete( DbBysMarker ) returns (crud.DMLResult);

//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
rpc SelectOne( crud.DMLParam ) returns ( DbBysMarker );

//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
rpc SelectMany( crud.DMLParam ) returns ( stream DbBysMarker );

//根据条件查询数据
rpc Query( crud.QueryParam ) returns ( stream DbBysMarker );
//根据条件查询数据,批量返回数据,crud.QueryParam.Batch指定一次返回多少条数据
rpc QueryBatch( crud.QueryParam ) returns ( stream DbBysMarkerList );
}

//带权限检查(有相应的群组权限和编辑权限)的crud
service   PrpcDbBysMarker {
//插入一行数据
rpc Insert( DbBysMarker ) returns (crud.DMLResult);

//以RID为条件,全量修改一行数据
rpc Update( DbBysMarker ) returns (crud.DMLResult);

//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
rpc PartialUpdate( DbBysMarker ) returns (crud.DMLResult);

//以RID为条件,删除一行数据
rpc Delete( DbBysMarker ) returns (crud.DMLResult);

//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
rpc SelectOne( crud.DMLParam ) returns ( DbBysMarker );

//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
rpc SelectMany( crud.DMLParam ) returns ( stream DbBysMarker );

//根据条件取得我有权限的数据
rpc Query( crud.PrivilegeParam ) returns ( stream DbBysMarker );
//根据条件取得我有权限的数据,批量返回数据,crud.PrivilegeParam.QueryParam.Batch指定一次返回多少条数据
rpc QueryBatch( crud.PrivilegeParam ) returns ( stream DbBysMarkerList );
}

//普通crud，没有权限检查
service   RpcDbControllerOnlineHistory {
//插入一行数据
rpc Insert( DbControllerOnlineHistory ) returns (crud.DMLResult);

//根据条件查询数据
rpc Query( crud.QueryParam ) returns ( stream DbControllerOnlineHistory );
//根据条件查询数据,批量返回数据,crud.QueryParam.Batch指定一次返回多少条数据
rpc QueryBatch( crud.QueryParam ) returns ( stream DbControllerOnlineHistoryList );
}

//带权限检查(有相应的群组权限和编辑权限)的crud
service   PrpcDbControllerOnlineHistory {
//插入一行数据
rpc Insert( DbControllerOnlineHistory ) returns (crud.DMLResult);

//根据条件取得我有权限的数据
rpc Query( crud.PrivilegeParam ) returns ( stream DbControllerOnlineHistory );
//根据条件取得我有权限的数据,批量返回数据,crud.PrivilegeParam.QueryParam.Batch指定一次返回多少条数据
rpc QueryBatch( crud.PrivilegeParam ) returns ( stream DbControllerOnlineHistoryList );
}

//普通crud，没有权限检查
service   RpcDbMediaInfo {
//插入一行数据
rpc Insert( DbMediaInfo ) returns (crud.DMLResult);

//以RID为条件,全量修改一行数据
rpc Update( DbMediaInfo ) returns (crud.DMLResult);

//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
rpc PartialUpdate( DbMediaInfo ) returns (crud.DMLResult);

//以RID为条件,删除一行数据
rpc Delete( DbMediaInfo ) returns (crud.DMLResult);

//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
rpc SelectOne( crud.DMLParam ) returns ( DbMediaInfo );

//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
rpc SelectMany( crud.DMLParam ) returns ( stream DbMediaInfo );

//根据条件查询数据
rpc Query( crud.QueryParam ) returns ( stream DbMediaInfo );
//根据条件查询数据,批量返回数据,crud.QueryParam.Batch指定一次返回多少条数据
rpc QueryBatch( crud.QueryParam ) returns ( stream DbMediaInfoList );
}

//带权限检查(有相应的群组权限和编辑权限)的crud
service   PrpcDbMediaInfo {
//插入一行数据
rpc Insert( DbMediaInfo ) returns (crud.DMLResult);

//以RID为条件,全量修改一行数据
rpc Update( DbMediaInfo ) returns (crud.DMLResult);

//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
rpc PartialUpdate( DbMediaInfo ) returns (crud.DMLResult);

//以RID为条件,删除一行数据
rpc Delete( DbMediaInfo ) returns (crud.DMLResult);

//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
rpc SelectOne( crud.DMLParam ) returns ( DbMediaInfo );

//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
rpc SelectMany( crud.DMLParam ) returns ( stream DbMediaInfo );

//根据条件取得我有权限的数据
rpc Query( crud.PrivilegeParam ) returns ( stream DbMediaInfo );
//根据条件取得我有权限的数据,批量返回数据,crud.PrivilegeParam.QueryParam.Batch指定一次返回多少条数据
rpc QueryBatch( crud.PrivilegeParam ) returns ( stream DbMediaInfoList );
}

//普通crud，没有权限检查
service   RpcDbMarkerHistory {
//插入一行数据
rpc Insert( DbMarkerHistory ) returns (crud.DMLResult);

//根据条件查询数据
rpc Query( crud.QueryParam ) returns ( stream DbMarkerHistory );
//根据条件查询数据,批量返回数据,crud.QueryParam.Batch指定一次返回多少条数据
rpc QueryBatch( crud.QueryParam ) returns ( stream DbMarkerHistoryList );
}

//带权限检查(有相应的群组权限和编辑权限)的crud
service   PrpcDbMarkerHistory {
//插入一行数据
rpc Insert( DbMarkerHistory ) returns (crud.DMLResult);

//根据条件取得我有权限的数据
rpc Query( crud.PrivilegeParam ) returns ( stream DbMarkerHistory );
//根据条件取得我有权限的数据,批量返回数据,crud.PrivilegeParam.QueryParam.Batch指定一次返回多少条数据
rpc QueryBatch( crud.PrivilegeParam ) returns ( stream DbMarkerHistoryList );
}

//普通crud，没有权限检查
service   RpcDbMarkerPatrolHistory {
//插入一行数据
rpc Insert( DbMarkerPatrolHistory ) returns (crud.DMLResult);

//根据条件查询数据
rpc Query( crud.QueryParam ) returns ( stream DbMarkerPatrolHistory );
//根据条件查询数据,批量返回数据,crud.QueryParam.Batch指定一次返回多少条数据
rpc QueryBatch( crud.QueryParam ) returns ( stream DbMarkerPatrolHistoryList );
}

//带权限检查(有相应的群组权限和编辑权限)的crud
service   PrpcDbMarkerPatrolHistory {
//插入一行数据
rpc Insert( DbMarkerPatrolHistory ) returns (crud.DMLResult);

//根据条件取得我有权限的数据
rpc Query( crud.PrivilegeParam ) returns ( stream DbMarkerPatrolHistory );
//根据条件取得我有权限的数据,批量返回数据,crud.PrivilegeParam.QueryParam.Batch指定一次返回多少条数据
rpc QueryBatch( crud.PrivilegeParam ) returns ( stream DbMarkerPatrolHistoryList );
}

//普通crud，没有权限检查
service   RpcDbNFCPatrolLine {
//插入一行数据
rpc Insert( DbNFCPatrolLine ) returns (crud.DMLResult);

//以RID为条件,全量修改一行数据
rpc Update( DbNFCPatrolLine ) returns (crud.DMLResult);

//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
rpc PartialUpdate( DbNFCPatrolLine ) returns (crud.DMLResult);

//以RID为条件,删除一行数据
rpc Delete( DbNFCPatrolLine ) returns (crud.DMLResult);

//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
rpc SelectOne( crud.DMLParam ) returns ( DbNFCPatrolLine );

//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
rpc SelectMany( crud.DMLParam ) returns ( stream DbNFCPatrolLine );

//根据条件查询数据
rpc Query( crud.QueryParam ) returns ( stream DbNFCPatrolLine );
//根据条件查询数据,批量返回数据,crud.QueryParam.Batch指定一次返回多少条数据
rpc QueryBatch( crud.QueryParam ) returns ( stream DbNFCPatrolLineList );
}

//带权限检查(有相应的群组权限和编辑权限)的crud
service   PrpcDbNFCPatrolLine {
//插入一行数据
rpc Insert( DbNFCPatrolLine ) returns (crud.DMLResult);

//以RID为条件,全量修改一行数据
rpc Update( DbNFCPatrolLine ) returns (crud.DMLResult);

//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
rpc PartialUpdate( DbNFCPatrolLine ) returns (crud.DMLResult);

//以RID为条件,删除一行数据
rpc Delete( DbNFCPatrolLine ) returns (crud.DMLResult);

//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
rpc SelectOne( crud.DMLParam ) returns ( DbNFCPatrolLine );

//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
rpc SelectMany( crud.DMLParam ) returns ( stream DbNFCPatrolLine );

//根据条件取得我有权限的数据
rpc Query( crud.PrivilegeParam ) returns ( stream DbNFCPatrolLine );
//根据条件取得我有权限的数据,批量返回数据,crud.PrivilegeParam.QueryParam.Batch指定一次返回多少条数据
rpc QueryBatch( crud.PrivilegeParam ) returns ( stream DbNFCPatrolLineList );
}

//普通crud，没有权限检查
service   RpcDbNFCPatrolLineDetail {
//插入一行数据
rpc Insert( DbNFCPatrolLineDetail ) returns (crud.DMLResult);

//以RID为条件,全量修改一行数据
rpc Update( DbNFCPatrolLineDetail ) returns (crud.DMLResult);

//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
rpc PartialUpdate( DbNFCPatrolLineDetail ) returns (crud.DMLResult);

//以RID为条件,删除一行数据
rpc Delete( DbNFCPatrolLineDetail ) returns (crud.DMLResult);

//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
rpc SelectOne( crud.DMLParam ) returns ( DbNFCPatrolLineDetail );

//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
rpc SelectMany( crud.DMLParam ) returns ( stream DbNFCPatrolLineDetail );

//根据条件查询数据
rpc Query( crud.QueryParam ) returns ( stream DbNFCPatrolLineDetail );
//根据条件查询数据,批量返回数据,crud.QueryParam.Batch指定一次返回多少条数据
rpc QueryBatch( crud.QueryParam ) returns ( stream DbNFCPatrolLineDetailList );
}

//带权限检查(有相应的群组权限和编辑权限)的crud
service   PrpcDbNFCPatrolLineDetail {
//插入一行数据
rpc Insert( DbNFCPatrolLineDetail ) returns (crud.DMLResult);

//以RID为条件,全量修改一行数据
rpc Update( DbNFCPatrolLineDetail ) returns (crud.DMLResult);

//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
rpc PartialUpdate( DbNFCPatrolLineDetail ) returns (crud.DMLResult);

//以RID为条件,删除一行数据
rpc Delete( DbNFCPatrolLineDetail ) returns (crud.DMLResult);

//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
rpc SelectOne( crud.DMLParam ) returns ( DbNFCPatrolLineDetail );

//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
rpc SelectMany( crud.DMLParam ) returns ( stream DbNFCPatrolLineDetail );

//根据条件取得我有权限的数据
rpc Query( crud.PrivilegeParam ) returns ( stream DbNFCPatrolLineDetail );
//根据条件取得我有权限的数据,批量返回数据,crud.PrivilegeParam.QueryParam.Batch指定一次返回多少条数据
rpc QueryBatch( crud.PrivilegeParam ) returns ( stream DbNFCPatrolLineDetailList );
}

//普通crud，没有权限检查
service   RpcDbNFCPatrolLineRules {
//插入一行数据
rpc Insert( DbNFCPatrolLineRules ) returns (crud.DMLResult);

//以RID为条件,全量修改一行数据
rpc Update( DbNFCPatrolLineRules ) returns (crud.DMLResult);

//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
rpc PartialUpdate( DbNFCPatrolLineRules ) returns (crud.DMLResult);

//以RID为条件,删除一行数据
rpc Delete( DbNFCPatrolLineRules ) returns (crud.DMLResult);

//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
rpc SelectOne( crud.DMLParam ) returns ( DbNFCPatrolLineRules );

//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
rpc SelectMany( crud.DMLParam ) returns ( stream DbNFCPatrolLineRules );

//根据条件查询数据
rpc Query( crud.QueryParam ) returns ( stream DbNFCPatrolLineRules );
//根据条件查询数据,批量返回数据,crud.QueryParam.Batch指定一次返回多少条数据
rpc QueryBatch( crud.QueryParam ) returns ( stream DbNFCPatrolLineRulesList );
}

//带权限检查(有相应的群组权限和编辑权限)的crud
service   PrpcDbNFCPatrolLineRules {
//插入一行数据
rpc Insert( DbNFCPatrolLineRules ) returns (crud.DMLResult);

//以RID为条件,全量修改一行数据
rpc Update( DbNFCPatrolLineRules ) returns (crud.DMLResult);

//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
rpc PartialUpdate( DbNFCPatrolLineRules ) returns (crud.DMLResult);

//以RID为条件,删除一行数据
rpc Delete( DbNFCPatrolLineRules ) returns (crud.DMLResult);

//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
rpc SelectOne( crud.DMLParam ) returns ( DbNFCPatrolLineRules );

//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
rpc SelectMany( crud.DMLParam ) returns ( stream DbNFCPatrolLineRules );

//根据条件取得我有权限的数据
rpc Query( crud.PrivilegeParam ) returns ( stream DbNFCPatrolLineRules );
//根据条件取得我有权限的数据,批量返回数据,crud.PrivilegeParam.QueryParam.Batch指定一次返回多少条数据
rpc QueryBatch( crud.PrivilegeParam ) returns ( stream DbNFCPatrolLineRulesList );
}

//普通crud，没有权限检查
service   RpcDbNFCPatrolLineAndRules {
//插入一行数据
rpc Insert( DbNFCPatrolLineAndRules ) returns (crud.DMLResult);

//以RID为条件,全量修改一行数据
rpc Update( DbNFCPatrolLineAndRules ) returns (crud.DMLResult);

//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
rpc PartialUpdate( DbNFCPatrolLineAndRules ) returns (crud.DMLResult);

//以RID为条件,删除一行数据
rpc Delete( DbNFCPatrolLineAndRules ) returns (crud.DMLResult);

//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
rpc SelectOne( crud.DMLParam ) returns ( DbNFCPatrolLineAndRules );

//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
rpc SelectMany( crud.DMLParam ) returns ( stream DbNFCPatrolLineAndRules );

//根据条件查询数据
rpc Query( crud.QueryParam ) returns ( stream DbNFCPatrolLineAndRules );
//根据条件查询数据,批量返回数据,crud.QueryParam.Batch指定一次返回多少条数据
rpc QueryBatch( crud.QueryParam ) returns ( stream DbNFCPatrolLineAndRulesList );
}

//带权限检查(有相应的群组权限和编辑权限)的crud
service   PrpcDbNFCPatrolLineAndRules {
//插入一行数据
rpc Insert( DbNFCPatrolLineAndRules ) returns (crud.DMLResult);

//以RID为条件,全量修改一行数据
rpc Update( DbNFCPatrolLineAndRules ) returns (crud.DMLResult);

//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
rpc PartialUpdate( DbNFCPatrolLineAndRules ) returns (crud.DMLResult);

//以RID为条件,删除一行数据
rpc Delete( DbNFCPatrolLineAndRules ) returns (crud.DMLResult);

//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
rpc SelectOne( crud.DMLParam ) returns ( DbNFCPatrolLineAndRules );

//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
rpc SelectMany( crud.DMLParam ) returns ( stream DbNFCPatrolLineAndRules );

//根据条件取得我有权限的数据
rpc Query( crud.PrivilegeParam ) returns ( stream DbNFCPatrolLineAndRules );
//根据条件取得我有权限的数据,批量返回数据,crud.PrivilegeParam.QueryParam.Batch指定一次返回多少条数据
rpc QueryBatch( crud.PrivilegeParam ) returns ( stream DbNFCPatrolLineAndRulesList );
}

//普通crud，没有权限检查
service   RpcDbMarkerUploadImageHistory {
//插入一行数据
rpc Insert( DbMarkerUploadImageHistory ) returns (crud.DMLResult);

//根据条件查询数据
rpc Query( crud.QueryParam ) returns ( stream DbMarkerUploadImageHistory );
//根据条件查询数据,批量返回数据,crud.QueryParam.Batch指定一次返回多少条数据
rpc QueryBatch( crud.QueryParam ) returns ( stream DbMarkerUploadImageHistoryList );
}

//带权限检查(有相应的群组权限和编辑权限)的crud
service   PrpcDbMarkerUploadImageHistory {
//插入一行数据
rpc Insert( DbMarkerUploadImageHistory ) returns (crud.DMLResult);

//根据条件取得我有权限的数据
rpc Query( crud.PrivilegeParam ) returns ( stream DbMarkerUploadImageHistory );
//根据条件取得我有权限的数据,批量返回数据,crud.PrivilegeParam.QueryParam.Batch指定一次返回多少条数据
rpc QueryBatch( crud.PrivilegeParam ) returns ( stream DbMarkerUploadImageHistoryList );
}

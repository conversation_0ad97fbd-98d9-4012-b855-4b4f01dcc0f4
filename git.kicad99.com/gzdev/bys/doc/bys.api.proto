syntax = "proto3";
package doc;

option optimize_for = LITE_RUNTIME;

import "yrpcmsg.proto";
import "bysdb.proto";
import "userPermission.proto";
import "userPermission.list.proto";
import "controller.proto";

option go_package = "git.kicad99.com/gzdev/bys/doc";

message OnlineControllerInfo {
  //连接时间
  string ConnectTime = 1;

  //控制器ID
  sint32 ControllerID = 2;

  //登录网络方式
  sint32 NetworkType = 3;

  //最后数据时间
  string LastDataTime = 4;

  //最后电量
  float Power = 5;

  //通道手台信道号
  sint32 ChannelNo = 6;

  //本控制器数据中转的通道号和对应的信道号
  //[通道号1,通道1手台的信道号，通道号2,通道2手台的信道号，...]
  //如[1,1,2,1]表示通道1 在1信道，通道2也在1信道
  repeated sint32 TransferChannelNos = 7;

    //登录设备类型 0:fsk控制器 1:4g界桩
  int32 DeviceType = 8;
}

message MarkerQueueNoInfo {
  //控制器RID
  string ControllerRID = 1;
  //控制器通道号
  sint32 ControllerChannelNo = 2;
  // 0:查询可用的排队号
  //>0:此排队号是否可用
  sint32 QueueNoAvailable = 3;
  //结果
  //可用的排队号,或者=QueueNoAvailable，或者是新的可用排队号
  sint32 Result = 4;
}

//界桩信息
message MarkerInfo {
  //控制器ID
  sint32 ControllerID = 1;
  //控制器通道
  sint32 ControllerChannelNo = 2;
  //界桩ID
  sint32 MarkerID = 3;
  //界桩RID
  string MarkerRID = 4;
  //上来的指令，0xd1,0xd2,0xd3
  sint32 MarkerUploadCmd = 5;
  //界桩所在信道,未知设置为0
  sint32 MarkerChannel = 6;
  //上传的命令时间 yyyy-mm-dd HH:MM:SS(utc)
  string MarkerCmdTime = 7;
  //上传的命令系统密码是否正确
  bool MarkerSysPassOK = 8;
  //界桩的参数配置时间
  string MarkerParamTime = 9;
  //界桩状态，目前只用低位一个字节
  sint32 Status = 10;

  // 界桩上报
  bysproto.BInfoReporting InfoReporting = 11;
  bysproto.BAlarmReporting AlarmReporting = 12;
  //常规界桩：0, 4g界桩: 1
  sint32 MarkerType = 13;
  // 4g界桩报警状态，0:无报警，1:报警
  sint32 AlarmStatusWith4G = 14;
}

message ControllerCmd {
  //目标控制器ID
  sint32 ControllerID = 1;
  //在上级控制器的通道号，中继时需要填写，基站本地通道时为0
  sint32 ControllerChannelNo = 2;
  //新监听信道号
  sint32 NewChannelNo = 4;

  //命令
  //13,14
  sint32 Cmd = 5;
}

message UpdataMarkerParam {
  //系统设置上级控制器HWID,用于后台缓存找不到markerinfo时使用
  int32 controllerID = 1;
  //界桩更新后的参数
  bysdb.DbBysMarker markerData = 2;
}

// 更新控制器新服务器的地址。如果更改的地址错误，需要维护人员到现场修改控制器配置
message ControllerTarget {
  // 基站控制器ID
  sint32 StationID = 1;

  // 在上级控制器的通道号，中继时需要填写，基站本地通道时为0
  sint32 ControllerChannelNo = 2;
}

// 控制器回应对应 controller.proto ： BControllerNewServerAddr
// bmsg.cmd=15 bmsg.Res=1
// Nats事件主题：device.OrgRID.ControllerID.
message ControllerNewServerAddr {
  // 基站控制器ID
  repeated ControllerTarget Target = 1;

  // 新服务器IP地址
  string Ip = 2;

  // 新服务器端口
  sint32 Port = 3;
}

//查询界桩/控制器链路通道
message MarkerOrControllerId {
  //req: 界桩/控制器id  resp: 上级控制器id
  int32 id = 1;
  //resp: 上级控制器通道
  int32 controllerChannelNo = 2;
  //界桩=1,控制器=2
  bool isMarker = 3;
}

//bys 常规 api
service RpcBysApi {
  //获取有权限的在线的控制器列表信息
  rpc OnlineController(yrpcmsg.Yempty) returns (stream OnlineControllerInfo);
  //获取或查询控制器下界桩排队号
  rpc QueryMarkerQueueNo(MarkerQueueNoInfo) returns (MarkerQueueNoInfo);
  //界桩取消报警
  rpc CancelEmergency(MarkerInfo) returns (yrpcmsg.Yempty);
  //界桩下发参数
  rpc Send0xD0(MarkerInfo) returns (yrpcmsg.Yempty);
  //获取界桩最后数据，
  //参数MarkerInfo需要填写MarkerID,MarkerCmdTime
  //MarkerCmdTime作为查询数据库时最早的时间(缓存失效的情况)，第一个必须填写，以后的不需要
  rpc MarkerLatestInfo(stream MarkerInfo) returns (stream MarkerInfo);
  //修改基站/中继默认信道号
  rpc SendControllerCmd(ControllerCmd) returns (yrpcmsg.Yempty);
  //下发指令 更新界桩参数
  rpc UpdateMarkerParamter(UpdataMarkerParam) returns (yrpcmsg.Yempty);
  //实时ping控制器
  //ControllerCmd.ControllerID为基站ID, Ping中继时为中继控制器的上级控制器ID
  //ControllerCmd.ControllerChannelNo 通道，ping基站时为0,ping中继时为中继对应基站下的通道号
  //ControllerCmd.NewChannelNo,ping基站时为0,ping中继时为中继的ID
  rpc PingController(ControllerCmd) returns (yrpcmsg.Yempty);

  //请求角色拥有的权限
  rpc GetRolePermissions(user.DbRole) returns (user.DbRolePermissionList);

  // 更新指定控制器连接的服务器地址
  rpc UpdateControllerServerAddr(ControllerNewServerAddr) returns (yrpcmsg.Yempty);

  //查询界桩/控制器链路信息
  rpc QueryMarkerOrControllerLinkList(MarkerOrControllerId) returns (MarkerOrControllerId);

  // 4g界桩遥毙/遥晕/遥活
  // 应答的Bmsg中，Res=1 表示下发成功，Res=2 表示已缓存到数据库中，等待界桩唤醒再下发
  rpc MarkerRemoteKillOrActive(bysproto.BShutDown) returns (bysproto.Bmsg);

  // 4g界桩NFC巡查打卡上传
  rpc MarkerUploadNFCRecord(bysdb.DbMarkerPatrolHistory) returns (yrpcmsg.Yempty);

  // nfc巡查统计查询
  rpc NFCPatrolStatistics(PatrolStatisticsQuery) returns (stream PatrolStatisticsQueryResult);
}

// 巡查统计条件
message PatrolStatisticsQuery {
  // 开始时间(utc)， 格式：yyyy-mm-dd HH:MM:SS
  string StartTime = 1;  
  // 结束时间(utc)
  string EndTime = 2; 
  // 巡查路线RID
  string LineRid = 3;
}

message PatrolCheckItem {
  // 巡查的界桩的HWID
  int32 MarkerHWID = 1;
  // 巡查时间 格式：yyyy-mm-dd HH:MM:SS
  string CheckTime = 2;
  // 巡查用户
  string CheckUserRID = 3;
  // 巡查结果, 0:正常, 1: 未打卡
  int32 CheckResult = 4;
}

// 巡查统计结果
message PatrolStatisticsQueryResult {
  // 巡查路线RID
  string LineRid = 1;
  // 路线规则RID
  string RuleRID = 2;
  // 应查数目
  sint32 ShouldCheckCount = 3;
  // 巡查日期 yyyy-mm-dd
  string CheckDate = 4;
  // 巡查结果
  repeated PatrolCheckItem CheckResult = 5;
}

// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: bys.api.proto

package doc

import (
	context "context"
	encoding_binary "encoding/binary"
	fmt "fmt"
	bysdb "git.kicad99.com/gzdev/bys/doc/bysdb"
	bysproto "git.kicad99.com/gzdev/bys/doc/bysproto"
	user "git.kicad99.com/ykit/database/user"
	proto "github.com/gogo/protobuf/proto"
	yrpcmsg "github.com/yangjuncode/yrpcmsg"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	io "io"
	math "math"
	math_bits "math/bits"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

type OnlineControllerInfo struct {
	//连接时间
	ConnectTime string `protobuf:"bytes,1,opt,name=ConnectTime,proto3" json:"ConnectTime,omitempty"`
	//控制器ID
	ControllerID int32 `protobuf:"zigzag32,2,opt,name=ControllerID,proto3" json:"ControllerID,omitempty"`
	//登录网络方式
	NetworkType int32 `protobuf:"zigzag32,3,opt,name=NetworkType,proto3" json:"NetworkType,omitempty"`
	//最后数据时间
	LastDataTime string `protobuf:"bytes,4,opt,name=LastDataTime,proto3" json:"LastDataTime,omitempty"`
	//最后电量
	Power float32 `protobuf:"fixed32,5,opt,name=Power,proto3" json:"Power,omitempty"`
	//通道手台信道号
	ChannelNo int32 `protobuf:"zigzag32,6,opt,name=ChannelNo,proto3" json:"ChannelNo,omitempty"`
	//本控制器数据中转的通道号和对应的信道号
	//[通道号1,通道1手台的信道号，通道号2,通道2手台的信道号，...]
	//如[1,1,2,1]表示通道1 在1信道，通道2也在1信道
	TransferChannelNos []int32 `protobuf:"zigzag32,7,rep,packed,name=TransferChannelNos,proto3" json:"TransferChannelNos,omitempty"`
	//登录设备类型 0:fsk控制器 1:4g界桩
	DeviceType int32 `protobuf:"varint,8,opt,name=DeviceType,proto3" json:"DeviceType,omitempty"`
}

func (m *OnlineControllerInfo) Reset()         { *m = OnlineControllerInfo{} }
func (m *OnlineControllerInfo) String() string { return proto.CompactTextString(m) }
func (*OnlineControllerInfo) ProtoMessage()    {}
func (*OnlineControllerInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_acb84ab7f5ee3611, []int{0}
}
func (m *OnlineControllerInfo) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *OnlineControllerInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_OnlineControllerInfo.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *OnlineControllerInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OnlineControllerInfo.Merge(m, src)
}
func (m *OnlineControllerInfo) XXX_Size() int {
	return m.Size()
}
func (m *OnlineControllerInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_OnlineControllerInfo.DiscardUnknown(m)
}

var xxx_messageInfo_OnlineControllerInfo proto.InternalMessageInfo

func (m *OnlineControllerInfo) GetConnectTime() string {
	if m != nil {
		return m.ConnectTime
	}
	return ""
}

func (m *OnlineControllerInfo) GetControllerID() int32 {
	if m != nil {
		return m.ControllerID
	}
	return 0
}

func (m *OnlineControllerInfo) GetNetworkType() int32 {
	if m != nil {
		return m.NetworkType
	}
	return 0
}

func (m *OnlineControllerInfo) GetLastDataTime() string {
	if m != nil {
		return m.LastDataTime
	}
	return ""
}

func (m *OnlineControllerInfo) GetPower() float32 {
	if m != nil {
		return m.Power
	}
	return 0
}

func (m *OnlineControllerInfo) GetChannelNo() int32 {
	if m != nil {
		return m.ChannelNo
	}
	return 0
}

func (m *OnlineControllerInfo) GetTransferChannelNos() []int32 {
	if m != nil {
		return m.TransferChannelNos
	}
	return nil
}

func (m *OnlineControllerInfo) GetDeviceType() int32 {
	if m != nil {
		return m.DeviceType
	}
	return 0
}

type MarkerQueueNoInfo struct {
	//控制器RID
	ControllerRID string `protobuf:"bytes,1,opt,name=ControllerRID,proto3" json:"ControllerRID,omitempty"`
	//控制器通道号
	ControllerChannelNo int32 `protobuf:"zigzag32,2,opt,name=ControllerChannelNo,proto3" json:"ControllerChannelNo,omitempty"`
	// 0:查询可用的排队号
	//>0:此排队号是否可用
	QueueNoAvailable int32 `protobuf:"zigzag32,3,opt,name=QueueNoAvailable,proto3" json:"QueueNoAvailable,omitempty"`
	//结果
	//可用的排队号,或者=QueueNoAvailable，或者是新的可用排队号
	Result int32 `protobuf:"zigzag32,4,opt,name=Result,proto3" json:"Result,omitempty"`
}

func (m *MarkerQueueNoInfo) Reset()         { *m = MarkerQueueNoInfo{} }
func (m *MarkerQueueNoInfo) String() string { return proto.CompactTextString(m) }
func (*MarkerQueueNoInfo) ProtoMessage()    {}
func (*MarkerQueueNoInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_acb84ab7f5ee3611, []int{1}
}
func (m *MarkerQueueNoInfo) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *MarkerQueueNoInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_MarkerQueueNoInfo.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *MarkerQueueNoInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarkerQueueNoInfo.Merge(m, src)
}
func (m *MarkerQueueNoInfo) XXX_Size() int {
	return m.Size()
}
func (m *MarkerQueueNoInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MarkerQueueNoInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MarkerQueueNoInfo proto.InternalMessageInfo

func (m *MarkerQueueNoInfo) GetControllerRID() string {
	if m != nil {
		return m.ControllerRID
	}
	return ""
}

func (m *MarkerQueueNoInfo) GetControllerChannelNo() int32 {
	if m != nil {
		return m.ControllerChannelNo
	}
	return 0
}

func (m *MarkerQueueNoInfo) GetQueueNoAvailable() int32 {
	if m != nil {
		return m.QueueNoAvailable
	}
	return 0
}

func (m *MarkerQueueNoInfo) GetResult() int32 {
	if m != nil {
		return m.Result
	}
	return 0
}

// 界桩信息
type MarkerInfo struct {
	//控制器ID
	ControllerID int32 `protobuf:"zigzag32,1,opt,name=ControllerID,proto3" json:"ControllerID,omitempty"`
	//控制器通道
	ControllerChannelNo int32 `protobuf:"zigzag32,2,opt,name=ControllerChannelNo,proto3" json:"ControllerChannelNo,omitempty"`
	//界桩ID
	MarkerID int32 `protobuf:"zigzag32,3,opt,name=MarkerID,proto3" json:"MarkerID,omitempty"`
	//界桩RID
	MarkerRID string `protobuf:"bytes,4,opt,name=MarkerRID,proto3" json:"MarkerRID,omitempty"`
	//上来的指令，0xd1,0xd2,0xd3
	MarkerUploadCmd int32 `protobuf:"zigzag32,5,opt,name=MarkerUploadCmd,proto3" json:"MarkerUploadCmd,omitempty"`
	//界桩所在信道,未知设置为0
	MarkerChannel int32 `protobuf:"zigzag32,6,opt,name=MarkerChannel,proto3" json:"MarkerChannel,omitempty"`
	//上传的命令时间 yyyy-mm-dd HH:MM:SS(utc)
	MarkerCmdTime string `protobuf:"bytes,7,opt,name=MarkerCmdTime,proto3" json:"MarkerCmdTime,omitempty"`
	//上传的命令系统密码是否正确
	MarkerSysPassOK bool `protobuf:"varint,8,opt,name=MarkerSysPassOK,proto3" json:"MarkerSysPassOK,omitempty"`
	//界桩的参数配置时间
	MarkerParamTime string `protobuf:"bytes,9,opt,name=MarkerParamTime,proto3" json:"MarkerParamTime,omitempty"`
	//界桩状态，目前只用低位一个字节
	Status int32 `protobuf:"zigzag32,10,opt,name=Status,proto3" json:"Status,omitempty"`
	// 界桩上报
	InfoReporting  *bysproto.BInfoReporting  `protobuf:"bytes,11,opt,name=InfoReporting,proto3" json:"InfoReporting,omitempty"`
	AlarmReporting *bysproto.BAlarmReporting `protobuf:"bytes,12,opt,name=AlarmReporting,proto3" json:"AlarmReporting,omitempty"`
	//常规界桩：0, 4g界桩: 1
	MarkerType int32 `protobuf:"zigzag32,13,opt,name=MarkerType,proto3" json:"MarkerType,omitempty"`
	// 4g界桩报警状态，0:无报警，1:报警
	AlarmStatusWith4G int32 `protobuf:"zigzag32,14,opt,name=AlarmStatusWith4G,proto3" json:"AlarmStatusWith4G,omitempty"`
}

func (m *MarkerInfo) Reset()         { *m = MarkerInfo{} }
func (m *MarkerInfo) String() string { return proto.CompactTextString(m) }
func (*MarkerInfo) ProtoMessage()    {}
func (*MarkerInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_acb84ab7f5ee3611, []int{2}
}
func (m *MarkerInfo) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *MarkerInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_MarkerInfo.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *MarkerInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarkerInfo.Merge(m, src)
}
func (m *MarkerInfo) XXX_Size() int {
	return m.Size()
}
func (m *MarkerInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MarkerInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MarkerInfo proto.InternalMessageInfo

func (m *MarkerInfo) GetControllerID() int32 {
	if m != nil {
		return m.ControllerID
	}
	return 0
}

func (m *MarkerInfo) GetControllerChannelNo() int32 {
	if m != nil {
		return m.ControllerChannelNo
	}
	return 0
}

func (m *MarkerInfo) GetMarkerID() int32 {
	if m != nil {
		return m.MarkerID
	}
	return 0
}

func (m *MarkerInfo) GetMarkerRID() string {
	if m != nil {
		return m.MarkerRID
	}
	return ""
}

func (m *MarkerInfo) GetMarkerUploadCmd() int32 {
	if m != nil {
		return m.MarkerUploadCmd
	}
	return 0
}

func (m *MarkerInfo) GetMarkerChannel() int32 {
	if m != nil {
		return m.MarkerChannel
	}
	return 0
}

func (m *MarkerInfo) GetMarkerCmdTime() string {
	if m != nil {
		return m.MarkerCmdTime
	}
	return ""
}

func (m *MarkerInfo) GetMarkerSysPassOK() bool {
	if m != nil {
		return m.MarkerSysPassOK
	}
	return false
}

func (m *MarkerInfo) GetMarkerParamTime() string {
	if m != nil {
		return m.MarkerParamTime
	}
	return ""
}

func (m *MarkerInfo) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *MarkerInfo) GetInfoReporting() *bysproto.BInfoReporting {
	if m != nil {
		return m.InfoReporting
	}
	return nil
}

func (m *MarkerInfo) GetAlarmReporting() *bysproto.BAlarmReporting {
	if m != nil {
		return m.AlarmReporting
	}
	return nil
}

func (m *MarkerInfo) GetMarkerType() int32 {
	if m != nil {
		return m.MarkerType
	}
	return 0
}

func (m *MarkerInfo) GetAlarmStatusWith4G() int32 {
	if m != nil {
		return m.AlarmStatusWith4G
	}
	return 0
}

type ControllerCmd struct {
	//目标控制器ID
	ControllerID int32 `protobuf:"zigzag32,1,opt,name=ControllerID,proto3" json:"ControllerID,omitempty"`
	//在上级控制器的通道号，中继时需要填写，基站本地通道时为0
	ControllerChannelNo int32 `protobuf:"zigzag32,2,opt,name=ControllerChannelNo,proto3" json:"ControllerChannelNo,omitempty"`
	//新监听信道号
	NewChannelNo int32 `protobuf:"zigzag32,4,opt,name=NewChannelNo,proto3" json:"NewChannelNo,omitempty"`
	//命令
	//13,14
	Cmd int32 `protobuf:"zigzag32,5,opt,name=Cmd,proto3" json:"Cmd,omitempty"`
}

func (m *ControllerCmd) Reset()         { *m = ControllerCmd{} }
func (m *ControllerCmd) String() string { return proto.CompactTextString(m) }
func (*ControllerCmd) ProtoMessage()    {}
func (*ControllerCmd) Descriptor() ([]byte, []int) {
	return fileDescriptor_acb84ab7f5ee3611, []int{3}
}
func (m *ControllerCmd) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ControllerCmd) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ControllerCmd.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ControllerCmd) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ControllerCmd.Merge(m, src)
}
func (m *ControllerCmd) XXX_Size() int {
	return m.Size()
}
func (m *ControllerCmd) XXX_DiscardUnknown() {
	xxx_messageInfo_ControllerCmd.DiscardUnknown(m)
}

var xxx_messageInfo_ControllerCmd proto.InternalMessageInfo

func (m *ControllerCmd) GetControllerID() int32 {
	if m != nil {
		return m.ControllerID
	}
	return 0
}

func (m *ControllerCmd) GetControllerChannelNo() int32 {
	if m != nil {
		return m.ControllerChannelNo
	}
	return 0
}

func (m *ControllerCmd) GetNewChannelNo() int32 {
	if m != nil {
		return m.NewChannelNo
	}
	return 0
}

func (m *ControllerCmd) GetCmd() int32 {
	if m != nil {
		return m.Cmd
	}
	return 0
}

type UpdataMarkerParam struct {
	//系统设置上级控制器HWID,用于后台缓存找不到markerinfo时使用
	ControllerID int32 `protobuf:"varint,1,opt,name=controllerID,proto3" json:"controllerID,omitempty"`
	//界桩更新后的参数
	MarkerData *bysdb.DbBysMarker `protobuf:"bytes,2,opt,name=markerData,proto3" json:"markerData,omitempty"`
}

func (m *UpdataMarkerParam) Reset()         { *m = UpdataMarkerParam{} }
func (m *UpdataMarkerParam) String() string { return proto.CompactTextString(m) }
func (*UpdataMarkerParam) ProtoMessage()    {}
func (*UpdataMarkerParam) Descriptor() ([]byte, []int) {
	return fileDescriptor_acb84ab7f5ee3611, []int{4}
}
func (m *UpdataMarkerParam) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *UpdataMarkerParam) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_UpdataMarkerParam.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *UpdataMarkerParam) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdataMarkerParam.Merge(m, src)
}
func (m *UpdataMarkerParam) XXX_Size() int {
	return m.Size()
}
func (m *UpdataMarkerParam) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdataMarkerParam.DiscardUnknown(m)
}

var xxx_messageInfo_UpdataMarkerParam proto.InternalMessageInfo

func (m *UpdataMarkerParam) GetControllerID() int32 {
	if m != nil {
		return m.ControllerID
	}
	return 0
}

func (m *UpdataMarkerParam) GetMarkerData() *bysdb.DbBysMarker {
	if m != nil {
		return m.MarkerData
	}
	return nil
}

// 更新控制器新服务器的地址。如果更改的地址错误，需要维护人员到现场修改控制器配置
type ControllerTarget struct {
	// 基站控制器ID
	StationID int32 `protobuf:"zigzag32,1,opt,name=StationID,proto3" json:"StationID,omitempty"`
	// 在上级控制器的通道号，中继时需要填写，基站本地通道时为0
	ControllerChannelNo int32 `protobuf:"zigzag32,2,opt,name=ControllerChannelNo,proto3" json:"ControllerChannelNo,omitempty"`
}

func (m *ControllerTarget) Reset()         { *m = ControllerTarget{} }
func (m *ControllerTarget) String() string { return proto.CompactTextString(m) }
func (*ControllerTarget) ProtoMessage()    {}
func (*ControllerTarget) Descriptor() ([]byte, []int) {
	return fileDescriptor_acb84ab7f5ee3611, []int{5}
}
func (m *ControllerTarget) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ControllerTarget) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ControllerTarget.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ControllerTarget) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ControllerTarget.Merge(m, src)
}
func (m *ControllerTarget) XXX_Size() int {
	return m.Size()
}
func (m *ControllerTarget) XXX_DiscardUnknown() {
	xxx_messageInfo_ControllerTarget.DiscardUnknown(m)
}

var xxx_messageInfo_ControllerTarget proto.InternalMessageInfo

func (m *ControllerTarget) GetStationID() int32 {
	if m != nil {
		return m.StationID
	}
	return 0
}

func (m *ControllerTarget) GetControllerChannelNo() int32 {
	if m != nil {
		return m.ControllerChannelNo
	}
	return 0
}

// 控制器回应对应 controller.proto ： BControllerNewServerAddr
// bmsg.cmd=15 bmsg.Res=1
// Nats事件主题：device.OrgRID.ControllerID.
type ControllerNewServerAddr struct {
	// 基站控制器ID
	Target []*ControllerTarget `protobuf:"bytes,1,rep,name=Target,proto3" json:"Target,omitempty"`
	// 新服务器IP地址
	Ip string `protobuf:"bytes,2,opt,name=Ip,proto3" json:"Ip,omitempty"`
	// 新服务器端口
	Port int32 `protobuf:"zigzag32,3,opt,name=Port,proto3" json:"Port,omitempty"`
}

func (m *ControllerNewServerAddr) Reset()         { *m = ControllerNewServerAddr{} }
func (m *ControllerNewServerAddr) String() string { return proto.CompactTextString(m) }
func (*ControllerNewServerAddr) ProtoMessage()    {}
func (*ControllerNewServerAddr) Descriptor() ([]byte, []int) {
	return fileDescriptor_acb84ab7f5ee3611, []int{6}
}
func (m *ControllerNewServerAddr) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ControllerNewServerAddr) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ControllerNewServerAddr.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ControllerNewServerAddr) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ControllerNewServerAddr.Merge(m, src)
}
func (m *ControllerNewServerAddr) XXX_Size() int {
	return m.Size()
}
func (m *ControllerNewServerAddr) XXX_DiscardUnknown() {
	xxx_messageInfo_ControllerNewServerAddr.DiscardUnknown(m)
}

var xxx_messageInfo_ControllerNewServerAddr proto.InternalMessageInfo

func (m *ControllerNewServerAddr) GetTarget() []*ControllerTarget {
	if m != nil {
		return m.Target
	}
	return nil
}

func (m *ControllerNewServerAddr) GetIp() string {
	if m != nil {
		return m.Ip
	}
	return ""
}

func (m *ControllerNewServerAddr) GetPort() int32 {
	if m != nil {
		return m.Port
	}
	return 0
}

// 查询界桩/控制器链路通道
type MarkerOrControllerId struct {
	//req: 界桩/控制器id  resp: 上级控制器id
	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	//resp: 上级控制器通道
	ControllerChannelNo int32 `protobuf:"varint,2,opt,name=controllerChannelNo,proto3" json:"controllerChannelNo,omitempty"`
	//界桩=1,控制器=2
	IsMarker bool `protobuf:"varint,3,opt,name=isMarker,proto3" json:"isMarker,omitempty"`
}

func (m *MarkerOrControllerId) Reset()         { *m = MarkerOrControllerId{} }
func (m *MarkerOrControllerId) String() string { return proto.CompactTextString(m) }
func (*MarkerOrControllerId) ProtoMessage()    {}
func (*MarkerOrControllerId) Descriptor() ([]byte, []int) {
	return fileDescriptor_acb84ab7f5ee3611, []int{7}
}
func (m *MarkerOrControllerId) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *MarkerOrControllerId) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_MarkerOrControllerId.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *MarkerOrControllerId) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarkerOrControllerId.Merge(m, src)
}
func (m *MarkerOrControllerId) XXX_Size() int {
	return m.Size()
}
func (m *MarkerOrControllerId) XXX_DiscardUnknown() {
	xxx_messageInfo_MarkerOrControllerId.DiscardUnknown(m)
}

var xxx_messageInfo_MarkerOrControllerId proto.InternalMessageInfo

func (m *MarkerOrControllerId) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *MarkerOrControllerId) GetControllerChannelNo() int32 {
	if m != nil {
		return m.ControllerChannelNo
	}
	return 0
}

func (m *MarkerOrControllerId) GetIsMarker() bool {
	if m != nil {
		return m.IsMarker
	}
	return false
}

// 巡查统计条件
type PatrolStatisticsQuery struct {
	// 开始时间(utc)， 格式：yyyy-mm-dd HH:MM:SS
	StartTime string `protobuf:"bytes,1,opt,name=StartTime,proto3" json:"StartTime,omitempty"`
	// 结束时间(utc)
	EndTime string `protobuf:"bytes,2,opt,name=EndTime,proto3" json:"EndTime,omitempty"`
	// 巡查路线RID
	LineRid string `protobuf:"bytes,3,opt,name=LineRid,proto3" json:"LineRid,omitempty"`
}

func (m *PatrolStatisticsQuery) Reset()         { *m = PatrolStatisticsQuery{} }
func (m *PatrolStatisticsQuery) String() string { return proto.CompactTextString(m) }
func (*PatrolStatisticsQuery) ProtoMessage()    {}
func (*PatrolStatisticsQuery) Descriptor() ([]byte, []int) {
	return fileDescriptor_acb84ab7f5ee3611, []int{8}
}
func (m *PatrolStatisticsQuery) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *PatrolStatisticsQuery) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_PatrolStatisticsQuery.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *PatrolStatisticsQuery) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PatrolStatisticsQuery.Merge(m, src)
}
func (m *PatrolStatisticsQuery) XXX_Size() int {
	return m.Size()
}
func (m *PatrolStatisticsQuery) XXX_DiscardUnknown() {
	xxx_messageInfo_PatrolStatisticsQuery.DiscardUnknown(m)
}

var xxx_messageInfo_PatrolStatisticsQuery proto.InternalMessageInfo

func (m *PatrolStatisticsQuery) GetStartTime() string {
	if m != nil {
		return m.StartTime
	}
	return ""
}

func (m *PatrolStatisticsQuery) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

func (m *PatrolStatisticsQuery) GetLineRid() string {
	if m != nil {
		return m.LineRid
	}
	return ""
}

type PatrolCheckItem struct {
	// 巡查的界桩的HWID
	MarkerHWID int32 `protobuf:"varint,1,opt,name=MarkerHWID,proto3" json:"MarkerHWID,omitempty"`
	// 巡查时间 格式：yyyy-mm-dd HH:MM:SS
	CheckTime string `protobuf:"bytes,2,opt,name=CheckTime,proto3" json:"CheckTime,omitempty"`
	// 巡查用户
	CheckUserRID string `protobuf:"bytes,3,opt,name=CheckUserRID,proto3" json:"CheckUserRID,omitempty"`
	// 巡查结果, 0:正常, 1: 未打卡
	CheckResult int32 `protobuf:"varint,4,opt,name=CheckResult,proto3" json:"CheckResult,omitempty"`
}

func (m *PatrolCheckItem) Reset()         { *m = PatrolCheckItem{} }
func (m *PatrolCheckItem) String() string { return proto.CompactTextString(m) }
func (*PatrolCheckItem) ProtoMessage()    {}
func (*PatrolCheckItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_acb84ab7f5ee3611, []int{9}
}
func (m *PatrolCheckItem) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *PatrolCheckItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_PatrolCheckItem.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *PatrolCheckItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PatrolCheckItem.Merge(m, src)
}
func (m *PatrolCheckItem) XXX_Size() int {
	return m.Size()
}
func (m *PatrolCheckItem) XXX_DiscardUnknown() {
	xxx_messageInfo_PatrolCheckItem.DiscardUnknown(m)
}

var xxx_messageInfo_PatrolCheckItem proto.InternalMessageInfo

func (m *PatrolCheckItem) GetMarkerHWID() int32 {
	if m != nil {
		return m.MarkerHWID
	}
	return 0
}

func (m *PatrolCheckItem) GetCheckTime() string {
	if m != nil {
		return m.CheckTime
	}
	return ""
}

func (m *PatrolCheckItem) GetCheckUserRID() string {
	if m != nil {
		return m.CheckUserRID
	}
	return ""
}

func (m *PatrolCheckItem) GetCheckResult() int32 {
	if m != nil {
		return m.CheckResult
	}
	return 0
}

// 巡查统计结果
type PatrolStatisticsQueryResult struct {
	// 巡查路线RID
	LineRid string `protobuf:"bytes,1,opt,name=LineRid,proto3" json:"LineRid,omitempty"`
	// 路线规则RID
	RuleRID string `protobuf:"bytes,2,opt,name=RuleRID,proto3" json:"RuleRID,omitempty"`
	// 应查数目
	ShouldCheckCount int32 `protobuf:"zigzag32,3,opt,name=ShouldCheckCount,proto3" json:"ShouldCheckCount,omitempty"`
	// 巡查日期 yyyy-mm-dd
	CheckDate string `protobuf:"bytes,4,opt,name=CheckDate,proto3" json:"CheckDate,omitempty"`
	// 巡查结果
	CheckResult []*PatrolCheckItem `protobuf:"bytes,5,rep,name=CheckResult,proto3" json:"CheckResult,omitempty"`
}

func (m *PatrolStatisticsQueryResult) Reset()         { *m = PatrolStatisticsQueryResult{} }
func (m *PatrolStatisticsQueryResult) String() string { return proto.CompactTextString(m) }
func (*PatrolStatisticsQueryResult) ProtoMessage()    {}
func (*PatrolStatisticsQueryResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_acb84ab7f5ee3611, []int{10}
}
func (m *PatrolStatisticsQueryResult) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *PatrolStatisticsQueryResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_PatrolStatisticsQueryResult.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *PatrolStatisticsQueryResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PatrolStatisticsQueryResult.Merge(m, src)
}
func (m *PatrolStatisticsQueryResult) XXX_Size() int {
	return m.Size()
}
func (m *PatrolStatisticsQueryResult) XXX_DiscardUnknown() {
	xxx_messageInfo_PatrolStatisticsQueryResult.DiscardUnknown(m)
}

var xxx_messageInfo_PatrolStatisticsQueryResult proto.InternalMessageInfo

func (m *PatrolStatisticsQueryResult) GetLineRid() string {
	if m != nil {
		return m.LineRid
	}
	return ""
}

func (m *PatrolStatisticsQueryResult) GetRuleRID() string {
	if m != nil {
		return m.RuleRID
	}
	return ""
}

func (m *PatrolStatisticsQueryResult) GetShouldCheckCount() int32 {
	if m != nil {
		return m.ShouldCheckCount
	}
	return 0
}

func (m *PatrolStatisticsQueryResult) GetCheckDate() string {
	if m != nil {
		return m.CheckDate
	}
	return ""
}

func (m *PatrolStatisticsQueryResult) GetCheckResult() []*PatrolCheckItem {
	if m != nil {
		return m.CheckResult
	}
	return nil
}

func init() {
	proto.RegisterType((*OnlineControllerInfo)(nil), "doc.OnlineControllerInfo")
	proto.RegisterType((*MarkerQueueNoInfo)(nil), "doc.MarkerQueueNoInfo")
	proto.RegisterType((*MarkerInfo)(nil), "doc.MarkerInfo")
	proto.RegisterType((*ControllerCmd)(nil), "doc.ControllerCmd")
	proto.RegisterType((*UpdataMarkerParam)(nil), "doc.UpdataMarkerParam")
	proto.RegisterType((*ControllerTarget)(nil), "doc.ControllerTarget")
	proto.RegisterType((*ControllerNewServerAddr)(nil), "doc.ControllerNewServerAddr")
	proto.RegisterType((*MarkerOrControllerId)(nil), "doc.MarkerOrControllerId")
	proto.RegisterType((*PatrolStatisticsQuery)(nil), "doc.PatrolStatisticsQuery")
	proto.RegisterType((*PatrolCheckItem)(nil), "doc.PatrolCheckItem")
	proto.RegisterType((*PatrolStatisticsQueryResult)(nil), "doc.PatrolStatisticsQueryResult")
}

func init() { proto.RegisterFile("bys.api.proto", fileDescriptor_acb84ab7f5ee3611) }

var fileDescriptor_acb84ab7f5ee3611 = []byte{
	// 1257 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x56, 0x4b, 0x6f, 0xdb, 0xc6,
	0x13, 0x0f, 0xed, 0xf8, 0xa1, 0xf1, 0x53, 0x1b, 0x27, 0x7f, 0x59, 0xff, 0x54, 0x15, 0x88, 0x1e,
	0x84, 0x22, 0x65, 0x0c, 0xa7, 0x2d, 0x92, 0x4b, 0x52, 0x5b, 0xca, 0xc3, 0x88, 0x2b, 0x2b, 0x6b,
	0x1b, 0x41, 0x7b, 0xa3, 0xc8, 0x8d, 0xbc, 0x10, 0xc9, 0x15, 0x76, 0x57, 0x76, 0xd9, 0x4f, 0xd1,
	0x4b, 0x4f, 0xbd, 0x17, 0xbd, 0xf5, 0x6b, 0xf4, 0xd6, 0x00, 0xbd, 0xf4, 0x58, 0x24, 0x5f, 0xa4,
	0xd8, 0x5d, 0x4a, 0x5c, 0x52, 0x32, 0x90, 0x1c, 0x7a, 0xe3, 0xfc, 0x66, 0x76, 0x7f, 0xf3, 0xda,
	0x19, 0xc2, 0x46, 0x3f, 0x15, 0x9e, 0x3f, 0xa2, 0xde, 0x88, 0x33, 0xc9, 0xd0, 0x62, 0xc8, 0x82,
	0xfa, 0x46, 0xca, 0x47, 0x41, 0x2c, 0x06, 0x06, 0xab, 0xaf, 0xf5, 0x53, 0x11, 0xf6, 0x33, 0x61,
	0x67, 0x2c, 0x08, 0xef, 0x11, 0x1e, 0x53, 0x21, 0x28, 0x4b, 0x32, 0x74, 0xb7, 0x84, 0x46, 0x54,
	0xc8, 0x4c, 0xb5, 0x1d, 0xb0, 0x44, 0x72, 0x16, 0x45, 0x84, 0x1b, 0xc4, 0xfd, 0x75, 0x01, 0x76,
	0x4e, 0x92, 0x88, 0x26, 0xa4, 0x3d, 0x55, 0x1d, 0x25, 0x6f, 0x18, 0x6a, 0xc2, 0x5a, 0x9b, 0x25,
	0x09, 0x09, 0xe4, 0x19, 0x8d, 0x49, 0xcd, 0x69, 0x3a, 0xad, 0x0a, 0xb6, 0x21, 0xe4, 0xc2, 0xba,
	0x75, 0xa6, 0x53, 0x5b, 0x68, 0x3a, 0xad, 0x2a, 0x2e, 0x60, 0xea, 0x96, 0x2e, 0x91, 0x57, 0x8c,
	0x0f, 0xcf, 0xd2, 0x11, 0xa9, 0x2d, 0x6a, 0x13, 0x1b, 0x52, 0xb7, 0x1c, 0xfb, 0x42, 0x76, 0x7c,
	0xe9, 0x6b, 0xa2, 0x9b, 0x9a, 0xa8, 0x80, 0xa1, 0x1d, 0x58, 0xea, 0xb1, 0x2b, 0xc2, 0x6b, 0x4b,
	0x4d, 0xa7, 0xb5, 0x80, 0x8d, 0x80, 0xee, 0x42, 0xa5, 0x7d, 0xe1, 0x27, 0x09, 0x89, 0xba, 0xac,
	0xb6, 0xac, 0x6f, 0xce, 0x01, 0xe4, 0x01, 0x3a, 0xe3, 0x7e, 0x22, 0xde, 0x10, 0x3e, 0x05, 0x45,
	0x6d, 0xa5, 0xb9, 0xd8, 0xaa, 0xe2, 0x39, 0x1a, 0xd4, 0x00, 0xe8, 0x90, 0x4b, 0x1a, 0x10, 0xed,
	0xe8, 0x6a, 0xd3, 0x69, 0x2d, 0x61, 0x0b, 0x71, 0x7f, 0x77, 0xa0, 0xfa, 0xad, 0xcf, 0x87, 0x84,
	0xbf, 0x1a, 0x93, 0x31, 0xe9, 0x32, 0x9d, 0xa5, 0xcf, 0x60, 0x23, 0x8f, 0x17, 0x1f, 0x75, 0xb2,
	0x3c, 0x15, 0x41, 0xb4, 0x07, 0xb7, 0x72, 0x20, 0xf7, 0xd9, 0x24, 0x6c, 0x9e, 0x0a, 0x7d, 0x0e,
	0xdb, 0x19, 0xcd, 0xc1, 0xa5, 0x4f, 0x23, 0xbf, 0x1f, 0x4d, 0x92, 0x37, 0x83, 0xa3, 0x3b, 0xb0,
	0x8c, 0x89, 0x18, 0x47, 0x52, 0xe7, 0xae, 0x8a, 0x33, 0xc9, 0xfd, 0xf3, 0x26, 0x80, 0xf1, 0x58,
	0xbb, 0x5a, 0x2e, 0x97, 0x33, 0xa7, 0x5c, 0x1f, 0xef, 0x68, 0x1d, 0x56, 0x33, 0x8e, 0x4e, 0xe6,
	0xe0, 0x54, 0x56, 0x05, 0x32, 0xdf, 0x2a, 0x31, 0xa6, 0xae, 0x39, 0x80, 0x5a, 0xb0, 0x65, 0x84,
	0xf3, 0x51, 0xc4, 0xfc, 0xb0, 0x1d, 0x87, 0xba, 0xbc, 0x55, 0x5c, 0x86, 0x55, 0x92, 0x0d, 0x94,
	0xd1, 0x66, 0xc5, 0x2e, 0x82, 0x96, 0x55, 0x1c, 0xea, 0x4e, 0x5a, 0x31, 0xa5, 0x28, 0x80, 0x39,
	0xeb, 0x69, 0x2a, 0x7a, 0xbe, 0x10, 0x27, 0x2f, 0x75, 0xad, 0x57, 0x71, 0x19, 0xce, 0x2d, 0x7b,
	0x3e, 0xf7, 0x63, 0x7d, 0x63, 0x45, 0xdf, 0x58, 0x86, 0x55, 0x01, 0x4e, 0xa5, 0x2f, 0xc7, 0xa2,
	0x06, 0xa6, 0x00, 0x46, 0x42, 0x8f, 0x61, 0x43, 0x65, 0x1e, 0x93, 0x11, 0xe3, 0x92, 0x26, 0x83,
	0xda, 0x5a, 0xd3, 0x69, 0xad, 0xed, 0xd7, 0xbc, 0x7e, 0x2a, 0xf4, 0xeb, 0xf3, 0x0e, 0x0b, 0x7a,
	0x5c, 0x34, 0x47, 0x07, 0xb0, 0x79, 0x10, 0xf9, 0x3c, 0xce, 0x2f, 0x58, 0xd7, 0x17, 0xec, 0x5a,
	0x17, 0x14, 0x0d, 0x70, 0xe9, 0x80, 0xea, 0x6a, 0xe3, 0xad, 0xee, 0xea, 0x0d, 0xed, 0x9e, 0x85,
	0xa0, 0x7b, 0x50, 0xd5, 0x27, 0x8c, 0xc7, 0xaf, 0xa9, 0xbc, 0xf8, 0xf2, 0x79, 0x6d, 0x53, 0x9b,
	0xcd, 0x2a, 0xdc, 0x5f, 0x1c, 0xbb, 0xdd, 0x55, 0x69, 0xfe, 0x9b, 0xa6, 0x72, 0x61, 0xbd, 0x4b,
	0xae, 0x72, 0x53, 0xd3, 0xd7, 0x05, 0x0c, 0x6d, 0xc3, 0x62, 0xde, 0x32, 0xea, 0xd3, 0x1d, 0x42,
	0xf5, 0x7c, 0x14, 0xfa, 0xd2, 0xb7, 0xea, 0xa3, 0xae, 0x0a, 0xca, 0x0e, 0x2e, 0xe1, 0x02, 0x86,
	0xf6, 0x01, 0x62, 0x7d, 0x44, 0x0d, 0x1c, 0xed, 0xd7, 0xda, 0x3e, 0xf2, 0xcc, 0xa0, 0xed, 0xf4,
	0x0f, 0x53, 0x91, 0x35, 0xad, 0x65, 0xe5, 0xf6, 0x61, 0x3b, 0xf7, 0xfc, 0xcc, 0xe7, 0x03, 0x22,
	0x55, 0xbf, 0xab, 0x74, 0x51, 0x96, 0x4c, 0x33, 0x91, 0x03, 0x1f, 0x9f, 0x06, 0x37, 0x82, 0xff,
	0xe5, 0x70, 0x97, 0x5c, 0x9d, 0x12, 0x7e, 0x49, 0xf8, 0x41, 0x18, 0x72, 0xf4, 0x05, 0x2c, 0x1b,
	0xd2, 0x9a, 0xd3, 0x5c, 0x6c, 0xad, 0xed, 0xdf, 0xf6, 0x42, 0x16, 0x78, 0x65, 0x8f, 0x70, 0x66,
	0x84, 0x36, 0x61, 0xe1, 0x68, 0xa4, 0xa9, 0x2a, 0x78, 0xe1, 0x68, 0x84, 0x10, 0xdc, 0xec, 0x31,
	0x2e, 0xb3, 0x17, 0xab, 0xbf, 0x5d, 0x09, 0x3b, 0x26, 0xce, 0x13, 0x6e, 0x95, 0x2f, 0x54, 0x67,
	0x69, 0x98, 0xe5, 0x6d, 0x81, 0x86, 0x2a, 0x8e, 0xe0, 0x9a, 0x38, 0x96, 0xf0, 0x3c, 0x95, 0x9a,
	0x11, 0x34, 0xcb, 0xa1, 0x66, 0x5c, 0xc5, 0x53, 0xd9, 0xa5, 0x70, 0xbb, 0xe7, 0xab, 0x13, 0x3a,
	0x51, 0x42, 0xd2, 0x40, 0xbc, 0x1a, 0x13, 0x9e, 0x66, 0xc9, 0xe4, 0xf6, 0xf6, 0xc9, 0x01, 0x54,
	0x83, 0x95, 0xa7, 0x89, 0x79, 0xe6, 0x26, 0xaa, 0x89, 0xa8, 0x34, 0xc7, 0x34, 0x21, 0x98, 0x86,
	0x9a, 0xab, 0x82, 0x27, 0xa2, 0xfb, 0xb3, 0x03, 0x5b, 0x86, 0xab, 0x7d, 0x41, 0x82, 0xe1, 0x91,
	0x24, 0x71, 0xfe, 0x3e, 0x5e, 0xbc, 0x9e, 0x36, 0x87, 0x85, 0x98, 0x1d, 0x43, 0x82, 0xa1, 0xc5,
	0x94, 0x03, 0xba, 0xfb, 0x95, 0x70, 0x2e, 0xcc, 0x8c, 0x33, 0x84, 0x05, 0x4c, 0xef, 0x51, 0x25,
	0x5b, 0x23, 0x7a, 0x09, 0xdb, 0x90, 0xfb, 0x97, 0x03, 0xff, 0x9f, 0x9b, 0x03, 0xa3, 0xb7, 0x23,
	0x72, 0x0a, 0x11, 0x29, 0x0d, 0x1e, 0x47, 0x04, 0x67, 0xcb, 0xb7, 0x82, 0x27, 0xa2, 0xda, 0x1f,
	0xa7, 0x17, 0x6c, 0x1c, 0x85, 0x9a, 0xa8, 0xcd, 0xc6, 0xc9, 0xa4, 0xd8, 0x33, 0xf8, 0x34, 0xc6,
	0x8e, 0x2f, 0x27, 0xeb, 0x37, 0x07, 0xd0, 0xd7, 0x45, 0xff, 0x97, 0x74, 0xbb, 0xed, 0xe8, 0x76,
	0x2b, 0x25, 0xb3, 0x10, 0xd5, 0xfe, 0x6f, 0x2b, 0x50, 0xc1, 0xa3, 0xe0, 0x30, 0x15, 0x07, 0x23,
	0x8a, 0xbe, 0x81, 0xed, 0xf2, 0x5f, 0x06, 0xda, 0xf2, 0x26, 0xbf, 0x36, 0xdf, 0x91, 0x78, 0x24,
	0xd3, 0xfa, 0xae, 0xbe, 0x75, 0xde, 0xdf, 0xc8, 0x9e, 0x83, 0x3a, 0x80, 0x74, 0x52, 0x0a, 0x3b,
	0x18, 0xdd, 0xd1, 0x47, 0x66, 0xf6, 0x72, 0xfd, 0x1a, 0x1c, 0x3d, 0x80, 0xad, 0xb6, 0x9f, 0x04,
	0x24, 0x7a, 0x1a, 0x13, 0x3e, 0x20, 0x49, 0x90, 0xa2, 0x2d, 0xcb, 0x54, 0x9f, 0x2d, 0xfb, 0x85,
	0xee, 0xc1, 0xea, 0x29, 0x49, 0xc2, 0xbd, 0x1f, 0x3a, 0x7b, 0x1f, 0x60, 0xfd, 0x10, 0xb6, 0x8d,
	0xfa, 0xd8, 0x97, 0x44, 0x48, 0x4d, 0x3b, 0xe7, 0x54, 0x11, 0x68, 0x39, 0x7b, 0x0e, 0x7a, 0x08,
	0x55, 0xc5, 0x53, 0x9c, 0xb0, 0xa8, 0xf4, 0xb2, 0xdb, 0x71, 0x38, 0xcb, 0xf9, 0x04, 0x76, 0xf4,
	0xe8, 0x23, 0xd6, 0xe8, 0x93, 0x84, 0x67, 0xe9, 0x99, 0x99, 0x8a, 0xb3, 0x17, 0x7c, 0x05, 0x9b,
	0x3d, 0x9a, 0x0c, 0xac, 0xea, 0x7c, 0x10, 0xef, 0x63, 0x40, 0xcf, 0x89, 0xc4, 0x2c, 0x22, 0xf9,
	0xff, 0xa6, 0x40, 0xeb, 0x9e, 0xfa, 0x03, 0xf5, 0x3a, 0x7d, 0xa5, 0xa8, 0xd7, 0x6d, 0x29, 0x37,
	0x3b, 0xa6, 0x42, 0xa2, 0x97, 0x50, 0x37, 0x7e, 0xe7, 0x3c, 0xd6, 0x90, 0xbb, 0x5b, 0x72, 0xa1,
	0x30, 0x02, 0x67, 0x9d, 0x79, 0x0d, 0x9f, 0x5a, 0x1d, 0x62, 0x4f, 0xb1, 0x63, 0x9a, 0x0c, 0x35,
	0xdf, 0xae, 0x95, 0xf6, 0xe2, 0x98, 0xab, 0x5f, 0xaf, 0x42, 0x4f, 0xa0, 0x96, 0x6d, 0x00, 0x12,
	0x33, 0x49, 0x5e, 0xd2, 0x28, 0x3a, 0xe1, 0x07, 0x81, 0xa4, 0x97, 0x04, 0xdd, 0xb2, 0x76, 0xf1,
	0xe9, 0xc5, 0x58, 0x76, 0xd8, 0x55, 0x52, 0xdf, 0xb4, 0xc0, 0x58, 0x0c, 0xd0, 0x33, 0xb8, 0x6d,
	0xff, 0xd3, 0x74, 0x9f, 0xb5, 0x31, 0x09, 0x18, 0x0f, 0xd1, 0xdd, 0xe9, 0x96, 0x99, 0x54, 0x47,
	0xb1, 0xbe, 0xa0, 0x42, 0x32, 0x9e, 0xce, 0x46, 0x78, 0x0e, 0xb7, 0xba, 0xcf, 0xda, 0xe5, 0x59,
	0x81, 0xea, 0xd6, 0x6b, 0x2c, 0x8d, 0x90, 0x7a, 0xf3, 0x7a, 0x9d, 0x79, 0xa8, 0x7b, 0xce, 0xe1,
	0xa3, 0x3f, 0xde, 0x35, 0x9c, 0xb7, 0xef, 0x1a, 0xce, 0x3f, 0xef, 0x1a, 0xce, 0x4f, 0xef, 0x1b,
	0x37, 0xde, 0xbe, 0x6f, 0xdc, 0xf8, 0xfb, 0x7d, 0xe3, 0xc6, 0x8b, 0xc5, 0xef, 0x3f, 0x19, 0x50,
	0xe9, 0x0d, 0x69, 0xe0, 0x87, 0x8f, 0x1e, 0x79, 0x01, 0x8b, 0xef, 0x0f, 0x7e, 0x0c, 0xc9, 0xe5,
	0xfd, 0x7e, 0x2a, 0xee, 0x87, 0x2c, 0xe8, 0x2f, 0xeb, 0x28, 0x1f, 0xfc, 0x1b, 0x00, 0x00, 0xff,
	0xff, 0xd3, 0x2e, 0x3b, 0x0f, 0xba, 0x0c, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// RpcBysApiClient is the client API for RpcBysApi service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type RpcBysApiClient interface {
	//获取有权限的在线的控制器列表信息
	OnlineController(ctx context.Context, in *yrpcmsg.Yempty, opts ...grpc.CallOption) (RpcBysApi_OnlineControllerClient, error)
	//获取或查询控制器下界桩排队号
	QueryMarkerQueueNo(ctx context.Context, in *MarkerQueueNoInfo, opts ...grpc.CallOption) (*MarkerQueueNoInfo, error)
	//界桩取消报警
	CancelEmergency(ctx context.Context, in *MarkerInfo, opts ...grpc.CallOption) (*yrpcmsg.Yempty, error)
	//界桩下发参数
	Send0XD0(ctx context.Context, in *MarkerInfo, opts ...grpc.CallOption) (*yrpcmsg.Yempty, error)
	//获取界桩最后数据，
	//参数MarkerInfo需要填写MarkerID,MarkerCmdTime
	//MarkerCmdTime作为查询数据库时最早的时间(缓存失效的情况)，第一个必须填写，以后的不需要
	MarkerLatestInfo(ctx context.Context, opts ...grpc.CallOption) (RpcBysApi_MarkerLatestInfoClient, error)
	//修改基站/中继默认信道号
	SendControllerCmd(ctx context.Context, in *ControllerCmd, opts ...grpc.CallOption) (*yrpcmsg.Yempty, error)
	//下发指令 更新界桩参数
	UpdateMarkerParamter(ctx context.Context, in *UpdataMarkerParam, opts ...grpc.CallOption) (*yrpcmsg.Yempty, error)
	//实时ping控制器
	//ControllerCmd.ControllerID为基站ID, Ping中继时为中继控制器的上级控制器ID
	//ControllerCmd.ControllerChannelNo 通道，ping基站时为0,ping中继时为中继对应基站下的通道号
	//ControllerCmd.NewChannelNo,ping基站时为0,ping中继时为中继的ID
	PingController(ctx context.Context, in *ControllerCmd, opts ...grpc.CallOption) (*yrpcmsg.Yempty, error)
	//请求角色拥有的权限
	GetRolePermissions(ctx context.Context, in *user.DbRole, opts ...grpc.CallOption) (*user.DbRolePermissionList, error)
	// 更新指定控制器连接的服务器地址
	UpdateControllerServerAddr(ctx context.Context, in *ControllerNewServerAddr, opts ...grpc.CallOption) (*yrpcmsg.Yempty, error)
	//查询界桩/控制器链路信息
	QueryMarkerOrControllerLinkList(ctx context.Context, in *MarkerOrControllerId, opts ...grpc.CallOption) (*MarkerOrControllerId, error)
	// 4g界桩遥毙/遥晕/遥活
	// 应答的Bmsg中，Res=1 表示下发成功，Res=2 表示已缓存到数据库中，等待界桩唤醒再下发
	MarkerRemoteKillOrActive(ctx context.Context, in *bysproto.BShutDown, opts ...grpc.CallOption) (*bysproto.Bmsg, error)
	// 4g界桩NFC巡查打卡上传
	MarkerUploadNFCRecord(ctx context.Context, in *bysdb.DbMarkerPatrolHistory, opts ...grpc.CallOption) (*yrpcmsg.Yempty, error)
	// nfc巡查统计查询
	NFCPatrolStatistics(ctx context.Context, in *PatrolStatisticsQuery, opts ...grpc.CallOption) (RpcBysApi_NFCPatrolStatisticsClient, error)
}

type rpcBysApiClient struct {
	cc *grpc.ClientConn
}

func NewRpcBysApiClient(cc *grpc.ClientConn) RpcBysApiClient {
	return &rpcBysApiClient{cc}
}

func (c *rpcBysApiClient) OnlineController(ctx context.Context, in *yrpcmsg.Yempty, opts ...grpc.CallOption) (RpcBysApi_OnlineControllerClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RpcBysApi_serviceDesc.Streams[0], "/doc.RpcBysApi/OnlineController", opts...)
	if err != nil {
		return nil, err
	}
	x := &rpcBysApiOnlineControllerClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type RpcBysApi_OnlineControllerClient interface {
	Recv() (*OnlineControllerInfo, error)
	grpc.ClientStream
}

type rpcBysApiOnlineControllerClient struct {
	grpc.ClientStream
}

func (x *rpcBysApiOnlineControllerClient) Recv() (*OnlineControllerInfo, error) {
	m := new(OnlineControllerInfo)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *rpcBysApiClient) QueryMarkerQueueNo(ctx context.Context, in *MarkerQueueNoInfo, opts ...grpc.CallOption) (*MarkerQueueNoInfo, error) {
	out := new(MarkerQueueNoInfo)
	err := c.cc.Invoke(ctx, "/doc.RpcBysApi/QueryMarkerQueueNo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcBysApiClient) CancelEmergency(ctx context.Context, in *MarkerInfo, opts ...grpc.CallOption) (*yrpcmsg.Yempty, error) {
	out := new(yrpcmsg.Yempty)
	err := c.cc.Invoke(ctx, "/doc.RpcBysApi/CancelEmergency", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcBysApiClient) Send0XD0(ctx context.Context, in *MarkerInfo, opts ...grpc.CallOption) (*yrpcmsg.Yempty, error) {
	out := new(yrpcmsg.Yempty)
	err := c.cc.Invoke(ctx, "/doc.RpcBysApi/Send0xD0", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcBysApiClient) MarkerLatestInfo(ctx context.Context, opts ...grpc.CallOption) (RpcBysApi_MarkerLatestInfoClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RpcBysApi_serviceDesc.Streams[1], "/doc.RpcBysApi/MarkerLatestInfo", opts...)
	if err != nil {
		return nil, err
	}
	x := &rpcBysApiMarkerLatestInfoClient{stream}
	return x, nil
}

type RpcBysApi_MarkerLatestInfoClient interface {
	Send(*MarkerInfo) error
	Recv() (*MarkerInfo, error)
	grpc.ClientStream
}

type rpcBysApiMarkerLatestInfoClient struct {
	grpc.ClientStream
}

func (x *rpcBysApiMarkerLatestInfoClient) Send(m *MarkerInfo) error {
	return x.ClientStream.SendMsg(m)
}

func (x *rpcBysApiMarkerLatestInfoClient) Recv() (*MarkerInfo, error) {
	m := new(MarkerInfo)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *rpcBysApiClient) SendControllerCmd(ctx context.Context, in *ControllerCmd, opts ...grpc.CallOption) (*yrpcmsg.Yempty, error) {
	out := new(yrpcmsg.Yempty)
	err := c.cc.Invoke(ctx, "/doc.RpcBysApi/SendControllerCmd", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcBysApiClient) UpdateMarkerParamter(ctx context.Context, in *UpdataMarkerParam, opts ...grpc.CallOption) (*yrpcmsg.Yempty, error) {
	out := new(yrpcmsg.Yempty)
	err := c.cc.Invoke(ctx, "/doc.RpcBysApi/UpdateMarkerParamter", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcBysApiClient) PingController(ctx context.Context, in *ControllerCmd, opts ...grpc.CallOption) (*yrpcmsg.Yempty, error) {
	out := new(yrpcmsg.Yempty)
	err := c.cc.Invoke(ctx, "/doc.RpcBysApi/PingController", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcBysApiClient) GetRolePermissions(ctx context.Context, in *user.DbRole, opts ...grpc.CallOption) (*user.DbRolePermissionList, error) {
	out := new(user.DbRolePermissionList)
	err := c.cc.Invoke(ctx, "/doc.RpcBysApi/GetRolePermissions", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcBysApiClient) UpdateControllerServerAddr(ctx context.Context, in *ControllerNewServerAddr, opts ...grpc.CallOption) (*yrpcmsg.Yempty, error) {
	out := new(yrpcmsg.Yempty)
	err := c.cc.Invoke(ctx, "/doc.RpcBysApi/UpdateControllerServerAddr", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcBysApiClient) QueryMarkerOrControllerLinkList(ctx context.Context, in *MarkerOrControllerId, opts ...grpc.CallOption) (*MarkerOrControllerId, error) {
	out := new(MarkerOrControllerId)
	err := c.cc.Invoke(ctx, "/doc.RpcBysApi/QueryMarkerOrControllerLinkList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcBysApiClient) MarkerRemoteKillOrActive(ctx context.Context, in *bysproto.BShutDown, opts ...grpc.CallOption) (*bysproto.Bmsg, error) {
	out := new(bysproto.Bmsg)
	err := c.cc.Invoke(ctx, "/doc.RpcBysApi/MarkerRemoteKillOrActive", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcBysApiClient) MarkerUploadNFCRecord(ctx context.Context, in *bysdb.DbMarkerPatrolHistory, opts ...grpc.CallOption) (*yrpcmsg.Yempty, error) {
	out := new(yrpcmsg.Yempty)
	err := c.cc.Invoke(ctx, "/doc.RpcBysApi/MarkerUploadNFCRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcBysApiClient) NFCPatrolStatistics(ctx context.Context, in *PatrolStatisticsQuery, opts ...grpc.CallOption) (RpcBysApi_NFCPatrolStatisticsClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RpcBysApi_serviceDesc.Streams[2], "/doc.RpcBysApi/NFCPatrolStatistics", opts...)
	if err != nil {
		return nil, err
	}
	x := &rpcBysApiNFCPatrolStatisticsClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type RpcBysApi_NFCPatrolStatisticsClient interface {
	Recv() (*PatrolStatisticsQueryResult, error)
	grpc.ClientStream
}

type rpcBysApiNFCPatrolStatisticsClient struct {
	grpc.ClientStream
}

func (x *rpcBysApiNFCPatrolStatisticsClient) Recv() (*PatrolStatisticsQueryResult, error) {
	m := new(PatrolStatisticsQueryResult)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// RpcBysApiServer is the server API for RpcBysApi service.
type RpcBysApiServer interface {
	//获取有权限的在线的控制器列表信息
	OnlineController(*yrpcmsg.Yempty, RpcBysApi_OnlineControllerServer) error
	//获取或查询控制器下界桩排队号
	QueryMarkerQueueNo(context.Context, *MarkerQueueNoInfo) (*MarkerQueueNoInfo, error)
	//界桩取消报警
	CancelEmergency(context.Context, *MarkerInfo) (*yrpcmsg.Yempty, error)
	//界桩下发参数
	Send0XD0(context.Context, *MarkerInfo) (*yrpcmsg.Yempty, error)
	//获取界桩最后数据，
	//参数MarkerInfo需要填写MarkerID,MarkerCmdTime
	//MarkerCmdTime作为查询数据库时最早的时间(缓存失效的情况)，第一个必须填写，以后的不需要
	MarkerLatestInfo(RpcBysApi_MarkerLatestInfoServer) error
	//修改基站/中继默认信道号
	SendControllerCmd(context.Context, *ControllerCmd) (*yrpcmsg.Yempty, error)
	//下发指令 更新界桩参数
	UpdateMarkerParamter(context.Context, *UpdataMarkerParam) (*yrpcmsg.Yempty, error)
	//实时ping控制器
	//ControllerCmd.ControllerID为基站ID, Ping中继时为中继控制器的上级控制器ID
	//ControllerCmd.ControllerChannelNo 通道，ping基站时为0,ping中继时为中继对应基站下的通道号
	//ControllerCmd.NewChannelNo,ping基站时为0,ping中继时为中继的ID
	PingController(context.Context, *ControllerCmd) (*yrpcmsg.Yempty, error)
	//请求角色拥有的权限
	GetRolePermissions(context.Context, *user.DbRole) (*user.DbRolePermissionList, error)
	// 更新指定控制器连接的服务器地址
	UpdateControllerServerAddr(context.Context, *ControllerNewServerAddr) (*yrpcmsg.Yempty, error)
	//查询界桩/控制器链路信息
	QueryMarkerOrControllerLinkList(context.Context, *MarkerOrControllerId) (*MarkerOrControllerId, error)
	// 4g界桩遥毙/遥晕/遥活
	// 应答的Bmsg中，Res=1 表示下发成功，Res=2 表示已缓存到数据库中，等待界桩唤醒再下发
	MarkerRemoteKillOrActive(context.Context, *bysproto.BShutDown) (*bysproto.Bmsg, error)
	// 4g界桩NFC巡查打卡上传
	MarkerUploadNFCRecord(context.Context, *bysdb.DbMarkerPatrolHistory) (*yrpcmsg.Yempty, error)
	// nfc巡查统计查询
	NFCPatrolStatistics(*PatrolStatisticsQuery, RpcBysApi_NFCPatrolStatisticsServer) error
}

// UnimplementedRpcBysApiServer can be embedded to have forward compatible implementations.
type UnimplementedRpcBysApiServer struct {
}

func (*UnimplementedRpcBysApiServer) OnlineController(req *yrpcmsg.Yempty, srv RpcBysApi_OnlineControllerServer) error {
	return status.Errorf(codes.Unimplemented, "method OnlineController not implemented")
}
func (*UnimplementedRpcBysApiServer) QueryMarkerQueueNo(ctx context.Context, req *MarkerQueueNoInfo) (*MarkerQueueNoInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryMarkerQueueNo not implemented")
}
func (*UnimplementedRpcBysApiServer) CancelEmergency(ctx context.Context, req *MarkerInfo) (*yrpcmsg.Yempty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CancelEmergency not implemented")
}
func (*UnimplementedRpcBysApiServer) Send0XD0(ctx context.Context, req *MarkerInfo) (*yrpcmsg.Yempty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Send0XD0 not implemented")
}
func (*UnimplementedRpcBysApiServer) MarkerLatestInfo(srv RpcBysApi_MarkerLatestInfoServer) error {
	return status.Errorf(codes.Unimplemented, "method MarkerLatestInfo not implemented")
}
func (*UnimplementedRpcBysApiServer) SendControllerCmd(ctx context.Context, req *ControllerCmd) (*yrpcmsg.Yempty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendControllerCmd not implemented")
}
func (*UnimplementedRpcBysApiServer) UpdateMarkerParamter(ctx context.Context, req *UpdataMarkerParam) (*yrpcmsg.Yempty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateMarkerParamter not implemented")
}
func (*UnimplementedRpcBysApiServer) PingController(ctx context.Context, req *ControllerCmd) (*yrpcmsg.Yempty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PingController not implemented")
}
func (*UnimplementedRpcBysApiServer) GetRolePermissions(ctx context.Context, req *user.DbRole) (*user.DbRolePermissionList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRolePermissions not implemented")
}
func (*UnimplementedRpcBysApiServer) UpdateControllerServerAddr(ctx context.Context, req *ControllerNewServerAddr) (*yrpcmsg.Yempty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateControllerServerAddr not implemented")
}
func (*UnimplementedRpcBysApiServer) QueryMarkerOrControllerLinkList(ctx context.Context, req *MarkerOrControllerId) (*MarkerOrControllerId, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryMarkerOrControllerLinkList not implemented")
}
func (*UnimplementedRpcBysApiServer) MarkerRemoteKillOrActive(ctx context.Context, req *bysproto.BShutDown) (*bysproto.Bmsg, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MarkerRemoteKillOrActive not implemented")
}
func (*UnimplementedRpcBysApiServer) MarkerUploadNFCRecord(ctx context.Context, req *bysdb.DbMarkerPatrolHistory) (*yrpcmsg.Yempty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MarkerUploadNFCRecord not implemented")
}
func (*UnimplementedRpcBysApiServer) NFCPatrolStatistics(req *PatrolStatisticsQuery, srv RpcBysApi_NFCPatrolStatisticsServer) error {
	return status.Errorf(codes.Unimplemented, "method NFCPatrolStatistics not implemented")
}

func RegisterRpcBysApiServer(s *grpc.Server, srv RpcBysApiServer) {
	s.RegisterService(&_RpcBysApi_serviceDesc, srv)
}

func _RpcBysApi_OnlineController_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(yrpcmsg.Yempty)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RpcBysApiServer).OnlineController(m, &rpcBysApiOnlineControllerServer{stream})
}

type RpcBysApi_OnlineControllerServer interface {
	Send(*OnlineControllerInfo) error
	grpc.ServerStream
}

type rpcBysApiOnlineControllerServer struct {
	grpc.ServerStream
}

func (x *rpcBysApiOnlineControllerServer) Send(m *OnlineControllerInfo) error {
	return x.ServerStream.SendMsg(m)
}

func _RpcBysApi_QueryMarkerQueueNo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MarkerQueueNoInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcBysApiServer).QueryMarkerQueueNo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/doc.RpcBysApi/QueryMarkerQueueNo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcBysApiServer).QueryMarkerQueueNo(ctx, req.(*MarkerQueueNoInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcBysApi_CancelEmergency_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MarkerInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcBysApiServer).CancelEmergency(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/doc.RpcBysApi/CancelEmergency",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcBysApiServer).CancelEmergency(ctx, req.(*MarkerInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcBysApi_Send0XD0_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MarkerInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcBysApiServer).Send0XD0(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/doc.RpcBysApi/Send0XD0",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcBysApiServer).Send0XD0(ctx, req.(*MarkerInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcBysApi_MarkerLatestInfo_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(RpcBysApiServer).MarkerLatestInfo(&rpcBysApiMarkerLatestInfoServer{stream})
}

type RpcBysApi_MarkerLatestInfoServer interface {
	Send(*MarkerInfo) error
	Recv() (*MarkerInfo, error)
	grpc.ServerStream
}

type rpcBysApiMarkerLatestInfoServer struct {
	grpc.ServerStream
}

func (x *rpcBysApiMarkerLatestInfoServer) Send(m *MarkerInfo) error {
	return x.ServerStream.SendMsg(m)
}

func (x *rpcBysApiMarkerLatestInfoServer) Recv() (*MarkerInfo, error) {
	m := new(MarkerInfo)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func _RpcBysApi_SendControllerCmd_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ControllerCmd)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcBysApiServer).SendControllerCmd(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/doc.RpcBysApi/SendControllerCmd",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcBysApiServer).SendControllerCmd(ctx, req.(*ControllerCmd))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcBysApi_UpdateMarkerParamter_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdataMarkerParam)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcBysApiServer).UpdateMarkerParamter(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/doc.RpcBysApi/UpdateMarkerParamter",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcBysApiServer).UpdateMarkerParamter(ctx, req.(*UpdataMarkerParam))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcBysApi_PingController_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ControllerCmd)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcBysApiServer).PingController(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/doc.RpcBysApi/PingController",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcBysApiServer).PingController(ctx, req.(*ControllerCmd))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcBysApi_GetRolePermissions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(user.DbRole)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcBysApiServer).GetRolePermissions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/doc.RpcBysApi/GetRolePermissions",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcBysApiServer).GetRolePermissions(ctx, req.(*user.DbRole))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcBysApi_UpdateControllerServerAddr_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ControllerNewServerAddr)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcBysApiServer).UpdateControllerServerAddr(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/doc.RpcBysApi/UpdateControllerServerAddr",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcBysApiServer).UpdateControllerServerAddr(ctx, req.(*ControllerNewServerAddr))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcBysApi_QueryMarkerOrControllerLinkList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MarkerOrControllerId)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcBysApiServer).QueryMarkerOrControllerLinkList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/doc.RpcBysApi/QueryMarkerOrControllerLinkList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcBysApiServer).QueryMarkerOrControllerLinkList(ctx, req.(*MarkerOrControllerId))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcBysApi_MarkerRemoteKillOrActive_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(bysproto.BShutDown)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcBysApiServer).MarkerRemoteKillOrActive(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/doc.RpcBysApi/MarkerRemoteKillOrActive",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcBysApiServer).MarkerRemoteKillOrActive(ctx, req.(*bysproto.BShutDown))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcBysApi_MarkerUploadNFCRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(bysdb.DbMarkerPatrolHistory)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcBysApiServer).MarkerUploadNFCRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/doc.RpcBysApi/MarkerUploadNFCRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcBysApiServer).MarkerUploadNFCRecord(ctx, req.(*bysdb.DbMarkerPatrolHistory))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcBysApi_NFCPatrolStatistics_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(PatrolStatisticsQuery)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RpcBysApiServer).NFCPatrolStatistics(m, &rpcBysApiNFCPatrolStatisticsServer{stream})
}

type RpcBysApi_NFCPatrolStatisticsServer interface {
	Send(*PatrolStatisticsQueryResult) error
	grpc.ServerStream
}

type rpcBysApiNFCPatrolStatisticsServer struct {
	grpc.ServerStream
}

func (x *rpcBysApiNFCPatrolStatisticsServer) Send(m *PatrolStatisticsQueryResult) error {
	return x.ServerStream.SendMsg(m)
}

var _RpcBysApi_serviceDesc = grpc.ServiceDesc{
	ServiceName: "doc.RpcBysApi",
	HandlerType: (*RpcBysApiServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "QueryMarkerQueueNo",
			Handler:    _RpcBysApi_QueryMarkerQueueNo_Handler,
		},
		{
			MethodName: "CancelEmergency",
			Handler:    _RpcBysApi_CancelEmergency_Handler,
		},
		{
			MethodName: "Send0xD0",
			Handler:    _RpcBysApi_Send0XD0_Handler,
		},
		{
			MethodName: "SendControllerCmd",
			Handler:    _RpcBysApi_SendControllerCmd_Handler,
		},
		{
			MethodName: "UpdateMarkerParamter",
			Handler:    _RpcBysApi_UpdateMarkerParamter_Handler,
		},
		{
			MethodName: "PingController",
			Handler:    _RpcBysApi_PingController_Handler,
		},
		{
			MethodName: "GetRolePermissions",
			Handler:    _RpcBysApi_GetRolePermissions_Handler,
		},
		{
			MethodName: "UpdateControllerServerAddr",
			Handler:    _RpcBysApi_UpdateControllerServerAddr_Handler,
		},
		{
			MethodName: "QueryMarkerOrControllerLinkList",
			Handler:    _RpcBysApi_QueryMarkerOrControllerLinkList_Handler,
		},
		{
			MethodName: "MarkerRemoteKillOrActive",
			Handler:    _RpcBysApi_MarkerRemoteKillOrActive_Handler,
		},
		{
			MethodName: "MarkerUploadNFCRecord",
			Handler:    _RpcBysApi_MarkerUploadNFCRecord_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "OnlineController",
			Handler:       _RpcBysApi_OnlineController_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "MarkerLatestInfo",
			Handler:       _RpcBysApi_MarkerLatestInfo_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
		{
			StreamName:    "NFCPatrolStatistics",
			Handler:       _RpcBysApi_NFCPatrolStatistics_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "bys.api.proto",
}

func (m *OnlineControllerInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *OnlineControllerInfo) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *OnlineControllerInfo) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.DeviceType != 0 {
		i = encodeVarintBysApi(dAtA, i, uint64(m.DeviceType))
		i--
		dAtA[i] = 0x40
	}
	if len(m.TransferChannelNos) > 0 {
		dAtA1 := make([]byte, len(m.TransferChannelNos)*5)
		var j2 int
		for _, num := range m.TransferChannelNos {
			x3 := (uint32(num) << 1) ^ uint32((num >> 31))
			for x3 >= 1<<7 {
				dAtA1[j2] = uint8(uint64(x3)&0x7f | 0x80)
				j2++
				x3 >>= 7
			}
			dAtA1[j2] = uint8(x3)
			j2++
		}
		i -= j2
		copy(dAtA[i:], dAtA1[:j2])
		i = encodeVarintBysApi(dAtA, i, uint64(j2))
		i--
		dAtA[i] = 0x3a
	}
	if m.ChannelNo != 0 {
		i = encodeVarintBysApi(dAtA, i, uint64((uint32(m.ChannelNo)<<1)^uint32((m.ChannelNo>>31))))
		i--
		dAtA[i] = 0x30
	}
	if m.Power != 0 {
		i -= 4
		encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(math.Float32bits(float32(m.Power))))
		i--
		dAtA[i] = 0x2d
	}
	if len(m.LastDataTime) > 0 {
		i -= len(m.LastDataTime)
		copy(dAtA[i:], m.LastDataTime)
		i = encodeVarintBysApi(dAtA, i, uint64(len(m.LastDataTime)))
		i--
		dAtA[i] = 0x22
	}
	if m.NetworkType != 0 {
		i = encodeVarintBysApi(dAtA, i, uint64((uint32(m.NetworkType)<<1)^uint32((m.NetworkType>>31))))
		i--
		dAtA[i] = 0x18
	}
	if m.ControllerID != 0 {
		i = encodeVarintBysApi(dAtA, i, uint64((uint32(m.ControllerID)<<1)^uint32((m.ControllerID>>31))))
		i--
		dAtA[i] = 0x10
	}
	if len(m.ConnectTime) > 0 {
		i -= len(m.ConnectTime)
		copy(dAtA[i:], m.ConnectTime)
		i = encodeVarintBysApi(dAtA, i, uint64(len(m.ConnectTime)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *MarkerQueueNoInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MarkerQueueNoInfo) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *MarkerQueueNoInfo) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Result != 0 {
		i = encodeVarintBysApi(dAtA, i, uint64((uint32(m.Result)<<1)^uint32((m.Result>>31))))
		i--
		dAtA[i] = 0x20
	}
	if m.QueueNoAvailable != 0 {
		i = encodeVarintBysApi(dAtA, i, uint64((uint32(m.QueueNoAvailable)<<1)^uint32((m.QueueNoAvailable>>31))))
		i--
		dAtA[i] = 0x18
	}
	if m.ControllerChannelNo != 0 {
		i = encodeVarintBysApi(dAtA, i, uint64((uint32(m.ControllerChannelNo)<<1)^uint32((m.ControllerChannelNo>>31))))
		i--
		dAtA[i] = 0x10
	}
	if len(m.ControllerRID) > 0 {
		i -= len(m.ControllerRID)
		copy(dAtA[i:], m.ControllerRID)
		i = encodeVarintBysApi(dAtA, i, uint64(len(m.ControllerRID)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *MarkerInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MarkerInfo) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *MarkerInfo) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.AlarmStatusWith4G != 0 {
		i = encodeVarintBysApi(dAtA, i, uint64((uint32(m.AlarmStatusWith4G)<<1)^uint32((m.AlarmStatusWith4G>>31))))
		i--
		dAtA[i] = 0x70
	}
	if m.MarkerType != 0 {
		i = encodeVarintBysApi(dAtA, i, uint64((uint32(m.MarkerType)<<1)^uint32((m.MarkerType>>31))))
		i--
		dAtA[i] = 0x68
	}
	if m.AlarmReporting != nil {
		{
			size, err := m.AlarmReporting.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintBysApi(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x62
	}
	if m.InfoReporting != nil {
		{
			size, err := m.InfoReporting.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintBysApi(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x5a
	}
	if m.Status != 0 {
		i = encodeVarintBysApi(dAtA, i, uint64((uint32(m.Status)<<1)^uint32((m.Status>>31))))
		i--
		dAtA[i] = 0x50
	}
	if len(m.MarkerParamTime) > 0 {
		i -= len(m.MarkerParamTime)
		copy(dAtA[i:], m.MarkerParamTime)
		i = encodeVarintBysApi(dAtA, i, uint64(len(m.MarkerParamTime)))
		i--
		dAtA[i] = 0x4a
	}
	if m.MarkerSysPassOK {
		i--
		if m.MarkerSysPassOK {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x40
	}
	if len(m.MarkerCmdTime) > 0 {
		i -= len(m.MarkerCmdTime)
		copy(dAtA[i:], m.MarkerCmdTime)
		i = encodeVarintBysApi(dAtA, i, uint64(len(m.MarkerCmdTime)))
		i--
		dAtA[i] = 0x3a
	}
	if m.MarkerChannel != 0 {
		i = encodeVarintBysApi(dAtA, i, uint64((uint32(m.MarkerChannel)<<1)^uint32((m.MarkerChannel>>31))))
		i--
		dAtA[i] = 0x30
	}
	if m.MarkerUploadCmd != 0 {
		i = encodeVarintBysApi(dAtA, i, uint64((uint32(m.MarkerUploadCmd)<<1)^uint32((m.MarkerUploadCmd>>31))))
		i--
		dAtA[i] = 0x28
	}
	if len(m.MarkerRID) > 0 {
		i -= len(m.MarkerRID)
		copy(dAtA[i:], m.MarkerRID)
		i = encodeVarintBysApi(dAtA, i, uint64(len(m.MarkerRID)))
		i--
		dAtA[i] = 0x22
	}
	if m.MarkerID != 0 {
		i = encodeVarintBysApi(dAtA, i, uint64((uint32(m.MarkerID)<<1)^uint32((m.MarkerID>>31))))
		i--
		dAtA[i] = 0x18
	}
	if m.ControllerChannelNo != 0 {
		i = encodeVarintBysApi(dAtA, i, uint64((uint32(m.ControllerChannelNo)<<1)^uint32((m.ControllerChannelNo>>31))))
		i--
		dAtA[i] = 0x10
	}
	if m.ControllerID != 0 {
		i = encodeVarintBysApi(dAtA, i, uint64((uint32(m.ControllerID)<<1)^uint32((m.ControllerID>>31))))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *ControllerCmd) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ControllerCmd) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ControllerCmd) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Cmd != 0 {
		i = encodeVarintBysApi(dAtA, i, uint64((uint32(m.Cmd)<<1)^uint32((m.Cmd>>31))))
		i--
		dAtA[i] = 0x28
	}
	if m.NewChannelNo != 0 {
		i = encodeVarintBysApi(dAtA, i, uint64((uint32(m.NewChannelNo)<<1)^uint32((m.NewChannelNo>>31))))
		i--
		dAtA[i] = 0x20
	}
	if m.ControllerChannelNo != 0 {
		i = encodeVarintBysApi(dAtA, i, uint64((uint32(m.ControllerChannelNo)<<1)^uint32((m.ControllerChannelNo>>31))))
		i--
		dAtA[i] = 0x10
	}
	if m.ControllerID != 0 {
		i = encodeVarintBysApi(dAtA, i, uint64((uint32(m.ControllerID)<<1)^uint32((m.ControllerID>>31))))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *UpdataMarkerParam) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdataMarkerParam) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *UpdataMarkerParam) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.MarkerData != nil {
		{
			size, err := m.MarkerData.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintBysApi(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if m.ControllerID != 0 {
		i = encodeVarintBysApi(dAtA, i, uint64(m.ControllerID))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *ControllerTarget) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ControllerTarget) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ControllerTarget) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.ControllerChannelNo != 0 {
		i = encodeVarintBysApi(dAtA, i, uint64((uint32(m.ControllerChannelNo)<<1)^uint32((m.ControllerChannelNo>>31))))
		i--
		dAtA[i] = 0x10
	}
	if m.StationID != 0 {
		i = encodeVarintBysApi(dAtA, i, uint64((uint32(m.StationID)<<1)^uint32((m.StationID>>31))))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *ControllerNewServerAddr) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ControllerNewServerAddr) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ControllerNewServerAddr) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Port != 0 {
		i = encodeVarintBysApi(dAtA, i, uint64((uint32(m.Port)<<1)^uint32((m.Port>>31))))
		i--
		dAtA[i] = 0x18
	}
	if len(m.Ip) > 0 {
		i -= len(m.Ip)
		copy(dAtA[i:], m.Ip)
		i = encodeVarintBysApi(dAtA, i, uint64(len(m.Ip)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Target) > 0 {
		for iNdEx := len(m.Target) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Target[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintBysApi(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *MarkerOrControllerId) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MarkerOrControllerId) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *MarkerOrControllerId) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.IsMarker {
		i--
		if m.IsMarker {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x18
	}
	if m.ControllerChannelNo != 0 {
		i = encodeVarintBysApi(dAtA, i, uint64(m.ControllerChannelNo))
		i--
		dAtA[i] = 0x10
	}
	if m.Id != 0 {
		i = encodeVarintBysApi(dAtA, i, uint64(m.Id))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *PatrolStatisticsQuery) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PatrolStatisticsQuery) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *PatrolStatisticsQuery) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.LineRid) > 0 {
		i -= len(m.LineRid)
		copy(dAtA[i:], m.LineRid)
		i = encodeVarintBysApi(dAtA, i, uint64(len(m.LineRid)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.EndTime) > 0 {
		i -= len(m.EndTime)
		copy(dAtA[i:], m.EndTime)
		i = encodeVarintBysApi(dAtA, i, uint64(len(m.EndTime)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.StartTime) > 0 {
		i -= len(m.StartTime)
		copy(dAtA[i:], m.StartTime)
		i = encodeVarintBysApi(dAtA, i, uint64(len(m.StartTime)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *PatrolCheckItem) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PatrolCheckItem) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *PatrolCheckItem) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.CheckResult != 0 {
		i = encodeVarintBysApi(dAtA, i, uint64(m.CheckResult))
		i--
		dAtA[i] = 0x20
	}
	if len(m.CheckUserRID) > 0 {
		i -= len(m.CheckUserRID)
		copy(dAtA[i:], m.CheckUserRID)
		i = encodeVarintBysApi(dAtA, i, uint64(len(m.CheckUserRID)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.CheckTime) > 0 {
		i -= len(m.CheckTime)
		copy(dAtA[i:], m.CheckTime)
		i = encodeVarintBysApi(dAtA, i, uint64(len(m.CheckTime)))
		i--
		dAtA[i] = 0x12
	}
	if m.MarkerHWID != 0 {
		i = encodeVarintBysApi(dAtA, i, uint64(m.MarkerHWID))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *PatrolStatisticsQueryResult) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PatrolStatisticsQueryResult) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *PatrolStatisticsQueryResult) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.CheckResult) > 0 {
		for iNdEx := len(m.CheckResult) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.CheckResult[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintBysApi(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x2a
		}
	}
	if len(m.CheckDate) > 0 {
		i -= len(m.CheckDate)
		copy(dAtA[i:], m.CheckDate)
		i = encodeVarintBysApi(dAtA, i, uint64(len(m.CheckDate)))
		i--
		dAtA[i] = 0x22
	}
	if m.ShouldCheckCount != 0 {
		i = encodeVarintBysApi(dAtA, i, uint64((uint32(m.ShouldCheckCount)<<1)^uint32((m.ShouldCheckCount>>31))))
		i--
		dAtA[i] = 0x18
	}
	if len(m.RuleRID) > 0 {
		i -= len(m.RuleRID)
		copy(dAtA[i:], m.RuleRID)
		i = encodeVarintBysApi(dAtA, i, uint64(len(m.RuleRID)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.LineRid) > 0 {
		i -= len(m.LineRid)
		copy(dAtA[i:], m.LineRid)
		i = encodeVarintBysApi(dAtA, i, uint64(len(m.LineRid)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func encodeVarintBysApi(dAtA []byte, offset int, v uint64) int {
	offset -= sovBysApi(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *OnlineControllerInfo) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.ConnectTime)
	if l > 0 {
		n += 1 + l + sovBysApi(uint64(l))
	}
	if m.ControllerID != 0 {
		n += 1 + sozBysApi(uint64(m.ControllerID))
	}
	if m.NetworkType != 0 {
		n += 1 + sozBysApi(uint64(m.NetworkType))
	}
	l = len(m.LastDataTime)
	if l > 0 {
		n += 1 + l + sovBysApi(uint64(l))
	}
	if m.Power != 0 {
		n += 5
	}
	if m.ChannelNo != 0 {
		n += 1 + sozBysApi(uint64(m.ChannelNo))
	}
	if len(m.TransferChannelNos) > 0 {
		l = 0
		for _, e := range m.TransferChannelNos {
			l += sozBysApi(uint64(e))
		}
		n += 1 + sovBysApi(uint64(l)) + l
	}
	if m.DeviceType != 0 {
		n += 1 + sovBysApi(uint64(m.DeviceType))
	}
	return n
}

func (m *MarkerQueueNoInfo) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.ControllerRID)
	if l > 0 {
		n += 1 + l + sovBysApi(uint64(l))
	}
	if m.ControllerChannelNo != 0 {
		n += 1 + sozBysApi(uint64(m.ControllerChannelNo))
	}
	if m.QueueNoAvailable != 0 {
		n += 1 + sozBysApi(uint64(m.QueueNoAvailable))
	}
	if m.Result != 0 {
		n += 1 + sozBysApi(uint64(m.Result))
	}
	return n
}

func (m *MarkerInfo) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.ControllerID != 0 {
		n += 1 + sozBysApi(uint64(m.ControllerID))
	}
	if m.ControllerChannelNo != 0 {
		n += 1 + sozBysApi(uint64(m.ControllerChannelNo))
	}
	if m.MarkerID != 0 {
		n += 1 + sozBysApi(uint64(m.MarkerID))
	}
	l = len(m.MarkerRID)
	if l > 0 {
		n += 1 + l + sovBysApi(uint64(l))
	}
	if m.MarkerUploadCmd != 0 {
		n += 1 + sozBysApi(uint64(m.MarkerUploadCmd))
	}
	if m.MarkerChannel != 0 {
		n += 1 + sozBysApi(uint64(m.MarkerChannel))
	}
	l = len(m.MarkerCmdTime)
	if l > 0 {
		n += 1 + l + sovBysApi(uint64(l))
	}
	if m.MarkerSysPassOK {
		n += 2
	}
	l = len(m.MarkerParamTime)
	if l > 0 {
		n += 1 + l + sovBysApi(uint64(l))
	}
	if m.Status != 0 {
		n += 1 + sozBysApi(uint64(m.Status))
	}
	if m.InfoReporting != nil {
		l = m.InfoReporting.Size()
		n += 1 + l + sovBysApi(uint64(l))
	}
	if m.AlarmReporting != nil {
		l = m.AlarmReporting.Size()
		n += 1 + l + sovBysApi(uint64(l))
	}
	if m.MarkerType != 0 {
		n += 1 + sozBysApi(uint64(m.MarkerType))
	}
	if m.AlarmStatusWith4G != 0 {
		n += 1 + sozBysApi(uint64(m.AlarmStatusWith4G))
	}
	return n
}

func (m *ControllerCmd) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.ControllerID != 0 {
		n += 1 + sozBysApi(uint64(m.ControllerID))
	}
	if m.ControllerChannelNo != 0 {
		n += 1 + sozBysApi(uint64(m.ControllerChannelNo))
	}
	if m.NewChannelNo != 0 {
		n += 1 + sozBysApi(uint64(m.NewChannelNo))
	}
	if m.Cmd != 0 {
		n += 1 + sozBysApi(uint64(m.Cmd))
	}
	return n
}

func (m *UpdataMarkerParam) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.ControllerID != 0 {
		n += 1 + sovBysApi(uint64(m.ControllerID))
	}
	if m.MarkerData != nil {
		l = m.MarkerData.Size()
		n += 1 + l + sovBysApi(uint64(l))
	}
	return n
}

func (m *ControllerTarget) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.StationID != 0 {
		n += 1 + sozBysApi(uint64(m.StationID))
	}
	if m.ControllerChannelNo != 0 {
		n += 1 + sozBysApi(uint64(m.ControllerChannelNo))
	}
	return n
}

func (m *ControllerNewServerAddr) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Target) > 0 {
		for _, e := range m.Target {
			l = e.Size()
			n += 1 + l + sovBysApi(uint64(l))
		}
	}
	l = len(m.Ip)
	if l > 0 {
		n += 1 + l + sovBysApi(uint64(l))
	}
	if m.Port != 0 {
		n += 1 + sozBysApi(uint64(m.Port))
	}
	return n
}

func (m *MarkerOrControllerId) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Id != 0 {
		n += 1 + sovBysApi(uint64(m.Id))
	}
	if m.ControllerChannelNo != 0 {
		n += 1 + sovBysApi(uint64(m.ControllerChannelNo))
	}
	if m.IsMarker {
		n += 2
	}
	return n
}

func (m *PatrolStatisticsQuery) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.StartTime)
	if l > 0 {
		n += 1 + l + sovBysApi(uint64(l))
	}
	l = len(m.EndTime)
	if l > 0 {
		n += 1 + l + sovBysApi(uint64(l))
	}
	l = len(m.LineRid)
	if l > 0 {
		n += 1 + l + sovBysApi(uint64(l))
	}
	return n
}

func (m *PatrolCheckItem) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.MarkerHWID != 0 {
		n += 1 + sovBysApi(uint64(m.MarkerHWID))
	}
	l = len(m.CheckTime)
	if l > 0 {
		n += 1 + l + sovBysApi(uint64(l))
	}
	l = len(m.CheckUserRID)
	if l > 0 {
		n += 1 + l + sovBysApi(uint64(l))
	}
	if m.CheckResult != 0 {
		n += 1 + sovBysApi(uint64(m.CheckResult))
	}
	return n
}

func (m *PatrolStatisticsQueryResult) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.LineRid)
	if l > 0 {
		n += 1 + l + sovBysApi(uint64(l))
	}
	l = len(m.RuleRID)
	if l > 0 {
		n += 1 + l + sovBysApi(uint64(l))
	}
	if m.ShouldCheckCount != 0 {
		n += 1 + sozBysApi(uint64(m.ShouldCheckCount))
	}
	l = len(m.CheckDate)
	if l > 0 {
		n += 1 + l + sovBysApi(uint64(l))
	}
	if len(m.CheckResult) > 0 {
		for _, e := range m.CheckResult {
			l = e.Size()
			n += 1 + l + sovBysApi(uint64(l))
		}
	}
	return n
}

func sovBysApi(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozBysApi(x uint64) (n int) {
	return sovBysApi(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *OnlineControllerInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBysApi
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: OnlineControllerInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: OnlineControllerInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ConnectTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysApi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysApi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ConnectTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ControllerID", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.ControllerID = v
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field NetworkType", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.NetworkType = v
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LastDataTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysApi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysApi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.LastDataTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field Power", wireType)
			}
			var v uint32
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
			m.Power = float32(math.Float32frombits(v))
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ChannelNo", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.ChannelNo = v
		case 7:
			if wireType == 0 {
				var v int32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowBysApi
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= int32(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
				m.TransferChannelNos = append(m.TransferChannelNos, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowBysApi
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthBysApi
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthBysApi
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				var count int
				for _, integer := range dAtA[iNdEx:postIndex] {
					if integer < 128 {
						count++
					}
				}
				elementCount = count
				if elementCount != 0 && len(m.TransferChannelNos) == 0 {
					m.TransferChannelNos = make([]int32, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v int32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowBysApi
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= int32(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
					m.TransferChannelNos = append(m.TransferChannelNos, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field TransferChannelNos", wireType)
			}
		case 8:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field DeviceType", wireType)
			}
			m.DeviceType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DeviceType |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBysApi(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBysApi
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthBysApi
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *MarkerQueueNoInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBysApi
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: MarkerQueueNoInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: MarkerQueueNoInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ControllerRID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysApi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysApi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ControllerRID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ControllerChannelNo", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.ControllerChannelNo = v
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field QueueNoAvailable", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.QueueNoAvailable = v
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Result", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.Result = v
		default:
			iNdEx = preIndex
			skippy, err := skipBysApi(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBysApi
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthBysApi
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *MarkerInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBysApi
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: MarkerInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: MarkerInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ControllerID", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.ControllerID = v
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ControllerChannelNo", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.ControllerChannelNo = v
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field MarkerID", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.MarkerID = v
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field MarkerRID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysApi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysApi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.MarkerRID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field MarkerUploadCmd", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.MarkerUploadCmd = v
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field MarkerChannel", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.MarkerChannel = v
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field MarkerCmdTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysApi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysApi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.MarkerCmdTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field MarkerSysPassOK", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.MarkerSysPassOK = bool(v != 0)
		case 9:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field MarkerParamTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysApi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysApi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.MarkerParamTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 10:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.Status = v
		case 11:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field InfoReporting", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBysApi
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthBysApi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.InfoReporting == nil {
				m.InfoReporting = &bysproto.BInfoReporting{}
			}
			if err := m.InfoReporting.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 12:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AlarmReporting", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBysApi
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthBysApi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.AlarmReporting == nil {
				m.AlarmReporting = &bysproto.BAlarmReporting{}
			}
			if err := m.AlarmReporting.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 13:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field MarkerType", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.MarkerType = v
		case 14:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field AlarmStatusWith4G", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.AlarmStatusWith4G = v
		default:
			iNdEx = preIndex
			skippy, err := skipBysApi(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBysApi
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthBysApi
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ControllerCmd) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBysApi
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ControllerCmd: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ControllerCmd: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ControllerID", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.ControllerID = v
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ControllerChannelNo", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.ControllerChannelNo = v
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field NewChannelNo", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.NewChannelNo = v
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Cmd", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.Cmd = v
		default:
			iNdEx = preIndex
			skippy, err := skipBysApi(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBysApi
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthBysApi
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdataMarkerParam) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBysApi
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: UpdataMarkerParam: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: UpdataMarkerParam: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ControllerID", wireType)
			}
			m.ControllerID = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ControllerID |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field MarkerData", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBysApi
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthBysApi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.MarkerData == nil {
				m.MarkerData = &bysdb.DbBysMarker{}
			}
			if err := m.MarkerData.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBysApi(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBysApi
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthBysApi
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ControllerTarget) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBysApi
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ControllerTarget: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ControllerTarget: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StationID", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.StationID = v
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ControllerChannelNo", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.ControllerChannelNo = v
		default:
			iNdEx = preIndex
			skippy, err := skipBysApi(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBysApi
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthBysApi
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ControllerNewServerAddr) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBysApi
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ControllerNewServerAddr: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ControllerNewServerAddr: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Target", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBysApi
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthBysApi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Target = append(m.Target, &ControllerTarget{})
			if err := m.Target[len(m.Target)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Ip", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysApi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysApi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Ip = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Port", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.Port = v
		default:
			iNdEx = preIndex
			skippy, err := skipBysApi(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBysApi
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthBysApi
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *MarkerOrControllerId) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBysApi
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: MarkerOrControllerId: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: MarkerOrControllerId: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ControllerChannelNo", wireType)
			}
			m.ControllerChannelNo = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ControllerChannelNo |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field IsMarker", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsMarker = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipBysApi(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBysApi
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthBysApi
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *PatrolStatisticsQuery) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBysApi
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: PatrolStatisticsQuery: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: PatrolStatisticsQuery: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field StartTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysApi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysApi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.StartTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field EndTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysApi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysApi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.EndTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LineRid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysApi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysApi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.LineRid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBysApi(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBysApi
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthBysApi
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *PatrolCheckItem) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBysApi
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: PatrolCheckItem: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: PatrolCheckItem: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field MarkerHWID", wireType)
			}
			m.MarkerHWID = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MarkerHWID |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CheckTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysApi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysApi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CheckTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CheckUserRID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysApi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysApi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CheckUserRID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CheckResult", wireType)
			}
			m.CheckResult = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CheckResult |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBysApi(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBysApi
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthBysApi
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *PatrolStatisticsQueryResult) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBysApi
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: PatrolStatisticsQueryResult: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: PatrolStatisticsQueryResult: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LineRid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysApi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysApi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.LineRid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RuleRID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysApi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysApi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.RuleRID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ShouldCheckCount", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.ShouldCheckCount = v
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CheckDate", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysApi
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysApi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CheckDate = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CheckResult", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysApi
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBysApi
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthBysApi
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CheckResult = append(m.CheckResult, &PatrolCheckItem{})
			if err := m.CheckResult[len(m.CheckResult)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBysApi(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBysApi
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthBysApi
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipBysApi(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowBysApi
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowBysApi
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowBysApi
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthBysApi
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupBysApi
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthBysApi
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthBysApi        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowBysApi          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupBysApi = fmt.Errorf("proto: unexpected end of group")
)

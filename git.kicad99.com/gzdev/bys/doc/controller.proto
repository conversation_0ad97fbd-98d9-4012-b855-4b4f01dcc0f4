syntax = "proto3";

package bysproto;

option go_package = "git.kicad99.com/gzdev/bys/doc/bysproto";

//系统与控制器所有的消息交互底层都以此为包装
message Bmsg {
  //命令字
  // 1:登录
  // 2:界桩打卡上传
  // 12:回应界桩上传
  // 13：修改控制器手台监听信道号
  // 15：更新控制器新的注册地址
  // 11:ping
  //----4G界桩新增----
  // 21：信息上报
  // 31：上报应答
  // 22：报警
  // 23：拓展报警(外接传感器)
  // 32：报警应答
  // 33：报警解除
  // 28: 报警解除应答
  // 24：参数更新
  // 34：参数更新应答
  // 25：软件更新(预留)
  // 35：软件更新应答(预留)
  // 26：主动抓拍(预留)
  // 36：主动抓拍应答(预留)
  // 27：遥闭
  // 37：遥闭应答
  fixed32 Cmd = 2;

  // 从0开始增1使用,用于区分相同命令重复的包
  uint32 No = 5;

  // response code
  sint32 Res = 9;

  // msg body
  bytes Body = 10;

  // optional str parameter,额外的信息,一般不会有,有些特殊命令里面可能用到
  string Optstr = 11;

  // optional binary parameter,额外的信息,一般不会有,有些特殊命令里面可能用到
  bytes Optbin = 12;
};

//登录信息
// bmsg.cmd=1
message BloginReq {
  //控制器名称，不是控制器硬件ID
  string Name = 1;
  // base64(sha256(base64(sha256(controllerName+controllerPass))+LoginTimeStr))
  // 4g界桩: base64(sha256(base64(sha256(IMEI+ICCID))+LoginTimeStr))
  string PassHash = 2;
  // yyyy-mm-dd HH:MM:SS, 必须是3分钟内的时间(utc)
  string LoginTimeStr = 3;

  //系统名称
  string Sys = 4;

  //电量,单位V,=0无效，7.2v = 72
  sint32 Power = 5;

  //控制器本地手台当前信道号(对界桩通讯的手台)，无效时为0
  sint32 ChannelNo = 6;

  //本控制器数据中转的通道号和对应的信道号
  //[通道号1,通道1手台的信道号，通道号2,通道2手台的信道号，...]
  //如[1,1,2,1]表示通道1 在1信道，通道2也在1信道
  repeated sint32 TransferChannelNos = 7;

  //登录渠道， 0:未知 1:有线网络， 2:4g网络 3:无线FSK
  int32 NetworkType = 8;

  //登录设备类型 0:fsk控制器 1:4g界桩
  int32 DeviceType = 9;

  // 4g界桩IMEI
  string IMEI = 10;

  // 4g界桩ICCID
  string ICCID = 11;

  // 界桩参数版本时间
  //yyyy-mm-dd HH:MM:SS
  string dataVersion = 12;
}

//登录回应
// bmsy.cmd=1
// bmsg.Res=1
// bmsg.body=BloginRes
message BloginRes {
  // 100: 登录成功 -1：无此控制器信息 -2:时间参数不对 -3: 未知错误 -4:pass 不对
  int32 Code = 1;

  //控制器硬件ID，登录成功时返回
  fixed32 ControllerID = 2;

  //服务器时间yyyy-mm-dd HH:MM:SS.zzz(utc)
  string ServerTime = 5;

  //校验界桩crc2的系统密码
  string SystemPassword = 6;

  //错误描述，未知错误时可能有
  string Err = 7;
}

//界桩上传的gps信息,无效gps时为全0
message BGPS {
  //东经为+，西经为-,单位为度
  double Lon = 1;
  //北纬为+，南纬为-,单位为度
  double Lat = 2;
  sint32 Height = 3;
}

//界桩数据上传
// bmsg.cmd=2
message BdeviceUpdate {
  //界桩ID
  sint32 DeviceID = 1;
  //上传的命令
  // 0xd1: 常规上传
  // 0xd2: 报警上传
  sint32 Cmd = 2;
  //界桩状态，目前只用低位一个字节
  sint32 Status = 3;

  //界桩上传的系统密码检验是否正确 0:正确 1：不正确
  int32 SystemPassCheckOK = 4;

  // gps信息，没有时不需要填写
  BGPS GPS = 5;

  //界桩参数版本
  int32 ParamVersion = 6;

  //界桩参数更新时间
  string ParamTime = 7;

  //指令时间
  string CmdTime = 8;

  //接收的基站/中继控制器ID
  sint32 StationID = 9;

  //基站控制器的接收通道号，编号从1开始,不是基站上传的，此值为0
  sint32 StationDeviceNo = 10;

  //接收设备的场强值
  sint32 DeviceFieldStrength = 12;

  //接收设备的信道号
  sint32 DeviceChannelNo = 13;
}

//中继/基站状态信息
message ControllerStatus {
  //接收的基站/中继控制器ID
  sint32 StationID = 1;

  //基站控制器的接收手台号，编号从1开始,本控制器的状态时，此值为0
  sint32 StationDeviceNo = 2;

  //电量,单位V,=0无效，7.2v = 72
  sint32 Power = 3;

  //控制器手台当前信道号(与界桩通讯的手台)
  sint32 ChannelNo = 4;

  //本控制器数据中转的通道号和对应的信道号
  //[通道号1,通道1手台的信道号，通道号2,通道2手台的信道号，...]
  //如[1,1,2,1]表示通道1 在1信道，通道2也在1信道
  repeated sint32 TransferChannelNos = 7;

  //控制器状态
  //bit0:时间异常 0:无异常 1:无法获取时间（可能gps异常)
  uint32 Status = 8;  
}

// ping 对方
// bmsg.cmd=11
// bmsg.body = ControllerStatus
// bmsg.Res 0: ping 1:pong 10:ping,需要对方回应时间 11:pong
// 带时间的回应,时间(utc)在bmsg.Optstr=yyyy-mm-dd HH:MM:SS.zzz
// 控制器登录成功后5分钟内无数据活动需要发送Ping信息
//
// 后台可以主动Ping基站或基站下的中继控制器
// 此时bmsg.cmd=11
// bmsg.body = ControllerStatus
// ControllerStatus只有StationID，StationDeviceNo有效
// 基站收到Ping时如果是给自己的StationDeviceNo=0,马上回应自己的状态
// 如果是自己的下级控制器，转发命令下相应的下级控制器，让它回传最新状态信息

// 回应界桩上传（如果确实有参数需要修改的话）
// bmsg.cmd=12
message BdeviceUpdateResponse {

  //界桩ID
  sint32 DeviceID = 1;

  //接收的基站/中继控制器ID
  sint32 StationID = 9;

  //基站控制器的通道号，编号从1开始,不是基站上传的，此值为0
  sint32 StationDeviceNo = 10;

  //回应命令数据
  bytes Cmd0xD0 = 11;
}

//修改控制器手台默认信道号（针对与界桩通讯的手台）
// bmsg.cmd=13
message BControllerUpdateChannel {
  //控制器ID
  sint32 StationID = 9;

  //控制器通道号
  sint32 StationDeviceNo = 10;

  //新默认监听信道号
  sint32 NewChannelNo = 3;
}

//修改控制器手台默认信道号（针对中继/基站通讯的手台）
// bmsg.cmd=14, 此命令只对基站下发
// bmsg.body=BControllerUpdateChannel
// message BControllerUpdateChannel {
//   //基站控制器ID
//   sint32 StationID = 9;

//   //中继控制器通道号
//   sint32 StationDeviceNo = 10;

//   //新默认监听信道号
//   sint32 NewChannelNo = 3;
// }

// bmsg.cmd=15, 更新控制器新的注册地址
// bmsg.body=BControllerNewServerAddr
// bmsg.Res=1,控制器应答
message BControllerNewServerAddr {
  // 基站控制器ID
  sint32 StationID = 1;

  // 在上级控制器的通道号，中继时需要填写，基站本地通道时为0
  sint32 ControllerChannelNo = 2;

  // 新服务器IP地址，支持域名和IP
  string Ip = 3;

  // 新服务器端口
  sint32 Port = 4;
}

// 控制器/中继报警
// bmsg.cmd=16
// bmsg.body = ControllerStatus
// 报警内容见ControllerStatus.Status

//信息上报
//bmsg.cmd=21
message BInfoReporting
{
    //本机设备编号
    uint32 deviceID    = 1;
    //1：开机上报 2：调试上报 3：定时上报
    uint32 type                 = 2;
    //sim卡iccid 20位
    string iccid                = 3;
    //格式：Vx.x.x_yyMMdd V1.0.1_231031
    string softwareVersion      = 4;
    //yyyy-mm-dd HH:MM:SS
    string dataVersion          = 5;
    //设备时间yyyy-mm-dd HH:MM:SS
    string time                 = 6;
    //设备电池电压单位mv
    uint32 battery              = 7;
    //设备4G模块场强信号幅度 单位dbm
    sint32 signal               = 8;
    // Bit0：报警锁定(0:正常，1:报警锁定)
    // Bit1：RTC时钟状态(0:正常，1:故障)
    // Bit2：GPS模块状态(0:正常，1:故障)
    // Bit3：三轴传感器状态(0:正常，1:故障)
    // Bit4：电池状态(0:正常，1:故障)
    // Bit5：摄像头状态(0:正常，1:故障)(预留)
    // Bit6：红外探头状态(0:正常，1:故障)(预留)
    // Bit7-Bit31：预留
    uint32 state                = 9;
    // Bit0：位移报警(0:正常，1:报警)
    // Bit1：震动报警(0:正常，1:报警)
    // Bit2：倾斜报警(0:正常，1:报警)
    // Bit3：红外报警(0:正常，1:报警)(预留)
    // Bit4-Bit31：预留
    uint32 alarmstate           = 10;
    //设备温度
    float temp                 = 11;
    //湿度 单位：%RH
    float rh                   = 12;
}

//震动报警参数
message BVamp
{
    //震动幅度
    uint32 amplitude             = 1;
    //震动持续时间 s
    uint32 duration              = 2;
} 
//报警上报
//bmsg.cmd=22 
message BAlarmReporting
{
    //本机设备编号
    uint32 deviceID    = 1;
    //1：开机上报 2：调试上报 3：报警上报
    uint32 type                 = 2;
    // Bit0：位移报警(0:正常，1:报警)
    // Bit1：震动报警(0:正常，1:报警)
    // Bit2：倾斜报警(0:正常，1:报警)
    // Bit3：红外报警(0:正常，1:报警)(预留)
    // Bit4-Bit31：预留
    uint32 alarmstate           = 3;
    //定位坐标
    BGPS locate                 = 4;
    //倾斜角度，单位：度
    uint32 attitude             = 5;
    //震动报警
    BVamp vibration             = 6;
    //红外预留
    uint32 infrared             = 7;
    //摄像头预留
    uint32 camera               = 8;
    //其他（预留）
    bytes  reserve              = 9;
}

//报警解除
//bmsg.cmd=33
message BAlarmClear
{
    //目标设备编号
    uint32 deviceID    = 1;
    //1：报警解除 2：报警锁定
    uint32 response             = 2;
}

//参数更新
//bmsg.cmd=24
message BDataUpdate
{
    //目标设备编号
    uint32 deviceID    = 1;
    //Bit0：解除报警锁定 Bit1：更新参数 Bit2：校准时间
    uint32 type                 = 2;
    //版本yyyy-mm-dd HH:MM:SS
    string dataVersion          = 3;
    //服务器地址  xxx.xxx.xxx.xxx:xxxx   xxxxxx.com:xxxx
    string addr                 = 4;
    //定时上报基准时间HH:MM:SS
    string timerbase            = 5;
    //timer 6h、8h、12h、24h
    uint32 timer                = 6;
    //姿态报警阈值
    uint32 attitude             = 7;
    //位移报警阈值
    uint32 dirft                = 8;
    //震动报警阈值
    BVamp vibration             = 9;
    //红外报警阈值
    uint32 infrared             = 10;
    //延迟休眠时间 10s
    uint32 t1                   = 11;
    //调试模式时间 120s
    uint32 t2                   = 12;
    //报警间隔 10s
    uint32 t3                   = 13;
    //报警次数 10
    uint32 n1                   = 14;
}

//遥闭
//bmsg.cmd=27
message BShutDown
{
    //目标设备编号
    uint32 deviceID    = 1;
    //0:遥活 1:遥闭  2:遥晕
    uint32 type        = 2;
    //0:遥活 1:遥闭  2:遥晕
    uint32 cam_type        = 3;
}

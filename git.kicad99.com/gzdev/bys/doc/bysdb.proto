syntax = "proto3";

package bysdb;

option go_package = "git.kicad99.com/gzdev/bys/doc/bysdb";

// 控制器信息表
//@rpc crud pcrud
//@dbpost CREATE INDEX IF NOT EXISTS idxDbControllerOrgRID on DbController USING hash(OrgRID);
//@dbpost CREATE INDEX IF NOT EXISTS idxDbControllerParentRID on DbController USING hash(ParentRID);
//@dbpost CREATE UNIQUE INDEX IF NOT EXISTS uidxDbControllerParent ON DbController (ParentRID, ParentChannelNo) WHERE ControllerType=1;
message DbController {
  //@db uuid primary key
  //行ID
  string RID = 1;

  //@db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
  //控制器所属的群组
  string OrgRID = 2;

  //@db varchar(16) not null unique
  //controller编号
  string ControllerNo = 3;

  //@db text
  //controller描述信息
  string ControllerDescription = 4;

  //@db int
  //控制器类型 1:中继控制器  2:基站控制器
  int32 ControllerType = 5;

  //@db uuid
  //上级控制器RID, 可以没有，中继控制器一般都有
  string ParentRID = 6;

  //@db double precision
  //经度
  double Lon = 7;

  //@db double precision
  //纬度
  double Lat = 8;

  //@db jsonb not null default  '{}'::jsonb
  // 其它设置，可以在里面扩展需要用到的其它信息
  string Setting = 9;

  //@db int not null unique
  // 控制器硬件ID，int32 > 0
  sint32 ControllerHWID = 10;

  //@db int not null default 1
  //控制器可用信道数，中继控制器为1,基站按实际情况
  sint32 ChannelCount = 11;

  //@db int default 12
  //地图开始显示级别
  sint32 MapShowLevel = 12;

  //@db timestamp not null default now_utc()
  //数据最后修改时间
  string UpdatedAt = 14;

  //@db text
  //数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
  string UpdatedDC = 15;

  //@db int
  //默认通讯方式， 1:有线网络， 2:4g网络 3:无线FSK
  sint32 DefaultNetworkType = 16;

  //@db int
  //中继对应的上级控制器的通道
  sint32 ParentChannelNo = 17;
}

// 界桩信息表
//@rpc crud pcrud
//@dbpost CREATE INDEX IF NOT EXISTS idxDbBysMarkerOrgRID on DbBysMarker USING hash(OrgRID);
//@dbpost ALTER TABLE DbBysMarker ADD COLUMN IF NOT EXISTS MarkerType integer not null default 0;
//@dbpost ALTER TABLE DbBysMarker ADD COLUMN IF NOT EXISTS MarkerSettings jsonb not null default  '{}'::jsonb;
//@dbpost ALTER TABLE dbbysmarker DROP CONSTRAINT if exists dbbysmarker_controllerrid_controllerchannel_markerqueueno_key;
//@dbpost drop index if exists dbbysmarker_controllerrid_controllerchannel_markerqueueno_key;
//@dbpost CREATE UNIQUE INDEX IF NOT EXISTS idxdbbysmarker_controllerrid_controllerchannel_markerqueueno_key on DbBysMarker (ControllerRID, ControllerChannel, MarkerQueueNo) where MarkerType=0; --同一个信道的排队号必须唯一,只针对旧的界桩
//@dbpost ALTER TABLE DbBysMarker ALTER COLUMN ControllerRID DROP NOT NULL;
//@dbpost ALTER TABLE DbBysMarker ADD COLUMN IF NOT EXISTS ICCID varchar(20);
//@dbpost ALTER TABLE DbBysMarker ADD COLUMN IF NOT EXISTS ExpirationDate timestamp;
//@dbpost CREATE UNIQUE INDEX IF NOT EXISTS idxDbBysMarkerICCID on DbBysMarker (ICCID) where MarkerType=1;
//@dbpost ALTER TABLE DbBysMarker ADD COLUMN IF NOT EXISTS IMEI text;
//@dbpost ALTER TABLE DbBysMarker ADD COLUMN IF NOT EXISTS CameraDisabled integer not null default 0;
message DbBysMarker {
  //@db uuid primary key
  //行ID
  string RID = 1;

  //@db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
  //界桩所属的群组
  string OrgRID = 2;

  //@db varchar(16) not null unique
  //界桩编号
  string MarkerNo = 3;

  //@db text
  //界桩描述信息
  string MarkerDescription = 4;

  //@db uuid REFERENCES DbController(RID) ON DELETE RESTRICT
  //所属控制器RID
  string ControllerRID = 5;

  //@db int not null
  //所属的基站通道，如果所属的控制器为中继控制器，此值固定为1
  sint32 ControllerChannel = 6;

  //@db double precision
  //经度
  double Lon = 7;

  //@db double precision
  //纬度
  double Lat = 8;

  //@db int not null unique
  //界桩硬件ID,范围
  sint32 MarkerHWID = 9;

  //@db jsonb not null default  '{}'::jsonb
  // 其它设置，可以在里面扩展需要用到的其它信息
  // 新的红线图规则属性为：redLine，不存在该字段为默认连接，空数组为不连接，
  // 数组元素为界桩硬件ID，每个都连接
  string Setting = 10;

  //@db int
  //界桩型号 1:长型 X*Y*Z cm, 2:短型 x*y*z cm
  sint32 MarkerModel = 11;

  //@db int default 12
  //地图开始显示级别
  sint32 MapShowLevel = 12;

  //@db int not null
  //在所属于基站下的排队号
  sint32 MarkerQueueNo = 13;

  //@db timestamp not null default now_utc()
  //数据最后修改时间
  string UpdatedAt = 14;

  //@db text
  //数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
  string UpdatedDC = 15;

  //@db timestamp not null default now_utc()
  //参数更新时间
  string MarkerParamTime = 16;

  //@db int not null default 24
  //打卡时间间隔(6-24小时)
  sint32 MarkerDayInterval = 17;

  //@db int not null default 6
  //排队发射间隔时间（秒）
  sint32 MarkerQueueInterval = 18;

  //@db int not null default 60
  //报警后发射间隔(30-240秒)
  sint32 MarkerEmergentInterval = 19;

  //@db int not null default 1
  //界桩通信信道
  sint32 MarkerChannel = 20;

  //@db time not null
  //界桩苏醒基准时间
  string MarkerWakeupBaseTime = 21;

  //@db int not null default 0
  //界桩是否已经被遥毙, 0:遥活 1:遥闭  2:遥晕
  sint32 MarkerDisabled = 22;

  //@db boolean default true
  //界桩是否有安装电子设备
  bool HasInstallDevice = 23;

  //@db int unique
  //界桩红线顺序,这个是当前界桩序号*1000, 预留以后增加界桩
  //sint32 MarkerRedLineNo = 24;

  //@db boolean default false
  //石头界桩是否已安装
  bool HasInstallStone = 25;

  //@db int not null default 0
  //常规界桩：0, 4g界桩(BF-DTD002): 1, 4g界桩带摄像机(BF-DTD002(Pro)): 2, 专网界桩dmr版(BF-DTD003): 3
  sint32 MarkerType = 26;

  //@db jsonb not null default  '{}'::jsonb
  //MarkerType=1时，填写bysproto.BDataUpdate
  string MarkerSettings = 27;

  //@db varchar(20)
  // 4G界桩的iccid卡号，20位数字字符串
  string ICCID = 28;

  //@db timestamp
  // iccid卡号到期日期
  string ExpirationDate = 29;

  //@db text
  // 4g界桩的设备唯一标识
  string IMEI = 30;

  //@db int not null default 0
  //摄像机是否已经被遥毙, 0:遥活 1:遥闭  2:遥晕
  sint32 CameraDisabled = 31;
}

//控制器上下线历史表，以月为单位分表
//@rpc pcrud
//@dbend PARTITION BY RANGE (ActionTime)
//@dbpost CREATE INDEX IF NOT EXISTS idxDbControllerOnlineHistoryControllerHWID on DbControllerOnlineHistory USING hash(ControllerHWID);
//@dbpost CREATE INDEX IF NOT EXISTS idxDbControllerOnlineHistoryOrgRID on DbControllerOnlineHistory USING hash(OrgRID);
//@dbpost ALTER TABLE dbcontrolleronlinehistory ADD COLUMN IF NOT EXISTS Status integer;
//@dbpost ALTER TABLE dbcontrolleronlinehistory ADD COLUMN IF NOT EXISTS DeviceType integer;
message DbControllerOnlineHistory {

  //@db uuid not null
  //所属的群组
  string OrgRID = 2;

  //@db timestamp not null
  //动作时间,utc
  string ActionTime = 3;

  //@db int
  //控制器硬件ID
  sint32 ControllerHWID = 4;

  //@db int
  //action 1:上线 2:下线 11:ping信息
  sint32 ActionCode = 5;

  //@db text
  //ip/状态信息
  string IpInfo = 6;

  //@db int
  //登录网络类型,只对上线有效
  sint32 NetworkType = 7;

  //@db real
  //电量V
  float Power = 8;

  //@db int
  //状态
  uint32 Status = 9;

  //登录设备类型 0:fsk控制器 1:4g界桩
  int32 DeviceType = 10;
}

//界桩/控制器媒体信息表
//@rpc pcrud
//@dbpost CREATE INDEX IF NOT EXISTS idxDbMediaInfoOrgRID on DbMediaInfo USING hash(OrgRID);
//@dbpost CREATE INDEX IF NOT EXISTS idxDbMediaInfoMarkerRID on DbMediaInfo USING hash(MarkerRID);
//@dbpost CREATE INDEX IF NOT EXISTS idxDbMediaInfoControllerRID on DbMediaInfo USING hash(ControllerRID);
message DbMediaInfo {
  //@db uuid primary key
  //行ID
  string RID = 1;

  //@db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
  //物体所属的群组
  string OrgRID = 2;

  //@db uuid null REFERENCES DbBysMarker(RID) ON DELETE set null
  //界桩的RID
  string MarkerRID = 3;

  //@db uuid null REFERENCES DbController(RID) ON DELETE set null
  //控制器的RID
  string ControllerRID = 4;

  //@db int
  //媒体类型 1:normal pic 2:3d pic 3:video
  sint32 MediaType = 5;

  //@db text
  //媒体原始文件名
  string MediaOrigFileName = 6;

  //@db uuid REFERENCES DbUser(RID) ON DELETE set null
  //上传用户rid
  string UploadUserRID = 7;

  //@db text
  //描述信息
  string MediaDescription = 8;

  //@db timestamp not null
  //上传时间,utc
  string UploadTime = 9;

  //@db jsonb not null default  '{}'::jsonb
  // 其它设置，可以在里面扩展需要用到的其它信息
  string Setting = 10;

  //@db uuid REFERENCES DbUser(RID) ON DELETE set null
  //最新编辑用户rid
  string LastUpdateUserRID = 11;

  //@db timestamp not null default now_utc()
  //最新编辑时间,utc
  string LastUpdateTime = 12;
}

//界桩上传数据历史表，以月为单位分表
//@rpc pcrud
//@dbend PARTITION BY RANGE (ActionTime)
//@dbpost CREATE INDEX IF NOT EXISTS idxDbMarkerHistoryMarkerHWID on DbMarkerHistory USING hash(MarkerHWID);
//@dbpost CREATE INDEX IF NOT EXISTS idxDbMarkerHistoryOrgRID on DbMarkerHistory USING hash(OrgRID);
//@dbpost CREATE INDEX IF NOT EXISTS idxDbMarkerHistoryEmergency on DbMarkerHistory (Status) where (Status & 128) >0;
//@dbpost alter table DbMarkerHistory ADD COLUMN IF NOT EXISTS RecvControllerID int;
//@dbpost alter table DbMarkerHistory ADD COLUMN IF NOT EXISTS ReportInfo jsonb not null default  '{}'::jsonb;
message DbMarkerHistory {
  //@db int
  //接收的控制器ID
  sint32 ControllerID = 1;

  //@db int
  //接收的控制器通道
  sint32 ControllerChannel = 2;

  //@db uuid not null
  //所属的群组
  string OrgRID = 3;

  //@db timestamp not null
  //动作时间,utc
  string ActionTime = 4;

  //@db int
  //界桩硬件ID
  sint32 MarkerHWID = 5;

  //@db int
  //action 0xd1, 0xd2
  //4g界桩信息上报21, ReportInfo=bysproto.BInfoReporting
  //4g界桩报警22, ReportInfo=bysproto.BAlarmReporting
  sint32 ActionCode = 6;

  //@db int
  sint32 Status = 7;

  //@db text
  //配置参数信息
  string ParamInfo = 8;

  //@db double precision
  //gps lon
  double Lon = 9;

  //@db double precision
  //gps lat
  double Lat = 10;

  //@db timestamp
  //cmd time
  string CmdTime = 11;

  //@db int
  //实际接收的控制器ID（中继/基站）
  sint32 RecvControllerID = 12;

  //@db jsonb not null default  '{}'::jsonb
  string ReportInfo = 13;
}

//4g界桩NFC巡查打卡历史表，以月为单位分表
//@rpc pcrud
//@dbend PARTITION BY RANGE (ActionTime)
//@dbpost CREATE INDEX IF NOT EXISTS idxDbMarkerPatrolHistoryHWID on DbMarkerPatrolHistory USING hash(MarkerHWID);
//@dbpost CREATE INDEX IF NOT EXISTS idxDbMarkerPatrolHistoryOrgRID on DbMarkerPatrolHistory USING hash(OrgRID);
message DbMarkerPatrolHistory {
  //@db text
  // NFC卡片ID，hex string
  string NFCID = 1;

  //@db timestamp not null
  // NFC打卡时间,utc
  string ActionTime = 2;

  //@db int
  // 界桩硬件ID
  sint32 MarkerHWID = 3;

  //@db uuid not null
  // 打卡用户
  string UserID = 4;

  //@db uuid not null
  //所属的群组
  string OrgRID = 5;
}

//巡查线路表
//@rpc pcrud
//@dbpost CREATE INDEX IF NOT EXISTS idxDbNFCPatrolLineOrgRID on DbNFCPatrolLine USING hash(OrgRID);
//@dbpost CREATE INDEX IF NOT EXISTS idxDbNFCPatrolLineName on DbNFCPatrolLine USING hash(Name);
message DbNFCPatrolLine {
  //@db uuid primary key
  //行ID
  string RID = 1;

  //@db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
  //线路的归属组
  string OrgRID = 2;

  //@db varchar(16) not null unique
  //线路名称
  string Name = 3;

  //@db text
  string Note = 4;

  //@db timestamp not null default now_utc()
  //数据最后修改时间
  string UpdatedAt = 5;

  //@db text
  //数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
  string UpdatedDC = 6;
}

//巡查线路详细表，分开主要是方便使用数据库外键约束
//@rpc pcrud
//@dbpost CREATE INDEX IF NOT EXISTS idxDbNFCPatrolLineDetailOrgRID on DbNFCPatrolLineDetail USING hash(OrgRID);
//@dbpost CREATE INDEX IF NOT EXISTS idxDDbNFCPatrolLineDetailLineRID on DbNFCPatrolLineDetail USING hash(LineRID);
//@dbpost CREATE INDEX IF NOT EXISTS idxDbNFCPatrolLineDetailMarkerRID on DbNFCPatrolLineDetail USING hash(MarkerRID);
message DbNFCPatrolLineDetail {
  //@db uuid primary key
  //行ID
  string RID = 1;

  //@db uuid not null REFERENCES DbNFCPatrolLine(RID) ON DELETE CASCADE
  //归属线路
  string LineRID = 2;

  //@db uuid not null REFERENCES DbBysMarker(RID) ON DELETE CASCADE
  //线路下的巡查点
  string MarkerRID = 3;

  //@db timestamp not null default now_utc()
  //数据最后修改时间
  string UpdatedAt = 4;

  //@db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
  //归属组，与路线的上线一致
  string OrgRID = 5;
}

//界桩NFC巡查规则
//@rpc pcrud
//@dbpost CREATE INDEX IF NOT EXISTS idxDbNFCPatrolLineRulesOrgRID on DbNFCPatrolLineRules USING hash(OrgRID);
//@dbpost CREATE INDEX IF NOT EXISTS idxDbNFCPatrolLineRulesName on DbNFCPatrolLineRules USING hash(Name);
message DbNFCPatrolLineRules {
  //@db uuid primary key
  //行ID
  string RID = 1;

  //@db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
  //规则的归属组
  string OrgRID = 2;

  //@db varchar(16) not null unique
  //规则名称
  string Name = 3;

  //@db boolean default false
  //星期一
  bool Day1 = 4;
  //@db boolean default false
  //星期二
  bool Day2 = 5;
  //@db boolean default false
  //星期三
  bool Day3 = 6;
  //@db boolean default false
  //星期四
  bool Day4 = 7;
  //@db boolean default false
  //星期五
  bool Day5 = 8;
  //@db boolean default false
  //星期六
  bool Day6 = 9;
  //@db boolean default false
  //星期日
  bool Day7 = 10;

  //@db time
  //巡查开始的时间
  string CheckStartTime = 11;

  //@db time
  //巡查结束的时间
  string CheckEndTime = 12;

  //@db int not null default 1
  //巡查次数
  int32 CheckCount = 13;

  //@db int default 0
  //线路巡查规则生效时间的类型 0:总是生效 1:按有效时间段
  int32 EffectiveType = 14;

  //@db timestamp
  //规则开始生效时间
  string EffectiveStart = 15;

  //@db timestamp
  //规则生效结束时间
  string EffectiveEnd = 16;

  //@db text
  string Note = 17;

  //@db timestamp not null default now_utc()
  //数据最后修改时间
  string UpdatedAt = 18;

  //@db text
  //数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
  string UpdatedDC = 19;
}

//巡查线路和规则的关系表，分开主要是方便使用数据库外键约束
//@rpc pcrud
//@dbpost CREATE INDEX IF NOT EXISTS idxDbNFCPatrolLineAndRulesOrgRID on DbNFCPatrolLineAndRules USING hash(OrgRID);
//@dbpost CREATE INDEX IF NOT EXISTS idxDbNFCPatrolLineAndRulesLineRID on DbNFCPatrolLineAndRules USING hash(LineRID);
//@dbpost CREATE INDEX IF NOT EXISTS idxDbNFCPatrolLineAndRulesRuleRID on DbNFCPatrolLineAndRules USING hash(RuleRID);
message DbNFCPatrolLineAndRules {
  //@db uuid primary key
  //行ID
  string RID = 1;

  //@db uuid not null REFERENCES DbNFCPatrolLine(RID) ON DELETE CASCADE
  //归属线路
  string LineRID = 2;

  //@db uuid not null REFERENCES DbNFCPatrolLineRules(RID) ON DELETE CASCADE
  //线路规则
  string RuleRID = 3;

  //@db timestamp not null default now_utc()
  //数据最后修改时间
  string UpdatedAt = 4;

  //@db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
  //归属组，与路线的上线一致
  string OrgRID = 5;
}

// 合方圆摄像机上传图片时，附带的json结构体
message CamImageData {
  // 设备IMEI
  string dev_id = 1;
  // 4G模块自动添加
  string ccid = 2;
  // 固件版本
  string firmware_version = 3;
  // 抓拍时间戳，Unix秒时间戳
  sfixed64 timestamp = 4;
  // 电池电压单位mv
  float battery = 5;
  // 4G信号强度
  string signal = 6;
  // 环境温度
  sint32 temp_env = 7;
  // CPU温度
  sint32 temp_cpu = 8;
  // 工作类型
  // 0 – 手动
  // 1 – 唤醒拍照
  // 2 – 定时拍照
  // 3 – 唤醒录像
  // 4 – 定时录像
  sint32 type = 9;

  // 以下为可选字段
  // 放大系数
  float zoom_rate = 10;
  // 充电电流，合方圆太阳能充电模块支持
  float icharge = 11;
  // 负载电流，合方圆太阳能充电模块支持
  float iload = 12;
  // 充电电压，合方圆太阳能充电模块支持
  float vcharge = 13;
  // 上传图片时，界桩自定义的json数据
  // "{\"hwid\": 14, \"type\": 2}"
  // hwid: 界桩硬件ID
  // type: 1、红外感应抓拍，2、报警抓拍，3、定时抓拍，4、手动抓拍
  string cust_data = 14;
}

//4g界桩上传的图片历史表，以月为单位分表
//@rpc pcrud
//@dbend PARTITION BY RANGE (UploadTime)
//@dbpost CREATE INDEX IF NOT EXISTS idxDbMarkerUploadImageHistoryHWID on DbMarkerUploadImageHistory USING hash(MarkerHWID);
//@dbpost CREATE INDEX IF NOT EXISTS idxDbMarkerUploadImageHistoryOrgRID on DbMarkerUploadImageHistory USING hash(OrgRID);
message DbMarkerUploadImageHistory {
  //@db uuid not null
  //所属的群组
  string OrgRID = 1;

  //@db int
  // 界桩硬件ID
  sint32 MarkerHWID = 2;

  //@db timestamp not null
  // 抓拍时间,utc
  string CaptureTime = 3;

  //@db int
  // 抓拍的动作类型，与CamImageData.type一致
  // 0 – 手动
  // 1 – 唤醒拍照
  // 2 – 定时拍照
  // 3 – 唤醒录像
  // 4 – 定时录像
  sint32 CaptureType = 4;

  //@db timestamp not null
  // 服务器接收上传时间,utc
  string UploadTime = 5;

  //@db text
  //上传的图片保存路径，f512673f-0f6a-4b54-95bf-d377b78fba1a.jpg
  string FileName = 6;

  //@db jsonb not null default  '{}'::jsonb
  // 界桩上传图片时附带的json数据，CamImageData json字符串
  string FormData = 7;
}

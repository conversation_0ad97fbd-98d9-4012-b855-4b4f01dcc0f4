#!/bin/sh

#./update-bysproto.sh

ykitDir="../../../ykit"
output="../../../../"
# output="./output"
mkdir -p $output

#gogo protobuf for controller
protoc --gogofaster_out=$output controller.proto



#ygen for bysdb
mkdir -p generatedsql
protoc --ygen-gogofaster_out=$output --ygen-gocrud_out=./bysdb/ --ygen-gocrud-proto_out=. --ygen-sql_out=generatedsql bysdb.proto
protoc --ygen-gogofaster_out=$output bysdb.list.proto
protoc -I=. -I=$ykitDir/goutil/crud --ygen-gogofaster_out=plugins=grpc:$output bysdb.rpc.proto

#bys api
protoc -I=. -I=$ykitDir/yrpcmsg -I=$ykitDir/database --gogofaster_out=plugins=grpc:$output bys.api.proto


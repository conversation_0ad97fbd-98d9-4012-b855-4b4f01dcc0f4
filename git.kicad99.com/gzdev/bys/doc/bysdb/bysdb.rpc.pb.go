// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: bysdb.rpc.proto

package bysdb

import (
	context "context"
	fmt "fmt"
	crud "git.kicad99.com/ykit/goutil/crud"
	proto "github.com/gogo/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

func init() { proto.RegisterFile("bysdb.rpc.proto", fileDescriptor_70ef6f54c0988c87) }

var fileDescriptor_70ef6f54c0988c87 = []byte{
	// 1071 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xec, 0x5a, 0xcd, 0x6f, 0xdc, 0x44,
	0x14, 0xcf, 0x68, 0xd3, 0x48, 0x0c, 0xa2, 0x89, 0xa6, 0x88, 0xc2, 0xb6, 0x35, 0xe5, 0xeb, 0x58,
	0xaf, 0x9b, 0x2d, 0x69, 0x56, 0xc9, 0x96, 0x66, 0xbb, 0x84, 0x06, 0x12, 0xba, 0x04, 0xf5, 0xc2,
	0xcd, 0x6b, 0x0f, 0xc1, 0xaa, 0x63, 0xaf, 0x6c, 0xa7, 0xd2, 0xf2, 0x07, 0x70, 0x85, 0x23, 0x77,
	0xae, 0x5c, 0xb9, 0x70, 0xe5, 0x00, 0xc7, 0x88, 0x13, 0x12, 0x17, 0x94, 0x48, 0x5c, 0x38, 0x70,
	0xe1, 0x8a, 0x84, 0x62, 0x8f, 0x07, 0xaf, 0x3d, 0x7e, 0xf3, 0x4c, 0x83, 0x72, 0xd9, 0xe3, 0x7a,
	0xde, 0xef, 0x8d, 0xdf, 0xfc, 0x3e, 0xa6, 0xb5, 0x42, 0x97, 0xc7, 0xd3, 0xd8, 0x1d, 0x9b, 0xd1,
	0xc4, 0x31, 0x27, 0x51, 0x98, 0x84, 0xec, 0x52, 0xfa, 0xa0, 0x4d, 0x9d, 0xe8, 0xc8, 0xcd, 0x1e,
	0xb5, 0x9f, 0xcf, 0x6a, 0xb2, 0x1f, 0x2b, 0xd9, 0x0f, 0xdf, 0x8b, 0x93, 0xec, 0xc9, 0xea, 0xb7,
	0x2d, 0xba, 0xbc, 0x3f, 0x71, 0x86, 0xe3, 0x07, 0x61, 0x90, 0x44, 0xa1, 0xef, 0xf3, 0x88, 0x99,
	0x74, 0x69, 0x27, 0x88, 0x79, 0x94, 0xb0, 0x2b, 0x66, 0x06, 0x28, 0x2e, 0xb7, 0x97, 0xcd, 0xb4,
	0xfd, 0x70, 0x6f, 0x77, 0x9f, 0xc7, 0x47, 0x7e, 0x72, 0x56, 0xff, 0x78, 0xe2, 0xda, 0x09, 0x47,
	0xd6, 0xbf, 0x4d, 0x5f, 0x18, 0xd9, 0x51, 0xe2, 0xd9, 0x7e, 0x23, 0x98, 0x49, 0x97, 0x86, 0xdc,
	0xe7, 0xe8, 0x7a, 0x8b, 0x3e, 0xf7, 0x31, 0xf7, 0xb9, 0x93, 0x3c, 0x0a, 0x38, 0xbb, 0x2c, 0x57,
	0x47, 0x76, 0x64, 0x1f, 0xb6, 0x55, 0x2d, 0x58, 0x97, 0xd2, 0x0c, 0xb1, 0x67, 0x07, 0x53, 0x14,
	0xc4, 0x22, 0xcc, 0xa2, 0x97, 0x3e, 0x3a, 0xe2, 0xd1, 0x94, 0xad, 0x64, 0xf5, 0xe9, 0x0f, 0x10,
	0xd1, 0xa3, 0x34, 0x2d, 0x1a, 0xd8, 0x89, 0xf3, 0x99, 0x02, 0x76, 0x55, 0x01, 0xdb, 0xf5, 0xe2,
	0xc4, 0x22, 0xab, 0xdf, 0xb5, 0xe8, 0xca, 0x28, 0x9a, 0xf3, 0xf5, 0x0c, 0x7c, 0xdd, 0xc9, 0xf9,
	0x7a, 0x31, 0xab, 0x1f, 0x45, 0xde, 0x53, 0xcf, 0xe7, 0x07, 0x1c, 0x44, 0xf5, 0x67, 0x38, 0x53,
	0x43, 0x01, 0xde, 0xbe, 0x69, 0xd1, 0xcb, 0xa9, 0xcd, 0x06, 0xd3, 0x78, 0xcf, 0x8e, 0x9e, 0xf0,
	0x88, 0xdd, 0x92, 0xac, 0x31, 0x89, 0x93, 0xab, 0xd5, 0xd3, 0xb9, 0x25, 0x49, 0x43, 0x95, 0xdf,
	0x29, 0x73, 0x86, 0xdd, 0x44, 0x50, 0x86, 0x2a, 0xef, 0x40, 0x8c, 0x29, 0x3a, 0xb0, 0x55, 0x90,
	0x30, 0x05, 0xc2, 0x22, 0xac, 0x53, 0xef, 0x2f, 0x35, 0x60, 0x5d, 0x63, 0xaf, 0x97, 0xaa, 0x28,
	0xc1, 0xd2, 0x59, 0x18, 0x66, 0xee, 0x9a, 0xd3, 0xd4, 0x94, 0xa6, 0x2e, 0x6c, 0x2b, 0x35, 0x68,
	0x13, 0xe1, 0xaa, 0x7a, 0xba, 0x4e, 0x08, 0xbd, 0x5e, 0xba, 0xbb, 0x1e, 0x05, 0xbe, 0x17, 0xf0,
	0x87, 0x5e, 0x9c, 0x84, 0xd1, 0x94, 0x6d, 0x48, 0xee, 0x6e, 0x2a, 0xac, 0x39, 0x53, 0x5b, 0x3d,
	0xb5, 0x7e, 0xbd, 0xee, 0xb4, 0xdd, 0x2c, 0xc2, 0xb6, 0x35, 0x2a, 0x7c, 0x53, 0xd7, 0x43, 0x0c,
	0xf9, 0x07, 0xa1, 0x37, 0xca, 0x89, 0x7f, 0x8e, 0x53, 0x6e, 0xc1, 0xb4, 0x61, 0x26, 0x7d, 0x1f,
	0x41, 0x22, 0x76, 0x5a, 0x99, 0x93, 0x7b, 0xdc, 0xf5, 0xec, 0x9d, 0xe0, 0xd3, 0x50, 0x69, 0x40,
	0xb9, 0x8a, 0x33, 0x20, 0x50, 0x0e, 0x18, 0x10, 0xde, 0xa4, 0x62, 0x40, 0xa0, 0x1c, 0x69, 0xc0,
	0x7f, 0x67, 0xc6, 0x1a, 0x50, 0x22, 0xb0, 0x39, 0x59, 0x04, 0xe0, 0x73, 0x52, 0xa2, 0x2a, 0x39,
	0x39, 0xa7, 0xa9, 0x29, 0x4d, 0xe8, 0x9c, 0x2c, 0x82, 0x9a, 0xe5, 0x64, 0x99, 0xae, 0xef, 0x09,
	0x65, 0x99, 0xa9, 0xd2, 0xf4, 0xcc, 0x73, 0xe3, 0xb6, 0x64, 0xac, 0x00, 0x2d, 0x56, 0x54, 0x4f,
	0xa8, 0x5b, 0xaf, 0xb1, 0x9a, 0x1e, 0x95, 0x97, 0xaf, 0x22, 0xdb, 0x6a, 0xa4, 0x78, 0xf9, 0x1f,
	0x08, 0xbd, 0x22, 0xb4, 0xf6, 0xac, 0x6f, 0x7f, 0x17, 0x3e, 0xfa, 0xfa, 0x09, 0xee, 0x23, 0x8e,
	0x1f, 0x9e, 0xe2, 0x67, 0x42, 0x5f, 0x2e, 0x50, 0x30, 0xb2, 0xcf, 0x32, 0x30, 0x1f, 0xe5, 0xae,
	0x1c, 0xe5, 0x7a, 0xa9, 0xc9, 0x4c, 0x5d, 0x75, 0xa0, 0x5e, 0x3d, 0x1d, 0x60, 0x27, 0x8b, 0xb0,
	0x81, 0x86, 0x94, 0x9b, 0x10, 0x5e, 0x0c, 0xf5, 0x2b, 0xa1, 0xaf, 0x14, 0xa9, 0x39, 0xa7, 0xa9,
	0xfa, 0x30, 0x4d, 0xba, 0xc9, 0xb6, 0x11, 0x64, 0x61, 0xa6, 0xfb, 0xb1, 0x25, 0x5c, 0xf3, 0xe1,
	0xf6, 0x83, 0x6c, 0x7d, 0xd7, 0x0b, 0xb8, 0x52, 0x77, 0x33, 0x15, 0xd5, 0x81, 0x6e, 0xcb, 0xac,
	0x43, 0x43, 0xd6, 0xcb, 0x79, 0xd7, 0x64, 0x33, 0x91, 0x79, 0x68, 0x48, 0x17, 0xca, 0xbd, 0x9a,
	0x2e, 0x6c, 0x0d, 0xcc, 0xbe, 0x1a, 0x54, 0x31, 0xff, 0xa0, 0x08, 0x29, 0x83, 0xf0, 0x11, 0x32,
	0x83, 0x14, 0x4c, 0x1e, 0xb7, 0xf2, 0x08, 0x99, 0x53, 0x79, 0x9e, 0x54, 0xa2, 0xf3, 0xb4, 0x0c,
	0x6c, 0x96, 0xa7, 0x2a, 0x4a, 0xff, 0x6e, 0x89, 0x3c, 0x9d, 0x59, 0x1c, 0xf2, 0xc4, 0xf6, 0x7c,
	0x65, 0xf2, 0x28, 0xea, 0x54, 0x17, 0x44, 0xce, 0x6e, 0x43, 0xe0, 0xbd, 0x32, 0xc7, 0xcd, 0x37,
	0x16, 0x4c, 0x37, 0x04, 0xf6, 0x20, 0xbe, 0xc1, 0x5e, 0x67, 0x9e, 0x02, 0x58, 0x07, 0xb1, 0xe9,
	0x37, 0x2c, 0xc4, 0xd5, 0xa3, 0x86, 0xe2, 0xaf, 0x1e, 0x05, 0x5e, 0xf0, 0xff, 0xc5, 0x62, 0x7e,
	0xf5, 0xcc, 0x05, 0x70, 0x31, 0x02, 0x40, 0xdf, 0xd2, 0x6a, 0x78, 0xb3, 0x5b, 0xba, 0x5e, 0x08,
	0x7f, 0xb5, 0xe8, 0xd5, 0x6a, 0x10, 0xec, 0x1f, 0xf9, 0x3c, 0x66, 0x6b, 0x52, 0x06, 0xd7, 0xd4,
	0x9d, 0xd2, 0xb2, 0xea, 0x99, 0xae, 0x49, 0x15, 0x34, 0xc3, 0xf5, 0xcb, 0x22, 0x68, 0xbc, 0xad,
	0xd0, 0x40, 0x33, 0xdc, 0x3a, 0x24, 0x01, 0xa8, 0x15, 0xdb, 0x00, 0x15, 0x00, 0x41, 0xd3, 0xff,
	0x3e, 0xd6, 0x26, 0x80, 0x06, 0xb9, 0xa5, 0x09, 0x80, 0x57, 0x01, 0x78, 0x21, 0xff, 0x15, 0xfe,
	0x9f, 0xf3, 0xfe, 0xff, 0xf3, 0xbe, 0x09, 0x1b, 0x5f, 0x83, 0x7e, 0x17, 0xe1, 0x7b, 0x04, 0xff,
	0x5f, 0x2e, 0xd2, 0x76, 0xd5, 0xf6, 0x5b, 0x81, 0x9b, 0x0d, 0xd8, 0x93, 0x0a, 0x30, 0xd4, 0xbd,
	0xf2, 0x4a, 0x55, 0xa0, 0xe6, 0x22, 0x68, 0x0c, 0xbd, 0x5f, 0xd6, 0xc1, 0x7f, 0xd9, 0x5c, 0x48,
	0xa1, 0x31, 0x74, 0x03, 0x52, 0x83, 0xa6, 0x1b, 0xbb, 0x07, 0x0a, 0x42, 0x83, 0xb6, 0x08, 0xdb,
	0xa8, 0xcf, 0x02, 0x3d, 0x78, 0xa8, 0x89, 0x83, 0xd7, 0xe1, 0x0e, 0x42, 0x11, 0x5f, 0x2f, 0xd2,
	0x6b, 0x8a, 0x44, 0x98, 0x4b, 0xe2, 0x82, 0x24, 0xf1, 0x0e, 0x1c, 0x13, 0xfa, 0x06, 0x0f, 0x11,
	0x49, 0x81, 0x93, 0xc6, 0xef, 0x84, 0xde, 0x28, 0x7c, 0x7c, 0x79, 0x3c, 0xf1, 0x43, 0xdb, 0xdd,
	0x39, 0xb4, 0x0f, 0xe4, 0x27, 0xf4, 0x4d, 0x29, 0x8e, 0xd7, 0x4a, 0x5f, 0x06, 0xaa, 0xc5, 0xaa,
	0x7f, 0xfc, 0xd5, 0xaa, 0x5f, 0xdf, 0xce, 0x22, 0xec, 0x3d, 0x8d, 0x01, 0xde, 0xd2, 0x36, 0x11,
	0x83, 0xfe, 0x49, 0xa8, 0x51, 0xfc, 0x20, 0x73, 0xee, 0x93, 0x0e, 0x60, 0x52, 0x51, 0xd3, 0x7e,
	0x80, 0xe0, 0x15, 0x3b, 0xf1, 0xa0, 0xff, 0xd3, 0x89, 0x41, 0x8e, 0x4f, 0x0c, 0xf2, 0xdb, 0x89,
	0x41, 0xbe, 0x3a, 0x35, 0x16, 0x8e, 0x4f, 0x8d, 0x85, 0x5f, 0x4e, 0x8d, 0x85, 0x4f, 0xde, 0x38,
	0xf0, 0x12, 0xf3, 0x89, 0xe7, 0xd8, 0x6e, 0xaf, 0x67, 0x3a, 0xe1, 0x61, 0xe7, 0xe0, 0x73, 0x97,
	0x3f, 0xed, 0x8c, 0xa7, 0x71, 0xc7, 0x0d, 0x9d, 0x4e, 0xda, 0x7f, 0xbc, 0x94, 0xfe, 0x11, 0x44,
	0xf7, 0x9f, 0x00, 0x00, 0x00, 0xff, 0xff, 0xde, 0xfc, 0xca, 0x3b, 0x49, 0x21, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// RpcDbControllerClient is the client API for RpcDbController service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type RpcDbControllerClient interface {
	//插入一行数据
	Insert(ctx context.Context, in *DbController, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,全量修改一行数据
	Update(ctx context.Context, in *DbController, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
	PartialUpdate(ctx context.Context, in *DbController, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,删除一行数据
	Delete(ctx context.Context, in *DbController, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
	SelectOne(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (*DbController, error)
	//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
	SelectMany(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (RpcDbController_SelectManyClient, error)
	//根据条件查询数据
	Query(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbController_QueryClient, error)
	//根据条件查询数据,批量返回数据,crud.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbController_QueryBatchClient, error)
}

type rpcDbControllerClient struct {
	cc *grpc.ClientConn
}

func NewRpcDbControllerClient(cc *grpc.ClientConn) RpcDbControllerClient {
	return &rpcDbControllerClient{cc}
}

func (c *rpcDbControllerClient) Insert(ctx context.Context, in *DbController, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/bysdb.RpcDbController/Insert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbControllerClient) Update(ctx context.Context, in *DbController, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/bysdb.RpcDbController/Update", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbControllerClient) PartialUpdate(ctx context.Context, in *DbController, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/bysdb.RpcDbController/PartialUpdate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbControllerClient) Delete(ctx context.Context, in *DbController, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/bysdb.RpcDbController/Delete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbControllerClient) SelectOne(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (*DbController, error) {
	out := new(DbController)
	err := c.cc.Invoke(ctx, "/bysdb.RpcDbController/SelectOne", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbControllerClient) SelectMany(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (RpcDbController_SelectManyClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RpcDbController_serviceDesc.Streams[0], "/bysdb.RpcDbController/SelectMany", opts...)
	if err != nil {
		return nil, err
	}
	x := &rpcDbControllerSelectManyClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type RpcDbController_SelectManyClient interface {
	Recv() (*DbController, error)
	grpc.ClientStream
}

type rpcDbControllerSelectManyClient struct {
	grpc.ClientStream
}

func (x *rpcDbControllerSelectManyClient) Recv() (*DbController, error) {
	m := new(DbController)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *rpcDbControllerClient) Query(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbController_QueryClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RpcDbController_serviceDesc.Streams[1], "/bysdb.RpcDbController/Query", opts...)
	if err != nil {
		return nil, err
	}
	x := &rpcDbControllerQueryClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type RpcDbController_QueryClient interface {
	Recv() (*DbController, error)
	grpc.ClientStream
}

type rpcDbControllerQueryClient struct {
	grpc.ClientStream
}

func (x *rpcDbControllerQueryClient) Recv() (*DbController, error) {
	m := new(DbController)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *rpcDbControllerClient) QueryBatch(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbController_QueryBatchClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RpcDbController_serviceDesc.Streams[2], "/bysdb.RpcDbController/QueryBatch", opts...)
	if err != nil {
		return nil, err
	}
	x := &rpcDbControllerQueryBatchClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type RpcDbController_QueryBatchClient interface {
	Recv() (*DbControllerList, error)
	grpc.ClientStream
}

type rpcDbControllerQueryBatchClient struct {
	grpc.ClientStream
}

func (x *rpcDbControllerQueryBatchClient) Recv() (*DbControllerList, error) {
	m := new(DbControllerList)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// RpcDbControllerServer is the server API for RpcDbController service.
type RpcDbControllerServer interface {
	//插入一行数据
	Insert(context.Context, *DbController) (*crud.DMLResult, error)
	//以RID为条件,全量修改一行数据
	Update(context.Context, *DbController) (*crud.DMLResult, error)
	//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
	PartialUpdate(context.Context, *DbController) (*crud.DMLResult, error)
	//以RID为条件,删除一行数据
	Delete(context.Context, *DbController) (*crud.DMLResult, error)
	//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
	SelectOne(context.Context, *crud.DMLParam) (*DbController, error)
	//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
	SelectMany(*crud.DMLParam, RpcDbController_SelectManyServer) error
	//根据条件查询数据
	Query(*crud.QueryParam, RpcDbController_QueryServer) error
	//根据条件查询数据,批量返回数据,crud.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(*crud.QueryParam, RpcDbController_QueryBatchServer) error
}

// UnimplementedRpcDbControllerServer can be embedded to have forward compatible implementations.
type UnimplementedRpcDbControllerServer struct {
}

func (*UnimplementedRpcDbControllerServer) Insert(ctx context.Context, req *DbController) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Insert not implemented")
}
func (*UnimplementedRpcDbControllerServer) Update(ctx context.Context, req *DbController) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Update not implemented")
}
func (*UnimplementedRpcDbControllerServer) PartialUpdate(ctx context.Context, req *DbController) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PartialUpdate not implemented")
}
func (*UnimplementedRpcDbControllerServer) Delete(ctx context.Context, req *DbController) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Delete not implemented")
}
func (*UnimplementedRpcDbControllerServer) SelectOne(ctx context.Context, req *crud.DMLParam) (*DbController, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SelectOne not implemented")
}
func (*UnimplementedRpcDbControllerServer) SelectMany(req *crud.DMLParam, srv RpcDbController_SelectManyServer) error {
	return status.Errorf(codes.Unimplemented, "method SelectMany not implemented")
}
func (*UnimplementedRpcDbControllerServer) Query(req *crud.QueryParam, srv RpcDbController_QueryServer) error {
	return status.Errorf(codes.Unimplemented, "method Query not implemented")
}
func (*UnimplementedRpcDbControllerServer) QueryBatch(req *crud.QueryParam, srv RpcDbController_QueryBatchServer) error {
	return status.Errorf(codes.Unimplemented, "method QueryBatch not implemented")
}

func RegisterRpcDbControllerServer(s *grpc.Server, srv RpcDbControllerServer) {
	s.RegisterService(&_RpcDbController_serviceDesc, srv)
}

func _RpcDbController_Insert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbController)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbControllerServer).Insert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.RpcDbController/Insert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbControllerServer).Insert(ctx, req.(*DbController))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbController_Update_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbController)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbControllerServer).Update(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.RpcDbController/Update",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbControllerServer).Update(ctx, req.(*DbController))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbController_PartialUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbController)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbControllerServer).PartialUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.RpcDbController/PartialUpdate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbControllerServer).PartialUpdate(ctx, req.(*DbController))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbController_Delete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbController)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbControllerServer).Delete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.RpcDbController/Delete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbControllerServer).Delete(ctx, req.(*DbController))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbController_SelectOne_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(crud.DMLParam)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbControllerServer).SelectOne(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.RpcDbController/SelectOne",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbControllerServer).SelectOne(ctx, req.(*crud.DMLParam))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbController_SelectMany_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.DMLParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RpcDbControllerServer).SelectMany(m, &rpcDbControllerSelectManyServer{stream})
}

type RpcDbController_SelectManyServer interface {
	Send(*DbController) error
	grpc.ServerStream
}

type rpcDbControllerSelectManyServer struct {
	grpc.ServerStream
}

func (x *rpcDbControllerSelectManyServer) Send(m *DbController) error {
	return x.ServerStream.SendMsg(m)
}

func _RpcDbController_Query_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.QueryParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RpcDbControllerServer).Query(m, &rpcDbControllerQueryServer{stream})
}

type RpcDbController_QueryServer interface {
	Send(*DbController) error
	grpc.ServerStream
}

type rpcDbControllerQueryServer struct {
	grpc.ServerStream
}

func (x *rpcDbControllerQueryServer) Send(m *DbController) error {
	return x.ServerStream.SendMsg(m)
}

func _RpcDbController_QueryBatch_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.QueryParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RpcDbControllerServer).QueryBatch(m, &rpcDbControllerQueryBatchServer{stream})
}

type RpcDbController_QueryBatchServer interface {
	Send(*DbControllerList) error
	grpc.ServerStream
}

type rpcDbControllerQueryBatchServer struct {
	grpc.ServerStream
}

func (x *rpcDbControllerQueryBatchServer) Send(m *DbControllerList) error {
	return x.ServerStream.SendMsg(m)
}

var _RpcDbController_serviceDesc = grpc.ServiceDesc{
	ServiceName: "bysdb.RpcDbController",
	HandlerType: (*RpcDbControllerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Insert",
			Handler:    _RpcDbController_Insert_Handler,
		},
		{
			MethodName: "Update",
			Handler:    _RpcDbController_Update_Handler,
		},
		{
			MethodName: "PartialUpdate",
			Handler:    _RpcDbController_PartialUpdate_Handler,
		},
		{
			MethodName: "Delete",
			Handler:    _RpcDbController_Delete_Handler,
		},
		{
			MethodName: "SelectOne",
			Handler:    _RpcDbController_SelectOne_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "SelectMany",
			Handler:       _RpcDbController_SelectMany_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "Query",
			Handler:       _RpcDbController_Query_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "QueryBatch",
			Handler:       _RpcDbController_QueryBatch_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "bysdb.rpc.proto",
}

// PrpcDbControllerClient is the client API for PrpcDbController service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type PrpcDbControllerClient interface {
	//插入一行数据
	Insert(ctx context.Context, in *DbController, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,全量修改一行数据
	Update(ctx context.Context, in *DbController, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
	PartialUpdate(ctx context.Context, in *DbController, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,删除一行数据
	Delete(ctx context.Context, in *DbController, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
	SelectOne(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (*DbController, error)
	//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
	SelectMany(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (PrpcDbController_SelectManyClient, error)
	//根据条件取得我有权限的数据
	Query(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbController_QueryClient, error)
	//根据条件取得我有权限的数据,批量返回数据,crud.PrivilegeParam.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbController_QueryBatchClient, error)
}

type prpcDbControllerClient struct {
	cc *grpc.ClientConn
}

func NewPrpcDbControllerClient(cc *grpc.ClientConn) PrpcDbControllerClient {
	return &prpcDbControllerClient{cc}
}

func (c *prpcDbControllerClient) Insert(ctx context.Context, in *DbController, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/bysdb.PrpcDbController/Insert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prpcDbControllerClient) Update(ctx context.Context, in *DbController, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/bysdb.PrpcDbController/Update", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prpcDbControllerClient) PartialUpdate(ctx context.Context, in *DbController, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/bysdb.PrpcDbController/PartialUpdate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prpcDbControllerClient) Delete(ctx context.Context, in *DbController, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/bysdb.PrpcDbController/Delete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prpcDbControllerClient) SelectOne(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (*DbController, error) {
	out := new(DbController)
	err := c.cc.Invoke(ctx, "/bysdb.PrpcDbController/SelectOne", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prpcDbControllerClient) SelectMany(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (PrpcDbController_SelectManyClient, error) {
	stream, err := c.cc.NewStream(ctx, &_PrpcDbController_serviceDesc.Streams[0], "/bysdb.PrpcDbController/SelectMany", opts...)
	if err != nil {
		return nil, err
	}
	x := &prpcDbControllerSelectManyClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type PrpcDbController_SelectManyClient interface {
	Recv() (*DbController, error)
	grpc.ClientStream
}

type prpcDbControllerSelectManyClient struct {
	grpc.ClientStream
}

func (x *prpcDbControllerSelectManyClient) Recv() (*DbController, error) {
	m := new(DbController)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *prpcDbControllerClient) Query(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbController_QueryClient, error) {
	stream, err := c.cc.NewStream(ctx, &_PrpcDbController_serviceDesc.Streams[1], "/bysdb.PrpcDbController/Query", opts...)
	if err != nil {
		return nil, err
	}
	x := &prpcDbControllerQueryClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type PrpcDbController_QueryClient interface {
	Recv() (*DbController, error)
	grpc.ClientStream
}

type prpcDbControllerQueryClient struct {
	grpc.ClientStream
}

func (x *prpcDbControllerQueryClient) Recv() (*DbController, error) {
	m := new(DbController)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *prpcDbControllerClient) QueryBatch(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbController_QueryBatchClient, error) {
	stream, err := c.cc.NewStream(ctx, &_PrpcDbController_serviceDesc.Streams[2], "/bysdb.PrpcDbController/QueryBatch", opts...)
	if err != nil {
		return nil, err
	}
	x := &prpcDbControllerQueryBatchClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type PrpcDbController_QueryBatchClient interface {
	Recv() (*DbControllerList, error)
	grpc.ClientStream
}

type prpcDbControllerQueryBatchClient struct {
	grpc.ClientStream
}

func (x *prpcDbControllerQueryBatchClient) Recv() (*DbControllerList, error) {
	m := new(DbControllerList)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// PrpcDbControllerServer is the server API for PrpcDbController service.
type PrpcDbControllerServer interface {
	//插入一行数据
	Insert(context.Context, *DbController) (*crud.DMLResult, error)
	//以RID为条件,全量修改一行数据
	Update(context.Context, *DbController) (*crud.DMLResult, error)
	//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
	PartialUpdate(context.Context, *DbController) (*crud.DMLResult, error)
	//以RID为条件,删除一行数据
	Delete(context.Context, *DbController) (*crud.DMLResult, error)
	//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
	SelectOne(context.Context, *crud.DMLParam) (*DbController, error)
	//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
	SelectMany(*crud.DMLParam, PrpcDbController_SelectManyServer) error
	//根据条件取得我有权限的数据
	Query(*crud.PrivilegeParam, PrpcDbController_QueryServer) error
	//根据条件取得我有权限的数据,批量返回数据,crud.PrivilegeParam.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(*crud.PrivilegeParam, PrpcDbController_QueryBatchServer) error
}

// UnimplementedPrpcDbControllerServer can be embedded to have forward compatible implementations.
type UnimplementedPrpcDbControllerServer struct {
}

func (*UnimplementedPrpcDbControllerServer) Insert(ctx context.Context, req *DbController) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Insert not implemented")
}
func (*UnimplementedPrpcDbControllerServer) Update(ctx context.Context, req *DbController) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Update not implemented")
}
func (*UnimplementedPrpcDbControllerServer) PartialUpdate(ctx context.Context, req *DbController) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PartialUpdate not implemented")
}
func (*UnimplementedPrpcDbControllerServer) Delete(ctx context.Context, req *DbController) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Delete not implemented")
}
func (*UnimplementedPrpcDbControllerServer) SelectOne(ctx context.Context, req *crud.DMLParam) (*DbController, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SelectOne not implemented")
}
func (*UnimplementedPrpcDbControllerServer) SelectMany(req *crud.DMLParam, srv PrpcDbController_SelectManyServer) error {
	return status.Errorf(codes.Unimplemented, "method SelectMany not implemented")
}
func (*UnimplementedPrpcDbControllerServer) Query(req *crud.PrivilegeParam, srv PrpcDbController_QueryServer) error {
	return status.Errorf(codes.Unimplemented, "method Query not implemented")
}
func (*UnimplementedPrpcDbControllerServer) QueryBatch(req *crud.PrivilegeParam, srv PrpcDbController_QueryBatchServer) error {
	return status.Errorf(codes.Unimplemented, "method QueryBatch not implemented")
}

func RegisterPrpcDbControllerServer(s *grpc.Server, srv PrpcDbControllerServer) {
	s.RegisterService(&_PrpcDbController_serviceDesc, srv)
}

func _PrpcDbController_Insert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbController)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrpcDbControllerServer).Insert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.PrpcDbController/Insert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrpcDbControllerServer).Insert(ctx, req.(*DbController))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrpcDbController_Update_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbController)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrpcDbControllerServer).Update(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.PrpcDbController/Update",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrpcDbControllerServer).Update(ctx, req.(*DbController))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrpcDbController_PartialUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbController)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrpcDbControllerServer).PartialUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.PrpcDbController/PartialUpdate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrpcDbControllerServer).PartialUpdate(ctx, req.(*DbController))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrpcDbController_Delete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbController)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrpcDbControllerServer).Delete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.PrpcDbController/Delete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrpcDbControllerServer).Delete(ctx, req.(*DbController))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrpcDbController_SelectOne_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(crud.DMLParam)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrpcDbControllerServer).SelectOne(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.PrpcDbController/SelectOne",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrpcDbControllerServer).SelectOne(ctx, req.(*crud.DMLParam))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrpcDbController_SelectMany_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.DMLParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(PrpcDbControllerServer).SelectMany(m, &prpcDbControllerSelectManyServer{stream})
}

type PrpcDbController_SelectManyServer interface {
	Send(*DbController) error
	grpc.ServerStream
}

type prpcDbControllerSelectManyServer struct {
	grpc.ServerStream
}

func (x *prpcDbControllerSelectManyServer) Send(m *DbController) error {
	return x.ServerStream.SendMsg(m)
}

func _PrpcDbController_Query_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.PrivilegeParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(PrpcDbControllerServer).Query(m, &prpcDbControllerQueryServer{stream})
}

type PrpcDbController_QueryServer interface {
	Send(*DbController) error
	grpc.ServerStream
}

type prpcDbControllerQueryServer struct {
	grpc.ServerStream
}

func (x *prpcDbControllerQueryServer) Send(m *DbController) error {
	return x.ServerStream.SendMsg(m)
}

func _PrpcDbController_QueryBatch_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.PrivilegeParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(PrpcDbControllerServer).QueryBatch(m, &prpcDbControllerQueryBatchServer{stream})
}

type PrpcDbController_QueryBatchServer interface {
	Send(*DbControllerList) error
	grpc.ServerStream
}

type prpcDbControllerQueryBatchServer struct {
	grpc.ServerStream
}

func (x *prpcDbControllerQueryBatchServer) Send(m *DbControllerList) error {
	return x.ServerStream.SendMsg(m)
}

var _PrpcDbController_serviceDesc = grpc.ServiceDesc{
	ServiceName: "bysdb.PrpcDbController",
	HandlerType: (*PrpcDbControllerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Insert",
			Handler:    _PrpcDbController_Insert_Handler,
		},
		{
			MethodName: "Update",
			Handler:    _PrpcDbController_Update_Handler,
		},
		{
			MethodName: "PartialUpdate",
			Handler:    _PrpcDbController_PartialUpdate_Handler,
		},
		{
			MethodName: "Delete",
			Handler:    _PrpcDbController_Delete_Handler,
		},
		{
			MethodName: "SelectOne",
			Handler:    _PrpcDbController_SelectOne_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "SelectMany",
			Handler:       _PrpcDbController_SelectMany_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "Query",
			Handler:       _PrpcDbController_Query_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "QueryBatch",
			Handler:       _PrpcDbController_QueryBatch_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "bysdb.rpc.proto",
}

// RpcDbBysMarkerClient is the client API for RpcDbBysMarker service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type RpcDbBysMarkerClient interface {
	//插入一行数据
	Insert(ctx context.Context, in *DbBysMarker, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,全量修改一行数据
	Update(ctx context.Context, in *DbBysMarker, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
	PartialUpdate(ctx context.Context, in *DbBysMarker, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,删除一行数据
	Delete(ctx context.Context, in *DbBysMarker, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
	SelectOne(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (*DbBysMarker, error)
	//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
	SelectMany(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (RpcDbBysMarker_SelectManyClient, error)
	//根据条件查询数据
	Query(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbBysMarker_QueryClient, error)
	//根据条件查询数据,批量返回数据,crud.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbBysMarker_QueryBatchClient, error)
}

type rpcDbBysMarkerClient struct {
	cc *grpc.ClientConn
}

func NewRpcDbBysMarkerClient(cc *grpc.ClientConn) RpcDbBysMarkerClient {
	return &rpcDbBysMarkerClient{cc}
}

func (c *rpcDbBysMarkerClient) Insert(ctx context.Context, in *DbBysMarker, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/bysdb.RpcDbBysMarker/Insert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbBysMarkerClient) Update(ctx context.Context, in *DbBysMarker, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/bysdb.RpcDbBysMarker/Update", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbBysMarkerClient) PartialUpdate(ctx context.Context, in *DbBysMarker, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/bysdb.RpcDbBysMarker/PartialUpdate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbBysMarkerClient) Delete(ctx context.Context, in *DbBysMarker, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/bysdb.RpcDbBysMarker/Delete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbBysMarkerClient) SelectOne(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (*DbBysMarker, error) {
	out := new(DbBysMarker)
	err := c.cc.Invoke(ctx, "/bysdb.RpcDbBysMarker/SelectOne", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbBysMarkerClient) SelectMany(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (RpcDbBysMarker_SelectManyClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RpcDbBysMarker_serviceDesc.Streams[0], "/bysdb.RpcDbBysMarker/SelectMany", opts...)
	if err != nil {
		return nil, err
	}
	x := &rpcDbBysMarkerSelectManyClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type RpcDbBysMarker_SelectManyClient interface {
	Recv() (*DbBysMarker, error)
	grpc.ClientStream
}

type rpcDbBysMarkerSelectManyClient struct {
	grpc.ClientStream
}

func (x *rpcDbBysMarkerSelectManyClient) Recv() (*DbBysMarker, error) {
	m := new(DbBysMarker)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *rpcDbBysMarkerClient) Query(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbBysMarker_QueryClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RpcDbBysMarker_serviceDesc.Streams[1], "/bysdb.RpcDbBysMarker/Query", opts...)
	if err != nil {
		return nil, err
	}
	x := &rpcDbBysMarkerQueryClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type RpcDbBysMarker_QueryClient interface {
	Recv() (*DbBysMarker, error)
	grpc.ClientStream
}

type rpcDbBysMarkerQueryClient struct {
	grpc.ClientStream
}

func (x *rpcDbBysMarkerQueryClient) Recv() (*DbBysMarker, error) {
	m := new(DbBysMarker)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *rpcDbBysMarkerClient) QueryBatch(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbBysMarker_QueryBatchClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RpcDbBysMarker_serviceDesc.Streams[2], "/bysdb.RpcDbBysMarker/QueryBatch", opts...)
	if err != nil {
		return nil, err
	}
	x := &rpcDbBysMarkerQueryBatchClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type RpcDbBysMarker_QueryBatchClient interface {
	Recv() (*DbBysMarkerList, error)
	grpc.ClientStream
}

type rpcDbBysMarkerQueryBatchClient struct {
	grpc.ClientStream
}

func (x *rpcDbBysMarkerQueryBatchClient) Recv() (*DbBysMarkerList, error) {
	m := new(DbBysMarkerList)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// RpcDbBysMarkerServer is the server API for RpcDbBysMarker service.
type RpcDbBysMarkerServer interface {
	//插入一行数据
	Insert(context.Context, *DbBysMarker) (*crud.DMLResult, error)
	//以RID为条件,全量修改一行数据
	Update(context.Context, *DbBysMarker) (*crud.DMLResult, error)
	//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
	PartialUpdate(context.Context, *DbBysMarker) (*crud.DMLResult, error)
	//以RID为条件,删除一行数据
	Delete(context.Context, *DbBysMarker) (*crud.DMLResult, error)
	//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
	SelectOne(context.Context, *crud.DMLParam) (*DbBysMarker, error)
	//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
	SelectMany(*crud.DMLParam, RpcDbBysMarker_SelectManyServer) error
	//根据条件查询数据
	Query(*crud.QueryParam, RpcDbBysMarker_QueryServer) error
	//根据条件查询数据,批量返回数据,crud.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(*crud.QueryParam, RpcDbBysMarker_QueryBatchServer) error
}

// UnimplementedRpcDbBysMarkerServer can be embedded to have forward compatible implementations.
type UnimplementedRpcDbBysMarkerServer struct {
}

func (*UnimplementedRpcDbBysMarkerServer) Insert(ctx context.Context, req *DbBysMarker) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Insert not implemented")
}
func (*UnimplementedRpcDbBysMarkerServer) Update(ctx context.Context, req *DbBysMarker) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Update not implemented")
}
func (*UnimplementedRpcDbBysMarkerServer) PartialUpdate(ctx context.Context, req *DbBysMarker) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PartialUpdate not implemented")
}
func (*UnimplementedRpcDbBysMarkerServer) Delete(ctx context.Context, req *DbBysMarker) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Delete not implemented")
}
func (*UnimplementedRpcDbBysMarkerServer) SelectOne(ctx context.Context, req *crud.DMLParam) (*DbBysMarker, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SelectOne not implemented")
}
func (*UnimplementedRpcDbBysMarkerServer) SelectMany(req *crud.DMLParam, srv RpcDbBysMarker_SelectManyServer) error {
	return status.Errorf(codes.Unimplemented, "method SelectMany not implemented")
}
func (*UnimplementedRpcDbBysMarkerServer) Query(req *crud.QueryParam, srv RpcDbBysMarker_QueryServer) error {
	return status.Errorf(codes.Unimplemented, "method Query not implemented")
}
func (*UnimplementedRpcDbBysMarkerServer) QueryBatch(req *crud.QueryParam, srv RpcDbBysMarker_QueryBatchServer) error {
	return status.Errorf(codes.Unimplemented, "method QueryBatch not implemented")
}

func RegisterRpcDbBysMarkerServer(s *grpc.Server, srv RpcDbBysMarkerServer) {
	s.RegisterService(&_RpcDbBysMarker_serviceDesc, srv)
}

func _RpcDbBysMarker_Insert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbBysMarker)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbBysMarkerServer).Insert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.RpcDbBysMarker/Insert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbBysMarkerServer).Insert(ctx, req.(*DbBysMarker))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbBysMarker_Update_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbBysMarker)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbBysMarkerServer).Update(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.RpcDbBysMarker/Update",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbBysMarkerServer).Update(ctx, req.(*DbBysMarker))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbBysMarker_PartialUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbBysMarker)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbBysMarkerServer).PartialUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.RpcDbBysMarker/PartialUpdate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbBysMarkerServer).PartialUpdate(ctx, req.(*DbBysMarker))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbBysMarker_Delete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbBysMarker)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbBysMarkerServer).Delete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.RpcDbBysMarker/Delete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbBysMarkerServer).Delete(ctx, req.(*DbBysMarker))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbBysMarker_SelectOne_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(crud.DMLParam)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbBysMarkerServer).SelectOne(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.RpcDbBysMarker/SelectOne",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbBysMarkerServer).SelectOne(ctx, req.(*crud.DMLParam))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbBysMarker_SelectMany_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.DMLParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RpcDbBysMarkerServer).SelectMany(m, &rpcDbBysMarkerSelectManyServer{stream})
}

type RpcDbBysMarker_SelectManyServer interface {
	Send(*DbBysMarker) error
	grpc.ServerStream
}

type rpcDbBysMarkerSelectManyServer struct {
	grpc.ServerStream
}

func (x *rpcDbBysMarkerSelectManyServer) Send(m *DbBysMarker) error {
	return x.ServerStream.SendMsg(m)
}

func _RpcDbBysMarker_Query_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.QueryParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RpcDbBysMarkerServer).Query(m, &rpcDbBysMarkerQueryServer{stream})
}

type RpcDbBysMarker_QueryServer interface {
	Send(*DbBysMarker) error
	grpc.ServerStream
}

type rpcDbBysMarkerQueryServer struct {
	grpc.ServerStream
}

func (x *rpcDbBysMarkerQueryServer) Send(m *DbBysMarker) error {
	return x.ServerStream.SendMsg(m)
}

func _RpcDbBysMarker_QueryBatch_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.QueryParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RpcDbBysMarkerServer).QueryBatch(m, &rpcDbBysMarkerQueryBatchServer{stream})
}

type RpcDbBysMarker_QueryBatchServer interface {
	Send(*DbBysMarkerList) error
	grpc.ServerStream
}

type rpcDbBysMarkerQueryBatchServer struct {
	grpc.ServerStream
}

func (x *rpcDbBysMarkerQueryBatchServer) Send(m *DbBysMarkerList) error {
	return x.ServerStream.SendMsg(m)
}

var _RpcDbBysMarker_serviceDesc = grpc.ServiceDesc{
	ServiceName: "bysdb.RpcDbBysMarker",
	HandlerType: (*RpcDbBysMarkerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Insert",
			Handler:    _RpcDbBysMarker_Insert_Handler,
		},
		{
			MethodName: "Update",
			Handler:    _RpcDbBysMarker_Update_Handler,
		},
		{
			MethodName: "PartialUpdate",
			Handler:    _RpcDbBysMarker_PartialUpdate_Handler,
		},
		{
			MethodName: "Delete",
			Handler:    _RpcDbBysMarker_Delete_Handler,
		},
		{
			MethodName: "SelectOne",
			Handler:    _RpcDbBysMarker_SelectOne_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "SelectMany",
			Handler:       _RpcDbBysMarker_SelectMany_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "Query",
			Handler:       _RpcDbBysMarker_Query_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "QueryBatch",
			Handler:       _RpcDbBysMarker_QueryBatch_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "bysdb.rpc.proto",
}

// PrpcDbBysMarkerClient is the client API for PrpcDbBysMarker service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type PrpcDbBysMarkerClient interface {
	//插入一行数据
	Insert(ctx context.Context, in *DbBysMarker, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,全量修改一行数据
	Update(ctx context.Context, in *DbBysMarker, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
	PartialUpdate(ctx context.Context, in *DbBysMarker, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,删除一行数据
	Delete(ctx context.Context, in *DbBysMarker, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
	SelectOne(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (*DbBysMarker, error)
	//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
	SelectMany(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (PrpcDbBysMarker_SelectManyClient, error)
	//根据条件取得我有权限的数据
	Query(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbBysMarker_QueryClient, error)
	//根据条件取得我有权限的数据,批量返回数据,crud.PrivilegeParam.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbBysMarker_QueryBatchClient, error)
}

type prpcDbBysMarkerClient struct {
	cc *grpc.ClientConn
}

func NewPrpcDbBysMarkerClient(cc *grpc.ClientConn) PrpcDbBysMarkerClient {
	return &prpcDbBysMarkerClient{cc}
}

func (c *prpcDbBysMarkerClient) Insert(ctx context.Context, in *DbBysMarker, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/bysdb.PrpcDbBysMarker/Insert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prpcDbBysMarkerClient) Update(ctx context.Context, in *DbBysMarker, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/bysdb.PrpcDbBysMarker/Update", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prpcDbBysMarkerClient) PartialUpdate(ctx context.Context, in *DbBysMarker, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/bysdb.PrpcDbBysMarker/PartialUpdate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prpcDbBysMarkerClient) Delete(ctx context.Context, in *DbBysMarker, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/bysdb.PrpcDbBysMarker/Delete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prpcDbBysMarkerClient) SelectOne(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (*DbBysMarker, error) {
	out := new(DbBysMarker)
	err := c.cc.Invoke(ctx, "/bysdb.PrpcDbBysMarker/SelectOne", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prpcDbBysMarkerClient) SelectMany(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (PrpcDbBysMarker_SelectManyClient, error) {
	stream, err := c.cc.NewStream(ctx, &_PrpcDbBysMarker_serviceDesc.Streams[0], "/bysdb.PrpcDbBysMarker/SelectMany", opts...)
	if err != nil {
		return nil, err
	}
	x := &prpcDbBysMarkerSelectManyClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type PrpcDbBysMarker_SelectManyClient interface {
	Recv() (*DbBysMarker, error)
	grpc.ClientStream
}

type prpcDbBysMarkerSelectManyClient struct {
	grpc.ClientStream
}

func (x *prpcDbBysMarkerSelectManyClient) Recv() (*DbBysMarker, error) {
	m := new(DbBysMarker)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *prpcDbBysMarkerClient) Query(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbBysMarker_QueryClient, error) {
	stream, err := c.cc.NewStream(ctx, &_PrpcDbBysMarker_serviceDesc.Streams[1], "/bysdb.PrpcDbBysMarker/Query", opts...)
	if err != nil {
		return nil, err
	}
	x := &prpcDbBysMarkerQueryClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type PrpcDbBysMarker_QueryClient interface {
	Recv() (*DbBysMarker, error)
	grpc.ClientStream
}

type prpcDbBysMarkerQueryClient struct {
	grpc.ClientStream
}

func (x *prpcDbBysMarkerQueryClient) Recv() (*DbBysMarker, error) {
	m := new(DbBysMarker)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *prpcDbBysMarkerClient) QueryBatch(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbBysMarker_QueryBatchClient, error) {
	stream, err := c.cc.NewStream(ctx, &_PrpcDbBysMarker_serviceDesc.Streams[2], "/bysdb.PrpcDbBysMarker/QueryBatch", opts...)
	if err != nil {
		return nil, err
	}
	x := &prpcDbBysMarkerQueryBatchClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type PrpcDbBysMarker_QueryBatchClient interface {
	Recv() (*DbBysMarkerList, error)
	grpc.ClientStream
}

type prpcDbBysMarkerQueryBatchClient struct {
	grpc.ClientStream
}

func (x *prpcDbBysMarkerQueryBatchClient) Recv() (*DbBysMarkerList, error) {
	m := new(DbBysMarkerList)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// PrpcDbBysMarkerServer is the server API for PrpcDbBysMarker service.
type PrpcDbBysMarkerServer interface {
	//插入一行数据
	Insert(context.Context, *DbBysMarker) (*crud.DMLResult, error)
	//以RID为条件,全量修改一行数据
	Update(context.Context, *DbBysMarker) (*crud.DMLResult, error)
	//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
	PartialUpdate(context.Context, *DbBysMarker) (*crud.DMLResult, error)
	//以RID为条件,删除一行数据
	Delete(context.Context, *DbBysMarker) (*crud.DMLResult, error)
	//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
	SelectOne(context.Context, *crud.DMLParam) (*DbBysMarker, error)
	//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
	SelectMany(*crud.DMLParam, PrpcDbBysMarker_SelectManyServer) error
	//根据条件取得我有权限的数据
	Query(*crud.PrivilegeParam, PrpcDbBysMarker_QueryServer) error
	//根据条件取得我有权限的数据,批量返回数据,crud.PrivilegeParam.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(*crud.PrivilegeParam, PrpcDbBysMarker_QueryBatchServer) error
}

// UnimplementedPrpcDbBysMarkerServer can be embedded to have forward compatible implementations.
type UnimplementedPrpcDbBysMarkerServer struct {
}

func (*UnimplementedPrpcDbBysMarkerServer) Insert(ctx context.Context, req *DbBysMarker) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Insert not implemented")
}
func (*UnimplementedPrpcDbBysMarkerServer) Update(ctx context.Context, req *DbBysMarker) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Update not implemented")
}
func (*UnimplementedPrpcDbBysMarkerServer) PartialUpdate(ctx context.Context, req *DbBysMarker) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PartialUpdate not implemented")
}
func (*UnimplementedPrpcDbBysMarkerServer) Delete(ctx context.Context, req *DbBysMarker) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Delete not implemented")
}
func (*UnimplementedPrpcDbBysMarkerServer) SelectOne(ctx context.Context, req *crud.DMLParam) (*DbBysMarker, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SelectOne not implemented")
}
func (*UnimplementedPrpcDbBysMarkerServer) SelectMany(req *crud.DMLParam, srv PrpcDbBysMarker_SelectManyServer) error {
	return status.Errorf(codes.Unimplemented, "method SelectMany not implemented")
}
func (*UnimplementedPrpcDbBysMarkerServer) Query(req *crud.PrivilegeParam, srv PrpcDbBysMarker_QueryServer) error {
	return status.Errorf(codes.Unimplemented, "method Query not implemented")
}
func (*UnimplementedPrpcDbBysMarkerServer) QueryBatch(req *crud.PrivilegeParam, srv PrpcDbBysMarker_QueryBatchServer) error {
	return status.Errorf(codes.Unimplemented, "method QueryBatch not implemented")
}

func RegisterPrpcDbBysMarkerServer(s *grpc.Server, srv PrpcDbBysMarkerServer) {
	s.RegisterService(&_PrpcDbBysMarker_serviceDesc, srv)
}

func _PrpcDbBysMarker_Insert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbBysMarker)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrpcDbBysMarkerServer).Insert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.PrpcDbBysMarker/Insert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrpcDbBysMarkerServer).Insert(ctx, req.(*DbBysMarker))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrpcDbBysMarker_Update_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbBysMarker)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrpcDbBysMarkerServer).Update(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.PrpcDbBysMarker/Update",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrpcDbBysMarkerServer).Update(ctx, req.(*DbBysMarker))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrpcDbBysMarker_PartialUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbBysMarker)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrpcDbBysMarkerServer).PartialUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.PrpcDbBysMarker/PartialUpdate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrpcDbBysMarkerServer).PartialUpdate(ctx, req.(*DbBysMarker))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrpcDbBysMarker_Delete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbBysMarker)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrpcDbBysMarkerServer).Delete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.PrpcDbBysMarker/Delete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrpcDbBysMarkerServer).Delete(ctx, req.(*DbBysMarker))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrpcDbBysMarker_SelectOne_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(crud.DMLParam)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrpcDbBysMarkerServer).SelectOne(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.PrpcDbBysMarker/SelectOne",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrpcDbBysMarkerServer).SelectOne(ctx, req.(*crud.DMLParam))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrpcDbBysMarker_SelectMany_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.DMLParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(PrpcDbBysMarkerServer).SelectMany(m, &prpcDbBysMarkerSelectManyServer{stream})
}

type PrpcDbBysMarker_SelectManyServer interface {
	Send(*DbBysMarker) error
	grpc.ServerStream
}

type prpcDbBysMarkerSelectManyServer struct {
	grpc.ServerStream
}

func (x *prpcDbBysMarkerSelectManyServer) Send(m *DbBysMarker) error {
	return x.ServerStream.SendMsg(m)
}

func _PrpcDbBysMarker_Query_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.PrivilegeParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(PrpcDbBysMarkerServer).Query(m, &prpcDbBysMarkerQueryServer{stream})
}

type PrpcDbBysMarker_QueryServer interface {
	Send(*DbBysMarker) error
	grpc.ServerStream
}

type prpcDbBysMarkerQueryServer struct {
	grpc.ServerStream
}

func (x *prpcDbBysMarkerQueryServer) Send(m *DbBysMarker) error {
	return x.ServerStream.SendMsg(m)
}

func _PrpcDbBysMarker_QueryBatch_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.PrivilegeParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(PrpcDbBysMarkerServer).QueryBatch(m, &prpcDbBysMarkerQueryBatchServer{stream})
}

type PrpcDbBysMarker_QueryBatchServer interface {
	Send(*DbBysMarkerList) error
	grpc.ServerStream
}

type prpcDbBysMarkerQueryBatchServer struct {
	grpc.ServerStream
}

func (x *prpcDbBysMarkerQueryBatchServer) Send(m *DbBysMarkerList) error {
	return x.ServerStream.SendMsg(m)
}

var _PrpcDbBysMarker_serviceDesc = grpc.ServiceDesc{
	ServiceName: "bysdb.PrpcDbBysMarker",
	HandlerType: (*PrpcDbBysMarkerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Insert",
			Handler:    _PrpcDbBysMarker_Insert_Handler,
		},
		{
			MethodName: "Update",
			Handler:    _PrpcDbBysMarker_Update_Handler,
		},
		{
			MethodName: "PartialUpdate",
			Handler:    _PrpcDbBysMarker_PartialUpdate_Handler,
		},
		{
			MethodName: "Delete",
			Handler:    _PrpcDbBysMarker_Delete_Handler,
		},
		{
			MethodName: "SelectOne",
			Handler:    _PrpcDbBysMarker_SelectOne_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "SelectMany",
			Handler:       _PrpcDbBysMarker_SelectMany_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "Query",
			Handler:       _PrpcDbBysMarker_Query_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "QueryBatch",
			Handler:       _PrpcDbBysMarker_QueryBatch_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "bysdb.rpc.proto",
}

// RpcDbControllerOnlineHistoryClient is the client API for RpcDbControllerOnlineHistory service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type RpcDbControllerOnlineHistoryClient interface {
	//插入一行数据
	Insert(ctx context.Context, in *DbControllerOnlineHistory, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//根据条件查询数据
	Query(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbControllerOnlineHistory_QueryClient, error)
	//根据条件查询数据,批量返回数据,crud.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbControllerOnlineHistory_QueryBatchClient, error)
}

type rpcDbControllerOnlineHistoryClient struct {
	cc *grpc.ClientConn
}

func NewRpcDbControllerOnlineHistoryClient(cc *grpc.ClientConn) RpcDbControllerOnlineHistoryClient {
	return &rpcDbControllerOnlineHistoryClient{cc}
}

func (c *rpcDbControllerOnlineHistoryClient) Insert(ctx context.Context, in *DbControllerOnlineHistory, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/bysdb.RpcDbControllerOnlineHistory/Insert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbControllerOnlineHistoryClient) Query(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbControllerOnlineHistory_QueryClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RpcDbControllerOnlineHistory_serviceDesc.Streams[0], "/bysdb.RpcDbControllerOnlineHistory/Query", opts...)
	if err != nil {
		return nil, err
	}
	x := &rpcDbControllerOnlineHistoryQueryClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type RpcDbControllerOnlineHistory_QueryClient interface {
	Recv() (*DbControllerOnlineHistory, error)
	grpc.ClientStream
}

type rpcDbControllerOnlineHistoryQueryClient struct {
	grpc.ClientStream
}

func (x *rpcDbControllerOnlineHistoryQueryClient) Recv() (*DbControllerOnlineHistory, error) {
	m := new(DbControllerOnlineHistory)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *rpcDbControllerOnlineHistoryClient) QueryBatch(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbControllerOnlineHistory_QueryBatchClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RpcDbControllerOnlineHistory_serviceDesc.Streams[1], "/bysdb.RpcDbControllerOnlineHistory/QueryBatch", opts...)
	if err != nil {
		return nil, err
	}
	x := &rpcDbControllerOnlineHistoryQueryBatchClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type RpcDbControllerOnlineHistory_QueryBatchClient interface {
	Recv() (*DbControllerOnlineHistoryList, error)
	grpc.ClientStream
}

type rpcDbControllerOnlineHistoryQueryBatchClient struct {
	grpc.ClientStream
}

func (x *rpcDbControllerOnlineHistoryQueryBatchClient) Recv() (*DbControllerOnlineHistoryList, error) {
	m := new(DbControllerOnlineHistoryList)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// RpcDbControllerOnlineHistoryServer is the server API for RpcDbControllerOnlineHistory service.
type RpcDbControllerOnlineHistoryServer interface {
	//插入一行数据
	Insert(context.Context, *DbControllerOnlineHistory) (*crud.DMLResult, error)
	//根据条件查询数据
	Query(*crud.QueryParam, RpcDbControllerOnlineHistory_QueryServer) error
	//根据条件查询数据,批量返回数据,crud.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(*crud.QueryParam, RpcDbControllerOnlineHistory_QueryBatchServer) error
}

// UnimplementedRpcDbControllerOnlineHistoryServer can be embedded to have forward compatible implementations.
type UnimplementedRpcDbControllerOnlineHistoryServer struct {
}

func (*UnimplementedRpcDbControllerOnlineHistoryServer) Insert(ctx context.Context, req *DbControllerOnlineHistory) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Insert not implemented")
}
func (*UnimplementedRpcDbControllerOnlineHistoryServer) Query(req *crud.QueryParam, srv RpcDbControllerOnlineHistory_QueryServer) error {
	return status.Errorf(codes.Unimplemented, "method Query not implemented")
}
func (*UnimplementedRpcDbControllerOnlineHistoryServer) QueryBatch(req *crud.QueryParam, srv RpcDbControllerOnlineHistory_QueryBatchServer) error {
	return status.Errorf(codes.Unimplemented, "method QueryBatch not implemented")
}

func RegisterRpcDbControllerOnlineHistoryServer(s *grpc.Server, srv RpcDbControllerOnlineHistoryServer) {
	s.RegisterService(&_RpcDbControllerOnlineHistory_serviceDesc, srv)
}

func _RpcDbControllerOnlineHistory_Insert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbControllerOnlineHistory)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbControllerOnlineHistoryServer).Insert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.RpcDbControllerOnlineHistory/Insert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbControllerOnlineHistoryServer).Insert(ctx, req.(*DbControllerOnlineHistory))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbControllerOnlineHistory_Query_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.QueryParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RpcDbControllerOnlineHistoryServer).Query(m, &rpcDbControllerOnlineHistoryQueryServer{stream})
}

type RpcDbControllerOnlineHistory_QueryServer interface {
	Send(*DbControllerOnlineHistory) error
	grpc.ServerStream
}

type rpcDbControllerOnlineHistoryQueryServer struct {
	grpc.ServerStream
}

func (x *rpcDbControllerOnlineHistoryQueryServer) Send(m *DbControllerOnlineHistory) error {
	return x.ServerStream.SendMsg(m)
}

func _RpcDbControllerOnlineHistory_QueryBatch_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.QueryParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RpcDbControllerOnlineHistoryServer).QueryBatch(m, &rpcDbControllerOnlineHistoryQueryBatchServer{stream})
}

type RpcDbControllerOnlineHistory_QueryBatchServer interface {
	Send(*DbControllerOnlineHistoryList) error
	grpc.ServerStream
}

type rpcDbControllerOnlineHistoryQueryBatchServer struct {
	grpc.ServerStream
}

func (x *rpcDbControllerOnlineHistoryQueryBatchServer) Send(m *DbControllerOnlineHistoryList) error {
	return x.ServerStream.SendMsg(m)
}

var _RpcDbControllerOnlineHistory_serviceDesc = grpc.ServiceDesc{
	ServiceName: "bysdb.RpcDbControllerOnlineHistory",
	HandlerType: (*RpcDbControllerOnlineHistoryServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Insert",
			Handler:    _RpcDbControllerOnlineHistory_Insert_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "Query",
			Handler:       _RpcDbControllerOnlineHistory_Query_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "QueryBatch",
			Handler:       _RpcDbControllerOnlineHistory_QueryBatch_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "bysdb.rpc.proto",
}

// PrpcDbControllerOnlineHistoryClient is the client API for PrpcDbControllerOnlineHistory service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type PrpcDbControllerOnlineHistoryClient interface {
	//插入一行数据
	Insert(ctx context.Context, in *DbControllerOnlineHistory, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//根据条件取得我有权限的数据
	Query(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbControllerOnlineHistory_QueryClient, error)
	//根据条件取得我有权限的数据,批量返回数据,crud.PrivilegeParam.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbControllerOnlineHistory_QueryBatchClient, error)
}

type prpcDbControllerOnlineHistoryClient struct {
	cc *grpc.ClientConn
}

func NewPrpcDbControllerOnlineHistoryClient(cc *grpc.ClientConn) PrpcDbControllerOnlineHistoryClient {
	return &prpcDbControllerOnlineHistoryClient{cc}
}

func (c *prpcDbControllerOnlineHistoryClient) Insert(ctx context.Context, in *DbControllerOnlineHistory, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/bysdb.PrpcDbControllerOnlineHistory/Insert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prpcDbControllerOnlineHistoryClient) Query(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbControllerOnlineHistory_QueryClient, error) {
	stream, err := c.cc.NewStream(ctx, &_PrpcDbControllerOnlineHistory_serviceDesc.Streams[0], "/bysdb.PrpcDbControllerOnlineHistory/Query", opts...)
	if err != nil {
		return nil, err
	}
	x := &prpcDbControllerOnlineHistoryQueryClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type PrpcDbControllerOnlineHistory_QueryClient interface {
	Recv() (*DbControllerOnlineHistory, error)
	grpc.ClientStream
}

type prpcDbControllerOnlineHistoryQueryClient struct {
	grpc.ClientStream
}

func (x *prpcDbControllerOnlineHistoryQueryClient) Recv() (*DbControllerOnlineHistory, error) {
	m := new(DbControllerOnlineHistory)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *prpcDbControllerOnlineHistoryClient) QueryBatch(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbControllerOnlineHistory_QueryBatchClient, error) {
	stream, err := c.cc.NewStream(ctx, &_PrpcDbControllerOnlineHistory_serviceDesc.Streams[1], "/bysdb.PrpcDbControllerOnlineHistory/QueryBatch", opts...)
	if err != nil {
		return nil, err
	}
	x := &prpcDbControllerOnlineHistoryQueryBatchClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type PrpcDbControllerOnlineHistory_QueryBatchClient interface {
	Recv() (*DbControllerOnlineHistoryList, error)
	grpc.ClientStream
}

type prpcDbControllerOnlineHistoryQueryBatchClient struct {
	grpc.ClientStream
}

func (x *prpcDbControllerOnlineHistoryQueryBatchClient) Recv() (*DbControllerOnlineHistoryList, error) {
	m := new(DbControllerOnlineHistoryList)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// PrpcDbControllerOnlineHistoryServer is the server API for PrpcDbControllerOnlineHistory service.
type PrpcDbControllerOnlineHistoryServer interface {
	//插入一行数据
	Insert(context.Context, *DbControllerOnlineHistory) (*crud.DMLResult, error)
	//根据条件取得我有权限的数据
	Query(*crud.PrivilegeParam, PrpcDbControllerOnlineHistory_QueryServer) error
	//根据条件取得我有权限的数据,批量返回数据,crud.PrivilegeParam.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(*crud.PrivilegeParam, PrpcDbControllerOnlineHistory_QueryBatchServer) error
}

// UnimplementedPrpcDbControllerOnlineHistoryServer can be embedded to have forward compatible implementations.
type UnimplementedPrpcDbControllerOnlineHistoryServer struct {
}

func (*UnimplementedPrpcDbControllerOnlineHistoryServer) Insert(ctx context.Context, req *DbControllerOnlineHistory) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Insert not implemented")
}
func (*UnimplementedPrpcDbControllerOnlineHistoryServer) Query(req *crud.PrivilegeParam, srv PrpcDbControllerOnlineHistory_QueryServer) error {
	return status.Errorf(codes.Unimplemented, "method Query not implemented")
}
func (*UnimplementedPrpcDbControllerOnlineHistoryServer) QueryBatch(req *crud.PrivilegeParam, srv PrpcDbControllerOnlineHistory_QueryBatchServer) error {
	return status.Errorf(codes.Unimplemented, "method QueryBatch not implemented")
}

func RegisterPrpcDbControllerOnlineHistoryServer(s *grpc.Server, srv PrpcDbControllerOnlineHistoryServer) {
	s.RegisterService(&_PrpcDbControllerOnlineHistory_serviceDesc, srv)
}

func _PrpcDbControllerOnlineHistory_Insert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbControllerOnlineHistory)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrpcDbControllerOnlineHistoryServer).Insert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.PrpcDbControllerOnlineHistory/Insert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrpcDbControllerOnlineHistoryServer).Insert(ctx, req.(*DbControllerOnlineHistory))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrpcDbControllerOnlineHistory_Query_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.PrivilegeParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(PrpcDbControllerOnlineHistoryServer).Query(m, &prpcDbControllerOnlineHistoryQueryServer{stream})
}

type PrpcDbControllerOnlineHistory_QueryServer interface {
	Send(*DbControllerOnlineHistory) error
	grpc.ServerStream
}

type prpcDbControllerOnlineHistoryQueryServer struct {
	grpc.ServerStream
}

func (x *prpcDbControllerOnlineHistoryQueryServer) Send(m *DbControllerOnlineHistory) error {
	return x.ServerStream.SendMsg(m)
}

func _PrpcDbControllerOnlineHistory_QueryBatch_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.PrivilegeParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(PrpcDbControllerOnlineHistoryServer).QueryBatch(m, &prpcDbControllerOnlineHistoryQueryBatchServer{stream})
}

type PrpcDbControllerOnlineHistory_QueryBatchServer interface {
	Send(*DbControllerOnlineHistoryList) error
	grpc.ServerStream
}

type prpcDbControllerOnlineHistoryQueryBatchServer struct {
	grpc.ServerStream
}

func (x *prpcDbControllerOnlineHistoryQueryBatchServer) Send(m *DbControllerOnlineHistoryList) error {
	return x.ServerStream.SendMsg(m)
}

var _PrpcDbControllerOnlineHistory_serviceDesc = grpc.ServiceDesc{
	ServiceName: "bysdb.PrpcDbControllerOnlineHistory",
	HandlerType: (*PrpcDbControllerOnlineHistoryServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Insert",
			Handler:    _PrpcDbControllerOnlineHistory_Insert_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "Query",
			Handler:       _PrpcDbControllerOnlineHistory_Query_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "QueryBatch",
			Handler:       _PrpcDbControllerOnlineHistory_QueryBatch_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "bysdb.rpc.proto",
}

// RpcDbMediaInfoClient is the client API for RpcDbMediaInfo service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type RpcDbMediaInfoClient interface {
	//插入一行数据
	Insert(ctx context.Context, in *DbMediaInfo, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,全量修改一行数据
	Update(ctx context.Context, in *DbMediaInfo, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
	PartialUpdate(ctx context.Context, in *DbMediaInfo, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,删除一行数据
	Delete(ctx context.Context, in *DbMediaInfo, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
	SelectOne(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (*DbMediaInfo, error)
	//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
	SelectMany(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (RpcDbMediaInfo_SelectManyClient, error)
	//根据条件查询数据
	Query(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbMediaInfo_QueryClient, error)
	//根据条件查询数据,批量返回数据,crud.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbMediaInfo_QueryBatchClient, error)
}

type rpcDbMediaInfoClient struct {
	cc *grpc.ClientConn
}

func NewRpcDbMediaInfoClient(cc *grpc.ClientConn) RpcDbMediaInfoClient {
	return &rpcDbMediaInfoClient{cc}
}

func (c *rpcDbMediaInfoClient) Insert(ctx context.Context, in *DbMediaInfo, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/bysdb.RpcDbMediaInfo/Insert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbMediaInfoClient) Update(ctx context.Context, in *DbMediaInfo, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/bysdb.RpcDbMediaInfo/Update", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbMediaInfoClient) PartialUpdate(ctx context.Context, in *DbMediaInfo, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/bysdb.RpcDbMediaInfo/PartialUpdate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbMediaInfoClient) Delete(ctx context.Context, in *DbMediaInfo, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/bysdb.RpcDbMediaInfo/Delete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbMediaInfoClient) SelectOne(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (*DbMediaInfo, error) {
	out := new(DbMediaInfo)
	err := c.cc.Invoke(ctx, "/bysdb.RpcDbMediaInfo/SelectOne", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbMediaInfoClient) SelectMany(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (RpcDbMediaInfo_SelectManyClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RpcDbMediaInfo_serviceDesc.Streams[0], "/bysdb.RpcDbMediaInfo/SelectMany", opts...)
	if err != nil {
		return nil, err
	}
	x := &rpcDbMediaInfoSelectManyClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type RpcDbMediaInfo_SelectManyClient interface {
	Recv() (*DbMediaInfo, error)
	grpc.ClientStream
}

type rpcDbMediaInfoSelectManyClient struct {
	grpc.ClientStream
}

func (x *rpcDbMediaInfoSelectManyClient) Recv() (*DbMediaInfo, error) {
	m := new(DbMediaInfo)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *rpcDbMediaInfoClient) Query(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbMediaInfo_QueryClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RpcDbMediaInfo_serviceDesc.Streams[1], "/bysdb.RpcDbMediaInfo/Query", opts...)
	if err != nil {
		return nil, err
	}
	x := &rpcDbMediaInfoQueryClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type RpcDbMediaInfo_QueryClient interface {
	Recv() (*DbMediaInfo, error)
	grpc.ClientStream
}

type rpcDbMediaInfoQueryClient struct {
	grpc.ClientStream
}

func (x *rpcDbMediaInfoQueryClient) Recv() (*DbMediaInfo, error) {
	m := new(DbMediaInfo)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *rpcDbMediaInfoClient) QueryBatch(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbMediaInfo_QueryBatchClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RpcDbMediaInfo_serviceDesc.Streams[2], "/bysdb.RpcDbMediaInfo/QueryBatch", opts...)
	if err != nil {
		return nil, err
	}
	x := &rpcDbMediaInfoQueryBatchClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type RpcDbMediaInfo_QueryBatchClient interface {
	Recv() (*DbMediaInfoList, error)
	grpc.ClientStream
}

type rpcDbMediaInfoQueryBatchClient struct {
	grpc.ClientStream
}

func (x *rpcDbMediaInfoQueryBatchClient) Recv() (*DbMediaInfoList, error) {
	m := new(DbMediaInfoList)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// RpcDbMediaInfoServer is the server API for RpcDbMediaInfo service.
type RpcDbMediaInfoServer interface {
	//插入一行数据
	Insert(context.Context, *DbMediaInfo) (*crud.DMLResult, error)
	//以RID为条件,全量修改一行数据
	Update(context.Context, *DbMediaInfo) (*crud.DMLResult, error)
	//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
	PartialUpdate(context.Context, *DbMediaInfo) (*crud.DMLResult, error)
	//以RID为条件,删除一行数据
	Delete(context.Context, *DbMediaInfo) (*crud.DMLResult, error)
	//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
	SelectOne(context.Context, *crud.DMLParam) (*DbMediaInfo, error)
	//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
	SelectMany(*crud.DMLParam, RpcDbMediaInfo_SelectManyServer) error
	//根据条件查询数据
	Query(*crud.QueryParam, RpcDbMediaInfo_QueryServer) error
	//根据条件查询数据,批量返回数据,crud.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(*crud.QueryParam, RpcDbMediaInfo_QueryBatchServer) error
}

// UnimplementedRpcDbMediaInfoServer can be embedded to have forward compatible implementations.
type UnimplementedRpcDbMediaInfoServer struct {
}

func (*UnimplementedRpcDbMediaInfoServer) Insert(ctx context.Context, req *DbMediaInfo) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Insert not implemented")
}
func (*UnimplementedRpcDbMediaInfoServer) Update(ctx context.Context, req *DbMediaInfo) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Update not implemented")
}
func (*UnimplementedRpcDbMediaInfoServer) PartialUpdate(ctx context.Context, req *DbMediaInfo) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PartialUpdate not implemented")
}
func (*UnimplementedRpcDbMediaInfoServer) Delete(ctx context.Context, req *DbMediaInfo) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Delete not implemented")
}
func (*UnimplementedRpcDbMediaInfoServer) SelectOne(ctx context.Context, req *crud.DMLParam) (*DbMediaInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SelectOne not implemented")
}
func (*UnimplementedRpcDbMediaInfoServer) SelectMany(req *crud.DMLParam, srv RpcDbMediaInfo_SelectManyServer) error {
	return status.Errorf(codes.Unimplemented, "method SelectMany not implemented")
}
func (*UnimplementedRpcDbMediaInfoServer) Query(req *crud.QueryParam, srv RpcDbMediaInfo_QueryServer) error {
	return status.Errorf(codes.Unimplemented, "method Query not implemented")
}
func (*UnimplementedRpcDbMediaInfoServer) QueryBatch(req *crud.QueryParam, srv RpcDbMediaInfo_QueryBatchServer) error {
	return status.Errorf(codes.Unimplemented, "method QueryBatch not implemented")
}

func RegisterRpcDbMediaInfoServer(s *grpc.Server, srv RpcDbMediaInfoServer) {
	s.RegisterService(&_RpcDbMediaInfo_serviceDesc, srv)
}

func _RpcDbMediaInfo_Insert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbMediaInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbMediaInfoServer).Insert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.RpcDbMediaInfo/Insert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbMediaInfoServer).Insert(ctx, req.(*DbMediaInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbMediaInfo_Update_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbMediaInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbMediaInfoServer).Update(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.RpcDbMediaInfo/Update",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbMediaInfoServer).Update(ctx, req.(*DbMediaInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbMediaInfo_PartialUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbMediaInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbMediaInfoServer).PartialUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.RpcDbMediaInfo/PartialUpdate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbMediaInfoServer).PartialUpdate(ctx, req.(*DbMediaInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbMediaInfo_Delete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbMediaInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbMediaInfoServer).Delete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.RpcDbMediaInfo/Delete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbMediaInfoServer).Delete(ctx, req.(*DbMediaInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbMediaInfo_SelectOne_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(crud.DMLParam)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbMediaInfoServer).SelectOne(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.RpcDbMediaInfo/SelectOne",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbMediaInfoServer).SelectOne(ctx, req.(*crud.DMLParam))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbMediaInfo_SelectMany_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.DMLParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RpcDbMediaInfoServer).SelectMany(m, &rpcDbMediaInfoSelectManyServer{stream})
}

type RpcDbMediaInfo_SelectManyServer interface {
	Send(*DbMediaInfo) error
	grpc.ServerStream
}

type rpcDbMediaInfoSelectManyServer struct {
	grpc.ServerStream
}

func (x *rpcDbMediaInfoSelectManyServer) Send(m *DbMediaInfo) error {
	return x.ServerStream.SendMsg(m)
}

func _RpcDbMediaInfo_Query_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.QueryParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RpcDbMediaInfoServer).Query(m, &rpcDbMediaInfoQueryServer{stream})
}

type RpcDbMediaInfo_QueryServer interface {
	Send(*DbMediaInfo) error
	grpc.ServerStream
}

type rpcDbMediaInfoQueryServer struct {
	grpc.ServerStream
}

func (x *rpcDbMediaInfoQueryServer) Send(m *DbMediaInfo) error {
	return x.ServerStream.SendMsg(m)
}

func _RpcDbMediaInfo_QueryBatch_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.QueryParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RpcDbMediaInfoServer).QueryBatch(m, &rpcDbMediaInfoQueryBatchServer{stream})
}

type RpcDbMediaInfo_QueryBatchServer interface {
	Send(*DbMediaInfoList) error
	grpc.ServerStream
}

type rpcDbMediaInfoQueryBatchServer struct {
	grpc.ServerStream
}

func (x *rpcDbMediaInfoQueryBatchServer) Send(m *DbMediaInfoList) error {
	return x.ServerStream.SendMsg(m)
}

var _RpcDbMediaInfo_serviceDesc = grpc.ServiceDesc{
	ServiceName: "bysdb.RpcDbMediaInfo",
	HandlerType: (*RpcDbMediaInfoServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Insert",
			Handler:    _RpcDbMediaInfo_Insert_Handler,
		},
		{
			MethodName: "Update",
			Handler:    _RpcDbMediaInfo_Update_Handler,
		},
		{
			MethodName: "PartialUpdate",
			Handler:    _RpcDbMediaInfo_PartialUpdate_Handler,
		},
		{
			MethodName: "Delete",
			Handler:    _RpcDbMediaInfo_Delete_Handler,
		},
		{
			MethodName: "SelectOne",
			Handler:    _RpcDbMediaInfo_SelectOne_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "SelectMany",
			Handler:       _RpcDbMediaInfo_SelectMany_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "Query",
			Handler:       _RpcDbMediaInfo_Query_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "QueryBatch",
			Handler:       _RpcDbMediaInfo_QueryBatch_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "bysdb.rpc.proto",
}

// PrpcDbMediaInfoClient is the client API for PrpcDbMediaInfo service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type PrpcDbMediaInfoClient interface {
	//插入一行数据
	Insert(ctx context.Context, in *DbMediaInfo, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,全量修改一行数据
	Update(ctx context.Context, in *DbMediaInfo, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
	PartialUpdate(ctx context.Context, in *DbMediaInfo, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,删除一行数据
	Delete(ctx context.Context, in *DbMediaInfo, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
	SelectOne(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (*DbMediaInfo, error)
	//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
	SelectMany(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (PrpcDbMediaInfo_SelectManyClient, error)
	//根据条件取得我有权限的数据
	Query(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbMediaInfo_QueryClient, error)
	//根据条件取得我有权限的数据,批量返回数据,crud.PrivilegeParam.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbMediaInfo_QueryBatchClient, error)
}

type prpcDbMediaInfoClient struct {
	cc *grpc.ClientConn
}

func NewPrpcDbMediaInfoClient(cc *grpc.ClientConn) PrpcDbMediaInfoClient {
	return &prpcDbMediaInfoClient{cc}
}

func (c *prpcDbMediaInfoClient) Insert(ctx context.Context, in *DbMediaInfo, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/bysdb.PrpcDbMediaInfo/Insert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prpcDbMediaInfoClient) Update(ctx context.Context, in *DbMediaInfo, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/bysdb.PrpcDbMediaInfo/Update", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prpcDbMediaInfoClient) PartialUpdate(ctx context.Context, in *DbMediaInfo, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/bysdb.PrpcDbMediaInfo/PartialUpdate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prpcDbMediaInfoClient) Delete(ctx context.Context, in *DbMediaInfo, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/bysdb.PrpcDbMediaInfo/Delete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prpcDbMediaInfoClient) SelectOne(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (*DbMediaInfo, error) {
	out := new(DbMediaInfo)
	err := c.cc.Invoke(ctx, "/bysdb.PrpcDbMediaInfo/SelectOne", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prpcDbMediaInfoClient) SelectMany(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (PrpcDbMediaInfo_SelectManyClient, error) {
	stream, err := c.cc.NewStream(ctx, &_PrpcDbMediaInfo_serviceDesc.Streams[0], "/bysdb.PrpcDbMediaInfo/SelectMany", opts...)
	if err != nil {
		return nil, err
	}
	x := &prpcDbMediaInfoSelectManyClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type PrpcDbMediaInfo_SelectManyClient interface {
	Recv() (*DbMediaInfo, error)
	grpc.ClientStream
}

type prpcDbMediaInfoSelectManyClient struct {
	grpc.ClientStream
}

func (x *prpcDbMediaInfoSelectManyClient) Recv() (*DbMediaInfo, error) {
	m := new(DbMediaInfo)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *prpcDbMediaInfoClient) Query(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbMediaInfo_QueryClient, error) {
	stream, err := c.cc.NewStream(ctx, &_PrpcDbMediaInfo_serviceDesc.Streams[1], "/bysdb.PrpcDbMediaInfo/Query", opts...)
	if err != nil {
		return nil, err
	}
	x := &prpcDbMediaInfoQueryClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type PrpcDbMediaInfo_QueryClient interface {
	Recv() (*DbMediaInfo, error)
	grpc.ClientStream
}

type prpcDbMediaInfoQueryClient struct {
	grpc.ClientStream
}

func (x *prpcDbMediaInfoQueryClient) Recv() (*DbMediaInfo, error) {
	m := new(DbMediaInfo)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *prpcDbMediaInfoClient) QueryBatch(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbMediaInfo_QueryBatchClient, error) {
	stream, err := c.cc.NewStream(ctx, &_PrpcDbMediaInfo_serviceDesc.Streams[2], "/bysdb.PrpcDbMediaInfo/QueryBatch", opts...)
	if err != nil {
		return nil, err
	}
	x := &prpcDbMediaInfoQueryBatchClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type PrpcDbMediaInfo_QueryBatchClient interface {
	Recv() (*DbMediaInfoList, error)
	grpc.ClientStream
}

type prpcDbMediaInfoQueryBatchClient struct {
	grpc.ClientStream
}

func (x *prpcDbMediaInfoQueryBatchClient) Recv() (*DbMediaInfoList, error) {
	m := new(DbMediaInfoList)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// PrpcDbMediaInfoServer is the server API for PrpcDbMediaInfo service.
type PrpcDbMediaInfoServer interface {
	//插入一行数据
	Insert(context.Context, *DbMediaInfo) (*crud.DMLResult, error)
	//以RID为条件,全量修改一行数据
	Update(context.Context, *DbMediaInfo) (*crud.DMLResult, error)
	//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
	PartialUpdate(context.Context, *DbMediaInfo) (*crud.DMLResult, error)
	//以RID为条件,删除一行数据
	Delete(context.Context, *DbMediaInfo) (*crud.DMLResult, error)
	//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
	SelectOne(context.Context, *crud.DMLParam) (*DbMediaInfo, error)
	//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
	SelectMany(*crud.DMLParam, PrpcDbMediaInfo_SelectManyServer) error
	//根据条件取得我有权限的数据
	Query(*crud.PrivilegeParam, PrpcDbMediaInfo_QueryServer) error
	//根据条件取得我有权限的数据,批量返回数据,crud.PrivilegeParam.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(*crud.PrivilegeParam, PrpcDbMediaInfo_QueryBatchServer) error
}

// UnimplementedPrpcDbMediaInfoServer can be embedded to have forward compatible implementations.
type UnimplementedPrpcDbMediaInfoServer struct {
}

func (*UnimplementedPrpcDbMediaInfoServer) Insert(ctx context.Context, req *DbMediaInfo) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Insert not implemented")
}
func (*UnimplementedPrpcDbMediaInfoServer) Update(ctx context.Context, req *DbMediaInfo) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Update not implemented")
}
func (*UnimplementedPrpcDbMediaInfoServer) PartialUpdate(ctx context.Context, req *DbMediaInfo) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PartialUpdate not implemented")
}
func (*UnimplementedPrpcDbMediaInfoServer) Delete(ctx context.Context, req *DbMediaInfo) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Delete not implemented")
}
func (*UnimplementedPrpcDbMediaInfoServer) SelectOne(ctx context.Context, req *crud.DMLParam) (*DbMediaInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SelectOne not implemented")
}
func (*UnimplementedPrpcDbMediaInfoServer) SelectMany(req *crud.DMLParam, srv PrpcDbMediaInfo_SelectManyServer) error {
	return status.Errorf(codes.Unimplemented, "method SelectMany not implemented")
}
func (*UnimplementedPrpcDbMediaInfoServer) Query(req *crud.PrivilegeParam, srv PrpcDbMediaInfo_QueryServer) error {
	return status.Errorf(codes.Unimplemented, "method Query not implemented")
}
func (*UnimplementedPrpcDbMediaInfoServer) QueryBatch(req *crud.PrivilegeParam, srv PrpcDbMediaInfo_QueryBatchServer) error {
	return status.Errorf(codes.Unimplemented, "method QueryBatch not implemented")
}

func RegisterPrpcDbMediaInfoServer(s *grpc.Server, srv PrpcDbMediaInfoServer) {
	s.RegisterService(&_PrpcDbMediaInfo_serviceDesc, srv)
}

func _PrpcDbMediaInfo_Insert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbMediaInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrpcDbMediaInfoServer).Insert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.PrpcDbMediaInfo/Insert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrpcDbMediaInfoServer).Insert(ctx, req.(*DbMediaInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrpcDbMediaInfo_Update_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbMediaInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrpcDbMediaInfoServer).Update(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.PrpcDbMediaInfo/Update",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrpcDbMediaInfoServer).Update(ctx, req.(*DbMediaInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrpcDbMediaInfo_PartialUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbMediaInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrpcDbMediaInfoServer).PartialUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.PrpcDbMediaInfo/PartialUpdate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrpcDbMediaInfoServer).PartialUpdate(ctx, req.(*DbMediaInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrpcDbMediaInfo_Delete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbMediaInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrpcDbMediaInfoServer).Delete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.PrpcDbMediaInfo/Delete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrpcDbMediaInfoServer).Delete(ctx, req.(*DbMediaInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrpcDbMediaInfo_SelectOne_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(crud.DMLParam)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrpcDbMediaInfoServer).SelectOne(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.PrpcDbMediaInfo/SelectOne",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrpcDbMediaInfoServer).SelectOne(ctx, req.(*crud.DMLParam))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrpcDbMediaInfo_SelectMany_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.DMLParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(PrpcDbMediaInfoServer).SelectMany(m, &prpcDbMediaInfoSelectManyServer{stream})
}

type PrpcDbMediaInfo_SelectManyServer interface {
	Send(*DbMediaInfo) error
	grpc.ServerStream
}

type prpcDbMediaInfoSelectManyServer struct {
	grpc.ServerStream
}

func (x *prpcDbMediaInfoSelectManyServer) Send(m *DbMediaInfo) error {
	return x.ServerStream.SendMsg(m)
}

func _PrpcDbMediaInfo_Query_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.PrivilegeParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(PrpcDbMediaInfoServer).Query(m, &prpcDbMediaInfoQueryServer{stream})
}

type PrpcDbMediaInfo_QueryServer interface {
	Send(*DbMediaInfo) error
	grpc.ServerStream
}

type prpcDbMediaInfoQueryServer struct {
	grpc.ServerStream
}

func (x *prpcDbMediaInfoQueryServer) Send(m *DbMediaInfo) error {
	return x.ServerStream.SendMsg(m)
}

func _PrpcDbMediaInfo_QueryBatch_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.PrivilegeParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(PrpcDbMediaInfoServer).QueryBatch(m, &prpcDbMediaInfoQueryBatchServer{stream})
}

type PrpcDbMediaInfo_QueryBatchServer interface {
	Send(*DbMediaInfoList) error
	grpc.ServerStream
}

type prpcDbMediaInfoQueryBatchServer struct {
	grpc.ServerStream
}

func (x *prpcDbMediaInfoQueryBatchServer) Send(m *DbMediaInfoList) error {
	return x.ServerStream.SendMsg(m)
}

var _PrpcDbMediaInfo_serviceDesc = grpc.ServiceDesc{
	ServiceName: "bysdb.PrpcDbMediaInfo",
	HandlerType: (*PrpcDbMediaInfoServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Insert",
			Handler:    _PrpcDbMediaInfo_Insert_Handler,
		},
		{
			MethodName: "Update",
			Handler:    _PrpcDbMediaInfo_Update_Handler,
		},
		{
			MethodName: "PartialUpdate",
			Handler:    _PrpcDbMediaInfo_PartialUpdate_Handler,
		},
		{
			MethodName: "Delete",
			Handler:    _PrpcDbMediaInfo_Delete_Handler,
		},
		{
			MethodName: "SelectOne",
			Handler:    _PrpcDbMediaInfo_SelectOne_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "SelectMany",
			Handler:       _PrpcDbMediaInfo_SelectMany_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "Query",
			Handler:       _PrpcDbMediaInfo_Query_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "QueryBatch",
			Handler:       _PrpcDbMediaInfo_QueryBatch_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "bysdb.rpc.proto",
}

// RpcDbMarkerHistoryClient is the client API for RpcDbMarkerHistory service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type RpcDbMarkerHistoryClient interface {
	//插入一行数据
	Insert(ctx context.Context, in *DbMarkerHistory, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//根据条件查询数据
	Query(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbMarkerHistory_QueryClient, error)
	//根据条件查询数据,批量返回数据,crud.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbMarkerHistory_QueryBatchClient, error)
}

type rpcDbMarkerHistoryClient struct {
	cc *grpc.ClientConn
}

func NewRpcDbMarkerHistoryClient(cc *grpc.ClientConn) RpcDbMarkerHistoryClient {
	return &rpcDbMarkerHistoryClient{cc}
}

func (c *rpcDbMarkerHistoryClient) Insert(ctx context.Context, in *DbMarkerHistory, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/bysdb.RpcDbMarkerHistory/Insert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbMarkerHistoryClient) Query(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbMarkerHistory_QueryClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RpcDbMarkerHistory_serviceDesc.Streams[0], "/bysdb.RpcDbMarkerHistory/Query", opts...)
	if err != nil {
		return nil, err
	}
	x := &rpcDbMarkerHistoryQueryClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type RpcDbMarkerHistory_QueryClient interface {
	Recv() (*DbMarkerHistory, error)
	grpc.ClientStream
}

type rpcDbMarkerHistoryQueryClient struct {
	grpc.ClientStream
}

func (x *rpcDbMarkerHistoryQueryClient) Recv() (*DbMarkerHistory, error) {
	m := new(DbMarkerHistory)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *rpcDbMarkerHistoryClient) QueryBatch(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbMarkerHistory_QueryBatchClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RpcDbMarkerHistory_serviceDesc.Streams[1], "/bysdb.RpcDbMarkerHistory/QueryBatch", opts...)
	if err != nil {
		return nil, err
	}
	x := &rpcDbMarkerHistoryQueryBatchClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type RpcDbMarkerHistory_QueryBatchClient interface {
	Recv() (*DbMarkerHistoryList, error)
	grpc.ClientStream
}

type rpcDbMarkerHistoryQueryBatchClient struct {
	grpc.ClientStream
}

func (x *rpcDbMarkerHistoryQueryBatchClient) Recv() (*DbMarkerHistoryList, error) {
	m := new(DbMarkerHistoryList)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// RpcDbMarkerHistoryServer is the server API for RpcDbMarkerHistory service.
type RpcDbMarkerHistoryServer interface {
	//插入一行数据
	Insert(context.Context, *DbMarkerHistory) (*crud.DMLResult, error)
	//根据条件查询数据
	Query(*crud.QueryParam, RpcDbMarkerHistory_QueryServer) error
	//根据条件查询数据,批量返回数据,crud.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(*crud.QueryParam, RpcDbMarkerHistory_QueryBatchServer) error
}

// UnimplementedRpcDbMarkerHistoryServer can be embedded to have forward compatible implementations.
type UnimplementedRpcDbMarkerHistoryServer struct {
}

func (*UnimplementedRpcDbMarkerHistoryServer) Insert(ctx context.Context, req *DbMarkerHistory) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Insert not implemented")
}
func (*UnimplementedRpcDbMarkerHistoryServer) Query(req *crud.QueryParam, srv RpcDbMarkerHistory_QueryServer) error {
	return status.Errorf(codes.Unimplemented, "method Query not implemented")
}
func (*UnimplementedRpcDbMarkerHistoryServer) QueryBatch(req *crud.QueryParam, srv RpcDbMarkerHistory_QueryBatchServer) error {
	return status.Errorf(codes.Unimplemented, "method QueryBatch not implemented")
}

func RegisterRpcDbMarkerHistoryServer(s *grpc.Server, srv RpcDbMarkerHistoryServer) {
	s.RegisterService(&_RpcDbMarkerHistory_serviceDesc, srv)
}

func _RpcDbMarkerHistory_Insert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbMarkerHistory)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbMarkerHistoryServer).Insert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.RpcDbMarkerHistory/Insert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbMarkerHistoryServer).Insert(ctx, req.(*DbMarkerHistory))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbMarkerHistory_Query_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.QueryParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RpcDbMarkerHistoryServer).Query(m, &rpcDbMarkerHistoryQueryServer{stream})
}

type RpcDbMarkerHistory_QueryServer interface {
	Send(*DbMarkerHistory) error
	grpc.ServerStream
}

type rpcDbMarkerHistoryQueryServer struct {
	grpc.ServerStream
}

func (x *rpcDbMarkerHistoryQueryServer) Send(m *DbMarkerHistory) error {
	return x.ServerStream.SendMsg(m)
}

func _RpcDbMarkerHistory_QueryBatch_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.QueryParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RpcDbMarkerHistoryServer).QueryBatch(m, &rpcDbMarkerHistoryQueryBatchServer{stream})
}

type RpcDbMarkerHistory_QueryBatchServer interface {
	Send(*DbMarkerHistoryList) error
	grpc.ServerStream
}

type rpcDbMarkerHistoryQueryBatchServer struct {
	grpc.ServerStream
}

func (x *rpcDbMarkerHistoryQueryBatchServer) Send(m *DbMarkerHistoryList) error {
	return x.ServerStream.SendMsg(m)
}

var _RpcDbMarkerHistory_serviceDesc = grpc.ServiceDesc{
	ServiceName: "bysdb.RpcDbMarkerHistory",
	HandlerType: (*RpcDbMarkerHistoryServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Insert",
			Handler:    _RpcDbMarkerHistory_Insert_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "Query",
			Handler:       _RpcDbMarkerHistory_Query_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "QueryBatch",
			Handler:       _RpcDbMarkerHistory_QueryBatch_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "bysdb.rpc.proto",
}

// PrpcDbMarkerHistoryClient is the client API for PrpcDbMarkerHistory service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type PrpcDbMarkerHistoryClient interface {
	//插入一行数据
	Insert(ctx context.Context, in *DbMarkerHistory, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//根据条件取得我有权限的数据
	Query(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbMarkerHistory_QueryClient, error)
	//根据条件取得我有权限的数据,批量返回数据,crud.PrivilegeParam.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbMarkerHistory_QueryBatchClient, error)
}

type prpcDbMarkerHistoryClient struct {
	cc *grpc.ClientConn
}

func NewPrpcDbMarkerHistoryClient(cc *grpc.ClientConn) PrpcDbMarkerHistoryClient {
	return &prpcDbMarkerHistoryClient{cc}
}

func (c *prpcDbMarkerHistoryClient) Insert(ctx context.Context, in *DbMarkerHistory, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/bysdb.PrpcDbMarkerHistory/Insert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prpcDbMarkerHistoryClient) Query(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbMarkerHistory_QueryClient, error) {
	stream, err := c.cc.NewStream(ctx, &_PrpcDbMarkerHistory_serviceDesc.Streams[0], "/bysdb.PrpcDbMarkerHistory/Query", opts...)
	if err != nil {
		return nil, err
	}
	x := &prpcDbMarkerHistoryQueryClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type PrpcDbMarkerHistory_QueryClient interface {
	Recv() (*DbMarkerHistory, error)
	grpc.ClientStream
}

type prpcDbMarkerHistoryQueryClient struct {
	grpc.ClientStream
}

func (x *prpcDbMarkerHistoryQueryClient) Recv() (*DbMarkerHistory, error) {
	m := new(DbMarkerHistory)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *prpcDbMarkerHistoryClient) QueryBatch(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbMarkerHistory_QueryBatchClient, error) {
	stream, err := c.cc.NewStream(ctx, &_PrpcDbMarkerHistory_serviceDesc.Streams[1], "/bysdb.PrpcDbMarkerHistory/QueryBatch", opts...)
	if err != nil {
		return nil, err
	}
	x := &prpcDbMarkerHistoryQueryBatchClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type PrpcDbMarkerHistory_QueryBatchClient interface {
	Recv() (*DbMarkerHistoryList, error)
	grpc.ClientStream
}

type prpcDbMarkerHistoryQueryBatchClient struct {
	grpc.ClientStream
}

func (x *prpcDbMarkerHistoryQueryBatchClient) Recv() (*DbMarkerHistoryList, error) {
	m := new(DbMarkerHistoryList)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// PrpcDbMarkerHistoryServer is the server API for PrpcDbMarkerHistory service.
type PrpcDbMarkerHistoryServer interface {
	//插入一行数据
	Insert(context.Context, *DbMarkerHistory) (*crud.DMLResult, error)
	//根据条件取得我有权限的数据
	Query(*crud.PrivilegeParam, PrpcDbMarkerHistory_QueryServer) error
	//根据条件取得我有权限的数据,批量返回数据,crud.PrivilegeParam.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(*crud.PrivilegeParam, PrpcDbMarkerHistory_QueryBatchServer) error
}

// UnimplementedPrpcDbMarkerHistoryServer can be embedded to have forward compatible implementations.
type UnimplementedPrpcDbMarkerHistoryServer struct {
}

func (*UnimplementedPrpcDbMarkerHistoryServer) Insert(ctx context.Context, req *DbMarkerHistory) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Insert not implemented")
}
func (*UnimplementedPrpcDbMarkerHistoryServer) Query(req *crud.PrivilegeParam, srv PrpcDbMarkerHistory_QueryServer) error {
	return status.Errorf(codes.Unimplemented, "method Query not implemented")
}
func (*UnimplementedPrpcDbMarkerHistoryServer) QueryBatch(req *crud.PrivilegeParam, srv PrpcDbMarkerHistory_QueryBatchServer) error {
	return status.Errorf(codes.Unimplemented, "method QueryBatch not implemented")
}

func RegisterPrpcDbMarkerHistoryServer(s *grpc.Server, srv PrpcDbMarkerHistoryServer) {
	s.RegisterService(&_PrpcDbMarkerHistory_serviceDesc, srv)
}

func _PrpcDbMarkerHistory_Insert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbMarkerHistory)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrpcDbMarkerHistoryServer).Insert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.PrpcDbMarkerHistory/Insert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrpcDbMarkerHistoryServer).Insert(ctx, req.(*DbMarkerHistory))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrpcDbMarkerHistory_Query_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.PrivilegeParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(PrpcDbMarkerHistoryServer).Query(m, &prpcDbMarkerHistoryQueryServer{stream})
}

type PrpcDbMarkerHistory_QueryServer interface {
	Send(*DbMarkerHistory) error
	grpc.ServerStream
}

type prpcDbMarkerHistoryQueryServer struct {
	grpc.ServerStream
}

func (x *prpcDbMarkerHistoryQueryServer) Send(m *DbMarkerHistory) error {
	return x.ServerStream.SendMsg(m)
}

func _PrpcDbMarkerHistory_QueryBatch_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.PrivilegeParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(PrpcDbMarkerHistoryServer).QueryBatch(m, &prpcDbMarkerHistoryQueryBatchServer{stream})
}

type PrpcDbMarkerHistory_QueryBatchServer interface {
	Send(*DbMarkerHistoryList) error
	grpc.ServerStream
}

type prpcDbMarkerHistoryQueryBatchServer struct {
	grpc.ServerStream
}

func (x *prpcDbMarkerHistoryQueryBatchServer) Send(m *DbMarkerHistoryList) error {
	return x.ServerStream.SendMsg(m)
}

var _PrpcDbMarkerHistory_serviceDesc = grpc.ServiceDesc{
	ServiceName: "bysdb.PrpcDbMarkerHistory",
	HandlerType: (*PrpcDbMarkerHistoryServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Insert",
			Handler:    _PrpcDbMarkerHistory_Insert_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "Query",
			Handler:       _PrpcDbMarkerHistory_Query_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "QueryBatch",
			Handler:       _PrpcDbMarkerHistory_QueryBatch_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "bysdb.rpc.proto",
}

// RpcDbMarkerPatrolHistoryClient is the client API for RpcDbMarkerPatrolHistory service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type RpcDbMarkerPatrolHistoryClient interface {
	//插入一行数据
	Insert(ctx context.Context, in *DbMarkerPatrolHistory, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//根据条件查询数据
	Query(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbMarkerPatrolHistory_QueryClient, error)
	//根据条件查询数据,批量返回数据,crud.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbMarkerPatrolHistory_QueryBatchClient, error)
}

type rpcDbMarkerPatrolHistoryClient struct {
	cc *grpc.ClientConn
}

func NewRpcDbMarkerPatrolHistoryClient(cc *grpc.ClientConn) RpcDbMarkerPatrolHistoryClient {
	return &rpcDbMarkerPatrolHistoryClient{cc}
}

func (c *rpcDbMarkerPatrolHistoryClient) Insert(ctx context.Context, in *DbMarkerPatrolHistory, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/bysdb.RpcDbMarkerPatrolHistory/Insert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbMarkerPatrolHistoryClient) Query(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbMarkerPatrolHistory_QueryClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RpcDbMarkerPatrolHistory_serviceDesc.Streams[0], "/bysdb.RpcDbMarkerPatrolHistory/Query", opts...)
	if err != nil {
		return nil, err
	}
	x := &rpcDbMarkerPatrolHistoryQueryClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type RpcDbMarkerPatrolHistory_QueryClient interface {
	Recv() (*DbMarkerPatrolHistory, error)
	grpc.ClientStream
}

type rpcDbMarkerPatrolHistoryQueryClient struct {
	grpc.ClientStream
}

func (x *rpcDbMarkerPatrolHistoryQueryClient) Recv() (*DbMarkerPatrolHistory, error) {
	m := new(DbMarkerPatrolHistory)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *rpcDbMarkerPatrolHistoryClient) QueryBatch(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbMarkerPatrolHistory_QueryBatchClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RpcDbMarkerPatrolHistory_serviceDesc.Streams[1], "/bysdb.RpcDbMarkerPatrolHistory/QueryBatch", opts...)
	if err != nil {
		return nil, err
	}
	x := &rpcDbMarkerPatrolHistoryQueryBatchClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type RpcDbMarkerPatrolHistory_QueryBatchClient interface {
	Recv() (*DbMarkerPatrolHistoryList, error)
	grpc.ClientStream
}

type rpcDbMarkerPatrolHistoryQueryBatchClient struct {
	grpc.ClientStream
}

func (x *rpcDbMarkerPatrolHistoryQueryBatchClient) Recv() (*DbMarkerPatrolHistoryList, error) {
	m := new(DbMarkerPatrolHistoryList)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// RpcDbMarkerPatrolHistoryServer is the server API for RpcDbMarkerPatrolHistory service.
type RpcDbMarkerPatrolHistoryServer interface {
	//插入一行数据
	Insert(context.Context, *DbMarkerPatrolHistory) (*crud.DMLResult, error)
	//根据条件查询数据
	Query(*crud.QueryParam, RpcDbMarkerPatrolHistory_QueryServer) error
	//根据条件查询数据,批量返回数据,crud.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(*crud.QueryParam, RpcDbMarkerPatrolHistory_QueryBatchServer) error
}

// UnimplementedRpcDbMarkerPatrolHistoryServer can be embedded to have forward compatible implementations.
type UnimplementedRpcDbMarkerPatrolHistoryServer struct {
}

func (*UnimplementedRpcDbMarkerPatrolHistoryServer) Insert(ctx context.Context, req *DbMarkerPatrolHistory) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Insert not implemented")
}
func (*UnimplementedRpcDbMarkerPatrolHistoryServer) Query(req *crud.QueryParam, srv RpcDbMarkerPatrolHistory_QueryServer) error {
	return status.Errorf(codes.Unimplemented, "method Query not implemented")
}
func (*UnimplementedRpcDbMarkerPatrolHistoryServer) QueryBatch(req *crud.QueryParam, srv RpcDbMarkerPatrolHistory_QueryBatchServer) error {
	return status.Errorf(codes.Unimplemented, "method QueryBatch not implemented")
}

func RegisterRpcDbMarkerPatrolHistoryServer(s *grpc.Server, srv RpcDbMarkerPatrolHistoryServer) {
	s.RegisterService(&_RpcDbMarkerPatrolHistory_serviceDesc, srv)
}

func _RpcDbMarkerPatrolHistory_Insert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbMarkerPatrolHistory)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbMarkerPatrolHistoryServer).Insert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.RpcDbMarkerPatrolHistory/Insert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbMarkerPatrolHistoryServer).Insert(ctx, req.(*DbMarkerPatrolHistory))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbMarkerPatrolHistory_Query_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.QueryParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RpcDbMarkerPatrolHistoryServer).Query(m, &rpcDbMarkerPatrolHistoryQueryServer{stream})
}

type RpcDbMarkerPatrolHistory_QueryServer interface {
	Send(*DbMarkerPatrolHistory) error
	grpc.ServerStream
}

type rpcDbMarkerPatrolHistoryQueryServer struct {
	grpc.ServerStream
}

func (x *rpcDbMarkerPatrolHistoryQueryServer) Send(m *DbMarkerPatrolHistory) error {
	return x.ServerStream.SendMsg(m)
}

func _RpcDbMarkerPatrolHistory_QueryBatch_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.QueryParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RpcDbMarkerPatrolHistoryServer).QueryBatch(m, &rpcDbMarkerPatrolHistoryQueryBatchServer{stream})
}

type RpcDbMarkerPatrolHistory_QueryBatchServer interface {
	Send(*DbMarkerPatrolHistoryList) error
	grpc.ServerStream
}

type rpcDbMarkerPatrolHistoryQueryBatchServer struct {
	grpc.ServerStream
}

func (x *rpcDbMarkerPatrolHistoryQueryBatchServer) Send(m *DbMarkerPatrolHistoryList) error {
	return x.ServerStream.SendMsg(m)
}

var _RpcDbMarkerPatrolHistory_serviceDesc = grpc.ServiceDesc{
	ServiceName: "bysdb.RpcDbMarkerPatrolHistory",
	HandlerType: (*RpcDbMarkerPatrolHistoryServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Insert",
			Handler:    _RpcDbMarkerPatrolHistory_Insert_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "Query",
			Handler:       _RpcDbMarkerPatrolHistory_Query_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "QueryBatch",
			Handler:       _RpcDbMarkerPatrolHistory_QueryBatch_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "bysdb.rpc.proto",
}

// PrpcDbMarkerPatrolHistoryClient is the client API for PrpcDbMarkerPatrolHistory service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type PrpcDbMarkerPatrolHistoryClient interface {
	//插入一行数据
	Insert(ctx context.Context, in *DbMarkerPatrolHistory, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//根据条件取得我有权限的数据
	Query(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbMarkerPatrolHistory_QueryClient, error)
	//根据条件取得我有权限的数据,批量返回数据,crud.PrivilegeParam.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbMarkerPatrolHistory_QueryBatchClient, error)
}

type prpcDbMarkerPatrolHistoryClient struct {
	cc *grpc.ClientConn
}

func NewPrpcDbMarkerPatrolHistoryClient(cc *grpc.ClientConn) PrpcDbMarkerPatrolHistoryClient {
	return &prpcDbMarkerPatrolHistoryClient{cc}
}

func (c *prpcDbMarkerPatrolHistoryClient) Insert(ctx context.Context, in *DbMarkerPatrolHistory, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/bysdb.PrpcDbMarkerPatrolHistory/Insert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prpcDbMarkerPatrolHistoryClient) Query(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbMarkerPatrolHistory_QueryClient, error) {
	stream, err := c.cc.NewStream(ctx, &_PrpcDbMarkerPatrolHistory_serviceDesc.Streams[0], "/bysdb.PrpcDbMarkerPatrolHistory/Query", opts...)
	if err != nil {
		return nil, err
	}
	x := &prpcDbMarkerPatrolHistoryQueryClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type PrpcDbMarkerPatrolHistory_QueryClient interface {
	Recv() (*DbMarkerPatrolHistory, error)
	grpc.ClientStream
}

type prpcDbMarkerPatrolHistoryQueryClient struct {
	grpc.ClientStream
}

func (x *prpcDbMarkerPatrolHistoryQueryClient) Recv() (*DbMarkerPatrolHistory, error) {
	m := new(DbMarkerPatrolHistory)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *prpcDbMarkerPatrolHistoryClient) QueryBatch(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbMarkerPatrolHistory_QueryBatchClient, error) {
	stream, err := c.cc.NewStream(ctx, &_PrpcDbMarkerPatrolHistory_serviceDesc.Streams[1], "/bysdb.PrpcDbMarkerPatrolHistory/QueryBatch", opts...)
	if err != nil {
		return nil, err
	}
	x := &prpcDbMarkerPatrolHistoryQueryBatchClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type PrpcDbMarkerPatrolHistory_QueryBatchClient interface {
	Recv() (*DbMarkerPatrolHistoryList, error)
	grpc.ClientStream
}

type prpcDbMarkerPatrolHistoryQueryBatchClient struct {
	grpc.ClientStream
}

func (x *prpcDbMarkerPatrolHistoryQueryBatchClient) Recv() (*DbMarkerPatrolHistoryList, error) {
	m := new(DbMarkerPatrolHistoryList)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// PrpcDbMarkerPatrolHistoryServer is the server API for PrpcDbMarkerPatrolHistory service.
type PrpcDbMarkerPatrolHistoryServer interface {
	//插入一行数据
	Insert(context.Context, *DbMarkerPatrolHistory) (*crud.DMLResult, error)
	//根据条件取得我有权限的数据
	Query(*crud.PrivilegeParam, PrpcDbMarkerPatrolHistory_QueryServer) error
	//根据条件取得我有权限的数据,批量返回数据,crud.PrivilegeParam.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(*crud.PrivilegeParam, PrpcDbMarkerPatrolHistory_QueryBatchServer) error
}

// UnimplementedPrpcDbMarkerPatrolHistoryServer can be embedded to have forward compatible implementations.
type UnimplementedPrpcDbMarkerPatrolHistoryServer struct {
}

func (*UnimplementedPrpcDbMarkerPatrolHistoryServer) Insert(ctx context.Context, req *DbMarkerPatrolHistory) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Insert not implemented")
}
func (*UnimplementedPrpcDbMarkerPatrolHistoryServer) Query(req *crud.PrivilegeParam, srv PrpcDbMarkerPatrolHistory_QueryServer) error {
	return status.Errorf(codes.Unimplemented, "method Query not implemented")
}
func (*UnimplementedPrpcDbMarkerPatrolHistoryServer) QueryBatch(req *crud.PrivilegeParam, srv PrpcDbMarkerPatrolHistory_QueryBatchServer) error {
	return status.Errorf(codes.Unimplemented, "method QueryBatch not implemented")
}

func RegisterPrpcDbMarkerPatrolHistoryServer(s *grpc.Server, srv PrpcDbMarkerPatrolHistoryServer) {
	s.RegisterService(&_PrpcDbMarkerPatrolHistory_serviceDesc, srv)
}

func _PrpcDbMarkerPatrolHistory_Insert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbMarkerPatrolHistory)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrpcDbMarkerPatrolHistoryServer).Insert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.PrpcDbMarkerPatrolHistory/Insert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrpcDbMarkerPatrolHistoryServer).Insert(ctx, req.(*DbMarkerPatrolHistory))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrpcDbMarkerPatrolHistory_Query_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.PrivilegeParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(PrpcDbMarkerPatrolHistoryServer).Query(m, &prpcDbMarkerPatrolHistoryQueryServer{stream})
}

type PrpcDbMarkerPatrolHistory_QueryServer interface {
	Send(*DbMarkerPatrolHistory) error
	grpc.ServerStream
}

type prpcDbMarkerPatrolHistoryQueryServer struct {
	grpc.ServerStream
}

func (x *prpcDbMarkerPatrolHistoryQueryServer) Send(m *DbMarkerPatrolHistory) error {
	return x.ServerStream.SendMsg(m)
}

func _PrpcDbMarkerPatrolHistory_QueryBatch_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.PrivilegeParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(PrpcDbMarkerPatrolHistoryServer).QueryBatch(m, &prpcDbMarkerPatrolHistoryQueryBatchServer{stream})
}

type PrpcDbMarkerPatrolHistory_QueryBatchServer interface {
	Send(*DbMarkerPatrolHistoryList) error
	grpc.ServerStream
}

type prpcDbMarkerPatrolHistoryQueryBatchServer struct {
	grpc.ServerStream
}

func (x *prpcDbMarkerPatrolHistoryQueryBatchServer) Send(m *DbMarkerPatrolHistoryList) error {
	return x.ServerStream.SendMsg(m)
}

var _PrpcDbMarkerPatrolHistory_serviceDesc = grpc.ServiceDesc{
	ServiceName: "bysdb.PrpcDbMarkerPatrolHistory",
	HandlerType: (*PrpcDbMarkerPatrolHistoryServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Insert",
			Handler:    _PrpcDbMarkerPatrolHistory_Insert_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "Query",
			Handler:       _PrpcDbMarkerPatrolHistory_Query_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "QueryBatch",
			Handler:       _PrpcDbMarkerPatrolHistory_QueryBatch_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "bysdb.rpc.proto",
}

// RpcDbNFCPatrolLineClient is the client API for RpcDbNFCPatrolLine service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type RpcDbNFCPatrolLineClient interface {
	//插入一行数据
	Insert(ctx context.Context, in *DbNFCPatrolLine, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,全量修改一行数据
	Update(ctx context.Context, in *DbNFCPatrolLine, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
	PartialUpdate(ctx context.Context, in *DbNFCPatrolLine, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,删除一行数据
	Delete(ctx context.Context, in *DbNFCPatrolLine, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
	SelectOne(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (*DbNFCPatrolLine, error)
	//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
	SelectMany(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (RpcDbNFCPatrolLine_SelectManyClient, error)
	//根据条件查询数据
	Query(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbNFCPatrolLine_QueryClient, error)
	//根据条件查询数据,批量返回数据,crud.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbNFCPatrolLine_QueryBatchClient, error)
}

type rpcDbNFCPatrolLineClient struct {
	cc *grpc.ClientConn
}

func NewRpcDbNFCPatrolLineClient(cc *grpc.ClientConn) RpcDbNFCPatrolLineClient {
	return &rpcDbNFCPatrolLineClient{cc}
}

func (c *rpcDbNFCPatrolLineClient) Insert(ctx context.Context, in *DbNFCPatrolLine, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/bysdb.RpcDbNFCPatrolLine/Insert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbNFCPatrolLineClient) Update(ctx context.Context, in *DbNFCPatrolLine, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/bysdb.RpcDbNFCPatrolLine/Update", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbNFCPatrolLineClient) PartialUpdate(ctx context.Context, in *DbNFCPatrolLine, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/bysdb.RpcDbNFCPatrolLine/PartialUpdate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbNFCPatrolLineClient) Delete(ctx context.Context, in *DbNFCPatrolLine, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/bysdb.RpcDbNFCPatrolLine/Delete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbNFCPatrolLineClient) SelectOne(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (*DbNFCPatrolLine, error) {
	out := new(DbNFCPatrolLine)
	err := c.cc.Invoke(ctx, "/bysdb.RpcDbNFCPatrolLine/SelectOne", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbNFCPatrolLineClient) SelectMany(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (RpcDbNFCPatrolLine_SelectManyClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RpcDbNFCPatrolLine_serviceDesc.Streams[0], "/bysdb.RpcDbNFCPatrolLine/SelectMany", opts...)
	if err != nil {
		return nil, err
	}
	x := &rpcDbNFCPatrolLineSelectManyClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type RpcDbNFCPatrolLine_SelectManyClient interface {
	Recv() (*DbNFCPatrolLine, error)
	grpc.ClientStream
}

type rpcDbNFCPatrolLineSelectManyClient struct {
	grpc.ClientStream
}

func (x *rpcDbNFCPatrolLineSelectManyClient) Recv() (*DbNFCPatrolLine, error) {
	m := new(DbNFCPatrolLine)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *rpcDbNFCPatrolLineClient) Query(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbNFCPatrolLine_QueryClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RpcDbNFCPatrolLine_serviceDesc.Streams[1], "/bysdb.RpcDbNFCPatrolLine/Query", opts...)
	if err != nil {
		return nil, err
	}
	x := &rpcDbNFCPatrolLineQueryClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type RpcDbNFCPatrolLine_QueryClient interface {
	Recv() (*DbNFCPatrolLine, error)
	grpc.ClientStream
}

type rpcDbNFCPatrolLineQueryClient struct {
	grpc.ClientStream
}

func (x *rpcDbNFCPatrolLineQueryClient) Recv() (*DbNFCPatrolLine, error) {
	m := new(DbNFCPatrolLine)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *rpcDbNFCPatrolLineClient) QueryBatch(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbNFCPatrolLine_QueryBatchClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RpcDbNFCPatrolLine_serviceDesc.Streams[2], "/bysdb.RpcDbNFCPatrolLine/QueryBatch", opts...)
	if err != nil {
		return nil, err
	}
	x := &rpcDbNFCPatrolLineQueryBatchClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type RpcDbNFCPatrolLine_QueryBatchClient interface {
	Recv() (*DbNFCPatrolLineList, error)
	grpc.ClientStream
}

type rpcDbNFCPatrolLineQueryBatchClient struct {
	grpc.ClientStream
}

func (x *rpcDbNFCPatrolLineQueryBatchClient) Recv() (*DbNFCPatrolLineList, error) {
	m := new(DbNFCPatrolLineList)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// RpcDbNFCPatrolLineServer is the server API for RpcDbNFCPatrolLine service.
type RpcDbNFCPatrolLineServer interface {
	//插入一行数据
	Insert(context.Context, *DbNFCPatrolLine) (*crud.DMLResult, error)
	//以RID为条件,全量修改一行数据
	Update(context.Context, *DbNFCPatrolLine) (*crud.DMLResult, error)
	//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
	PartialUpdate(context.Context, *DbNFCPatrolLine) (*crud.DMLResult, error)
	//以RID为条件,删除一行数据
	Delete(context.Context, *DbNFCPatrolLine) (*crud.DMLResult, error)
	//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
	SelectOne(context.Context, *crud.DMLParam) (*DbNFCPatrolLine, error)
	//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
	SelectMany(*crud.DMLParam, RpcDbNFCPatrolLine_SelectManyServer) error
	//根据条件查询数据
	Query(*crud.QueryParam, RpcDbNFCPatrolLine_QueryServer) error
	//根据条件查询数据,批量返回数据,crud.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(*crud.QueryParam, RpcDbNFCPatrolLine_QueryBatchServer) error
}

// UnimplementedRpcDbNFCPatrolLineServer can be embedded to have forward compatible implementations.
type UnimplementedRpcDbNFCPatrolLineServer struct {
}

func (*UnimplementedRpcDbNFCPatrolLineServer) Insert(ctx context.Context, req *DbNFCPatrolLine) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Insert not implemented")
}
func (*UnimplementedRpcDbNFCPatrolLineServer) Update(ctx context.Context, req *DbNFCPatrolLine) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Update not implemented")
}
func (*UnimplementedRpcDbNFCPatrolLineServer) PartialUpdate(ctx context.Context, req *DbNFCPatrolLine) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PartialUpdate not implemented")
}
func (*UnimplementedRpcDbNFCPatrolLineServer) Delete(ctx context.Context, req *DbNFCPatrolLine) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Delete not implemented")
}
func (*UnimplementedRpcDbNFCPatrolLineServer) SelectOne(ctx context.Context, req *crud.DMLParam) (*DbNFCPatrolLine, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SelectOne not implemented")
}
func (*UnimplementedRpcDbNFCPatrolLineServer) SelectMany(req *crud.DMLParam, srv RpcDbNFCPatrolLine_SelectManyServer) error {
	return status.Errorf(codes.Unimplemented, "method SelectMany not implemented")
}
func (*UnimplementedRpcDbNFCPatrolLineServer) Query(req *crud.QueryParam, srv RpcDbNFCPatrolLine_QueryServer) error {
	return status.Errorf(codes.Unimplemented, "method Query not implemented")
}
func (*UnimplementedRpcDbNFCPatrolLineServer) QueryBatch(req *crud.QueryParam, srv RpcDbNFCPatrolLine_QueryBatchServer) error {
	return status.Errorf(codes.Unimplemented, "method QueryBatch not implemented")
}

func RegisterRpcDbNFCPatrolLineServer(s *grpc.Server, srv RpcDbNFCPatrolLineServer) {
	s.RegisterService(&_RpcDbNFCPatrolLine_serviceDesc, srv)
}

func _RpcDbNFCPatrolLine_Insert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbNFCPatrolLine)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbNFCPatrolLineServer).Insert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.RpcDbNFCPatrolLine/Insert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbNFCPatrolLineServer).Insert(ctx, req.(*DbNFCPatrolLine))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbNFCPatrolLine_Update_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbNFCPatrolLine)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbNFCPatrolLineServer).Update(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.RpcDbNFCPatrolLine/Update",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbNFCPatrolLineServer).Update(ctx, req.(*DbNFCPatrolLine))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbNFCPatrolLine_PartialUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbNFCPatrolLine)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbNFCPatrolLineServer).PartialUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.RpcDbNFCPatrolLine/PartialUpdate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbNFCPatrolLineServer).PartialUpdate(ctx, req.(*DbNFCPatrolLine))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbNFCPatrolLine_Delete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbNFCPatrolLine)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbNFCPatrolLineServer).Delete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.RpcDbNFCPatrolLine/Delete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbNFCPatrolLineServer).Delete(ctx, req.(*DbNFCPatrolLine))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbNFCPatrolLine_SelectOne_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(crud.DMLParam)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbNFCPatrolLineServer).SelectOne(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.RpcDbNFCPatrolLine/SelectOne",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbNFCPatrolLineServer).SelectOne(ctx, req.(*crud.DMLParam))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbNFCPatrolLine_SelectMany_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.DMLParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RpcDbNFCPatrolLineServer).SelectMany(m, &rpcDbNFCPatrolLineSelectManyServer{stream})
}

type RpcDbNFCPatrolLine_SelectManyServer interface {
	Send(*DbNFCPatrolLine) error
	grpc.ServerStream
}

type rpcDbNFCPatrolLineSelectManyServer struct {
	grpc.ServerStream
}

func (x *rpcDbNFCPatrolLineSelectManyServer) Send(m *DbNFCPatrolLine) error {
	return x.ServerStream.SendMsg(m)
}

func _RpcDbNFCPatrolLine_Query_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.QueryParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RpcDbNFCPatrolLineServer).Query(m, &rpcDbNFCPatrolLineQueryServer{stream})
}

type RpcDbNFCPatrolLine_QueryServer interface {
	Send(*DbNFCPatrolLine) error
	grpc.ServerStream
}

type rpcDbNFCPatrolLineQueryServer struct {
	grpc.ServerStream
}

func (x *rpcDbNFCPatrolLineQueryServer) Send(m *DbNFCPatrolLine) error {
	return x.ServerStream.SendMsg(m)
}

func _RpcDbNFCPatrolLine_QueryBatch_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.QueryParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RpcDbNFCPatrolLineServer).QueryBatch(m, &rpcDbNFCPatrolLineQueryBatchServer{stream})
}

type RpcDbNFCPatrolLine_QueryBatchServer interface {
	Send(*DbNFCPatrolLineList) error
	grpc.ServerStream
}

type rpcDbNFCPatrolLineQueryBatchServer struct {
	grpc.ServerStream
}

func (x *rpcDbNFCPatrolLineQueryBatchServer) Send(m *DbNFCPatrolLineList) error {
	return x.ServerStream.SendMsg(m)
}

var _RpcDbNFCPatrolLine_serviceDesc = grpc.ServiceDesc{
	ServiceName: "bysdb.RpcDbNFCPatrolLine",
	HandlerType: (*RpcDbNFCPatrolLineServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Insert",
			Handler:    _RpcDbNFCPatrolLine_Insert_Handler,
		},
		{
			MethodName: "Update",
			Handler:    _RpcDbNFCPatrolLine_Update_Handler,
		},
		{
			MethodName: "PartialUpdate",
			Handler:    _RpcDbNFCPatrolLine_PartialUpdate_Handler,
		},
		{
			MethodName: "Delete",
			Handler:    _RpcDbNFCPatrolLine_Delete_Handler,
		},
		{
			MethodName: "SelectOne",
			Handler:    _RpcDbNFCPatrolLine_SelectOne_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "SelectMany",
			Handler:       _RpcDbNFCPatrolLine_SelectMany_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "Query",
			Handler:       _RpcDbNFCPatrolLine_Query_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "QueryBatch",
			Handler:       _RpcDbNFCPatrolLine_QueryBatch_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "bysdb.rpc.proto",
}

// PrpcDbNFCPatrolLineClient is the client API for PrpcDbNFCPatrolLine service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type PrpcDbNFCPatrolLineClient interface {
	//插入一行数据
	Insert(ctx context.Context, in *DbNFCPatrolLine, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,全量修改一行数据
	Update(ctx context.Context, in *DbNFCPatrolLine, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
	PartialUpdate(ctx context.Context, in *DbNFCPatrolLine, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,删除一行数据
	Delete(ctx context.Context, in *DbNFCPatrolLine, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
	SelectOne(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (*DbNFCPatrolLine, error)
	//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
	SelectMany(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (PrpcDbNFCPatrolLine_SelectManyClient, error)
	//根据条件取得我有权限的数据
	Query(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbNFCPatrolLine_QueryClient, error)
	//根据条件取得我有权限的数据,批量返回数据,crud.PrivilegeParam.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbNFCPatrolLine_QueryBatchClient, error)
}

type prpcDbNFCPatrolLineClient struct {
	cc *grpc.ClientConn
}

func NewPrpcDbNFCPatrolLineClient(cc *grpc.ClientConn) PrpcDbNFCPatrolLineClient {
	return &prpcDbNFCPatrolLineClient{cc}
}

func (c *prpcDbNFCPatrolLineClient) Insert(ctx context.Context, in *DbNFCPatrolLine, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/bysdb.PrpcDbNFCPatrolLine/Insert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prpcDbNFCPatrolLineClient) Update(ctx context.Context, in *DbNFCPatrolLine, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/bysdb.PrpcDbNFCPatrolLine/Update", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prpcDbNFCPatrolLineClient) PartialUpdate(ctx context.Context, in *DbNFCPatrolLine, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/bysdb.PrpcDbNFCPatrolLine/PartialUpdate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prpcDbNFCPatrolLineClient) Delete(ctx context.Context, in *DbNFCPatrolLine, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/bysdb.PrpcDbNFCPatrolLine/Delete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prpcDbNFCPatrolLineClient) SelectOne(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (*DbNFCPatrolLine, error) {
	out := new(DbNFCPatrolLine)
	err := c.cc.Invoke(ctx, "/bysdb.PrpcDbNFCPatrolLine/SelectOne", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prpcDbNFCPatrolLineClient) SelectMany(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (PrpcDbNFCPatrolLine_SelectManyClient, error) {
	stream, err := c.cc.NewStream(ctx, &_PrpcDbNFCPatrolLine_serviceDesc.Streams[0], "/bysdb.PrpcDbNFCPatrolLine/SelectMany", opts...)
	if err != nil {
		return nil, err
	}
	x := &prpcDbNFCPatrolLineSelectManyClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type PrpcDbNFCPatrolLine_SelectManyClient interface {
	Recv() (*DbNFCPatrolLine, error)
	grpc.ClientStream
}

type prpcDbNFCPatrolLineSelectManyClient struct {
	grpc.ClientStream
}

func (x *prpcDbNFCPatrolLineSelectManyClient) Recv() (*DbNFCPatrolLine, error) {
	m := new(DbNFCPatrolLine)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *prpcDbNFCPatrolLineClient) Query(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbNFCPatrolLine_QueryClient, error) {
	stream, err := c.cc.NewStream(ctx, &_PrpcDbNFCPatrolLine_serviceDesc.Streams[1], "/bysdb.PrpcDbNFCPatrolLine/Query", opts...)
	if err != nil {
		return nil, err
	}
	x := &prpcDbNFCPatrolLineQueryClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type PrpcDbNFCPatrolLine_QueryClient interface {
	Recv() (*DbNFCPatrolLine, error)
	grpc.ClientStream
}

type prpcDbNFCPatrolLineQueryClient struct {
	grpc.ClientStream
}

func (x *prpcDbNFCPatrolLineQueryClient) Recv() (*DbNFCPatrolLine, error) {
	m := new(DbNFCPatrolLine)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *prpcDbNFCPatrolLineClient) QueryBatch(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbNFCPatrolLine_QueryBatchClient, error) {
	stream, err := c.cc.NewStream(ctx, &_PrpcDbNFCPatrolLine_serviceDesc.Streams[2], "/bysdb.PrpcDbNFCPatrolLine/QueryBatch", opts...)
	if err != nil {
		return nil, err
	}
	x := &prpcDbNFCPatrolLineQueryBatchClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type PrpcDbNFCPatrolLine_QueryBatchClient interface {
	Recv() (*DbNFCPatrolLineList, error)
	grpc.ClientStream
}

type prpcDbNFCPatrolLineQueryBatchClient struct {
	grpc.ClientStream
}

func (x *prpcDbNFCPatrolLineQueryBatchClient) Recv() (*DbNFCPatrolLineList, error) {
	m := new(DbNFCPatrolLineList)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// PrpcDbNFCPatrolLineServer is the server API for PrpcDbNFCPatrolLine service.
type PrpcDbNFCPatrolLineServer interface {
	//插入一行数据
	Insert(context.Context, *DbNFCPatrolLine) (*crud.DMLResult, error)
	//以RID为条件,全量修改一行数据
	Update(context.Context, *DbNFCPatrolLine) (*crud.DMLResult, error)
	//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
	PartialUpdate(context.Context, *DbNFCPatrolLine) (*crud.DMLResult, error)
	//以RID为条件,删除一行数据
	Delete(context.Context, *DbNFCPatrolLine) (*crud.DMLResult, error)
	//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
	SelectOne(context.Context, *crud.DMLParam) (*DbNFCPatrolLine, error)
	//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
	SelectMany(*crud.DMLParam, PrpcDbNFCPatrolLine_SelectManyServer) error
	//根据条件取得我有权限的数据
	Query(*crud.PrivilegeParam, PrpcDbNFCPatrolLine_QueryServer) error
	//根据条件取得我有权限的数据,批量返回数据,crud.PrivilegeParam.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(*crud.PrivilegeParam, PrpcDbNFCPatrolLine_QueryBatchServer) error
}

// UnimplementedPrpcDbNFCPatrolLineServer can be embedded to have forward compatible implementations.
type UnimplementedPrpcDbNFCPatrolLineServer struct {
}

func (*UnimplementedPrpcDbNFCPatrolLineServer) Insert(ctx context.Context, req *DbNFCPatrolLine) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Insert not implemented")
}
func (*UnimplementedPrpcDbNFCPatrolLineServer) Update(ctx context.Context, req *DbNFCPatrolLine) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Update not implemented")
}
func (*UnimplementedPrpcDbNFCPatrolLineServer) PartialUpdate(ctx context.Context, req *DbNFCPatrolLine) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PartialUpdate not implemented")
}
func (*UnimplementedPrpcDbNFCPatrolLineServer) Delete(ctx context.Context, req *DbNFCPatrolLine) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Delete not implemented")
}
func (*UnimplementedPrpcDbNFCPatrolLineServer) SelectOne(ctx context.Context, req *crud.DMLParam) (*DbNFCPatrolLine, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SelectOne not implemented")
}
func (*UnimplementedPrpcDbNFCPatrolLineServer) SelectMany(req *crud.DMLParam, srv PrpcDbNFCPatrolLine_SelectManyServer) error {
	return status.Errorf(codes.Unimplemented, "method SelectMany not implemented")
}
func (*UnimplementedPrpcDbNFCPatrolLineServer) Query(req *crud.PrivilegeParam, srv PrpcDbNFCPatrolLine_QueryServer) error {
	return status.Errorf(codes.Unimplemented, "method Query not implemented")
}
func (*UnimplementedPrpcDbNFCPatrolLineServer) QueryBatch(req *crud.PrivilegeParam, srv PrpcDbNFCPatrolLine_QueryBatchServer) error {
	return status.Errorf(codes.Unimplemented, "method QueryBatch not implemented")
}

func RegisterPrpcDbNFCPatrolLineServer(s *grpc.Server, srv PrpcDbNFCPatrolLineServer) {
	s.RegisterService(&_PrpcDbNFCPatrolLine_serviceDesc, srv)
}

func _PrpcDbNFCPatrolLine_Insert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbNFCPatrolLine)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrpcDbNFCPatrolLineServer).Insert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.PrpcDbNFCPatrolLine/Insert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrpcDbNFCPatrolLineServer).Insert(ctx, req.(*DbNFCPatrolLine))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrpcDbNFCPatrolLine_Update_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbNFCPatrolLine)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrpcDbNFCPatrolLineServer).Update(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.PrpcDbNFCPatrolLine/Update",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrpcDbNFCPatrolLineServer).Update(ctx, req.(*DbNFCPatrolLine))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrpcDbNFCPatrolLine_PartialUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbNFCPatrolLine)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrpcDbNFCPatrolLineServer).PartialUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.PrpcDbNFCPatrolLine/PartialUpdate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrpcDbNFCPatrolLineServer).PartialUpdate(ctx, req.(*DbNFCPatrolLine))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrpcDbNFCPatrolLine_Delete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbNFCPatrolLine)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrpcDbNFCPatrolLineServer).Delete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.PrpcDbNFCPatrolLine/Delete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrpcDbNFCPatrolLineServer).Delete(ctx, req.(*DbNFCPatrolLine))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrpcDbNFCPatrolLine_SelectOne_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(crud.DMLParam)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrpcDbNFCPatrolLineServer).SelectOne(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.PrpcDbNFCPatrolLine/SelectOne",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrpcDbNFCPatrolLineServer).SelectOne(ctx, req.(*crud.DMLParam))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrpcDbNFCPatrolLine_SelectMany_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.DMLParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(PrpcDbNFCPatrolLineServer).SelectMany(m, &prpcDbNFCPatrolLineSelectManyServer{stream})
}

type PrpcDbNFCPatrolLine_SelectManyServer interface {
	Send(*DbNFCPatrolLine) error
	grpc.ServerStream
}

type prpcDbNFCPatrolLineSelectManyServer struct {
	grpc.ServerStream
}

func (x *prpcDbNFCPatrolLineSelectManyServer) Send(m *DbNFCPatrolLine) error {
	return x.ServerStream.SendMsg(m)
}

func _PrpcDbNFCPatrolLine_Query_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.PrivilegeParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(PrpcDbNFCPatrolLineServer).Query(m, &prpcDbNFCPatrolLineQueryServer{stream})
}

type PrpcDbNFCPatrolLine_QueryServer interface {
	Send(*DbNFCPatrolLine) error
	grpc.ServerStream
}

type prpcDbNFCPatrolLineQueryServer struct {
	grpc.ServerStream
}

func (x *prpcDbNFCPatrolLineQueryServer) Send(m *DbNFCPatrolLine) error {
	return x.ServerStream.SendMsg(m)
}

func _PrpcDbNFCPatrolLine_QueryBatch_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.PrivilegeParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(PrpcDbNFCPatrolLineServer).QueryBatch(m, &prpcDbNFCPatrolLineQueryBatchServer{stream})
}

type PrpcDbNFCPatrolLine_QueryBatchServer interface {
	Send(*DbNFCPatrolLineList) error
	grpc.ServerStream
}

type prpcDbNFCPatrolLineQueryBatchServer struct {
	grpc.ServerStream
}

func (x *prpcDbNFCPatrolLineQueryBatchServer) Send(m *DbNFCPatrolLineList) error {
	return x.ServerStream.SendMsg(m)
}

var _PrpcDbNFCPatrolLine_serviceDesc = grpc.ServiceDesc{
	ServiceName: "bysdb.PrpcDbNFCPatrolLine",
	HandlerType: (*PrpcDbNFCPatrolLineServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Insert",
			Handler:    _PrpcDbNFCPatrolLine_Insert_Handler,
		},
		{
			MethodName: "Update",
			Handler:    _PrpcDbNFCPatrolLine_Update_Handler,
		},
		{
			MethodName: "PartialUpdate",
			Handler:    _PrpcDbNFCPatrolLine_PartialUpdate_Handler,
		},
		{
			MethodName: "Delete",
			Handler:    _PrpcDbNFCPatrolLine_Delete_Handler,
		},
		{
			MethodName: "SelectOne",
			Handler:    _PrpcDbNFCPatrolLine_SelectOne_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "SelectMany",
			Handler:       _PrpcDbNFCPatrolLine_SelectMany_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "Query",
			Handler:       _PrpcDbNFCPatrolLine_Query_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "QueryBatch",
			Handler:       _PrpcDbNFCPatrolLine_QueryBatch_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "bysdb.rpc.proto",
}

// RpcDbNFCPatrolLineDetailClient is the client API for RpcDbNFCPatrolLineDetail service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type RpcDbNFCPatrolLineDetailClient interface {
	//插入一行数据
	Insert(ctx context.Context, in *DbNFCPatrolLineDetail, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,全量修改一行数据
	Update(ctx context.Context, in *DbNFCPatrolLineDetail, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
	PartialUpdate(ctx context.Context, in *DbNFCPatrolLineDetail, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,删除一行数据
	Delete(ctx context.Context, in *DbNFCPatrolLineDetail, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
	SelectOne(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (*DbNFCPatrolLineDetail, error)
	//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
	SelectMany(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (RpcDbNFCPatrolLineDetail_SelectManyClient, error)
	//根据条件查询数据
	Query(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbNFCPatrolLineDetail_QueryClient, error)
	//根据条件查询数据,批量返回数据,crud.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbNFCPatrolLineDetail_QueryBatchClient, error)
}

type rpcDbNFCPatrolLineDetailClient struct {
	cc *grpc.ClientConn
}

func NewRpcDbNFCPatrolLineDetailClient(cc *grpc.ClientConn) RpcDbNFCPatrolLineDetailClient {
	return &rpcDbNFCPatrolLineDetailClient{cc}
}

func (c *rpcDbNFCPatrolLineDetailClient) Insert(ctx context.Context, in *DbNFCPatrolLineDetail, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/bysdb.RpcDbNFCPatrolLineDetail/Insert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbNFCPatrolLineDetailClient) Update(ctx context.Context, in *DbNFCPatrolLineDetail, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/bysdb.RpcDbNFCPatrolLineDetail/Update", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbNFCPatrolLineDetailClient) PartialUpdate(ctx context.Context, in *DbNFCPatrolLineDetail, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/bysdb.RpcDbNFCPatrolLineDetail/PartialUpdate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbNFCPatrolLineDetailClient) Delete(ctx context.Context, in *DbNFCPatrolLineDetail, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/bysdb.RpcDbNFCPatrolLineDetail/Delete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbNFCPatrolLineDetailClient) SelectOne(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (*DbNFCPatrolLineDetail, error) {
	out := new(DbNFCPatrolLineDetail)
	err := c.cc.Invoke(ctx, "/bysdb.RpcDbNFCPatrolLineDetail/SelectOne", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbNFCPatrolLineDetailClient) SelectMany(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (RpcDbNFCPatrolLineDetail_SelectManyClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RpcDbNFCPatrolLineDetail_serviceDesc.Streams[0], "/bysdb.RpcDbNFCPatrolLineDetail/SelectMany", opts...)
	if err != nil {
		return nil, err
	}
	x := &rpcDbNFCPatrolLineDetailSelectManyClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type RpcDbNFCPatrolLineDetail_SelectManyClient interface {
	Recv() (*DbNFCPatrolLineDetail, error)
	grpc.ClientStream
}

type rpcDbNFCPatrolLineDetailSelectManyClient struct {
	grpc.ClientStream
}

func (x *rpcDbNFCPatrolLineDetailSelectManyClient) Recv() (*DbNFCPatrolLineDetail, error) {
	m := new(DbNFCPatrolLineDetail)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *rpcDbNFCPatrolLineDetailClient) Query(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbNFCPatrolLineDetail_QueryClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RpcDbNFCPatrolLineDetail_serviceDesc.Streams[1], "/bysdb.RpcDbNFCPatrolLineDetail/Query", opts...)
	if err != nil {
		return nil, err
	}
	x := &rpcDbNFCPatrolLineDetailQueryClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type RpcDbNFCPatrolLineDetail_QueryClient interface {
	Recv() (*DbNFCPatrolLineDetail, error)
	grpc.ClientStream
}

type rpcDbNFCPatrolLineDetailQueryClient struct {
	grpc.ClientStream
}

func (x *rpcDbNFCPatrolLineDetailQueryClient) Recv() (*DbNFCPatrolLineDetail, error) {
	m := new(DbNFCPatrolLineDetail)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *rpcDbNFCPatrolLineDetailClient) QueryBatch(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbNFCPatrolLineDetail_QueryBatchClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RpcDbNFCPatrolLineDetail_serviceDesc.Streams[2], "/bysdb.RpcDbNFCPatrolLineDetail/QueryBatch", opts...)
	if err != nil {
		return nil, err
	}
	x := &rpcDbNFCPatrolLineDetailQueryBatchClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type RpcDbNFCPatrolLineDetail_QueryBatchClient interface {
	Recv() (*DbNFCPatrolLineDetailList, error)
	grpc.ClientStream
}

type rpcDbNFCPatrolLineDetailQueryBatchClient struct {
	grpc.ClientStream
}

func (x *rpcDbNFCPatrolLineDetailQueryBatchClient) Recv() (*DbNFCPatrolLineDetailList, error) {
	m := new(DbNFCPatrolLineDetailList)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// RpcDbNFCPatrolLineDetailServer is the server API for RpcDbNFCPatrolLineDetail service.
type RpcDbNFCPatrolLineDetailServer interface {
	//插入一行数据
	Insert(context.Context, *DbNFCPatrolLineDetail) (*crud.DMLResult, error)
	//以RID为条件,全量修改一行数据
	Update(context.Context, *DbNFCPatrolLineDetail) (*crud.DMLResult, error)
	//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
	PartialUpdate(context.Context, *DbNFCPatrolLineDetail) (*crud.DMLResult, error)
	//以RID为条件,删除一行数据
	Delete(context.Context, *DbNFCPatrolLineDetail) (*crud.DMLResult, error)
	//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
	SelectOne(context.Context, *crud.DMLParam) (*DbNFCPatrolLineDetail, error)
	//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
	SelectMany(*crud.DMLParam, RpcDbNFCPatrolLineDetail_SelectManyServer) error
	//根据条件查询数据
	Query(*crud.QueryParam, RpcDbNFCPatrolLineDetail_QueryServer) error
	//根据条件查询数据,批量返回数据,crud.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(*crud.QueryParam, RpcDbNFCPatrolLineDetail_QueryBatchServer) error
}

// UnimplementedRpcDbNFCPatrolLineDetailServer can be embedded to have forward compatible implementations.
type UnimplementedRpcDbNFCPatrolLineDetailServer struct {
}

func (*UnimplementedRpcDbNFCPatrolLineDetailServer) Insert(ctx context.Context, req *DbNFCPatrolLineDetail) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Insert not implemented")
}
func (*UnimplementedRpcDbNFCPatrolLineDetailServer) Update(ctx context.Context, req *DbNFCPatrolLineDetail) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Update not implemented")
}
func (*UnimplementedRpcDbNFCPatrolLineDetailServer) PartialUpdate(ctx context.Context, req *DbNFCPatrolLineDetail) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PartialUpdate not implemented")
}
func (*UnimplementedRpcDbNFCPatrolLineDetailServer) Delete(ctx context.Context, req *DbNFCPatrolLineDetail) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Delete not implemented")
}
func (*UnimplementedRpcDbNFCPatrolLineDetailServer) SelectOne(ctx context.Context, req *crud.DMLParam) (*DbNFCPatrolLineDetail, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SelectOne not implemented")
}
func (*UnimplementedRpcDbNFCPatrolLineDetailServer) SelectMany(req *crud.DMLParam, srv RpcDbNFCPatrolLineDetail_SelectManyServer) error {
	return status.Errorf(codes.Unimplemented, "method SelectMany not implemented")
}
func (*UnimplementedRpcDbNFCPatrolLineDetailServer) Query(req *crud.QueryParam, srv RpcDbNFCPatrolLineDetail_QueryServer) error {
	return status.Errorf(codes.Unimplemented, "method Query not implemented")
}
func (*UnimplementedRpcDbNFCPatrolLineDetailServer) QueryBatch(req *crud.QueryParam, srv RpcDbNFCPatrolLineDetail_QueryBatchServer) error {
	return status.Errorf(codes.Unimplemented, "method QueryBatch not implemented")
}

func RegisterRpcDbNFCPatrolLineDetailServer(s *grpc.Server, srv RpcDbNFCPatrolLineDetailServer) {
	s.RegisterService(&_RpcDbNFCPatrolLineDetail_serviceDesc, srv)
}

func _RpcDbNFCPatrolLineDetail_Insert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbNFCPatrolLineDetail)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbNFCPatrolLineDetailServer).Insert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.RpcDbNFCPatrolLineDetail/Insert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbNFCPatrolLineDetailServer).Insert(ctx, req.(*DbNFCPatrolLineDetail))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbNFCPatrolLineDetail_Update_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbNFCPatrolLineDetail)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbNFCPatrolLineDetailServer).Update(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.RpcDbNFCPatrolLineDetail/Update",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbNFCPatrolLineDetailServer).Update(ctx, req.(*DbNFCPatrolLineDetail))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbNFCPatrolLineDetail_PartialUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbNFCPatrolLineDetail)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbNFCPatrolLineDetailServer).PartialUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.RpcDbNFCPatrolLineDetail/PartialUpdate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbNFCPatrolLineDetailServer).PartialUpdate(ctx, req.(*DbNFCPatrolLineDetail))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbNFCPatrolLineDetail_Delete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbNFCPatrolLineDetail)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbNFCPatrolLineDetailServer).Delete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.RpcDbNFCPatrolLineDetail/Delete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbNFCPatrolLineDetailServer).Delete(ctx, req.(*DbNFCPatrolLineDetail))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbNFCPatrolLineDetail_SelectOne_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(crud.DMLParam)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbNFCPatrolLineDetailServer).SelectOne(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.RpcDbNFCPatrolLineDetail/SelectOne",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbNFCPatrolLineDetailServer).SelectOne(ctx, req.(*crud.DMLParam))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbNFCPatrolLineDetail_SelectMany_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.DMLParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RpcDbNFCPatrolLineDetailServer).SelectMany(m, &rpcDbNFCPatrolLineDetailSelectManyServer{stream})
}

type RpcDbNFCPatrolLineDetail_SelectManyServer interface {
	Send(*DbNFCPatrolLineDetail) error
	grpc.ServerStream
}

type rpcDbNFCPatrolLineDetailSelectManyServer struct {
	grpc.ServerStream
}

func (x *rpcDbNFCPatrolLineDetailSelectManyServer) Send(m *DbNFCPatrolLineDetail) error {
	return x.ServerStream.SendMsg(m)
}

func _RpcDbNFCPatrolLineDetail_Query_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.QueryParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RpcDbNFCPatrolLineDetailServer).Query(m, &rpcDbNFCPatrolLineDetailQueryServer{stream})
}

type RpcDbNFCPatrolLineDetail_QueryServer interface {
	Send(*DbNFCPatrolLineDetail) error
	grpc.ServerStream
}

type rpcDbNFCPatrolLineDetailQueryServer struct {
	grpc.ServerStream
}

func (x *rpcDbNFCPatrolLineDetailQueryServer) Send(m *DbNFCPatrolLineDetail) error {
	return x.ServerStream.SendMsg(m)
}

func _RpcDbNFCPatrolLineDetail_QueryBatch_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.QueryParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RpcDbNFCPatrolLineDetailServer).QueryBatch(m, &rpcDbNFCPatrolLineDetailQueryBatchServer{stream})
}

type RpcDbNFCPatrolLineDetail_QueryBatchServer interface {
	Send(*DbNFCPatrolLineDetailList) error
	grpc.ServerStream
}

type rpcDbNFCPatrolLineDetailQueryBatchServer struct {
	grpc.ServerStream
}

func (x *rpcDbNFCPatrolLineDetailQueryBatchServer) Send(m *DbNFCPatrolLineDetailList) error {
	return x.ServerStream.SendMsg(m)
}

var _RpcDbNFCPatrolLineDetail_serviceDesc = grpc.ServiceDesc{
	ServiceName: "bysdb.RpcDbNFCPatrolLineDetail",
	HandlerType: (*RpcDbNFCPatrolLineDetailServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Insert",
			Handler:    _RpcDbNFCPatrolLineDetail_Insert_Handler,
		},
		{
			MethodName: "Update",
			Handler:    _RpcDbNFCPatrolLineDetail_Update_Handler,
		},
		{
			MethodName: "PartialUpdate",
			Handler:    _RpcDbNFCPatrolLineDetail_PartialUpdate_Handler,
		},
		{
			MethodName: "Delete",
			Handler:    _RpcDbNFCPatrolLineDetail_Delete_Handler,
		},
		{
			MethodName: "SelectOne",
			Handler:    _RpcDbNFCPatrolLineDetail_SelectOne_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "SelectMany",
			Handler:       _RpcDbNFCPatrolLineDetail_SelectMany_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "Query",
			Handler:       _RpcDbNFCPatrolLineDetail_Query_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "QueryBatch",
			Handler:       _RpcDbNFCPatrolLineDetail_QueryBatch_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "bysdb.rpc.proto",
}

// PrpcDbNFCPatrolLineDetailClient is the client API for PrpcDbNFCPatrolLineDetail service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type PrpcDbNFCPatrolLineDetailClient interface {
	//插入一行数据
	Insert(ctx context.Context, in *DbNFCPatrolLineDetail, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,全量修改一行数据
	Update(ctx context.Context, in *DbNFCPatrolLineDetail, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
	PartialUpdate(ctx context.Context, in *DbNFCPatrolLineDetail, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,删除一行数据
	Delete(ctx context.Context, in *DbNFCPatrolLineDetail, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
	SelectOne(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (*DbNFCPatrolLineDetail, error)
	//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
	SelectMany(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (PrpcDbNFCPatrolLineDetail_SelectManyClient, error)
	//根据条件取得我有权限的数据
	Query(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbNFCPatrolLineDetail_QueryClient, error)
	//根据条件取得我有权限的数据,批量返回数据,crud.PrivilegeParam.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbNFCPatrolLineDetail_QueryBatchClient, error)
}

type prpcDbNFCPatrolLineDetailClient struct {
	cc *grpc.ClientConn
}

func NewPrpcDbNFCPatrolLineDetailClient(cc *grpc.ClientConn) PrpcDbNFCPatrolLineDetailClient {
	return &prpcDbNFCPatrolLineDetailClient{cc}
}

func (c *prpcDbNFCPatrolLineDetailClient) Insert(ctx context.Context, in *DbNFCPatrolLineDetail, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/bysdb.PrpcDbNFCPatrolLineDetail/Insert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prpcDbNFCPatrolLineDetailClient) Update(ctx context.Context, in *DbNFCPatrolLineDetail, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/bysdb.PrpcDbNFCPatrolLineDetail/Update", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prpcDbNFCPatrolLineDetailClient) PartialUpdate(ctx context.Context, in *DbNFCPatrolLineDetail, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/bysdb.PrpcDbNFCPatrolLineDetail/PartialUpdate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prpcDbNFCPatrolLineDetailClient) Delete(ctx context.Context, in *DbNFCPatrolLineDetail, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/bysdb.PrpcDbNFCPatrolLineDetail/Delete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prpcDbNFCPatrolLineDetailClient) SelectOne(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (*DbNFCPatrolLineDetail, error) {
	out := new(DbNFCPatrolLineDetail)
	err := c.cc.Invoke(ctx, "/bysdb.PrpcDbNFCPatrolLineDetail/SelectOne", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prpcDbNFCPatrolLineDetailClient) SelectMany(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (PrpcDbNFCPatrolLineDetail_SelectManyClient, error) {
	stream, err := c.cc.NewStream(ctx, &_PrpcDbNFCPatrolLineDetail_serviceDesc.Streams[0], "/bysdb.PrpcDbNFCPatrolLineDetail/SelectMany", opts...)
	if err != nil {
		return nil, err
	}
	x := &prpcDbNFCPatrolLineDetailSelectManyClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type PrpcDbNFCPatrolLineDetail_SelectManyClient interface {
	Recv() (*DbNFCPatrolLineDetail, error)
	grpc.ClientStream
}

type prpcDbNFCPatrolLineDetailSelectManyClient struct {
	grpc.ClientStream
}

func (x *prpcDbNFCPatrolLineDetailSelectManyClient) Recv() (*DbNFCPatrolLineDetail, error) {
	m := new(DbNFCPatrolLineDetail)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *prpcDbNFCPatrolLineDetailClient) Query(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbNFCPatrolLineDetail_QueryClient, error) {
	stream, err := c.cc.NewStream(ctx, &_PrpcDbNFCPatrolLineDetail_serviceDesc.Streams[1], "/bysdb.PrpcDbNFCPatrolLineDetail/Query", opts...)
	if err != nil {
		return nil, err
	}
	x := &prpcDbNFCPatrolLineDetailQueryClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type PrpcDbNFCPatrolLineDetail_QueryClient interface {
	Recv() (*DbNFCPatrolLineDetail, error)
	grpc.ClientStream
}

type prpcDbNFCPatrolLineDetailQueryClient struct {
	grpc.ClientStream
}

func (x *prpcDbNFCPatrolLineDetailQueryClient) Recv() (*DbNFCPatrolLineDetail, error) {
	m := new(DbNFCPatrolLineDetail)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *prpcDbNFCPatrolLineDetailClient) QueryBatch(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbNFCPatrolLineDetail_QueryBatchClient, error) {
	stream, err := c.cc.NewStream(ctx, &_PrpcDbNFCPatrolLineDetail_serviceDesc.Streams[2], "/bysdb.PrpcDbNFCPatrolLineDetail/QueryBatch", opts...)
	if err != nil {
		return nil, err
	}
	x := &prpcDbNFCPatrolLineDetailQueryBatchClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type PrpcDbNFCPatrolLineDetail_QueryBatchClient interface {
	Recv() (*DbNFCPatrolLineDetailList, error)
	grpc.ClientStream
}

type prpcDbNFCPatrolLineDetailQueryBatchClient struct {
	grpc.ClientStream
}

func (x *prpcDbNFCPatrolLineDetailQueryBatchClient) Recv() (*DbNFCPatrolLineDetailList, error) {
	m := new(DbNFCPatrolLineDetailList)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// PrpcDbNFCPatrolLineDetailServer is the server API for PrpcDbNFCPatrolLineDetail service.
type PrpcDbNFCPatrolLineDetailServer interface {
	//插入一行数据
	Insert(context.Context, *DbNFCPatrolLineDetail) (*crud.DMLResult, error)
	//以RID为条件,全量修改一行数据
	Update(context.Context, *DbNFCPatrolLineDetail) (*crud.DMLResult, error)
	//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
	PartialUpdate(context.Context, *DbNFCPatrolLineDetail) (*crud.DMLResult, error)
	//以RID为条件,删除一行数据
	Delete(context.Context, *DbNFCPatrolLineDetail) (*crud.DMLResult, error)
	//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
	SelectOne(context.Context, *crud.DMLParam) (*DbNFCPatrolLineDetail, error)
	//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
	SelectMany(*crud.DMLParam, PrpcDbNFCPatrolLineDetail_SelectManyServer) error
	//根据条件取得我有权限的数据
	Query(*crud.PrivilegeParam, PrpcDbNFCPatrolLineDetail_QueryServer) error
	//根据条件取得我有权限的数据,批量返回数据,crud.PrivilegeParam.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(*crud.PrivilegeParam, PrpcDbNFCPatrolLineDetail_QueryBatchServer) error
}

// UnimplementedPrpcDbNFCPatrolLineDetailServer can be embedded to have forward compatible implementations.
type UnimplementedPrpcDbNFCPatrolLineDetailServer struct {
}

func (*UnimplementedPrpcDbNFCPatrolLineDetailServer) Insert(ctx context.Context, req *DbNFCPatrolLineDetail) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Insert not implemented")
}
func (*UnimplementedPrpcDbNFCPatrolLineDetailServer) Update(ctx context.Context, req *DbNFCPatrolLineDetail) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Update not implemented")
}
func (*UnimplementedPrpcDbNFCPatrolLineDetailServer) PartialUpdate(ctx context.Context, req *DbNFCPatrolLineDetail) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PartialUpdate not implemented")
}
func (*UnimplementedPrpcDbNFCPatrolLineDetailServer) Delete(ctx context.Context, req *DbNFCPatrolLineDetail) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Delete not implemented")
}
func (*UnimplementedPrpcDbNFCPatrolLineDetailServer) SelectOne(ctx context.Context, req *crud.DMLParam) (*DbNFCPatrolLineDetail, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SelectOne not implemented")
}
func (*UnimplementedPrpcDbNFCPatrolLineDetailServer) SelectMany(req *crud.DMLParam, srv PrpcDbNFCPatrolLineDetail_SelectManyServer) error {
	return status.Errorf(codes.Unimplemented, "method SelectMany not implemented")
}
func (*UnimplementedPrpcDbNFCPatrolLineDetailServer) Query(req *crud.PrivilegeParam, srv PrpcDbNFCPatrolLineDetail_QueryServer) error {
	return status.Errorf(codes.Unimplemented, "method Query not implemented")
}
func (*UnimplementedPrpcDbNFCPatrolLineDetailServer) QueryBatch(req *crud.PrivilegeParam, srv PrpcDbNFCPatrolLineDetail_QueryBatchServer) error {
	return status.Errorf(codes.Unimplemented, "method QueryBatch not implemented")
}

func RegisterPrpcDbNFCPatrolLineDetailServer(s *grpc.Server, srv PrpcDbNFCPatrolLineDetailServer) {
	s.RegisterService(&_PrpcDbNFCPatrolLineDetail_serviceDesc, srv)
}

func _PrpcDbNFCPatrolLineDetail_Insert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbNFCPatrolLineDetail)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrpcDbNFCPatrolLineDetailServer).Insert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.PrpcDbNFCPatrolLineDetail/Insert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrpcDbNFCPatrolLineDetailServer).Insert(ctx, req.(*DbNFCPatrolLineDetail))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrpcDbNFCPatrolLineDetail_Update_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbNFCPatrolLineDetail)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrpcDbNFCPatrolLineDetailServer).Update(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.PrpcDbNFCPatrolLineDetail/Update",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrpcDbNFCPatrolLineDetailServer).Update(ctx, req.(*DbNFCPatrolLineDetail))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrpcDbNFCPatrolLineDetail_PartialUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbNFCPatrolLineDetail)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrpcDbNFCPatrolLineDetailServer).PartialUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.PrpcDbNFCPatrolLineDetail/PartialUpdate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrpcDbNFCPatrolLineDetailServer).PartialUpdate(ctx, req.(*DbNFCPatrolLineDetail))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrpcDbNFCPatrolLineDetail_Delete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbNFCPatrolLineDetail)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrpcDbNFCPatrolLineDetailServer).Delete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.PrpcDbNFCPatrolLineDetail/Delete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrpcDbNFCPatrolLineDetailServer).Delete(ctx, req.(*DbNFCPatrolLineDetail))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrpcDbNFCPatrolLineDetail_SelectOne_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(crud.DMLParam)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrpcDbNFCPatrolLineDetailServer).SelectOne(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.PrpcDbNFCPatrolLineDetail/SelectOne",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrpcDbNFCPatrolLineDetailServer).SelectOne(ctx, req.(*crud.DMLParam))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrpcDbNFCPatrolLineDetail_SelectMany_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.DMLParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(PrpcDbNFCPatrolLineDetailServer).SelectMany(m, &prpcDbNFCPatrolLineDetailSelectManyServer{stream})
}

type PrpcDbNFCPatrolLineDetail_SelectManyServer interface {
	Send(*DbNFCPatrolLineDetail) error
	grpc.ServerStream
}

type prpcDbNFCPatrolLineDetailSelectManyServer struct {
	grpc.ServerStream
}

func (x *prpcDbNFCPatrolLineDetailSelectManyServer) Send(m *DbNFCPatrolLineDetail) error {
	return x.ServerStream.SendMsg(m)
}

func _PrpcDbNFCPatrolLineDetail_Query_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.PrivilegeParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(PrpcDbNFCPatrolLineDetailServer).Query(m, &prpcDbNFCPatrolLineDetailQueryServer{stream})
}

type PrpcDbNFCPatrolLineDetail_QueryServer interface {
	Send(*DbNFCPatrolLineDetail) error
	grpc.ServerStream
}

type prpcDbNFCPatrolLineDetailQueryServer struct {
	grpc.ServerStream
}

func (x *prpcDbNFCPatrolLineDetailQueryServer) Send(m *DbNFCPatrolLineDetail) error {
	return x.ServerStream.SendMsg(m)
}

func _PrpcDbNFCPatrolLineDetail_QueryBatch_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.PrivilegeParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(PrpcDbNFCPatrolLineDetailServer).QueryBatch(m, &prpcDbNFCPatrolLineDetailQueryBatchServer{stream})
}

type PrpcDbNFCPatrolLineDetail_QueryBatchServer interface {
	Send(*DbNFCPatrolLineDetailList) error
	grpc.ServerStream
}

type prpcDbNFCPatrolLineDetailQueryBatchServer struct {
	grpc.ServerStream
}

func (x *prpcDbNFCPatrolLineDetailQueryBatchServer) Send(m *DbNFCPatrolLineDetailList) error {
	return x.ServerStream.SendMsg(m)
}

var _PrpcDbNFCPatrolLineDetail_serviceDesc = grpc.ServiceDesc{
	ServiceName: "bysdb.PrpcDbNFCPatrolLineDetail",
	HandlerType: (*PrpcDbNFCPatrolLineDetailServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Insert",
			Handler:    _PrpcDbNFCPatrolLineDetail_Insert_Handler,
		},
		{
			MethodName: "Update",
			Handler:    _PrpcDbNFCPatrolLineDetail_Update_Handler,
		},
		{
			MethodName: "PartialUpdate",
			Handler:    _PrpcDbNFCPatrolLineDetail_PartialUpdate_Handler,
		},
		{
			MethodName: "Delete",
			Handler:    _PrpcDbNFCPatrolLineDetail_Delete_Handler,
		},
		{
			MethodName: "SelectOne",
			Handler:    _PrpcDbNFCPatrolLineDetail_SelectOne_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "SelectMany",
			Handler:       _PrpcDbNFCPatrolLineDetail_SelectMany_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "Query",
			Handler:       _PrpcDbNFCPatrolLineDetail_Query_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "QueryBatch",
			Handler:       _PrpcDbNFCPatrolLineDetail_QueryBatch_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "bysdb.rpc.proto",
}

// RpcDbNFCPatrolLineRulesClient is the client API for RpcDbNFCPatrolLineRules service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type RpcDbNFCPatrolLineRulesClient interface {
	//插入一行数据
	Insert(ctx context.Context, in *DbNFCPatrolLineRules, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,全量修改一行数据
	Update(ctx context.Context, in *DbNFCPatrolLineRules, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
	PartialUpdate(ctx context.Context, in *DbNFCPatrolLineRules, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,删除一行数据
	Delete(ctx context.Context, in *DbNFCPatrolLineRules, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
	SelectOne(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (*DbNFCPatrolLineRules, error)
	//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
	SelectMany(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (RpcDbNFCPatrolLineRules_SelectManyClient, error)
	//根据条件查询数据
	Query(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbNFCPatrolLineRules_QueryClient, error)
	//根据条件查询数据,批量返回数据,crud.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbNFCPatrolLineRules_QueryBatchClient, error)
}

type rpcDbNFCPatrolLineRulesClient struct {
	cc *grpc.ClientConn
}

func NewRpcDbNFCPatrolLineRulesClient(cc *grpc.ClientConn) RpcDbNFCPatrolLineRulesClient {
	return &rpcDbNFCPatrolLineRulesClient{cc}
}

func (c *rpcDbNFCPatrolLineRulesClient) Insert(ctx context.Context, in *DbNFCPatrolLineRules, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/bysdb.RpcDbNFCPatrolLineRules/Insert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbNFCPatrolLineRulesClient) Update(ctx context.Context, in *DbNFCPatrolLineRules, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/bysdb.RpcDbNFCPatrolLineRules/Update", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbNFCPatrolLineRulesClient) PartialUpdate(ctx context.Context, in *DbNFCPatrolLineRules, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/bysdb.RpcDbNFCPatrolLineRules/PartialUpdate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbNFCPatrolLineRulesClient) Delete(ctx context.Context, in *DbNFCPatrolLineRules, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/bysdb.RpcDbNFCPatrolLineRules/Delete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbNFCPatrolLineRulesClient) SelectOne(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (*DbNFCPatrolLineRules, error) {
	out := new(DbNFCPatrolLineRules)
	err := c.cc.Invoke(ctx, "/bysdb.RpcDbNFCPatrolLineRules/SelectOne", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbNFCPatrolLineRulesClient) SelectMany(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (RpcDbNFCPatrolLineRules_SelectManyClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RpcDbNFCPatrolLineRules_serviceDesc.Streams[0], "/bysdb.RpcDbNFCPatrolLineRules/SelectMany", opts...)
	if err != nil {
		return nil, err
	}
	x := &rpcDbNFCPatrolLineRulesSelectManyClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type RpcDbNFCPatrolLineRules_SelectManyClient interface {
	Recv() (*DbNFCPatrolLineRules, error)
	grpc.ClientStream
}

type rpcDbNFCPatrolLineRulesSelectManyClient struct {
	grpc.ClientStream
}

func (x *rpcDbNFCPatrolLineRulesSelectManyClient) Recv() (*DbNFCPatrolLineRules, error) {
	m := new(DbNFCPatrolLineRules)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *rpcDbNFCPatrolLineRulesClient) Query(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbNFCPatrolLineRules_QueryClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RpcDbNFCPatrolLineRules_serviceDesc.Streams[1], "/bysdb.RpcDbNFCPatrolLineRules/Query", opts...)
	if err != nil {
		return nil, err
	}
	x := &rpcDbNFCPatrolLineRulesQueryClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type RpcDbNFCPatrolLineRules_QueryClient interface {
	Recv() (*DbNFCPatrolLineRules, error)
	grpc.ClientStream
}

type rpcDbNFCPatrolLineRulesQueryClient struct {
	grpc.ClientStream
}

func (x *rpcDbNFCPatrolLineRulesQueryClient) Recv() (*DbNFCPatrolLineRules, error) {
	m := new(DbNFCPatrolLineRules)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *rpcDbNFCPatrolLineRulesClient) QueryBatch(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbNFCPatrolLineRules_QueryBatchClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RpcDbNFCPatrolLineRules_serviceDesc.Streams[2], "/bysdb.RpcDbNFCPatrolLineRules/QueryBatch", opts...)
	if err != nil {
		return nil, err
	}
	x := &rpcDbNFCPatrolLineRulesQueryBatchClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type RpcDbNFCPatrolLineRules_QueryBatchClient interface {
	Recv() (*DbNFCPatrolLineRulesList, error)
	grpc.ClientStream
}

type rpcDbNFCPatrolLineRulesQueryBatchClient struct {
	grpc.ClientStream
}

func (x *rpcDbNFCPatrolLineRulesQueryBatchClient) Recv() (*DbNFCPatrolLineRulesList, error) {
	m := new(DbNFCPatrolLineRulesList)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// RpcDbNFCPatrolLineRulesServer is the server API for RpcDbNFCPatrolLineRules service.
type RpcDbNFCPatrolLineRulesServer interface {
	//插入一行数据
	Insert(context.Context, *DbNFCPatrolLineRules) (*crud.DMLResult, error)
	//以RID为条件,全量修改一行数据
	Update(context.Context, *DbNFCPatrolLineRules) (*crud.DMLResult, error)
	//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
	PartialUpdate(context.Context, *DbNFCPatrolLineRules) (*crud.DMLResult, error)
	//以RID为条件,删除一行数据
	Delete(context.Context, *DbNFCPatrolLineRules) (*crud.DMLResult, error)
	//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
	SelectOne(context.Context, *crud.DMLParam) (*DbNFCPatrolLineRules, error)
	//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
	SelectMany(*crud.DMLParam, RpcDbNFCPatrolLineRules_SelectManyServer) error
	//根据条件查询数据
	Query(*crud.QueryParam, RpcDbNFCPatrolLineRules_QueryServer) error
	//根据条件查询数据,批量返回数据,crud.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(*crud.QueryParam, RpcDbNFCPatrolLineRules_QueryBatchServer) error
}

// UnimplementedRpcDbNFCPatrolLineRulesServer can be embedded to have forward compatible implementations.
type UnimplementedRpcDbNFCPatrolLineRulesServer struct {
}

func (*UnimplementedRpcDbNFCPatrolLineRulesServer) Insert(ctx context.Context, req *DbNFCPatrolLineRules) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Insert not implemented")
}
func (*UnimplementedRpcDbNFCPatrolLineRulesServer) Update(ctx context.Context, req *DbNFCPatrolLineRules) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Update not implemented")
}
func (*UnimplementedRpcDbNFCPatrolLineRulesServer) PartialUpdate(ctx context.Context, req *DbNFCPatrolLineRules) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PartialUpdate not implemented")
}
func (*UnimplementedRpcDbNFCPatrolLineRulesServer) Delete(ctx context.Context, req *DbNFCPatrolLineRules) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Delete not implemented")
}
func (*UnimplementedRpcDbNFCPatrolLineRulesServer) SelectOne(ctx context.Context, req *crud.DMLParam) (*DbNFCPatrolLineRules, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SelectOne not implemented")
}
func (*UnimplementedRpcDbNFCPatrolLineRulesServer) SelectMany(req *crud.DMLParam, srv RpcDbNFCPatrolLineRules_SelectManyServer) error {
	return status.Errorf(codes.Unimplemented, "method SelectMany not implemented")
}
func (*UnimplementedRpcDbNFCPatrolLineRulesServer) Query(req *crud.QueryParam, srv RpcDbNFCPatrolLineRules_QueryServer) error {
	return status.Errorf(codes.Unimplemented, "method Query not implemented")
}
func (*UnimplementedRpcDbNFCPatrolLineRulesServer) QueryBatch(req *crud.QueryParam, srv RpcDbNFCPatrolLineRules_QueryBatchServer) error {
	return status.Errorf(codes.Unimplemented, "method QueryBatch not implemented")
}

func RegisterRpcDbNFCPatrolLineRulesServer(s *grpc.Server, srv RpcDbNFCPatrolLineRulesServer) {
	s.RegisterService(&_RpcDbNFCPatrolLineRules_serviceDesc, srv)
}

func _RpcDbNFCPatrolLineRules_Insert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbNFCPatrolLineRules)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbNFCPatrolLineRulesServer).Insert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.RpcDbNFCPatrolLineRules/Insert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbNFCPatrolLineRulesServer).Insert(ctx, req.(*DbNFCPatrolLineRules))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbNFCPatrolLineRules_Update_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbNFCPatrolLineRules)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbNFCPatrolLineRulesServer).Update(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.RpcDbNFCPatrolLineRules/Update",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbNFCPatrolLineRulesServer).Update(ctx, req.(*DbNFCPatrolLineRules))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbNFCPatrolLineRules_PartialUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbNFCPatrolLineRules)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbNFCPatrolLineRulesServer).PartialUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.RpcDbNFCPatrolLineRules/PartialUpdate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbNFCPatrolLineRulesServer).PartialUpdate(ctx, req.(*DbNFCPatrolLineRules))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbNFCPatrolLineRules_Delete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbNFCPatrolLineRules)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbNFCPatrolLineRulesServer).Delete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.RpcDbNFCPatrolLineRules/Delete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbNFCPatrolLineRulesServer).Delete(ctx, req.(*DbNFCPatrolLineRules))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbNFCPatrolLineRules_SelectOne_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(crud.DMLParam)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbNFCPatrolLineRulesServer).SelectOne(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.RpcDbNFCPatrolLineRules/SelectOne",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbNFCPatrolLineRulesServer).SelectOne(ctx, req.(*crud.DMLParam))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbNFCPatrolLineRules_SelectMany_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.DMLParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RpcDbNFCPatrolLineRulesServer).SelectMany(m, &rpcDbNFCPatrolLineRulesSelectManyServer{stream})
}

type RpcDbNFCPatrolLineRules_SelectManyServer interface {
	Send(*DbNFCPatrolLineRules) error
	grpc.ServerStream
}

type rpcDbNFCPatrolLineRulesSelectManyServer struct {
	grpc.ServerStream
}

func (x *rpcDbNFCPatrolLineRulesSelectManyServer) Send(m *DbNFCPatrolLineRules) error {
	return x.ServerStream.SendMsg(m)
}

func _RpcDbNFCPatrolLineRules_Query_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.QueryParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RpcDbNFCPatrolLineRulesServer).Query(m, &rpcDbNFCPatrolLineRulesQueryServer{stream})
}

type RpcDbNFCPatrolLineRules_QueryServer interface {
	Send(*DbNFCPatrolLineRules) error
	grpc.ServerStream
}

type rpcDbNFCPatrolLineRulesQueryServer struct {
	grpc.ServerStream
}

func (x *rpcDbNFCPatrolLineRulesQueryServer) Send(m *DbNFCPatrolLineRules) error {
	return x.ServerStream.SendMsg(m)
}

func _RpcDbNFCPatrolLineRules_QueryBatch_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.QueryParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RpcDbNFCPatrolLineRulesServer).QueryBatch(m, &rpcDbNFCPatrolLineRulesQueryBatchServer{stream})
}

type RpcDbNFCPatrolLineRules_QueryBatchServer interface {
	Send(*DbNFCPatrolLineRulesList) error
	grpc.ServerStream
}

type rpcDbNFCPatrolLineRulesQueryBatchServer struct {
	grpc.ServerStream
}

func (x *rpcDbNFCPatrolLineRulesQueryBatchServer) Send(m *DbNFCPatrolLineRulesList) error {
	return x.ServerStream.SendMsg(m)
}

var _RpcDbNFCPatrolLineRules_serviceDesc = grpc.ServiceDesc{
	ServiceName: "bysdb.RpcDbNFCPatrolLineRules",
	HandlerType: (*RpcDbNFCPatrolLineRulesServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Insert",
			Handler:    _RpcDbNFCPatrolLineRules_Insert_Handler,
		},
		{
			MethodName: "Update",
			Handler:    _RpcDbNFCPatrolLineRules_Update_Handler,
		},
		{
			MethodName: "PartialUpdate",
			Handler:    _RpcDbNFCPatrolLineRules_PartialUpdate_Handler,
		},
		{
			MethodName: "Delete",
			Handler:    _RpcDbNFCPatrolLineRules_Delete_Handler,
		},
		{
			MethodName: "SelectOne",
			Handler:    _RpcDbNFCPatrolLineRules_SelectOne_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "SelectMany",
			Handler:       _RpcDbNFCPatrolLineRules_SelectMany_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "Query",
			Handler:       _RpcDbNFCPatrolLineRules_Query_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "QueryBatch",
			Handler:       _RpcDbNFCPatrolLineRules_QueryBatch_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "bysdb.rpc.proto",
}

// PrpcDbNFCPatrolLineRulesClient is the client API for PrpcDbNFCPatrolLineRules service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type PrpcDbNFCPatrolLineRulesClient interface {
	//插入一行数据
	Insert(ctx context.Context, in *DbNFCPatrolLineRules, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,全量修改一行数据
	Update(ctx context.Context, in *DbNFCPatrolLineRules, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
	PartialUpdate(ctx context.Context, in *DbNFCPatrolLineRules, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,删除一行数据
	Delete(ctx context.Context, in *DbNFCPatrolLineRules, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
	SelectOne(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (*DbNFCPatrolLineRules, error)
	//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
	SelectMany(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (PrpcDbNFCPatrolLineRules_SelectManyClient, error)
	//根据条件取得我有权限的数据
	Query(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbNFCPatrolLineRules_QueryClient, error)
	//根据条件取得我有权限的数据,批量返回数据,crud.PrivilegeParam.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbNFCPatrolLineRules_QueryBatchClient, error)
}

type prpcDbNFCPatrolLineRulesClient struct {
	cc *grpc.ClientConn
}

func NewPrpcDbNFCPatrolLineRulesClient(cc *grpc.ClientConn) PrpcDbNFCPatrolLineRulesClient {
	return &prpcDbNFCPatrolLineRulesClient{cc}
}

func (c *prpcDbNFCPatrolLineRulesClient) Insert(ctx context.Context, in *DbNFCPatrolLineRules, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/bysdb.PrpcDbNFCPatrolLineRules/Insert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prpcDbNFCPatrolLineRulesClient) Update(ctx context.Context, in *DbNFCPatrolLineRules, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/bysdb.PrpcDbNFCPatrolLineRules/Update", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prpcDbNFCPatrolLineRulesClient) PartialUpdate(ctx context.Context, in *DbNFCPatrolLineRules, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/bysdb.PrpcDbNFCPatrolLineRules/PartialUpdate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prpcDbNFCPatrolLineRulesClient) Delete(ctx context.Context, in *DbNFCPatrolLineRules, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/bysdb.PrpcDbNFCPatrolLineRules/Delete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prpcDbNFCPatrolLineRulesClient) SelectOne(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (*DbNFCPatrolLineRules, error) {
	out := new(DbNFCPatrolLineRules)
	err := c.cc.Invoke(ctx, "/bysdb.PrpcDbNFCPatrolLineRules/SelectOne", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prpcDbNFCPatrolLineRulesClient) SelectMany(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (PrpcDbNFCPatrolLineRules_SelectManyClient, error) {
	stream, err := c.cc.NewStream(ctx, &_PrpcDbNFCPatrolLineRules_serviceDesc.Streams[0], "/bysdb.PrpcDbNFCPatrolLineRules/SelectMany", opts...)
	if err != nil {
		return nil, err
	}
	x := &prpcDbNFCPatrolLineRulesSelectManyClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type PrpcDbNFCPatrolLineRules_SelectManyClient interface {
	Recv() (*DbNFCPatrolLineRules, error)
	grpc.ClientStream
}

type prpcDbNFCPatrolLineRulesSelectManyClient struct {
	grpc.ClientStream
}

func (x *prpcDbNFCPatrolLineRulesSelectManyClient) Recv() (*DbNFCPatrolLineRules, error) {
	m := new(DbNFCPatrolLineRules)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *prpcDbNFCPatrolLineRulesClient) Query(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbNFCPatrolLineRules_QueryClient, error) {
	stream, err := c.cc.NewStream(ctx, &_PrpcDbNFCPatrolLineRules_serviceDesc.Streams[1], "/bysdb.PrpcDbNFCPatrolLineRules/Query", opts...)
	if err != nil {
		return nil, err
	}
	x := &prpcDbNFCPatrolLineRulesQueryClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type PrpcDbNFCPatrolLineRules_QueryClient interface {
	Recv() (*DbNFCPatrolLineRules, error)
	grpc.ClientStream
}

type prpcDbNFCPatrolLineRulesQueryClient struct {
	grpc.ClientStream
}

func (x *prpcDbNFCPatrolLineRulesQueryClient) Recv() (*DbNFCPatrolLineRules, error) {
	m := new(DbNFCPatrolLineRules)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *prpcDbNFCPatrolLineRulesClient) QueryBatch(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbNFCPatrolLineRules_QueryBatchClient, error) {
	stream, err := c.cc.NewStream(ctx, &_PrpcDbNFCPatrolLineRules_serviceDesc.Streams[2], "/bysdb.PrpcDbNFCPatrolLineRules/QueryBatch", opts...)
	if err != nil {
		return nil, err
	}
	x := &prpcDbNFCPatrolLineRulesQueryBatchClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type PrpcDbNFCPatrolLineRules_QueryBatchClient interface {
	Recv() (*DbNFCPatrolLineRulesList, error)
	grpc.ClientStream
}

type prpcDbNFCPatrolLineRulesQueryBatchClient struct {
	grpc.ClientStream
}

func (x *prpcDbNFCPatrolLineRulesQueryBatchClient) Recv() (*DbNFCPatrolLineRulesList, error) {
	m := new(DbNFCPatrolLineRulesList)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// PrpcDbNFCPatrolLineRulesServer is the server API for PrpcDbNFCPatrolLineRules service.
type PrpcDbNFCPatrolLineRulesServer interface {
	//插入一行数据
	Insert(context.Context, *DbNFCPatrolLineRules) (*crud.DMLResult, error)
	//以RID为条件,全量修改一行数据
	Update(context.Context, *DbNFCPatrolLineRules) (*crud.DMLResult, error)
	//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
	PartialUpdate(context.Context, *DbNFCPatrolLineRules) (*crud.DMLResult, error)
	//以RID为条件,删除一行数据
	Delete(context.Context, *DbNFCPatrolLineRules) (*crud.DMLResult, error)
	//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
	SelectOne(context.Context, *crud.DMLParam) (*DbNFCPatrolLineRules, error)
	//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
	SelectMany(*crud.DMLParam, PrpcDbNFCPatrolLineRules_SelectManyServer) error
	//根据条件取得我有权限的数据
	Query(*crud.PrivilegeParam, PrpcDbNFCPatrolLineRules_QueryServer) error
	//根据条件取得我有权限的数据,批量返回数据,crud.PrivilegeParam.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(*crud.PrivilegeParam, PrpcDbNFCPatrolLineRules_QueryBatchServer) error
}

// UnimplementedPrpcDbNFCPatrolLineRulesServer can be embedded to have forward compatible implementations.
type UnimplementedPrpcDbNFCPatrolLineRulesServer struct {
}

func (*UnimplementedPrpcDbNFCPatrolLineRulesServer) Insert(ctx context.Context, req *DbNFCPatrolLineRules) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Insert not implemented")
}
func (*UnimplementedPrpcDbNFCPatrolLineRulesServer) Update(ctx context.Context, req *DbNFCPatrolLineRules) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Update not implemented")
}
func (*UnimplementedPrpcDbNFCPatrolLineRulesServer) PartialUpdate(ctx context.Context, req *DbNFCPatrolLineRules) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PartialUpdate not implemented")
}
func (*UnimplementedPrpcDbNFCPatrolLineRulesServer) Delete(ctx context.Context, req *DbNFCPatrolLineRules) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Delete not implemented")
}
func (*UnimplementedPrpcDbNFCPatrolLineRulesServer) SelectOne(ctx context.Context, req *crud.DMLParam) (*DbNFCPatrolLineRules, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SelectOne not implemented")
}
func (*UnimplementedPrpcDbNFCPatrolLineRulesServer) SelectMany(req *crud.DMLParam, srv PrpcDbNFCPatrolLineRules_SelectManyServer) error {
	return status.Errorf(codes.Unimplemented, "method SelectMany not implemented")
}
func (*UnimplementedPrpcDbNFCPatrolLineRulesServer) Query(req *crud.PrivilegeParam, srv PrpcDbNFCPatrolLineRules_QueryServer) error {
	return status.Errorf(codes.Unimplemented, "method Query not implemented")
}
func (*UnimplementedPrpcDbNFCPatrolLineRulesServer) QueryBatch(req *crud.PrivilegeParam, srv PrpcDbNFCPatrolLineRules_QueryBatchServer) error {
	return status.Errorf(codes.Unimplemented, "method QueryBatch not implemented")
}

func RegisterPrpcDbNFCPatrolLineRulesServer(s *grpc.Server, srv PrpcDbNFCPatrolLineRulesServer) {
	s.RegisterService(&_PrpcDbNFCPatrolLineRules_serviceDesc, srv)
}

func _PrpcDbNFCPatrolLineRules_Insert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbNFCPatrolLineRules)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrpcDbNFCPatrolLineRulesServer).Insert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.PrpcDbNFCPatrolLineRules/Insert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrpcDbNFCPatrolLineRulesServer).Insert(ctx, req.(*DbNFCPatrolLineRules))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrpcDbNFCPatrolLineRules_Update_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbNFCPatrolLineRules)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrpcDbNFCPatrolLineRulesServer).Update(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.PrpcDbNFCPatrolLineRules/Update",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrpcDbNFCPatrolLineRulesServer).Update(ctx, req.(*DbNFCPatrolLineRules))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrpcDbNFCPatrolLineRules_PartialUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbNFCPatrolLineRules)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrpcDbNFCPatrolLineRulesServer).PartialUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.PrpcDbNFCPatrolLineRules/PartialUpdate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrpcDbNFCPatrolLineRulesServer).PartialUpdate(ctx, req.(*DbNFCPatrolLineRules))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrpcDbNFCPatrolLineRules_Delete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbNFCPatrolLineRules)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrpcDbNFCPatrolLineRulesServer).Delete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.PrpcDbNFCPatrolLineRules/Delete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrpcDbNFCPatrolLineRulesServer).Delete(ctx, req.(*DbNFCPatrolLineRules))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrpcDbNFCPatrolLineRules_SelectOne_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(crud.DMLParam)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrpcDbNFCPatrolLineRulesServer).SelectOne(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.PrpcDbNFCPatrolLineRules/SelectOne",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrpcDbNFCPatrolLineRulesServer).SelectOne(ctx, req.(*crud.DMLParam))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrpcDbNFCPatrolLineRules_SelectMany_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.DMLParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(PrpcDbNFCPatrolLineRulesServer).SelectMany(m, &prpcDbNFCPatrolLineRulesSelectManyServer{stream})
}

type PrpcDbNFCPatrolLineRules_SelectManyServer interface {
	Send(*DbNFCPatrolLineRules) error
	grpc.ServerStream
}

type prpcDbNFCPatrolLineRulesSelectManyServer struct {
	grpc.ServerStream
}

func (x *prpcDbNFCPatrolLineRulesSelectManyServer) Send(m *DbNFCPatrolLineRules) error {
	return x.ServerStream.SendMsg(m)
}

func _PrpcDbNFCPatrolLineRules_Query_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.PrivilegeParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(PrpcDbNFCPatrolLineRulesServer).Query(m, &prpcDbNFCPatrolLineRulesQueryServer{stream})
}

type PrpcDbNFCPatrolLineRules_QueryServer interface {
	Send(*DbNFCPatrolLineRules) error
	grpc.ServerStream
}

type prpcDbNFCPatrolLineRulesQueryServer struct {
	grpc.ServerStream
}

func (x *prpcDbNFCPatrolLineRulesQueryServer) Send(m *DbNFCPatrolLineRules) error {
	return x.ServerStream.SendMsg(m)
}

func _PrpcDbNFCPatrolLineRules_QueryBatch_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.PrivilegeParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(PrpcDbNFCPatrolLineRulesServer).QueryBatch(m, &prpcDbNFCPatrolLineRulesQueryBatchServer{stream})
}

type PrpcDbNFCPatrolLineRules_QueryBatchServer interface {
	Send(*DbNFCPatrolLineRulesList) error
	grpc.ServerStream
}

type prpcDbNFCPatrolLineRulesQueryBatchServer struct {
	grpc.ServerStream
}

func (x *prpcDbNFCPatrolLineRulesQueryBatchServer) Send(m *DbNFCPatrolLineRulesList) error {
	return x.ServerStream.SendMsg(m)
}

var _PrpcDbNFCPatrolLineRules_serviceDesc = grpc.ServiceDesc{
	ServiceName: "bysdb.PrpcDbNFCPatrolLineRules",
	HandlerType: (*PrpcDbNFCPatrolLineRulesServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Insert",
			Handler:    _PrpcDbNFCPatrolLineRules_Insert_Handler,
		},
		{
			MethodName: "Update",
			Handler:    _PrpcDbNFCPatrolLineRules_Update_Handler,
		},
		{
			MethodName: "PartialUpdate",
			Handler:    _PrpcDbNFCPatrolLineRules_PartialUpdate_Handler,
		},
		{
			MethodName: "Delete",
			Handler:    _PrpcDbNFCPatrolLineRules_Delete_Handler,
		},
		{
			MethodName: "SelectOne",
			Handler:    _PrpcDbNFCPatrolLineRules_SelectOne_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "SelectMany",
			Handler:       _PrpcDbNFCPatrolLineRules_SelectMany_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "Query",
			Handler:       _PrpcDbNFCPatrolLineRules_Query_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "QueryBatch",
			Handler:       _PrpcDbNFCPatrolLineRules_QueryBatch_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "bysdb.rpc.proto",
}

// RpcDbNFCPatrolLineAndRulesClient is the client API for RpcDbNFCPatrolLineAndRules service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type RpcDbNFCPatrolLineAndRulesClient interface {
	//插入一行数据
	Insert(ctx context.Context, in *DbNFCPatrolLineAndRules, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,全量修改一行数据
	Update(ctx context.Context, in *DbNFCPatrolLineAndRules, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
	PartialUpdate(ctx context.Context, in *DbNFCPatrolLineAndRules, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,删除一行数据
	Delete(ctx context.Context, in *DbNFCPatrolLineAndRules, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
	SelectOne(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (*DbNFCPatrolLineAndRules, error)
	//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
	SelectMany(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (RpcDbNFCPatrolLineAndRules_SelectManyClient, error)
	//根据条件查询数据
	Query(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbNFCPatrolLineAndRules_QueryClient, error)
	//根据条件查询数据,批量返回数据,crud.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbNFCPatrolLineAndRules_QueryBatchClient, error)
}

type rpcDbNFCPatrolLineAndRulesClient struct {
	cc *grpc.ClientConn
}

func NewRpcDbNFCPatrolLineAndRulesClient(cc *grpc.ClientConn) RpcDbNFCPatrolLineAndRulesClient {
	return &rpcDbNFCPatrolLineAndRulesClient{cc}
}

func (c *rpcDbNFCPatrolLineAndRulesClient) Insert(ctx context.Context, in *DbNFCPatrolLineAndRules, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/bysdb.RpcDbNFCPatrolLineAndRules/Insert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbNFCPatrolLineAndRulesClient) Update(ctx context.Context, in *DbNFCPatrolLineAndRules, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/bysdb.RpcDbNFCPatrolLineAndRules/Update", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbNFCPatrolLineAndRulesClient) PartialUpdate(ctx context.Context, in *DbNFCPatrolLineAndRules, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/bysdb.RpcDbNFCPatrolLineAndRules/PartialUpdate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbNFCPatrolLineAndRulesClient) Delete(ctx context.Context, in *DbNFCPatrolLineAndRules, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/bysdb.RpcDbNFCPatrolLineAndRules/Delete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbNFCPatrolLineAndRulesClient) SelectOne(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (*DbNFCPatrolLineAndRules, error) {
	out := new(DbNFCPatrolLineAndRules)
	err := c.cc.Invoke(ctx, "/bysdb.RpcDbNFCPatrolLineAndRules/SelectOne", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbNFCPatrolLineAndRulesClient) SelectMany(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (RpcDbNFCPatrolLineAndRules_SelectManyClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RpcDbNFCPatrolLineAndRules_serviceDesc.Streams[0], "/bysdb.RpcDbNFCPatrolLineAndRules/SelectMany", opts...)
	if err != nil {
		return nil, err
	}
	x := &rpcDbNFCPatrolLineAndRulesSelectManyClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type RpcDbNFCPatrolLineAndRules_SelectManyClient interface {
	Recv() (*DbNFCPatrolLineAndRules, error)
	grpc.ClientStream
}

type rpcDbNFCPatrolLineAndRulesSelectManyClient struct {
	grpc.ClientStream
}

func (x *rpcDbNFCPatrolLineAndRulesSelectManyClient) Recv() (*DbNFCPatrolLineAndRules, error) {
	m := new(DbNFCPatrolLineAndRules)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *rpcDbNFCPatrolLineAndRulesClient) Query(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbNFCPatrolLineAndRules_QueryClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RpcDbNFCPatrolLineAndRules_serviceDesc.Streams[1], "/bysdb.RpcDbNFCPatrolLineAndRules/Query", opts...)
	if err != nil {
		return nil, err
	}
	x := &rpcDbNFCPatrolLineAndRulesQueryClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type RpcDbNFCPatrolLineAndRules_QueryClient interface {
	Recv() (*DbNFCPatrolLineAndRules, error)
	grpc.ClientStream
}

type rpcDbNFCPatrolLineAndRulesQueryClient struct {
	grpc.ClientStream
}

func (x *rpcDbNFCPatrolLineAndRulesQueryClient) Recv() (*DbNFCPatrolLineAndRules, error) {
	m := new(DbNFCPatrolLineAndRules)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *rpcDbNFCPatrolLineAndRulesClient) QueryBatch(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbNFCPatrolLineAndRules_QueryBatchClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RpcDbNFCPatrolLineAndRules_serviceDesc.Streams[2], "/bysdb.RpcDbNFCPatrolLineAndRules/QueryBatch", opts...)
	if err != nil {
		return nil, err
	}
	x := &rpcDbNFCPatrolLineAndRulesQueryBatchClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type RpcDbNFCPatrolLineAndRules_QueryBatchClient interface {
	Recv() (*DbNFCPatrolLineAndRulesList, error)
	grpc.ClientStream
}

type rpcDbNFCPatrolLineAndRulesQueryBatchClient struct {
	grpc.ClientStream
}

func (x *rpcDbNFCPatrolLineAndRulesQueryBatchClient) Recv() (*DbNFCPatrolLineAndRulesList, error) {
	m := new(DbNFCPatrolLineAndRulesList)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// RpcDbNFCPatrolLineAndRulesServer is the server API for RpcDbNFCPatrolLineAndRules service.
type RpcDbNFCPatrolLineAndRulesServer interface {
	//插入一行数据
	Insert(context.Context, *DbNFCPatrolLineAndRules) (*crud.DMLResult, error)
	//以RID为条件,全量修改一行数据
	Update(context.Context, *DbNFCPatrolLineAndRules) (*crud.DMLResult, error)
	//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
	PartialUpdate(context.Context, *DbNFCPatrolLineAndRules) (*crud.DMLResult, error)
	//以RID为条件,删除一行数据
	Delete(context.Context, *DbNFCPatrolLineAndRules) (*crud.DMLResult, error)
	//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
	SelectOne(context.Context, *crud.DMLParam) (*DbNFCPatrolLineAndRules, error)
	//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
	SelectMany(*crud.DMLParam, RpcDbNFCPatrolLineAndRules_SelectManyServer) error
	//根据条件查询数据
	Query(*crud.QueryParam, RpcDbNFCPatrolLineAndRules_QueryServer) error
	//根据条件查询数据,批量返回数据,crud.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(*crud.QueryParam, RpcDbNFCPatrolLineAndRules_QueryBatchServer) error
}

// UnimplementedRpcDbNFCPatrolLineAndRulesServer can be embedded to have forward compatible implementations.
type UnimplementedRpcDbNFCPatrolLineAndRulesServer struct {
}

func (*UnimplementedRpcDbNFCPatrolLineAndRulesServer) Insert(ctx context.Context, req *DbNFCPatrolLineAndRules) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Insert not implemented")
}
func (*UnimplementedRpcDbNFCPatrolLineAndRulesServer) Update(ctx context.Context, req *DbNFCPatrolLineAndRules) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Update not implemented")
}
func (*UnimplementedRpcDbNFCPatrolLineAndRulesServer) PartialUpdate(ctx context.Context, req *DbNFCPatrolLineAndRules) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PartialUpdate not implemented")
}
func (*UnimplementedRpcDbNFCPatrolLineAndRulesServer) Delete(ctx context.Context, req *DbNFCPatrolLineAndRules) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Delete not implemented")
}
func (*UnimplementedRpcDbNFCPatrolLineAndRulesServer) SelectOne(ctx context.Context, req *crud.DMLParam) (*DbNFCPatrolLineAndRules, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SelectOne not implemented")
}
func (*UnimplementedRpcDbNFCPatrolLineAndRulesServer) SelectMany(req *crud.DMLParam, srv RpcDbNFCPatrolLineAndRules_SelectManyServer) error {
	return status.Errorf(codes.Unimplemented, "method SelectMany not implemented")
}
func (*UnimplementedRpcDbNFCPatrolLineAndRulesServer) Query(req *crud.QueryParam, srv RpcDbNFCPatrolLineAndRules_QueryServer) error {
	return status.Errorf(codes.Unimplemented, "method Query not implemented")
}
func (*UnimplementedRpcDbNFCPatrolLineAndRulesServer) QueryBatch(req *crud.QueryParam, srv RpcDbNFCPatrolLineAndRules_QueryBatchServer) error {
	return status.Errorf(codes.Unimplemented, "method QueryBatch not implemented")
}

func RegisterRpcDbNFCPatrolLineAndRulesServer(s *grpc.Server, srv RpcDbNFCPatrolLineAndRulesServer) {
	s.RegisterService(&_RpcDbNFCPatrolLineAndRules_serviceDesc, srv)
}

func _RpcDbNFCPatrolLineAndRules_Insert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbNFCPatrolLineAndRules)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbNFCPatrolLineAndRulesServer).Insert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.RpcDbNFCPatrolLineAndRules/Insert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbNFCPatrolLineAndRulesServer).Insert(ctx, req.(*DbNFCPatrolLineAndRules))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbNFCPatrolLineAndRules_Update_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbNFCPatrolLineAndRules)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbNFCPatrolLineAndRulesServer).Update(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.RpcDbNFCPatrolLineAndRules/Update",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbNFCPatrolLineAndRulesServer).Update(ctx, req.(*DbNFCPatrolLineAndRules))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbNFCPatrolLineAndRules_PartialUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbNFCPatrolLineAndRules)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbNFCPatrolLineAndRulesServer).PartialUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.RpcDbNFCPatrolLineAndRules/PartialUpdate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbNFCPatrolLineAndRulesServer).PartialUpdate(ctx, req.(*DbNFCPatrolLineAndRules))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbNFCPatrolLineAndRules_Delete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbNFCPatrolLineAndRules)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbNFCPatrolLineAndRulesServer).Delete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.RpcDbNFCPatrolLineAndRules/Delete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbNFCPatrolLineAndRulesServer).Delete(ctx, req.(*DbNFCPatrolLineAndRules))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbNFCPatrolLineAndRules_SelectOne_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(crud.DMLParam)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbNFCPatrolLineAndRulesServer).SelectOne(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.RpcDbNFCPatrolLineAndRules/SelectOne",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbNFCPatrolLineAndRulesServer).SelectOne(ctx, req.(*crud.DMLParam))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbNFCPatrolLineAndRules_SelectMany_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.DMLParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RpcDbNFCPatrolLineAndRulesServer).SelectMany(m, &rpcDbNFCPatrolLineAndRulesSelectManyServer{stream})
}

type RpcDbNFCPatrolLineAndRules_SelectManyServer interface {
	Send(*DbNFCPatrolLineAndRules) error
	grpc.ServerStream
}

type rpcDbNFCPatrolLineAndRulesSelectManyServer struct {
	grpc.ServerStream
}

func (x *rpcDbNFCPatrolLineAndRulesSelectManyServer) Send(m *DbNFCPatrolLineAndRules) error {
	return x.ServerStream.SendMsg(m)
}

func _RpcDbNFCPatrolLineAndRules_Query_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.QueryParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RpcDbNFCPatrolLineAndRulesServer).Query(m, &rpcDbNFCPatrolLineAndRulesQueryServer{stream})
}

type RpcDbNFCPatrolLineAndRules_QueryServer interface {
	Send(*DbNFCPatrolLineAndRules) error
	grpc.ServerStream
}

type rpcDbNFCPatrolLineAndRulesQueryServer struct {
	grpc.ServerStream
}

func (x *rpcDbNFCPatrolLineAndRulesQueryServer) Send(m *DbNFCPatrolLineAndRules) error {
	return x.ServerStream.SendMsg(m)
}

func _RpcDbNFCPatrolLineAndRules_QueryBatch_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.QueryParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RpcDbNFCPatrolLineAndRulesServer).QueryBatch(m, &rpcDbNFCPatrolLineAndRulesQueryBatchServer{stream})
}

type RpcDbNFCPatrolLineAndRules_QueryBatchServer interface {
	Send(*DbNFCPatrolLineAndRulesList) error
	grpc.ServerStream
}

type rpcDbNFCPatrolLineAndRulesQueryBatchServer struct {
	grpc.ServerStream
}

func (x *rpcDbNFCPatrolLineAndRulesQueryBatchServer) Send(m *DbNFCPatrolLineAndRulesList) error {
	return x.ServerStream.SendMsg(m)
}

var _RpcDbNFCPatrolLineAndRules_serviceDesc = grpc.ServiceDesc{
	ServiceName: "bysdb.RpcDbNFCPatrolLineAndRules",
	HandlerType: (*RpcDbNFCPatrolLineAndRulesServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Insert",
			Handler:    _RpcDbNFCPatrolLineAndRules_Insert_Handler,
		},
		{
			MethodName: "Update",
			Handler:    _RpcDbNFCPatrolLineAndRules_Update_Handler,
		},
		{
			MethodName: "PartialUpdate",
			Handler:    _RpcDbNFCPatrolLineAndRules_PartialUpdate_Handler,
		},
		{
			MethodName: "Delete",
			Handler:    _RpcDbNFCPatrolLineAndRules_Delete_Handler,
		},
		{
			MethodName: "SelectOne",
			Handler:    _RpcDbNFCPatrolLineAndRules_SelectOne_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "SelectMany",
			Handler:       _RpcDbNFCPatrolLineAndRules_SelectMany_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "Query",
			Handler:       _RpcDbNFCPatrolLineAndRules_Query_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "QueryBatch",
			Handler:       _RpcDbNFCPatrolLineAndRules_QueryBatch_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "bysdb.rpc.proto",
}

// PrpcDbNFCPatrolLineAndRulesClient is the client API for PrpcDbNFCPatrolLineAndRules service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type PrpcDbNFCPatrolLineAndRulesClient interface {
	//插入一行数据
	Insert(ctx context.Context, in *DbNFCPatrolLineAndRules, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,全量修改一行数据
	Update(ctx context.Context, in *DbNFCPatrolLineAndRules, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
	PartialUpdate(ctx context.Context, in *DbNFCPatrolLineAndRules, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,删除一行数据
	Delete(ctx context.Context, in *DbNFCPatrolLineAndRules, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
	SelectOne(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (*DbNFCPatrolLineAndRules, error)
	//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
	SelectMany(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (PrpcDbNFCPatrolLineAndRules_SelectManyClient, error)
	//根据条件取得我有权限的数据
	Query(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbNFCPatrolLineAndRules_QueryClient, error)
	//根据条件取得我有权限的数据,批量返回数据,crud.PrivilegeParam.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbNFCPatrolLineAndRules_QueryBatchClient, error)
}

type prpcDbNFCPatrolLineAndRulesClient struct {
	cc *grpc.ClientConn
}

func NewPrpcDbNFCPatrolLineAndRulesClient(cc *grpc.ClientConn) PrpcDbNFCPatrolLineAndRulesClient {
	return &prpcDbNFCPatrolLineAndRulesClient{cc}
}

func (c *prpcDbNFCPatrolLineAndRulesClient) Insert(ctx context.Context, in *DbNFCPatrolLineAndRules, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/bysdb.PrpcDbNFCPatrolLineAndRules/Insert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prpcDbNFCPatrolLineAndRulesClient) Update(ctx context.Context, in *DbNFCPatrolLineAndRules, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/bysdb.PrpcDbNFCPatrolLineAndRules/Update", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prpcDbNFCPatrolLineAndRulesClient) PartialUpdate(ctx context.Context, in *DbNFCPatrolLineAndRules, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/bysdb.PrpcDbNFCPatrolLineAndRules/PartialUpdate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prpcDbNFCPatrolLineAndRulesClient) Delete(ctx context.Context, in *DbNFCPatrolLineAndRules, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/bysdb.PrpcDbNFCPatrolLineAndRules/Delete", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prpcDbNFCPatrolLineAndRulesClient) SelectOne(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (*DbNFCPatrolLineAndRules, error) {
	out := new(DbNFCPatrolLineAndRules)
	err := c.cc.Invoke(ctx, "/bysdb.PrpcDbNFCPatrolLineAndRules/SelectOne", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prpcDbNFCPatrolLineAndRulesClient) SelectMany(ctx context.Context, in *crud.DMLParam, opts ...grpc.CallOption) (PrpcDbNFCPatrolLineAndRules_SelectManyClient, error) {
	stream, err := c.cc.NewStream(ctx, &_PrpcDbNFCPatrolLineAndRules_serviceDesc.Streams[0], "/bysdb.PrpcDbNFCPatrolLineAndRules/SelectMany", opts...)
	if err != nil {
		return nil, err
	}
	x := &prpcDbNFCPatrolLineAndRulesSelectManyClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type PrpcDbNFCPatrolLineAndRules_SelectManyClient interface {
	Recv() (*DbNFCPatrolLineAndRules, error)
	grpc.ClientStream
}

type prpcDbNFCPatrolLineAndRulesSelectManyClient struct {
	grpc.ClientStream
}

func (x *prpcDbNFCPatrolLineAndRulesSelectManyClient) Recv() (*DbNFCPatrolLineAndRules, error) {
	m := new(DbNFCPatrolLineAndRules)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *prpcDbNFCPatrolLineAndRulesClient) Query(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbNFCPatrolLineAndRules_QueryClient, error) {
	stream, err := c.cc.NewStream(ctx, &_PrpcDbNFCPatrolLineAndRules_serviceDesc.Streams[1], "/bysdb.PrpcDbNFCPatrolLineAndRules/Query", opts...)
	if err != nil {
		return nil, err
	}
	x := &prpcDbNFCPatrolLineAndRulesQueryClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type PrpcDbNFCPatrolLineAndRules_QueryClient interface {
	Recv() (*DbNFCPatrolLineAndRules, error)
	grpc.ClientStream
}

type prpcDbNFCPatrolLineAndRulesQueryClient struct {
	grpc.ClientStream
}

func (x *prpcDbNFCPatrolLineAndRulesQueryClient) Recv() (*DbNFCPatrolLineAndRules, error) {
	m := new(DbNFCPatrolLineAndRules)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *prpcDbNFCPatrolLineAndRulesClient) QueryBatch(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbNFCPatrolLineAndRules_QueryBatchClient, error) {
	stream, err := c.cc.NewStream(ctx, &_PrpcDbNFCPatrolLineAndRules_serviceDesc.Streams[2], "/bysdb.PrpcDbNFCPatrolLineAndRules/QueryBatch", opts...)
	if err != nil {
		return nil, err
	}
	x := &prpcDbNFCPatrolLineAndRulesQueryBatchClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type PrpcDbNFCPatrolLineAndRules_QueryBatchClient interface {
	Recv() (*DbNFCPatrolLineAndRulesList, error)
	grpc.ClientStream
}

type prpcDbNFCPatrolLineAndRulesQueryBatchClient struct {
	grpc.ClientStream
}

func (x *prpcDbNFCPatrolLineAndRulesQueryBatchClient) Recv() (*DbNFCPatrolLineAndRulesList, error) {
	m := new(DbNFCPatrolLineAndRulesList)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// PrpcDbNFCPatrolLineAndRulesServer is the server API for PrpcDbNFCPatrolLineAndRules service.
type PrpcDbNFCPatrolLineAndRulesServer interface {
	//插入一行数据
	Insert(context.Context, *DbNFCPatrolLineAndRules) (*crud.DMLResult, error)
	//以RID为条件,全量修改一行数据
	Update(context.Context, *DbNFCPatrolLineAndRules) (*crud.DMLResult, error)
	//以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
	PartialUpdate(context.Context, *DbNFCPatrolLineAndRules) (*crud.DMLResult, error)
	//以RID为条件,删除一行数据
	Delete(context.Context, *DbNFCPatrolLineAndRules) (*crud.DMLResult, error)
	//以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
	SelectOne(context.Context, *crud.DMLParam) (*DbNFCPatrolLineAndRules, error)
	//以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
	SelectMany(*crud.DMLParam, PrpcDbNFCPatrolLineAndRules_SelectManyServer) error
	//根据条件取得我有权限的数据
	Query(*crud.PrivilegeParam, PrpcDbNFCPatrolLineAndRules_QueryServer) error
	//根据条件取得我有权限的数据,批量返回数据,crud.PrivilegeParam.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(*crud.PrivilegeParam, PrpcDbNFCPatrolLineAndRules_QueryBatchServer) error
}

// UnimplementedPrpcDbNFCPatrolLineAndRulesServer can be embedded to have forward compatible implementations.
type UnimplementedPrpcDbNFCPatrolLineAndRulesServer struct {
}

func (*UnimplementedPrpcDbNFCPatrolLineAndRulesServer) Insert(ctx context.Context, req *DbNFCPatrolLineAndRules) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Insert not implemented")
}
func (*UnimplementedPrpcDbNFCPatrolLineAndRulesServer) Update(ctx context.Context, req *DbNFCPatrolLineAndRules) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Update not implemented")
}
func (*UnimplementedPrpcDbNFCPatrolLineAndRulesServer) PartialUpdate(ctx context.Context, req *DbNFCPatrolLineAndRules) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PartialUpdate not implemented")
}
func (*UnimplementedPrpcDbNFCPatrolLineAndRulesServer) Delete(ctx context.Context, req *DbNFCPatrolLineAndRules) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Delete not implemented")
}
func (*UnimplementedPrpcDbNFCPatrolLineAndRulesServer) SelectOne(ctx context.Context, req *crud.DMLParam) (*DbNFCPatrolLineAndRules, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SelectOne not implemented")
}
func (*UnimplementedPrpcDbNFCPatrolLineAndRulesServer) SelectMany(req *crud.DMLParam, srv PrpcDbNFCPatrolLineAndRules_SelectManyServer) error {
	return status.Errorf(codes.Unimplemented, "method SelectMany not implemented")
}
func (*UnimplementedPrpcDbNFCPatrolLineAndRulesServer) Query(req *crud.PrivilegeParam, srv PrpcDbNFCPatrolLineAndRules_QueryServer) error {
	return status.Errorf(codes.Unimplemented, "method Query not implemented")
}
func (*UnimplementedPrpcDbNFCPatrolLineAndRulesServer) QueryBatch(req *crud.PrivilegeParam, srv PrpcDbNFCPatrolLineAndRules_QueryBatchServer) error {
	return status.Errorf(codes.Unimplemented, "method QueryBatch not implemented")
}

func RegisterPrpcDbNFCPatrolLineAndRulesServer(s *grpc.Server, srv PrpcDbNFCPatrolLineAndRulesServer) {
	s.RegisterService(&_PrpcDbNFCPatrolLineAndRules_serviceDesc, srv)
}

func _PrpcDbNFCPatrolLineAndRules_Insert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbNFCPatrolLineAndRules)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrpcDbNFCPatrolLineAndRulesServer).Insert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.PrpcDbNFCPatrolLineAndRules/Insert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrpcDbNFCPatrolLineAndRulesServer).Insert(ctx, req.(*DbNFCPatrolLineAndRules))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrpcDbNFCPatrolLineAndRules_Update_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbNFCPatrolLineAndRules)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrpcDbNFCPatrolLineAndRulesServer).Update(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.PrpcDbNFCPatrolLineAndRules/Update",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrpcDbNFCPatrolLineAndRulesServer).Update(ctx, req.(*DbNFCPatrolLineAndRules))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrpcDbNFCPatrolLineAndRules_PartialUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbNFCPatrolLineAndRules)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrpcDbNFCPatrolLineAndRulesServer).PartialUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.PrpcDbNFCPatrolLineAndRules/PartialUpdate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrpcDbNFCPatrolLineAndRulesServer).PartialUpdate(ctx, req.(*DbNFCPatrolLineAndRules))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrpcDbNFCPatrolLineAndRules_Delete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbNFCPatrolLineAndRules)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrpcDbNFCPatrolLineAndRulesServer).Delete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.PrpcDbNFCPatrolLineAndRules/Delete",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrpcDbNFCPatrolLineAndRulesServer).Delete(ctx, req.(*DbNFCPatrolLineAndRules))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrpcDbNFCPatrolLineAndRules_SelectOne_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(crud.DMLParam)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrpcDbNFCPatrolLineAndRulesServer).SelectOne(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.PrpcDbNFCPatrolLineAndRules/SelectOne",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrpcDbNFCPatrolLineAndRulesServer).SelectOne(ctx, req.(*crud.DMLParam))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrpcDbNFCPatrolLineAndRules_SelectMany_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.DMLParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(PrpcDbNFCPatrolLineAndRulesServer).SelectMany(m, &prpcDbNFCPatrolLineAndRulesSelectManyServer{stream})
}

type PrpcDbNFCPatrolLineAndRules_SelectManyServer interface {
	Send(*DbNFCPatrolLineAndRules) error
	grpc.ServerStream
}

type prpcDbNFCPatrolLineAndRulesSelectManyServer struct {
	grpc.ServerStream
}

func (x *prpcDbNFCPatrolLineAndRulesSelectManyServer) Send(m *DbNFCPatrolLineAndRules) error {
	return x.ServerStream.SendMsg(m)
}

func _PrpcDbNFCPatrolLineAndRules_Query_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.PrivilegeParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(PrpcDbNFCPatrolLineAndRulesServer).Query(m, &prpcDbNFCPatrolLineAndRulesQueryServer{stream})
}

type PrpcDbNFCPatrolLineAndRules_QueryServer interface {
	Send(*DbNFCPatrolLineAndRules) error
	grpc.ServerStream
}

type prpcDbNFCPatrolLineAndRulesQueryServer struct {
	grpc.ServerStream
}

func (x *prpcDbNFCPatrolLineAndRulesQueryServer) Send(m *DbNFCPatrolLineAndRules) error {
	return x.ServerStream.SendMsg(m)
}

func _PrpcDbNFCPatrolLineAndRules_QueryBatch_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.PrivilegeParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(PrpcDbNFCPatrolLineAndRulesServer).QueryBatch(m, &prpcDbNFCPatrolLineAndRulesQueryBatchServer{stream})
}

type PrpcDbNFCPatrolLineAndRules_QueryBatchServer interface {
	Send(*DbNFCPatrolLineAndRulesList) error
	grpc.ServerStream
}

type prpcDbNFCPatrolLineAndRulesQueryBatchServer struct {
	grpc.ServerStream
}

func (x *prpcDbNFCPatrolLineAndRulesQueryBatchServer) Send(m *DbNFCPatrolLineAndRulesList) error {
	return x.ServerStream.SendMsg(m)
}

var _PrpcDbNFCPatrolLineAndRules_serviceDesc = grpc.ServiceDesc{
	ServiceName: "bysdb.PrpcDbNFCPatrolLineAndRules",
	HandlerType: (*PrpcDbNFCPatrolLineAndRulesServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Insert",
			Handler:    _PrpcDbNFCPatrolLineAndRules_Insert_Handler,
		},
		{
			MethodName: "Update",
			Handler:    _PrpcDbNFCPatrolLineAndRules_Update_Handler,
		},
		{
			MethodName: "PartialUpdate",
			Handler:    _PrpcDbNFCPatrolLineAndRules_PartialUpdate_Handler,
		},
		{
			MethodName: "Delete",
			Handler:    _PrpcDbNFCPatrolLineAndRules_Delete_Handler,
		},
		{
			MethodName: "SelectOne",
			Handler:    _PrpcDbNFCPatrolLineAndRules_SelectOne_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "SelectMany",
			Handler:       _PrpcDbNFCPatrolLineAndRules_SelectMany_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "Query",
			Handler:       _PrpcDbNFCPatrolLineAndRules_Query_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "QueryBatch",
			Handler:       _PrpcDbNFCPatrolLineAndRules_QueryBatch_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "bysdb.rpc.proto",
}

// RpcDbMarkerUploadImageHistoryClient is the client API for RpcDbMarkerUploadImageHistory service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type RpcDbMarkerUploadImageHistoryClient interface {
	//插入一行数据
	Insert(ctx context.Context, in *DbMarkerUploadImageHistory, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//根据条件查询数据
	Query(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbMarkerUploadImageHistory_QueryClient, error)
	//根据条件查询数据,批量返回数据,crud.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbMarkerUploadImageHistory_QueryBatchClient, error)
}

type rpcDbMarkerUploadImageHistoryClient struct {
	cc *grpc.ClientConn
}

func NewRpcDbMarkerUploadImageHistoryClient(cc *grpc.ClientConn) RpcDbMarkerUploadImageHistoryClient {
	return &rpcDbMarkerUploadImageHistoryClient{cc}
}

func (c *rpcDbMarkerUploadImageHistoryClient) Insert(ctx context.Context, in *DbMarkerUploadImageHistory, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/bysdb.RpcDbMarkerUploadImageHistory/Insert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rpcDbMarkerUploadImageHistoryClient) Query(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbMarkerUploadImageHistory_QueryClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RpcDbMarkerUploadImageHistory_serviceDesc.Streams[0], "/bysdb.RpcDbMarkerUploadImageHistory/Query", opts...)
	if err != nil {
		return nil, err
	}
	x := &rpcDbMarkerUploadImageHistoryQueryClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type RpcDbMarkerUploadImageHistory_QueryClient interface {
	Recv() (*DbMarkerUploadImageHistory, error)
	grpc.ClientStream
}

type rpcDbMarkerUploadImageHistoryQueryClient struct {
	grpc.ClientStream
}

func (x *rpcDbMarkerUploadImageHistoryQueryClient) Recv() (*DbMarkerUploadImageHistory, error) {
	m := new(DbMarkerUploadImageHistory)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *rpcDbMarkerUploadImageHistoryClient) QueryBatch(ctx context.Context, in *crud.QueryParam, opts ...grpc.CallOption) (RpcDbMarkerUploadImageHistory_QueryBatchClient, error) {
	stream, err := c.cc.NewStream(ctx, &_RpcDbMarkerUploadImageHistory_serviceDesc.Streams[1], "/bysdb.RpcDbMarkerUploadImageHistory/QueryBatch", opts...)
	if err != nil {
		return nil, err
	}
	x := &rpcDbMarkerUploadImageHistoryQueryBatchClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type RpcDbMarkerUploadImageHistory_QueryBatchClient interface {
	Recv() (*DbMarkerUploadImageHistoryList, error)
	grpc.ClientStream
}

type rpcDbMarkerUploadImageHistoryQueryBatchClient struct {
	grpc.ClientStream
}

func (x *rpcDbMarkerUploadImageHistoryQueryBatchClient) Recv() (*DbMarkerUploadImageHistoryList, error) {
	m := new(DbMarkerUploadImageHistoryList)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// RpcDbMarkerUploadImageHistoryServer is the server API for RpcDbMarkerUploadImageHistory service.
type RpcDbMarkerUploadImageHistoryServer interface {
	//插入一行数据
	Insert(context.Context, *DbMarkerUploadImageHistory) (*crud.DMLResult, error)
	//根据条件查询数据
	Query(*crud.QueryParam, RpcDbMarkerUploadImageHistory_QueryServer) error
	//根据条件查询数据,批量返回数据,crud.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(*crud.QueryParam, RpcDbMarkerUploadImageHistory_QueryBatchServer) error
}

// UnimplementedRpcDbMarkerUploadImageHistoryServer can be embedded to have forward compatible implementations.
type UnimplementedRpcDbMarkerUploadImageHistoryServer struct {
}

func (*UnimplementedRpcDbMarkerUploadImageHistoryServer) Insert(ctx context.Context, req *DbMarkerUploadImageHistory) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Insert not implemented")
}
func (*UnimplementedRpcDbMarkerUploadImageHistoryServer) Query(req *crud.QueryParam, srv RpcDbMarkerUploadImageHistory_QueryServer) error {
	return status.Errorf(codes.Unimplemented, "method Query not implemented")
}
func (*UnimplementedRpcDbMarkerUploadImageHistoryServer) QueryBatch(req *crud.QueryParam, srv RpcDbMarkerUploadImageHistory_QueryBatchServer) error {
	return status.Errorf(codes.Unimplemented, "method QueryBatch not implemented")
}

func RegisterRpcDbMarkerUploadImageHistoryServer(s *grpc.Server, srv RpcDbMarkerUploadImageHistoryServer) {
	s.RegisterService(&_RpcDbMarkerUploadImageHistory_serviceDesc, srv)
}

func _RpcDbMarkerUploadImageHistory_Insert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbMarkerUploadImageHistory)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RpcDbMarkerUploadImageHistoryServer).Insert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.RpcDbMarkerUploadImageHistory/Insert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RpcDbMarkerUploadImageHistoryServer).Insert(ctx, req.(*DbMarkerUploadImageHistory))
	}
	return interceptor(ctx, in, info, handler)
}

func _RpcDbMarkerUploadImageHistory_Query_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.QueryParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RpcDbMarkerUploadImageHistoryServer).Query(m, &rpcDbMarkerUploadImageHistoryQueryServer{stream})
}

type RpcDbMarkerUploadImageHistory_QueryServer interface {
	Send(*DbMarkerUploadImageHistory) error
	grpc.ServerStream
}

type rpcDbMarkerUploadImageHistoryQueryServer struct {
	grpc.ServerStream
}

func (x *rpcDbMarkerUploadImageHistoryQueryServer) Send(m *DbMarkerUploadImageHistory) error {
	return x.ServerStream.SendMsg(m)
}

func _RpcDbMarkerUploadImageHistory_QueryBatch_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.QueryParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RpcDbMarkerUploadImageHistoryServer).QueryBatch(m, &rpcDbMarkerUploadImageHistoryQueryBatchServer{stream})
}

type RpcDbMarkerUploadImageHistory_QueryBatchServer interface {
	Send(*DbMarkerUploadImageHistoryList) error
	grpc.ServerStream
}

type rpcDbMarkerUploadImageHistoryQueryBatchServer struct {
	grpc.ServerStream
}

func (x *rpcDbMarkerUploadImageHistoryQueryBatchServer) Send(m *DbMarkerUploadImageHistoryList) error {
	return x.ServerStream.SendMsg(m)
}

var _RpcDbMarkerUploadImageHistory_serviceDesc = grpc.ServiceDesc{
	ServiceName: "bysdb.RpcDbMarkerUploadImageHistory",
	HandlerType: (*RpcDbMarkerUploadImageHistoryServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Insert",
			Handler:    _RpcDbMarkerUploadImageHistory_Insert_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "Query",
			Handler:       _RpcDbMarkerUploadImageHistory_Query_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "QueryBatch",
			Handler:       _RpcDbMarkerUploadImageHistory_QueryBatch_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "bysdb.rpc.proto",
}

// PrpcDbMarkerUploadImageHistoryClient is the client API for PrpcDbMarkerUploadImageHistory service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type PrpcDbMarkerUploadImageHistoryClient interface {
	//插入一行数据
	Insert(ctx context.Context, in *DbMarkerUploadImageHistory, opts ...grpc.CallOption) (*crud.DMLResult, error)
	//根据条件取得我有权限的数据
	Query(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbMarkerUploadImageHistory_QueryClient, error)
	//根据条件取得我有权限的数据,批量返回数据,crud.PrivilegeParam.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbMarkerUploadImageHistory_QueryBatchClient, error)
}

type prpcDbMarkerUploadImageHistoryClient struct {
	cc *grpc.ClientConn
}

func NewPrpcDbMarkerUploadImageHistoryClient(cc *grpc.ClientConn) PrpcDbMarkerUploadImageHistoryClient {
	return &prpcDbMarkerUploadImageHistoryClient{cc}
}

func (c *prpcDbMarkerUploadImageHistoryClient) Insert(ctx context.Context, in *DbMarkerUploadImageHistory, opts ...grpc.CallOption) (*crud.DMLResult, error) {
	out := new(crud.DMLResult)
	err := c.cc.Invoke(ctx, "/bysdb.PrpcDbMarkerUploadImageHistory/Insert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prpcDbMarkerUploadImageHistoryClient) Query(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbMarkerUploadImageHistory_QueryClient, error) {
	stream, err := c.cc.NewStream(ctx, &_PrpcDbMarkerUploadImageHistory_serviceDesc.Streams[0], "/bysdb.PrpcDbMarkerUploadImageHistory/Query", opts...)
	if err != nil {
		return nil, err
	}
	x := &prpcDbMarkerUploadImageHistoryQueryClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type PrpcDbMarkerUploadImageHistory_QueryClient interface {
	Recv() (*DbMarkerUploadImageHistory, error)
	grpc.ClientStream
}

type prpcDbMarkerUploadImageHistoryQueryClient struct {
	grpc.ClientStream
}

func (x *prpcDbMarkerUploadImageHistoryQueryClient) Recv() (*DbMarkerUploadImageHistory, error) {
	m := new(DbMarkerUploadImageHistory)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *prpcDbMarkerUploadImageHistoryClient) QueryBatch(ctx context.Context, in *crud.PrivilegeParam, opts ...grpc.CallOption) (PrpcDbMarkerUploadImageHistory_QueryBatchClient, error) {
	stream, err := c.cc.NewStream(ctx, &_PrpcDbMarkerUploadImageHistory_serviceDesc.Streams[1], "/bysdb.PrpcDbMarkerUploadImageHistory/QueryBatch", opts...)
	if err != nil {
		return nil, err
	}
	x := &prpcDbMarkerUploadImageHistoryQueryBatchClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type PrpcDbMarkerUploadImageHistory_QueryBatchClient interface {
	Recv() (*DbMarkerUploadImageHistoryList, error)
	grpc.ClientStream
}

type prpcDbMarkerUploadImageHistoryQueryBatchClient struct {
	grpc.ClientStream
}

func (x *prpcDbMarkerUploadImageHistoryQueryBatchClient) Recv() (*DbMarkerUploadImageHistoryList, error) {
	m := new(DbMarkerUploadImageHistoryList)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// PrpcDbMarkerUploadImageHistoryServer is the server API for PrpcDbMarkerUploadImageHistory service.
type PrpcDbMarkerUploadImageHistoryServer interface {
	//插入一行数据
	Insert(context.Context, *DbMarkerUploadImageHistory) (*crud.DMLResult, error)
	//根据条件取得我有权限的数据
	Query(*crud.PrivilegeParam, PrpcDbMarkerUploadImageHistory_QueryServer) error
	//根据条件取得我有权限的数据,批量返回数据,crud.PrivilegeParam.QueryParam.Batch指定一次返回多少条数据
	QueryBatch(*crud.PrivilegeParam, PrpcDbMarkerUploadImageHistory_QueryBatchServer) error
}

// UnimplementedPrpcDbMarkerUploadImageHistoryServer can be embedded to have forward compatible implementations.
type UnimplementedPrpcDbMarkerUploadImageHistoryServer struct {
}

func (*UnimplementedPrpcDbMarkerUploadImageHistoryServer) Insert(ctx context.Context, req *DbMarkerUploadImageHistory) (*crud.DMLResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Insert not implemented")
}
func (*UnimplementedPrpcDbMarkerUploadImageHistoryServer) Query(req *crud.PrivilegeParam, srv PrpcDbMarkerUploadImageHistory_QueryServer) error {
	return status.Errorf(codes.Unimplemented, "method Query not implemented")
}
func (*UnimplementedPrpcDbMarkerUploadImageHistoryServer) QueryBatch(req *crud.PrivilegeParam, srv PrpcDbMarkerUploadImageHistory_QueryBatchServer) error {
	return status.Errorf(codes.Unimplemented, "method QueryBatch not implemented")
}

func RegisterPrpcDbMarkerUploadImageHistoryServer(s *grpc.Server, srv PrpcDbMarkerUploadImageHistoryServer) {
	s.RegisterService(&_PrpcDbMarkerUploadImageHistory_serviceDesc, srv)
}

func _PrpcDbMarkerUploadImageHistory_Insert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DbMarkerUploadImageHistory)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrpcDbMarkerUploadImageHistoryServer).Insert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/bysdb.PrpcDbMarkerUploadImageHistory/Insert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrpcDbMarkerUploadImageHistoryServer).Insert(ctx, req.(*DbMarkerUploadImageHistory))
	}
	return interceptor(ctx, in, info, handler)
}

func _PrpcDbMarkerUploadImageHistory_Query_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.PrivilegeParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(PrpcDbMarkerUploadImageHistoryServer).Query(m, &prpcDbMarkerUploadImageHistoryQueryServer{stream})
}

type PrpcDbMarkerUploadImageHistory_QueryServer interface {
	Send(*DbMarkerUploadImageHistory) error
	grpc.ServerStream
}

type prpcDbMarkerUploadImageHistoryQueryServer struct {
	grpc.ServerStream
}

func (x *prpcDbMarkerUploadImageHistoryQueryServer) Send(m *DbMarkerUploadImageHistory) error {
	return x.ServerStream.SendMsg(m)
}

func _PrpcDbMarkerUploadImageHistory_QueryBatch_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(crud.PrivilegeParam)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(PrpcDbMarkerUploadImageHistoryServer).QueryBatch(m, &prpcDbMarkerUploadImageHistoryQueryBatchServer{stream})
}

type PrpcDbMarkerUploadImageHistory_QueryBatchServer interface {
	Send(*DbMarkerUploadImageHistoryList) error
	grpc.ServerStream
}

type prpcDbMarkerUploadImageHistoryQueryBatchServer struct {
	grpc.ServerStream
}

func (x *prpcDbMarkerUploadImageHistoryQueryBatchServer) Send(m *DbMarkerUploadImageHistoryList) error {
	return x.ServerStream.SendMsg(m)
}

var _PrpcDbMarkerUploadImageHistory_serviceDesc = grpc.ServiceDesc{
	ServiceName: "bysdb.PrpcDbMarkerUploadImageHistory",
	HandlerType: (*PrpcDbMarkerUploadImageHistoryServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Insert",
			Handler:    _PrpcDbMarkerUploadImageHistory_Insert_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "Query",
			Handler:       _PrpcDbMarkerUploadImageHistory_Query_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "QueryBatch",
			Handler:       _PrpcDbMarkerUploadImageHistory_QueryBatch_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "bysdb.rpc.proto",
}

// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: bysdb.proto

package bysdb

import (
	encoding_binary "encoding/binary"
	fmt "fmt"
	proto "github.com/gogo/protobuf/proto"
	io "io"
	math "math"
	math_bits "math/bits"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

// 控制器信息表
// @rpc crud pcrud
// @dbpost CREATE INDEX IF NOT EXISTS idxDbControllerOrgRID on DbController USING hash(OrgRID);
// @dbpost CREATE INDEX IF NOT EXISTS idxDbControllerParentRID on DbController USING hash(ParentRID);
// @dbpost CREATE UNIQUE INDEX IF NOT EXISTS uidxDbControllerParent ON DbController (ParentRID, ParentChannelNo) WHERE ControllerType=1;
type DbController struct {
	// @db uuid primary key
	// 行ID
	RID string `protobuf:"bytes,1,opt,name=RID,proto3" json:"RID,omitempty" ykit:"prk"`
	// @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
	// 控制器所属的群组
	OrgRID string `protobuf:"bytes,2,opt,name=OrgRID,proto3" json:"OrgRID,omitempty" ykit:"notnull"`
	// @db varchar(16) not null unique
	// controller编号
	ControllerNo string `protobuf:"bytes,3,opt,name=ControllerNo,proto3" json:"ControllerNo,omitempty" ykit:"unique,notnull"`
	// @db text
	// controller描述信息
	ControllerDescription string `protobuf:"bytes,4,opt,name=ControllerDescription,proto3" json:"ControllerDescription,omitempty" ykit:"null"`
	// @db int
	// 控制器类型 1:中继控制器  2:基站控制器
	ControllerType int32 `protobuf:"varint,5,opt,name=ControllerType,proto3" json:"ControllerType,omitempty" ykit:"null"`
	// @db uuid
	// 上级控制器RID, 可以没有，中继控制器一般都有
	ParentRID string `protobuf:"bytes,6,opt,name=ParentRID,proto3" json:"ParentRID,omitempty" ykit:"null"`
	// @db double precision
	// 经度
	Lon float64 `protobuf:"fixed64,7,opt,name=Lon,proto3" json:"Lon,omitempty" ykit:"null"`
	// @db double precision
	// 纬度
	Lat float64 `protobuf:"fixed64,8,opt,name=Lat,proto3" json:"Lat,omitempty" ykit:"null"`
	// @db jsonb not null default  '{}'::jsonb
	// 其它设置，可以在里面扩展需要用到的其它信息
	Setting string `protobuf:"bytes,9,opt,name=Setting,proto3" json:"Setting,omitempty" ykit:"notnull"`
	// @db int not null unique
	// 控制器硬件ID，int32 > 0
	ControllerHWID int32 `protobuf:"zigzag32,10,opt,name=ControllerHWID,proto3" json:"ControllerHWID,omitempty" ykit:"unique,notnull"`
	// @db int not null default 1
	// 控制器可用信道数，中继控制器为1,基站按实际情况
	ChannelCount int32 `protobuf:"zigzag32,11,opt,name=ChannelCount,proto3" json:"ChannelCount,omitempty" ykit:"notnull"`
	// @db int default 12
	// 地图开始显示级别
	MapShowLevel int32 `protobuf:"zigzag32,12,opt,name=MapShowLevel,proto3" json:"MapShowLevel,omitempty"`
	// @db timestamp not null default now_utc()
	// 数据最后修改时间
	UpdatedAt string `protobuf:"bytes,14,opt,name=UpdatedAt,proto3" json:"UpdatedAt,omitempty" ykit:"notnull"`
	// @db text
	// 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
	UpdatedDC string `protobuf:"bytes,15,opt,name=UpdatedDC,proto3" json:"UpdatedDC,omitempty" ykit:"null"`
	// @db int
	// 默认通讯方式， 1:有线网络， 2:4g网络 3:无线FSK
	DefaultNetworkType int32 `protobuf:"zigzag32,16,opt,name=DefaultNetworkType,proto3" json:"DefaultNetworkType,omitempty" ykit:"null"`
	// @db int
	// 中继对应的上级控制器的通道
	ParentChannelNo int32 `protobuf:"zigzag32,17,opt,name=ParentChannelNo,proto3" json:"ParentChannelNo,omitempty" ykit:"null"`
}

func (m *DbController) Reset()         { *m = DbController{} }
func (m *DbController) String() string { return proto.CompactTextString(m) }
func (*DbController) ProtoMessage()    {}
func (*DbController) Descriptor() ([]byte, []int) {
	return fileDescriptor_ee9b802a25f08f08, []int{0}
}
func (m *DbController) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbController) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbController.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbController) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbController.Merge(m, src)
}
func (m *DbController) XXX_Size() int {
	return m.Size()
}
func (m *DbController) XXX_DiscardUnknown() {
	xxx_messageInfo_DbController.DiscardUnknown(m)
}

var xxx_messageInfo_DbController proto.InternalMessageInfo

func (m *DbController) GetRID() string {
	if m != nil {
		return m.RID
	}
	return ""
}

func (m *DbController) GetOrgRID() string {
	if m != nil {
		return m.OrgRID
	}
	return ""
}

func (m *DbController) GetControllerNo() string {
	if m != nil {
		return m.ControllerNo
	}
	return ""
}

func (m *DbController) GetControllerDescription() string {
	if m != nil {
		return m.ControllerDescription
	}
	return ""
}

func (m *DbController) GetControllerType() int32 {
	if m != nil {
		return m.ControllerType
	}
	return 0
}

func (m *DbController) GetParentRID() string {
	if m != nil {
		return m.ParentRID
	}
	return ""
}

func (m *DbController) GetLon() float64 {
	if m != nil {
		return m.Lon
	}
	return 0
}

func (m *DbController) GetLat() float64 {
	if m != nil {
		return m.Lat
	}
	return 0
}

func (m *DbController) GetSetting() string {
	if m != nil {
		return m.Setting
	}
	return ""
}

func (m *DbController) GetControllerHWID() int32 {
	if m != nil {
		return m.ControllerHWID
	}
	return 0
}

func (m *DbController) GetChannelCount() int32 {
	if m != nil {
		return m.ChannelCount
	}
	return 0
}

func (m *DbController) GetMapShowLevel() int32 {
	if m != nil {
		return m.MapShowLevel
	}
	return 0
}

func (m *DbController) GetUpdatedAt() string {
	if m != nil {
		return m.UpdatedAt
	}
	return ""
}

func (m *DbController) GetUpdatedDC() string {
	if m != nil {
		return m.UpdatedDC
	}
	return ""
}

func (m *DbController) GetDefaultNetworkType() int32 {
	if m != nil {
		return m.DefaultNetworkType
	}
	return 0
}

func (m *DbController) GetParentChannelNo() int32 {
	if m != nil {
		return m.ParentChannelNo
	}
	return 0
}

// 界桩信息表
// @rpc crud pcrud
// @dbpost CREATE INDEX IF NOT EXISTS idxDbBysMarkerOrgRID on DbBysMarker USING hash(OrgRID);
// @dbpost ALTER TABLE DbBysMarker ADD COLUMN IF NOT EXISTS MarkerType integer not null default 0;
// @dbpost ALTER TABLE DbBysMarker ADD COLUMN IF NOT EXISTS MarkerSettings jsonb not null default  '{}'::jsonb;
// @dbpost ALTER TABLE dbbysmarker DROP CONSTRAINT if exists dbbysmarker_controllerrid_controllerchannel_markerqueueno_key;
// @dbpost drop index if exists dbbysmarker_controllerrid_controllerchannel_markerqueueno_key;
// @dbpost CREATE UNIQUE INDEX IF NOT EXISTS idxdbbysmarker_controllerrid_controllerchannel_markerqueueno_key on DbBysMarker (ControllerRID, ControllerChannel, MarkerQueueNo) where MarkerType=0; --同一个信道的排队号必须唯一,只针对旧的界桩
// @dbpost ALTER TABLE DbBysMarker ALTER COLUMN ControllerRID DROP NOT NULL;
// @dbpost ALTER TABLE DbBysMarker ADD COLUMN IF NOT EXISTS ICCID varchar(20);
// @dbpost ALTER TABLE DbBysMarker ADD COLUMN IF NOT EXISTS ExpirationDate timestamp;
// @dbpost CREATE UNIQUE INDEX IF NOT EXISTS idxDbBysMarkerICCID on DbBysMarker (ICCID) where MarkerType=1;
// @dbpost ALTER TABLE DbBysMarker ADD COLUMN IF NOT EXISTS IMEI text;
// @dbpost ALTER TABLE DbBysMarker ADD COLUMN IF NOT EXISTS CameraDisabled integer not null default 0;
type DbBysMarker struct {
	// @db uuid primary key
	// 行ID
	RID string `protobuf:"bytes,1,opt,name=RID,proto3" json:"RID,omitempty" ykit:"prk"`
	// @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
	// 界桩所属的群组
	OrgRID string `protobuf:"bytes,2,opt,name=OrgRID,proto3" json:"OrgRID,omitempty" ykit:"notnull"`
	// @db varchar(16) not null unique
	// 界桩编号
	MarkerNo string `protobuf:"bytes,3,opt,name=MarkerNo,proto3" json:"MarkerNo,omitempty" ykit:"unique,notnull"`
	// @db text
	// 界桩描述信息
	MarkerDescription string `protobuf:"bytes,4,opt,name=MarkerDescription,proto3" json:"MarkerDescription,omitempty" ykit:"null"`
	// @db uuid REFERENCES DbController(RID) ON DELETE RESTRICT
	// 所属控制器RID
	ControllerRID string `protobuf:"bytes,5,opt,name=ControllerRID,proto3" json:"ControllerRID,omitempty" ykit:"null"`
	// @db int not null
	// 所属的基站通道，如果所属的控制器为中继控制器，此值固定为1
	ControllerChannel int32 `protobuf:"zigzag32,6,opt,name=ControllerChannel,proto3" json:"ControllerChannel,omitempty" ykit:"notnull"`
	// @db double precision
	// 经度
	Lon float64 `protobuf:"fixed64,7,opt,name=Lon,proto3" json:"Lon,omitempty" ykit:"null"`
	// @db double precision
	// 纬度
	Lat float64 `protobuf:"fixed64,8,opt,name=Lat,proto3" json:"Lat,omitempty" ykit:"null"`
	// @db int not null unique
	// 界桩硬件ID,范围
	MarkerHWID int32 `protobuf:"zigzag32,9,opt,name=MarkerHWID,proto3" json:"MarkerHWID,omitempty" ykit:"unique,notnull"`
	// @db jsonb not null default  '{}'::jsonb
	// 其它设置，可以在里面扩展需要用到的其它信息
	// 新的红线图规则属性为：redLine，不存在该字段为默认连接，空数组为不连接，
	// 数组元素为界桩硬件ID，每个都连接
	Setting string `protobuf:"bytes,10,opt,name=Setting,proto3" json:"Setting,omitempty" ykit:"notnull"`
	// @db int
	// 界桩型号 1:长型 X*Y*Z cm, 2:短型 x*y*z cm
	MarkerModel int32 `protobuf:"zigzag32,11,opt,name=MarkerModel,proto3" json:"MarkerModel,omitempty" ykit:"null"`
	// @db int default 12
	// 地图开始显示级别
	MapShowLevel int32 `protobuf:"zigzag32,12,opt,name=MapShowLevel,proto3" json:"MapShowLevel,omitempty"`
	// @db int not null
	// 在所属于基站下的排队号
	MarkerQueueNo int32 `protobuf:"zigzag32,13,opt,name=MarkerQueueNo,proto3" json:"MarkerQueueNo,omitempty" ykit:"notnull"`
	// @db timestamp not null default now_utc()
	// 数据最后修改时间
	UpdatedAt string `protobuf:"bytes,14,opt,name=UpdatedAt,proto3" json:"UpdatedAt,omitempty" ykit:"notnull"`
	// @db text
	// 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
	UpdatedDC string `protobuf:"bytes,15,opt,name=UpdatedDC,proto3" json:"UpdatedDC,omitempty" ykit:"null"`
	// @db timestamp not null default now_utc()
	// 参数更新时间
	MarkerParamTime string `protobuf:"bytes,16,opt,name=MarkerParamTime,proto3" json:"MarkerParamTime,omitempty" ykit:"notnull"`
	// @db int not null default 24
	// 打卡时间间隔(6-24小时)
	MarkerDayInterval int32 `protobuf:"zigzag32,17,opt,name=MarkerDayInterval,proto3" json:"MarkerDayInterval,omitempty" ykit:"notnull"`
	// @db int not null default 6
	// 排队发射间隔时间（秒）
	MarkerQueueInterval int32 `protobuf:"zigzag32,18,opt,name=MarkerQueueInterval,proto3" json:"MarkerQueueInterval,omitempty" ykit:"notnull"`
	// @db int not null default 60
	// 报警后发射间隔(30-240秒)
	MarkerEmergentInterval int32 `protobuf:"zigzag32,19,opt,name=MarkerEmergentInterval,proto3" json:"MarkerEmergentInterval,omitempty" ykit:"notnull"`
	// @db int not null default 1
	// 界桩通信信道
	MarkerChannel int32 `protobuf:"zigzag32,20,opt,name=MarkerChannel,proto3" json:"MarkerChannel,omitempty" ykit:"notnull"`
	// @db time not null
	// 界桩苏醒基准时间
	MarkerWakeupBaseTime string `protobuf:"bytes,21,opt,name=MarkerWakeupBaseTime,proto3" json:"MarkerWakeupBaseTime,omitempty" ykit:"notnull"`
	// @db int not null default 0
	// 界桩是否已经被遥毙, 0:遥活 1:遥闭  2:遥晕
	MarkerDisabled int32 `protobuf:"zigzag32,22,opt,name=MarkerDisabled,proto3" json:"MarkerDisabled,omitempty" ykit:"notnull"`
	// @db boolean default true
	// 界桩是否有安装电子设备
	HasInstallDevice bool `protobuf:"varint,23,opt,name=HasInstallDevice,proto3" json:"HasInstallDevice,omitempty"`
	// @db boolean default false
	// 石头界桩是否已安装
	HasInstallStone bool `protobuf:"varint,25,opt,name=HasInstallStone,proto3" json:"HasInstallStone,omitempty"`
	// @db int not null default 0
	// 常规界桩：0, 4g界桩(BF-DTD002): 1, 4g界桩带摄像机(BF-DTD002(Pro)): 2, 专网界桩dmr版(BF-DTD003): 3
	MarkerType int32 `protobuf:"zigzag32,26,opt,name=MarkerType,proto3" json:"MarkerType,omitempty" ykit:"notnull"`
	// @db jsonb not null default  '{}'::jsonb
	// MarkerType=1时，填写bysproto.BDataUpdate
	MarkerSettings string `protobuf:"bytes,27,opt,name=MarkerSettings,proto3" json:"MarkerSettings,omitempty" ykit:"notnull"`
	// @db varchar(20)
	// 4G界桩的iccid卡号，20位数字字符串
	ICCID string `protobuf:"bytes,28,opt,name=ICCID,proto3" json:"ICCID,omitempty" ykit:"null"`
	// @db timestamp
	// iccid卡号到期日期
	ExpirationDate string `protobuf:"bytes,29,opt,name=ExpirationDate,proto3" json:"ExpirationDate,omitempty" ykit:"null"`
	// @db text
	// 4g界桩的设备唯一标识
	IMEI string `protobuf:"bytes,30,opt,name=IMEI,proto3" json:"IMEI,omitempty" ykit:"null"`
	// @db int not null default 0
	// 摄像机是否已经被遥毙, 0:遥活 1:遥闭  2:遥晕
	CameraDisabled int32 `protobuf:"zigzag32,31,opt,name=CameraDisabled,proto3" json:"CameraDisabled,omitempty" ykit:"notnull"`
}

func (m *DbBysMarker) Reset()         { *m = DbBysMarker{} }
func (m *DbBysMarker) String() string { return proto.CompactTextString(m) }
func (*DbBysMarker) ProtoMessage()    {}
func (*DbBysMarker) Descriptor() ([]byte, []int) {
	return fileDescriptor_ee9b802a25f08f08, []int{1}
}
func (m *DbBysMarker) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbBysMarker) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbBysMarker.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbBysMarker) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbBysMarker.Merge(m, src)
}
func (m *DbBysMarker) XXX_Size() int {
	return m.Size()
}
func (m *DbBysMarker) XXX_DiscardUnknown() {
	xxx_messageInfo_DbBysMarker.DiscardUnknown(m)
}

var xxx_messageInfo_DbBysMarker proto.InternalMessageInfo

func (m *DbBysMarker) GetRID() string {
	if m != nil {
		return m.RID
	}
	return ""
}

func (m *DbBysMarker) GetOrgRID() string {
	if m != nil {
		return m.OrgRID
	}
	return ""
}

func (m *DbBysMarker) GetMarkerNo() string {
	if m != nil {
		return m.MarkerNo
	}
	return ""
}

func (m *DbBysMarker) GetMarkerDescription() string {
	if m != nil {
		return m.MarkerDescription
	}
	return ""
}

func (m *DbBysMarker) GetControllerRID() string {
	if m != nil {
		return m.ControllerRID
	}
	return ""
}

func (m *DbBysMarker) GetControllerChannel() int32 {
	if m != nil {
		return m.ControllerChannel
	}
	return 0
}

func (m *DbBysMarker) GetLon() float64 {
	if m != nil {
		return m.Lon
	}
	return 0
}

func (m *DbBysMarker) GetLat() float64 {
	if m != nil {
		return m.Lat
	}
	return 0
}

func (m *DbBysMarker) GetMarkerHWID() int32 {
	if m != nil {
		return m.MarkerHWID
	}
	return 0
}

func (m *DbBysMarker) GetSetting() string {
	if m != nil {
		return m.Setting
	}
	return ""
}

func (m *DbBysMarker) GetMarkerModel() int32 {
	if m != nil {
		return m.MarkerModel
	}
	return 0
}

func (m *DbBysMarker) GetMapShowLevel() int32 {
	if m != nil {
		return m.MapShowLevel
	}
	return 0
}

func (m *DbBysMarker) GetMarkerQueueNo() int32 {
	if m != nil {
		return m.MarkerQueueNo
	}
	return 0
}

func (m *DbBysMarker) GetUpdatedAt() string {
	if m != nil {
		return m.UpdatedAt
	}
	return ""
}

func (m *DbBysMarker) GetUpdatedDC() string {
	if m != nil {
		return m.UpdatedDC
	}
	return ""
}

func (m *DbBysMarker) GetMarkerParamTime() string {
	if m != nil {
		return m.MarkerParamTime
	}
	return ""
}

func (m *DbBysMarker) GetMarkerDayInterval() int32 {
	if m != nil {
		return m.MarkerDayInterval
	}
	return 0
}

func (m *DbBysMarker) GetMarkerQueueInterval() int32 {
	if m != nil {
		return m.MarkerQueueInterval
	}
	return 0
}

func (m *DbBysMarker) GetMarkerEmergentInterval() int32 {
	if m != nil {
		return m.MarkerEmergentInterval
	}
	return 0
}

func (m *DbBysMarker) GetMarkerChannel() int32 {
	if m != nil {
		return m.MarkerChannel
	}
	return 0
}

func (m *DbBysMarker) GetMarkerWakeupBaseTime() string {
	if m != nil {
		return m.MarkerWakeupBaseTime
	}
	return ""
}

func (m *DbBysMarker) GetMarkerDisabled() int32 {
	if m != nil {
		return m.MarkerDisabled
	}
	return 0
}

func (m *DbBysMarker) GetHasInstallDevice() bool {
	if m != nil {
		return m.HasInstallDevice
	}
	return false
}

func (m *DbBysMarker) GetHasInstallStone() bool {
	if m != nil {
		return m.HasInstallStone
	}
	return false
}

func (m *DbBysMarker) GetMarkerType() int32 {
	if m != nil {
		return m.MarkerType
	}
	return 0
}

func (m *DbBysMarker) GetMarkerSettings() string {
	if m != nil {
		return m.MarkerSettings
	}
	return ""
}

func (m *DbBysMarker) GetICCID() string {
	if m != nil {
		return m.ICCID
	}
	return ""
}

func (m *DbBysMarker) GetExpirationDate() string {
	if m != nil {
		return m.ExpirationDate
	}
	return ""
}

func (m *DbBysMarker) GetIMEI() string {
	if m != nil {
		return m.IMEI
	}
	return ""
}

func (m *DbBysMarker) GetCameraDisabled() int32 {
	if m != nil {
		return m.CameraDisabled
	}
	return 0
}

// 控制器上下线历史表，以月为单位分表
// @rpc pcrud
// @dbend PARTITION BY RANGE (ActionTime)
// @dbpost CREATE INDEX IF NOT EXISTS idxDbControllerOnlineHistoryControllerHWID on DbControllerOnlineHistory USING hash(ControllerHWID);
// @dbpost CREATE INDEX IF NOT EXISTS idxDbControllerOnlineHistoryOrgRID on DbControllerOnlineHistory USING hash(OrgRID);
// @dbpost ALTER TABLE dbcontrolleronlinehistory ADD COLUMN IF NOT EXISTS Status integer;
// @dbpost ALTER TABLE dbcontrolleronlinehistory ADD COLUMN IF NOT EXISTS DeviceType integer;
type DbControllerOnlineHistory struct {
	// @db uuid not null
	// 所属的群组
	OrgRID string `protobuf:"bytes,2,opt,name=OrgRID,proto3" json:"OrgRID,omitempty" ykit:"notnull"`
	// @db timestamp not null
	// 动作时间,utc
	ActionTime string `protobuf:"bytes,3,opt,name=ActionTime,proto3" json:"ActionTime,omitempty" ykit:"notnull"`
	// @db int
	// 控制器硬件ID
	ControllerHWID int32 `protobuf:"zigzag32,4,opt,name=ControllerHWID,proto3" json:"ControllerHWID,omitempty" ykit:"null"`
	// @db int
	// action 1:上线 2:下线 11:ping信息
	ActionCode int32 `protobuf:"zigzag32,5,opt,name=ActionCode,proto3" json:"ActionCode,omitempty" ykit:"null"`
	// @db text
	// ip/状态信息
	IpInfo string `protobuf:"bytes,6,opt,name=IpInfo,proto3" json:"IpInfo,omitempty" ykit:"null"`
	// @db int
	// 登录网络类型,只对上线有效
	NetworkType int32 `protobuf:"zigzag32,7,opt,name=NetworkType,proto3" json:"NetworkType,omitempty" ykit:"null"`
	// @db real
	// 电量V
	Power float32 `protobuf:"fixed32,8,opt,name=Power,proto3" json:"Power,omitempty" ykit:"null"`
	// @db int
	// 状态
	Status uint32 `protobuf:"varint,9,opt,name=Status,proto3" json:"Status,omitempty" ykit:"null"`
	// 登录设备类型 0:fsk控制器 1:4g界桩
	DeviceType int32 `protobuf:"varint,10,opt,name=DeviceType,proto3" json:"DeviceType,omitempty" ykit:"nodb"`
}

func (m *DbControllerOnlineHistory) Reset()         { *m = DbControllerOnlineHistory{} }
func (m *DbControllerOnlineHistory) String() string { return proto.CompactTextString(m) }
func (*DbControllerOnlineHistory) ProtoMessage()    {}
func (*DbControllerOnlineHistory) Descriptor() ([]byte, []int) {
	return fileDescriptor_ee9b802a25f08f08, []int{2}
}
func (m *DbControllerOnlineHistory) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbControllerOnlineHistory) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbControllerOnlineHistory.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbControllerOnlineHistory) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbControllerOnlineHistory.Merge(m, src)
}
func (m *DbControllerOnlineHistory) XXX_Size() int {
	return m.Size()
}
func (m *DbControllerOnlineHistory) XXX_DiscardUnknown() {
	xxx_messageInfo_DbControllerOnlineHistory.DiscardUnknown(m)
}

var xxx_messageInfo_DbControllerOnlineHistory proto.InternalMessageInfo

func (m *DbControllerOnlineHistory) GetOrgRID() string {
	if m != nil {
		return m.OrgRID
	}
	return ""
}

func (m *DbControllerOnlineHistory) GetActionTime() string {
	if m != nil {
		return m.ActionTime
	}
	return ""
}

func (m *DbControllerOnlineHistory) GetControllerHWID() int32 {
	if m != nil {
		return m.ControllerHWID
	}
	return 0
}

func (m *DbControllerOnlineHistory) GetActionCode() int32 {
	if m != nil {
		return m.ActionCode
	}
	return 0
}

func (m *DbControllerOnlineHistory) GetIpInfo() string {
	if m != nil {
		return m.IpInfo
	}
	return ""
}

func (m *DbControllerOnlineHistory) GetNetworkType() int32 {
	if m != nil {
		return m.NetworkType
	}
	return 0
}

func (m *DbControllerOnlineHistory) GetPower() float32 {
	if m != nil {
		return m.Power
	}
	return 0
}

func (m *DbControllerOnlineHistory) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *DbControllerOnlineHistory) GetDeviceType() int32 {
	if m != nil {
		return m.DeviceType
	}
	return 0
}

// 界桩/控制器媒体信息表
// @rpc pcrud
// @dbpost CREATE INDEX IF NOT EXISTS idxDbMediaInfoOrgRID on DbMediaInfo USING hash(OrgRID);
// @dbpost CREATE INDEX IF NOT EXISTS idxDbMediaInfoMarkerRID on DbMediaInfo USING hash(MarkerRID);
// @dbpost CREATE INDEX IF NOT EXISTS idxDbMediaInfoControllerRID on DbMediaInfo USING hash(ControllerRID);
type DbMediaInfo struct {
	// @db uuid primary key
	// 行ID
	RID string `protobuf:"bytes,1,opt,name=RID,proto3" json:"RID,omitempty" ykit:"prk"`
	// @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
	// 物体所属的群组
	OrgRID string `protobuf:"bytes,2,opt,name=OrgRID,proto3" json:"OrgRID,omitempty" ykit:"notnull"`
	// @db uuid null REFERENCES DbBysMarker(RID) ON DELETE set null
	// 界桩的RID
	MarkerRID string `protobuf:"bytes,3,opt,name=MarkerRID,proto3" json:"MarkerRID,omitempty" ykit:"null"`
	// @db uuid null REFERENCES DbController(RID) ON DELETE set null
	// 控制器的RID
	ControllerRID string `protobuf:"bytes,4,opt,name=ControllerRID,proto3" json:"ControllerRID,omitempty" ykit:"null"`
	// @db int
	// 媒体类型 1:normal pic 2:3d pic 3:video
	MediaType int32 `protobuf:"zigzag32,5,opt,name=MediaType,proto3" json:"MediaType,omitempty" ykit:"null"`
	// @db text
	// 媒体原始文件名
	MediaOrigFileName string `protobuf:"bytes,6,opt,name=MediaOrigFileName,proto3" json:"MediaOrigFileName,omitempty" ykit:"null"`
	// @db uuid REFERENCES DbUser(RID) ON DELETE set null
	// 上传用户rid
	UploadUserRID string `protobuf:"bytes,7,opt,name=UploadUserRID,proto3" json:"UploadUserRID,omitempty" ykit:"null"`
	// @db text
	// 描述信息
	MediaDescription string `protobuf:"bytes,8,opt,name=MediaDescription,proto3" json:"MediaDescription,omitempty" ykit:"null"`
	// @db timestamp not null
	// 上传时间,utc
	UploadTime string `protobuf:"bytes,9,opt,name=UploadTime,proto3" json:"UploadTime,omitempty" ykit:"notnull"`
	// @db jsonb not null default  '{}'::jsonb
	// 其它设置，可以在里面扩展需要用到的其它信息
	Setting string `protobuf:"bytes,10,opt,name=Setting,proto3" json:"Setting,omitempty" ykit:"notnull"`
	// @db uuid REFERENCES DbUser(RID) ON DELETE set null
	// 最新编辑用户rid
	LastUpdateUserRID string `protobuf:"bytes,11,opt,name=LastUpdateUserRID,proto3" json:"LastUpdateUserRID,omitempty" ykit:"null"`
	// @db timestamp not null default now_utc()
	// 最新编辑时间,utc
	LastUpdateTime string `protobuf:"bytes,12,opt,name=LastUpdateTime,proto3" json:"LastUpdateTime,omitempty" ykit:"notnull"`
}

func (m *DbMediaInfo) Reset()         { *m = DbMediaInfo{} }
func (m *DbMediaInfo) String() string { return proto.CompactTextString(m) }
func (*DbMediaInfo) ProtoMessage()    {}
func (*DbMediaInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_ee9b802a25f08f08, []int{3}
}
func (m *DbMediaInfo) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbMediaInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbMediaInfo.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbMediaInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbMediaInfo.Merge(m, src)
}
func (m *DbMediaInfo) XXX_Size() int {
	return m.Size()
}
func (m *DbMediaInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_DbMediaInfo.DiscardUnknown(m)
}

var xxx_messageInfo_DbMediaInfo proto.InternalMessageInfo

func (m *DbMediaInfo) GetRID() string {
	if m != nil {
		return m.RID
	}
	return ""
}

func (m *DbMediaInfo) GetOrgRID() string {
	if m != nil {
		return m.OrgRID
	}
	return ""
}

func (m *DbMediaInfo) GetMarkerRID() string {
	if m != nil {
		return m.MarkerRID
	}
	return ""
}

func (m *DbMediaInfo) GetControllerRID() string {
	if m != nil {
		return m.ControllerRID
	}
	return ""
}

func (m *DbMediaInfo) GetMediaType() int32 {
	if m != nil {
		return m.MediaType
	}
	return 0
}

func (m *DbMediaInfo) GetMediaOrigFileName() string {
	if m != nil {
		return m.MediaOrigFileName
	}
	return ""
}

func (m *DbMediaInfo) GetUploadUserRID() string {
	if m != nil {
		return m.UploadUserRID
	}
	return ""
}

func (m *DbMediaInfo) GetMediaDescription() string {
	if m != nil {
		return m.MediaDescription
	}
	return ""
}

func (m *DbMediaInfo) GetUploadTime() string {
	if m != nil {
		return m.UploadTime
	}
	return ""
}

func (m *DbMediaInfo) GetSetting() string {
	if m != nil {
		return m.Setting
	}
	return ""
}

func (m *DbMediaInfo) GetLastUpdateUserRID() string {
	if m != nil {
		return m.LastUpdateUserRID
	}
	return ""
}

func (m *DbMediaInfo) GetLastUpdateTime() string {
	if m != nil {
		return m.LastUpdateTime
	}
	return ""
}

// 界桩上传数据历史表，以月为单位分表
// @rpc pcrud
// @dbend PARTITION BY RANGE (ActionTime)
// @dbpost CREATE INDEX IF NOT EXISTS idxDbMarkerHistoryMarkerHWID on DbMarkerHistory USING hash(MarkerHWID);
// @dbpost CREATE INDEX IF NOT EXISTS idxDbMarkerHistoryOrgRID on DbMarkerHistory USING hash(OrgRID);
// @dbpost CREATE INDEX IF NOT EXISTS idxDbMarkerHistoryEmergency on DbMarkerHistory (Status) where (Status & 128) >0;
// @dbpost alter table DbMarkerHistory ADD COLUMN IF NOT EXISTS RecvControllerID int;
// @dbpost alter table DbMarkerHistory ADD COLUMN IF NOT EXISTS ReportInfo jsonb not null default  '{}'::jsonb;
type DbMarkerHistory struct {
	// @db int
	// 接收的控制器ID
	ControllerID int32 `protobuf:"zigzag32,1,opt,name=ControllerID,proto3" json:"ControllerID,omitempty" ykit:"null"`
	// @db int
	// 接收的控制器通道
	ControllerChannel int32 `protobuf:"zigzag32,2,opt,name=ControllerChannel,proto3" json:"ControllerChannel,omitempty" ykit:"null"`
	// @db uuid not null
	// 所属的群组
	OrgRID string `protobuf:"bytes,3,opt,name=OrgRID,proto3" json:"OrgRID,omitempty" ykit:"notnull"`
	// @db timestamp not null
	// 动作时间,utc
	ActionTime string `protobuf:"bytes,4,opt,name=ActionTime,proto3" json:"ActionTime,omitempty" ykit:"notnull"`
	// @db int
	// 界桩硬件ID
	MarkerHWID int32 `protobuf:"zigzag32,5,opt,name=MarkerHWID,proto3" json:"MarkerHWID,omitempty" ykit:"null"`
	// @db int
	// action 0xd1, 0xd2
	// 4g界桩信息上报21, ReportInfo=bysproto.BInfoReporting
	// 4g界桩报警22, ReportInfo=bysproto.BAlarmReporting
	ActionCode int32 `protobuf:"zigzag32,6,opt,name=ActionCode,proto3" json:"ActionCode,omitempty" ykit:"null"`
	// @db int
	Status int32 `protobuf:"zigzag32,7,opt,name=Status,proto3" json:"Status,omitempty" ykit:"null"`
	// @db text
	// 配置参数信息
	ParamInfo string `protobuf:"bytes,8,opt,name=ParamInfo,proto3" json:"ParamInfo,omitempty" ykit:"null"`
	// @db double precision
	// gps lon
	Lon float64 `protobuf:"fixed64,9,opt,name=Lon,proto3" json:"Lon,omitempty" ykit:"null"`
	// @db double precision
	// gps lat
	Lat float64 `protobuf:"fixed64,10,opt,name=Lat,proto3" json:"Lat,omitempty" ykit:"null"`
	// @db timestamp
	// cmd time
	CmdTime string `protobuf:"bytes,11,opt,name=CmdTime,proto3" json:"CmdTime,omitempty" ykit:"null"`
	// @db int
	// 实际接收的控制器ID（中继/基站）
	RecvControllerID int32 `protobuf:"zigzag32,12,opt,name=RecvControllerID,proto3" json:"RecvControllerID,omitempty" ykit:"null"`
	// @db jsonb not null default  '{}'::jsonb
	ReportInfo string `protobuf:"bytes,13,opt,name=ReportInfo,proto3" json:"ReportInfo,omitempty" ykit:"notnull"`
}

func (m *DbMarkerHistory) Reset()         { *m = DbMarkerHistory{} }
func (m *DbMarkerHistory) String() string { return proto.CompactTextString(m) }
func (*DbMarkerHistory) ProtoMessage()    {}
func (*DbMarkerHistory) Descriptor() ([]byte, []int) {
	return fileDescriptor_ee9b802a25f08f08, []int{4}
}
func (m *DbMarkerHistory) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbMarkerHistory) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbMarkerHistory.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbMarkerHistory) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbMarkerHistory.Merge(m, src)
}
func (m *DbMarkerHistory) XXX_Size() int {
	return m.Size()
}
func (m *DbMarkerHistory) XXX_DiscardUnknown() {
	xxx_messageInfo_DbMarkerHistory.DiscardUnknown(m)
}

var xxx_messageInfo_DbMarkerHistory proto.InternalMessageInfo

func (m *DbMarkerHistory) GetControllerID() int32 {
	if m != nil {
		return m.ControllerID
	}
	return 0
}

func (m *DbMarkerHistory) GetControllerChannel() int32 {
	if m != nil {
		return m.ControllerChannel
	}
	return 0
}

func (m *DbMarkerHistory) GetOrgRID() string {
	if m != nil {
		return m.OrgRID
	}
	return ""
}

func (m *DbMarkerHistory) GetActionTime() string {
	if m != nil {
		return m.ActionTime
	}
	return ""
}

func (m *DbMarkerHistory) GetMarkerHWID() int32 {
	if m != nil {
		return m.MarkerHWID
	}
	return 0
}

func (m *DbMarkerHistory) GetActionCode() int32 {
	if m != nil {
		return m.ActionCode
	}
	return 0
}

func (m *DbMarkerHistory) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *DbMarkerHistory) GetParamInfo() string {
	if m != nil {
		return m.ParamInfo
	}
	return ""
}

func (m *DbMarkerHistory) GetLon() float64 {
	if m != nil {
		return m.Lon
	}
	return 0
}

func (m *DbMarkerHistory) GetLat() float64 {
	if m != nil {
		return m.Lat
	}
	return 0
}

func (m *DbMarkerHistory) GetCmdTime() string {
	if m != nil {
		return m.CmdTime
	}
	return ""
}

func (m *DbMarkerHistory) GetRecvControllerID() int32 {
	if m != nil {
		return m.RecvControllerID
	}
	return 0
}

func (m *DbMarkerHistory) GetReportInfo() string {
	if m != nil {
		return m.ReportInfo
	}
	return ""
}

// 4g界桩NFC巡查打卡历史表，以月为单位分表
// @rpc pcrud
// @dbend PARTITION BY RANGE (ActionTime)
// @dbpost CREATE INDEX IF NOT EXISTS idxDbMarkerPatrolHistoryHWID on DbMarkerPatrolHistory USING hash(MarkerHWID);
// @dbpost CREATE INDEX IF NOT EXISTS idxDbMarkerPatrolHistoryOrgRID on DbMarkerPatrolHistory USING hash(OrgRID);
type DbMarkerPatrolHistory struct {
	// @db text
	// NFC卡片ID，hex string
	NFCID string `protobuf:"bytes,1,opt,name=NFCID,proto3" json:"NFCID,omitempty" ykit:"null"`
	// @db timestamp not null
	// NFC打卡时间,utc
	ActionTime string `protobuf:"bytes,2,opt,name=ActionTime,proto3" json:"ActionTime,omitempty" ykit:"notnull"`
	// @db int
	// 界桩硬件ID
	MarkerHWID int32 `protobuf:"zigzag32,3,opt,name=MarkerHWID,proto3" json:"MarkerHWID,omitempty" ykit:"null"`
	// @db uuid not null
	// 打卡用户
	UserID string `protobuf:"bytes,4,opt,name=UserID,proto3" json:"UserID,omitempty" ykit:"notnull"`
	// @db uuid not null
	// 所属的群组
	OrgRID string `protobuf:"bytes,5,opt,name=OrgRID,proto3" json:"OrgRID,omitempty" ykit:"notnull"`
}

func (m *DbMarkerPatrolHistory) Reset()         { *m = DbMarkerPatrolHistory{} }
func (m *DbMarkerPatrolHistory) String() string { return proto.CompactTextString(m) }
func (*DbMarkerPatrolHistory) ProtoMessage()    {}
func (*DbMarkerPatrolHistory) Descriptor() ([]byte, []int) {
	return fileDescriptor_ee9b802a25f08f08, []int{5}
}
func (m *DbMarkerPatrolHistory) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbMarkerPatrolHistory) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbMarkerPatrolHistory.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbMarkerPatrolHistory) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbMarkerPatrolHistory.Merge(m, src)
}
func (m *DbMarkerPatrolHistory) XXX_Size() int {
	return m.Size()
}
func (m *DbMarkerPatrolHistory) XXX_DiscardUnknown() {
	xxx_messageInfo_DbMarkerPatrolHistory.DiscardUnknown(m)
}

var xxx_messageInfo_DbMarkerPatrolHistory proto.InternalMessageInfo

func (m *DbMarkerPatrolHistory) GetNFCID() string {
	if m != nil {
		return m.NFCID
	}
	return ""
}

func (m *DbMarkerPatrolHistory) GetActionTime() string {
	if m != nil {
		return m.ActionTime
	}
	return ""
}

func (m *DbMarkerPatrolHistory) GetMarkerHWID() int32 {
	if m != nil {
		return m.MarkerHWID
	}
	return 0
}

func (m *DbMarkerPatrolHistory) GetUserID() string {
	if m != nil {
		return m.UserID
	}
	return ""
}

func (m *DbMarkerPatrolHistory) GetOrgRID() string {
	if m != nil {
		return m.OrgRID
	}
	return ""
}

// 巡查线路表
// @rpc pcrud
// @dbpost CREATE INDEX IF NOT EXISTS idxDbNFCPatrolLineOrgRID on DbNFCPatrolLine USING hash(OrgRID);
// @dbpost CREATE INDEX IF NOT EXISTS idxDbNFCPatrolLineName on DbNFCPatrolLine USING hash(Name);
type DbNFCPatrolLine struct {
	// @db uuid primary key
	// 行ID
	RID string `protobuf:"bytes,1,opt,name=RID,proto3" json:"RID,omitempty" ykit:"prk"`
	// @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
	// 线路的归属组
	OrgRID string `protobuf:"bytes,2,opt,name=OrgRID,proto3" json:"OrgRID,omitempty" ykit:"notnull"`
	// @db varchar(16) not null unique
	// 线路名称
	Name string `protobuf:"bytes,3,opt,name=Name,proto3" json:"Name,omitempty" ykit:"unique,notnull"`
	// @db text
	Note string `protobuf:"bytes,4,opt,name=Note,proto3" json:"Note,omitempty" ykit:"null"`
	// @db timestamp not null default now_utc()
	// 数据最后修改时间
	UpdatedAt string `protobuf:"bytes,5,opt,name=UpdatedAt,proto3" json:"UpdatedAt,omitempty" ykit:"notnull"`
	// @db text
	// 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
	UpdatedDC string `protobuf:"bytes,6,opt,name=UpdatedDC,proto3" json:"UpdatedDC,omitempty" ykit:"null"`
}

func (m *DbNFCPatrolLine) Reset()         { *m = DbNFCPatrolLine{} }
func (m *DbNFCPatrolLine) String() string { return proto.CompactTextString(m) }
func (*DbNFCPatrolLine) ProtoMessage()    {}
func (*DbNFCPatrolLine) Descriptor() ([]byte, []int) {
	return fileDescriptor_ee9b802a25f08f08, []int{6}
}
func (m *DbNFCPatrolLine) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbNFCPatrolLine) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbNFCPatrolLine.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbNFCPatrolLine) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbNFCPatrolLine.Merge(m, src)
}
func (m *DbNFCPatrolLine) XXX_Size() int {
	return m.Size()
}
func (m *DbNFCPatrolLine) XXX_DiscardUnknown() {
	xxx_messageInfo_DbNFCPatrolLine.DiscardUnknown(m)
}

var xxx_messageInfo_DbNFCPatrolLine proto.InternalMessageInfo

func (m *DbNFCPatrolLine) GetRID() string {
	if m != nil {
		return m.RID
	}
	return ""
}

func (m *DbNFCPatrolLine) GetOrgRID() string {
	if m != nil {
		return m.OrgRID
	}
	return ""
}

func (m *DbNFCPatrolLine) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *DbNFCPatrolLine) GetNote() string {
	if m != nil {
		return m.Note
	}
	return ""
}

func (m *DbNFCPatrolLine) GetUpdatedAt() string {
	if m != nil {
		return m.UpdatedAt
	}
	return ""
}

func (m *DbNFCPatrolLine) GetUpdatedDC() string {
	if m != nil {
		return m.UpdatedDC
	}
	return ""
}

// 巡查线路详细表，分开主要是方便使用数据库外键约束
// @rpc pcrud
// @dbpost CREATE INDEX IF NOT EXISTS idxDbNFCPatrolLineDetailOrgRID on DbNFCPatrolLineDetail USING hash(OrgRID);
// @dbpost CREATE INDEX IF NOT EXISTS idxDDbNFCPatrolLineDetailLineRID on DbNFCPatrolLineDetail USING hash(LineRID);
// @dbpost CREATE INDEX IF NOT EXISTS idxDbNFCPatrolLineDetailMarkerRID on DbNFCPatrolLineDetail USING hash(MarkerRID);
type DbNFCPatrolLineDetail struct {
	// @db uuid primary key
	// 行ID
	RID string `protobuf:"bytes,1,opt,name=RID,proto3" json:"RID,omitempty" ykit:"prk"`
	// @db uuid not null REFERENCES DbNFCPatrolLine(RID) ON DELETE CASCADE
	// 归属线路
	LineRID string `protobuf:"bytes,2,opt,name=LineRID,proto3" json:"LineRID,omitempty" ykit:"notnull"`
	// @db uuid not null REFERENCES DbBysMarker(RID) ON DELETE CASCADE
	// 线路下的巡查点
	MarkerRID string `protobuf:"bytes,3,opt,name=MarkerRID,proto3" json:"MarkerRID,omitempty" ykit:"notnull"`
	// @db timestamp not null default now_utc()
	// 数据最后修改时间
	UpdatedAt string `protobuf:"bytes,4,opt,name=UpdatedAt,proto3" json:"UpdatedAt,omitempty" ykit:"notnull"`
	// @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
	// 归属组，与路线的上线一致
	OrgRID string `protobuf:"bytes,5,opt,name=OrgRID,proto3" json:"OrgRID,omitempty" ykit:"notnull"`
}

func (m *DbNFCPatrolLineDetail) Reset()         { *m = DbNFCPatrolLineDetail{} }
func (m *DbNFCPatrolLineDetail) String() string { return proto.CompactTextString(m) }
func (*DbNFCPatrolLineDetail) ProtoMessage()    {}
func (*DbNFCPatrolLineDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_ee9b802a25f08f08, []int{7}
}
func (m *DbNFCPatrolLineDetail) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbNFCPatrolLineDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbNFCPatrolLineDetail.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbNFCPatrolLineDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbNFCPatrolLineDetail.Merge(m, src)
}
func (m *DbNFCPatrolLineDetail) XXX_Size() int {
	return m.Size()
}
func (m *DbNFCPatrolLineDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_DbNFCPatrolLineDetail.DiscardUnknown(m)
}

var xxx_messageInfo_DbNFCPatrolLineDetail proto.InternalMessageInfo

func (m *DbNFCPatrolLineDetail) GetRID() string {
	if m != nil {
		return m.RID
	}
	return ""
}

func (m *DbNFCPatrolLineDetail) GetLineRID() string {
	if m != nil {
		return m.LineRID
	}
	return ""
}

func (m *DbNFCPatrolLineDetail) GetMarkerRID() string {
	if m != nil {
		return m.MarkerRID
	}
	return ""
}

func (m *DbNFCPatrolLineDetail) GetUpdatedAt() string {
	if m != nil {
		return m.UpdatedAt
	}
	return ""
}

func (m *DbNFCPatrolLineDetail) GetOrgRID() string {
	if m != nil {
		return m.OrgRID
	}
	return ""
}

// 界桩NFC巡查规则
// @rpc pcrud
// @dbpost CREATE INDEX IF NOT EXISTS idxDbNFCPatrolLineRulesOrgRID on DbNFCPatrolLineRules USING hash(OrgRID);
// @dbpost CREATE INDEX IF NOT EXISTS idxDbNFCPatrolLineRulesName on DbNFCPatrolLineRules USING hash(Name);
type DbNFCPatrolLineRules struct {
	// @db uuid primary key
	// 行ID
	RID string `protobuf:"bytes,1,opt,name=RID,proto3" json:"RID,omitempty" ykit:"prk"`
	// @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
	// 规则的归属组
	OrgRID string `protobuf:"bytes,2,opt,name=OrgRID,proto3" json:"OrgRID,omitempty" ykit:"notnull"`
	// @db varchar(16) not null unique
	// 规则名称
	Name string `protobuf:"bytes,3,opt,name=Name,proto3" json:"Name,omitempty" ykit:"unique,notnull"`
	// @db boolean default false
	// 星期一
	Day1 bool `protobuf:"varint,4,opt,name=Day1,proto3" json:"Day1,omitempty"`
	// @db boolean default false
	// 星期二
	Day2 bool `protobuf:"varint,5,opt,name=Day2,proto3" json:"Day2,omitempty"`
	// @db boolean default false
	// 星期三
	Day3 bool `protobuf:"varint,6,opt,name=Day3,proto3" json:"Day3,omitempty"`
	// @db boolean default false
	// 星期四
	Day4 bool `protobuf:"varint,7,opt,name=Day4,proto3" json:"Day4,omitempty"`
	// @db boolean default false
	// 星期五
	Day5 bool `protobuf:"varint,8,opt,name=Day5,proto3" json:"Day5,omitempty"`
	// @db boolean default false
	// 星期六
	Day6 bool `protobuf:"varint,9,opt,name=Day6,proto3" json:"Day6,omitempty"`
	// @db boolean default false
	// 星期日
	Day7 bool `protobuf:"varint,10,opt,name=Day7,proto3" json:"Day7,omitempty"`
	// @db time
	// 巡查开始的时间
	CheckStartTime string `protobuf:"bytes,11,opt,name=CheckStartTime,proto3" json:"CheckStartTime,omitempty" ykit:"null"`
	// @db time
	// 巡查结束的时间
	CheckEndTime string `protobuf:"bytes,12,opt,name=CheckEndTime,proto3" json:"CheckEndTime,omitempty" ykit:"null"`
	// @db int not null default 1
	// 巡查次数
	CheckCount int32 `protobuf:"varint,13,opt,name=CheckCount,proto3" json:"CheckCount,omitempty" ykit:"notnull"`
	// @db int default 0
	// 线路巡查规则生效时间的类型 0:总是生效 1:按有效时间段
	EffectiveType int32 `protobuf:"varint,14,opt,name=EffectiveType,proto3" json:"EffectiveType,omitempty"`
	// @db timestamp
	// 规则开始生效时间
	EffectiveStart string `protobuf:"bytes,15,opt,name=EffectiveStart,proto3" json:"EffectiveStart,omitempty" ykit:"null"`
	// @db timestamp
	// 规则生效结束时间
	EffectiveEnd string `protobuf:"bytes,16,opt,name=EffectiveEnd,proto3" json:"EffectiveEnd,omitempty" ykit:"null"`
	// @db text
	Note string `protobuf:"bytes,17,opt,name=Note,proto3" json:"Note,omitempty" ykit:"null"`
	// @db timestamp not null default now_utc()
	// 数据最后修改时间
	UpdatedAt string `protobuf:"bytes,18,opt,name=UpdatedAt,proto3" json:"UpdatedAt,omitempty" ykit:"notnull"`
	// @db text
	// 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
	UpdatedDC string `protobuf:"bytes,19,opt,name=UpdatedDC,proto3" json:"UpdatedDC,omitempty" ykit:"null"`
}

func (m *DbNFCPatrolLineRules) Reset()         { *m = DbNFCPatrolLineRules{} }
func (m *DbNFCPatrolLineRules) String() string { return proto.CompactTextString(m) }
func (*DbNFCPatrolLineRules) ProtoMessage()    {}
func (*DbNFCPatrolLineRules) Descriptor() ([]byte, []int) {
	return fileDescriptor_ee9b802a25f08f08, []int{8}
}
func (m *DbNFCPatrolLineRules) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbNFCPatrolLineRules) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbNFCPatrolLineRules.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbNFCPatrolLineRules) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbNFCPatrolLineRules.Merge(m, src)
}
func (m *DbNFCPatrolLineRules) XXX_Size() int {
	return m.Size()
}
func (m *DbNFCPatrolLineRules) XXX_DiscardUnknown() {
	xxx_messageInfo_DbNFCPatrolLineRules.DiscardUnknown(m)
}

var xxx_messageInfo_DbNFCPatrolLineRules proto.InternalMessageInfo

func (m *DbNFCPatrolLineRules) GetRID() string {
	if m != nil {
		return m.RID
	}
	return ""
}

func (m *DbNFCPatrolLineRules) GetOrgRID() string {
	if m != nil {
		return m.OrgRID
	}
	return ""
}

func (m *DbNFCPatrolLineRules) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *DbNFCPatrolLineRules) GetDay1() bool {
	if m != nil {
		return m.Day1
	}
	return false
}

func (m *DbNFCPatrolLineRules) GetDay2() bool {
	if m != nil {
		return m.Day2
	}
	return false
}

func (m *DbNFCPatrolLineRules) GetDay3() bool {
	if m != nil {
		return m.Day3
	}
	return false
}

func (m *DbNFCPatrolLineRules) GetDay4() bool {
	if m != nil {
		return m.Day4
	}
	return false
}

func (m *DbNFCPatrolLineRules) GetDay5() bool {
	if m != nil {
		return m.Day5
	}
	return false
}

func (m *DbNFCPatrolLineRules) GetDay6() bool {
	if m != nil {
		return m.Day6
	}
	return false
}

func (m *DbNFCPatrolLineRules) GetDay7() bool {
	if m != nil {
		return m.Day7
	}
	return false
}

func (m *DbNFCPatrolLineRules) GetCheckStartTime() string {
	if m != nil {
		return m.CheckStartTime
	}
	return ""
}

func (m *DbNFCPatrolLineRules) GetCheckEndTime() string {
	if m != nil {
		return m.CheckEndTime
	}
	return ""
}

func (m *DbNFCPatrolLineRules) GetCheckCount() int32 {
	if m != nil {
		return m.CheckCount
	}
	return 0
}

func (m *DbNFCPatrolLineRules) GetEffectiveType() int32 {
	if m != nil {
		return m.EffectiveType
	}
	return 0
}

func (m *DbNFCPatrolLineRules) GetEffectiveStart() string {
	if m != nil {
		return m.EffectiveStart
	}
	return ""
}

func (m *DbNFCPatrolLineRules) GetEffectiveEnd() string {
	if m != nil {
		return m.EffectiveEnd
	}
	return ""
}

func (m *DbNFCPatrolLineRules) GetNote() string {
	if m != nil {
		return m.Note
	}
	return ""
}

func (m *DbNFCPatrolLineRules) GetUpdatedAt() string {
	if m != nil {
		return m.UpdatedAt
	}
	return ""
}

func (m *DbNFCPatrolLineRules) GetUpdatedDC() string {
	if m != nil {
		return m.UpdatedDC
	}
	return ""
}

// 巡查线路和规则的关系表，分开主要是方便使用数据库外键约束
// @rpc pcrud
// @dbpost CREATE INDEX IF NOT EXISTS idxDbNFCPatrolLineAndRulesOrgRID on DbNFCPatrolLineAndRules USING hash(OrgRID);
// @dbpost CREATE INDEX IF NOT EXISTS idxDbNFCPatrolLineAndRulesLineRID on DbNFCPatrolLineAndRules USING hash(LineRID);
// @dbpost CREATE INDEX IF NOT EXISTS idxDbNFCPatrolLineAndRulesRuleRID on DbNFCPatrolLineAndRules USING hash(RuleRID);
type DbNFCPatrolLineAndRules struct {
	// @db uuid primary key
	// 行ID
	RID string `protobuf:"bytes,1,opt,name=RID,proto3" json:"RID,omitempty" ykit:"prk"`
	// @db uuid not null REFERENCES DbNFCPatrolLine(RID) ON DELETE CASCADE
	// 归属线路
	LineRID string `protobuf:"bytes,2,opt,name=LineRID,proto3" json:"LineRID,omitempty" ykit:"notnull"`
	// @db uuid not null REFERENCES DbNFCPatrolLineRules(RID) ON DELETE CASCADE
	// 线路规则
	RuleRID string `protobuf:"bytes,3,opt,name=RuleRID,proto3" json:"RuleRID,omitempty" ykit:"notnull"`
	// @db timestamp not null default now_utc()
	// 数据最后修改时间
	UpdatedAt string `protobuf:"bytes,4,opt,name=UpdatedAt,proto3" json:"UpdatedAt,omitempty" ykit:"notnull"`
	// @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
	// 归属组，与路线的上线一致
	OrgRID string `protobuf:"bytes,5,opt,name=OrgRID,proto3" json:"OrgRID,omitempty" ykit:"notnull"`
}

func (m *DbNFCPatrolLineAndRules) Reset()         { *m = DbNFCPatrolLineAndRules{} }
func (m *DbNFCPatrolLineAndRules) String() string { return proto.CompactTextString(m) }
func (*DbNFCPatrolLineAndRules) ProtoMessage()    {}
func (*DbNFCPatrolLineAndRules) Descriptor() ([]byte, []int) {
	return fileDescriptor_ee9b802a25f08f08, []int{9}
}
func (m *DbNFCPatrolLineAndRules) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbNFCPatrolLineAndRules) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbNFCPatrolLineAndRules.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbNFCPatrolLineAndRules) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbNFCPatrolLineAndRules.Merge(m, src)
}
func (m *DbNFCPatrolLineAndRules) XXX_Size() int {
	return m.Size()
}
func (m *DbNFCPatrolLineAndRules) XXX_DiscardUnknown() {
	xxx_messageInfo_DbNFCPatrolLineAndRules.DiscardUnknown(m)
}

var xxx_messageInfo_DbNFCPatrolLineAndRules proto.InternalMessageInfo

func (m *DbNFCPatrolLineAndRules) GetRID() string {
	if m != nil {
		return m.RID
	}
	return ""
}

func (m *DbNFCPatrolLineAndRules) GetLineRID() string {
	if m != nil {
		return m.LineRID
	}
	return ""
}

func (m *DbNFCPatrolLineAndRules) GetRuleRID() string {
	if m != nil {
		return m.RuleRID
	}
	return ""
}

func (m *DbNFCPatrolLineAndRules) GetUpdatedAt() string {
	if m != nil {
		return m.UpdatedAt
	}
	return ""
}

func (m *DbNFCPatrolLineAndRules) GetOrgRID() string {
	if m != nil {
		return m.OrgRID
	}
	return ""
}

// 合方圆摄像机上传图片时，附带的json结构体
type CamImageData struct {
	// 设备IMEI
	DevId string `protobuf:"bytes,1,opt,name=dev_id,json=devId,proto3" json:"dev_id,omitempty"`
	// 4G模块自动添加
	Ccid string `protobuf:"bytes,2,opt,name=ccid,proto3" json:"ccid,omitempty"`
	// 固件版本
	FirmwareVersion string `protobuf:"bytes,3,opt,name=firmware_version,json=firmwareVersion,proto3" json:"firmware_version,omitempty"`
	// 抓拍时间戳，Unix秒时间戳
	Timestamp int64 `protobuf:"fixed64,4,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	// 电池电压单位mv
	Battery float32 `protobuf:"fixed32,5,opt,name=battery,proto3" json:"battery,omitempty"`
	// 4G信号强度
	Signal string `protobuf:"bytes,6,opt,name=signal,proto3" json:"signal,omitempty"`
	// 环境温度
	TempEnv int32 `protobuf:"zigzag32,7,opt,name=temp_env,json=tempEnv,proto3" json:"temp_env,omitempty"`
	// CPU温度
	TempCpu int32 `protobuf:"zigzag32,8,opt,name=temp_cpu,json=tempCpu,proto3" json:"temp_cpu,omitempty"`
	// 工作类型
	// 0 – 手动
	// 1 – 唤醒拍照
	// 2 – 定时拍照
	// 3 – 唤醒录像
	// 4 – 定时录像
	Type int32 `protobuf:"zigzag32,9,opt,name=type,proto3" json:"type,omitempty"`
	// 以下为可选字段
	// 放大系数
	ZoomRate float32 `protobuf:"fixed32,10,opt,name=zoom_rate,json=zoomRate,proto3" json:"zoom_rate,omitempty"`
	// 充电电流，合方圆太阳能充电模块支持
	Icharge float32 `protobuf:"fixed32,11,opt,name=icharge,proto3" json:"icharge,omitempty"`
	// 负载电流，合方圆太阳能充电模块支持
	Iload float32 `protobuf:"fixed32,12,opt,name=iload,proto3" json:"iload,omitempty"`
	// 充电电压，合方圆太阳能充电模块支持
	Vcharge float32 `protobuf:"fixed32,13,opt,name=vcharge,proto3" json:"vcharge,omitempty"`
	// 上传图片时，界桩自定义的json数据
	// "{\"hwid\": 14, \"type\": 2}"
	// hwid: 界桩硬件ID
	// type: 1、红外感应抓拍，2、报警抓拍，3、定时抓拍，4、手动抓拍
	CustData string `protobuf:"bytes,14,opt,name=cust_data,json=custData,proto3" json:"cust_data,omitempty"`
}

func (m *CamImageData) Reset()         { *m = CamImageData{} }
func (m *CamImageData) String() string { return proto.CompactTextString(m) }
func (*CamImageData) ProtoMessage()    {}
func (*CamImageData) Descriptor() ([]byte, []int) {
	return fileDescriptor_ee9b802a25f08f08, []int{10}
}
func (m *CamImageData) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CamImageData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CamImageData.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *CamImageData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CamImageData.Merge(m, src)
}
func (m *CamImageData) XXX_Size() int {
	return m.Size()
}
func (m *CamImageData) XXX_DiscardUnknown() {
	xxx_messageInfo_CamImageData.DiscardUnknown(m)
}

var xxx_messageInfo_CamImageData proto.InternalMessageInfo

func (m *CamImageData) GetDevId() string {
	if m != nil {
		return m.DevId
	}
	return ""
}

func (m *CamImageData) GetCcid() string {
	if m != nil {
		return m.Ccid
	}
	return ""
}

func (m *CamImageData) GetFirmwareVersion() string {
	if m != nil {
		return m.FirmwareVersion
	}
	return ""
}

func (m *CamImageData) GetTimestamp() int64 {
	if m != nil {
		return m.Timestamp
	}
	return 0
}

func (m *CamImageData) GetBattery() float32 {
	if m != nil {
		return m.Battery
	}
	return 0
}

func (m *CamImageData) GetSignal() string {
	if m != nil {
		return m.Signal
	}
	return ""
}

func (m *CamImageData) GetTempEnv() int32 {
	if m != nil {
		return m.TempEnv
	}
	return 0
}

func (m *CamImageData) GetTempCpu() int32 {
	if m != nil {
		return m.TempCpu
	}
	return 0
}

func (m *CamImageData) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *CamImageData) GetZoomRate() float32 {
	if m != nil {
		return m.ZoomRate
	}
	return 0
}

func (m *CamImageData) GetIcharge() float32 {
	if m != nil {
		return m.Icharge
	}
	return 0
}

func (m *CamImageData) GetIload() float32 {
	if m != nil {
		return m.Iload
	}
	return 0
}

func (m *CamImageData) GetVcharge() float32 {
	if m != nil {
		return m.Vcharge
	}
	return 0
}

func (m *CamImageData) GetCustData() string {
	if m != nil {
		return m.CustData
	}
	return ""
}

// 4g界桩上传的图片历史表，以月为单位分表
// @rpc pcrud
// @dbend PARTITION BY RANGE (UploadTime)
// @dbpost CREATE INDEX IF NOT EXISTS idxDbMarkerUploadImageHistoryHWID on DbMarkerUploadImageHistory USING hash(MarkerHWID);
// @dbpost CREATE INDEX IF NOT EXISTS idxDbMarkerUploadImageHistoryOrgRID on DbMarkerUploadImageHistory USING hash(OrgRID);
type DbMarkerUploadImageHistory struct {
	// @db uuid not null
	// 所属的群组
	OrgRID string `protobuf:"bytes,1,opt,name=OrgRID,proto3" json:"OrgRID,omitempty" ykit:"notnull"`
	// @db int
	// 界桩硬件ID
	MarkerHWID int32 `protobuf:"zigzag32,2,opt,name=MarkerHWID,proto3" json:"MarkerHWID,omitempty" ykit:"null"`
	// @db timestamp not null
	// 抓拍时间,utc
	CaptureTime string `protobuf:"bytes,3,opt,name=CaptureTime,proto3" json:"CaptureTime,omitempty" ykit:"notnull"`
	// @db int
	// 抓拍的动作类型，与CamImageData.type一致
	// 0 – 手动
	// 1 – 唤醒拍照
	// 2 – 定时拍照
	// 3 – 唤醒录像
	// 4 – 定时录像
	CaptureType int32 `protobuf:"zigzag32,4,opt,name=CaptureType,proto3" json:"CaptureType,omitempty" ykit:"null"`
	// @db timestamp not null
	// 服务器接收上传时间,utc
	UploadTime string `protobuf:"bytes,5,opt,name=UploadTime,proto3" json:"UploadTime,omitempty" ykit:"notnull"`
	// @db text
	// 上传的图片保存路径，f512673f-0f6a-4b54-95bf-d377b78fba1a.jpg
	FileName string `protobuf:"bytes,6,opt,name=FileName,proto3" json:"FileName,omitempty" ykit:"null"`
	// @db jsonb not null default  '{}'::jsonb
	// 界桩上传图片时附带的json数据，CamImageData json字符串
	FormData string `protobuf:"bytes,7,opt,name=FormData,proto3" json:"FormData,omitempty" ykit:"notnull"`
}

func (m *DbMarkerUploadImageHistory) Reset()         { *m = DbMarkerUploadImageHistory{} }
func (m *DbMarkerUploadImageHistory) String() string { return proto.CompactTextString(m) }
func (*DbMarkerUploadImageHistory) ProtoMessage()    {}
func (*DbMarkerUploadImageHistory) Descriptor() ([]byte, []int) {
	return fileDescriptor_ee9b802a25f08f08, []int{11}
}
func (m *DbMarkerUploadImageHistory) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DbMarkerUploadImageHistory) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DbMarkerUploadImageHistory.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DbMarkerUploadImageHistory) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbMarkerUploadImageHistory.Merge(m, src)
}
func (m *DbMarkerUploadImageHistory) XXX_Size() int {
	return m.Size()
}
func (m *DbMarkerUploadImageHistory) XXX_DiscardUnknown() {
	xxx_messageInfo_DbMarkerUploadImageHistory.DiscardUnknown(m)
}

var xxx_messageInfo_DbMarkerUploadImageHistory proto.InternalMessageInfo

func (m *DbMarkerUploadImageHistory) GetOrgRID() string {
	if m != nil {
		return m.OrgRID
	}
	return ""
}

func (m *DbMarkerUploadImageHistory) GetMarkerHWID() int32 {
	if m != nil {
		return m.MarkerHWID
	}
	return 0
}

func (m *DbMarkerUploadImageHistory) GetCaptureTime() string {
	if m != nil {
		return m.CaptureTime
	}
	return ""
}

func (m *DbMarkerUploadImageHistory) GetCaptureType() int32 {
	if m != nil {
		return m.CaptureType
	}
	return 0
}

func (m *DbMarkerUploadImageHistory) GetUploadTime() string {
	if m != nil {
		return m.UploadTime
	}
	return ""
}

func (m *DbMarkerUploadImageHistory) GetFileName() string {
	if m != nil {
		return m.FileName
	}
	return ""
}

func (m *DbMarkerUploadImageHistory) GetFormData() string {
	if m != nil {
		return m.FormData
	}
	return ""
}

func init() {
	proto.RegisterType((*DbController)(nil), "bysdb.DbController")
	proto.RegisterType((*DbBysMarker)(nil), "bysdb.DbBysMarker")
	proto.RegisterType((*DbControllerOnlineHistory)(nil), "bysdb.DbControllerOnlineHistory")
	proto.RegisterType((*DbMediaInfo)(nil), "bysdb.DbMediaInfo")
	proto.RegisterType((*DbMarkerHistory)(nil), "bysdb.DbMarkerHistory")
	proto.RegisterType((*DbMarkerPatrolHistory)(nil), "bysdb.DbMarkerPatrolHistory")
	proto.RegisterType((*DbNFCPatrolLine)(nil), "bysdb.DbNFCPatrolLine")
	proto.RegisterType((*DbNFCPatrolLineDetail)(nil), "bysdb.DbNFCPatrolLineDetail")
	proto.RegisterType((*DbNFCPatrolLineRules)(nil), "bysdb.DbNFCPatrolLineRules")
	proto.RegisterType((*DbNFCPatrolLineAndRules)(nil), "bysdb.DbNFCPatrolLineAndRules")
	proto.RegisterType((*CamImageData)(nil), "bysdb.CamImageData")
	proto.RegisterType((*DbMarkerUploadImageHistory)(nil), "bysdb.DbMarkerUploadImageHistory")
}

func init() { proto.RegisterFile("bysdb.proto", fileDescriptor_ee9b802a25f08f08) }

var fileDescriptor_ee9b802a25f08f08 = []byte{
	// 1600 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xa4, 0x58, 0xcd, 0x6e, 0x1b, 0x47,
	0x12, 0xf6, 0x50, 0xa4, 0x48, 0xb6, 0x24, 0x4b, 0x1c, 0x4b, 0xf2, 0x58, 0xf6, 0x72, 0x05, 0xae,
	0xb1, 0xd0, 0x2e, 0x0c, 0x29, 0xf1, 0x2f, 0x7c, 0xc8, 0xc1, 0xe6, 0xd0, 0x30, 0x01, 0x89, 0x56,
	0x46, 0x76, 0x0c, 0xe4, 0x22, 0x34, 0x67, 0x5a, 0xd4, 0x40, 0xf3, 0x87, 0x99, 0x26, 0x15, 0xfa,
	0x98, 0x07, 0x08, 0x72, 0x09, 0x72, 0x48, 0x82, 0x1c, 0x72, 0xca, 0x13, 0xe4, 0x15, 0x72, 0xf4,
	0x31, 0xc7, 0xc0, 0x7e, 0x81, 0x3c, 0x42, 0xd0, 0x55, 0x3d, 0xff, 0xa4, 0x6c, 0xc7, 0xb7, 0xae,
	0xaf, 0xaa, 0xa7, 0xab, 0xab, 0xbf, 0xfa, 0x21, 0xc9, 0xd2, 0x70, 0x1a, 0x59, 0xc3, 0xdd, 0x20,
	0xf4, 0xb9, 0xaf, 0xd6, 0x40, 0xe8, 0xfc, 0x58, 0x25, 0xcb, 0xfa, 0xb0, 0xeb, 0x7b, 0x3c, 0xf4,
	0x1d, 0x87, 0x85, 0xea, 0x1a, 0x59, 0x30, 0xfa, 0xba, 0xa6, 0x6c, 0x2b, 0x3b, 0x4d, 0x43, 0x2c,
	0xd5, 0x4d, 0xb2, 0xf8, 0x2c, 0x1c, 0x09, 0xb0, 0x02, 0xa0, 0x94, 0xd4, 0x0e, 0x59, 0x4e, 0xf7,
	0x0d, 0x7c, 0x6d, 0x01, 0xb4, 0x39, 0x4c, 0xbd, 0x4b, 0x36, 0x52, 0x59, 0x67, 0x91, 0x19, 0xda,
	0x01, 0xb7, 0x7d, 0x4f, 0xab, 0x82, 0xf1, 0x6c, 0xa5, 0xfa, 0x5f, 0x72, 0x39, 0x55, 0x3c, 0x9f,
	0x06, 0x4c, 0xab, 0x6d, 0x2b, 0x3b, 0x35, 0xa3, 0x80, 0xaa, 0x37, 0x48, 0xf3, 0x90, 0x86, 0xcc,
	0xe3, 0xc2, 0xb9, 0x45, 0xf8, 0x62, 0x0a, 0x88, 0x9b, 0xec, 0xfb, 0x9e, 0x56, 0xdf, 0x56, 0x76,
	0x14, 0x43, 0x2c, 0x01, 0xa1, 0x5c, 0x6b, 0x48, 0x84, 0x72, 0x55, 0x23, 0xf5, 0x23, 0xc6, 0xb9,
	0xed, 0x8d, 0xb4, 0x26, 0xec, 0x8f, 0xc5, 0xbc, 0x0f, 0x4f, 0x5f, 0xf6, 0x75, 0x8d, 0x6c, 0x2b,
	0x3b, 0x2d, 0xa3, 0x80, 0x42, 0x14, 0x4e, 0xa9, 0xe7, 0x31, 0xa7, 0xeb, 0x8f, 0x3d, 0xae, 0x2d,
	0x81, 0x55, 0x0e, 0x13, 0x36, 0x07, 0x34, 0x38, 0x3a, 0xf5, 0xcf, 0xf7, 0xd9, 0x84, 0x39, 0xda,
	0x32, 0xda, 0x64, 0x31, 0x71, 0x97, 0x17, 0x81, 0x45, 0x39, 0xb3, 0x1e, 0x71, 0xed, 0x32, 0xde,
	0x25, 0x01, 0x32, 0x5a, 0xbd, 0xab, 0xad, 0xe6, 0xb4, 0x7a, 0x57, 0xdd, 0x25, 0xaa, 0xce, 0x4e,
	0xe8, 0xd8, 0xe1, 0x03, 0xc6, 0xcf, 0xfd, 0xf0, 0x0c, 0x62, 0xb6, 0x06, 0xa7, 0xcc, 0xd0, 0xa8,
	0x3b, 0x64, 0x15, 0xc3, 0x24, 0xbd, 0x1c, 0xf8, 0x5a, 0x0b, 0x8c, 0x8b, 0x70, 0xe7, 0xb7, 0x06,
	0x59, 0xd2, 0x87, 0x8f, 0xa7, 0xd1, 0x01, 0x0d, 0xcf, 0x3e, 0x88, 0x1d, 0x5b, 0xa4, 0x81, 0x7b,
	0x12, 0x66, 0x24, 0xb2, 0x7a, 0x8b, 0xb4, 0x70, 0x5d, 0x66, 0x44, 0x59, 0xa1, 0xde, 0x24, 0x2b,
	0x69, 0xcc, 0xc5, 0x41, 0x35, 0xb0, 0xcc, 0x83, 0xe2, 0x9b, 0x29, 0x20, 0x2f, 0x00, 0x9c, 0x68,
	0x19, 0x65, 0xc5, 0x7b, 0x71, 0xa3, 0x4d, 0x08, 0x3a, 0x03, 0xaf, 0xdf, 0x84, 0x4f, 0x65, 0x90,
	0x2c, 0x77, 0x48, 0x9e, 0x3b, 0xdb, 0x64, 0x09, 0xed, 0x0e, 0x7c, 0x8b, 0x39, 0x92, 0x12, 0x59,
	0xe8, 0xbd, 0x18, 0x71, 0x93, 0xac, 0xe0, 0x96, 0xcf, 0xc7, 0x6c, 0xcc, 0x06, 0xbe, 0xb6, 0x02,
	0x46, 0x79, 0xf0, 0xa3, 0x78, 0xb3, 0x43, 0x56, 0xf1, 0x63, 0x87, 0x34, 0xa4, 0xee, 0x73, 0xdb,
	0x45, 0xd2, 0x34, 0x8d, 0x22, 0x9c, 0x79, 0x31, 0x3a, 0xed, 0x7b, 0x9c, 0x85, 0x13, 0xea, 0x48,
	0xce, 0x94, 0x15, 0xea, 0x27, 0xe4, 0x4a, 0xc6, 0xc9, 0xc4, 0x5e, 0x05, 0xfb, 0x59, 0x2a, 0xf5,
	0x3e, 0xd9, 0x44, 0xb8, 0xe7, 0xb2, 0x70, 0xc4, 0x3c, 0x9e, 0x6c, 0xba, 0x02, 0x9b, 0xe6, 0x68,
	0xd3, 0x18, 0xc5, 0x2f, 0xbe, 0x9e, 0x8d, 0x51, 0xfc, 0xda, 0xb7, 0xc9, 0x3a, 0x02, 0x2f, 0xe9,
	0x19, 0x1b, 0x07, 0x8f, 0x69, 0xc4, 0xe0, 0xb2, 0x1b, 0x70, 0xd9, 0x99, 0x3a, 0x91, 0xff, 0xf2,
	0x62, 0x76, 0x44, 0x87, 0x0e, 0xb3, 0xb4, 0x4d, 0xcc, 0xff, 0x3c, 0xaa, 0xfe, 0x9f, 0xac, 0x3d,
	0xa5, 0x51, 0xdf, 0x8b, 0x38, 0x75, 0x1c, 0x9d, 0x4d, 0x6c, 0x93, 0x69, 0x57, 0xb7, 0x95, 0x9d,
	0x86, 0x51, 0xc2, 0x45, 0xbc, 0x53, 0xec, 0x88, 0xfb, 0x1e, 0xd3, 0xae, 0x81, 0x69, 0x11, 0x4e,
	0xb9, 0x07, 0x99, 0xbc, 0x95, 0xe5, 0x1e, 0x64, 0x70, 0xe2, 0x9d, 0xa4, 0x5c, 0xa4, 0x5d, 0x87,
	0xbb, 0x14, 0x50, 0x75, 0x9d, 0xd4, 0xfa, 0xdd, 0x6e, 0x5f, 0xd7, 0x6e, 0x80, 0x1a, 0x05, 0xb1,
	0xbb, 0xf7, 0x55, 0x60, 0x87, 0x54, 0xe4, 0x97, 0x4e, 0x39, 0xd3, 0xfe, 0x85, 0xbb, 0xf3, 0xa8,
	0xaa, 0x92, 0x6a, 0xff, 0xa0, 0xd7, 0xd7, 0xda, 0xa0, 0x85, 0x35, 0xd4, 0x45, 0xea, 0xb2, 0x90,
	0x26, 0x71, 0xf9, 0xb7, 0xac, 0x8b, 0x39, 0xb4, 0xf3, 0x4b, 0x85, 0x5c, 0xcb, 0x36, 0x96, 0x67,
	0x9e, 0x63, 0x7b, 0xec, 0xa9, 0x1d, 0x71, 0x3f, 0x9c, 0xce, 0xad, 0x1a, 0x6d, 0x42, 0x1e, 0x99,
	0xe2, 0x7c, 0x78, 0x1f, 0xac, 0x1b, 0x19, 0x64, 0x46, 0x55, 0xae, 0xce, 0xac, 0xca, 0xc9, 0x77,
	0xba, 0xbe, 0x85, 0xdd, 0xa3, 0x65, 0x64, 0x10, 0x71, 0x7e, 0x3f, 0xe8, 0x7b, 0x27, 0xbe, 0x6c,
	0x1b, 0x52, 0x12, 0x99, 0x9b, 0x2d, 0xa1, 0x75, 0xcc, 0xdc, 0x6c, 0xed, 0x5c, 0x27, 0xb5, 0x43,
	0xff, 0x9c, 0x85, 0x50, 0x29, 0x2a, 0x06, 0x0a, 0xe2, 0x7b, 0x47, 0x9c, 0xf2, 0x71, 0x04, 0x75,
	0x62, 0xc5, 0x90, 0x92, 0xf0, 0x03, 0xdf, 0x1e, 0x3e, 0x47, 0xa0, 0x8b, 0x65, 0x90, 0xce, 0xaf,
	0x0b, 0xa2, 0xbe, 0x1e, 0x30, 0xcb, 0xa6, 0x70, 0xfe, 0xfb, 0xd7, 0xd7, 0x1b, 0xa4, 0x89, 0x6f,
	0x2d, 0x54, 0x18, 0xa8, 0x14, 0x28, 0xd7, 0xcc, 0xea, 0xac, 0x9a, 0x29, 0xbe, 0x21, 0x8e, 0x4e,
	0x5a, 0x6c, 0xcb, 0x48, 0x01, 0xc8, 0x79, 0x21, 0x3c, 0x0b, 0xed, 0xd1, 0x13, 0xdb, 0x61, 0x03,
	0xea, 0x32, 0x19, 0xae, 0xb2, 0x42, 0x9c, 0xf8, 0x22, 0x70, 0x7c, 0x6a, 0xbd, 0x88, 0xf0, 0xc4,
	0x3a, 0x9e, 0x98, 0x03, 0x45, 0xb6, 0xc0, 0xd6, 0x6c, 0xe1, 0x6f, 0x80, 0x61, 0x09, 0x17, 0xb1,
	0xc3, 0xcd, 0xc0, 0x05, 0x6c, 0xcf, 0x19, 0xe4, 0x82, 0xfa, 0x7b, 0x8b, 0xb4, 0xf6, 0x69, 0xc4,
	0xb1, 0xd0, 0xc5, 0xfe, 0x2c, 0xa1, 0xe7, 0x25, 0x85, 0xe0, 0x54, 0x0a, 0xc2, 0x59, 0xcb, 0x98,
	0x0d, 0x79, 0xb4, 0xf3, 0xc3, 0x02, 0x59, 0xd5, 0x87, 0xb2, 0x01, 0x48, 0x1e, 0xe7, 0x66, 0x20,
	0xf9, 0x70, 0x2d, 0x23, 0x87, 0xcd, 0xee, 0x4c, 0x95, 0x79, 0x9d, 0x29, 0x7d, 0xef, 0x85, 0x0b,
	0x32, 0xa3, 0x5a, 0xca, 0x8c, 0x7c, 0xb7, 0xaa, 0x95, 0xba, 0x55, 0x3e, 0x23, 0x16, 0x67, 0x65,
	0x84, 0x64, 0x30, 0x92, 0x3e, 0x66, 0x30, 0xce, 0x58, 0xd4, 0x85, 0x64, 0x69, 0x24, 0x33, 0x16,
	0x02, 0x71, 0x1f, 0x6d, 0x96, 0xfa, 0x28, 0xc9, 0xcd, 0x58, 0x5d, 0x17, 0x1f, 0x11, 0xdf, 0x20,
	0x16, 0x05, 0x1b, 0x0c, 0x66, 0x4e, 0x72, 0x11, 0xc4, 0x4e, 0x58, 0xc2, 0x85, 0xff, 0x06, 0x0b,
	0xfc, 0x90, 0x83, 0x23, 0x2b, 0x78, 0xff, 0x14, 0xe9, 0xfc, 0xa4, 0x90, 0x8d, 0xf8, 0x75, 0x0e,
	0xa9, 0xd8, 0x17, 0xbf, 0xd1, 0x3a, 0xa9, 0x0d, 0x9e, 0x74, 0x93, 0xac, 0x42, 0xa1, 0x10, 0xcf,
	0xca, 0x3b, 0xe2, 0xb9, 0x50, 0x8a, 0xe7, 0x26, 0x59, 0x14, 0x04, 0x4a, 0x52, 0x4b, 0x4a, 0x99,
	0xf7, 0xab, 0x65, 0xdf, 0xaf, 0xf3, 0xb3, 0x22, 0xd8, 0x33, 0x78, 0xd2, 0x45, 0xe7, 0xf6, 0x6d,
	0x8f, 0x7d, 0x40, 0xb6, 0xab, 0xa4, 0x0a, 0xe9, 0x87, 0x9c, 0x80, 0x35, 0x60, 0x3e, 0x8f, 0xb9,
	0x00, 0xeb, 0xfc, 0x34, 0x50, 0xbb, 0x70, 0x1a, 0x58, 0x2c, 0x4c, 0x03, 0x9d, 0xef, 0x21, 0x82,
	0x39, 0x0f, 0x75, 0xc6, 0xa9, 0xed, 0xcc, 0xf0, 0x53, 0x23, 0x75, 0xa1, 0x4f, 0x1d, 0x8d, 0xc5,
	0x77, 0xd4, 0xa5, 0x9c, 0x7f, 0xd5, 0xa2, 0x7f, 0xf3, 0x62, 0xf7, 0x4d, 0x95, 0xac, 0x17, 0x3c,
	0x33, 0xc6, 0x0e, 0x8b, 0x3e, 0x3e, 0x80, 0x3a, 0x9d, 0x7e, 0x0a, 0x7e, 0x34, 0x0c, 0x58, 0x4b,
	0xec, 0x36, 0x38, 0x80, 0xd8, 0x6d, 0x89, 0xdd, 0x81, 0x88, 0x21, 0x76, 0x47, 0x62, 0x77, 0x21,
	0x59, 0x10, 0xbb, 0x2b, 0xb1, 0x7b, 0x90, 0x25, 0x88, 0xdd, 0x93, 0xd8, 0x7d, 0xc8, 0x10, 0xc4,
	0xee, 0x4b, 0xec, 0x01, 0xe4, 0x08, 0x62, 0x0f, 0xa0, 0xb1, 0x9d, 0x32, 0xf3, 0xec, 0x88, 0xd3,
	0x90, 0x67, 0x72, 0xa5, 0x80, 0xe2, 0xcf, 0x0d, 0x66, 0x9e, 0xf5, 0x3c, 0x2b, 0x53, 0xaa, 0x72,
	0x98, 0xa0, 0x2e, 0xc8, 0xf8, 0x83, 0x64, 0x05, 0x9b, 0x4e, 0x8a, 0x88, 0x52, 0xdd, 0x3b, 0x39,
	0x61, 0x26, 0xb7, 0x27, 0xd8, 0x97, 0x2e, 0x83, 0x49, 0x1e, 0x84, 0x21, 0x21, 0x06, 0xe0, 0x7c,
	0x39, 0x3f, 0x16, 0x50, 0xe1, 0x51, 0x82, 0xf4, 0x3c, 0x4b, 0x4e, 0x90, 0x39, 0x2c, 0xa1, 0x6a,
	0x6b, 0x1e, 0x55, 0xd5, 0x0b, 0xa9, 0x7a, 0xa5, 0x48, 0xd5, 0xef, 0x14, 0x72, 0xb5, 0x40, 0x88,
	0x47, 0x9e, 0x35, 0x8f, 0x13, 0xf3, 0xc9, 0xaa, 0x91, 0xba, 0xd8, 0x94, 0x52, 0x35, 0x16, 0xff,
	0x21, 0x51, 0xbf, 0x5e, 0x20, 0xcb, 0x5d, 0xea, 0xf6, 0x5d, 0x3a, 0x62, 0x3a, 0xe5, 0x54, 0xdd,
	0x20, 0x8b, 0x16, 0x9b, 0x1c, 0xdb, 0x56, 0x5c, 0x7c, 0x2c, 0x36, 0xe9, 0x43, 0x3c, 0x4c, 0xd3,
	0xb6, 0xa4, 0x3b, 0xb0, 0x56, 0xff, 0x47, 0xd6, 0x4e, 0xec, 0xd0, 0x3d, 0xa7, 0x21, 0x3b, 0x9e,
	0xb0, 0x30, 0x12, 0xad, 0x11, 0x9d, 0x5a, 0x8d, 0xf1, 0x2f, 0x10, 0x16, 0xce, 0x71, 0xdb, 0x65,
	0x11, 0xa7, 0x6e, 0x00, 0xce, 0xad, 0x19, 0x29, 0x20, 0x2e, 0x35, 0xa4, 0x9c, 0xb3, 0x70, 0x0a,
	0xde, 0x55, 0x8c, 0x58, 0x14, 0x6e, 0x47, 0xf6, 0xc8, 0xa3, 0x4e, 0x3c, 0xf5, 0xa0, 0xa4, 0x5e,
	0x23, 0x0d, 0xce, 0xdc, 0xe0, 0x98, 0x79, 0x13, 0x59, 0xfd, 0xeb, 0x42, 0xee, 0x79, 0x93, 0x44,
	0x65, 0x06, 0x63, 0xe0, 0xb5, 0x54, 0x75, 0x83, 0xb1, 0xb8, 0x04, 0x17, 0xec, 0xc1, 0x5f, 0x46,
	0xb0, 0x56, 0xaf, 0x93, 0xe6, 0x2b, 0xdf, 0x77, 0x8f, 0x43, 0x31, 0x54, 0x12, 0x38, 0xbd, 0x21,
	0x00, 0x43, 0x8c, 0x93, 0x1a, 0xa9, 0xdb, 0xe6, 0x29, 0x0d, 0x47, 0x48, 0xee, 0x8a, 0x11, 0x8b,
	0xa2, 0x44, 0xdb, 0xa2, 0xaf, 0x03, 0x9d, 0x2b, 0x06, 0x0a, 0xc2, 0x7e, 0x22, 0xed, 0x57, 0xd0,
	0x5e, 0x8a, 0xe2, 0x18, 0x73, 0x1c, 0xf1, 0x63, 0x8b, 0x72, 0x2a, 0x7f, 0xf4, 0x34, 0x04, 0x20,
	0x62, 0xde, 0xf9, 0x4b, 0x21, 0x5b, 0x71, 0x27, 0xc0, 0x71, 0x01, 0xde, 0xa3, 0x3c, 0x7a, 0x2a,
	0xc5, 0x06, 0x9b, 0x29, 0xf8, 0x95, 0x52, 0xc1, 0xdf, 0x26, 0x4b, 0x5d, 0x1a, 0xf0, 0x71, 0xc8,
	0x32, 0xb3, 0x69, 0x16, 0xca, 0x5a, 0x88, 0xb8, 0xe0, 0x64, 0x9a, 0x85, 0x0a, 0x23, 0x4d, 0xad,
	0x34, 0xd2, 0x6c, 0x91, 0x46, 0x61, 0xd2, 0x4a, 0x64, 0xd0, 0xf9, 0xa1, 0x2b, 0xae, 0x28, 0x67,
	0xab, 0x44, 0x7e, 0xfc, 0xd9, 0xef, 0x6f, 0xda, 0xca, 0xeb, 0x37, 0x6d, 0xe5, 0xcf, 0x37, 0x6d,
	0xe5, 0xdb, 0xb7, 0xed, 0x4b, 0xaf, 0xdf, 0xb6, 0x2f, 0xfd, 0xf1, 0xb6, 0x7d, 0xe9, 0xcb, 0xff,
	0x8c, 0x6c, 0xbe, 0x7b, 0x66, 0x9b, 0xd4, 0x7a, 0xf8, 0x70, 0xd7, 0xf4, 0xdd, 0xbd, 0xd1, 0x2b,
	0x8b, 0x4d, 0xf6, 0x86, 0xd3, 0x68, 0xcf, 0xf2, 0xcd, 0x3d, 0xf8, 0x13, 0x68, 0xb8, 0x08, 0x7f,
	0x09, 0xdd, 0xf9, 0x3b, 0x00, 0x00, 0xff, 0xff, 0x5a, 0x16, 0xb7, 0xba, 0x21, 0x12, 0x00, 0x00,
}

func (m *DbController) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbController) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbController) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.ParentChannelNo != 0 {
		i = encodeVarintBysdb(dAtA, i, uint64((uint32(m.ParentChannelNo)<<1)^uint32((m.ParentChannelNo>>31))))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x88
	}
	if m.DefaultNetworkType != 0 {
		i = encodeVarintBysdb(dAtA, i, uint64((uint32(m.DefaultNetworkType)<<1)^uint32((m.DefaultNetworkType>>31))))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x80
	}
	if len(m.UpdatedDC) > 0 {
		i -= len(m.UpdatedDC)
		copy(dAtA[i:], m.UpdatedDC)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.UpdatedDC)))
		i--
		dAtA[i] = 0x7a
	}
	if len(m.UpdatedAt) > 0 {
		i -= len(m.UpdatedAt)
		copy(dAtA[i:], m.UpdatedAt)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.UpdatedAt)))
		i--
		dAtA[i] = 0x72
	}
	if m.MapShowLevel != 0 {
		i = encodeVarintBysdb(dAtA, i, uint64((uint32(m.MapShowLevel)<<1)^uint32((m.MapShowLevel>>31))))
		i--
		dAtA[i] = 0x60
	}
	if m.ChannelCount != 0 {
		i = encodeVarintBysdb(dAtA, i, uint64((uint32(m.ChannelCount)<<1)^uint32((m.ChannelCount>>31))))
		i--
		dAtA[i] = 0x58
	}
	if m.ControllerHWID != 0 {
		i = encodeVarintBysdb(dAtA, i, uint64((uint32(m.ControllerHWID)<<1)^uint32((m.ControllerHWID>>31))))
		i--
		dAtA[i] = 0x50
	}
	if len(m.Setting) > 0 {
		i -= len(m.Setting)
		copy(dAtA[i:], m.Setting)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.Setting)))
		i--
		dAtA[i] = 0x4a
	}
	if m.Lat != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.Lat))))
		i--
		dAtA[i] = 0x41
	}
	if m.Lon != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.Lon))))
		i--
		dAtA[i] = 0x39
	}
	if len(m.ParentRID) > 0 {
		i -= len(m.ParentRID)
		copy(dAtA[i:], m.ParentRID)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.ParentRID)))
		i--
		dAtA[i] = 0x32
	}
	if m.ControllerType != 0 {
		i = encodeVarintBysdb(dAtA, i, uint64(m.ControllerType))
		i--
		dAtA[i] = 0x28
	}
	if len(m.ControllerDescription) > 0 {
		i -= len(m.ControllerDescription)
		copy(dAtA[i:], m.ControllerDescription)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.ControllerDescription)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.ControllerNo) > 0 {
		i -= len(m.ControllerNo)
		copy(dAtA[i:], m.ControllerNo)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.ControllerNo)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.OrgRID) > 0 {
		i -= len(m.OrgRID)
		copy(dAtA[i:], m.OrgRID)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.OrgRID)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.RID) > 0 {
		i -= len(m.RID)
		copy(dAtA[i:], m.RID)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.RID)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *DbBysMarker) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbBysMarker) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbBysMarker) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.CameraDisabled != 0 {
		i = encodeVarintBysdb(dAtA, i, uint64((uint32(m.CameraDisabled)<<1)^uint32((m.CameraDisabled>>31))))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xf8
	}
	if len(m.IMEI) > 0 {
		i -= len(m.IMEI)
		copy(dAtA[i:], m.IMEI)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.IMEI)))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xf2
	}
	if len(m.ExpirationDate) > 0 {
		i -= len(m.ExpirationDate)
		copy(dAtA[i:], m.ExpirationDate)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.ExpirationDate)))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xea
	}
	if len(m.ICCID) > 0 {
		i -= len(m.ICCID)
		copy(dAtA[i:], m.ICCID)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.ICCID)))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xe2
	}
	if len(m.MarkerSettings) > 0 {
		i -= len(m.MarkerSettings)
		copy(dAtA[i:], m.MarkerSettings)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.MarkerSettings)))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xda
	}
	if m.MarkerType != 0 {
		i = encodeVarintBysdb(dAtA, i, uint64((uint32(m.MarkerType)<<1)^uint32((m.MarkerType>>31))))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xd0
	}
	if m.HasInstallStone {
		i--
		if m.HasInstallStone {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xc8
	}
	if m.HasInstallDevice {
		i--
		if m.HasInstallDevice {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xb8
	}
	if m.MarkerDisabled != 0 {
		i = encodeVarintBysdb(dAtA, i, uint64((uint32(m.MarkerDisabled)<<1)^uint32((m.MarkerDisabled>>31))))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xb0
	}
	if len(m.MarkerWakeupBaseTime) > 0 {
		i -= len(m.MarkerWakeupBaseTime)
		copy(dAtA[i:], m.MarkerWakeupBaseTime)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.MarkerWakeupBaseTime)))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xaa
	}
	if m.MarkerChannel != 0 {
		i = encodeVarintBysdb(dAtA, i, uint64((uint32(m.MarkerChannel)<<1)^uint32((m.MarkerChannel>>31))))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xa0
	}
	if m.MarkerEmergentInterval != 0 {
		i = encodeVarintBysdb(dAtA, i, uint64((uint32(m.MarkerEmergentInterval)<<1)^uint32((m.MarkerEmergentInterval>>31))))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x98
	}
	if m.MarkerQueueInterval != 0 {
		i = encodeVarintBysdb(dAtA, i, uint64((uint32(m.MarkerQueueInterval)<<1)^uint32((m.MarkerQueueInterval>>31))))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x90
	}
	if m.MarkerDayInterval != 0 {
		i = encodeVarintBysdb(dAtA, i, uint64((uint32(m.MarkerDayInterval)<<1)^uint32((m.MarkerDayInterval>>31))))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x88
	}
	if len(m.MarkerParamTime) > 0 {
		i -= len(m.MarkerParamTime)
		copy(dAtA[i:], m.MarkerParamTime)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.MarkerParamTime)))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x82
	}
	if len(m.UpdatedDC) > 0 {
		i -= len(m.UpdatedDC)
		copy(dAtA[i:], m.UpdatedDC)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.UpdatedDC)))
		i--
		dAtA[i] = 0x7a
	}
	if len(m.UpdatedAt) > 0 {
		i -= len(m.UpdatedAt)
		copy(dAtA[i:], m.UpdatedAt)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.UpdatedAt)))
		i--
		dAtA[i] = 0x72
	}
	if m.MarkerQueueNo != 0 {
		i = encodeVarintBysdb(dAtA, i, uint64((uint32(m.MarkerQueueNo)<<1)^uint32((m.MarkerQueueNo>>31))))
		i--
		dAtA[i] = 0x68
	}
	if m.MapShowLevel != 0 {
		i = encodeVarintBysdb(dAtA, i, uint64((uint32(m.MapShowLevel)<<1)^uint32((m.MapShowLevel>>31))))
		i--
		dAtA[i] = 0x60
	}
	if m.MarkerModel != 0 {
		i = encodeVarintBysdb(dAtA, i, uint64((uint32(m.MarkerModel)<<1)^uint32((m.MarkerModel>>31))))
		i--
		dAtA[i] = 0x58
	}
	if len(m.Setting) > 0 {
		i -= len(m.Setting)
		copy(dAtA[i:], m.Setting)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.Setting)))
		i--
		dAtA[i] = 0x52
	}
	if m.MarkerHWID != 0 {
		i = encodeVarintBysdb(dAtA, i, uint64((uint32(m.MarkerHWID)<<1)^uint32((m.MarkerHWID>>31))))
		i--
		dAtA[i] = 0x48
	}
	if m.Lat != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.Lat))))
		i--
		dAtA[i] = 0x41
	}
	if m.Lon != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.Lon))))
		i--
		dAtA[i] = 0x39
	}
	if m.ControllerChannel != 0 {
		i = encodeVarintBysdb(dAtA, i, uint64((uint32(m.ControllerChannel)<<1)^uint32((m.ControllerChannel>>31))))
		i--
		dAtA[i] = 0x30
	}
	if len(m.ControllerRID) > 0 {
		i -= len(m.ControllerRID)
		copy(dAtA[i:], m.ControllerRID)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.ControllerRID)))
		i--
		dAtA[i] = 0x2a
	}
	if len(m.MarkerDescription) > 0 {
		i -= len(m.MarkerDescription)
		copy(dAtA[i:], m.MarkerDescription)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.MarkerDescription)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.MarkerNo) > 0 {
		i -= len(m.MarkerNo)
		copy(dAtA[i:], m.MarkerNo)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.MarkerNo)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.OrgRID) > 0 {
		i -= len(m.OrgRID)
		copy(dAtA[i:], m.OrgRID)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.OrgRID)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.RID) > 0 {
		i -= len(m.RID)
		copy(dAtA[i:], m.RID)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.RID)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *DbControllerOnlineHistory) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbControllerOnlineHistory) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbControllerOnlineHistory) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.DeviceType != 0 {
		i = encodeVarintBysdb(dAtA, i, uint64(m.DeviceType))
		i--
		dAtA[i] = 0x50
	}
	if m.Status != 0 {
		i = encodeVarintBysdb(dAtA, i, uint64(m.Status))
		i--
		dAtA[i] = 0x48
	}
	if m.Power != 0 {
		i -= 4
		encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(math.Float32bits(float32(m.Power))))
		i--
		dAtA[i] = 0x45
	}
	if m.NetworkType != 0 {
		i = encodeVarintBysdb(dAtA, i, uint64((uint32(m.NetworkType)<<1)^uint32((m.NetworkType>>31))))
		i--
		dAtA[i] = 0x38
	}
	if len(m.IpInfo) > 0 {
		i -= len(m.IpInfo)
		copy(dAtA[i:], m.IpInfo)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.IpInfo)))
		i--
		dAtA[i] = 0x32
	}
	if m.ActionCode != 0 {
		i = encodeVarintBysdb(dAtA, i, uint64((uint32(m.ActionCode)<<1)^uint32((m.ActionCode>>31))))
		i--
		dAtA[i] = 0x28
	}
	if m.ControllerHWID != 0 {
		i = encodeVarintBysdb(dAtA, i, uint64((uint32(m.ControllerHWID)<<1)^uint32((m.ControllerHWID>>31))))
		i--
		dAtA[i] = 0x20
	}
	if len(m.ActionTime) > 0 {
		i -= len(m.ActionTime)
		copy(dAtA[i:], m.ActionTime)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.ActionTime)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.OrgRID) > 0 {
		i -= len(m.OrgRID)
		copy(dAtA[i:], m.OrgRID)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.OrgRID)))
		i--
		dAtA[i] = 0x12
	}
	return len(dAtA) - i, nil
}

func (m *DbMediaInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbMediaInfo) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbMediaInfo) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.LastUpdateTime) > 0 {
		i -= len(m.LastUpdateTime)
		copy(dAtA[i:], m.LastUpdateTime)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.LastUpdateTime)))
		i--
		dAtA[i] = 0x62
	}
	if len(m.LastUpdateUserRID) > 0 {
		i -= len(m.LastUpdateUserRID)
		copy(dAtA[i:], m.LastUpdateUserRID)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.LastUpdateUserRID)))
		i--
		dAtA[i] = 0x5a
	}
	if len(m.Setting) > 0 {
		i -= len(m.Setting)
		copy(dAtA[i:], m.Setting)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.Setting)))
		i--
		dAtA[i] = 0x52
	}
	if len(m.UploadTime) > 0 {
		i -= len(m.UploadTime)
		copy(dAtA[i:], m.UploadTime)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.UploadTime)))
		i--
		dAtA[i] = 0x4a
	}
	if len(m.MediaDescription) > 0 {
		i -= len(m.MediaDescription)
		copy(dAtA[i:], m.MediaDescription)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.MediaDescription)))
		i--
		dAtA[i] = 0x42
	}
	if len(m.UploadUserRID) > 0 {
		i -= len(m.UploadUserRID)
		copy(dAtA[i:], m.UploadUserRID)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.UploadUserRID)))
		i--
		dAtA[i] = 0x3a
	}
	if len(m.MediaOrigFileName) > 0 {
		i -= len(m.MediaOrigFileName)
		copy(dAtA[i:], m.MediaOrigFileName)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.MediaOrigFileName)))
		i--
		dAtA[i] = 0x32
	}
	if m.MediaType != 0 {
		i = encodeVarintBysdb(dAtA, i, uint64((uint32(m.MediaType)<<1)^uint32((m.MediaType>>31))))
		i--
		dAtA[i] = 0x28
	}
	if len(m.ControllerRID) > 0 {
		i -= len(m.ControllerRID)
		copy(dAtA[i:], m.ControllerRID)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.ControllerRID)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.MarkerRID) > 0 {
		i -= len(m.MarkerRID)
		copy(dAtA[i:], m.MarkerRID)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.MarkerRID)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.OrgRID) > 0 {
		i -= len(m.OrgRID)
		copy(dAtA[i:], m.OrgRID)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.OrgRID)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.RID) > 0 {
		i -= len(m.RID)
		copy(dAtA[i:], m.RID)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.RID)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *DbMarkerHistory) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbMarkerHistory) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbMarkerHistory) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.ReportInfo) > 0 {
		i -= len(m.ReportInfo)
		copy(dAtA[i:], m.ReportInfo)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.ReportInfo)))
		i--
		dAtA[i] = 0x6a
	}
	if m.RecvControllerID != 0 {
		i = encodeVarintBysdb(dAtA, i, uint64((uint32(m.RecvControllerID)<<1)^uint32((m.RecvControllerID>>31))))
		i--
		dAtA[i] = 0x60
	}
	if len(m.CmdTime) > 0 {
		i -= len(m.CmdTime)
		copy(dAtA[i:], m.CmdTime)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.CmdTime)))
		i--
		dAtA[i] = 0x5a
	}
	if m.Lat != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.Lat))))
		i--
		dAtA[i] = 0x51
	}
	if m.Lon != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.Lon))))
		i--
		dAtA[i] = 0x49
	}
	if len(m.ParamInfo) > 0 {
		i -= len(m.ParamInfo)
		copy(dAtA[i:], m.ParamInfo)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.ParamInfo)))
		i--
		dAtA[i] = 0x42
	}
	if m.Status != 0 {
		i = encodeVarintBysdb(dAtA, i, uint64((uint32(m.Status)<<1)^uint32((m.Status>>31))))
		i--
		dAtA[i] = 0x38
	}
	if m.ActionCode != 0 {
		i = encodeVarintBysdb(dAtA, i, uint64((uint32(m.ActionCode)<<1)^uint32((m.ActionCode>>31))))
		i--
		dAtA[i] = 0x30
	}
	if m.MarkerHWID != 0 {
		i = encodeVarintBysdb(dAtA, i, uint64((uint32(m.MarkerHWID)<<1)^uint32((m.MarkerHWID>>31))))
		i--
		dAtA[i] = 0x28
	}
	if len(m.ActionTime) > 0 {
		i -= len(m.ActionTime)
		copy(dAtA[i:], m.ActionTime)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.ActionTime)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.OrgRID) > 0 {
		i -= len(m.OrgRID)
		copy(dAtA[i:], m.OrgRID)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.OrgRID)))
		i--
		dAtA[i] = 0x1a
	}
	if m.ControllerChannel != 0 {
		i = encodeVarintBysdb(dAtA, i, uint64((uint32(m.ControllerChannel)<<1)^uint32((m.ControllerChannel>>31))))
		i--
		dAtA[i] = 0x10
	}
	if m.ControllerID != 0 {
		i = encodeVarintBysdb(dAtA, i, uint64((uint32(m.ControllerID)<<1)^uint32((m.ControllerID>>31))))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *DbMarkerPatrolHistory) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbMarkerPatrolHistory) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbMarkerPatrolHistory) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.OrgRID) > 0 {
		i -= len(m.OrgRID)
		copy(dAtA[i:], m.OrgRID)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.OrgRID)))
		i--
		dAtA[i] = 0x2a
	}
	if len(m.UserID) > 0 {
		i -= len(m.UserID)
		copy(dAtA[i:], m.UserID)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.UserID)))
		i--
		dAtA[i] = 0x22
	}
	if m.MarkerHWID != 0 {
		i = encodeVarintBysdb(dAtA, i, uint64((uint32(m.MarkerHWID)<<1)^uint32((m.MarkerHWID>>31))))
		i--
		dAtA[i] = 0x18
	}
	if len(m.ActionTime) > 0 {
		i -= len(m.ActionTime)
		copy(dAtA[i:], m.ActionTime)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.ActionTime)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.NFCID) > 0 {
		i -= len(m.NFCID)
		copy(dAtA[i:], m.NFCID)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.NFCID)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *DbNFCPatrolLine) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbNFCPatrolLine) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbNFCPatrolLine) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.UpdatedDC) > 0 {
		i -= len(m.UpdatedDC)
		copy(dAtA[i:], m.UpdatedDC)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.UpdatedDC)))
		i--
		dAtA[i] = 0x32
	}
	if len(m.UpdatedAt) > 0 {
		i -= len(m.UpdatedAt)
		copy(dAtA[i:], m.UpdatedAt)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.UpdatedAt)))
		i--
		dAtA[i] = 0x2a
	}
	if len(m.Note) > 0 {
		i -= len(m.Note)
		copy(dAtA[i:], m.Note)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.Note)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.Name) > 0 {
		i -= len(m.Name)
		copy(dAtA[i:], m.Name)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.Name)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.OrgRID) > 0 {
		i -= len(m.OrgRID)
		copy(dAtA[i:], m.OrgRID)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.OrgRID)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.RID) > 0 {
		i -= len(m.RID)
		copy(dAtA[i:], m.RID)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.RID)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *DbNFCPatrolLineDetail) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbNFCPatrolLineDetail) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbNFCPatrolLineDetail) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.OrgRID) > 0 {
		i -= len(m.OrgRID)
		copy(dAtA[i:], m.OrgRID)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.OrgRID)))
		i--
		dAtA[i] = 0x2a
	}
	if len(m.UpdatedAt) > 0 {
		i -= len(m.UpdatedAt)
		copy(dAtA[i:], m.UpdatedAt)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.UpdatedAt)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.MarkerRID) > 0 {
		i -= len(m.MarkerRID)
		copy(dAtA[i:], m.MarkerRID)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.MarkerRID)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.LineRID) > 0 {
		i -= len(m.LineRID)
		copy(dAtA[i:], m.LineRID)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.LineRID)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.RID) > 0 {
		i -= len(m.RID)
		copy(dAtA[i:], m.RID)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.RID)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *DbNFCPatrolLineRules) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbNFCPatrolLineRules) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbNFCPatrolLineRules) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.UpdatedDC) > 0 {
		i -= len(m.UpdatedDC)
		copy(dAtA[i:], m.UpdatedDC)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.UpdatedDC)))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x9a
	}
	if len(m.UpdatedAt) > 0 {
		i -= len(m.UpdatedAt)
		copy(dAtA[i:], m.UpdatedAt)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.UpdatedAt)))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x92
	}
	if len(m.Note) > 0 {
		i -= len(m.Note)
		copy(dAtA[i:], m.Note)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.Note)))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x8a
	}
	if len(m.EffectiveEnd) > 0 {
		i -= len(m.EffectiveEnd)
		copy(dAtA[i:], m.EffectiveEnd)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.EffectiveEnd)))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x82
	}
	if len(m.EffectiveStart) > 0 {
		i -= len(m.EffectiveStart)
		copy(dAtA[i:], m.EffectiveStart)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.EffectiveStart)))
		i--
		dAtA[i] = 0x7a
	}
	if m.EffectiveType != 0 {
		i = encodeVarintBysdb(dAtA, i, uint64(m.EffectiveType))
		i--
		dAtA[i] = 0x70
	}
	if m.CheckCount != 0 {
		i = encodeVarintBysdb(dAtA, i, uint64(m.CheckCount))
		i--
		dAtA[i] = 0x68
	}
	if len(m.CheckEndTime) > 0 {
		i -= len(m.CheckEndTime)
		copy(dAtA[i:], m.CheckEndTime)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.CheckEndTime)))
		i--
		dAtA[i] = 0x62
	}
	if len(m.CheckStartTime) > 0 {
		i -= len(m.CheckStartTime)
		copy(dAtA[i:], m.CheckStartTime)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.CheckStartTime)))
		i--
		dAtA[i] = 0x5a
	}
	if m.Day7 {
		i--
		if m.Day7 {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x50
	}
	if m.Day6 {
		i--
		if m.Day6 {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x48
	}
	if m.Day5 {
		i--
		if m.Day5 {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x40
	}
	if m.Day4 {
		i--
		if m.Day4 {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x38
	}
	if m.Day3 {
		i--
		if m.Day3 {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x30
	}
	if m.Day2 {
		i--
		if m.Day2 {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x28
	}
	if m.Day1 {
		i--
		if m.Day1 {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x20
	}
	if len(m.Name) > 0 {
		i -= len(m.Name)
		copy(dAtA[i:], m.Name)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.Name)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.OrgRID) > 0 {
		i -= len(m.OrgRID)
		copy(dAtA[i:], m.OrgRID)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.OrgRID)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.RID) > 0 {
		i -= len(m.RID)
		copy(dAtA[i:], m.RID)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.RID)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *DbNFCPatrolLineAndRules) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbNFCPatrolLineAndRules) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbNFCPatrolLineAndRules) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.OrgRID) > 0 {
		i -= len(m.OrgRID)
		copy(dAtA[i:], m.OrgRID)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.OrgRID)))
		i--
		dAtA[i] = 0x2a
	}
	if len(m.UpdatedAt) > 0 {
		i -= len(m.UpdatedAt)
		copy(dAtA[i:], m.UpdatedAt)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.UpdatedAt)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.RuleRID) > 0 {
		i -= len(m.RuleRID)
		copy(dAtA[i:], m.RuleRID)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.RuleRID)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.LineRID) > 0 {
		i -= len(m.LineRID)
		copy(dAtA[i:], m.LineRID)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.LineRID)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.RID) > 0 {
		i -= len(m.RID)
		copy(dAtA[i:], m.RID)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.RID)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *CamImageData) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CamImageData) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CamImageData) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.CustData) > 0 {
		i -= len(m.CustData)
		copy(dAtA[i:], m.CustData)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.CustData)))
		i--
		dAtA[i] = 0x72
	}
	if m.Vcharge != 0 {
		i -= 4
		encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(math.Float32bits(float32(m.Vcharge))))
		i--
		dAtA[i] = 0x6d
	}
	if m.Iload != 0 {
		i -= 4
		encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(math.Float32bits(float32(m.Iload))))
		i--
		dAtA[i] = 0x65
	}
	if m.Icharge != 0 {
		i -= 4
		encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(math.Float32bits(float32(m.Icharge))))
		i--
		dAtA[i] = 0x5d
	}
	if m.ZoomRate != 0 {
		i -= 4
		encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(math.Float32bits(float32(m.ZoomRate))))
		i--
		dAtA[i] = 0x55
	}
	if m.Type != 0 {
		i = encodeVarintBysdb(dAtA, i, uint64((uint32(m.Type)<<1)^uint32((m.Type>>31))))
		i--
		dAtA[i] = 0x48
	}
	if m.TempCpu != 0 {
		i = encodeVarintBysdb(dAtA, i, uint64((uint32(m.TempCpu)<<1)^uint32((m.TempCpu>>31))))
		i--
		dAtA[i] = 0x40
	}
	if m.TempEnv != 0 {
		i = encodeVarintBysdb(dAtA, i, uint64((uint32(m.TempEnv)<<1)^uint32((m.TempEnv>>31))))
		i--
		dAtA[i] = 0x38
	}
	if len(m.Signal) > 0 {
		i -= len(m.Signal)
		copy(dAtA[i:], m.Signal)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.Signal)))
		i--
		dAtA[i] = 0x32
	}
	if m.Battery != 0 {
		i -= 4
		encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(math.Float32bits(float32(m.Battery))))
		i--
		dAtA[i] = 0x2d
	}
	if m.Timestamp != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(m.Timestamp))
		i--
		dAtA[i] = 0x21
	}
	if len(m.FirmwareVersion) > 0 {
		i -= len(m.FirmwareVersion)
		copy(dAtA[i:], m.FirmwareVersion)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.FirmwareVersion)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.Ccid) > 0 {
		i -= len(m.Ccid)
		copy(dAtA[i:], m.Ccid)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.Ccid)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.DevId) > 0 {
		i -= len(m.DevId)
		copy(dAtA[i:], m.DevId)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.DevId)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *DbMarkerUploadImageHistory) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DbMarkerUploadImageHistory) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DbMarkerUploadImageHistory) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.FormData) > 0 {
		i -= len(m.FormData)
		copy(dAtA[i:], m.FormData)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.FormData)))
		i--
		dAtA[i] = 0x3a
	}
	if len(m.FileName) > 0 {
		i -= len(m.FileName)
		copy(dAtA[i:], m.FileName)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.FileName)))
		i--
		dAtA[i] = 0x32
	}
	if len(m.UploadTime) > 0 {
		i -= len(m.UploadTime)
		copy(dAtA[i:], m.UploadTime)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.UploadTime)))
		i--
		dAtA[i] = 0x2a
	}
	if m.CaptureType != 0 {
		i = encodeVarintBysdb(dAtA, i, uint64((uint32(m.CaptureType)<<1)^uint32((m.CaptureType>>31))))
		i--
		dAtA[i] = 0x20
	}
	if len(m.CaptureTime) > 0 {
		i -= len(m.CaptureTime)
		copy(dAtA[i:], m.CaptureTime)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.CaptureTime)))
		i--
		dAtA[i] = 0x1a
	}
	if m.MarkerHWID != 0 {
		i = encodeVarintBysdb(dAtA, i, uint64((uint32(m.MarkerHWID)<<1)^uint32((m.MarkerHWID>>31))))
		i--
		dAtA[i] = 0x10
	}
	if len(m.OrgRID) > 0 {
		i -= len(m.OrgRID)
		copy(dAtA[i:], m.OrgRID)
		i = encodeVarintBysdb(dAtA, i, uint64(len(m.OrgRID)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func encodeVarintBysdb(dAtA []byte, offset int, v uint64) int {
	offset -= sovBysdb(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *DbController) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.RID)
	if l > 0 {
		n += 1 + l + sovBysdb(uint64(l))
	}
	l = len(m.OrgRID)
	if l > 0 {
		n += 1 + l + sovBysdb(uint64(l))
	}
	l = len(m.ControllerNo)
	if l > 0 {
		n += 1 + l + sovBysdb(uint64(l))
	}
	l = len(m.ControllerDescription)
	if l > 0 {
		n += 1 + l + sovBysdb(uint64(l))
	}
	if m.ControllerType != 0 {
		n += 1 + sovBysdb(uint64(m.ControllerType))
	}
	l = len(m.ParentRID)
	if l > 0 {
		n += 1 + l + sovBysdb(uint64(l))
	}
	if m.Lon != 0 {
		n += 9
	}
	if m.Lat != 0 {
		n += 9
	}
	l = len(m.Setting)
	if l > 0 {
		n += 1 + l + sovBysdb(uint64(l))
	}
	if m.ControllerHWID != 0 {
		n += 1 + sozBysdb(uint64(m.ControllerHWID))
	}
	if m.ChannelCount != 0 {
		n += 1 + sozBysdb(uint64(m.ChannelCount))
	}
	if m.MapShowLevel != 0 {
		n += 1 + sozBysdb(uint64(m.MapShowLevel))
	}
	l = len(m.UpdatedAt)
	if l > 0 {
		n += 1 + l + sovBysdb(uint64(l))
	}
	l = len(m.UpdatedDC)
	if l > 0 {
		n += 1 + l + sovBysdb(uint64(l))
	}
	if m.DefaultNetworkType != 0 {
		n += 2 + sozBysdb(uint64(m.DefaultNetworkType))
	}
	if m.ParentChannelNo != 0 {
		n += 2 + sozBysdb(uint64(m.ParentChannelNo))
	}
	return n
}

func (m *DbBysMarker) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.RID)
	if l > 0 {
		n += 1 + l + sovBysdb(uint64(l))
	}
	l = len(m.OrgRID)
	if l > 0 {
		n += 1 + l + sovBysdb(uint64(l))
	}
	l = len(m.MarkerNo)
	if l > 0 {
		n += 1 + l + sovBysdb(uint64(l))
	}
	l = len(m.MarkerDescription)
	if l > 0 {
		n += 1 + l + sovBysdb(uint64(l))
	}
	l = len(m.ControllerRID)
	if l > 0 {
		n += 1 + l + sovBysdb(uint64(l))
	}
	if m.ControllerChannel != 0 {
		n += 1 + sozBysdb(uint64(m.ControllerChannel))
	}
	if m.Lon != 0 {
		n += 9
	}
	if m.Lat != 0 {
		n += 9
	}
	if m.MarkerHWID != 0 {
		n += 1 + sozBysdb(uint64(m.MarkerHWID))
	}
	l = len(m.Setting)
	if l > 0 {
		n += 1 + l + sovBysdb(uint64(l))
	}
	if m.MarkerModel != 0 {
		n += 1 + sozBysdb(uint64(m.MarkerModel))
	}
	if m.MapShowLevel != 0 {
		n += 1 + sozBysdb(uint64(m.MapShowLevel))
	}
	if m.MarkerQueueNo != 0 {
		n += 1 + sozBysdb(uint64(m.MarkerQueueNo))
	}
	l = len(m.UpdatedAt)
	if l > 0 {
		n += 1 + l + sovBysdb(uint64(l))
	}
	l = len(m.UpdatedDC)
	if l > 0 {
		n += 1 + l + sovBysdb(uint64(l))
	}
	l = len(m.MarkerParamTime)
	if l > 0 {
		n += 2 + l + sovBysdb(uint64(l))
	}
	if m.MarkerDayInterval != 0 {
		n += 2 + sozBysdb(uint64(m.MarkerDayInterval))
	}
	if m.MarkerQueueInterval != 0 {
		n += 2 + sozBysdb(uint64(m.MarkerQueueInterval))
	}
	if m.MarkerEmergentInterval != 0 {
		n += 2 + sozBysdb(uint64(m.MarkerEmergentInterval))
	}
	if m.MarkerChannel != 0 {
		n += 2 + sozBysdb(uint64(m.MarkerChannel))
	}
	l = len(m.MarkerWakeupBaseTime)
	if l > 0 {
		n += 2 + l + sovBysdb(uint64(l))
	}
	if m.MarkerDisabled != 0 {
		n += 2 + sozBysdb(uint64(m.MarkerDisabled))
	}
	if m.HasInstallDevice {
		n += 3
	}
	if m.HasInstallStone {
		n += 3
	}
	if m.MarkerType != 0 {
		n += 2 + sozBysdb(uint64(m.MarkerType))
	}
	l = len(m.MarkerSettings)
	if l > 0 {
		n += 2 + l + sovBysdb(uint64(l))
	}
	l = len(m.ICCID)
	if l > 0 {
		n += 2 + l + sovBysdb(uint64(l))
	}
	l = len(m.ExpirationDate)
	if l > 0 {
		n += 2 + l + sovBysdb(uint64(l))
	}
	l = len(m.IMEI)
	if l > 0 {
		n += 2 + l + sovBysdb(uint64(l))
	}
	if m.CameraDisabled != 0 {
		n += 2 + sozBysdb(uint64(m.CameraDisabled))
	}
	return n
}

func (m *DbControllerOnlineHistory) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.OrgRID)
	if l > 0 {
		n += 1 + l + sovBysdb(uint64(l))
	}
	l = len(m.ActionTime)
	if l > 0 {
		n += 1 + l + sovBysdb(uint64(l))
	}
	if m.ControllerHWID != 0 {
		n += 1 + sozBysdb(uint64(m.ControllerHWID))
	}
	if m.ActionCode != 0 {
		n += 1 + sozBysdb(uint64(m.ActionCode))
	}
	l = len(m.IpInfo)
	if l > 0 {
		n += 1 + l + sovBysdb(uint64(l))
	}
	if m.NetworkType != 0 {
		n += 1 + sozBysdb(uint64(m.NetworkType))
	}
	if m.Power != 0 {
		n += 5
	}
	if m.Status != 0 {
		n += 1 + sovBysdb(uint64(m.Status))
	}
	if m.DeviceType != 0 {
		n += 1 + sovBysdb(uint64(m.DeviceType))
	}
	return n
}

func (m *DbMediaInfo) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.RID)
	if l > 0 {
		n += 1 + l + sovBysdb(uint64(l))
	}
	l = len(m.OrgRID)
	if l > 0 {
		n += 1 + l + sovBysdb(uint64(l))
	}
	l = len(m.MarkerRID)
	if l > 0 {
		n += 1 + l + sovBysdb(uint64(l))
	}
	l = len(m.ControllerRID)
	if l > 0 {
		n += 1 + l + sovBysdb(uint64(l))
	}
	if m.MediaType != 0 {
		n += 1 + sozBysdb(uint64(m.MediaType))
	}
	l = len(m.MediaOrigFileName)
	if l > 0 {
		n += 1 + l + sovBysdb(uint64(l))
	}
	l = len(m.UploadUserRID)
	if l > 0 {
		n += 1 + l + sovBysdb(uint64(l))
	}
	l = len(m.MediaDescription)
	if l > 0 {
		n += 1 + l + sovBysdb(uint64(l))
	}
	l = len(m.UploadTime)
	if l > 0 {
		n += 1 + l + sovBysdb(uint64(l))
	}
	l = len(m.Setting)
	if l > 0 {
		n += 1 + l + sovBysdb(uint64(l))
	}
	l = len(m.LastUpdateUserRID)
	if l > 0 {
		n += 1 + l + sovBysdb(uint64(l))
	}
	l = len(m.LastUpdateTime)
	if l > 0 {
		n += 1 + l + sovBysdb(uint64(l))
	}
	return n
}

func (m *DbMarkerHistory) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.ControllerID != 0 {
		n += 1 + sozBysdb(uint64(m.ControllerID))
	}
	if m.ControllerChannel != 0 {
		n += 1 + sozBysdb(uint64(m.ControllerChannel))
	}
	l = len(m.OrgRID)
	if l > 0 {
		n += 1 + l + sovBysdb(uint64(l))
	}
	l = len(m.ActionTime)
	if l > 0 {
		n += 1 + l + sovBysdb(uint64(l))
	}
	if m.MarkerHWID != 0 {
		n += 1 + sozBysdb(uint64(m.MarkerHWID))
	}
	if m.ActionCode != 0 {
		n += 1 + sozBysdb(uint64(m.ActionCode))
	}
	if m.Status != 0 {
		n += 1 + sozBysdb(uint64(m.Status))
	}
	l = len(m.ParamInfo)
	if l > 0 {
		n += 1 + l + sovBysdb(uint64(l))
	}
	if m.Lon != 0 {
		n += 9
	}
	if m.Lat != 0 {
		n += 9
	}
	l = len(m.CmdTime)
	if l > 0 {
		n += 1 + l + sovBysdb(uint64(l))
	}
	if m.RecvControllerID != 0 {
		n += 1 + sozBysdb(uint64(m.RecvControllerID))
	}
	l = len(m.ReportInfo)
	if l > 0 {
		n += 1 + l + sovBysdb(uint64(l))
	}
	return n
}

func (m *DbMarkerPatrolHistory) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.NFCID)
	if l > 0 {
		n += 1 + l + sovBysdb(uint64(l))
	}
	l = len(m.ActionTime)
	if l > 0 {
		n += 1 + l + sovBysdb(uint64(l))
	}
	if m.MarkerHWID != 0 {
		n += 1 + sozBysdb(uint64(m.MarkerHWID))
	}
	l = len(m.UserID)
	if l > 0 {
		n += 1 + l + sovBysdb(uint64(l))
	}
	l = len(m.OrgRID)
	if l > 0 {
		n += 1 + l + sovBysdb(uint64(l))
	}
	return n
}

func (m *DbNFCPatrolLine) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.RID)
	if l > 0 {
		n += 1 + l + sovBysdb(uint64(l))
	}
	l = len(m.OrgRID)
	if l > 0 {
		n += 1 + l + sovBysdb(uint64(l))
	}
	l = len(m.Name)
	if l > 0 {
		n += 1 + l + sovBysdb(uint64(l))
	}
	l = len(m.Note)
	if l > 0 {
		n += 1 + l + sovBysdb(uint64(l))
	}
	l = len(m.UpdatedAt)
	if l > 0 {
		n += 1 + l + sovBysdb(uint64(l))
	}
	l = len(m.UpdatedDC)
	if l > 0 {
		n += 1 + l + sovBysdb(uint64(l))
	}
	return n
}

func (m *DbNFCPatrolLineDetail) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.RID)
	if l > 0 {
		n += 1 + l + sovBysdb(uint64(l))
	}
	l = len(m.LineRID)
	if l > 0 {
		n += 1 + l + sovBysdb(uint64(l))
	}
	l = len(m.MarkerRID)
	if l > 0 {
		n += 1 + l + sovBysdb(uint64(l))
	}
	l = len(m.UpdatedAt)
	if l > 0 {
		n += 1 + l + sovBysdb(uint64(l))
	}
	l = len(m.OrgRID)
	if l > 0 {
		n += 1 + l + sovBysdb(uint64(l))
	}
	return n
}

func (m *DbNFCPatrolLineRules) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.RID)
	if l > 0 {
		n += 1 + l + sovBysdb(uint64(l))
	}
	l = len(m.OrgRID)
	if l > 0 {
		n += 1 + l + sovBysdb(uint64(l))
	}
	l = len(m.Name)
	if l > 0 {
		n += 1 + l + sovBysdb(uint64(l))
	}
	if m.Day1 {
		n += 2
	}
	if m.Day2 {
		n += 2
	}
	if m.Day3 {
		n += 2
	}
	if m.Day4 {
		n += 2
	}
	if m.Day5 {
		n += 2
	}
	if m.Day6 {
		n += 2
	}
	if m.Day7 {
		n += 2
	}
	l = len(m.CheckStartTime)
	if l > 0 {
		n += 1 + l + sovBysdb(uint64(l))
	}
	l = len(m.CheckEndTime)
	if l > 0 {
		n += 1 + l + sovBysdb(uint64(l))
	}
	if m.CheckCount != 0 {
		n += 1 + sovBysdb(uint64(m.CheckCount))
	}
	if m.EffectiveType != 0 {
		n += 1 + sovBysdb(uint64(m.EffectiveType))
	}
	l = len(m.EffectiveStart)
	if l > 0 {
		n += 1 + l + sovBysdb(uint64(l))
	}
	l = len(m.EffectiveEnd)
	if l > 0 {
		n += 2 + l + sovBysdb(uint64(l))
	}
	l = len(m.Note)
	if l > 0 {
		n += 2 + l + sovBysdb(uint64(l))
	}
	l = len(m.UpdatedAt)
	if l > 0 {
		n += 2 + l + sovBysdb(uint64(l))
	}
	l = len(m.UpdatedDC)
	if l > 0 {
		n += 2 + l + sovBysdb(uint64(l))
	}
	return n
}

func (m *DbNFCPatrolLineAndRules) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.RID)
	if l > 0 {
		n += 1 + l + sovBysdb(uint64(l))
	}
	l = len(m.LineRID)
	if l > 0 {
		n += 1 + l + sovBysdb(uint64(l))
	}
	l = len(m.RuleRID)
	if l > 0 {
		n += 1 + l + sovBysdb(uint64(l))
	}
	l = len(m.UpdatedAt)
	if l > 0 {
		n += 1 + l + sovBysdb(uint64(l))
	}
	l = len(m.OrgRID)
	if l > 0 {
		n += 1 + l + sovBysdb(uint64(l))
	}
	return n
}

func (m *CamImageData) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.DevId)
	if l > 0 {
		n += 1 + l + sovBysdb(uint64(l))
	}
	l = len(m.Ccid)
	if l > 0 {
		n += 1 + l + sovBysdb(uint64(l))
	}
	l = len(m.FirmwareVersion)
	if l > 0 {
		n += 1 + l + sovBysdb(uint64(l))
	}
	if m.Timestamp != 0 {
		n += 9
	}
	if m.Battery != 0 {
		n += 5
	}
	l = len(m.Signal)
	if l > 0 {
		n += 1 + l + sovBysdb(uint64(l))
	}
	if m.TempEnv != 0 {
		n += 1 + sozBysdb(uint64(m.TempEnv))
	}
	if m.TempCpu != 0 {
		n += 1 + sozBysdb(uint64(m.TempCpu))
	}
	if m.Type != 0 {
		n += 1 + sozBysdb(uint64(m.Type))
	}
	if m.ZoomRate != 0 {
		n += 5
	}
	if m.Icharge != 0 {
		n += 5
	}
	if m.Iload != 0 {
		n += 5
	}
	if m.Vcharge != 0 {
		n += 5
	}
	l = len(m.CustData)
	if l > 0 {
		n += 1 + l + sovBysdb(uint64(l))
	}
	return n
}

func (m *DbMarkerUploadImageHistory) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.OrgRID)
	if l > 0 {
		n += 1 + l + sovBysdb(uint64(l))
	}
	if m.MarkerHWID != 0 {
		n += 1 + sozBysdb(uint64(m.MarkerHWID))
	}
	l = len(m.CaptureTime)
	if l > 0 {
		n += 1 + l + sovBysdb(uint64(l))
	}
	if m.CaptureType != 0 {
		n += 1 + sozBysdb(uint64(m.CaptureType))
	}
	l = len(m.UploadTime)
	if l > 0 {
		n += 1 + l + sovBysdb(uint64(l))
	}
	l = len(m.FileName)
	if l > 0 {
		n += 1 + l + sovBysdb(uint64(l))
	}
	l = len(m.FormData)
	if l > 0 {
		n += 1 + l + sovBysdb(uint64(l))
	}
	return n
}

func sovBysdb(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozBysdb(x uint64) (n int) {
	return sovBysdb(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *DbController) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBysdb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DbController: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DbController: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.RID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OrgRID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OrgRID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ControllerNo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ControllerNo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ControllerDescription", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ControllerDescription = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ControllerType", wireType)
			}
			m.ControllerType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ControllerType |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ParentRID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ParentRID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field Lon", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.Lon = float64(math.Float64frombits(v))
		case 8:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field Lat", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.Lat = float64(math.Float64frombits(v))
		case 9:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Setting", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Setting = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 10:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ControllerHWID", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.ControllerHWID = v
		case 11:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ChannelCount", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.ChannelCount = v
		case 12:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field MapShowLevel", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.MapShowLevel = v
		case 14:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UpdatedAt", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UpdatedAt = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 15:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UpdatedDC", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UpdatedDC = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 16:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field DefaultNetworkType", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.DefaultNetworkType = v
		case 17:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ParentChannelNo", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.ParentChannelNo = v
		default:
			iNdEx = preIndex
			skippy, err := skipBysdb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBysdb
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthBysdb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DbBysMarker) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBysdb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DbBysMarker: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DbBysMarker: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.RID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OrgRID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OrgRID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field MarkerNo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.MarkerNo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field MarkerDescription", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.MarkerDescription = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ControllerRID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ControllerRID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ControllerChannel", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.ControllerChannel = v
		case 7:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field Lon", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.Lon = float64(math.Float64frombits(v))
		case 8:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field Lat", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.Lat = float64(math.Float64frombits(v))
		case 9:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field MarkerHWID", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.MarkerHWID = v
		case 10:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Setting", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Setting = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 11:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field MarkerModel", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.MarkerModel = v
		case 12:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field MapShowLevel", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.MapShowLevel = v
		case 13:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field MarkerQueueNo", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.MarkerQueueNo = v
		case 14:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UpdatedAt", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UpdatedAt = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 15:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UpdatedDC", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UpdatedDC = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 16:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field MarkerParamTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.MarkerParamTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 17:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field MarkerDayInterval", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.MarkerDayInterval = v
		case 18:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field MarkerQueueInterval", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.MarkerQueueInterval = v
		case 19:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field MarkerEmergentInterval", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.MarkerEmergentInterval = v
		case 20:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field MarkerChannel", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.MarkerChannel = v
		case 21:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field MarkerWakeupBaseTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.MarkerWakeupBaseTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 22:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field MarkerDisabled", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.MarkerDisabled = v
		case 23:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field HasInstallDevice", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.HasInstallDevice = bool(v != 0)
		case 25:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field HasInstallStone", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.HasInstallStone = bool(v != 0)
		case 26:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field MarkerType", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.MarkerType = v
		case 27:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field MarkerSettings", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.MarkerSettings = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 28:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ICCID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ICCID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 29:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ExpirationDate", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ExpirationDate = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 30:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field IMEI", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.IMEI = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 31:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CameraDisabled", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.CameraDisabled = v
		default:
			iNdEx = preIndex
			skippy, err := skipBysdb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBysdb
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthBysdb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DbControllerOnlineHistory) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBysdb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DbControllerOnlineHistory: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DbControllerOnlineHistory: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OrgRID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OrgRID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ActionTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ActionTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ControllerHWID", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.ControllerHWID = v
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ActionCode", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.ActionCode = v
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field IpInfo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.IpInfo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field NetworkType", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.NetworkType = v
		case 8:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field Power", wireType)
			}
			var v uint32
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
			m.Power = float32(math.Float32frombits(v))
		case 9:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field DeviceType", wireType)
			}
			m.DeviceType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DeviceType |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBysdb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBysdb
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthBysdb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DbMediaInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBysdb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DbMediaInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DbMediaInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.RID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OrgRID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OrgRID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field MarkerRID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.MarkerRID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ControllerRID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ControllerRID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field MediaType", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.MediaType = v
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field MediaOrigFileName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.MediaOrigFileName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UploadUserRID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UploadUserRID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field MediaDescription", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.MediaDescription = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UploadTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UploadTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Setting", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Setting = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LastUpdateUserRID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.LastUpdateUserRID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 12:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LastUpdateTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.LastUpdateTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBysdb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBysdb
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthBysdb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DbMarkerHistory) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBysdb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DbMarkerHistory: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DbMarkerHistory: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ControllerID", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.ControllerID = v
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ControllerChannel", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.ControllerChannel = v
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OrgRID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OrgRID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ActionTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ActionTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field MarkerHWID", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.MarkerHWID = v
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ActionCode", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.ActionCode = v
		case 7:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.Status = v
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ParamInfo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ParamInfo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 9:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field Lon", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.Lon = float64(math.Float64frombits(v))
		case 10:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field Lat", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.Lat = float64(math.Float64frombits(v))
		case 11:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CmdTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CmdTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 12:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RecvControllerID", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.RecvControllerID = v
		case 13:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ReportInfo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ReportInfo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBysdb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBysdb
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthBysdb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DbMarkerPatrolHistory) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBysdb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DbMarkerPatrolHistory: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DbMarkerPatrolHistory: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field NFCID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.NFCID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ActionTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ActionTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field MarkerHWID", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.MarkerHWID = v
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UserID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UserID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OrgRID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OrgRID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBysdb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBysdb
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthBysdb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DbNFCPatrolLine) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBysdb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DbNFCPatrolLine: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DbNFCPatrolLine: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.RID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OrgRID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OrgRID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Note", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Note = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UpdatedAt", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UpdatedAt = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UpdatedDC", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UpdatedDC = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBysdb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBysdb
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthBysdb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DbNFCPatrolLineDetail) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBysdb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DbNFCPatrolLineDetail: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DbNFCPatrolLineDetail: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.RID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LineRID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.LineRID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field MarkerRID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.MarkerRID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UpdatedAt", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UpdatedAt = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OrgRID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OrgRID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBysdb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBysdb
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthBysdb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DbNFCPatrolLineRules) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBysdb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DbNFCPatrolLineRules: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DbNFCPatrolLineRules: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.RID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OrgRID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OrgRID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Day1", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Day1 = bool(v != 0)
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Day2", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Day2 = bool(v != 0)
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Day3", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Day3 = bool(v != 0)
		case 7:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Day4", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Day4 = bool(v != 0)
		case 8:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Day5", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Day5 = bool(v != 0)
		case 9:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Day6", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Day6 = bool(v != 0)
		case 10:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Day7", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Day7 = bool(v != 0)
		case 11:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CheckStartTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CheckStartTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 12:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CheckEndTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CheckEndTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 13:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CheckCount", wireType)
			}
			m.CheckCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CheckCount |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 14:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field EffectiveType", wireType)
			}
			m.EffectiveType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EffectiveType |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 15:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field EffectiveStart", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.EffectiveStart = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 16:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field EffectiveEnd", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.EffectiveEnd = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 17:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Note", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Note = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 18:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UpdatedAt", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UpdatedAt = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 19:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UpdatedDC", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UpdatedDC = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBysdb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBysdb
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthBysdb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DbNFCPatrolLineAndRules) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBysdb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DbNFCPatrolLineAndRules: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DbNFCPatrolLineAndRules: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.RID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LineRID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.LineRID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RuleRID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.RuleRID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UpdatedAt", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UpdatedAt = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OrgRID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OrgRID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBysdb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBysdb
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthBysdb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CamImageData) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBysdb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CamImageData: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CamImageData: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DevId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.DevId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Ccid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Ccid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field FirmwareVersion", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.FirmwareVersion = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field Timestamp", wireType)
			}
			m.Timestamp = 0
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			m.Timestamp = int64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
		case 5:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field Battery", wireType)
			}
			var v uint32
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
			m.Battery = float32(math.Float32frombits(v))
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Signal", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Signal = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TempEnv", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.TempEnv = v
		case 8:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TempCpu", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.TempCpu = v
		case 9:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.Type = v
		case 10:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field ZoomRate", wireType)
			}
			var v uint32
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
			m.ZoomRate = float32(math.Float32frombits(v))
		case 11:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field Icharge", wireType)
			}
			var v uint32
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
			m.Icharge = float32(math.Float32frombits(v))
		case 12:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field Iload", wireType)
			}
			var v uint32
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
			m.Iload = float32(math.Float32frombits(v))
		case 13:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field Vcharge", wireType)
			}
			var v uint32
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
			m.Vcharge = float32(math.Float32frombits(v))
		case 14:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CustData", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CustData = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBysdb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBysdb
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthBysdb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DbMarkerUploadImageHistory) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBysdb
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DbMarkerUploadImageHistory: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DbMarkerUploadImageHistory: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OrgRID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OrgRID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field MarkerHWID", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.MarkerHWID = v
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CaptureTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CaptureTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CaptureType", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.CaptureType = v
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UploadTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UploadTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field FileName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.FileName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field FormData", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBysdb
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBysdb
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.FormData = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBysdb(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthBysdb
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthBysdb
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipBysdb(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowBysdb
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowBysdb
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthBysdb
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupBysdb
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthBysdb
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthBysdb        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowBysdb          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupBysdb = fmt.Errorf("proto: unexpected end of group")
)

//Package bysdb  generated by ygen-gocrud. DO NOT EDIT.
//source: bysdb.proto
package bysdb

import (
	"context"
	"strconv"

	"git.kicad99.com/ykit/goutil/crud"
	"git.kicad99.com/ykit/goutil/crudhook"
	"git.kicad99.com/ykit/goutil"
	"git.kicad99.com/ykit/goutil/pgxdb"
	"git.kicad99.com/ykit/goutil/rpc"
	"git.kicad99.com/ykit/goutil/yreflect"
	"google.golang.org/grpc/metadata"
)

// CrudRpcDbControllerServer impl crud Service
type CrudRpcDbControllerServer struct {
}

//Insert impl crud insert
func (*CrudRpcDbControllerServer) Insert(ctx context.Context, req *DbController) (r *crud.DMLResult, err error) {
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	r, err = req.Insert(db)
	if err == nil {
		sys, userRid, sid := pgxdb.GetYkitInfoFromMetadata(ctx)
		ipinfo := goutil.GetIpinfoFromMeta(ctx)
		userOrgrid := goutil.GetUserOrgRIDFromMeta(ctx)
		goutil.NatsCdc(sys, req.OrgRID, sid, "DbController.Insert", req, nil)
		crudhook.CrudAfterHook(sys, userRid, userOrgrid, "DbController.Insert", req, nil, ipinfo)
	}
	return
}

//Update impl crud update
func (*CrudRpcDbControllerServer) Update(ctx context.Context, req *DbController) (r *crud.DMLResult, err error) {
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	r, err = req.Update(db)
	if err == nil {
		sys, userRid, sid := pgxdb.GetYkitInfoFromMetadata(ctx)
		ipinfo := goutil.GetIpinfoFromMeta(ctx)
		userOrgrid := goutil.GetUserOrgRIDFromMeta(ctx)
		goutil.NatsCdc(sys, req.OrgRID, sid, "DbController.Update", req, nil)
		crudhook.CrudAfterHook(sys, userRid, userOrgrid, "DbController.Update", req, nil, ipinfo)
	}
	return
}

//PartialUpdate impl crud PartialUpdate
func (*CrudRpcDbControllerServer) PartialUpdate(ctx context.Context, req *DbController) (r *crud.DMLResult, err error) {
	param, err := crud.GetDMLParamFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	r, err = req.PartialUpdate(param.KeyColumn, db)
	if err == nil {
		sys, userRid, sid := pgxdb.GetYkitInfoFromMetadata(ctx)
		ipinfo := goutil.GetIpinfoFromMeta(ctx)
		userOrgrid := goutil.GetUserOrgRIDFromMeta(ctx)
		goutil.NatsCdc(sys, req.OrgRID, sid, "DbController.PartialUpdate", req, param)
		crudhook.CrudAfterHook(sys, userRid, userOrgrid, "DbController.PartialUpdate", req, param, ipinfo)
	}
	return
}

//Delete impl crud Delete
func (*CrudRpcDbControllerServer) Delete(ctx context.Context, req *DbController) (r *crud.DMLResult, err error) {
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	r, err = req.Delete(db)
	if err == nil {
		sys, userRid, sid := pgxdb.GetYkitInfoFromMetadata(ctx)
		ipinfo := goutil.GetIpinfoFromMeta(ctx)
		userOrgrid := goutil.GetUserOrgRIDFromMeta(ctx)
		goutil.NatsCdc(sys, req.OrgRID, sid, "DbController.Delete", req, nil)
		crudhook.CrudAfterHook(sys, userRid, userOrgrid, "DbController.Delete", req, nil, ipinfo)
	}
	return
}

//SelectOne impl crud SelectOne
func (*CrudRpcDbControllerServer) SelectOne(ctx context.Context, dmlParam *crud.DMLParam) (*DbController, error) {
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	if len(dmlParam.KeyValue) != 1 {
		return nil, rpc.EBadProtoParam
	}
	msg := &DbController{RID: dmlParam.KeyValue[0]}

	selectRow, err := msg.SelectOne(dmlParam.ResultColumn, db)
	if err != nil {
		return nil, err
	}
	if selectRow == 0 {
		msg.Reset()
	}
	return msg, nil
}

//SelectMany impl crud SelectMany
func (*CrudRpcDbControllerServer) SelectMany(dmlParam *crud.DMLParam, srv RpcDbController_SelectManyServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)

	msg := &DbController{}

	rows, err := msg.SelectByRids(dmlParam.ResultColumn, dmlParam.KeyValue, db)
	if err != nil {
		return err
	}
	defer rows.Close()
	rowsName := crud.RowsColNames2GoFieldNames(rows, msg)
	rowsCount := 0
	for rows.Next() {
		msg := &DbController{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		err = srv.Send(msg)
		if err != nil {
			return err
		}
		rowsCount++
	}
	trailer := metadata.Pairs("rows", strconv.Itoa(rowsCount))
	srv.SetTrailer(trailer)
	return nil
}

//Query impl crud Query
func (*CrudRpcDbControllerServer) Query(req *crud.QueryParam, srv RpcDbController_QueryServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)

	rows, err := crud.DbSelectQuery("DbController", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbController{})
	rowsCount := 0
	for rows.Next() {
		msg := &DbController{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		err = srv.Send(msg)
		if err != nil {
			return err
		}
		rowsCount++
	}
	trailer := metadata.Pairs("rows", strconv.Itoa(rowsCount))
	srv.SetTrailer(trailer)
	return nil
}
//Query impl crud QueryBatch
func (*CrudRpcDbControllerServer) QueryBatch(req *crud.QueryParam, srv RpcDbController_QueryBatchServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)

	rows, err := crud.DbSelectQuery("DbController", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	if req.Batch < 1 {
		req.Batch = 1
	}
	if req.Batch > 10000 {
		req.Batch = 10000
	}

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbController{})
	var batchNo uint32 = 0
	var rowsOffset uint32 = 0
	var batchCount uint32 = 0
	list := &DbControllerList{}
	for rows.Next() {
		msg := &DbController{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		list.Rows = append(list.Rows, msg)
		batchCount++
		rowsOffset++

		if batchCount >= req.Batch {
			err = srv.Send(list)
			if err != nil {
				return err
			}
			batchNo++
			list = &DbControllerList{
				BatchNo:    batchNo,
				RowsOffset: rowsOffset}
			batchCount = 0
		}
	}
	if batchCount > 0 {
		err = srv.Send(list)
		if err != nil {
			return err
		}
	}
	trailer := metadata.Pairs("rows", strconv.FormatUint(uint64(rowsOffset), 10))
	srv.SetTrailer(trailer)
	return nil
}

// PcrudRpcDbControllerServer impl pcrud Service
type PcrudRpcDbControllerServer struct {
}

//Insert impl pcrud Insert
func (*PcrudRpcDbControllerServer) Insert(ctx context.Context, req *DbController) (*crud.DMLResult, error) {
	var crudServer *CrudRpcDbControllerServer
	_, err := crud.GrpcCheckHasPermission(ctx, "db", "DbController.Insert")
	if err != nil {
		return nil, err
	}
	err = crud.GrpcCheckHasOrgridPrivilege(ctx, req.OrgRID)
	if err != nil {
		return nil, err
	}
	return crudServer.Insert(ctx, req)
}

//Update impl pcrud Update
func (*PcrudRpcDbControllerServer) Update(ctx context.Context, req *DbController) (*crud.DMLResult, error) {
	var crudServer *CrudRpcDbControllerServer
	_, err := crud.GrpcCheckHasPermission(ctx, "db", "DbController.Update")
	if err != nil {
		return nil, err
	}
	err = crud.GrpcCheckHasOrgridPrivilege(ctx, req.OrgRID)
	if err != nil {
		return nil, err
	}
	return crudServer.Update(ctx, req)
}

//PartialUpdate impl pcrud PartialUpdate
func (*PcrudRpcDbControllerServer) PartialUpdate(ctx context.Context, req *DbController) (*crud.DMLResult, error) {
	var crudServer *CrudRpcDbControllerServer
	_, err := crud.GrpcCheckHasPermission(ctx, "db", "DbController.Update")
	if err != nil {
		return nil, err
	}
	err = crud.GrpcCheckHasOrgridPrivilege(ctx, req.OrgRID)
	if err != nil {
		return nil, err
	}
	return crudServer.PartialUpdate(ctx, req)
}

//Delete impl pcrud Delete
func (*PcrudRpcDbControllerServer) Delete(ctx context.Context, req *DbController) (*crud.DMLResult, error) {
	var crudServer *CrudRpcDbControllerServer
	_, err := crud.GrpcCheckHasPermission(ctx, "db", "DbController.Delete")
	if err != nil {
		return nil, err
	}
	err = crud.GrpcCheckHasOrgridPrivilege(ctx, req.OrgRID)
	if err != nil {
		return nil, err
	}
	return crudServer.Delete(ctx, req)
}

//SelectOne impl pcrud SelectOne
func (*PcrudRpcDbControllerServer) SelectOne(ctx context.Context, dmlParam *crud.DMLParam) (*DbController, error) {
	var crudServer *CrudRpcDbControllerServer
	return crudServer.SelectOne(ctx, dmlParam)
}

//SelectMany impl pcrud SelectMany
func (*PcrudRpcDbControllerServer) SelectMany(dmlParam *crud.DMLParam, srv PrpcDbController_SelectManyServer) error {
	var crudServer *CrudRpcDbControllerServer
	return crudServer.SelectMany(dmlParam, srv)
}

//Query impl pcrud Query
func (*PcrudRpcDbControllerServer) Query(req *crud.PrivilegeParam, srv PrpcDbController_QueryServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)
	rows, err := crud.DbSelectPrivilege("DbController", "OrgRID", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbController{})
	rowsCount := 0
	for rows.Next() {
		msg := &DbController{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		err = srv.Send(msg)
		if err != nil {
			return err
		}
		rowsCount++
	}
	trailer := metadata.Pairs("rows", strconv.Itoa(rowsCount))
	srv.SetTrailer(trailer)
	return nil
}
//Query impl pcrud QueryBatch
func (*PcrudRpcDbControllerServer) QueryBatch(req *crud.PrivilegeParam, srv PrpcDbController_QueryBatchServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)
	rows, err := crud.DbSelectPrivilege("DbController", "OrgRID", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	if req.QueryCondition.Batch < 1 {
		req.QueryCondition.Batch = 1
	}
	if req.QueryCondition.Batch > 10000 {
		req.QueryCondition.Batch = 10000
	}

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbController{})

	var batchNo uint32 = 0
	var rowsOffset uint32 = 0
	var batchCount uint32 = 0
	list := &DbControllerList{}
	for rows.Next() {
		msg := &DbController{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		list.Rows = append(list.Rows, msg)
		batchCount++
		rowsOffset++

		if batchCount >= req.QueryCondition.Batch {
			err = srv.Send(list)
			if err != nil {
				return err
			}
			batchNo++
			list = &DbControllerList{
				BatchNo:    batchNo,
				RowsOffset: rowsOffset}
			batchCount = 0
		}
	}
	if batchCount > 0 {
		err = srv.Send(list)
		if err != nil {
			return err
		}
	}
	trailer := metadata.Pairs("rows", strconv.FormatUint(uint64(rowsOffset), 10))
	srv.SetTrailer(trailer)
	return nil
}
// CrudRpcDbBysMarkerServer impl crud Service
type CrudRpcDbBysMarkerServer struct {
}

//Insert impl crud insert
func (*CrudRpcDbBysMarkerServer) Insert(ctx context.Context, req *DbBysMarker) (r *crud.DMLResult, err error) {
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	r, err = req.Insert(db)
	if err == nil {
		sys, userRid, sid := pgxdb.GetYkitInfoFromMetadata(ctx)
		ipinfo := goutil.GetIpinfoFromMeta(ctx)
		userOrgrid := goutil.GetUserOrgRIDFromMeta(ctx)
		goutil.NatsCdc(sys, req.OrgRID, sid, "DbBysMarker.Insert", req, nil)
		crudhook.CrudAfterHook(sys, userRid, userOrgrid, "DbBysMarker.Insert", req, nil, ipinfo)
	}
	return
}

//Update impl crud update
func (*CrudRpcDbBysMarkerServer) Update(ctx context.Context, req *DbBysMarker) (r *crud.DMLResult, err error) {
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	r, err = req.Update(db)
	if err == nil {
		sys, userRid, sid := pgxdb.GetYkitInfoFromMetadata(ctx)
		ipinfo := goutil.GetIpinfoFromMeta(ctx)
		userOrgrid := goutil.GetUserOrgRIDFromMeta(ctx)
		goutil.NatsCdc(sys, req.OrgRID, sid, "DbBysMarker.Update", req, nil)
		crudhook.CrudAfterHook(sys, userRid, userOrgrid, "DbBysMarker.Update", req, nil, ipinfo)
	}
	return
}

//PartialUpdate impl crud PartialUpdate
func (*CrudRpcDbBysMarkerServer) PartialUpdate(ctx context.Context, req *DbBysMarker) (r *crud.DMLResult, err error) {
	param, err := crud.GetDMLParamFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	r, err = req.PartialUpdate(param.KeyColumn, db)
	if err == nil {
		sys, userRid, sid := pgxdb.GetYkitInfoFromMetadata(ctx)
		ipinfo := goutil.GetIpinfoFromMeta(ctx)
		userOrgrid := goutil.GetUserOrgRIDFromMeta(ctx)
		goutil.NatsCdc(sys, req.OrgRID, sid, "DbBysMarker.PartialUpdate", req, param)
		crudhook.CrudAfterHook(sys, userRid, userOrgrid, "DbBysMarker.PartialUpdate", req, param, ipinfo)
	}
	return
}

//Delete impl crud Delete
func (*CrudRpcDbBysMarkerServer) Delete(ctx context.Context, req *DbBysMarker) (r *crud.DMLResult, err error) {
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	r, err = req.Delete(db)
	if err == nil {
		sys, userRid, sid := pgxdb.GetYkitInfoFromMetadata(ctx)
		ipinfo := goutil.GetIpinfoFromMeta(ctx)
		userOrgrid := goutil.GetUserOrgRIDFromMeta(ctx)
		goutil.NatsCdc(sys, req.OrgRID, sid, "DbBysMarker.Delete", req, nil)
		crudhook.CrudAfterHook(sys, userRid, userOrgrid, "DbBysMarker.Delete", req, nil, ipinfo)
	}
	return
}

//SelectOne impl crud SelectOne
func (*CrudRpcDbBysMarkerServer) SelectOne(ctx context.Context, dmlParam *crud.DMLParam) (*DbBysMarker, error) {
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	if len(dmlParam.KeyValue) != 1 {
		return nil, rpc.EBadProtoParam
	}
	msg := &DbBysMarker{RID: dmlParam.KeyValue[0]}

	selectRow, err := msg.SelectOne(dmlParam.ResultColumn, db)
	if err != nil {
		return nil, err
	}
	if selectRow == 0 {
		msg.Reset()
	}
	return msg, nil
}

//SelectMany impl crud SelectMany
func (*CrudRpcDbBysMarkerServer) SelectMany(dmlParam *crud.DMLParam, srv RpcDbBysMarker_SelectManyServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)

	msg := &DbBysMarker{}

	rows, err := msg.SelectByRids(dmlParam.ResultColumn, dmlParam.KeyValue, db)
	if err != nil {
		return err
	}
	defer rows.Close()
	rowsName := crud.RowsColNames2GoFieldNames(rows, msg)
	rowsCount := 0
	for rows.Next() {
		msg := &DbBysMarker{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		err = srv.Send(msg)
		if err != nil {
			return err
		}
		rowsCount++
	}
	trailer := metadata.Pairs("rows", strconv.Itoa(rowsCount))
	srv.SetTrailer(trailer)
	return nil
}

//Query impl crud Query
func (*CrudRpcDbBysMarkerServer) Query(req *crud.QueryParam, srv RpcDbBysMarker_QueryServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)

	rows, err := crud.DbSelectQuery("DbBysMarker", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbBysMarker{})
	rowsCount := 0
	for rows.Next() {
		msg := &DbBysMarker{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		err = srv.Send(msg)
		if err != nil {
			return err
		}
		rowsCount++
	}
	trailer := metadata.Pairs("rows", strconv.Itoa(rowsCount))
	srv.SetTrailer(trailer)
	return nil
}
//Query impl crud QueryBatch
func (*CrudRpcDbBysMarkerServer) QueryBatch(req *crud.QueryParam, srv RpcDbBysMarker_QueryBatchServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)

	rows, err := crud.DbSelectQuery("DbBysMarker", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	if req.Batch < 1 {
		req.Batch = 1
	}
	if req.Batch > 10000 {
		req.Batch = 10000
	}

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbBysMarker{})
	var batchNo uint32 = 0
	var rowsOffset uint32 = 0
	var batchCount uint32 = 0
	list := &DbBysMarkerList{}
	for rows.Next() {
		msg := &DbBysMarker{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		list.Rows = append(list.Rows, msg)
		batchCount++
		rowsOffset++

		if batchCount >= req.Batch {
			err = srv.Send(list)
			if err != nil {
				return err
			}
			batchNo++
			list = &DbBysMarkerList{
				BatchNo:    batchNo,
				RowsOffset: rowsOffset}
			batchCount = 0
		}
	}
	if batchCount > 0 {
		err = srv.Send(list)
		if err != nil {
			return err
		}
	}
	trailer := metadata.Pairs("rows", strconv.FormatUint(uint64(rowsOffset), 10))
	srv.SetTrailer(trailer)
	return nil
}

// PcrudRpcDbBysMarkerServer impl pcrud Service
type PcrudRpcDbBysMarkerServer struct {
}

//Insert impl pcrud Insert
func (*PcrudRpcDbBysMarkerServer) Insert(ctx context.Context, req *DbBysMarker) (*crud.DMLResult, error) {
	var crudServer *CrudRpcDbBysMarkerServer
	_, err := crud.GrpcCheckHasPermission(ctx, "db", "DbBysMarker.Insert")
	if err != nil {
		return nil, err
	}
	err = crud.GrpcCheckHasOrgridPrivilege(ctx, req.OrgRID)
	if err != nil {
		return nil, err
	}
	return crudServer.Insert(ctx, req)
}

//Update impl pcrud Update
func (*PcrudRpcDbBysMarkerServer) Update(ctx context.Context, req *DbBysMarker) (*crud.DMLResult, error) {
	var crudServer *CrudRpcDbBysMarkerServer
	_, err := crud.GrpcCheckHasPermission(ctx, "db", "DbBysMarker.Update")
	if err != nil {
		return nil, err
	}
	err = crud.GrpcCheckHasOrgridPrivilege(ctx, req.OrgRID)
	if err != nil {
		return nil, err
	}
	return crudServer.Update(ctx, req)
}

//PartialUpdate impl pcrud PartialUpdate
func (*PcrudRpcDbBysMarkerServer) PartialUpdate(ctx context.Context, req *DbBysMarker) (*crud.DMLResult, error) {
	var crudServer *CrudRpcDbBysMarkerServer
	_, err := crud.GrpcCheckHasPermission(ctx, "db", "DbBysMarker.Update")
	if err != nil {
		return nil, err
	}
	err = crud.GrpcCheckHasOrgridPrivilege(ctx, req.OrgRID)
	if err != nil {
		return nil, err
	}
	return crudServer.PartialUpdate(ctx, req)
}

//Delete impl pcrud Delete
func (*PcrudRpcDbBysMarkerServer) Delete(ctx context.Context, req *DbBysMarker) (*crud.DMLResult, error) {
	var crudServer *CrudRpcDbBysMarkerServer
	_, err := crud.GrpcCheckHasPermission(ctx, "db", "DbBysMarker.Delete")
	if err != nil {
		return nil, err
	}
	err = crud.GrpcCheckHasOrgridPrivilege(ctx, req.OrgRID)
	if err != nil {
		return nil, err
	}
	return crudServer.Delete(ctx, req)
}

//SelectOne impl pcrud SelectOne
func (*PcrudRpcDbBysMarkerServer) SelectOne(ctx context.Context, dmlParam *crud.DMLParam) (*DbBysMarker, error) {
	var crudServer *CrudRpcDbBysMarkerServer
	return crudServer.SelectOne(ctx, dmlParam)
}

//SelectMany impl pcrud SelectMany
func (*PcrudRpcDbBysMarkerServer) SelectMany(dmlParam *crud.DMLParam, srv PrpcDbBysMarker_SelectManyServer) error {
	var crudServer *CrudRpcDbBysMarkerServer
	return crudServer.SelectMany(dmlParam, srv)
}

//Query impl pcrud Query
func (*PcrudRpcDbBysMarkerServer) Query(req *crud.PrivilegeParam, srv PrpcDbBysMarker_QueryServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)
	rows, err := crud.DbSelectPrivilege("DbBysMarker", "OrgRID", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbBysMarker{})
	rowsCount := 0
	for rows.Next() {
		msg := &DbBysMarker{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		err = srv.Send(msg)
		if err != nil {
			return err
		}
		rowsCount++
	}
	trailer := metadata.Pairs("rows", strconv.Itoa(rowsCount))
	srv.SetTrailer(trailer)
	return nil
}
//Query impl pcrud QueryBatch
func (*PcrudRpcDbBysMarkerServer) QueryBatch(req *crud.PrivilegeParam, srv PrpcDbBysMarker_QueryBatchServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)
	rows, err := crud.DbSelectPrivilege("DbBysMarker", "OrgRID", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	if req.QueryCondition.Batch < 1 {
		req.QueryCondition.Batch = 1
	}
	if req.QueryCondition.Batch > 10000 {
		req.QueryCondition.Batch = 10000
	}

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbBysMarker{})

	var batchNo uint32 = 0
	var rowsOffset uint32 = 0
	var batchCount uint32 = 0
	list := &DbBysMarkerList{}
	for rows.Next() {
		msg := &DbBysMarker{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		list.Rows = append(list.Rows, msg)
		batchCount++
		rowsOffset++

		if batchCount >= req.QueryCondition.Batch {
			err = srv.Send(list)
			if err != nil {
				return err
			}
			batchNo++
			list = &DbBysMarkerList{
				BatchNo:    batchNo,
				RowsOffset: rowsOffset}
			batchCount = 0
		}
	}
	if batchCount > 0 {
		err = srv.Send(list)
		if err != nil {
			return err
		}
	}
	trailer := metadata.Pairs("rows", strconv.FormatUint(uint64(rowsOffset), 10))
	srv.SetTrailer(trailer)
	return nil
}
// CrudRpcDbControllerOnlineHistoryServer impl crud Service
type CrudRpcDbControllerOnlineHistoryServer struct {
}

//Insert impl crud insert
func (*CrudRpcDbControllerOnlineHistoryServer) Insert(ctx context.Context, req *DbControllerOnlineHistory) (r *crud.DMLResult, err error) {
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	r, err = req.Insert(db)
	if err == nil {
		sys, userRid, sid := pgxdb.GetYkitInfoFromMetadata(ctx)
		ipinfo := goutil.GetIpinfoFromMeta(ctx)
		userOrgrid := goutil.GetUserOrgRIDFromMeta(ctx)
		goutil.NatsCdc(sys, req.OrgRID, sid, "DbControllerOnlineHistory.Insert", req, nil)
		crudhook.CrudAfterHook(sys, userRid, userOrgrid, "DbControllerOnlineHistory.Insert", req, nil, ipinfo)
	}
	return
}

//Query impl crud Query
func (*CrudRpcDbControllerOnlineHistoryServer) Query(req *crud.QueryParam, srv RpcDbControllerOnlineHistory_QueryServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)

	rows, err := crud.DbSelectQuery("DbControllerOnlineHistory", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbControllerOnlineHistory{})
	rowsCount := 0
	for rows.Next() {
		msg := &DbControllerOnlineHistory{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		err = srv.Send(msg)
		if err != nil {
			return err
		}
		rowsCount++
	}
	trailer := metadata.Pairs("rows", strconv.Itoa(rowsCount))
	srv.SetTrailer(trailer)
	return nil
}
//Query impl crud QueryBatch
func (*CrudRpcDbControllerOnlineHistoryServer) QueryBatch(req *crud.QueryParam, srv RpcDbControllerOnlineHistory_QueryBatchServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)

	rows, err := crud.DbSelectQuery("DbControllerOnlineHistory", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	if req.Batch < 1 {
		req.Batch = 1
	}
	if req.Batch > 10000 {
		req.Batch = 10000
	}

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbControllerOnlineHistory{})
	var batchNo uint32 = 0
	var rowsOffset uint32 = 0
	var batchCount uint32 = 0
	list := &DbControllerOnlineHistoryList{}
	for rows.Next() {
		msg := &DbControllerOnlineHistory{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		list.Rows = append(list.Rows, msg)
		batchCount++
		rowsOffset++

		if batchCount >= req.Batch {
			err = srv.Send(list)
			if err != nil {
				return err
			}
			batchNo++
			list = &DbControllerOnlineHistoryList{
				BatchNo:    batchNo,
				RowsOffset: rowsOffset}
			batchCount = 0
		}
	}
	if batchCount > 0 {
		err = srv.Send(list)
		if err != nil {
			return err
		}
	}
	trailer := metadata.Pairs("rows", strconv.FormatUint(uint64(rowsOffset), 10))
	srv.SetTrailer(trailer)
	return nil
}

// PcrudRpcDbControllerOnlineHistoryServer impl pcrud Service
type PcrudRpcDbControllerOnlineHistoryServer struct {
}

//Insert impl pcrud Insert
func (*PcrudRpcDbControllerOnlineHistoryServer) Insert(ctx context.Context, req *DbControllerOnlineHistory) (*crud.DMLResult, error) {
	var crudServer *CrudRpcDbControllerOnlineHistoryServer
	_, err := crud.GrpcCheckHasPermission(ctx, "db", "DbControllerOnlineHistory.Insert")
	if err != nil {
		return nil, err
	}
	err = crud.GrpcCheckHasOrgridPrivilege(ctx, req.OrgRID)
	if err != nil {
		return nil, err
	}
	return crudServer.Insert(ctx, req)
}

//Query impl pcrud Query
func (*PcrudRpcDbControllerOnlineHistoryServer) Query(req *crud.PrivilegeParam, srv PrpcDbControllerOnlineHistory_QueryServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)
	rows, err := crud.DbSelectPrivilege("DbControllerOnlineHistory", "OrgRID", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbControllerOnlineHistory{})
	rowsCount := 0
	for rows.Next() {
		msg := &DbControllerOnlineHistory{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		err = srv.Send(msg)
		if err != nil {
			return err
		}
		rowsCount++
	}
	trailer := metadata.Pairs("rows", strconv.Itoa(rowsCount))
	srv.SetTrailer(trailer)
	return nil
}
//Query impl pcrud QueryBatch
func (*PcrudRpcDbControllerOnlineHistoryServer) QueryBatch(req *crud.PrivilegeParam, srv PrpcDbControllerOnlineHistory_QueryBatchServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)
	rows, err := crud.DbSelectPrivilege("DbControllerOnlineHistory", "OrgRID", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	if req.QueryCondition.Batch < 1 {
		req.QueryCondition.Batch = 1
	}
	if req.QueryCondition.Batch > 10000 {
		req.QueryCondition.Batch = 10000
	}

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbControllerOnlineHistory{})

	var batchNo uint32 = 0
	var rowsOffset uint32 = 0
	var batchCount uint32 = 0
	list := &DbControllerOnlineHistoryList{}
	for rows.Next() {
		msg := &DbControllerOnlineHistory{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		list.Rows = append(list.Rows, msg)
		batchCount++
		rowsOffset++

		if batchCount >= req.QueryCondition.Batch {
			err = srv.Send(list)
			if err != nil {
				return err
			}
			batchNo++
			list = &DbControllerOnlineHistoryList{
				BatchNo:    batchNo,
				RowsOffset: rowsOffset}
			batchCount = 0
		}
	}
	if batchCount > 0 {
		err = srv.Send(list)
		if err != nil {
			return err
		}
	}
	trailer := metadata.Pairs("rows", strconv.FormatUint(uint64(rowsOffset), 10))
	srv.SetTrailer(trailer)
	return nil
}
// CrudRpcDbMediaInfoServer impl crud Service
type CrudRpcDbMediaInfoServer struct {
}

//Insert impl crud insert
func (*CrudRpcDbMediaInfoServer) Insert(ctx context.Context, req *DbMediaInfo) (r *crud.DMLResult, err error) {
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	r, err = req.Insert(db)
	if err == nil {
		sys, userRid, sid := pgxdb.GetYkitInfoFromMetadata(ctx)
		ipinfo := goutil.GetIpinfoFromMeta(ctx)
		userOrgrid := goutil.GetUserOrgRIDFromMeta(ctx)
		goutil.NatsCdc(sys, req.OrgRID, sid, "DbMediaInfo.Insert", req, nil)
		crudhook.CrudAfterHook(sys, userRid, userOrgrid, "DbMediaInfo.Insert", req, nil, ipinfo)
	}
	return
}

//Update impl crud update
func (*CrudRpcDbMediaInfoServer) Update(ctx context.Context, req *DbMediaInfo) (r *crud.DMLResult, err error) {
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	r, err = req.Update(db)
	if err == nil {
		sys, userRid, sid := pgxdb.GetYkitInfoFromMetadata(ctx)
		ipinfo := goutil.GetIpinfoFromMeta(ctx)
		userOrgrid := goutil.GetUserOrgRIDFromMeta(ctx)
		goutil.NatsCdc(sys, req.OrgRID, sid, "DbMediaInfo.Update", req, nil)
		crudhook.CrudAfterHook(sys, userRid, userOrgrid, "DbMediaInfo.Update", req, nil, ipinfo)
	}
	return
}

//PartialUpdate impl crud PartialUpdate
func (*CrudRpcDbMediaInfoServer) PartialUpdate(ctx context.Context, req *DbMediaInfo) (r *crud.DMLResult, err error) {
	param, err := crud.GetDMLParamFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	r, err = req.PartialUpdate(param.KeyColumn, db)
	if err == nil {
		sys, userRid, sid := pgxdb.GetYkitInfoFromMetadata(ctx)
		ipinfo := goutil.GetIpinfoFromMeta(ctx)
		userOrgrid := goutil.GetUserOrgRIDFromMeta(ctx)
		goutil.NatsCdc(sys, req.OrgRID, sid, "DbMediaInfo.PartialUpdate", req, param)
		crudhook.CrudAfterHook(sys, userRid, userOrgrid, "DbMediaInfo.PartialUpdate", req, param, ipinfo)
	}
	return
}

//Delete impl crud Delete
func (*CrudRpcDbMediaInfoServer) Delete(ctx context.Context, req *DbMediaInfo) (r *crud.DMLResult, err error) {
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	r, err = req.Delete(db)
	if err == nil {
		sys, userRid, sid := pgxdb.GetYkitInfoFromMetadata(ctx)
		ipinfo := goutil.GetIpinfoFromMeta(ctx)
		userOrgrid := goutil.GetUserOrgRIDFromMeta(ctx)
		goutil.NatsCdc(sys, req.OrgRID, sid, "DbMediaInfo.Delete", req, nil)
		crudhook.CrudAfterHook(sys, userRid, userOrgrid, "DbMediaInfo.Delete", req, nil, ipinfo)
	}
	return
}

//SelectOne impl crud SelectOne
func (*CrudRpcDbMediaInfoServer) SelectOne(ctx context.Context, dmlParam *crud.DMLParam) (*DbMediaInfo, error) {
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	if len(dmlParam.KeyValue) != 1 {
		return nil, rpc.EBadProtoParam
	}
	msg := &DbMediaInfo{RID: dmlParam.KeyValue[0]}

	selectRow, err := msg.SelectOne(dmlParam.ResultColumn, db)
	if err != nil {
		return nil, err
	}
	if selectRow == 0 {
		msg.Reset()
	}
	return msg, nil
}

//SelectMany impl crud SelectMany
func (*CrudRpcDbMediaInfoServer) SelectMany(dmlParam *crud.DMLParam, srv RpcDbMediaInfo_SelectManyServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)

	msg := &DbMediaInfo{}

	rows, err := msg.SelectByRids(dmlParam.ResultColumn, dmlParam.KeyValue, db)
	if err != nil {
		return err
	}
	defer rows.Close()
	rowsName := crud.RowsColNames2GoFieldNames(rows, msg)
	rowsCount := 0
	for rows.Next() {
		msg := &DbMediaInfo{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		err = srv.Send(msg)
		if err != nil {
			return err
		}
		rowsCount++
	}
	trailer := metadata.Pairs("rows", strconv.Itoa(rowsCount))
	srv.SetTrailer(trailer)
	return nil
}

//Query impl crud Query
func (*CrudRpcDbMediaInfoServer) Query(req *crud.QueryParam, srv RpcDbMediaInfo_QueryServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)

	rows, err := crud.DbSelectQuery("DbMediaInfo", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbMediaInfo{})
	rowsCount := 0
	for rows.Next() {
		msg := &DbMediaInfo{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		err = srv.Send(msg)
		if err != nil {
			return err
		}
		rowsCount++
	}
	trailer := metadata.Pairs("rows", strconv.Itoa(rowsCount))
	srv.SetTrailer(trailer)
	return nil
}
//Query impl crud QueryBatch
func (*CrudRpcDbMediaInfoServer) QueryBatch(req *crud.QueryParam, srv RpcDbMediaInfo_QueryBatchServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)

	rows, err := crud.DbSelectQuery("DbMediaInfo", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	if req.Batch < 1 {
		req.Batch = 1
	}
	if req.Batch > 10000 {
		req.Batch = 10000
	}

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbMediaInfo{})
	var batchNo uint32 = 0
	var rowsOffset uint32 = 0
	var batchCount uint32 = 0
	list := &DbMediaInfoList{}
	for rows.Next() {
		msg := &DbMediaInfo{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		list.Rows = append(list.Rows, msg)
		batchCount++
		rowsOffset++

		if batchCount >= req.Batch {
			err = srv.Send(list)
			if err != nil {
				return err
			}
			batchNo++
			list = &DbMediaInfoList{
				BatchNo:    batchNo,
				RowsOffset: rowsOffset}
			batchCount = 0
		}
	}
	if batchCount > 0 {
		err = srv.Send(list)
		if err != nil {
			return err
		}
	}
	trailer := metadata.Pairs("rows", strconv.FormatUint(uint64(rowsOffset), 10))
	srv.SetTrailer(trailer)
	return nil
}

// PcrudRpcDbMediaInfoServer impl pcrud Service
type PcrudRpcDbMediaInfoServer struct {
}

//Insert impl pcrud Insert
func (*PcrudRpcDbMediaInfoServer) Insert(ctx context.Context, req *DbMediaInfo) (*crud.DMLResult, error) {
	var crudServer *CrudRpcDbMediaInfoServer
	_, err := crud.GrpcCheckHasPermission(ctx, "db", "DbMediaInfo.Insert")
	if err != nil {
		return nil, err
	}
	err = crud.GrpcCheckHasOrgridPrivilege(ctx, req.OrgRID)
	if err != nil {
		return nil, err
	}
	return crudServer.Insert(ctx, req)
}

//Update impl pcrud Update
func (*PcrudRpcDbMediaInfoServer) Update(ctx context.Context, req *DbMediaInfo) (*crud.DMLResult, error) {
	var crudServer *CrudRpcDbMediaInfoServer
	_, err := crud.GrpcCheckHasPermission(ctx, "db", "DbMediaInfo.Update")
	if err != nil {
		return nil, err
	}
	err = crud.GrpcCheckHasOrgridPrivilege(ctx, req.OrgRID)
	if err != nil {
		return nil, err
	}
	return crudServer.Update(ctx, req)
}

//PartialUpdate impl pcrud PartialUpdate
func (*PcrudRpcDbMediaInfoServer) PartialUpdate(ctx context.Context, req *DbMediaInfo) (*crud.DMLResult, error) {
	var crudServer *CrudRpcDbMediaInfoServer
	_, err := crud.GrpcCheckHasPermission(ctx, "db", "DbMediaInfo.Update")
	if err != nil {
		return nil, err
	}
	err = crud.GrpcCheckHasOrgridPrivilege(ctx, req.OrgRID)
	if err != nil {
		return nil, err
	}
	return crudServer.PartialUpdate(ctx, req)
}

//Delete impl pcrud Delete
func (*PcrudRpcDbMediaInfoServer) Delete(ctx context.Context, req *DbMediaInfo) (*crud.DMLResult, error) {
	var crudServer *CrudRpcDbMediaInfoServer
	_, err := crud.GrpcCheckHasPermission(ctx, "db", "DbMediaInfo.Delete")
	if err != nil {
		return nil, err
	}
	err = crud.GrpcCheckHasOrgridPrivilege(ctx, req.OrgRID)
	if err != nil {
		return nil, err
	}
	return crudServer.Delete(ctx, req)
}

//SelectOne impl pcrud SelectOne
func (*PcrudRpcDbMediaInfoServer) SelectOne(ctx context.Context, dmlParam *crud.DMLParam) (*DbMediaInfo, error) {
	var crudServer *CrudRpcDbMediaInfoServer
	return crudServer.SelectOne(ctx, dmlParam)
}

//SelectMany impl pcrud SelectMany
func (*PcrudRpcDbMediaInfoServer) SelectMany(dmlParam *crud.DMLParam, srv PrpcDbMediaInfo_SelectManyServer) error {
	var crudServer *CrudRpcDbMediaInfoServer
	return crudServer.SelectMany(dmlParam, srv)
}

//Query impl pcrud Query
func (*PcrudRpcDbMediaInfoServer) Query(req *crud.PrivilegeParam, srv PrpcDbMediaInfo_QueryServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)
	rows, err := crud.DbSelectPrivilege("DbMediaInfo", "OrgRID", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbMediaInfo{})
	rowsCount := 0
	for rows.Next() {
		msg := &DbMediaInfo{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		err = srv.Send(msg)
		if err != nil {
			return err
		}
		rowsCount++
	}
	trailer := metadata.Pairs("rows", strconv.Itoa(rowsCount))
	srv.SetTrailer(trailer)
	return nil
}
//Query impl pcrud QueryBatch
func (*PcrudRpcDbMediaInfoServer) QueryBatch(req *crud.PrivilegeParam, srv PrpcDbMediaInfo_QueryBatchServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)
	rows, err := crud.DbSelectPrivilege("DbMediaInfo", "OrgRID", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	if req.QueryCondition.Batch < 1 {
		req.QueryCondition.Batch = 1
	}
	if req.QueryCondition.Batch > 10000 {
		req.QueryCondition.Batch = 10000
	}

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbMediaInfo{})

	var batchNo uint32 = 0
	var rowsOffset uint32 = 0
	var batchCount uint32 = 0
	list := &DbMediaInfoList{}
	for rows.Next() {
		msg := &DbMediaInfo{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		list.Rows = append(list.Rows, msg)
		batchCount++
		rowsOffset++

		if batchCount >= req.QueryCondition.Batch {
			err = srv.Send(list)
			if err != nil {
				return err
			}
			batchNo++
			list = &DbMediaInfoList{
				BatchNo:    batchNo,
				RowsOffset: rowsOffset}
			batchCount = 0
		}
	}
	if batchCount > 0 {
		err = srv.Send(list)
		if err != nil {
			return err
		}
	}
	trailer := metadata.Pairs("rows", strconv.FormatUint(uint64(rowsOffset), 10))
	srv.SetTrailer(trailer)
	return nil
}
// CrudRpcDbMarkerHistoryServer impl crud Service
type CrudRpcDbMarkerHistoryServer struct {
}

//Insert impl crud insert
func (*CrudRpcDbMarkerHistoryServer) Insert(ctx context.Context, req *DbMarkerHistory) (r *crud.DMLResult, err error) {
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	r, err = req.Insert(db)
	if err == nil {
		sys, userRid, sid := pgxdb.GetYkitInfoFromMetadata(ctx)
		ipinfo := goutil.GetIpinfoFromMeta(ctx)
		userOrgrid := goutil.GetUserOrgRIDFromMeta(ctx)
		goutil.NatsCdc(sys, req.OrgRID, sid, "DbMarkerHistory.Insert", req, nil)
		crudhook.CrudAfterHook(sys, userRid, userOrgrid, "DbMarkerHistory.Insert", req, nil, ipinfo)
	}
	return
}

//Query impl crud Query
func (*CrudRpcDbMarkerHistoryServer) Query(req *crud.QueryParam, srv RpcDbMarkerHistory_QueryServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)

	rows, err := crud.DbSelectQuery("DbMarkerHistory", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbMarkerHistory{})
	rowsCount := 0
	for rows.Next() {
		msg := &DbMarkerHistory{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		err = srv.Send(msg)
		if err != nil {
			return err
		}
		rowsCount++
	}
	trailer := metadata.Pairs("rows", strconv.Itoa(rowsCount))
	srv.SetTrailer(trailer)
	return nil
}
//Query impl crud QueryBatch
func (*CrudRpcDbMarkerHistoryServer) QueryBatch(req *crud.QueryParam, srv RpcDbMarkerHistory_QueryBatchServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)

	rows, err := crud.DbSelectQuery("DbMarkerHistory", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	if req.Batch < 1 {
		req.Batch = 1
	}
	if req.Batch > 10000 {
		req.Batch = 10000
	}

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbMarkerHistory{})
	var batchNo uint32 = 0
	var rowsOffset uint32 = 0
	var batchCount uint32 = 0
	list := &DbMarkerHistoryList{}
	for rows.Next() {
		msg := &DbMarkerHistory{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		list.Rows = append(list.Rows, msg)
		batchCount++
		rowsOffset++

		if batchCount >= req.Batch {
			err = srv.Send(list)
			if err != nil {
				return err
			}
			batchNo++
			list = &DbMarkerHistoryList{
				BatchNo:    batchNo,
				RowsOffset: rowsOffset}
			batchCount = 0
		}
	}
	if batchCount > 0 {
		err = srv.Send(list)
		if err != nil {
			return err
		}
	}
	trailer := metadata.Pairs("rows", strconv.FormatUint(uint64(rowsOffset), 10))
	srv.SetTrailer(trailer)
	return nil
}

// PcrudRpcDbMarkerHistoryServer impl pcrud Service
type PcrudRpcDbMarkerHistoryServer struct {
}

//Insert impl pcrud Insert
func (*PcrudRpcDbMarkerHistoryServer) Insert(ctx context.Context, req *DbMarkerHistory) (*crud.DMLResult, error) {
	var crudServer *CrudRpcDbMarkerHistoryServer
	_, err := crud.GrpcCheckHasPermission(ctx, "db", "DbMarkerHistory.Insert")
	if err != nil {
		return nil, err
	}
	err = crud.GrpcCheckHasOrgridPrivilege(ctx, req.OrgRID)
	if err != nil {
		return nil, err
	}
	return crudServer.Insert(ctx, req)
}

//Query impl pcrud Query
func (*PcrudRpcDbMarkerHistoryServer) Query(req *crud.PrivilegeParam, srv PrpcDbMarkerHistory_QueryServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)
	rows, err := crud.DbSelectPrivilege("DbMarkerHistory", "OrgRID", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbMarkerHistory{})
	rowsCount := 0
	for rows.Next() {
		msg := &DbMarkerHistory{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		err = srv.Send(msg)
		if err != nil {
			return err
		}
		rowsCount++
	}
	trailer := metadata.Pairs("rows", strconv.Itoa(rowsCount))
	srv.SetTrailer(trailer)
	return nil
}
//Query impl pcrud QueryBatch
func (*PcrudRpcDbMarkerHistoryServer) QueryBatch(req *crud.PrivilegeParam, srv PrpcDbMarkerHistory_QueryBatchServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)
	rows, err := crud.DbSelectPrivilege("DbMarkerHistory", "OrgRID", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	if req.QueryCondition.Batch < 1 {
		req.QueryCondition.Batch = 1
	}
	if req.QueryCondition.Batch > 10000 {
		req.QueryCondition.Batch = 10000
	}

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbMarkerHistory{})

	var batchNo uint32 = 0
	var rowsOffset uint32 = 0
	var batchCount uint32 = 0
	list := &DbMarkerHistoryList{}
	for rows.Next() {
		msg := &DbMarkerHistory{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		list.Rows = append(list.Rows, msg)
		batchCount++
		rowsOffset++

		if batchCount >= req.QueryCondition.Batch {
			err = srv.Send(list)
			if err != nil {
				return err
			}
			batchNo++
			list = &DbMarkerHistoryList{
				BatchNo:    batchNo,
				RowsOffset: rowsOffset}
			batchCount = 0
		}
	}
	if batchCount > 0 {
		err = srv.Send(list)
		if err != nil {
			return err
		}
	}
	trailer := metadata.Pairs("rows", strconv.FormatUint(uint64(rowsOffset), 10))
	srv.SetTrailer(trailer)
	return nil
}
// CrudRpcDbMarkerPatrolHistoryServer impl crud Service
type CrudRpcDbMarkerPatrolHistoryServer struct {
}

//Insert impl crud insert
func (*CrudRpcDbMarkerPatrolHistoryServer) Insert(ctx context.Context, req *DbMarkerPatrolHistory) (r *crud.DMLResult, err error) {
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	r, err = req.Insert(db)
	if err == nil {
		sys, userRid, sid := pgxdb.GetYkitInfoFromMetadata(ctx)
		ipinfo := goutil.GetIpinfoFromMeta(ctx)
		userOrgrid := goutil.GetUserOrgRIDFromMeta(ctx)
		goutil.NatsCdc(sys, req.OrgRID, sid, "DbMarkerPatrolHistory.Insert", req, nil)
		crudhook.CrudAfterHook(sys, userRid, userOrgrid, "DbMarkerPatrolHistory.Insert", req, nil, ipinfo)
	}
	return
}

//Query impl crud Query
func (*CrudRpcDbMarkerPatrolHistoryServer) Query(req *crud.QueryParam, srv RpcDbMarkerPatrolHistory_QueryServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)

	rows, err := crud.DbSelectQuery("DbMarkerPatrolHistory", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbMarkerPatrolHistory{})
	rowsCount := 0
	for rows.Next() {
		msg := &DbMarkerPatrolHistory{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		err = srv.Send(msg)
		if err != nil {
			return err
		}
		rowsCount++
	}
	trailer := metadata.Pairs("rows", strconv.Itoa(rowsCount))
	srv.SetTrailer(trailer)
	return nil
}
//Query impl crud QueryBatch
func (*CrudRpcDbMarkerPatrolHistoryServer) QueryBatch(req *crud.QueryParam, srv RpcDbMarkerPatrolHistory_QueryBatchServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)

	rows, err := crud.DbSelectQuery("DbMarkerPatrolHistory", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	if req.Batch < 1 {
		req.Batch = 1
	}
	if req.Batch > 10000 {
		req.Batch = 10000
	}

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbMarkerPatrolHistory{})
	var batchNo uint32 = 0
	var rowsOffset uint32 = 0
	var batchCount uint32 = 0
	list := &DbMarkerPatrolHistoryList{}
	for rows.Next() {
		msg := &DbMarkerPatrolHistory{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		list.Rows = append(list.Rows, msg)
		batchCount++
		rowsOffset++

		if batchCount >= req.Batch {
			err = srv.Send(list)
			if err != nil {
				return err
			}
			batchNo++
			list = &DbMarkerPatrolHistoryList{
				BatchNo:    batchNo,
				RowsOffset: rowsOffset}
			batchCount = 0
		}
	}
	if batchCount > 0 {
		err = srv.Send(list)
		if err != nil {
			return err
		}
	}
	trailer := metadata.Pairs("rows", strconv.FormatUint(uint64(rowsOffset), 10))
	srv.SetTrailer(trailer)
	return nil
}

// PcrudRpcDbMarkerPatrolHistoryServer impl pcrud Service
type PcrudRpcDbMarkerPatrolHistoryServer struct {
}

//Insert impl pcrud Insert
func (*PcrudRpcDbMarkerPatrolHistoryServer) Insert(ctx context.Context, req *DbMarkerPatrolHistory) (*crud.DMLResult, error) {
	var crudServer *CrudRpcDbMarkerPatrolHistoryServer
	_, err := crud.GrpcCheckHasPermission(ctx, "db", "DbMarkerPatrolHistory.Insert")
	if err != nil {
		return nil, err
	}
	err = crud.GrpcCheckHasOrgridPrivilege(ctx, req.OrgRID)
	if err != nil {
		return nil, err
	}
	return crudServer.Insert(ctx, req)
}

//Query impl pcrud Query
func (*PcrudRpcDbMarkerPatrolHistoryServer) Query(req *crud.PrivilegeParam, srv PrpcDbMarkerPatrolHistory_QueryServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)
	rows, err := crud.DbSelectPrivilege("DbMarkerPatrolHistory", "OrgRID", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbMarkerPatrolHistory{})
	rowsCount := 0
	for rows.Next() {
		msg := &DbMarkerPatrolHistory{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		err = srv.Send(msg)
		if err != nil {
			return err
		}
		rowsCount++
	}
	trailer := metadata.Pairs("rows", strconv.Itoa(rowsCount))
	srv.SetTrailer(trailer)
	return nil
}
//Query impl pcrud QueryBatch
func (*PcrudRpcDbMarkerPatrolHistoryServer) QueryBatch(req *crud.PrivilegeParam, srv PrpcDbMarkerPatrolHistory_QueryBatchServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)
	rows, err := crud.DbSelectPrivilege("DbMarkerPatrolHistory", "OrgRID", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	if req.QueryCondition.Batch < 1 {
		req.QueryCondition.Batch = 1
	}
	if req.QueryCondition.Batch > 10000 {
		req.QueryCondition.Batch = 10000
	}

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbMarkerPatrolHistory{})

	var batchNo uint32 = 0
	var rowsOffset uint32 = 0
	var batchCount uint32 = 0
	list := &DbMarkerPatrolHistoryList{}
	for rows.Next() {
		msg := &DbMarkerPatrolHistory{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		list.Rows = append(list.Rows, msg)
		batchCount++
		rowsOffset++

		if batchCount >= req.QueryCondition.Batch {
			err = srv.Send(list)
			if err != nil {
				return err
			}
			batchNo++
			list = &DbMarkerPatrolHistoryList{
				BatchNo:    batchNo,
				RowsOffset: rowsOffset}
			batchCount = 0
		}
	}
	if batchCount > 0 {
		err = srv.Send(list)
		if err != nil {
			return err
		}
	}
	trailer := metadata.Pairs("rows", strconv.FormatUint(uint64(rowsOffset), 10))
	srv.SetTrailer(trailer)
	return nil
}
// CrudRpcDbNFCPatrolLineServer impl crud Service
type CrudRpcDbNFCPatrolLineServer struct {
}

//Insert impl crud insert
func (*CrudRpcDbNFCPatrolLineServer) Insert(ctx context.Context, req *DbNFCPatrolLine) (r *crud.DMLResult, err error) {
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	r, err = req.Insert(db)
	if err == nil {
		sys, userRid, sid := pgxdb.GetYkitInfoFromMetadata(ctx)
		ipinfo := goutil.GetIpinfoFromMeta(ctx)
		userOrgrid := goutil.GetUserOrgRIDFromMeta(ctx)
		goutil.NatsCdc(sys, req.OrgRID, sid, "DbNFCPatrolLine.Insert", req, nil)
		crudhook.CrudAfterHook(sys, userRid, userOrgrid, "DbNFCPatrolLine.Insert", req, nil, ipinfo)
	}
	return
}

//Update impl crud update
func (*CrudRpcDbNFCPatrolLineServer) Update(ctx context.Context, req *DbNFCPatrolLine) (r *crud.DMLResult, err error) {
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	r, err = req.Update(db)
	if err == nil {
		sys, userRid, sid := pgxdb.GetYkitInfoFromMetadata(ctx)
		ipinfo := goutil.GetIpinfoFromMeta(ctx)
		userOrgrid := goutil.GetUserOrgRIDFromMeta(ctx)
		goutil.NatsCdc(sys, req.OrgRID, sid, "DbNFCPatrolLine.Update", req, nil)
		crudhook.CrudAfterHook(sys, userRid, userOrgrid, "DbNFCPatrolLine.Update", req, nil, ipinfo)
	}
	return
}

//PartialUpdate impl crud PartialUpdate
func (*CrudRpcDbNFCPatrolLineServer) PartialUpdate(ctx context.Context, req *DbNFCPatrolLine) (r *crud.DMLResult, err error) {
	param, err := crud.GetDMLParamFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	r, err = req.PartialUpdate(param.KeyColumn, db)
	if err == nil {
		sys, userRid, sid := pgxdb.GetYkitInfoFromMetadata(ctx)
		ipinfo := goutil.GetIpinfoFromMeta(ctx)
		userOrgrid := goutil.GetUserOrgRIDFromMeta(ctx)
		goutil.NatsCdc(sys, req.OrgRID, sid, "DbNFCPatrolLine.PartialUpdate", req, param)
		crudhook.CrudAfterHook(sys, userRid, userOrgrid, "DbNFCPatrolLine.PartialUpdate", req, param, ipinfo)
	}
	return
}

//Delete impl crud Delete
func (*CrudRpcDbNFCPatrolLineServer) Delete(ctx context.Context, req *DbNFCPatrolLine) (r *crud.DMLResult, err error) {
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	r, err = req.Delete(db)
	if err == nil {
		sys, userRid, sid := pgxdb.GetYkitInfoFromMetadata(ctx)
		ipinfo := goutil.GetIpinfoFromMeta(ctx)
		userOrgrid := goutil.GetUserOrgRIDFromMeta(ctx)
		goutil.NatsCdc(sys, req.OrgRID, sid, "DbNFCPatrolLine.Delete", req, nil)
		crudhook.CrudAfterHook(sys, userRid, userOrgrid, "DbNFCPatrolLine.Delete", req, nil, ipinfo)
	}
	return
}

//SelectOne impl crud SelectOne
func (*CrudRpcDbNFCPatrolLineServer) SelectOne(ctx context.Context, dmlParam *crud.DMLParam) (*DbNFCPatrolLine, error) {
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	if len(dmlParam.KeyValue) != 1 {
		return nil, rpc.EBadProtoParam
	}
	msg := &DbNFCPatrolLine{RID: dmlParam.KeyValue[0]}

	selectRow, err := msg.SelectOne(dmlParam.ResultColumn, db)
	if err != nil {
		return nil, err
	}
	if selectRow == 0 {
		msg.Reset()
	}
	return msg, nil
}

//SelectMany impl crud SelectMany
func (*CrudRpcDbNFCPatrolLineServer) SelectMany(dmlParam *crud.DMLParam, srv RpcDbNFCPatrolLine_SelectManyServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)

	msg := &DbNFCPatrolLine{}

	rows, err := msg.SelectByRids(dmlParam.ResultColumn, dmlParam.KeyValue, db)
	if err != nil {
		return err
	}
	defer rows.Close()
	rowsName := crud.RowsColNames2GoFieldNames(rows, msg)
	rowsCount := 0
	for rows.Next() {
		msg := &DbNFCPatrolLine{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		err = srv.Send(msg)
		if err != nil {
			return err
		}
		rowsCount++
	}
	trailer := metadata.Pairs("rows", strconv.Itoa(rowsCount))
	srv.SetTrailer(trailer)
	return nil
}

//Query impl crud Query
func (*CrudRpcDbNFCPatrolLineServer) Query(req *crud.QueryParam, srv RpcDbNFCPatrolLine_QueryServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)

	rows, err := crud.DbSelectQuery("DbNFCPatrolLine", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbNFCPatrolLine{})
	rowsCount := 0
	for rows.Next() {
		msg := &DbNFCPatrolLine{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		err = srv.Send(msg)
		if err != nil {
			return err
		}
		rowsCount++
	}
	trailer := metadata.Pairs("rows", strconv.Itoa(rowsCount))
	srv.SetTrailer(trailer)
	return nil
}
//Query impl crud QueryBatch
func (*CrudRpcDbNFCPatrolLineServer) QueryBatch(req *crud.QueryParam, srv RpcDbNFCPatrolLine_QueryBatchServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)

	rows, err := crud.DbSelectQuery("DbNFCPatrolLine", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	if req.Batch < 1 {
		req.Batch = 1
	}
	if req.Batch > 10000 {
		req.Batch = 10000
	}

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbNFCPatrolLine{})
	var batchNo uint32 = 0
	var rowsOffset uint32 = 0
	var batchCount uint32 = 0
	list := &DbNFCPatrolLineList{}
	for rows.Next() {
		msg := &DbNFCPatrolLine{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		list.Rows = append(list.Rows, msg)
		batchCount++
		rowsOffset++

		if batchCount >= req.Batch {
			err = srv.Send(list)
			if err != nil {
				return err
			}
			batchNo++
			list = &DbNFCPatrolLineList{
				BatchNo:    batchNo,
				RowsOffset: rowsOffset}
			batchCount = 0
		}
	}
	if batchCount > 0 {
		err = srv.Send(list)
		if err != nil {
			return err
		}
	}
	trailer := metadata.Pairs("rows", strconv.FormatUint(uint64(rowsOffset), 10))
	srv.SetTrailer(trailer)
	return nil
}

// PcrudRpcDbNFCPatrolLineServer impl pcrud Service
type PcrudRpcDbNFCPatrolLineServer struct {
}

//Insert impl pcrud Insert
func (*PcrudRpcDbNFCPatrolLineServer) Insert(ctx context.Context, req *DbNFCPatrolLine) (*crud.DMLResult, error) {
	var crudServer *CrudRpcDbNFCPatrolLineServer
	_, err := crud.GrpcCheckHasPermission(ctx, "db", "DbNFCPatrolLine.Insert")
	if err != nil {
		return nil, err
	}
	err = crud.GrpcCheckHasOrgridPrivilege(ctx, req.OrgRID)
	if err != nil {
		return nil, err
	}
	return crudServer.Insert(ctx, req)
}

//Update impl pcrud Update
func (*PcrudRpcDbNFCPatrolLineServer) Update(ctx context.Context, req *DbNFCPatrolLine) (*crud.DMLResult, error) {
	var crudServer *CrudRpcDbNFCPatrolLineServer
	_, err := crud.GrpcCheckHasPermission(ctx, "db", "DbNFCPatrolLine.Update")
	if err != nil {
		return nil, err
	}
	err = crud.GrpcCheckHasOrgridPrivilege(ctx, req.OrgRID)
	if err != nil {
		return nil, err
	}
	return crudServer.Update(ctx, req)
}

//PartialUpdate impl pcrud PartialUpdate
func (*PcrudRpcDbNFCPatrolLineServer) PartialUpdate(ctx context.Context, req *DbNFCPatrolLine) (*crud.DMLResult, error) {
	var crudServer *CrudRpcDbNFCPatrolLineServer
	_, err := crud.GrpcCheckHasPermission(ctx, "db", "DbNFCPatrolLine.Update")
	if err != nil {
		return nil, err
	}
	err = crud.GrpcCheckHasOrgridPrivilege(ctx, req.OrgRID)
	if err != nil {
		return nil, err
	}
	return crudServer.PartialUpdate(ctx, req)
}

//Delete impl pcrud Delete
func (*PcrudRpcDbNFCPatrolLineServer) Delete(ctx context.Context, req *DbNFCPatrolLine) (*crud.DMLResult, error) {
	var crudServer *CrudRpcDbNFCPatrolLineServer
	_, err := crud.GrpcCheckHasPermission(ctx, "db", "DbNFCPatrolLine.Delete")
	if err != nil {
		return nil, err
	}
	err = crud.GrpcCheckHasOrgridPrivilege(ctx, req.OrgRID)
	if err != nil {
		return nil, err
	}
	return crudServer.Delete(ctx, req)
}

//SelectOne impl pcrud SelectOne
func (*PcrudRpcDbNFCPatrolLineServer) SelectOne(ctx context.Context, dmlParam *crud.DMLParam) (*DbNFCPatrolLine, error) {
	var crudServer *CrudRpcDbNFCPatrolLineServer
	return crudServer.SelectOne(ctx, dmlParam)
}

//SelectMany impl pcrud SelectMany
func (*PcrudRpcDbNFCPatrolLineServer) SelectMany(dmlParam *crud.DMLParam, srv PrpcDbNFCPatrolLine_SelectManyServer) error {
	var crudServer *CrudRpcDbNFCPatrolLineServer
	return crudServer.SelectMany(dmlParam, srv)
}

//Query impl pcrud Query
func (*PcrudRpcDbNFCPatrolLineServer) Query(req *crud.PrivilegeParam, srv PrpcDbNFCPatrolLine_QueryServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)
	rows, err := crud.DbSelectPrivilege("DbNFCPatrolLine", "OrgRID", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbNFCPatrolLine{})
	rowsCount := 0
	for rows.Next() {
		msg := &DbNFCPatrolLine{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		err = srv.Send(msg)
		if err != nil {
			return err
		}
		rowsCount++
	}
	trailer := metadata.Pairs("rows", strconv.Itoa(rowsCount))
	srv.SetTrailer(trailer)
	return nil
}
//Query impl pcrud QueryBatch
func (*PcrudRpcDbNFCPatrolLineServer) QueryBatch(req *crud.PrivilegeParam, srv PrpcDbNFCPatrolLine_QueryBatchServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)
	rows, err := crud.DbSelectPrivilege("DbNFCPatrolLine", "OrgRID", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	if req.QueryCondition.Batch < 1 {
		req.QueryCondition.Batch = 1
	}
	if req.QueryCondition.Batch > 10000 {
		req.QueryCondition.Batch = 10000
	}

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbNFCPatrolLine{})

	var batchNo uint32 = 0
	var rowsOffset uint32 = 0
	var batchCount uint32 = 0
	list := &DbNFCPatrolLineList{}
	for rows.Next() {
		msg := &DbNFCPatrolLine{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		list.Rows = append(list.Rows, msg)
		batchCount++
		rowsOffset++

		if batchCount >= req.QueryCondition.Batch {
			err = srv.Send(list)
			if err != nil {
				return err
			}
			batchNo++
			list = &DbNFCPatrolLineList{
				BatchNo:    batchNo,
				RowsOffset: rowsOffset}
			batchCount = 0
		}
	}
	if batchCount > 0 {
		err = srv.Send(list)
		if err != nil {
			return err
		}
	}
	trailer := metadata.Pairs("rows", strconv.FormatUint(uint64(rowsOffset), 10))
	srv.SetTrailer(trailer)
	return nil
}
// CrudRpcDbNFCPatrolLineDetailServer impl crud Service
type CrudRpcDbNFCPatrolLineDetailServer struct {
}

//Insert impl crud insert
func (*CrudRpcDbNFCPatrolLineDetailServer) Insert(ctx context.Context, req *DbNFCPatrolLineDetail) (r *crud.DMLResult, err error) {
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	r, err = req.Insert(db)
	if err == nil {
		sys, userRid, sid := pgxdb.GetYkitInfoFromMetadata(ctx)
		ipinfo := goutil.GetIpinfoFromMeta(ctx)
		userOrgrid := goutil.GetUserOrgRIDFromMeta(ctx)
		goutil.NatsCdc(sys, req.OrgRID, sid, "DbNFCPatrolLineDetail.Insert", req, nil)
		crudhook.CrudAfterHook(sys, userRid, userOrgrid, "DbNFCPatrolLineDetail.Insert", req, nil, ipinfo)
	}
	return
}

//Update impl crud update
func (*CrudRpcDbNFCPatrolLineDetailServer) Update(ctx context.Context, req *DbNFCPatrolLineDetail) (r *crud.DMLResult, err error) {
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	r, err = req.Update(db)
	if err == nil {
		sys, userRid, sid := pgxdb.GetYkitInfoFromMetadata(ctx)
		ipinfo := goutil.GetIpinfoFromMeta(ctx)
		userOrgrid := goutil.GetUserOrgRIDFromMeta(ctx)
		goutil.NatsCdc(sys, req.OrgRID, sid, "DbNFCPatrolLineDetail.Update", req, nil)
		crudhook.CrudAfterHook(sys, userRid, userOrgrid, "DbNFCPatrolLineDetail.Update", req, nil, ipinfo)
	}
	return
}

//PartialUpdate impl crud PartialUpdate
func (*CrudRpcDbNFCPatrolLineDetailServer) PartialUpdate(ctx context.Context, req *DbNFCPatrolLineDetail) (r *crud.DMLResult, err error) {
	param, err := crud.GetDMLParamFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	r, err = req.PartialUpdate(param.KeyColumn, db)
	if err == nil {
		sys, userRid, sid := pgxdb.GetYkitInfoFromMetadata(ctx)
		ipinfo := goutil.GetIpinfoFromMeta(ctx)
		userOrgrid := goutil.GetUserOrgRIDFromMeta(ctx)
		goutil.NatsCdc(sys, req.OrgRID, sid, "DbNFCPatrolLineDetail.PartialUpdate", req, param)
		crudhook.CrudAfterHook(sys, userRid, userOrgrid, "DbNFCPatrolLineDetail.PartialUpdate", req, param, ipinfo)
	}
	return
}

//Delete impl crud Delete
func (*CrudRpcDbNFCPatrolLineDetailServer) Delete(ctx context.Context, req *DbNFCPatrolLineDetail) (r *crud.DMLResult, err error) {
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	r, err = req.Delete(db)
	if err == nil {
		sys, userRid, sid := pgxdb.GetYkitInfoFromMetadata(ctx)
		ipinfo := goutil.GetIpinfoFromMeta(ctx)
		userOrgrid := goutil.GetUserOrgRIDFromMeta(ctx)
		goutil.NatsCdc(sys, req.OrgRID, sid, "DbNFCPatrolLineDetail.Delete", req, nil)
		crudhook.CrudAfterHook(sys, userRid, userOrgrid, "DbNFCPatrolLineDetail.Delete", req, nil, ipinfo)
	}
	return
}

//SelectOne impl crud SelectOne
func (*CrudRpcDbNFCPatrolLineDetailServer) SelectOne(ctx context.Context, dmlParam *crud.DMLParam) (*DbNFCPatrolLineDetail, error) {
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	if len(dmlParam.KeyValue) != 1 {
		return nil, rpc.EBadProtoParam
	}
	msg := &DbNFCPatrolLineDetail{RID: dmlParam.KeyValue[0]}

	selectRow, err := msg.SelectOne(dmlParam.ResultColumn, db)
	if err != nil {
		return nil, err
	}
	if selectRow == 0 {
		msg.Reset()
	}
	return msg, nil
}

//SelectMany impl crud SelectMany
func (*CrudRpcDbNFCPatrolLineDetailServer) SelectMany(dmlParam *crud.DMLParam, srv RpcDbNFCPatrolLineDetail_SelectManyServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)

	msg := &DbNFCPatrolLineDetail{}

	rows, err := msg.SelectByRids(dmlParam.ResultColumn, dmlParam.KeyValue, db)
	if err != nil {
		return err
	}
	defer rows.Close()
	rowsName := crud.RowsColNames2GoFieldNames(rows, msg)
	rowsCount := 0
	for rows.Next() {
		msg := &DbNFCPatrolLineDetail{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		err = srv.Send(msg)
		if err != nil {
			return err
		}
		rowsCount++
	}
	trailer := metadata.Pairs("rows", strconv.Itoa(rowsCount))
	srv.SetTrailer(trailer)
	return nil
}

//Query impl crud Query
func (*CrudRpcDbNFCPatrolLineDetailServer) Query(req *crud.QueryParam, srv RpcDbNFCPatrolLineDetail_QueryServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)

	rows, err := crud.DbSelectQuery("DbNFCPatrolLineDetail", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbNFCPatrolLineDetail{})
	rowsCount := 0
	for rows.Next() {
		msg := &DbNFCPatrolLineDetail{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		err = srv.Send(msg)
		if err != nil {
			return err
		}
		rowsCount++
	}
	trailer := metadata.Pairs("rows", strconv.Itoa(rowsCount))
	srv.SetTrailer(trailer)
	return nil
}
//Query impl crud QueryBatch
func (*CrudRpcDbNFCPatrolLineDetailServer) QueryBatch(req *crud.QueryParam, srv RpcDbNFCPatrolLineDetail_QueryBatchServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)

	rows, err := crud.DbSelectQuery("DbNFCPatrolLineDetail", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	if req.Batch < 1 {
		req.Batch = 1
	}
	if req.Batch > 10000 {
		req.Batch = 10000
	}

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbNFCPatrolLineDetail{})
	var batchNo uint32 = 0
	var rowsOffset uint32 = 0
	var batchCount uint32 = 0
	list := &DbNFCPatrolLineDetailList{}
	for rows.Next() {
		msg := &DbNFCPatrolLineDetail{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		list.Rows = append(list.Rows, msg)
		batchCount++
		rowsOffset++

		if batchCount >= req.Batch {
			err = srv.Send(list)
			if err != nil {
				return err
			}
			batchNo++
			list = &DbNFCPatrolLineDetailList{
				BatchNo:    batchNo,
				RowsOffset: rowsOffset}
			batchCount = 0
		}
	}
	if batchCount > 0 {
		err = srv.Send(list)
		if err != nil {
			return err
		}
	}
	trailer := metadata.Pairs("rows", strconv.FormatUint(uint64(rowsOffset), 10))
	srv.SetTrailer(trailer)
	return nil
}

// PcrudRpcDbNFCPatrolLineDetailServer impl pcrud Service
type PcrudRpcDbNFCPatrolLineDetailServer struct {
}

//Insert impl pcrud Insert
func (*PcrudRpcDbNFCPatrolLineDetailServer) Insert(ctx context.Context, req *DbNFCPatrolLineDetail) (*crud.DMLResult, error) {
	var crudServer *CrudRpcDbNFCPatrolLineDetailServer
	_, err := crud.GrpcCheckHasPermission(ctx, "db", "DbNFCPatrolLineDetail.Insert")
	if err != nil {
		return nil, err
	}
	err = crud.GrpcCheckHasOrgridPrivilege(ctx, req.OrgRID)
	if err != nil {
		return nil, err
	}
	return crudServer.Insert(ctx, req)
}

//Update impl pcrud Update
func (*PcrudRpcDbNFCPatrolLineDetailServer) Update(ctx context.Context, req *DbNFCPatrolLineDetail) (*crud.DMLResult, error) {
	var crudServer *CrudRpcDbNFCPatrolLineDetailServer
	_, err := crud.GrpcCheckHasPermission(ctx, "db", "DbNFCPatrolLineDetail.Update")
	if err != nil {
		return nil, err
	}
	err = crud.GrpcCheckHasOrgridPrivilege(ctx, req.OrgRID)
	if err != nil {
		return nil, err
	}
	return crudServer.Update(ctx, req)
}

//PartialUpdate impl pcrud PartialUpdate
func (*PcrudRpcDbNFCPatrolLineDetailServer) PartialUpdate(ctx context.Context, req *DbNFCPatrolLineDetail) (*crud.DMLResult, error) {
	var crudServer *CrudRpcDbNFCPatrolLineDetailServer
	_, err := crud.GrpcCheckHasPermission(ctx, "db", "DbNFCPatrolLineDetail.Update")
	if err != nil {
		return nil, err
	}
	err = crud.GrpcCheckHasOrgridPrivilege(ctx, req.OrgRID)
	if err != nil {
		return nil, err
	}
	return crudServer.PartialUpdate(ctx, req)
}

//Delete impl pcrud Delete
func (*PcrudRpcDbNFCPatrolLineDetailServer) Delete(ctx context.Context, req *DbNFCPatrolLineDetail) (*crud.DMLResult, error) {
	var crudServer *CrudRpcDbNFCPatrolLineDetailServer
	_, err := crud.GrpcCheckHasPermission(ctx, "db", "DbNFCPatrolLineDetail.Delete")
	if err != nil {
		return nil, err
	}
	err = crud.GrpcCheckHasOrgridPrivilege(ctx, req.OrgRID)
	if err != nil {
		return nil, err
	}
	return crudServer.Delete(ctx, req)
}

//SelectOne impl pcrud SelectOne
func (*PcrudRpcDbNFCPatrolLineDetailServer) SelectOne(ctx context.Context, dmlParam *crud.DMLParam) (*DbNFCPatrolLineDetail, error) {
	var crudServer *CrudRpcDbNFCPatrolLineDetailServer
	return crudServer.SelectOne(ctx, dmlParam)
}

//SelectMany impl pcrud SelectMany
func (*PcrudRpcDbNFCPatrolLineDetailServer) SelectMany(dmlParam *crud.DMLParam, srv PrpcDbNFCPatrolLineDetail_SelectManyServer) error {
	var crudServer *CrudRpcDbNFCPatrolLineDetailServer
	return crudServer.SelectMany(dmlParam, srv)
}

//Query impl pcrud Query
func (*PcrudRpcDbNFCPatrolLineDetailServer) Query(req *crud.PrivilegeParam, srv PrpcDbNFCPatrolLineDetail_QueryServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)
	rows, err := crud.DbSelectPrivilege("DbNFCPatrolLineDetail", "OrgRID", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbNFCPatrolLineDetail{})
	rowsCount := 0
	for rows.Next() {
		msg := &DbNFCPatrolLineDetail{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		err = srv.Send(msg)
		if err != nil {
			return err
		}
		rowsCount++
	}
	trailer := metadata.Pairs("rows", strconv.Itoa(rowsCount))
	srv.SetTrailer(trailer)
	return nil
}
//Query impl pcrud QueryBatch
func (*PcrudRpcDbNFCPatrolLineDetailServer) QueryBatch(req *crud.PrivilegeParam, srv PrpcDbNFCPatrolLineDetail_QueryBatchServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)
	rows, err := crud.DbSelectPrivilege("DbNFCPatrolLineDetail", "OrgRID", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	if req.QueryCondition.Batch < 1 {
		req.QueryCondition.Batch = 1
	}
	if req.QueryCondition.Batch > 10000 {
		req.QueryCondition.Batch = 10000
	}

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbNFCPatrolLineDetail{})

	var batchNo uint32 = 0
	var rowsOffset uint32 = 0
	var batchCount uint32 = 0
	list := &DbNFCPatrolLineDetailList{}
	for rows.Next() {
		msg := &DbNFCPatrolLineDetail{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		list.Rows = append(list.Rows, msg)
		batchCount++
		rowsOffset++

		if batchCount >= req.QueryCondition.Batch {
			err = srv.Send(list)
			if err != nil {
				return err
			}
			batchNo++
			list = &DbNFCPatrolLineDetailList{
				BatchNo:    batchNo,
				RowsOffset: rowsOffset}
			batchCount = 0
		}
	}
	if batchCount > 0 {
		err = srv.Send(list)
		if err != nil {
			return err
		}
	}
	trailer := metadata.Pairs("rows", strconv.FormatUint(uint64(rowsOffset), 10))
	srv.SetTrailer(trailer)
	return nil
}
// CrudRpcDbNFCPatrolLineRulesServer impl crud Service
type CrudRpcDbNFCPatrolLineRulesServer struct {
}

//Insert impl crud insert
func (*CrudRpcDbNFCPatrolLineRulesServer) Insert(ctx context.Context, req *DbNFCPatrolLineRules) (r *crud.DMLResult, err error) {
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	r, err = req.Insert(db)
	if err == nil {
		sys, userRid, sid := pgxdb.GetYkitInfoFromMetadata(ctx)
		ipinfo := goutil.GetIpinfoFromMeta(ctx)
		userOrgrid := goutil.GetUserOrgRIDFromMeta(ctx)
		goutil.NatsCdc(sys, req.OrgRID, sid, "DbNFCPatrolLineRules.Insert", req, nil)
		crudhook.CrudAfterHook(sys, userRid, userOrgrid, "DbNFCPatrolLineRules.Insert", req, nil, ipinfo)
	}
	return
}

//Update impl crud update
func (*CrudRpcDbNFCPatrolLineRulesServer) Update(ctx context.Context, req *DbNFCPatrolLineRules) (r *crud.DMLResult, err error) {
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	r, err = req.Update(db)
	if err == nil {
		sys, userRid, sid := pgxdb.GetYkitInfoFromMetadata(ctx)
		ipinfo := goutil.GetIpinfoFromMeta(ctx)
		userOrgrid := goutil.GetUserOrgRIDFromMeta(ctx)
		goutil.NatsCdc(sys, req.OrgRID, sid, "DbNFCPatrolLineRules.Update", req, nil)
		crudhook.CrudAfterHook(sys, userRid, userOrgrid, "DbNFCPatrolLineRules.Update", req, nil, ipinfo)
	}
	return
}

//PartialUpdate impl crud PartialUpdate
func (*CrudRpcDbNFCPatrolLineRulesServer) PartialUpdate(ctx context.Context, req *DbNFCPatrolLineRules) (r *crud.DMLResult, err error) {
	param, err := crud.GetDMLParamFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	r, err = req.PartialUpdate(param.KeyColumn, db)
	if err == nil {
		sys, userRid, sid := pgxdb.GetYkitInfoFromMetadata(ctx)
		ipinfo := goutil.GetIpinfoFromMeta(ctx)
		userOrgrid := goutil.GetUserOrgRIDFromMeta(ctx)
		goutil.NatsCdc(sys, req.OrgRID, sid, "DbNFCPatrolLineRules.PartialUpdate", req, param)
		crudhook.CrudAfterHook(sys, userRid, userOrgrid, "DbNFCPatrolLineRules.PartialUpdate", req, param, ipinfo)
	}
	return
}

//Delete impl crud Delete
func (*CrudRpcDbNFCPatrolLineRulesServer) Delete(ctx context.Context, req *DbNFCPatrolLineRules) (r *crud.DMLResult, err error) {
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	r, err = req.Delete(db)
	if err == nil {
		sys, userRid, sid := pgxdb.GetYkitInfoFromMetadata(ctx)
		ipinfo := goutil.GetIpinfoFromMeta(ctx)
		userOrgrid := goutil.GetUserOrgRIDFromMeta(ctx)
		goutil.NatsCdc(sys, req.OrgRID, sid, "DbNFCPatrolLineRules.Delete", req, nil)
		crudhook.CrudAfterHook(sys, userRid, userOrgrid, "DbNFCPatrolLineRules.Delete", req, nil, ipinfo)
	}
	return
}

//SelectOne impl crud SelectOne
func (*CrudRpcDbNFCPatrolLineRulesServer) SelectOne(ctx context.Context, dmlParam *crud.DMLParam) (*DbNFCPatrolLineRules, error) {
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	if len(dmlParam.KeyValue) != 1 {
		return nil, rpc.EBadProtoParam
	}
	msg := &DbNFCPatrolLineRules{RID: dmlParam.KeyValue[0]}

	selectRow, err := msg.SelectOne(dmlParam.ResultColumn, db)
	if err != nil {
		return nil, err
	}
	if selectRow == 0 {
		msg.Reset()
	}
	return msg, nil
}

//SelectMany impl crud SelectMany
func (*CrudRpcDbNFCPatrolLineRulesServer) SelectMany(dmlParam *crud.DMLParam, srv RpcDbNFCPatrolLineRules_SelectManyServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)

	msg := &DbNFCPatrolLineRules{}

	rows, err := msg.SelectByRids(dmlParam.ResultColumn, dmlParam.KeyValue, db)
	if err != nil {
		return err
	}
	defer rows.Close()
	rowsName := crud.RowsColNames2GoFieldNames(rows, msg)
	rowsCount := 0
	for rows.Next() {
		msg := &DbNFCPatrolLineRules{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		err = srv.Send(msg)
		if err != nil {
			return err
		}
		rowsCount++
	}
	trailer := metadata.Pairs("rows", strconv.Itoa(rowsCount))
	srv.SetTrailer(trailer)
	return nil
}

//Query impl crud Query
func (*CrudRpcDbNFCPatrolLineRulesServer) Query(req *crud.QueryParam, srv RpcDbNFCPatrolLineRules_QueryServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)

	rows, err := crud.DbSelectQuery("DbNFCPatrolLineRules", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbNFCPatrolLineRules{})
	rowsCount := 0
	for rows.Next() {
		msg := &DbNFCPatrolLineRules{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		err = srv.Send(msg)
		if err != nil {
			return err
		}
		rowsCount++
	}
	trailer := metadata.Pairs("rows", strconv.Itoa(rowsCount))
	srv.SetTrailer(trailer)
	return nil
}
//Query impl crud QueryBatch
func (*CrudRpcDbNFCPatrolLineRulesServer) QueryBatch(req *crud.QueryParam, srv RpcDbNFCPatrolLineRules_QueryBatchServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)

	rows, err := crud.DbSelectQuery("DbNFCPatrolLineRules", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	if req.Batch < 1 {
		req.Batch = 1
	}
	if req.Batch > 10000 {
		req.Batch = 10000
	}

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbNFCPatrolLineRules{})
	var batchNo uint32 = 0
	var rowsOffset uint32 = 0
	var batchCount uint32 = 0
	list := &DbNFCPatrolLineRulesList{}
	for rows.Next() {
		msg := &DbNFCPatrolLineRules{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		list.Rows = append(list.Rows, msg)
		batchCount++
		rowsOffset++

		if batchCount >= req.Batch {
			err = srv.Send(list)
			if err != nil {
				return err
			}
			batchNo++
			list = &DbNFCPatrolLineRulesList{
				BatchNo:    batchNo,
				RowsOffset: rowsOffset}
			batchCount = 0
		}
	}
	if batchCount > 0 {
		err = srv.Send(list)
		if err != nil {
			return err
		}
	}
	trailer := metadata.Pairs("rows", strconv.FormatUint(uint64(rowsOffset), 10))
	srv.SetTrailer(trailer)
	return nil
}

// PcrudRpcDbNFCPatrolLineRulesServer impl pcrud Service
type PcrudRpcDbNFCPatrolLineRulesServer struct {
}

//Insert impl pcrud Insert
func (*PcrudRpcDbNFCPatrolLineRulesServer) Insert(ctx context.Context, req *DbNFCPatrolLineRules) (*crud.DMLResult, error) {
	var crudServer *CrudRpcDbNFCPatrolLineRulesServer
	_, err := crud.GrpcCheckHasPermission(ctx, "db", "DbNFCPatrolLineRules.Insert")
	if err != nil {
		return nil, err
	}
	err = crud.GrpcCheckHasOrgridPrivilege(ctx, req.OrgRID)
	if err != nil {
		return nil, err
	}
	return crudServer.Insert(ctx, req)
}

//Update impl pcrud Update
func (*PcrudRpcDbNFCPatrolLineRulesServer) Update(ctx context.Context, req *DbNFCPatrolLineRules) (*crud.DMLResult, error) {
	var crudServer *CrudRpcDbNFCPatrolLineRulesServer
	_, err := crud.GrpcCheckHasPermission(ctx, "db", "DbNFCPatrolLineRules.Update")
	if err != nil {
		return nil, err
	}
	err = crud.GrpcCheckHasOrgridPrivilege(ctx, req.OrgRID)
	if err != nil {
		return nil, err
	}
	return crudServer.Update(ctx, req)
}

//PartialUpdate impl pcrud PartialUpdate
func (*PcrudRpcDbNFCPatrolLineRulesServer) PartialUpdate(ctx context.Context, req *DbNFCPatrolLineRules) (*crud.DMLResult, error) {
	var crudServer *CrudRpcDbNFCPatrolLineRulesServer
	_, err := crud.GrpcCheckHasPermission(ctx, "db", "DbNFCPatrolLineRules.Update")
	if err != nil {
		return nil, err
	}
	err = crud.GrpcCheckHasOrgridPrivilege(ctx, req.OrgRID)
	if err != nil {
		return nil, err
	}
	return crudServer.PartialUpdate(ctx, req)
}

//Delete impl pcrud Delete
func (*PcrudRpcDbNFCPatrolLineRulesServer) Delete(ctx context.Context, req *DbNFCPatrolLineRules) (*crud.DMLResult, error) {
	var crudServer *CrudRpcDbNFCPatrolLineRulesServer
	_, err := crud.GrpcCheckHasPermission(ctx, "db", "DbNFCPatrolLineRules.Delete")
	if err != nil {
		return nil, err
	}
	err = crud.GrpcCheckHasOrgridPrivilege(ctx, req.OrgRID)
	if err != nil {
		return nil, err
	}
	return crudServer.Delete(ctx, req)
}

//SelectOne impl pcrud SelectOne
func (*PcrudRpcDbNFCPatrolLineRulesServer) SelectOne(ctx context.Context, dmlParam *crud.DMLParam) (*DbNFCPatrolLineRules, error) {
	var crudServer *CrudRpcDbNFCPatrolLineRulesServer
	return crudServer.SelectOne(ctx, dmlParam)
}

//SelectMany impl pcrud SelectMany
func (*PcrudRpcDbNFCPatrolLineRulesServer) SelectMany(dmlParam *crud.DMLParam, srv PrpcDbNFCPatrolLineRules_SelectManyServer) error {
	var crudServer *CrudRpcDbNFCPatrolLineRulesServer
	return crudServer.SelectMany(dmlParam, srv)
}

//Query impl pcrud Query
func (*PcrudRpcDbNFCPatrolLineRulesServer) Query(req *crud.PrivilegeParam, srv PrpcDbNFCPatrolLineRules_QueryServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)
	rows, err := crud.DbSelectPrivilege("DbNFCPatrolLineRules", "OrgRID", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbNFCPatrolLineRules{})
	rowsCount := 0
	for rows.Next() {
		msg := &DbNFCPatrolLineRules{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		err = srv.Send(msg)
		if err != nil {
			return err
		}
		rowsCount++
	}
	trailer := metadata.Pairs("rows", strconv.Itoa(rowsCount))
	srv.SetTrailer(trailer)
	return nil
}
//Query impl pcrud QueryBatch
func (*PcrudRpcDbNFCPatrolLineRulesServer) QueryBatch(req *crud.PrivilegeParam, srv PrpcDbNFCPatrolLineRules_QueryBatchServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)
	rows, err := crud.DbSelectPrivilege("DbNFCPatrolLineRules", "OrgRID", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	if req.QueryCondition.Batch < 1 {
		req.QueryCondition.Batch = 1
	}
	if req.QueryCondition.Batch > 10000 {
		req.QueryCondition.Batch = 10000
	}

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbNFCPatrolLineRules{})

	var batchNo uint32 = 0
	var rowsOffset uint32 = 0
	var batchCount uint32 = 0
	list := &DbNFCPatrolLineRulesList{}
	for rows.Next() {
		msg := &DbNFCPatrolLineRules{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		list.Rows = append(list.Rows, msg)
		batchCount++
		rowsOffset++

		if batchCount >= req.QueryCondition.Batch {
			err = srv.Send(list)
			if err != nil {
				return err
			}
			batchNo++
			list = &DbNFCPatrolLineRulesList{
				BatchNo:    batchNo,
				RowsOffset: rowsOffset}
			batchCount = 0
		}
	}
	if batchCount > 0 {
		err = srv.Send(list)
		if err != nil {
			return err
		}
	}
	trailer := metadata.Pairs("rows", strconv.FormatUint(uint64(rowsOffset), 10))
	srv.SetTrailer(trailer)
	return nil
}
// CrudRpcDbNFCPatrolLineAndRulesServer impl crud Service
type CrudRpcDbNFCPatrolLineAndRulesServer struct {
}

//Insert impl crud insert
func (*CrudRpcDbNFCPatrolLineAndRulesServer) Insert(ctx context.Context, req *DbNFCPatrolLineAndRules) (r *crud.DMLResult, err error) {
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	r, err = req.Insert(db)
	if err == nil {
		sys, userRid, sid := pgxdb.GetYkitInfoFromMetadata(ctx)
		ipinfo := goutil.GetIpinfoFromMeta(ctx)
		userOrgrid := goutil.GetUserOrgRIDFromMeta(ctx)
		goutil.NatsCdc(sys, req.OrgRID, sid, "DbNFCPatrolLineAndRules.Insert", req, nil)
		crudhook.CrudAfterHook(sys, userRid, userOrgrid, "DbNFCPatrolLineAndRules.Insert", req, nil, ipinfo)
	}
	return
}

//Update impl crud update
func (*CrudRpcDbNFCPatrolLineAndRulesServer) Update(ctx context.Context, req *DbNFCPatrolLineAndRules) (r *crud.DMLResult, err error) {
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	r, err = req.Update(db)
	if err == nil {
		sys, userRid, sid := pgxdb.GetYkitInfoFromMetadata(ctx)
		ipinfo := goutil.GetIpinfoFromMeta(ctx)
		userOrgrid := goutil.GetUserOrgRIDFromMeta(ctx)
		goutil.NatsCdc(sys, req.OrgRID, sid, "DbNFCPatrolLineAndRules.Update", req, nil)
		crudhook.CrudAfterHook(sys, userRid, userOrgrid, "DbNFCPatrolLineAndRules.Update", req, nil, ipinfo)
	}
	return
}

//PartialUpdate impl crud PartialUpdate
func (*CrudRpcDbNFCPatrolLineAndRulesServer) PartialUpdate(ctx context.Context, req *DbNFCPatrolLineAndRules) (r *crud.DMLResult, err error) {
	param, err := crud.GetDMLParamFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	r, err = req.PartialUpdate(param.KeyColumn, db)
	if err == nil {
		sys, userRid, sid := pgxdb.GetYkitInfoFromMetadata(ctx)
		ipinfo := goutil.GetIpinfoFromMeta(ctx)
		userOrgrid := goutil.GetUserOrgRIDFromMeta(ctx)
		goutil.NatsCdc(sys, req.OrgRID, sid, "DbNFCPatrolLineAndRules.PartialUpdate", req, param)
		crudhook.CrudAfterHook(sys, userRid, userOrgrid, "DbNFCPatrolLineAndRules.PartialUpdate", req, param, ipinfo)
	}
	return
}

//Delete impl crud Delete
func (*CrudRpcDbNFCPatrolLineAndRulesServer) Delete(ctx context.Context, req *DbNFCPatrolLineAndRules) (r *crud.DMLResult, err error) {
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	r, err = req.Delete(db)
	if err == nil {
		sys, userRid, sid := pgxdb.GetYkitInfoFromMetadata(ctx)
		ipinfo := goutil.GetIpinfoFromMeta(ctx)
		userOrgrid := goutil.GetUserOrgRIDFromMeta(ctx)
		goutil.NatsCdc(sys, req.OrgRID, sid, "DbNFCPatrolLineAndRules.Delete", req, nil)
		crudhook.CrudAfterHook(sys, userRid, userOrgrid, "DbNFCPatrolLineAndRules.Delete", req, nil, ipinfo)
	}
	return
}

//SelectOne impl crud SelectOne
func (*CrudRpcDbNFCPatrolLineAndRulesServer) SelectOne(ctx context.Context, dmlParam *crud.DMLParam) (*DbNFCPatrolLineAndRules, error) {
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	if len(dmlParam.KeyValue) != 1 {
		return nil, rpc.EBadProtoParam
	}
	msg := &DbNFCPatrolLineAndRules{RID: dmlParam.KeyValue[0]}

	selectRow, err := msg.SelectOne(dmlParam.ResultColumn, db)
	if err != nil {
		return nil, err
	}
	if selectRow == 0 {
		msg.Reset()
	}
	return msg, nil
}

//SelectMany impl crud SelectMany
func (*CrudRpcDbNFCPatrolLineAndRulesServer) SelectMany(dmlParam *crud.DMLParam, srv RpcDbNFCPatrolLineAndRules_SelectManyServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)

	msg := &DbNFCPatrolLineAndRules{}

	rows, err := msg.SelectByRids(dmlParam.ResultColumn, dmlParam.KeyValue, db)
	if err != nil {
		return err
	}
	defer rows.Close()
	rowsName := crud.RowsColNames2GoFieldNames(rows, msg)
	rowsCount := 0
	for rows.Next() {
		msg := &DbNFCPatrolLineAndRules{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		err = srv.Send(msg)
		if err != nil {
			return err
		}
		rowsCount++
	}
	trailer := metadata.Pairs("rows", strconv.Itoa(rowsCount))
	srv.SetTrailer(trailer)
	return nil
}

//Query impl crud Query
func (*CrudRpcDbNFCPatrolLineAndRulesServer) Query(req *crud.QueryParam, srv RpcDbNFCPatrolLineAndRules_QueryServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)

	rows, err := crud.DbSelectQuery("DbNFCPatrolLineAndRules", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbNFCPatrolLineAndRules{})
	rowsCount := 0
	for rows.Next() {
		msg := &DbNFCPatrolLineAndRules{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		err = srv.Send(msg)
		if err != nil {
			return err
		}
		rowsCount++
	}
	trailer := metadata.Pairs("rows", strconv.Itoa(rowsCount))
	srv.SetTrailer(trailer)
	return nil
}
//Query impl crud QueryBatch
func (*CrudRpcDbNFCPatrolLineAndRulesServer) QueryBatch(req *crud.QueryParam, srv RpcDbNFCPatrolLineAndRules_QueryBatchServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)

	rows, err := crud.DbSelectQuery("DbNFCPatrolLineAndRules", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	if req.Batch < 1 {
		req.Batch = 1
	}
	if req.Batch > 10000 {
		req.Batch = 10000
	}

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbNFCPatrolLineAndRules{})
	var batchNo uint32 = 0
	var rowsOffset uint32 = 0
	var batchCount uint32 = 0
	list := &DbNFCPatrolLineAndRulesList{}
	for rows.Next() {
		msg := &DbNFCPatrolLineAndRules{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		list.Rows = append(list.Rows, msg)
		batchCount++
		rowsOffset++

		if batchCount >= req.Batch {
			err = srv.Send(list)
			if err != nil {
				return err
			}
			batchNo++
			list = &DbNFCPatrolLineAndRulesList{
				BatchNo:    batchNo,
				RowsOffset: rowsOffset}
			batchCount = 0
		}
	}
	if batchCount > 0 {
		err = srv.Send(list)
		if err != nil {
			return err
		}
	}
	trailer := metadata.Pairs("rows", strconv.FormatUint(uint64(rowsOffset), 10))
	srv.SetTrailer(trailer)
	return nil
}

// PcrudRpcDbNFCPatrolLineAndRulesServer impl pcrud Service
type PcrudRpcDbNFCPatrolLineAndRulesServer struct {
}

//Insert impl pcrud Insert
func (*PcrudRpcDbNFCPatrolLineAndRulesServer) Insert(ctx context.Context, req *DbNFCPatrolLineAndRules) (*crud.DMLResult, error) {
	var crudServer *CrudRpcDbNFCPatrolLineAndRulesServer
	_, err := crud.GrpcCheckHasPermission(ctx, "db", "DbNFCPatrolLineAndRules.Insert")
	if err != nil {
		return nil, err
	}
	err = crud.GrpcCheckHasOrgridPrivilege(ctx, req.OrgRID)
	if err != nil {
		return nil, err
	}
	return crudServer.Insert(ctx, req)
}

//Update impl pcrud Update
func (*PcrudRpcDbNFCPatrolLineAndRulesServer) Update(ctx context.Context, req *DbNFCPatrolLineAndRules) (*crud.DMLResult, error) {
	var crudServer *CrudRpcDbNFCPatrolLineAndRulesServer
	_, err := crud.GrpcCheckHasPermission(ctx, "db", "DbNFCPatrolLineAndRules.Update")
	if err != nil {
		return nil, err
	}
	err = crud.GrpcCheckHasOrgridPrivilege(ctx, req.OrgRID)
	if err != nil {
		return nil, err
	}
	return crudServer.Update(ctx, req)
}

//PartialUpdate impl pcrud PartialUpdate
func (*PcrudRpcDbNFCPatrolLineAndRulesServer) PartialUpdate(ctx context.Context, req *DbNFCPatrolLineAndRules) (*crud.DMLResult, error) {
	var crudServer *CrudRpcDbNFCPatrolLineAndRulesServer
	_, err := crud.GrpcCheckHasPermission(ctx, "db", "DbNFCPatrolLineAndRules.Update")
	if err != nil {
		return nil, err
	}
	err = crud.GrpcCheckHasOrgridPrivilege(ctx, req.OrgRID)
	if err != nil {
		return nil, err
	}
	return crudServer.PartialUpdate(ctx, req)
}

//Delete impl pcrud Delete
func (*PcrudRpcDbNFCPatrolLineAndRulesServer) Delete(ctx context.Context, req *DbNFCPatrolLineAndRules) (*crud.DMLResult, error) {
	var crudServer *CrudRpcDbNFCPatrolLineAndRulesServer
	_, err := crud.GrpcCheckHasPermission(ctx, "db", "DbNFCPatrolLineAndRules.Delete")
	if err != nil {
		return nil, err
	}
	err = crud.GrpcCheckHasOrgridPrivilege(ctx, req.OrgRID)
	if err != nil {
		return nil, err
	}
	return crudServer.Delete(ctx, req)
}

//SelectOne impl pcrud SelectOne
func (*PcrudRpcDbNFCPatrolLineAndRulesServer) SelectOne(ctx context.Context, dmlParam *crud.DMLParam) (*DbNFCPatrolLineAndRules, error) {
	var crudServer *CrudRpcDbNFCPatrolLineAndRulesServer
	return crudServer.SelectOne(ctx, dmlParam)
}

//SelectMany impl pcrud SelectMany
func (*PcrudRpcDbNFCPatrolLineAndRulesServer) SelectMany(dmlParam *crud.DMLParam, srv PrpcDbNFCPatrolLineAndRules_SelectManyServer) error {
	var crudServer *CrudRpcDbNFCPatrolLineAndRulesServer
	return crudServer.SelectMany(dmlParam, srv)
}

//Query impl pcrud Query
func (*PcrudRpcDbNFCPatrolLineAndRulesServer) Query(req *crud.PrivilegeParam, srv PrpcDbNFCPatrolLineAndRules_QueryServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)
	rows, err := crud.DbSelectPrivilege("DbNFCPatrolLineAndRules", "OrgRID", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbNFCPatrolLineAndRules{})
	rowsCount := 0
	for rows.Next() {
		msg := &DbNFCPatrolLineAndRules{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		err = srv.Send(msg)
		if err != nil {
			return err
		}
		rowsCount++
	}
	trailer := metadata.Pairs("rows", strconv.Itoa(rowsCount))
	srv.SetTrailer(trailer)
	return nil
}
//Query impl pcrud QueryBatch
func (*PcrudRpcDbNFCPatrolLineAndRulesServer) QueryBatch(req *crud.PrivilegeParam, srv PrpcDbNFCPatrolLineAndRules_QueryBatchServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)
	rows, err := crud.DbSelectPrivilege("DbNFCPatrolLineAndRules", "OrgRID", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	if req.QueryCondition.Batch < 1 {
		req.QueryCondition.Batch = 1
	}
	if req.QueryCondition.Batch > 10000 {
		req.QueryCondition.Batch = 10000
	}

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbNFCPatrolLineAndRules{})

	var batchNo uint32 = 0
	var rowsOffset uint32 = 0
	var batchCount uint32 = 0
	list := &DbNFCPatrolLineAndRulesList{}
	for rows.Next() {
		msg := &DbNFCPatrolLineAndRules{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		list.Rows = append(list.Rows, msg)
		batchCount++
		rowsOffset++

		if batchCount >= req.QueryCondition.Batch {
			err = srv.Send(list)
			if err != nil {
				return err
			}
			batchNo++
			list = &DbNFCPatrolLineAndRulesList{
				BatchNo:    batchNo,
				RowsOffset: rowsOffset}
			batchCount = 0
		}
	}
	if batchCount > 0 {
		err = srv.Send(list)
		if err != nil {
			return err
		}
	}
	trailer := metadata.Pairs("rows", strconv.FormatUint(uint64(rowsOffset), 10))
	srv.SetTrailer(trailer)
	return nil
}
// CrudRpcDbMarkerUploadImageHistoryServer impl crud Service
type CrudRpcDbMarkerUploadImageHistoryServer struct {
}

//Insert impl crud insert
func (*CrudRpcDbMarkerUploadImageHistoryServer) Insert(ctx context.Context, req *DbMarkerUploadImageHistory) (r *crud.DMLResult, err error) {
	db, err := pgxdb.GetDbFromRpcMetadata(ctx)
	if err != nil {
		return nil, err
	}
	defer pgxdb.ReleaseDbConn(db)

	r, err = req.Insert(db)
	if err == nil {
		sys, userRid, sid := pgxdb.GetYkitInfoFromMetadata(ctx)
		ipinfo := goutil.GetIpinfoFromMeta(ctx)
		userOrgrid := goutil.GetUserOrgRIDFromMeta(ctx)
		goutil.NatsCdc(sys, req.OrgRID, sid, "DbMarkerUploadImageHistory.Insert", req, nil)
		crudhook.CrudAfterHook(sys, userRid, userOrgrid, "DbMarkerUploadImageHistory.Insert", req, nil, ipinfo)
	}
	return
}

//Query impl crud Query
func (*CrudRpcDbMarkerUploadImageHistoryServer) Query(req *crud.QueryParam, srv RpcDbMarkerUploadImageHistory_QueryServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)

	rows, err := crud.DbSelectQuery("DbMarkerUploadImageHistory", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbMarkerUploadImageHistory{})
	rowsCount := 0
	for rows.Next() {
		msg := &DbMarkerUploadImageHistory{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		err = srv.Send(msg)
		if err != nil {
			return err
		}
		rowsCount++
	}
	trailer := metadata.Pairs("rows", strconv.Itoa(rowsCount))
	srv.SetTrailer(trailer)
	return nil
}
//Query impl crud QueryBatch
func (*CrudRpcDbMarkerUploadImageHistoryServer) QueryBatch(req *crud.QueryParam, srv RpcDbMarkerUploadImageHistory_QueryBatchServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)

	rows, err := crud.DbSelectQuery("DbMarkerUploadImageHistory", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	if req.Batch < 1 {
		req.Batch = 1
	}
	if req.Batch > 10000 {
		req.Batch = 10000
	}

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbMarkerUploadImageHistory{})
	var batchNo uint32 = 0
	var rowsOffset uint32 = 0
	var batchCount uint32 = 0
	list := &DbMarkerUploadImageHistoryList{}
	for rows.Next() {
		msg := &DbMarkerUploadImageHistory{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		list.Rows = append(list.Rows, msg)
		batchCount++
		rowsOffset++

		if batchCount >= req.Batch {
			err = srv.Send(list)
			if err != nil {
				return err
			}
			batchNo++
			list = &DbMarkerUploadImageHistoryList{
				BatchNo:    batchNo,
				RowsOffset: rowsOffset}
			batchCount = 0
		}
	}
	if batchCount > 0 {
		err = srv.Send(list)
		if err != nil {
			return err
		}
	}
	trailer := metadata.Pairs("rows", strconv.FormatUint(uint64(rowsOffset), 10))
	srv.SetTrailer(trailer)
	return nil
}

// PcrudRpcDbMarkerUploadImageHistoryServer impl pcrud Service
type PcrudRpcDbMarkerUploadImageHistoryServer struct {
}

//Insert impl pcrud Insert
func (*PcrudRpcDbMarkerUploadImageHistoryServer) Insert(ctx context.Context, req *DbMarkerUploadImageHistory) (*crud.DMLResult, error) {
	var crudServer *CrudRpcDbMarkerUploadImageHistoryServer
	_, err := crud.GrpcCheckHasPermission(ctx, "db", "DbMarkerUploadImageHistory.Insert")
	if err != nil {
		return nil, err
	}
	err = crud.GrpcCheckHasOrgridPrivilege(ctx, req.OrgRID)
	if err != nil {
		return nil, err
	}
	return crudServer.Insert(ctx, req)
}

//Query impl pcrud Query
func (*PcrudRpcDbMarkerUploadImageHistoryServer) Query(req *crud.PrivilegeParam, srv PrpcDbMarkerUploadImageHistory_QueryServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)
	rows, err := crud.DbSelectPrivilege("DbMarkerUploadImageHistory", "OrgRID", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbMarkerUploadImageHistory{})
	rowsCount := 0
	for rows.Next() {
		msg := &DbMarkerUploadImageHistory{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		err = srv.Send(msg)
		if err != nil {
			return err
		}
		rowsCount++
	}
	trailer := metadata.Pairs("rows", strconv.Itoa(rowsCount))
	srv.SetTrailer(trailer)
	return nil
}
//Query impl pcrud QueryBatch
func (*PcrudRpcDbMarkerUploadImageHistoryServer) QueryBatch(req *crud.PrivilegeParam, srv PrpcDbMarkerUploadImageHistory_QueryBatchServer) error {
	db, err := pgxdb.GetDbFromRpcMetadata(srv.Context())
	if err != nil {
		return err
	}
	defer pgxdb.ReleaseDbConn(db)
	rows, err := crud.DbSelectPrivilege("DbMarkerUploadImageHistory", "OrgRID", req, db)
	if err != nil {
		return err
	}
	defer rows.Close()

	if req.QueryCondition.Batch < 1 {
		req.QueryCondition.Batch = 1
	}
	if req.QueryCondition.Batch > 10000 {
		req.QueryCondition.Batch = 10000
	}

	rowsName := crud.RowsColNames2GoFieldNames(rows, &DbMarkerUploadImageHistory{})

	var batchNo uint32 = 0
	var rowsOffset uint32 = 0
	var batchCount uint32 = 0
	list := &DbMarkerUploadImageHistoryList{}
	for rows.Next() {
		msg := &DbMarkerUploadImageHistory{}
		if len(rowsName) < 1 {
			rowsName = crud.RowsColNames2GoFieldNames(rows, msg)
		}
		vals, err := rows.Values()
		if err == nil {
			_ = yreflect.SetFields(msg, rowsName, vals)
		} else {
			return err
		}

		list.Rows = append(list.Rows, msg)
		batchCount++
		rowsOffset++

		if batchCount >= req.QueryCondition.Batch {
			err = srv.Send(list)
			if err != nil {
				return err
			}
			batchNo++
			list = &DbMarkerUploadImageHistoryList{
				BatchNo:    batchNo,
				RowsOffset: rowsOffset}
			batchCount = 0
		}
	}
	if batchCount > 0 {
		err = srv.Send(list)
		if err != nil {
			return err
		}
	}
	trailer := metadata.Pairs("rows", strconv.FormatUint(uint64(rowsOffset), 10))
	srv.SetTrailer(trailer)
	return nil
}

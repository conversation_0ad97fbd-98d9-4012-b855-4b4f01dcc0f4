//Package bysdb generated by ygen-gocrud. DO NOT EDIT.
//source: bysdb.proto
package bysdb

import (
	"git.kicad99.com/ykit/goutil/crud"
	"github.com/jackc/pgx/v4"
	"github.com/jackc/pgx/v4/pgxpool"
)

//Insert
func (this *DbController) Insert(db *pgxpool.Conn) (r *crud.DMLResult, err error) {
	return crud.Insert(this, db)
}

//Update
func (this *DbController) Update(db *pgxpool.Conn) (r *crud.DMLResult, err error) {
	return crud.Update(this, db)
}

//PartialUpdate
func (this *DbController) PartialUpdate(updateCols []string, db *pgxpool.Conn) (r *crud.DMLResult, err error) {

	return crud.PartialUpdate(this, updateCols, db)
}

//Delete
func (this *DbController) Delete(db *pgxpool.Conn) (r *crud.DMLResult, err error) {
	return crud.Delete(this, db)
}

//SelectOne
func (this *DbController) SelectOne(resultCols []string, db *pgxpool.Conn) (selectRow int32, err error) {

	return crud.YkitDbSelectOne(this, resultCols, []string{"RID"}, db)
}

//SelectByRids
func (this *DbController) SelectByRids(resultCols []string, rids []string, db *pgxpool.Conn) (rows pgx.Rows, err error) {

	return crud.YkitDbSelectByRIDs(this, resultCols, rids, db)
}

//Insert
func (this *DbBysMarker) Insert(db *pgxpool.Conn) (r *crud.DMLResult, err error) {
	return crud.Insert(this, db)
}

//Update
func (this *DbBysMarker) Update(db *pgxpool.Conn) (r *crud.DMLResult, err error) {
	return crud.Update(this, db)
}

//PartialUpdate
func (this *DbBysMarker) PartialUpdate(updateCols []string, db *pgxpool.Conn) (r *crud.DMLResult, err error) {

	return crud.PartialUpdate(this, updateCols, db)
}

//Delete
func (this *DbBysMarker) Delete(db *pgxpool.Conn) (r *crud.DMLResult, err error) {
	return crud.Delete(this, db)
}

//SelectOne
func (this *DbBysMarker) SelectOne(resultCols []string, db *pgxpool.Conn) (selectRow int32, err error) {

	return crud.YkitDbSelectOne(this, resultCols, []string{"RID"}, db)
}

//SelectByRids
func (this *DbBysMarker) SelectByRids(resultCols []string, rids []string, db *pgxpool.Conn) (rows pgx.Rows, err error) {

	return crud.YkitDbSelectByRIDs(this, resultCols, rids, db)
}

//Insert
func (this *DbControllerOnlineHistory) Insert(db *pgxpool.Conn) (r *crud.DMLResult, err error) {
	return crud.Insert(this, db)
}


//Insert
func (this *DbMediaInfo) Insert(db *pgxpool.Conn) (r *crud.DMLResult, err error) {
	return crud.Insert(this, db)
}

//Update
func (this *DbMediaInfo) Update(db *pgxpool.Conn) (r *crud.DMLResult, err error) {
	return crud.Update(this, db)
}

//PartialUpdate
func (this *DbMediaInfo) PartialUpdate(updateCols []string, db *pgxpool.Conn) (r *crud.DMLResult, err error) {

	return crud.PartialUpdate(this, updateCols, db)
}

//Delete
func (this *DbMediaInfo) Delete(db *pgxpool.Conn) (r *crud.DMLResult, err error) {
	return crud.Delete(this, db)
}

//SelectOne
func (this *DbMediaInfo) SelectOne(resultCols []string, db *pgxpool.Conn) (selectRow int32, err error) {

	return crud.YkitDbSelectOne(this, resultCols, []string{"RID"}, db)
}

//SelectByRids
func (this *DbMediaInfo) SelectByRids(resultCols []string, rids []string, db *pgxpool.Conn) (rows pgx.Rows, err error) {

	return crud.YkitDbSelectByRIDs(this, resultCols, rids, db)
}

//Insert
func (this *DbMarkerHistory) Insert(db *pgxpool.Conn) (r *crud.DMLResult, err error) {
	return crud.Insert(this, db)
}


//Insert
func (this *DbMarkerPatrolHistory) Insert(db *pgxpool.Conn) (r *crud.DMLResult, err error) {
	return crud.Insert(this, db)
}


//Insert
func (this *DbNFCPatrolLine) Insert(db *pgxpool.Conn) (r *crud.DMLResult, err error) {
	return crud.Insert(this, db)
}

//Update
func (this *DbNFCPatrolLine) Update(db *pgxpool.Conn) (r *crud.DMLResult, err error) {
	return crud.Update(this, db)
}

//PartialUpdate
func (this *DbNFCPatrolLine) PartialUpdate(updateCols []string, db *pgxpool.Conn) (r *crud.DMLResult, err error) {

	return crud.PartialUpdate(this, updateCols, db)
}

//Delete
func (this *DbNFCPatrolLine) Delete(db *pgxpool.Conn) (r *crud.DMLResult, err error) {
	return crud.Delete(this, db)
}

//SelectOne
func (this *DbNFCPatrolLine) SelectOne(resultCols []string, db *pgxpool.Conn) (selectRow int32, err error) {

	return crud.YkitDbSelectOne(this, resultCols, []string{"RID"}, db)
}

//SelectByRids
func (this *DbNFCPatrolLine) SelectByRids(resultCols []string, rids []string, db *pgxpool.Conn) (rows pgx.Rows, err error) {

	return crud.YkitDbSelectByRIDs(this, resultCols, rids, db)
}

//Insert
func (this *DbNFCPatrolLineDetail) Insert(db *pgxpool.Conn) (r *crud.DMLResult, err error) {
	return crud.Insert(this, db)
}

//Update
func (this *DbNFCPatrolLineDetail) Update(db *pgxpool.Conn) (r *crud.DMLResult, err error) {
	return crud.Update(this, db)
}

//PartialUpdate
func (this *DbNFCPatrolLineDetail) PartialUpdate(updateCols []string, db *pgxpool.Conn) (r *crud.DMLResult, err error) {

	return crud.PartialUpdate(this, updateCols, db)
}

//Delete
func (this *DbNFCPatrolLineDetail) Delete(db *pgxpool.Conn) (r *crud.DMLResult, err error) {
	return crud.Delete(this, db)
}

//SelectOne
func (this *DbNFCPatrolLineDetail) SelectOne(resultCols []string, db *pgxpool.Conn) (selectRow int32, err error) {

	return crud.YkitDbSelectOne(this, resultCols, []string{"RID"}, db)
}

//SelectByRids
func (this *DbNFCPatrolLineDetail) SelectByRids(resultCols []string, rids []string, db *pgxpool.Conn) (rows pgx.Rows, err error) {

	return crud.YkitDbSelectByRIDs(this, resultCols, rids, db)
}

//Insert
func (this *DbNFCPatrolLineRules) Insert(db *pgxpool.Conn) (r *crud.DMLResult, err error) {
	return crud.Insert(this, db)
}

//Update
func (this *DbNFCPatrolLineRules) Update(db *pgxpool.Conn) (r *crud.DMLResult, err error) {
	return crud.Update(this, db)
}

//PartialUpdate
func (this *DbNFCPatrolLineRules) PartialUpdate(updateCols []string, db *pgxpool.Conn) (r *crud.DMLResult, err error) {

	return crud.PartialUpdate(this, updateCols, db)
}

//Delete
func (this *DbNFCPatrolLineRules) Delete(db *pgxpool.Conn) (r *crud.DMLResult, err error) {
	return crud.Delete(this, db)
}

//SelectOne
func (this *DbNFCPatrolLineRules) SelectOne(resultCols []string, db *pgxpool.Conn) (selectRow int32, err error) {

	return crud.YkitDbSelectOne(this, resultCols, []string{"RID"}, db)
}

//SelectByRids
func (this *DbNFCPatrolLineRules) SelectByRids(resultCols []string, rids []string, db *pgxpool.Conn) (rows pgx.Rows, err error) {

	return crud.YkitDbSelectByRIDs(this, resultCols, rids, db)
}

//Insert
func (this *DbNFCPatrolLineAndRules) Insert(db *pgxpool.Conn) (r *crud.DMLResult, err error) {
	return crud.Insert(this, db)
}

//Update
func (this *DbNFCPatrolLineAndRules) Update(db *pgxpool.Conn) (r *crud.DMLResult, err error) {
	return crud.Update(this, db)
}

//PartialUpdate
func (this *DbNFCPatrolLineAndRules) PartialUpdate(updateCols []string, db *pgxpool.Conn) (r *crud.DMLResult, err error) {

	return crud.PartialUpdate(this, updateCols, db)
}

//Delete
func (this *DbNFCPatrolLineAndRules) Delete(db *pgxpool.Conn) (r *crud.DMLResult, err error) {
	return crud.Delete(this, db)
}

//SelectOne
func (this *DbNFCPatrolLineAndRules) SelectOne(resultCols []string, db *pgxpool.Conn) (selectRow int32, err error) {

	return crud.YkitDbSelectOne(this, resultCols, []string{"RID"}, db)
}

//SelectByRids
func (this *DbNFCPatrolLineAndRules) SelectByRids(resultCols []string, rids []string, db *pgxpool.Conn) (rows pgx.Rows, err error) {

	return crud.YkitDbSelectByRIDs(this, resultCols, rids, db)
}

//Insert
func (this *DbMarkerUploadImageHistory) Insert(db *pgxpool.Conn) (r *crud.DMLResult, err error) {
	return crud.Insert(this, db)
}


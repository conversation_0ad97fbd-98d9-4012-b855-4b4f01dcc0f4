--depfile role-init.sql
INSERT INTO DbPermission (rid, permissiontype, permissionname, permissionvalue, updatedat, updateddc)
VALUES('322f8811-2952-51c1-c109-2e9908ec1683', 'db', 'DbController.Insert', 'DbController.Insert', now_utc(), '') ON CONFLICT  DO NOTHING;
--admin role
INSERT INTO dbrolepermission (rid, rolerid, permissionrid, creator, updatedat, updateddc)
VALUES('322f8811-2952-51c1-c109-2e9908ec1683', '00000000-0000-0000-0000-000000000000', '322f8811-2952-51c1-c109-2e9908ec1683', null, now_utc(), '') ON CONFLICT  DO NOTHING;


INSERT INTO DbPermission (rid, permissiontype, permissionname, permissionvalue, updatedat, updateddc)
VALUES('22915843-c27b-6d14-a310-ff6cfd0b1749', 'db', 'DbController.Query', 'DbController.Query', now_utc(), '') ON CONFLICT  DO NOTHING;
--base role
INSERT INTO dbrolepermission (rid, rolerid, permissionrid, creator, updatedat, updateddc)
VALUES('22915843-c27b-6d14-a310-ff6cfd0b1749', '55555555-5555-5555-5555-555555555555', '22915843-c27b-6d14-a310-ff6cfd0b1749', null, now_utc(), '') ON CONFLICT  DO NOTHING;
--admin role
INSERT INTO dbrolepermission (rid, rolerid, permissionrid, creator, updatedat, updateddc)
VALUES('a8328be0-9b56-2aee-e9a2-0de397b943d6', '00000000-0000-0000-0000-000000000000', '22915843-c27b-6d14-a310-ff6cfd0b1749', null, now_utc(), '') ON CONFLICT  DO NOTHING;

INSERT INTO DbPermission (rid, permissiontype, permissionname, permissionvalue, updatedat, updateddc)
VALUES('765b09b9-12b6-92bf-ed02-4a7f6b96eb80', 'db', 'DbController.Update', 'DbController.Update', now_utc(), '') ON CONFLICT  DO NOTHING;
--admin role
INSERT INTO dbrolepermission (rid, rolerid, permissionrid, creator, updatedat, updateddc)
VALUES('765b09b9-12b6-92bf-ed02-4a7f6b96eb80', '00000000-0000-0000-0000-000000000000', '765b09b9-12b6-92bf-ed02-4a7f6b96eb80', null, now_utc(), '') ON CONFLICT  DO NOTHING;


INSERT INTO DbPermission (rid, permissiontype, permissionname, permissionvalue, updatedat, updateddc)
VALUES('9bb4f887-457c-7dac-7426-81fa1fe8918d', 'db', 'DbController.Delete', 'DbController.Delete', now_utc(), '') ON CONFLICT  DO NOTHING;
--admin role
INSERT INTO dbrolepermission (rid, rolerid, permissionrid, creator, updatedat, updateddc)
VALUES('9bb4f887-457c-7dac-7426-81fa1fe8918d', '00000000-0000-0000-0000-000000000000', '9bb4f887-457c-7dac-7426-81fa1fe8918d', null, now_utc(), '') ON CONFLICT  DO NOTHING;


--depfile role-init.sql
INSERT INTO DbPermission (rid, permissiontype, permissionname, permissionvalue, updatedat, updateddc)
VALUES('6aba3eff-ffac-d393-8925-6e030da4192f', 'db', 'DbBysMarker.Insert', 'DbBysMarker.Insert', now_utc(), '') ON CONFLICT  DO NOTHING;
--admin role
INSERT INTO dbrolepermission (rid, rolerid, permissionrid, creator, updatedat, updateddc)
VALUES('6aba3eff-ffac-d393-8925-6e030da4192f', '00000000-0000-0000-0000-000000000000', '6aba3eff-ffac-d393-8925-6e030da4192f', null, now_utc(), '') ON CONFLICT  DO NOTHING;


INSERT INTO DbPermission (rid, permissiontype, permissionname, permissionvalue, updatedat, updateddc)
VALUES('95850f06-98f8-3a61-ba09-eca50e8ea2bf', 'db', 'DbBysMarker.Query', 'DbBysMarker.Query', now_utc(), '') ON CONFLICT  DO NOTHING;
--base role
INSERT INTO dbrolepermission (rid, rolerid, permissionrid, creator, updatedat, updateddc)
VALUES('95850f06-98f8-3a61-ba09-eca50e8ea2bf', '55555555-5555-5555-5555-555555555555', '95850f06-98f8-3a61-ba09-eca50e8ea2bf', null, now_utc(), '') ON CONFLICT  DO NOTHING;
--admin role
INSERT INTO dbrolepermission (rid, rolerid, permissionrid, creator, updatedat, updateddc)
VALUES('faf8784b-785c-1a5e-6863-ec2763c807c3', '00000000-0000-0000-0000-000000000000', '95850f06-98f8-3a61-ba09-eca50e8ea2bf', null, now_utc(), '') ON CONFLICT  DO NOTHING;

INSERT INTO DbPermission (rid, permissiontype, permissionname, permissionvalue, updatedat, updateddc)
VALUES('f29db1db-8f53-cc77-f8a4-f0a07d1dca98', 'db', 'DbBysMarker.Update', 'DbBysMarker.Update', now_utc(), '') ON CONFLICT  DO NOTHING;
--admin role
INSERT INTO dbrolepermission (rid, rolerid, permissionrid, creator, updatedat, updateddc)
VALUES('f29db1db-8f53-cc77-f8a4-f0a07d1dca98', '00000000-0000-0000-0000-000000000000', 'f29db1db-8f53-cc77-f8a4-f0a07d1dca98', null, now_utc(), '') ON CONFLICT  DO NOTHING;


INSERT INTO DbPermission (rid, permissiontype, permissionname, permissionvalue, updatedat, updateddc)
VALUES('5bfdb0a7-df2e-42a0-c530-8fdb08e189b0', 'db', 'DbBysMarker.Delete', 'DbBysMarker.Delete', now_utc(), '') ON CONFLICT  DO NOTHING;
--admin role
INSERT INTO dbrolepermission (rid, rolerid, permissionrid, creator, updatedat, updateddc)
VALUES('5bfdb0a7-df2e-42a0-c530-8fdb08e189b0', '00000000-0000-0000-0000-000000000000', '5bfdb0a7-df2e-42a0-c530-8fdb08e189b0', null, now_utc(), '') ON CONFLICT  DO NOTHING;


--depfile role-init.sql
INSERT INTO DbPermission (rid, permissiontype, permissionname, permissionvalue, updatedat, updateddc)
VALUES('32631637-f021-1552-e43d-831d00dd2749', 'db', 'DbControllerOnlineHistory.Insert', 'DbControllerOnlineHistory.Insert', now_utc(), '') ON CONFLICT  DO NOTHING;
--admin role
INSERT INTO dbrolepermission (rid, rolerid, permissionrid, creator, updatedat, updateddc)
VALUES('32631637-f021-1552-e43d-831d00dd2749', '00000000-0000-0000-0000-000000000000', '32631637-f021-1552-e43d-831d00dd2749', null, now_utc(), '') ON CONFLICT  DO NOTHING;


INSERT INTO DbPermission (rid, permissiontype, permissionname, permissionvalue, updatedat, updateddc)
VALUES('e02cd211-8101-3586-118b-f854ec72c3aa', 'db', 'DbControllerOnlineHistory.Query', 'DbControllerOnlineHistory.Query', now_utc(), '') ON CONFLICT  DO NOTHING;
--base role
INSERT INTO dbrolepermission (rid, rolerid, permissionrid, creator, updatedat, updateddc)
VALUES('e02cd211-8101-3586-118b-f854ec72c3aa', '55555555-5555-5555-5555-555555555555', 'e02cd211-8101-3586-118b-f854ec72c3aa', null, now_utc(), '') ON CONFLICT  DO NOTHING;
--admin role
INSERT INTO dbrolepermission (rid, rolerid, permissionrid, creator, updatedat, updateddc)
VALUES('87bc025a-f33b-038d-8880-9c9435c60cd3', '00000000-0000-0000-0000-000000000000', 'e02cd211-8101-3586-118b-f854ec72c3aa', null, now_utc(), '') ON CONFLICT  DO NOTHING;

--depfile role-init.sql
INSERT INTO DbPermission (rid, permissiontype, permissionname, permissionvalue, updatedat, updateddc)
VALUES('3d12707d-d235-d61b-b181-ab86d43058fd', 'db', 'DbMediaInfo.Insert', 'DbMediaInfo.Insert', now_utc(), '') ON CONFLICT  DO NOTHING;
--admin role
INSERT INTO dbrolepermission (rid, rolerid, permissionrid, creator, updatedat, updateddc)
VALUES('3d12707d-d235-d61b-b181-ab86d43058fd', '00000000-0000-0000-0000-000000000000', '3d12707d-d235-d61b-b181-ab86d43058fd', null, now_utc(), '') ON CONFLICT  DO NOTHING;


INSERT INTO DbPermission (rid, permissiontype, permissionname, permissionvalue, updatedat, updateddc)
VALUES('6b3f1b30-6c8e-b8e5-8937-110b660c419c', 'db', 'DbMediaInfo.Query', 'DbMediaInfo.Query', now_utc(), '') ON CONFLICT  DO NOTHING;
--base role
INSERT INTO dbrolepermission (rid, rolerid, permissionrid, creator, updatedat, updateddc)
VALUES('6b3f1b30-6c8e-b8e5-8937-110b660c419c', '55555555-5555-5555-5555-555555555555', '6b3f1b30-6c8e-b8e5-8937-110b660c419c', null, now_utc(), '') ON CONFLICT  DO NOTHING;
--admin role
INSERT INTO dbrolepermission (rid, rolerid, permissionrid, creator, updatedat, updateddc)
VALUES('633e27fd-3543-8465-051a-ee89192758eb', '00000000-0000-0000-0000-000000000000', '6b3f1b30-6c8e-b8e5-8937-110b660c419c', null, now_utc(), '') ON CONFLICT  DO NOTHING;

INSERT INTO DbPermission (rid, permissiontype, permissionname, permissionvalue, updatedat, updateddc)
VALUES('5fdd1c2d-5f52-08ef-62c5-36ca4f1d8c29', 'db', 'DbMediaInfo.Update', 'DbMediaInfo.Update', now_utc(), '') ON CONFLICT  DO NOTHING;
--admin role
INSERT INTO dbrolepermission (rid, rolerid, permissionrid, creator, updatedat, updateddc)
VALUES('5fdd1c2d-5f52-08ef-62c5-36ca4f1d8c29', '00000000-0000-0000-0000-000000000000', '5fdd1c2d-5f52-08ef-62c5-36ca4f1d8c29', null, now_utc(), '') ON CONFLICT  DO NOTHING;


INSERT INTO DbPermission (rid, permissiontype, permissionname, permissionvalue, updatedat, updateddc)
VALUES('2ecc6857-a405-94a9-bc5b-7440c740af3e', 'db', 'DbMediaInfo.Delete', 'DbMediaInfo.Delete', now_utc(), '') ON CONFLICT  DO NOTHING;
--admin role
INSERT INTO dbrolepermission (rid, rolerid, permissionrid, creator, updatedat, updateddc)
VALUES('2ecc6857-a405-94a9-bc5b-7440c740af3e', '00000000-0000-0000-0000-000000000000', '2ecc6857-a405-94a9-bc5b-7440c740af3e', null, now_utc(), '') ON CONFLICT  DO NOTHING;


--depfile role-init.sql
INSERT INTO DbPermission (rid, permissiontype, permissionname, permissionvalue, updatedat, updateddc)
VALUES('851c05dc-e6d2-3411-9e3b-bd28139879f2', 'db', 'DbMarkerHistory.Insert', 'DbMarkerHistory.Insert', now_utc(), '') ON CONFLICT  DO NOTHING;
--admin role
INSERT INTO dbrolepermission (rid, rolerid, permissionrid, creator, updatedat, updateddc)
VALUES('851c05dc-e6d2-3411-9e3b-bd28139879f2', '00000000-0000-0000-0000-000000000000', '851c05dc-e6d2-3411-9e3b-bd28139879f2', null, now_utc(), '') ON CONFLICT  DO NOTHING;


INSERT INTO DbPermission (rid, permissiontype, permissionname, permissionvalue, updatedat, updateddc)
VALUES('511d3670-8a80-5027-bc93-7726f2676f46', 'db', 'DbMarkerHistory.Query', 'DbMarkerHistory.Query', now_utc(), '') ON CONFLICT  DO NOTHING;
--base role
INSERT INTO dbrolepermission (rid, rolerid, permissionrid, creator, updatedat, updateddc)
VALUES('511d3670-8a80-5027-bc93-7726f2676f46', '55555555-5555-5555-5555-555555555555', '511d3670-8a80-5027-bc93-7726f2676f46', null, now_utc(), '') ON CONFLICT  DO NOTHING;
--admin role
INSERT INTO dbrolepermission (rid, rolerid, permissionrid, creator, updatedat, updateddc)
VALUES('8d390e80-ae1b-ca5e-fcfc-a0dc6e788f53', '00000000-0000-0000-0000-000000000000', '511d3670-8a80-5027-bc93-7726f2676f46', null, now_utc(), '') ON CONFLICT  DO NOTHING;

--depfile role-init.sql
INSERT INTO DbPermission (rid, permissiontype, permissionname, permissionvalue, updatedat, updateddc)
VALUES('d40085f6-09ea-cb0e-2d00-a5a806d8a512', 'db', 'DbMarkerPatrolHistory.Insert', 'DbMarkerPatrolHistory.Insert', now_utc(), '') ON CONFLICT  DO NOTHING;
--admin role
INSERT INTO dbrolepermission (rid, rolerid, permissionrid, creator, updatedat, updateddc)
VALUES('d40085f6-09ea-cb0e-2d00-a5a806d8a512', '00000000-0000-0000-0000-000000000000', 'd40085f6-09ea-cb0e-2d00-a5a806d8a512', null, now_utc(), '') ON CONFLICT  DO NOTHING;


INSERT INTO DbPermission (rid, permissiontype, permissionname, permissionvalue, updatedat, updateddc)
VALUES('b0707d04-c8c2-0c68-1c42-a49259354769', 'db', 'DbMarkerPatrolHistory.Query', 'DbMarkerPatrolHistory.Query', now_utc(), '') ON CONFLICT  DO NOTHING;
--base role
INSERT INTO dbrolepermission (rid, rolerid, permissionrid, creator, updatedat, updateddc)
VALUES('b0707d04-c8c2-0c68-1c42-a49259354769', '55555555-5555-5555-5555-555555555555', 'b0707d04-c8c2-0c68-1c42-a49259354769', null, now_utc(), '') ON CONFLICT  DO NOTHING;
--admin role
INSERT INTO dbrolepermission (rid, rolerid, permissionrid, creator, updatedat, updateddc)
VALUES('ae213286-38c7-c54c-f2f7-c237148c2c8f', '00000000-0000-0000-0000-000000000000', 'b0707d04-c8c2-0c68-1c42-a49259354769', null, now_utc(), '') ON CONFLICT  DO NOTHING;

--depfile role-init.sql
INSERT INTO DbPermission (rid, permissiontype, permissionname, permissionvalue, updatedat, updateddc)
VALUES('9ccb7ed7-7dac-1184-556f-4e23d1217f34', 'db', 'DbNFCPatrolLine.Insert', 'DbNFCPatrolLine.Insert', now_utc(), '') ON CONFLICT  DO NOTHING;
--admin role
INSERT INTO dbrolepermission (rid, rolerid, permissionrid, creator, updatedat, updateddc)
VALUES('9ccb7ed7-7dac-1184-556f-4e23d1217f34', '00000000-0000-0000-0000-000000000000', '9ccb7ed7-7dac-1184-556f-4e23d1217f34', null, now_utc(), '') ON CONFLICT  DO NOTHING;


INSERT INTO DbPermission (rid, permissiontype, permissionname, permissionvalue, updatedat, updateddc)
VALUES('a19ef09d-02af-a0de-f77c-5e309543087d', 'db', 'DbNFCPatrolLine.Query', 'DbNFCPatrolLine.Query', now_utc(), '') ON CONFLICT  DO NOTHING;
--base role
INSERT INTO dbrolepermission (rid, rolerid, permissionrid, creator, updatedat, updateddc)
VALUES('a19ef09d-02af-a0de-f77c-5e309543087d', '55555555-5555-5555-5555-555555555555', 'a19ef09d-02af-a0de-f77c-5e309543087d', null, now_utc(), '') ON CONFLICT  DO NOTHING;
--admin role
INSERT INTO dbrolepermission (rid, rolerid, permissionrid, creator, updatedat, updateddc)
VALUES('26e7b767-3f70-dfb2-1c1f-4af0fd15a76b', '00000000-0000-0000-0000-000000000000', 'a19ef09d-02af-a0de-f77c-5e309543087d', null, now_utc(), '') ON CONFLICT  DO NOTHING;

INSERT INTO DbPermission (rid, permissiontype, permissionname, permissionvalue, updatedat, updateddc)
VALUES('54f147d7-c40e-56f2-40d9-e51f9b0360a6', 'db', 'DbNFCPatrolLine.Update', 'DbNFCPatrolLine.Update', now_utc(), '') ON CONFLICT  DO NOTHING;
--admin role
INSERT INTO dbrolepermission (rid, rolerid, permissionrid, creator, updatedat, updateddc)
VALUES('54f147d7-c40e-56f2-40d9-e51f9b0360a6', '00000000-0000-0000-0000-000000000000', '54f147d7-c40e-56f2-40d9-e51f9b0360a6', null, now_utc(), '') ON CONFLICT  DO NOTHING;


INSERT INTO DbPermission (rid, permissiontype, permissionname, permissionvalue, updatedat, updateddc)
VALUES('289d6683-351d-3c29-13db-911a8f24435a', 'db', 'DbNFCPatrolLine.Delete', 'DbNFCPatrolLine.Delete', now_utc(), '') ON CONFLICT  DO NOTHING;
--admin role
INSERT INTO dbrolepermission (rid, rolerid, permissionrid, creator, updatedat, updateddc)
VALUES('289d6683-351d-3c29-13db-911a8f24435a', '00000000-0000-0000-0000-000000000000', '289d6683-351d-3c29-13db-911a8f24435a', null, now_utc(), '') ON CONFLICT  DO NOTHING;


--depfile role-init.sql
INSERT INTO DbPermission (rid, permissiontype, permissionname, permissionvalue, updatedat, updateddc)
VALUES('b8a09273-09e8-fbf7-f568-b478e62b5137', 'db', 'DbNFCPatrolLineDetail.Insert', 'DbNFCPatrolLineDetail.Insert', now_utc(), '') ON CONFLICT  DO NOTHING;
--admin role
INSERT INTO dbrolepermission (rid, rolerid, permissionrid, creator, updatedat, updateddc)
VALUES('b8a09273-09e8-fbf7-f568-b478e62b5137', '00000000-0000-0000-0000-000000000000', 'b8a09273-09e8-fbf7-f568-b478e62b5137', null, now_utc(), '') ON CONFLICT  DO NOTHING;


INSERT INTO DbPermission (rid, permissiontype, permissionname, permissionvalue, updatedat, updateddc)
VALUES('ac76e000-5537-836d-d7a3-8b68b75d4fec', 'db', 'DbNFCPatrolLineDetail.Query', 'DbNFCPatrolLineDetail.Query', now_utc(), '') ON CONFLICT  DO NOTHING;
--base role
INSERT INTO dbrolepermission (rid, rolerid, permissionrid, creator, updatedat, updateddc)
VALUES('ac76e000-5537-836d-d7a3-8b68b75d4fec', '55555555-5555-5555-5555-555555555555', 'ac76e000-5537-836d-d7a3-8b68b75d4fec', null, now_utc(), '') ON CONFLICT  DO NOTHING;
--admin role
INSERT INTO dbrolepermission (rid, rolerid, permissionrid, creator, updatedat, updateddc)
VALUES('31662346-d058-de2f-02d2-637a55ef68c9', '00000000-0000-0000-0000-000000000000', 'ac76e000-5537-836d-d7a3-8b68b75d4fec', null, now_utc(), '') ON CONFLICT  DO NOTHING;

INSERT INTO DbPermission (rid, permissiontype, permissionname, permissionvalue, updatedat, updateddc)
VALUES('3b8210fe-18c8-6198-ccc0-1815a7aaa58c', 'db', 'DbNFCPatrolLineDetail.Update', 'DbNFCPatrolLineDetail.Update', now_utc(), '') ON CONFLICT  DO NOTHING;
--admin role
INSERT INTO dbrolepermission (rid, rolerid, permissionrid, creator, updatedat, updateddc)
VALUES('3b8210fe-18c8-6198-ccc0-1815a7aaa58c', '00000000-0000-0000-0000-000000000000', '3b8210fe-18c8-6198-ccc0-1815a7aaa58c', null, now_utc(), '') ON CONFLICT  DO NOTHING;


INSERT INTO DbPermission (rid, permissiontype, permissionname, permissionvalue, updatedat, updateddc)
VALUES('0871e75c-f766-4f24-d844-8b34408fd0a9', 'db', 'DbNFCPatrolLineDetail.Delete', 'DbNFCPatrolLineDetail.Delete', now_utc(), '') ON CONFLICT  DO NOTHING;
--admin role
INSERT INTO dbrolepermission (rid, rolerid, permissionrid, creator, updatedat, updateddc)
VALUES('0871e75c-f766-4f24-d844-8b34408fd0a9', '00000000-0000-0000-0000-000000000000', '0871e75c-f766-4f24-d844-8b34408fd0a9', null, now_utc(), '') ON CONFLICT  DO NOTHING;


--depfile role-init.sql
INSERT INTO DbPermission (rid, permissiontype, permissionname, permissionvalue, updatedat, updateddc)
VALUES('8fee6607-eb38-f412-2249-351a6270f5ae', 'db', 'DbNFCPatrolLineRules.Insert', 'DbNFCPatrolLineRules.Insert', now_utc(), '') ON CONFLICT  DO NOTHING;
--admin role
INSERT INTO dbrolepermission (rid, rolerid, permissionrid, creator, updatedat, updateddc)
VALUES('8fee6607-eb38-f412-2249-351a6270f5ae', '00000000-0000-0000-0000-000000000000', '8fee6607-eb38-f412-2249-351a6270f5ae', null, now_utc(), '') ON CONFLICT  DO NOTHING;


INSERT INTO DbPermission (rid, permissiontype, permissionname, permissionvalue, updatedat, updateddc)
VALUES('efa88130-b15e-de0b-b5a1-b5f6e5d8c460', 'db', 'DbNFCPatrolLineRules.Query', 'DbNFCPatrolLineRules.Query', now_utc(), '') ON CONFLICT  DO NOTHING;
--base role
INSERT INTO dbrolepermission (rid, rolerid, permissionrid, creator, updatedat, updateddc)
VALUES('efa88130-b15e-de0b-b5a1-b5f6e5d8c460', '55555555-5555-5555-5555-555555555555', 'efa88130-b15e-de0b-b5a1-b5f6e5d8c460', null, now_utc(), '') ON CONFLICT  DO NOTHING;
--admin role
INSERT INTO dbrolepermission (rid, rolerid, permissionrid, creator, updatedat, updateddc)
VALUES('67ee4760-08c7-3013-8352-92d29e0bd46a', '00000000-0000-0000-0000-000000000000', 'efa88130-b15e-de0b-b5a1-b5f6e5d8c460', null, now_utc(), '') ON CONFLICT  DO NOTHING;

INSERT INTO DbPermission (rid, permissiontype, permissionname, permissionvalue, updatedat, updateddc)
VALUES('9f1d3cde-a756-6891-8878-4c4f3c5a5f79', 'db', 'DbNFCPatrolLineRules.Update', 'DbNFCPatrolLineRules.Update', now_utc(), '') ON CONFLICT  DO NOTHING;
--admin role
INSERT INTO dbrolepermission (rid, rolerid, permissionrid, creator, updatedat, updateddc)
VALUES('9f1d3cde-a756-6891-8878-4c4f3c5a5f79', '00000000-0000-0000-0000-000000000000', '9f1d3cde-a756-6891-8878-4c4f3c5a5f79', null, now_utc(), '') ON CONFLICT  DO NOTHING;


INSERT INTO DbPermission (rid, permissiontype, permissionname, permissionvalue, updatedat, updateddc)
VALUES('39aa1628-6601-ef4d-6e22-23100a08f5cf', 'db', 'DbNFCPatrolLineRules.Delete', 'DbNFCPatrolLineRules.Delete', now_utc(), '') ON CONFLICT  DO NOTHING;
--admin role
INSERT INTO dbrolepermission (rid, rolerid, permissionrid, creator, updatedat, updateddc)
VALUES('39aa1628-6601-ef4d-6e22-23100a08f5cf', '00000000-0000-0000-0000-000000000000', '39aa1628-6601-ef4d-6e22-23100a08f5cf', null, now_utc(), '') ON CONFLICT  DO NOTHING;


--depfile role-init.sql
INSERT INTO DbPermission (rid, permissiontype, permissionname, permissionvalue, updatedat, updateddc)
VALUES('56186cde-717e-c6fb-b1f5-a5ac32968cbd', 'db', 'DbNFCPatrolLineAndRules.Insert', 'DbNFCPatrolLineAndRules.Insert', now_utc(), '') ON CONFLICT  DO NOTHING;
--admin role
INSERT INTO dbrolepermission (rid, rolerid, permissionrid, creator, updatedat, updateddc)
VALUES('56186cde-717e-c6fb-b1f5-a5ac32968cbd', '00000000-0000-0000-0000-000000000000', '56186cde-717e-c6fb-b1f5-a5ac32968cbd', null, now_utc(), '') ON CONFLICT  DO NOTHING;


INSERT INTO DbPermission (rid, permissiontype, permissionname, permissionvalue, updatedat, updateddc)
VALUES('fe3945e5-ae3f-2f03-2469-4ffde284bd49', 'db', 'DbNFCPatrolLineAndRules.Query', 'DbNFCPatrolLineAndRules.Query', now_utc(), '') ON CONFLICT  DO NOTHING;
--base role
INSERT INTO dbrolepermission (rid, rolerid, permissionrid, creator, updatedat, updateddc)
VALUES('fe3945e5-ae3f-2f03-2469-4ffde284bd49', '55555555-5555-5555-5555-555555555555', 'fe3945e5-ae3f-2f03-2469-4ffde284bd49', null, now_utc(), '') ON CONFLICT  DO NOTHING;
--admin role
INSERT INTO dbrolepermission (rid, rolerid, permissionrid, creator, updatedat, updateddc)
VALUES('ff7c3411-79eb-1ebc-fa24-000ee74b1771', '00000000-0000-0000-0000-000000000000', 'fe3945e5-ae3f-2f03-2469-4ffde284bd49', null, now_utc(), '') ON CONFLICT  DO NOTHING;

INSERT INTO DbPermission (rid, permissiontype, permissionname, permissionvalue, updatedat, updateddc)
VALUES('92791afb-afbe-e6f7-06a9-95c7954ba7c2', 'db', 'DbNFCPatrolLineAndRules.Update', 'DbNFCPatrolLineAndRules.Update', now_utc(), '') ON CONFLICT  DO NOTHING;
--admin role
INSERT INTO dbrolepermission (rid, rolerid, permissionrid, creator, updatedat, updateddc)
VALUES('92791afb-afbe-e6f7-06a9-95c7954ba7c2', '00000000-0000-0000-0000-000000000000', '92791afb-afbe-e6f7-06a9-95c7954ba7c2', null, now_utc(), '') ON CONFLICT  DO NOTHING;


INSERT INTO DbPermission (rid, permissiontype, permissionname, permissionvalue, updatedat, updateddc)
VALUES('ced34697-081f-f0f4-b818-439772f32031', 'db', 'DbNFCPatrolLineAndRules.Delete', 'DbNFCPatrolLineAndRules.Delete', now_utc(), '') ON CONFLICT  DO NOTHING;
--admin role
INSERT INTO dbrolepermission (rid, rolerid, permissionrid, creator, updatedat, updateddc)
VALUES('ced34697-081f-f0f4-b818-439772f32031', '00000000-0000-0000-0000-000000000000', 'ced34697-081f-f0f4-b818-439772f32031', null, now_utc(), '') ON CONFLICT  DO NOTHING;


--depfile role-init.sql
INSERT INTO DbPermission (rid, permissiontype, permissionname, permissionvalue, updatedat, updateddc)
VALUES('891c4631-dd00-6844-e9f6-7d3242160273', 'db', 'DbMarkerUploadImageHistory.Insert', 'DbMarkerUploadImageHistory.Insert', now_utc(), '') ON CONFLICT  DO NOTHING;
--admin role
INSERT INTO dbrolepermission (rid, rolerid, permissionrid, creator, updatedat, updateddc)
VALUES('891c4631-dd00-6844-e9f6-7d3242160273', '00000000-0000-0000-0000-000000000000', '891c4631-dd00-6844-e9f6-7d3242160273', null, now_utc(), '') ON CONFLICT  DO NOTHING;


INSERT INTO DbPermission (rid, permissiontype, permissionname, permissionvalue, updatedat, updateddc)
VALUES('0348d2ac-4b4c-69b4-c411-096358cb196e', 'db', 'DbMarkerUploadImageHistory.Query', 'DbMarkerUploadImageHistory.Query', now_utc(), '') ON CONFLICT  DO NOTHING;
--base role
INSERT INTO dbrolepermission (rid, rolerid, permissionrid, creator, updatedat, updateddc)
VALUES('0348d2ac-4b4c-69b4-c411-096358cb196e', '55555555-5555-5555-5555-555555555555', '0348d2ac-4b4c-69b4-c411-096358cb196e', null, now_utc(), '') ON CONFLICT  DO NOTHING;
--admin role
INSERT INTO dbrolepermission (rid, rolerid, permissionrid, creator, updatedat, updateddc)
VALUES('9f0ad1e6-74a1-d784-05a5-42dce1435cc3', '00000000-0000-0000-0000-000000000000', '0348d2ac-4b4c-69b4-c411-096358cb196e', null, now_utc(), '') ON CONFLICT  DO NOTHING;


--generated by ygen-sql DO NOT EDIT.
--source: bysdb.proto

--begin   DbController

--控制器信息表
create table if not exists DbController (
--行ID
RID uuid primary key
--控制器所属的群组
,OrgRID uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
--controller编号
,Controller<PERSON><PERSON> varchar(16) not null unique
--controller描述信息
,ControllerDescription text
--控制器类型 1:中继控制器  2:基站控制器
,ControllerType int
--上级控制器RID, 可以没有，中继控制器一般都有
,ParentRID uuid
--经度
,Lon double precision
--纬度
,Lat double precision
--其它设置，可以在里面扩展需要用到的其它信息
,Setting jsonb not null default  '{}'::jsonb
--控制器硬件ID，int32 > 0
,ControllerHWID int not null unique
--控制器可用信道数，中继控制器为1,基站按实际情况
,ChannelCount int not null default 1
--地图开始显示级别
,MapShowLevel int default 12
--数据最后修改时间
,UpdatedAt timestamp not null default now_utc()
--数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
,UpdatedDC text
--默认通讯方式， 1:有线网络， 2:4g网络 3:无线FSK
,DefaultNetworkType int
--中继对应的上级控制器的通道
,ParentChannelNo int

);
 CREATE INDEX IF NOT EXISTS idxDbControllerOrgRID on DbController USING hash(OrgRID);
 CREATE INDEX IF NOT EXISTS idxDbControllerParentRID on DbController USING hash(ParentRID);
 CREATE UNIQUE INDEX IF NOT EXISTS uidxDbControllerParent ON DbController (ParentRID, ParentChannelNo) WHERE ControllerType=1;

--end DbController--begin   DbBysMarker

--界桩信息表
create table if not exists DbBysMarker (
--行ID
RID uuid primary key
--界桩所属的群组
,OrgRID uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
--界桩编号
,MarkerNo varchar(16) not null unique
--界桩描述信息
,MarkerDescription text
--所属控制器RID
,ControllerRID uuid REFERENCES DbController(RID) ON DELETE RESTRICT
--所属的基站通道，如果所属的控制器为中继控制器，此值固定为1
,ControllerChannel int not null
--经度
,Lon double precision
--纬度
,Lat double precision
--界桩硬件ID,范围
,MarkerHWID int not null unique
--其它设置，可以在里面扩展需要用到的其它信息
--新的红线图规则属性为：redLine，不存在该字段为默认连接，空数组为不连接，
--数组元素为界桩硬件ID，每个都连接
,Setting jsonb not null default  '{}'::jsonb
--界桩型号 1:长型 X*Y*Z cm, 2:短型 x*y*z cm
,MarkerModel int
--地图开始显示级别
,MapShowLevel int default 12
--在所属于基站下的排队号
,MarkerQueueNo int not null
--数据最后修改时间
,UpdatedAt timestamp not null default now_utc()
--数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
,UpdatedDC text
--参数更新时间
,MarkerParamTime timestamp not null default now_utc()
--打卡时间间隔(6-24小时)
,MarkerDayInterval int not null default 24
--排队发射间隔时间（秒）
,MarkerQueueInterval int not null default 6
--报警后发射间隔(30-240秒)
,MarkerEmergentInterval int not null default 60
--界桩通信信道
,MarkerChannel int not null default 1
--界桩苏醒基准时间
,MarkerWakeupBaseTime time not null
--界桩是否已经被遥毙, 0:遥活 1:遥闭  2:遥晕
,MarkerDisabled int not null default 0
--界桩是否有安装电子设备
,HasInstallDevice boolean default true
--石头界桩是否已安装
,HasInstallStone boolean default false
--常规界桩：0, 4g界桩(BF-DTD002): 1, 4g界桩带摄像机(BF-DTD002(Pro)): 2, 专网界桩dmr版(BF-DTD003): 3
,MarkerType int not null default 0
--MarkerType=1时，填写bysproto.BDataUpdate
,MarkerSettings jsonb not null default  '{}'::jsonb
--4G界桩的iccid卡号，20位数字字符串
,ICCID varchar(20)
--iccid卡号到期日期
,ExpirationDate timestamp
--4g界桩的设备唯一标识
,IMEI text
--摄像机是否已经被遥毙, 0:遥活 1:遥闭  2:遥晕
,CameraDisabled int not null default 0

);
 CREATE INDEX IF NOT EXISTS idxDbBysMarkerOrgRID on DbBysMarker USING hash(OrgRID);
 ALTER TABLE DbBysMarker ADD COLUMN IF NOT EXISTS MarkerType integer not null default 0;
 ALTER TABLE DbBysMarker ADD COLUMN IF NOT EXISTS MarkerSettings jsonb not null default  '{}'::jsonb;
 ALTER TABLE dbbysmarker DROP CONSTRAINT if exists dbbysmarker_controllerrid_controllerchannel_markerqueueno_key;
 drop index if exists dbbysmarker_controllerrid_controllerchannel_markerqueueno_key;
 CREATE UNIQUE INDEX IF NOT EXISTS idxdbbysmarker_controllerrid_controllerchannel_markerqueueno_key on DbBysMarker (ControllerRID, ControllerChannel, MarkerQueueNo) where MarkerType=0; --同一个信道的排队号必须唯一,只针对旧的界桩
 ALTER TABLE DbBysMarker ALTER COLUMN ControllerRID DROP NOT NULL;
 ALTER TABLE DbBysMarker ADD COLUMN IF NOT EXISTS ICCID varchar(20);
 ALTER TABLE DbBysMarker ADD COLUMN IF NOT EXISTS ExpirationDate timestamp;
 CREATE UNIQUE INDEX IF NOT EXISTS idxDbBysMarkerICCID on DbBysMarker (ICCID) where MarkerType=1;
 ALTER TABLE DbBysMarker ADD COLUMN IF NOT EXISTS IMEI text;
 ALTER TABLE DbBysMarker ADD COLUMN IF NOT EXISTS CameraDisabled integer not null default 0;

--end DbBysMarker--begin   DbControllerOnlineHistory

--控制器上下线历史表，以月为单位分表
create table if not exists DbControllerOnlineHistory (
--所属的群组
OrgRID uuid not null
--动作时间,utc
,ActionTime timestamp not null
--控制器硬件ID
,ControllerHWID int
--action 1:上线 2:下线 11:ping信息
,ActionCode int
--ip/状态信息
,IpInfo text
--登录网络类型,只对上线有效
,NetworkType int
--电量V
,Power real
--状态
,Status int

) PARTITION BY RANGE (ActionTime);
 CREATE INDEX IF NOT EXISTS idxDbControllerOnlineHistoryControllerHWID on DbControllerOnlineHistory USING hash(ControllerHWID);
 CREATE INDEX IF NOT EXISTS idxDbControllerOnlineHistoryOrgRID on DbControllerOnlineHistory USING hash(OrgRID);
 ALTER TABLE dbcontrolleronlinehistory ADD COLUMN IF NOT EXISTS Status integer;
 ALTER TABLE dbcontrolleronlinehistory ADD COLUMN IF NOT EXISTS DeviceType integer;

--end DbControllerOnlineHistory--begin   DbMediaInfo

--界桩/控制器媒体信息表
create table if not exists DbMediaInfo (
--行ID
RID uuid primary key
--物体所属的群组
,OrgRID uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
--界桩的RID
,MarkerRID uuid null REFERENCES DbBysMarker(RID) ON DELETE set null
--控制器的RID
,ControllerRID uuid null REFERENCES DbController(RID) ON DELETE set null
--媒体类型 1:normal pic 2:3d pic 3:video
,MediaType int
--媒体原始文件名
,MediaOrigFileName text
--上传用户rid
,UploadUserRID uuid REFERENCES DbUser(RID) ON DELETE set null
--描述信息
,MediaDescription text
--上传时间,utc
,UploadTime timestamp not null
--其它设置，可以在里面扩展需要用到的其它信息
,Setting jsonb not null default  '{}'::jsonb
--最新编辑用户rid
,LastUpdateUserRID uuid REFERENCES DbUser(RID) ON DELETE set null
--最新编辑时间,utc
,LastUpdateTime timestamp not null default now_utc()

);
 CREATE INDEX IF NOT EXISTS idxDbMediaInfoOrgRID on DbMediaInfo USING hash(OrgRID);
 CREATE INDEX IF NOT EXISTS idxDbMediaInfoMarkerRID on DbMediaInfo USING hash(MarkerRID);
 CREATE INDEX IF NOT EXISTS idxDbMediaInfoControllerRID on DbMediaInfo USING hash(ControllerRID);

--end DbMediaInfo--begin   DbMarkerHistory

--界桩上传数据历史表，以月为单位分表
create table if not exists DbMarkerHistory (
--接收的控制器ID
ControllerID int
--接收的控制器通道
,ControllerChannel int
--所属的群组
,OrgRID uuid not null
--动作时间,utc
,ActionTime timestamp not null
--界桩硬件ID
,MarkerHWID int
--action 0xd1, 0xd2
--4g界桩信息上报21, ReportInfo=bysproto.BInfoReporting
--4g界桩报警22, ReportInfo=bysproto.BAlarmReporting
,ActionCode int
,Status int
--配置参数信息
,ParamInfo text
--gps lon
,Lon double precision
--gps lat
,Lat double precision
--cmd time
,CmdTime timestamp
--实际接收的控制器ID（中继/基站）
,RecvControllerID int
,ReportInfo jsonb not null default  '{}'::jsonb

) PARTITION BY RANGE (ActionTime);
 CREATE INDEX IF NOT EXISTS idxDbMarkerHistoryMarkerHWID on DbMarkerHistory USING hash(MarkerHWID);
 CREATE INDEX IF NOT EXISTS idxDbMarkerHistoryOrgRID on DbMarkerHistory USING hash(OrgRID);
 CREATE INDEX IF NOT EXISTS idxDbMarkerHistoryEmergency on DbMarkerHistory (Status) where (Status & 128) >0;
 alter table DbMarkerHistory ADD COLUMN IF NOT EXISTS RecvControllerID int;
 alter table DbMarkerHistory ADD COLUMN IF NOT EXISTS ReportInfo jsonb not null default  '{}'::jsonb;

--end DbMarkerHistory--begin   DbMarkerPatrolHistory

--4g界桩NFC巡查打卡历史表，以月为单位分表
create table if not exists DbMarkerPatrolHistory (
--NFC卡片ID，hex string
NFCID text
--NFC打卡时间,utc
,ActionTime timestamp not null
--界桩硬件ID
,MarkerHWID int
--打卡用户
,UserID uuid not null
--所属的群组
,OrgRID uuid not null

) PARTITION BY RANGE (ActionTime);
 CREATE INDEX IF NOT EXISTS idxDbMarkerPatrolHistoryHWID on DbMarkerPatrolHistory USING hash(MarkerHWID);
 CREATE INDEX IF NOT EXISTS idxDbMarkerPatrolHistoryOrgRID on DbMarkerPatrolHistory USING hash(OrgRID);

--end DbMarkerPatrolHistory--begin   DbNFCPatrolLine

--巡查线路表
create table if not exists DbNFCPatrolLine (
--行ID
RID uuid primary key
--线路的归属组
,OrgRID uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
--线路名称
,Name varchar(16) not null unique
,Note text
--数据最后修改时间
,UpdatedAt timestamp not null default now_utc()
--数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
,UpdatedDC text

);
 CREATE INDEX IF NOT EXISTS idxDbNFCPatrolLineOrgRID on DbNFCPatrolLine USING hash(OrgRID);
 CREATE INDEX IF NOT EXISTS idxDbNFCPatrolLineName on DbNFCPatrolLine USING hash(Name);

--end DbNFCPatrolLine--begin   DbNFCPatrolLineDetail

--巡查线路详细表，分开主要是方便使用数据库外键约束
create table if not exists DbNFCPatrolLineDetail (
--行ID
RID uuid primary key
--归属线路
,LineRID uuid not null REFERENCES DbNFCPatrolLine(RID) ON DELETE CASCADE
--线路下的巡查点
,MarkerRID uuid not null REFERENCES DbBysMarker(RID) ON DELETE CASCADE
--数据最后修改时间
,UpdatedAt timestamp not null default now_utc()
--归属组，与路线的上线一致
,OrgRID uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE

);
 CREATE INDEX IF NOT EXISTS idxDbNFCPatrolLineDetailOrgRID on DbNFCPatrolLineDetail USING hash(OrgRID);
 CREATE INDEX IF NOT EXISTS idxDDbNFCPatrolLineDetailLineRID on DbNFCPatrolLineDetail USING hash(LineRID);
 CREATE INDEX IF NOT EXISTS idxDbNFCPatrolLineDetailMarkerRID on DbNFCPatrolLineDetail USING hash(MarkerRID);

--end DbNFCPatrolLineDetail--begin   DbNFCPatrolLineRules

--界桩NFC巡查规则
create table if not exists DbNFCPatrolLineRules (
--行ID
RID uuid primary key
--规则的归属组
,OrgRID uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
--规则名称
,Name varchar(16) not null unique
--星期一
,Day1 boolean default false
--星期二
,Day2 boolean default false
--星期三
,Day3 boolean default false
--星期四
,Day4 boolean default false
--星期五
,Day5 boolean default false
--星期六
,Day6 boolean default false
--星期日
,Day7 boolean default false
--巡查开始的时间
,CheckStartTime time
--巡查结束的时间
,CheckEndTime time
--巡查次数
,CheckCount int not null default 1
--线路巡查规则生效时间的类型 0:总是生效 1:按有效时间段
,EffectiveType int default 0
--规则开始生效时间
,EffectiveStart timestamp
--规则生效结束时间
,EffectiveEnd timestamp
,Note text
--数据最后修改时间
,UpdatedAt timestamp not null default now_utc()
--数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
,UpdatedDC text

);
 CREATE INDEX IF NOT EXISTS idxDbNFCPatrolLineRulesOrgRID on DbNFCPatrolLineRules USING hash(OrgRID);
 CREATE INDEX IF NOT EXISTS idxDbNFCPatrolLineRulesName on DbNFCPatrolLineRules USING hash(Name);

--end DbNFCPatrolLineRules--begin   DbNFCPatrolLineAndRules

--巡查线路和规则的关系表，分开主要是方便使用数据库外键约束
create table if not exists DbNFCPatrolLineAndRules (
--行ID
RID uuid primary key
--归属线路
,LineRID uuid not null REFERENCES DbNFCPatrolLine(RID) ON DELETE CASCADE
--线路规则
,RuleRID uuid not null REFERENCES DbNFCPatrolLineRules(RID) ON DELETE CASCADE
--数据最后修改时间
,UpdatedAt timestamp not null default now_utc()
--归属组，与路线的上线一致
,OrgRID uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE

);
 CREATE INDEX IF NOT EXISTS idxDbNFCPatrolLineAndRulesOrgRID on DbNFCPatrolLineAndRules USING hash(OrgRID);
 CREATE INDEX IF NOT EXISTS idxDbNFCPatrolLineAndRulesLineRID on DbNFCPatrolLineAndRules USING hash(LineRID);
 CREATE INDEX IF NOT EXISTS idxDbNFCPatrolLineAndRulesRuleRID on DbNFCPatrolLineAndRules USING hash(RuleRID);

--end DbNFCPatrolLineAndRules--begin   DbMarkerUploadImageHistory

--4g界桩上传的图片历史表，以月为单位分表
create table if not exists DbMarkerUploadImageHistory (
--所属的群组
OrgRID uuid not null
--界桩硬件ID
,MarkerHWID int
--抓拍时间,utc
,CaptureTime timestamp not null
--抓拍的动作类型，与CamImageData.type一致
--0 – 手动
--1 – 唤醒拍照
--2 – 定时拍照
--3 – 唤醒录像
--4 – 定时录像
,CaptureType int
--服务器接收上传时间,utc
,UploadTime timestamp not null
--上传的图片保存路径，f512673f-0f6a-4b54-95bf-d377b78fba1a.jpg
,FileName text
--界桩上传图片时附带的json数据，CamImageData json字符串
,FormData jsonb not null default  '{}'::jsonb

) PARTITION BY RANGE (UploadTime);
 CREATE INDEX IF NOT EXISTS idxDbMarkerUploadImageHistoryHWID on DbMarkerUploadImageHistory USING hash(MarkerHWID);
 CREATE INDEX IF NOT EXISTS idxDbMarkerUploadImageHistoryOrgRID on DbMarkerUploadImageHistory USING hash(OrgRID);

--end DbMarkerUploadImageHistory
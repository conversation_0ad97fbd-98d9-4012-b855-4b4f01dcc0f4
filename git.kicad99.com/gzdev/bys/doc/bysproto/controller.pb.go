// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: controller.proto

package bysproto

import (
	encoding_binary "encoding/binary"
	fmt "fmt"
	proto "github.com/gogo/protobuf/proto"
	io "io"
	math "math"
	math_bits "math/bits"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

// 系统与控制器所有的消息交互底层都以此为包装
type Bmsg struct {
	// 命令字
	// 1:登录
	// 2:界桩打卡上传
	// 12:回应界桩上传
	// 13：修改控制器手台监听信道号
	// 15：更新控制器新的注册地址
	// 11:ping
	// ----4G界桩新增----
	// 21：信息上报
	// 31：上报应答
	// 22：报警
	// 23：拓展报警(外接传感器)
	// 32：报警应答
	// 33：报警解除
	// 28: 报警解除应答
	// 24：参数更新
	// 34：参数更新应答
	// 25：软件更新(预留)
	// 35：软件更新应答(预留)
	// 26：主动抓拍(预留)
	// 36：主动抓拍应答(预留)
	// 27：遥闭
	// 37：遥闭应答
	Cmd uint32 `protobuf:"fixed32,2,opt,name=Cmd,proto3" json:"Cmd,omitempty"`
	// 从0开始增1使用,用于区分相同命令重复的包
	No uint32 `protobuf:"varint,5,opt,name=No,proto3" json:"No,omitempty"`
	// response code
	Res int32 `protobuf:"zigzag32,9,opt,name=Res,proto3" json:"Res,omitempty"`
	// msg body
	Body []byte `protobuf:"bytes,10,opt,name=Body,proto3" json:"Body,omitempty"`
	// optional str parameter,额外的信息,一般不会有,有些特殊命令里面可能用到
	Optstr string `protobuf:"bytes,11,opt,name=Optstr,proto3" json:"Optstr,omitempty"`
	// optional binary parameter,额外的信息,一般不会有,有些特殊命令里面可能用到
	Optbin []byte `protobuf:"bytes,12,opt,name=Optbin,proto3" json:"Optbin,omitempty"`
}

func (m *Bmsg) Reset()         { *m = Bmsg{} }
func (m *Bmsg) String() string { return proto.CompactTextString(m) }
func (*Bmsg) ProtoMessage()    {}
func (*Bmsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_ed7f10298fa1d90f, []int{0}
}
func (m *Bmsg) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Bmsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Bmsg.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Bmsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Bmsg.Merge(m, src)
}
func (m *Bmsg) XXX_Size() int {
	return m.Size()
}
func (m *Bmsg) XXX_DiscardUnknown() {
	xxx_messageInfo_Bmsg.DiscardUnknown(m)
}

var xxx_messageInfo_Bmsg proto.InternalMessageInfo

func (m *Bmsg) GetCmd() uint32 {
	if m != nil {
		return m.Cmd
	}
	return 0
}

func (m *Bmsg) GetNo() uint32 {
	if m != nil {
		return m.No
	}
	return 0
}

func (m *Bmsg) GetRes() int32 {
	if m != nil {
		return m.Res
	}
	return 0
}

func (m *Bmsg) GetBody() []byte {
	if m != nil {
		return m.Body
	}
	return nil
}

func (m *Bmsg) GetOptstr() string {
	if m != nil {
		return m.Optstr
	}
	return ""
}

func (m *Bmsg) GetOptbin() []byte {
	if m != nil {
		return m.Optbin
	}
	return nil
}

// 登录信息
// bmsg.cmd=1
type BloginReq struct {
	// 控制器名称，不是控制器硬件ID
	Name string `protobuf:"bytes,1,opt,name=Name,proto3" json:"Name,omitempty"`
	// base64(sha256(base64(sha256(controllerName+controllerPass))+LoginTimeStr))
	// 4g界桩: base64(sha256(base64(sha256(IMEI+ICCID))+LoginTimeStr))
	PassHash string `protobuf:"bytes,2,opt,name=PassHash,proto3" json:"PassHash,omitempty"`
	// yyyy-mm-dd HH:MM:SS, 必须是3分钟内的时间(utc)
	LoginTimeStr string `protobuf:"bytes,3,opt,name=LoginTimeStr,proto3" json:"LoginTimeStr,omitempty"`
	// 系统名称
	Sys string `protobuf:"bytes,4,opt,name=Sys,proto3" json:"Sys,omitempty"`
	// 电量,单位V,=0无效，7.2v = 72
	Power int32 `protobuf:"zigzag32,5,opt,name=Power,proto3" json:"Power,omitempty"`
	// 控制器本地手台当前信道号(对界桩通讯的手台)，无效时为0
	ChannelNo int32 `protobuf:"zigzag32,6,opt,name=ChannelNo,proto3" json:"ChannelNo,omitempty"`
	// 本控制器数据中转的通道号和对应的信道号
	// [通道号1,通道1手台的信道号，通道号2,通道2手台的信道号，...]
	// 如[1,1,2,1]表示通道1 在1信道，通道2也在1信道
	TransferChannelNos []int32 `protobuf:"zigzag32,7,rep,packed,name=TransferChannelNos,proto3" json:"TransferChannelNos,omitempty"`
	// 登录渠道， 0:未知 1:有线网络， 2:4g网络 3:无线FSK
	NetworkType int32 `protobuf:"varint,8,opt,name=NetworkType,proto3" json:"NetworkType,omitempty"`
	// 登录设备类型 0:fsk控制器 1:4g界桩
	DeviceType int32 `protobuf:"varint,9,opt,name=DeviceType,proto3" json:"DeviceType,omitempty"`
	// 4g界桩IMEI
	IMEI string `protobuf:"bytes,10,opt,name=IMEI,proto3" json:"IMEI,omitempty"`
	// 4g界桩ICCID
	ICCID string `protobuf:"bytes,11,opt,name=ICCID,proto3" json:"ICCID,omitempty"`
	// 界桩参数版本时间
	// yyyy-mm-dd HH:MM:SS
	DataVersion string `protobuf:"bytes,12,opt,name=dataVersion,proto3" json:"dataVersion,omitempty"`
}

func (m *BloginReq) Reset()         { *m = BloginReq{} }
func (m *BloginReq) String() string { return proto.CompactTextString(m) }
func (*BloginReq) ProtoMessage()    {}
func (*BloginReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ed7f10298fa1d90f, []int{1}
}
func (m *BloginReq) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *BloginReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_BloginReq.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *BloginReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BloginReq.Merge(m, src)
}
func (m *BloginReq) XXX_Size() int {
	return m.Size()
}
func (m *BloginReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BloginReq.DiscardUnknown(m)
}

var xxx_messageInfo_BloginReq proto.InternalMessageInfo

func (m *BloginReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *BloginReq) GetPassHash() string {
	if m != nil {
		return m.PassHash
	}
	return ""
}

func (m *BloginReq) GetLoginTimeStr() string {
	if m != nil {
		return m.LoginTimeStr
	}
	return ""
}

func (m *BloginReq) GetSys() string {
	if m != nil {
		return m.Sys
	}
	return ""
}

func (m *BloginReq) GetPower() int32 {
	if m != nil {
		return m.Power
	}
	return 0
}

func (m *BloginReq) GetChannelNo() int32 {
	if m != nil {
		return m.ChannelNo
	}
	return 0
}

func (m *BloginReq) GetTransferChannelNos() []int32 {
	if m != nil {
		return m.TransferChannelNos
	}
	return nil
}

func (m *BloginReq) GetNetworkType() int32 {
	if m != nil {
		return m.NetworkType
	}
	return 0
}

func (m *BloginReq) GetDeviceType() int32 {
	if m != nil {
		return m.DeviceType
	}
	return 0
}

func (m *BloginReq) GetIMEI() string {
	if m != nil {
		return m.IMEI
	}
	return ""
}

func (m *BloginReq) GetICCID() string {
	if m != nil {
		return m.ICCID
	}
	return ""
}

func (m *BloginReq) GetDataVersion() string {
	if m != nil {
		return m.DataVersion
	}
	return ""
}

// 登录回应
// bmsy.cmd=1
// bmsg.Res=1
// bmsg.body=BloginRes
type BloginRes struct {
	// 100: 登录成功 -1：无此控制器信息 -2:时间参数不对 -3: 未知错误 -4:pass 不对
	Code int32 `protobuf:"varint,1,opt,name=Code,proto3" json:"Code,omitempty"`
	// 控制器硬件ID，登录成功时返回
	ControllerID uint32 `protobuf:"fixed32,2,opt,name=ControllerID,proto3" json:"ControllerID,omitempty"`
	// 服务器时间yyyy-mm-dd HH:MM:SS.zzz(utc)
	ServerTime string `protobuf:"bytes,5,opt,name=ServerTime,proto3" json:"ServerTime,omitempty"`
	// 校验界桩crc2的系统密码
	SystemPassword string `protobuf:"bytes,6,opt,name=SystemPassword,proto3" json:"SystemPassword,omitempty"`
	// 错误描述，未知错误时可能有
	Err string `protobuf:"bytes,7,opt,name=Err,proto3" json:"Err,omitempty"`
}

func (m *BloginRes) Reset()         { *m = BloginRes{} }
func (m *BloginRes) String() string { return proto.CompactTextString(m) }
func (*BloginRes) ProtoMessage()    {}
func (*BloginRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_ed7f10298fa1d90f, []int{2}
}
func (m *BloginRes) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *BloginRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_BloginRes.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *BloginRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BloginRes.Merge(m, src)
}
func (m *BloginRes) XXX_Size() int {
	return m.Size()
}
func (m *BloginRes) XXX_DiscardUnknown() {
	xxx_messageInfo_BloginRes.DiscardUnknown(m)
}

var xxx_messageInfo_BloginRes proto.InternalMessageInfo

func (m *BloginRes) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *BloginRes) GetControllerID() uint32 {
	if m != nil {
		return m.ControllerID
	}
	return 0
}

func (m *BloginRes) GetServerTime() string {
	if m != nil {
		return m.ServerTime
	}
	return ""
}

func (m *BloginRes) GetSystemPassword() string {
	if m != nil {
		return m.SystemPassword
	}
	return ""
}

func (m *BloginRes) GetErr() string {
	if m != nil {
		return m.Err
	}
	return ""
}

// 界桩上传的gps信息,无效gps时为全0
type BGPS struct {
	// 东经为+，西经为-,单位为度
	Lon float64 `protobuf:"fixed64,1,opt,name=Lon,proto3" json:"Lon,omitempty"`
	// 北纬为+，南纬为-,单位为度
	Lat    float64 `protobuf:"fixed64,2,opt,name=Lat,proto3" json:"Lat,omitempty"`
	Height int32   `protobuf:"zigzag32,3,opt,name=Height,proto3" json:"Height,omitempty"`
}

func (m *BGPS) Reset()         { *m = BGPS{} }
func (m *BGPS) String() string { return proto.CompactTextString(m) }
func (*BGPS) ProtoMessage()    {}
func (*BGPS) Descriptor() ([]byte, []int) {
	return fileDescriptor_ed7f10298fa1d90f, []int{3}
}
func (m *BGPS) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *BGPS) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_BGPS.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *BGPS) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BGPS.Merge(m, src)
}
func (m *BGPS) XXX_Size() int {
	return m.Size()
}
func (m *BGPS) XXX_DiscardUnknown() {
	xxx_messageInfo_BGPS.DiscardUnknown(m)
}

var xxx_messageInfo_BGPS proto.InternalMessageInfo

func (m *BGPS) GetLon() float64 {
	if m != nil {
		return m.Lon
	}
	return 0
}

func (m *BGPS) GetLat() float64 {
	if m != nil {
		return m.Lat
	}
	return 0
}

func (m *BGPS) GetHeight() int32 {
	if m != nil {
		return m.Height
	}
	return 0
}

// 界桩数据上传
// bmsg.cmd=2
type BdeviceUpdate struct {
	// 界桩ID
	DeviceID int32 `protobuf:"zigzag32,1,opt,name=DeviceID,proto3" json:"DeviceID,omitempty"`
	// 上传的命令
	// 0xd1: 常规上传
	// 0xd2: 报警上传
	Cmd int32 `protobuf:"zigzag32,2,opt,name=Cmd,proto3" json:"Cmd,omitempty"`
	// 界桩状态，目前只用低位一个字节
	Status int32 `protobuf:"zigzag32,3,opt,name=Status,proto3" json:"Status,omitempty"`
	// 界桩上传的系统密码检验是否正确 0:正确 1：不正确
	SystemPassCheckOK int32 `protobuf:"varint,4,opt,name=SystemPassCheckOK,proto3" json:"SystemPassCheckOK,omitempty"`
	// gps信息，没有时不需要填写
	GPS *BGPS `protobuf:"bytes,5,opt,name=GPS,proto3" json:"GPS,omitempty"`
	// 界桩参数版本
	ParamVersion int32 `protobuf:"varint,6,opt,name=ParamVersion,proto3" json:"ParamVersion,omitempty"`
	// 界桩参数更新时间
	ParamTime string `protobuf:"bytes,7,opt,name=ParamTime,proto3" json:"ParamTime,omitempty"`
	// 指令时间
	CmdTime string `protobuf:"bytes,8,opt,name=CmdTime,proto3" json:"CmdTime,omitempty"`
	// 接收的基站/中继控制器ID
	StationID int32 `protobuf:"zigzag32,9,opt,name=StationID,proto3" json:"StationID,omitempty"`
	// 基站控制器的接收通道号，编号从1开始,不是基站上传的，此值为0
	StationDeviceNo int32 `protobuf:"zigzag32,10,opt,name=StationDeviceNo,proto3" json:"StationDeviceNo,omitempty"`
	// 接收设备的场强值
	DeviceFieldStrength int32 `protobuf:"zigzag32,12,opt,name=DeviceFieldStrength,proto3" json:"DeviceFieldStrength,omitempty"`
	// 接收设备的信道号
	DeviceChannelNo int32 `protobuf:"zigzag32,13,opt,name=DeviceChannelNo,proto3" json:"DeviceChannelNo,omitempty"`
}

func (m *BdeviceUpdate) Reset()         { *m = BdeviceUpdate{} }
func (m *BdeviceUpdate) String() string { return proto.CompactTextString(m) }
func (*BdeviceUpdate) ProtoMessage()    {}
func (*BdeviceUpdate) Descriptor() ([]byte, []int) {
	return fileDescriptor_ed7f10298fa1d90f, []int{4}
}
func (m *BdeviceUpdate) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *BdeviceUpdate) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_BdeviceUpdate.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *BdeviceUpdate) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BdeviceUpdate.Merge(m, src)
}
func (m *BdeviceUpdate) XXX_Size() int {
	return m.Size()
}
func (m *BdeviceUpdate) XXX_DiscardUnknown() {
	xxx_messageInfo_BdeviceUpdate.DiscardUnknown(m)
}

var xxx_messageInfo_BdeviceUpdate proto.InternalMessageInfo

func (m *BdeviceUpdate) GetDeviceID() int32 {
	if m != nil {
		return m.DeviceID
	}
	return 0
}

func (m *BdeviceUpdate) GetCmd() int32 {
	if m != nil {
		return m.Cmd
	}
	return 0
}

func (m *BdeviceUpdate) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *BdeviceUpdate) GetSystemPassCheckOK() int32 {
	if m != nil {
		return m.SystemPassCheckOK
	}
	return 0
}

func (m *BdeviceUpdate) GetGPS() *BGPS {
	if m != nil {
		return m.GPS
	}
	return nil
}

func (m *BdeviceUpdate) GetParamVersion() int32 {
	if m != nil {
		return m.ParamVersion
	}
	return 0
}

func (m *BdeviceUpdate) GetParamTime() string {
	if m != nil {
		return m.ParamTime
	}
	return ""
}

func (m *BdeviceUpdate) GetCmdTime() string {
	if m != nil {
		return m.CmdTime
	}
	return ""
}

func (m *BdeviceUpdate) GetStationID() int32 {
	if m != nil {
		return m.StationID
	}
	return 0
}

func (m *BdeviceUpdate) GetStationDeviceNo() int32 {
	if m != nil {
		return m.StationDeviceNo
	}
	return 0
}

func (m *BdeviceUpdate) GetDeviceFieldStrength() int32 {
	if m != nil {
		return m.DeviceFieldStrength
	}
	return 0
}

func (m *BdeviceUpdate) GetDeviceChannelNo() int32 {
	if m != nil {
		return m.DeviceChannelNo
	}
	return 0
}

// 中继/基站状态信息
type ControllerStatus struct {
	// 接收的基站/中继控制器ID
	StationID int32 `protobuf:"zigzag32,1,opt,name=StationID,proto3" json:"StationID,omitempty"`
	// 基站控制器的接收手台号，编号从1开始,本控制器的状态时，此值为0
	StationDeviceNo int32 `protobuf:"zigzag32,2,opt,name=StationDeviceNo,proto3" json:"StationDeviceNo,omitempty"`
	// 电量,单位V,=0无效，7.2v = 72
	Power int32 `protobuf:"zigzag32,3,opt,name=Power,proto3" json:"Power,omitempty"`
	// 控制器手台当前信道号(与界桩通讯的手台)
	ChannelNo int32 `protobuf:"zigzag32,4,opt,name=ChannelNo,proto3" json:"ChannelNo,omitempty"`
	// 本控制器数据中转的通道号和对应的信道号
	// [通道号1,通道1手台的信道号，通道号2,通道2手台的信道号，...]
	// 如[1,1,2,1]表示通道1 在1信道，通道2也在1信道
	TransferChannelNos []int32 `protobuf:"zigzag32,7,rep,packed,name=TransferChannelNos,proto3" json:"TransferChannelNos,omitempty"`
	// 控制器状态
	// bit0:时间异常 0:无异常 1:无法获取时间（可能gps异常)
	Status uint32 `protobuf:"varint,8,opt,name=Status,proto3" json:"Status,omitempty"`
}

func (m *ControllerStatus) Reset()         { *m = ControllerStatus{} }
func (m *ControllerStatus) String() string { return proto.CompactTextString(m) }
func (*ControllerStatus) ProtoMessage()    {}
func (*ControllerStatus) Descriptor() ([]byte, []int) {
	return fileDescriptor_ed7f10298fa1d90f, []int{5}
}
func (m *ControllerStatus) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ControllerStatus) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ControllerStatus.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ControllerStatus) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ControllerStatus.Merge(m, src)
}
func (m *ControllerStatus) XXX_Size() int {
	return m.Size()
}
func (m *ControllerStatus) XXX_DiscardUnknown() {
	xxx_messageInfo_ControllerStatus.DiscardUnknown(m)
}

var xxx_messageInfo_ControllerStatus proto.InternalMessageInfo

func (m *ControllerStatus) GetStationID() int32 {
	if m != nil {
		return m.StationID
	}
	return 0
}

func (m *ControllerStatus) GetStationDeviceNo() int32 {
	if m != nil {
		return m.StationDeviceNo
	}
	return 0
}

func (m *ControllerStatus) GetPower() int32 {
	if m != nil {
		return m.Power
	}
	return 0
}

func (m *ControllerStatus) GetChannelNo() int32 {
	if m != nil {
		return m.ChannelNo
	}
	return 0
}

func (m *ControllerStatus) GetTransferChannelNos() []int32 {
	if m != nil {
		return m.TransferChannelNos
	}
	return nil
}

func (m *ControllerStatus) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

// 回应界桩上传（如果确实有参数需要修改的话）
// bmsg.cmd=12
type BdeviceUpdateResponse struct {
	// 界桩ID
	DeviceID int32 `protobuf:"zigzag32,1,opt,name=DeviceID,proto3" json:"DeviceID,omitempty"`
	// 接收的基站/中继控制器ID
	StationID int32 `protobuf:"zigzag32,9,opt,name=StationID,proto3" json:"StationID,omitempty"`
	// 基站控制器的通道号，编号从1开始,不是基站上传的，此值为0
	StationDeviceNo int32 `protobuf:"zigzag32,10,opt,name=StationDeviceNo,proto3" json:"StationDeviceNo,omitempty"`
	// 回应命令数据
	Cmd0XD0 []byte `protobuf:"bytes,11,opt,name=Cmd0xD0,proto3" json:"Cmd0xD0,omitempty"`
}

func (m *BdeviceUpdateResponse) Reset()         { *m = BdeviceUpdateResponse{} }
func (m *BdeviceUpdateResponse) String() string { return proto.CompactTextString(m) }
func (*BdeviceUpdateResponse) ProtoMessage()    {}
func (*BdeviceUpdateResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_ed7f10298fa1d90f, []int{6}
}
func (m *BdeviceUpdateResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *BdeviceUpdateResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_BdeviceUpdateResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *BdeviceUpdateResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BdeviceUpdateResponse.Merge(m, src)
}
func (m *BdeviceUpdateResponse) XXX_Size() int {
	return m.Size()
}
func (m *BdeviceUpdateResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BdeviceUpdateResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BdeviceUpdateResponse proto.InternalMessageInfo

func (m *BdeviceUpdateResponse) GetDeviceID() int32 {
	if m != nil {
		return m.DeviceID
	}
	return 0
}

func (m *BdeviceUpdateResponse) GetStationID() int32 {
	if m != nil {
		return m.StationID
	}
	return 0
}

func (m *BdeviceUpdateResponse) GetStationDeviceNo() int32 {
	if m != nil {
		return m.StationDeviceNo
	}
	return 0
}

func (m *BdeviceUpdateResponse) GetCmd0XD0() []byte {
	if m != nil {
		return m.Cmd0XD0
	}
	return nil
}

// 修改控制器手台默认信道号（针对与界桩通讯的手台）
// bmsg.cmd=13
type BControllerUpdateChannel struct {
	// 控制器ID
	StationID int32 `protobuf:"zigzag32,9,opt,name=StationID,proto3" json:"StationID,omitempty"`
	// 控制器通道号
	StationDeviceNo int32 `protobuf:"zigzag32,10,opt,name=StationDeviceNo,proto3" json:"StationDeviceNo,omitempty"`
	// 新默认监听信道号
	NewChannelNo int32 `protobuf:"zigzag32,3,opt,name=NewChannelNo,proto3" json:"NewChannelNo,omitempty"`
}

func (m *BControllerUpdateChannel) Reset()         { *m = BControllerUpdateChannel{} }
func (m *BControllerUpdateChannel) String() string { return proto.CompactTextString(m) }
func (*BControllerUpdateChannel) ProtoMessage()    {}
func (*BControllerUpdateChannel) Descriptor() ([]byte, []int) {
	return fileDescriptor_ed7f10298fa1d90f, []int{7}
}
func (m *BControllerUpdateChannel) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *BControllerUpdateChannel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_BControllerUpdateChannel.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *BControllerUpdateChannel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BControllerUpdateChannel.Merge(m, src)
}
func (m *BControllerUpdateChannel) XXX_Size() int {
	return m.Size()
}
func (m *BControllerUpdateChannel) XXX_DiscardUnknown() {
	xxx_messageInfo_BControllerUpdateChannel.DiscardUnknown(m)
}

var xxx_messageInfo_BControllerUpdateChannel proto.InternalMessageInfo

func (m *BControllerUpdateChannel) GetStationID() int32 {
	if m != nil {
		return m.StationID
	}
	return 0
}

func (m *BControllerUpdateChannel) GetStationDeviceNo() int32 {
	if m != nil {
		return m.StationDeviceNo
	}
	return 0
}

func (m *BControllerUpdateChannel) GetNewChannelNo() int32 {
	if m != nil {
		return m.NewChannelNo
	}
	return 0
}

// bmsg.cmd=15, 更新控制器新的注册地址
// bmsg.body=BControllerNewServerAddr
// bmsg.Res=1,控制器应答
type BControllerNewServerAddr struct {
	// 基站控制器ID
	StationID int32 `protobuf:"zigzag32,1,opt,name=StationID,proto3" json:"StationID,omitempty"`
	// 在上级控制器的通道号，中继时需要填写，基站本地通道时为0
	ControllerChannelNo int32 `protobuf:"zigzag32,2,opt,name=ControllerChannelNo,proto3" json:"ControllerChannelNo,omitempty"`
	// 新服务器IP地址，支持域名和IP
	Ip string `protobuf:"bytes,3,opt,name=Ip,proto3" json:"Ip,omitempty"`
	// 新服务器端口
	Port int32 `protobuf:"zigzag32,4,opt,name=Port,proto3" json:"Port,omitempty"`
}

func (m *BControllerNewServerAddr) Reset()         { *m = BControllerNewServerAddr{} }
func (m *BControllerNewServerAddr) String() string { return proto.CompactTextString(m) }
func (*BControllerNewServerAddr) ProtoMessage()    {}
func (*BControllerNewServerAddr) Descriptor() ([]byte, []int) {
	return fileDescriptor_ed7f10298fa1d90f, []int{8}
}
func (m *BControllerNewServerAddr) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *BControllerNewServerAddr) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_BControllerNewServerAddr.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *BControllerNewServerAddr) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BControllerNewServerAddr.Merge(m, src)
}
func (m *BControllerNewServerAddr) XXX_Size() int {
	return m.Size()
}
func (m *BControllerNewServerAddr) XXX_DiscardUnknown() {
	xxx_messageInfo_BControllerNewServerAddr.DiscardUnknown(m)
}

var xxx_messageInfo_BControllerNewServerAddr proto.InternalMessageInfo

func (m *BControllerNewServerAddr) GetStationID() int32 {
	if m != nil {
		return m.StationID
	}
	return 0
}

func (m *BControllerNewServerAddr) GetControllerChannelNo() int32 {
	if m != nil {
		return m.ControllerChannelNo
	}
	return 0
}

func (m *BControllerNewServerAddr) GetIp() string {
	if m != nil {
		return m.Ip
	}
	return ""
}

func (m *BControllerNewServerAddr) GetPort() int32 {
	if m != nil {
		return m.Port
	}
	return 0
}

// 信息上报
// bmsg.cmd=21
type BInfoReporting struct {
	// 本机设备编号
	DeviceID uint32 `protobuf:"varint,1,opt,name=deviceID,proto3" json:"deviceID,omitempty"`
	// 1：开机上报 2：调试上报 3：定时上报
	Type uint32 `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`
	// sim卡iccid 20位
	Iccid string `protobuf:"bytes,3,opt,name=iccid,proto3" json:"iccid,omitempty"`
	// 格式：Vx.x.x_yyMMdd V1.0.1_231031
	SoftwareVersion string `protobuf:"bytes,4,opt,name=softwareVersion,proto3" json:"softwareVersion,omitempty"`
	// yyyy-mm-dd HH:MM:SS
	DataVersion string `protobuf:"bytes,5,opt,name=dataVersion,proto3" json:"dataVersion,omitempty"`
	// 设备时间yyyy-mm-dd HH:MM:SS
	Time string `protobuf:"bytes,6,opt,name=time,proto3" json:"time,omitempty"`
	// 设备电池电压单位mv
	Battery uint32 `protobuf:"varint,7,opt,name=battery,proto3" json:"battery,omitempty"`
	// 设备4G模块场强信号幅度 单位dbm
	Signal int32 `protobuf:"zigzag32,8,opt,name=signal,proto3" json:"signal,omitempty"`
	// Bit0：报警锁定(0:正常，1:报警锁定)
	// Bit1：RTC时钟状态(0:正常，1:故障)
	// Bit2：GPS模块状态(0:正常，1:故障)
	// Bit3：三轴传感器状态(0:正常，1:故障)
	// Bit4：电池状态(0:正常，1:故障)
	// Bit5：摄像头状态(0:正常，1:故障)(预留)
	// Bit6：红外探头状态(0:正常，1:故障)(预留)
	// Bit7-Bit31：预留
	State uint32 `protobuf:"varint,9,opt,name=state,proto3" json:"state,omitempty"`
	// Bit0：位移报警(0:正常，1:报警)
	// Bit1：震动报警(0:正常，1:报警)
	// Bit2：倾斜报警(0:正常，1:报警)
	// Bit3：红外报警(0:正常，1:报警)(预留)
	// Bit4-Bit31：预留
	Alarmstate uint32 `protobuf:"varint,10,opt,name=alarmstate,proto3" json:"alarmstate,omitempty"`
	// 设备温度
	Temp float32 `protobuf:"fixed32,11,opt,name=temp,proto3" json:"temp,omitempty"`
	// 湿度 单位：%RH
	Rh float32 `protobuf:"fixed32,12,opt,name=rh,proto3" json:"rh,omitempty"`
}

func (m *BInfoReporting) Reset()         { *m = BInfoReporting{} }
func (m *BInfoReporting) String() string { return proto.CompactTextString(m) }
func (*BInfoReporting) ProtoMessage()    {}
func (*BInfoReporting) Descriptor() ([]byte, []int) {
	return fileDescriptor_ed7f10298fa1d90f, []int{9}
}
func (m *BInfoReporting) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *BInfoReporting) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_BInfoReporting.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *BInfoReporting) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BInfoReporting.Merge(m, src)
}
func (m *BInfoReporting) XXX_Size() int {
	return m.Size()
}
func (m *BInfoReporting) XXX_DiscardUnknown() {
	xxx_messageInfo_BInfoReporting.DiscardUnknown(m)
}

var xxx_messageInfo_BInfoReporting proto.InternalMessageInfo

func (m *BInfoReporting) GetDeviceID() uint32 {
	if m != nil {
		return m.DeviceID
	}
	return 0
}

func (m *BInfoReporting) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *BInfoReporting) GetIccid() string {
	if m != nil {
		return m.Iccid
	}
	return ""
}

func (m *BInfoReporting) GetSoftwareVersion() string {
	if m != nil {
		return m.SoftwareVersion
	}
	return ""
}

func (m *BInfoReporting) GetDataVersion() string {
	if m != nil {
		return m.DataVersion
	}
	return ""
}

func (m *BInfoReporting) GetTime() string {
	if m != nil {
		return m.Time
	}
	return ""
}

func (m *BInfoReporting) GetBattery() uint32 {
	if m != nil {
		return m.Battery
	}
	return 0
}

func (m *BInfoReporting) GetSignal() int32 {
	if m != nil {
		return m.Signal
	}
	return 0
}

func (m *BInfoReporting) GetState() uint32 {
	if m != nil {
		return m.State
	}
	return 0
}

func (m *BInfoReporting) GetAlarmstate() uint32 {
	if m != nil {
		return m.Alarmstate
	}
	return 0
}

func (m *BInfoReporting) GetTemp() float32 {
	if m != nil {
		return m.Temp
	}
	return 0
}

func (m *BInfoReporting) GetRh() float32 {
	if m != nil {
		return m.Rh
	}
	return 0
}

// 震动报警参数
type BVamp struct {
	// 震动幅度
	Amplitude uint32 `protobuf:"varint,1,opt,name=amplitude,proto3" json:"amplitude,omitempty"`
	// 震动持续时间 s
	Duration uint32 `protobuf:"varint,2,opt,name=duration,proto3" json:"duration,omitempty"`
}

func (m *BVamp) Reset()         { *m = BVamp{} }
func (m *BVamp) String() string { return proto.CompactTextString(m) }
func (*BVamp) ProtoMessage()    {}
func (*BVamp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ed7f10298fa1d90f, []int{10}
}
func (m *BVamp) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *BVamp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_BVamp.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *BVamp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BVamp.Merge(m, src)
}
func (m *BVamp) XXX_Size() int {
	return m.Size()
}
func (m *BVamp) XXX_DiscardUnknown() {
	xxx_messageInfo_BVamp.DiscardUnknown(m)
}

var xxx_messageInfo_BVamp proto.InternalMessageInfo

func (m *BVamp) GetAmplitude() uint32 {
	if m != nil {
		return m.Amplitude
	}
	return 0
}

func (m *BVamp) GetDuration() uint32 {
	if m != nil {
		return m.Duration
	}
	return 0
}

// 报警上报
// bmsg.cmd=22
type BAlarmReporting struct {
	// 本机设备编号
	DeviceID uint32 `protobuf:"varint,1,opt,name=deviceID,proto3" json:"deviceID,omitempty"`
	// 1：开机上报 2：调试上报 3：报警上报
	Type uint32 `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`
	// Bit0：位移报警(0:正常，1:报警)
	// Bit1：震动报警(0:正常，1:报警)
	// Bit2：倾斜报警(0:正常，1:报警)
	// Bit3：红外报警(0:正常，1:报警)(预留)
	// Bit4-Bit31：预留
	Alarmstate uint32 `protobuf:"varint,3,opt,name=alarmstate,proto3" json:"alarmstate,omitempty"`
	// 定位坐标
	Locate *BGPS `protobuf:"bytes,4,opt,name=locate,proto3" json:"locate,omitempty"`
	// 倾斜角度，单位：度
	Attitude uint32 `protobuf:"varint,5,opt,name=attitude,proto3" json:"attitude,omitempty"`
	// 震动报警
	Vibration *BVamp `protobuf:"bytes,6,opt,name=vibration,proto3" json:"vibration,omitempty"`
	// 红外预留
	Infrared uint32 `protobuf:"varint,7,opt,name=infrared,proto3" json:"infrared,omitempty"`
	// 摄像头预留
	Camera uint32 `protobuf:"varint,8,opt,name=camera,proto3" json:"camera,omitempty"`
	// 其他（预留）
	Reserve []byte `protobuf:"bytes,9,opt,name=reserve,proto3" json:"reserve,omitempty"`
}

func (m *BAlarmReporting) Reset()         { *m = BAlarmReporting{} }
func (m *BAlarmReporting) String() string { return proto.CompactTextString(m) }
func (*BAlarmReporting) ProtoMessage()    {}
func (*BAlarmReporting) Descriptor() ([]byte, []int) {
	return fileDescriptor_ed7f10298fa1d90f, []int{11}
}
func (m *BAlarmReporting) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *BAlarmReporting) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_BAlarmReporting.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *BAlarmReporting) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BAlarmReporting.Merge(m, src)
}
func (m *BAlarmReporting) XXX_Size() int {
	return m.Size()
}
func (m *BAlarmReporting) XXX_DiscardUnknown() {
	xxx_messageInfo_BAlarmReporting.DiscardUnknown(m)
}

var xxx_messageInfo_BAlarmReporting proto.InternalMessageInfo

func (m *BAlarmReporting) GetDeviceID() uint32 {
	if m != nil {
		return m.DeviceID
	}
	return 0
}

func (m *BAlarmReporting) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *BAlarmReporting) GetAlarmstate() uint32 {
	if m != nil {
		return m.Alarmstate
	}
	return 0
}

func (m *BAlarmReporting) GetLocate() *BGPS {
	if m != nil {
		return m.Locate
	}
	return nil
}

func (m *BAlarmReporting) GetAttitude() uint32 {
	if m != nil {
		return m.Attitude
	}
	return 0
}

func (m *BAlarmReporting) GetVibration() *BVamp {
	if m != nil {
		return m.Vibration
	}
	return nil
}

func (m *BAlarmReporting) GetInfrared() uint32 {
	if m != nil {
		return m.Infrared
	}
	return 0
}

func (m *BAlarmReporting) GetCamera() uint32 {
	if m != nil {
		return m.Camera
	}
	return 0
}

func (m *BAlarmReporting) GetReserve() []byte {
	if m != nil {
		return m.Reserve
	}
	return nil
}

// 报警解除
// bmsg.cmd=33
type BAlarmClear struct {
	// 目标设备编号
	DeviceID uint32 `protobuf:"varint,1,opt,name=deviceID,proto3" json:"deviceID,omitempty"`
	// 1：报警解除 2：报警锁定
	Response uint32 `protobuf:"varint,2,opt,name=response,proto3" json:"response,omitempty"`
}

func (m *BAlarmClear) Reset()         { *m = BAlarmClear{} }
func (m *BAlarmClear) String() string { return proto.CompactTextString(m) }
func (*BAlarmClear) ProtoMessage()    {}
func (*BAlarmClear) Descriptor() ([]byte, []int) {
	return fileDescriptor_ed7f10298fa1d90f, []int{12}
}
func (m *BAlarmClear) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *BAlarmClear) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_BAlarmClear.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *BAlarmClear) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BAlarmClear.Merge(m, src)
}
func (m *BAlarmClear) XXX_Size() int {
	return m.Size()
}
func (m *BAlarmClear) XXX_DiscardUnknown() {
	xxx_messageInfo_BAlarmClear.DiscardUnknown(m)
}

var xxx_messageInfo_BAlarmClear proto.InternalMessageInfo

func (m *BAlarmClear) GetDeviceID() uint32 {
	if m != nil {
		return m.DeviceID
	}
	return 0
}

func (m *BAlarmClear) GetResponse() uint32 {
	if m != nil {
		return m.Response
	}
	return 0
}

// 参数更新
// bmsg.cmd=24
type BDataUpdate struct {
	// 目标设备编号
	DeviceID uint32 `protobuf:"varint,1,opt,name=deviceID,proto3" json:"deviceID,omitempty"`
	// Bit0：解除报警锁定 Bit1：更新参数 Bit2：校准时间
	Type uint32 `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`
	// 版本yyyy-mm-dd HH:MM:SS
	DataVersion string `protobuf:"bytes,3,opt,name=dataVersion,proto3" json:"dataVersion,omitempty"`
	// 服务器地址  xxx.xxx.xxx.xxx:xxxx   xxxxxx.com:xxxx
	Addr string `protobuf:"bytes,4,opt,name=addr,proto3" json:"addr,omitempty"`
	// 定时上报基准时间HH:MM:SS
	Timerbase string `protobuf:"bytes,5,opt,name=timerbase,proto3" json:"timerbase,omitempty"`
	// timer 6h、8h、12h、24h
	Timer uint32 `protobuf:"varint,6,opt,name=timer,proto3" json:"timer,omitempty"`
	// 姿态报警阈值
	Attitude uint32 `protobuf:"varint,7,opt,name=attitude,proto3" json:"attitude,omitempty"`
	// 位移报警阈值
	Dirft uint32 `protobuf:"varint,8,opt,name=dirft,proto3" json:"dirft,omitempty"`
	// 震动报警阈值
	Vibration *BVamp `protobuf:"bytes,9,opt,name=vibration,proto3" json:"vibration,omitempty"`
	// 红外报警阈值
	Infrared uint32 `protobuf:"varint,10,opt,name=infrared,proto3" json:"infrared,omitempty"`
	// 延迟休眠时间 10s
	T1 uint32 `protobuf:"varint,11,opt,name=t1,proto3" json:"t1,omitempty"`
	// 调试模式时间 120s
	T2 uint32 `protobuf:"varint,12,opt,name=t2,proto3" json:"t2,omitempty"`
	// 报警间隔 10s
	T3 uint32 `protobuf:"varint,13,opt,name=t3,proto3" json:"t3,omitempty"`
	// 报警次数 10
	N1 uint32 `protobuf:"varint,14,opt,name=n1,proto3" json:"n1,omitempty"`
}

func (m *BDataUpdate) Reset()         { *m = BDataUpdate{} }
func (m *BDataUpdate) String() string { return proto.CompactTextString(m) }
func (*BDataUpdate) ProtoMessage()    {}
func (*BDataUpdate) Descriptor() ([]byte, []int) {
	return fileDescriptor_ed7f10298fa1d90f, []int{13}
}
func (m *BDataUpdate) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *BDataUpdate) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_BDataUpdate.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *BDataUpdate) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BDataUpdate.Merge(m, src)
}
func (m *BDataUpdate) XXX_Size() int {
	return m.Size()
}
func (m *BDataUpdate) XXX_DiscardUnknown() {
	xxx_messageInfo_BDataUpdate.DiscardUnknown(m)
}

var xxx_messageInfo_BDataUpdate proto.InternalMessageInfo

func (m *BDataUpdate) GetDeviceID() uint32 {
	if m != nil {
		return m.DeviceID
	}
	return 0
}

func (m *BDataUpdate) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *BDataUpdate) GetDataVersion() string {
	if m != nil {
		return m.DataVersion
	}
	return ""
}

func (m *BDataUpdate) GetAddr() string {
	if m != nil {
		return m.Addr
	}
	return ""
}

func (m *BDataUpdate) GetTimerbase() string {
	if m != nil {
		return m.Timerbase
	}
	return ""
}

func (m *BDataUpdate) GetTimer() uint32 {
	if m != nil {
		return m.Timer
	}
	return 0
}

func (m *BDataUpdate) GetAttitude() uint32 {
	if m != nil {
		return m.Attitude
	}
	return 0
}

func (m *BDataUpdate) GetDirft() uint32 {
	if m != nil {
		return m.Dirft
	}
	return 0
}

func (m *BDataUpdate) GetVibration() *BVamp {
	if m != nil {
		return m.Vibration
	}
	return nil
}

func (m *BDataUpdate) GetInfrared() uint32 {
	if m != nil {
		return m.Infrared
	}
	return 0
}

func (m *BDataUpdate) GetT1() uint32 {
	if m != nil {
		return m.T1
	}
	return 0
}

func (m *BDataUpdate) GetT2() uint32 {
	if m != nil {
		return m.T2
	}
	return 0
}

func (m *BDataUpdate) GetT3() uint32 {
	if m != nil {
		return m.T3
	}
	return 0
}

func (m *BDataUpdate) GetN1() uint32 {
	if m != nil {
		return m.N1
	}
	return 0
}

// 遥闭
// bmsg.cmd=27
type BShutDown struct {
	// 目标设备编号
	DeviceID uint32 `protobuf:"varint,1,opt,name=deviceID,proto3" json:"deviceID,omitempty"`
	// 0:遥活 1:遥闭  2:遥晕
	Type uint32 `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`
	// 0:遥活 1:遥闭  2:遥晕
	CamType uint32 `protobuf:"varint,3,opt,name=cam_type,json=camType,proto3" json:"cam_type,omitempty"`
}

func (m *BShutDown) Reset()         { *m = BShutDown{} }
func (m *BShutDown) String() string { return proto.CompactTextString(m) }
func (*BShutDown) ProtoMessage()    {}
func (*BShutDown) Descriptor() ([]byte, []int) {
	return fileDescriptor_ed7f10298fa1d90f, []int{14}
}
func (m *BShutDown) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *BShutDown) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_BShutDown.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *BShutDown) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BShutDown.Merge(m, src)
}
func (m *BShutDown) XXX_Size() int {
	return m.Size()
}
func (m *BShutDown) XXX_DiscardUnknown() {
	xxx_messageInfo_BShutDown.DiscardUnknown(m)
}

var xxx_messageInfo_BShutDown proto.InternalMessageInfo

func (m *BShutDown) GetDeviceID() uint32 {
	if m != nil {
		return m.DeviceID
	}
	return 0
}

func (m *BShutDown) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *BShutDown) GetCamType() uint32 {
	if m != nil {
		return m.CamType
	}
	return 0
}

func init() {
	proto.RegisterType((*Bmsg)(nil), "bysproto.Bmsg")
	proto.RegisterType((*BloginReq)(nil), "bysproto.BloginReq")
	proto.RegisterType((*BloginRes)(nil), "bysproto.BloginRes")
	proto.RegisterType((*BGPS)(nil), "bysproto.BGPS")
	proto.RegisterType((*BdeviceUpdate)(nil), "bysproto.BdeviceUpdate")
	proto.RegisterType((*ControllerStatus)(nil), "bysproto.ControllerStatus")
	proto.RegisterType((*BdeviceUpdateResponse)(nil), "bysproto.BdeviceUpdateResponse")
	proto.RegisterType((*BControllerUpdateChannel)(nil), "bysproto.BControllerUpdateChannel")
	proto.RegisterType((*BControllerNewServerAddr)(nil), "bysproto.BControllerNewServerAddr")
	proto.RegisterType((*BInfoReporting)(nil), "bysproto.BInfoReporting")
	proto.RegisterType((*BVamp)(nil), "bysproto.BVamp")
	proto.RegisterType((*BAlarmReporting)(nil), "bysproto.BAlarmReporting")
	proto.RegisterType((*BAlarmClear)(nil), "bysproto.BAlarmClear")
	proto.RegisterType((*BDataUpdate)(nil), "bysproto.BDataUpdate")
	proto.RegisterType((*BShutDown)(nil), "bysproto.BShutDown")
}

func init() { proto.RegisterFile("controller.proto", fileDescriptor_ed7f10298fa1d90f) }

var fileDescriptor_ed7f10298fa1d90f = []byte{
	// 1219 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x56, 0xbf, 0x93, 0xdb, 0xc4,
	0x17, 0x8f, 0xe4, 0x9f, 0xda, 0xb3, 0x9d, 0x58, 0xc9, 0x37, 0xa3, 0x6f, 0x86, 0xf1, 0x78, 0x54,
	0x64, 0x5c, 0x80, 0xef, 0x2e, 0xa9, 0xd2, 0x11, 0xd9, 0x47, 0xe2, 0x21, 0x38, 0x9e, 0x75, 0x48,
	0x41, 0xc3, 0xac, 0xa5, 0x3d, 0x5b, 0x73, 0x96, 0xd6, 0xac, 0xf6, 0xce, 0x98, 0x82, 0x92, 0x92,
	0xa1, 0xa1, 0xa4, 0x61, 0x86, 0x7f, 0x84, 0x8a, 0x32, 0x15, 0x43, 0xc9, 0xdc, 0xf1, 0x87, 0x30,
	0xef, 0xad, 0x64, 0xd9, 0x3e, 0x13, 0x38, 0x48, 0xe5, 0xf7, 0xf9, 0x3c, 0x69, 0xf7, 0xe9, 0xf3,
	0x3e, 0xfb, 0xd6, 0xe4, 0x8e, 0x2f, 0x62, 0x25, 0xc5, 0x7c, 0xce, 0x65, 0x77, 0x21, 0x85, 0x12,
	0x76, 0x75, 0xb2, 0x4a, 0x30, 0x72, 0xbf, 0x26, 0x45, 0x2f, 0x4a, 0xa6, 0xf6, 0x1d, 0x52, 0xe8,
	0x45, 0x81, 0x63, 0xb6, 0x8d, 0x4e, 0x85, 0x42, 0x68, 0x37, 0x88, 0x39, 0x14, 0x4e, 0xa9, 0x6d,
	0x74, 0xea, 0xd4, 0x1c, 0x0a, 0x78, 0x82, 0xf2, 0xc4, 0xb1, 0xda, 0x46, 0xa7, 0x49, 0x21, 0xb4,
	0x6d, 0x52, 0xf4, 0x44, 0xb0, 0x72, 0x48, 0xdb, 0xe8, 0xd4, 0x28, 0xc6, 0xf6, 0x7d, 0x52, 0x7e,
	0xb9, 0x50, 0x89, 0x92, 0xce, 0x41, 0xdb, 0xe8, 0x58, 0x34, 0x45, 0x29, 0x3f, 0x09, 0x63, 0xa7,
	0x86, 0x4f, 0xa7, 0xc8, 0xbd, 0x32, 0x89, 0xe5, 0xcd, 0xc5, 0x34, 0x8c, 0x29, 0xff, 0x02, 0x56,
	0x1c, 0xb2, 0x88, 0x3b, 0x06, 0xbe, 0x8b, 0xb1, 0xfd, 0x80, 0x54, 0x47, 0x2c, 0x49, 0x9e, 0xb3,
	0x64, 0x86, 0xe5, 0x59, 0x74, 0x8d, 0x6d, 0x97, 0xd4, 0x5e, 0xc0, 0xbb, 0xaf, 0xc2, 0x88, 0x8f,
	0x95, 0x74, 0x0a, 0x98, 0xdf, 0xe2, 0xa0, 0xee, 0xf1, 0x2a, 0x71, 0x8a, 0x98, 0x82, 0xd0, 0xbe,
	0x47, 0x4a, 0x23, 0xb1, 0xe4, 0x12, 0x3f, 0xae, 0x49, 0x35, 0xb0, 0xdf, 0x23, 0x56, 0x6f, 0xc6,
	0xe2, 0x98, 0xcf, 0x87, 0xc2, 0x29, 0x63, 0x26, 0x27, 0xec, 0x2e, 0xb1, 0x5f, 0x49, 0x16, 0x27,
	0xa7, 0x5c, 0xae, 0xc9, 0xc4, 0xa9, 0xb4, 0x0b, 0x9d, 0x26, 0xdd, 0x93, 0xb1, 0xdb, 0xe4, 0x60,
	0xc8, 0xd5, 0x52, 0xc8, 0xb3, 0x57, 0xab, 0x05, 0x77, 0xaa, 0x6d, 0xa3, 0x53, 0xa2, 0x9b, 0x94,
	0xdd, 0x22, 0xa4, 0xcf, 0x2f, 0x42, 0x9f, 0xe3, 0x03, 0x16, 0x3e, 0xb0, 0xc1, 0x80, 0x16, 0x83,
	0x4f, 0x4e, 0x06, 0xa8, 0xae, 0x45, 0x31, 0x86, 0xca, 0x07, 0xbd, 0xde, 0xa0, 0x9f, 0x8a, 0xab,
	0x01, 0xec, 0x15, 0x30, 0xc5, 0x5e, 0x73, 0x99, 0x84, 0x42, 0x0b, 0x6c, 0xd1, 0x4d, 0xca, 0xfd,
	0xc1, 0xc8, 0x55, 0xc6, 0xbe, 0xf5, 0x44, 0xa0, 0x55, 0x2e, 0x51, 0x8c, 0x41, 0xc9, 0xde, 0xda,
	0x25, 0x83, 0x7e, 0x6a, 0x84, 0x2d, 0x0e, 0x2a, 0x1e, 0x73, 0x79, 0xc1, 0x25, 0x48, 0x8b, 0xe2,
	0x59, 0x74, 0x83, 0xb1, 0x1f, 0x92, 0xc6, 0x78, 0x95, 0x28, 0x1e, 0x41, 0x7f, 0x96, 0x42, 0x06,
	0x28, 0xa3, 0x45, 0x77, 0x58, 0xe8, 0xc8, 0x89, 0x94, 0x4e, 0x45, 0x77, 0xe4, 0x44, 0x4a, 0xd7,
	0x23, 0x45, 0xef, 0xd9, 0x68, 0x0c, 0x99, 0x17, 0x22, 0xc6, 0xc2, 0x0c, 0x0a, 0x21, 0x32, 0x4c,
	0x61, 0x39, 0xc0, 0x30, 0x05, 0x4e, 0x7a, 0xce, 0xc3, 0xe9, 0x4c, 0x61, 0xb7, 0x9b, 0x34, 0x45,
	0xee, 0x4f, 0x05, 0x52, 0xf7, 0x02, 0xd4, 0xef, 0xd3, 0x45, 0xc0, 0x14, 0x3a, 0x47, 0xeb, 0x39,
	0xe8, 0xe3, 0x92, 0x4d, 0xba, 0xc6, 0x9b, 0x7e, 0x6f, 0x6a, 0xbf, 0xdf, 0x27, 0xe5, 0xb1, 0x62,
	0xea, 0x3c, 0xc9, 0xd6, 0xd5, 0xc8, 0x7e, 0x9f, 0x34, 0xf3, 0xfa, 0x7b, 0x33, 0xee, 0x9f, 0xbd,
	0xfc, 0x18, 0xdd, 0x54, 0xa2, 0xd7, 0x13, 0x76, 0x9b, 0x14, 0x9e, 0x8d, 0xc6, 0x28, 0xce, 0xc1,
	0xa3, 0x46, 0x37, 0x3b, 0x67, 0x5d, 0xf8, 0x3c, 0x0a, 0x29, 0x50, 0x7a, 0xc4, 0x24, 0x8b, 0xb2,
	0x76, 0x95, 0x71, 0xa9, 0x2d, 0x0e, 0xbc, 0x88, 0x18, 0x85, 0xd6, 0x3a, 0xe5, 0x84, 0xed, 0x90,
	0x4a, 0x2f, 0x0a, 0x30, 0x57, 0xc5, 0x5c, 0x06, 0xe1, 0x3d, 0xa8, 0x3a, 0x14, 0xf1, 0xa0, 0x9f,
	0x9e, 0xd4, 0x9c, 0xb0, 0x3b, 0xe4, 0x76, 0x0a, 0xb4, 0x0c, 0x43, 0x81, 0xe6, 0x6a, 0xd2, 0x5d,
	0xda, 0x3e, 0x22, 0x77, 0x75, 0xfc, 0x51, 0xc8, 0xe7, 0xc1, 0x58, 0x49, 0x1e, 0x4f, 0xd5, 0x0c,
	0x9d, 0xd5, 0xa4, 0xfb, 0x52, 0xb0, 0xb6, 0xa6, 0xf3, 0x33, 0x54, 0xd7, 0x6b, 0xef, 0xd0, 0xee,
	0xaf, 0x06, 0xb9, 0x93, 0xdb, 0x2a, 0x15, 0x79, 0xab, 0x70, 0xe3, 0x1f, 0x14, 0x6e, 0xee, 0x2f,
	0x7c, 0x7d, 0xb4, 0x0b, 0x7f, 0x79, 0xb4, 0x8b, 0xff, 0xf5, 0x68, 0xe7, 0x46, 0xa9, 0xe2, 0x70,
	0x4c, 0x91, 0xfb, 0xbd, 0x41, 0xfe, 0xb7, 0x65, 0x40, 0xca, 0x93, 0x85, 0x88, 0x93, 0xb7, 0x1b,
	0xf1, 0x5d, 0xb5, 0x4c, 0x9b, 0xe2, 0xe8, 0xcb, 0xfe, 0x11, 0x0e, 0x87, 0x1a, 0xcd, 0xa0, 0xfb,
	0x8d, 0x41, 0x1c, 0x2f, 0x57, 0x5c, 0xd7, 0x96, 0x7e, 0xcf, 0x3b, 0xdb, 0xde, 0x25, 0xb5, 0x21,
	0x5f, 0xe6, 0x2a, 0x6b, 0xfd, 0xb7, 0x38, 0xf7, 0xdb, 0xed, 0x42, 0x86, 0x7c, 0xa9, 0x87, 0xc7,
	0xd3, 0x20, 0x90, 0x7f, 0xe3, 0x80, 0x23, 0x72, 0x37, 0x7f, 0x31, 0xdf, 0x45, 0xbb, 0x60, 0x5f,
	0x0a, 0xae, 0xaf, 0xc1, 0x22, 0xbd, 0x10, 0xcc, 0xc1, 0x02, 0x86, 0xde, 0x48, 0x48, 0x95, 0xb6,
	0x1f, 0x63, 0xf7, 0x67, 0x93, 0x34, 0xbc, 0x41, 0x7c, 0x2a, 0x28, 0x5f, 0x08, 0xa9, 0xc2, 0x78,
	0x0a, 0xad, 0x0a, 0x36, 0x5b, 0x55, 0xa7, 0x6b, 0x0c, 0x4b, 0x28, 0x98, 0xd5, 0x26, 0xf2, 0x18,
	0x83, 0xe1, 0x42, 0xdf, 0x0f, 0x83, 0x74, 0x27, 0x0d, 0x40, 0xb7, 0x44, 0x9c, 0xaa, 0x25, 0x93,
	0x3c, 0x3b, 0xe6, 0xfa, 0xfe, 0xd9, 0xa5, 0x77, 0x67, 0x77, 0xe9, 0xda, 0xec, 0xc6, 0x5d, 0xe1,
	0xa8, 0xeb, 0x59, 0x8a, 0x31, 0x34, 0x7b, 0xc2, 0x94, 0xe2, 0x72, 0x85, 0xd3, 0xa1, 0x4e, 0x33,
	0x08, 0xe6, 0x4c, 0xc2, 0x69, 0xcc, 0xe6, 0x68, 0xce, 0x26, 0x4d, 0x11, 0xd4, 0x99, 0x28, 0xa6,
	0xf4, 0x45, 0x53, 0xa7, 0x1a, 0xc0, 0x44, 0x67, 0x73, 0x26, 0x23, 0x9d, 0x22, 0x98, 0xda, 0x60,
	0x70, 0x6f, 0x1e, 0x2d, 0xd0, 0x51, 0x26, 0xc5, 0x18, 0x84, 0x95, 0x7a, 0x14, 0x98, 0xd4, 0x94,
	0x33, 0xf7, 0x29, 0x29, 0x79, 0xaf, 0x59, 0xb4, 0x80, 0x0e, 0xb2, 0x68, 0x31, 0x0f, 0xd5, 0x79,
	0x7a, 0xb7, 0xd4, 0x69, 0x4e, 0xa0, 0xb0, 0xe7, 0x12, 0xfb, 0x99, 0x0a, 0xb8, 0xc6, 0xee, 0x8f,
	0x26, 0xb9, 0xed, 0x3d, 0x85, 0x6d, 0xff, 0x7d, 0x23, 0xb6, 0x3f, 0xa5, 0x70, 0xed, 0x53, 0x1e,
	0x92, 0xf2, 0x5c, 0xf8, 0x90, 0x2b, 0xee, 0x9d, 0xcd, 0x69, 0x16, 0xf6, 0x65, 0x4a, 0xe9, 0x8f,
	0xd0, 0x7f, 0x7e, 0xd6, 0xd8, 0xfe, 0x80, 0x58, 0x17, 0xe1, 0x24, 0xfd, 0x88, 0x32, 0x2e, 0x73,
	0x7b, 0x63, 0x19, 0x50, 0x81, 0xe6, 0x4f, 0xc0, 0x52, 0x61, 0x7c, 0x2a, 0x99, 0xe4, 0x41, 0xda,
	0xa6, 0x35, 0x86, 0x3e, 0xf9, 0x2c, 0xe2, 0x92, 0x65, 0x43, 0x44, 0x23, 0xe8, 0xac, 0xe4, 0x09,
	0x1c, 0x0b, 0xec, 0x54, 0x8d, 0x66, 0xd0, 0x3d, 0x21, 0x07, 0x5a, 0xa3, 0xde, 0x9c, 0x33, 0xf9,
	0x56, 0x7d, 0x1e, 0x90, 0xaa, 0x4c, 0x67, 0x4f, 0xa6, 0x75, 0x86, 0xdd, 0x3f, 0x4c, 0x72, 0xe0,
	0xf5, 0x99, 0x62, 0xf9, 0x25, 0x79, 0x23, 0x9d, 0x77, 0x0c, 0x5b, 0xd8, 0x6b, 0x58, 0x16, 0x04,
	0x32, 0x75, 0x3c, 0xc6, 0xe0, 0x0d, 0x30, 0xae, 0x9c, 0xb0, 0x24, 0xfb, 0xe7, 0x90, 0x13, 0x60,
	0x4e, 0x04, 0xa8, 0x69, 0x9d, 0x6a, 0xb0, 0xd5, 0x89, 0xca, 0x4e, 0x27, 0xee, 0x91, 0x52, 0x10,
	0xca, 0x53, 0x95, 0xaa, 0xa7, 0xc1, 0x76, 0x7f, 0xac, 0x1b, 0xf5, 0x87, 0xec, 0xf4, 0xa7, 0x41,
	0x4c, 0x75, 0x8c, 0xbe, 0xaf, 0x53, 0x53, 0x1d, 0x23, 0x7e, 0x84, 0xae, 0x07, 0xfc, 0x08, 0xf1,
	0x63, 0xbc, 0xe2, 0x00, 0x3f, 0x06, 0x1c, 0x1f, 0x3b, 0x0d, 0x8d, 0xe3, 0x63, 0xf7, 0x35, 0xb1,
	0xbc, 0xf1, 0xec, 0x5c, 0xf5, 0xc5, 0x32, 0xbe, 0xb1, 0xc6, 0xff, 0x27, 0x55, 0x9f, 0x45, 0x9f,
	0x23, 0xaf, 0x9d, 0x5c, 0xf1, 0x59, 0x04, 0xff, 0x0a, 0xbd, 0x0f, 0x7f, 0xb9, 0x6c, 0x19, 0x6f,
	0x2e, 0x5b, 0xc6, 0xef, 0x97, 0x2d, 0xe3, 0xbb, 0xab, 0xd6, 0xad, 0x37, 0x57, 0xad, 0x5b, 0xbf,
	0x5d, 0xb5, 0x6e, 0x7d, 0xf6, 0x70, 0x1a, 0xaa, 0xee, 0x59, 0xe8, 0xb3, 0xe0, 0xc9, 0x93, 0xae,
	0x2f, 0xa2, 0xc3, 0xe9, 0x57, 0x01, 0xbf, 0x38, 0x9c, 0xac, 0x92, 0xc3, 0x40, 0xf8, 0x87, 0x99,
	0x0c, 0x93, 0x32, 0xfe, 0x3c, 0xfe, 0x33, 0x00, 0x00, 0xff, 0xff, 0x05, 0x8d, 0xa8, 0x18, 0x16,
	0x0c, 0x00, 0x00,
}

func (m *Bmsg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Bmsg) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Bmsg) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Optbin) > 0 {
		i -= len(m.Optbin)
		copy(dAtA[i:], m.Optbin)
		i = encodeVarintController(dAtA, i, uint64(len(m.Optbin)))
		i--
		dAtA[i] = 0x62
	}
	if len(m.Optstr) > 0 {
		i -= len(m.Optstr)
		copy(dAtA[i:], m.Optstr)
		i = encodeVarintController(dAtA, i, uint64(len(m.Optstr)))
		i--
		dAtA[i] = 0x5a
	}
	if len(m.Body) > 0 {
		i -= len(m.Body)
		copy(dAtA[i:], m.Body)
		i = encodeVarintController(dAtA, i, uint64(len(m.Body)))
		i--
		dAtA[i] = 0x52
	}
	if m.Res != 0 {
		i = encodeVarintController(dAtA, i, uint64((uint32(m.Res)<<1)^uint32((m.Res>>31))))
		i--
		dAtA[i] = 0x48
	}
	if m.No != 0 {
		i = encodeVarintController(dAtA, i, uint64(m.No))
		i--
		dAtA[i] = 0x28
	}
	if m.Cmd != 0 {
		i -= 4
		encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(m.Cmd))
		i--
		dAtA[i] = 0x15
	}
	return len(dAtA) - i, nil
}

func (m *BloginReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BloginReq) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BloginReq) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.DataVersion) > 0 {
		i -= len(m.DataVersion)
		copy(dAtA[i:], m.DataVersion)
		i = encodeVarintController(dAtA, i, uint64(len(m.DataVersion)))
		i--
		dAtA[i] = 0x62
	}
	if len(m.ICCID) > 0 {
		i -= len(m.ICCID)
		copy(dAtA[i:], m.ICCID)
		i = encodeVarintController(dAtA, i, uint64(len(m.ICCID)))
		i--
		dAtA[i] = 0x5a
	}
	if len(m.IMEI) > 0 {
		i -= len(m.IMEI)
		copy(dAtA[i:], m.IMEI)
		i = encodeVarintController(dAtA, i, uint64(len(m.IMEI)))
		i--
		dAtA[i] = 0x52
	}
	if m.DeviceType != 0 {
		i = encodeVarintController(dAtA, i, uint64(m.DeviceType))
		i--
		dAtA[i] = 0x48
	}
	if m.NetworkType != 0 {
		i = encodeVarintController(dAtA, i, uint64(m.NetworkType))
		i--
		dAtA[i] = 0x40
	}
	if len(m.TransferChannelNos) > 0 {
		dAtA1 := make([]byte, len(m.TransferChannelNos)*5)
		var j2 int
		for _, num := range m.TransferChannelNos {
			x3 := (uint32(num) << 1) ^ uint32((num >> 31))
			for x3 >= 1<<7 {
				dAtA1[j2] = uint8(uint64(x3)&0x7f | 0x80)
				j2++
				x3 >>= 7
			}
			dAtA1[j2] = uint8(x3)
			j2++
		}
		i -= j2
		copy(dAtA[i:], dAtA1[:j2])
		i = encodeVarintController(dAtA, i, uint64(j2))
		i--
		dAtA[i] = 0x3a
	}
	if m.ChannelNo != 0 {
		i = encodeVarintController(dAtA, i, uint64((uint32(m.ChannelNo)<<1)^uint32((m.ChannelNo>>31))))
		i--
		dAtA[i] = 0x30
	}
	if m.Power != 0 {
		i = encodeVarintController(dAtA, i, uint64((uint32(m.Power)<<1)^uint32((m.Power>>31))))
		i--
		dAtA[i] = 0x28
	}
	if len(m.Sys) > 0 {
		i -= len(m.Sys)
		copy(dAtA[i:], m.Sys)
		i = encodeVarintController(dAtA, i, uint64(len(m.Sys)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.LoginTimeStr) > 0 {
		i -= len(m.LoginTimeStr)
		copy(dAtA[i:], m.LoginTimeStr)
		i = encodeVarintController(dAtA, i, uint64(len(m.LoginTimeStr)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.PassHash) > 0 {
		i -= len(m.PassHash)
		copy(dAtA[i:], m.PassHash)
		i = encodeVarintController(dAtA, i, uint64(len(m.PassHash)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Name) > 0 {
		i -= len(m.Name)
		copy(dAtA[i:], m.Name)
		i = encodeVarintController(dAtA, i, uint64(len(m.Name)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *BloginRes) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BloginRes) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BloginRes) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Err) > 0 {
		i -= len(m.Err)
		copy(dAtA[i:], m.Err)
		i = encodeVarintController(dAtA, i, uint64(len(m.Err)))
		i--
		dAtA[i] = 0x3a
	}
	if len(m.SystemPassword) > 0 {
		i -= len(m.SystemPassword)
		copy(dAtA[i:], m.SystemPassword)
		i = encodeVarintController(dAtA, i, uint64(len(m.SystemPassword)))
		i--
		dAtA[i] = 0x32
	}
	if len(m.ServerTime) > 0 {
		i -= len(m.ServerTime)
		copy(dAtA[i:], m.ServerTime)
		i = encodeVarintController(dAtA, i, uint64(len(m.ServerTime)))
		i--
		dAtA[i] = 0x2a
	}
	if m.ControllerID != 0 {
		i -= 4
		encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(m.ControllerID))
		i--
		dAtA[i] = 0x15
	}
	if m.Code != 0 {
		i = encodeVarintController(dAtA, i, uint64(m.Code))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *BGPS) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BGPS) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BGPS) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Height != 0 {
		i = encodeVarintController(dAtA, i, uint64((uint32(m.Height)<<1)^uint32((m.Height>>31))))
		i--
		dAtA[i] = 0x18
	}
	if m.Lat != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.Lat))))
		i--
		dAtA[i] = 0x11
	}
	if m.Lon != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.Lon))))
		i--
		dAtA[i] = 0x9
	}
	return len(dAtA) - i, nil
}

func (m *BdeviceUpdate) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BdeviceUpdate) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BdeviceUpdate) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.DeviceChannelNo != 0 {
		i = encodeVarintController(dAtA, i, uint64((uint32(m.DeviceChannelNo)<<1)^uint32((m.DeviceChannelNo>>31))))
		i--
		dAtA[i] = 0x68
	}
	if m.DeviceFieldStrength != 0 {
		i = encodeVarintController(dAtA, i, uint64((uint32(m.DeviceFieldStrength)<<1)^uint32((m.DeviceFieldStrength>>31))))
		i--
		dAtA[i] = 0x60
	}
	if m.StationDeviceNo != 0 {
		i = encodeVarintController(dAtA, i, uint64((uint32(m.StationDeviceNo)<<1)^uint32((m.StationDeviceNo>>31))))
		i--
		dAtA[i] = 0x50
	}
	if m.StationID != 0 {
		i = encodeVarintController(dAtA, i, uint64((uint32(m.StationID)<<1)^uint32((m.StationID>>31))))
		i--
		dAtA[i] = 0x48
	}
	if len(m.CmdTime) > 0 {
		i -= len(m.CmdTime)
		copy(dAtA[i:], m.CmdTime)
		i = encodeVarintController(dAtA, i, uint64(len(m.CmdTime)))
		i--
		dAtA[i] = 0x42
	}
	if len(m.ParamTime) > 0 {
		i -= len(m.ParamTime)
		copy(dAtA[i:], m.ParamTime)
		i = encodeVarintController(dAtA, i, uint64(len(m.ParamTime)))
		i--
		dAtA[i] = 0x3a
	}
	if m.ParamVersion != 0 {
		i = encodeVarintController(dAtA, i, uint64(m.ParamVersion))
		i--
		dAtA[i] = 0x30
	}
	if m.GPS != nil {
		{
			size, err := m.GPS.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintController(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x2a
	}
	if m.SystemPassCheckOK != 0 {
		i = encodeVarintController(dAtA, i, uint64(m.SystemPassCheckOK))
		i--
		dAtA[i] = 0x20
	}
	if m.Status != 0 {
		i = encodeVarintController(dAtA, i, uint64((uint32(m.Status)<<1)^uint32((m.Status>>31))))
		i--
		dAtA[i] = 0x18
	}
	if m.Cmd != 0 {
		i = encodeVarintController(dAtA, i, uint64((uint32(m.Cmd)<<1)^uint32((m.Cmd>>31))))
		i--
		dAtA[i] = 0x10
	}
	if m.DeviceID != 0 {
		i = encodeVarintController(dAtA, i, uint64((uint32(m.DeviceID)<<1)^uint32((m.DeviceID>>31))))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *ControllerStatus) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ControllerStatus) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ControllerStatus) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Status != 0 {
		i = encodeVarintController(dAtA, i, uint64(m.Status))
		i--
		dAtA[i] = 0x40
	}
	if len(m.TransferChannelNos) > 0 {
		dAtA5 := make([]byte, len(m.TransferChannelNos)*5)
		var j6 int
		for _, num := range m.TransferChannelNos {
			x7 := (uint32(num) << 1) ^ uint32((num >> 31))
			for x7 >= 1<<7 {
				dAtA5[j6] = uint8(uint64(x7)&0x7f | 0x80)
				j6++
				x7 >>= 7
			}
			dAtA5[j6] = uint8(x7)
			j6++
		}
		i -= j6
		copy(dAtA[i:], dAtA5[:j6])
		i = encodeVarintController(dAtA, i, uint64(j6))
		i--
		dAtA[i] = 0x3a
	}
	if m.ChannelNo != 0 {
		i = encodeVarintController(dAtA, i, uint64((uint32(m.ChannelNo)<<1)^uint32((m.ChannelNo>>31))))
		i--
		dAtA[i] = 0x20
	}
	if m.Power != 0 {
		i = encodeVarintController(dAtA, i, uint64((uint32(m.Power)<<1)^uint32((m.Power>>31))))
		i--
		dAtA[i] = 0x18
	}
	if m.StationDeviceNo != 0 {
		i = encodeVarintController(dAtA, i, uint64((uint32(m.StationDeviceNo)<<1)^uint32((m.StationDeviceNo>>31))))
		i--
		dAtA[i] = 0x10
	}
	if m.StationID != 0 {
		i = encodeVarintController(dAtA, i, uint64((uint32(m.StationID)<<1)^uint32((m.StationID>>31))))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *BdeviceUpdateResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BdeviceUpdateResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BdeviceUpdateResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Cmd0XD0) > 0 {
		i -= len(m.Cmd0XD0)
		copy(dAtA[i:], m.Cmd0XD0)
		i = encodeVarintController(dAtA, i, uint64(len(m.Cmd0XD0)))
		i--
		dAtA[i] = 0x5a
	}
	if m.StationDeviceNo != 0 {
		i = encodeVarintController(dAtA, i, uint64((uint32(m.StationDeviceNo)<<1)^uint32((m.StationDeviceNo>>31))))
		i--
		dAtA[i] = 0x50
	}
	if m.StationID != 0 {
		i = encodeVarintController(dAtA, i, uint64((uint32(m.StationID)<<1)^uint32((m.StationID>>31))))
		i--
		dAtA[i] = 0x48
	}
	if m.DeviceID != 0 {
		i = encodeVarintController(dAtA, i, uint64((uint32(m.DeviceID)<<1)^uint32((m.DeviceID>>31))))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *BControllerUpdateChannel) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BControllerUpdateChannel) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BControllerUpdateChannel) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.StationDeviceNo != 0 {
		i = encodeVarintController(dAtA, i, uint64((uint32(m.StationDeviceNo)<<1)^uint32((m.StationDeviceNo>>31))))
		i--
		dAtA[i] = 0x50
	}
	if m.StationID != 0 {
		i = encodeVarintController(dAtA, i, uint64((uint32(m.StationID)<<1)^uint32((m.StationID>>31))))
		i--
		dAtA[i] = 0x48
	}
	if m.NewChannelNo != 0 {
		i = encodeVarintController(dAtA, i, uint64((uint32(m.NewChannelNo)<<1)^uint32((m.NewChannelNo>>31))))
		i--
		dAtA[i] = 0x18
	}
	return len(dAtA) - i, nil
}

func (m *BControllerNewServerAddr) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BControllerNewServerAddr) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BControllerNewServerAddr) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Port != 0 {
		i = encodeVarintController(dAtA, i, uint64((uint32(m.Port)<<1)^uint32((m.Port>>31))))
		i--
		dAtA[i] = 0x20
	}
	if len(m.Ip) > 0 {
		i -= len(m.Ip)
		copy(dAtA[i:], m.Ip)
		i = encodeVarintController(dAtA, i, uint64(len(m.Ip)))
		i--
		dAtA[i] = 0x1a
	}
	if m.ControllerChannelNo != 0 {
		i = encodeVarintController(dAtA, i, uint64((uint32(m.ControllerChannelNo)<<1)^uint32((m.ControllerChannelNo>>31))))
		i--
		dAtA[i] = 0x10
	}
	if m.StationID != 0 {
		i = encodeVarintController(dAtA, i, uint64((uint32(m.StationID)<<1)^uint32((m.StationID>>31))))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *BInfoReporting) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BInfoReporting) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BInfoReporting) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Rh != 0 {
		i -= 4
		encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(math.Float32bits(float32(m.Rh))))
		i--
		dAtA[i] = 0x65
	}
	if m.Temp != 0 {
		i -= 4
		encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(math.Float32bits(float32(m.Temp))))
		i--
		dAtA[i] = 0x5d
	}
	if m.Alarmstate != 0 {
		i = encodeVarintController(dAtA, i, uint64(m.Alarmstate))
		i--
		dAtA[i] = 0x50
	}
	if m.State != 0 {
		i = encodeVarintController(dAtA, i, uint64(m.State))
		i--
		dAtA[i] = 0x48
	}
	if m.Signal != 0 {
		i = encodeVarintController(dAtA, i, uint64((uint32(m.Signal)<<1)^uint32((m.Signal>>31))))
		i--
		dAtA[i] = 0x40
	}
	if m.Battery != 0 {
		i = encodeVarintController(dAtA, i, uint64(m.Battery))
		i--
		dAtA[i] = 0x38
	}
	if len(m.Time) > 0 {
		i -= len(m.Time)
		copy(dAtA[i:], m.Time)
		i = encodeVarintController(dAtA, i, uint64(len(m.Time)))
		i--
		dAtA[i] = 0x32
	}
	if len(m.DataVersion) > 0 {
		i -= len(m.DataVersion)
		copy(dAtA[i:], m.DataVersion)
		i = encodeVarintController(dAtA, i, uint64(len(m.DataVersion)))
		i--
		dAtA[i] = 0x2a
	}
	if len(m.SoftwareVersion) > 0 {
		i -= len(m.SoftwareVersion)
		copy(dAtA[i:], m.SoftwareVersion)
		i = encodeVarintController(dAtA, i, uint64(len(m.SoftwareVersion)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.Iccid) > 0 {
		i -= len(m.Iccid)
		copy(dAtA[i:], m.Iccid)
		i = encodeVarintController(dAtA, i, uint64(len(m.Iccid)))
		i--
		dAtA[i] = 0x1a
	}
	if m.Type != 0 {
		i = encodeVarintController(dAtA, i, uint64(m.Type))
		i--
		dAtA[i] = 0x10
	}
	if m.DeviceID != 0 {
		i = encodeVarintController(dAtA, i, uint64(m.DeviceID))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *BVamp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BVamp) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BVamp) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Duration != 0 {
		i = encodeVarintController(dAtA, i, uint64(m.Duration))
		i--
		dAtA[i] = 0x10
	}
	if m.Amplitude != 0 {
		i = encodeVarintController(dAtA, i, uint64(m.Amplitude))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *BAlarmReporting) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BAlarmReporting) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BAlarmReporting) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Reserve) > 0 {
		i -= len(m.Reserve)
		copy(dAtA[i:], m.Reserve)
		i = encodeVarintController(dAtA, i, uint64(len(m.Reserve)))
		i--
		dAtA[i] = 0x4a
	}
	if m.Camera != 0 {
		i = encodeVarintController(dAtA, i, uint64(m.Camera))
		i--
		dAtA[i] = 0x40
	}
	if m.Infrared != 0 {
		i = encodeVarintController(dAtA, i, uint64(m.Infrared))
		i--
		dAtA[i] = 0x38
	}
	if m.Vibration != nil {
		{
			size, err := m.Vibration.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintController(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x32
	}
	if m.Attitude != 0 {
		i = encodeVarintController(dAtA, i, uint64(m.Attitude))
		i--
		dAtA[i] = 0x28
	}
	if m.Locate != nil {
		{
			size, err := m.Locate.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintController(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x22
	}
	if m.Alarmstate != 0 {
		i = encodeVarintController(dAtA, i, uint64(m.Alarmstate))
		i--
		dAtA[i] = 0x18
	}
	if m.Type != 0 {
		i = encodeVarintController(dAtA, i, uint64(m.Type))
		i--
		dAtA[i] = 0x10
	}
	if m.DeviceID != 0 {
		i = encodeVarintController(dAtA, i, uint64(m.DeviceID))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *BAlarmClear) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BAlarmClear) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BAlarmClear) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Response != 0 {
		i = encodeVarintController(dAtA, i, uint64(m.Response))
		i--
		dAtA[i] = 0x10
	}
	if m.DeviceID != 0 {
		i = encodeVarintController(dAtA, i, uint64(m.DeviceID))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *BDataUpdate) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BDataUpdate) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BDataUpdate) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.N1 != 0 {
		i = encodeVarintController(dAtA, i, uint64(m.N1))
		i--
		dAtA[i] = 0x70
	}
	if m.T3 != 0 {
		i = encodeVarintController(dAtA, i, uint64(m.T3))
		i--
		dAtA[i] = 0x68
	}
	if m.T2 != 0 {
		i = encodeVarintController(dAtA, i, uint64(m.T2))
		i--
		dAtA[i] = 0x60
	}
	if m.T1 != 0 {
		i = encodeVarintController(dAtA, i, uint64(m.T1))
		i--
		dAtA[i] = 0x58
	}
	if m.Infrared != 0 {
		i = encodeVarintController(dAtA, i, uint64(m.Infrared))
		i--
		dAtA[i] = 0x50
	}
	if m.Vibration != nil {
		{
			size, err := m.Vibration.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintController(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x4a
	}
	if m.Dirft != 0 {
		i = encodeVarintController(dAtA, i, uint64(m.Dirft))
		i--
		dAtA[i] = 0x40
	}
	if m.Attitude != 0 {
		i = encodeVarintController(dAtA, i, uint64(m.Attitude))
		i--
		dAtA[i] = 0x38
	}
	if m.Timer != 0 {
		i = encodeVarintController(dAtA, i, uint64(m.Timer))
		i--
		dAtA[i] = 0x30
	}
	if len(m.Timerbase) > 0 {
		i -= len(m.Timerbase)
		copy(dAtA[i:], m.Timerbase)
		i = encodeVarintController(dAtA, i, uint64(len(m.Timerbase)))
		i--
		dAtA[i] = 0x2a
	}
	if len(m.Addr) > 0 {
		i -= len(m.Addr)
		copy(dAtA[i:], m.Addr)
		i = encodeVarintController(dAtA, i, uint64(len(m.Addr)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.DataVersion) > 0 {
		i -= len(m.DataVersion)
		copy(dAtA[i:], m.DataVersion)
		i = encodeVarintController(dAtA, i, uint64(len(m.DataVersion)))
		i--
		dAtA[i] = 0x1a
	}
	if m.Type != 0 {
		i = encodeVarintController(dAtA, i, uint64(m.Type))
		i--
		dAtA[i] = 0x10
	}
	if m.DeviceID != 0 {
		i = encodeVarintController(dAtA, i, uint64(m.DeviceID))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *BShutDown) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BShutDown) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BShutDown) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.CamType != 0 {
		i = encodeVarintController(dAtA, i, uint64(m.CamType))
		i--
		dAtA[i] = 0x18
	}
	if m.Type != 0 {
		i = encodeVarintController(dAtA, i, uint64(m.Type))
		i--
		dAtA[i] = 0x10
	}
	if m.DeviceID != 0 {
		i = encodeVarintController(dAtA, i, uint64(m.DeviceID))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func encodeVarintController(dAtA []byte, offset int, v uint64) int {
	offset -= sovController(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *Bmsg) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Cmd != 0 {
		n += 5
	}
	if m.No != 0 {
		n += 1 + sovController(uint64(m.No))
	}
	if m.Res != 0 {
		n += 1 + sozController(uint64(m.Res))
	}
	l = len(m.Body)
	if l > 0 {
		n += 1 + l + sovController(uint64(l))
	}
	l = len(m.Optstr)
	if l > 0 {
		n += 1 + l + sovController(uint64(l))
	}
	l = len(m.Optbin)
	if l > 0 {
		n += 1 + l + sovController(uint64(l))
	}
	return n
}

func (m *BloginReq) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Name)
	if l > 0 {
		n += 1 + l + sovController(uint64(l))
	}
	l = len(m.PassHash)
	if l > 0 {
		n += 1 + l + sovController(uint64(l))
	}
	l = len(m.LoginTimeStr)
	if l > 0 {
		n += 1 + l + sovController(uint64(l))
	}
	l = len(m.Sys)
	if l > 0 {
		n += 1 + l + sovController(uint64(l))
	}
	if m.Power != 0 {
		n += 1 + sozController(uint64(m.Power))
	}
	if m.ChannelNo != 0 {
		n += 1 + sozController(uint64(m.ChannelNo))
	}
	if len(m.TransferChannelNos) > 0 {
		l = 0
		for _, e := range m.TransferChannelNos {
			l += sozController(uint64(e))
		}
		n += 1 + sovController(uint64(l)) + l
	}
	if m.NetworkType != 0 {
		n += 1 + sovController(uint64(m.NetworkType))
	}
	if m.DeviceType != 0 {
		n += 1 + sovController(uint64(m.DeviceType))
	}
	l = len(m.IMEI)
	if l > 0 {
		n += 1 + l + sovController(uint64(l))
	}
	l = len(m.ICCID)
	if l > 0 {
		n += 1 + l + sovController(uint64(l))
	}
	l = len(m.DataVersion)
	if l > 0 {
		n += 1 + l + sovController(uint64(l))
	}
	return n
}

func (m *BloginRes) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Code != 0 {
		n += 1 + sovController(uint64(m.Code))
	}
	if m.ControllerID != 0 {
		n += 5
	}
	l = len(m.ServerTime)
	if l > 0 {
		n += 1 + l + sovController(uint64(l))
	}
	l = len(m.SystemPassword)
	if l > 0 {
		n += 1 + l + sovController(uint64(l))
	}
	l = len(m.Err)
	if l > 0 {
		n += 1 + l + sovController(uint64(l))
	}
	return n
}

func (m *BGPS) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Lon != 0 {
		n += 9
	}
	if m.Lat != 0 {
		n += 9
	}
	if m.Height != 0 {
		n += 1 + sozController(uint64(m.Height))
	}
	return n
}

func (m *BdeviceUpdate) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.DeviceID != 0 {
		n += 1 + sozController(uint64(m.DeviceID))
	}
	if m.Cmd != 0 {
		n += 1 + sozController(uint64(m.Cmd))
	}
	if m.Status != 0 {
		n += 1 + sozController(uint64(m.Status))
	}
	if m.SystemPassCheckOK != 0 {
		n += 1 + sovController(uint64(m.SystemPassCheckOK))
	}
	if m.GPS != nil {
		l = m.GPS.Size()
		n += 1 + l + sovController(uint64(l))
	}
	if m.ParamVersion != 0 {
		n += 1 + sovController(uint64(m.ParamVersion))
	}
	l = len(m.ParamTime)
	if l > 0 {
		n += 1 + l + sovController(uint64(l))
	}
	l = len(m.CmdTime)
	if l > 0 {
		n += 1 + l + sovController(uint64(l))
	}
	if m.StationID != 0 {
		n += 1 + sozController(uint64(m.StationID))
	}
	if m.StationDeviceNo != 0 {
		n += 1 + sozController(uint64(m.StationDeviceNo))
	}
	if m.DeviceFieldStrength != 0 {
		n += 1 + sozController(uint64(m.DeviceFieldStrength))
	}
	if m.DeviceChannelNo != 0 {
		n += 1 + sozController(uint64(m.DeviceChannelNo))
	}
	return n
}

func (m *ControllerStatus) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.StationID != 0 {
		n += 1 + sozController(uint64(m.StationID))
	}
	if m.StationDeviceNo != 0 {
		n += 1 + sozController(uint64(m.StationDeviceNo))
	}
	if m.Power != 0 {
		n += 1 + sozController(uint64(m.Power))
	}
	if m.ChannelNo != 0 {
		n += 1 + sozController(uint64(m.ChannelNo))
	}
	if len(m.TransferChannelNos) > 0 {
		l = 0
		for _, e := range m.TransferChannelNos {
			l += sozController(uint64(e))
		}
		n += 1 + sovController(uint64(l)) + l
	}
	if m.Status != 0 {
		n += 1 + sovController(uint64(m.Status))
	}
	return n
}

func (m *BdeviceUpdateResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.DeviceID != 0 {
		n += 1 + sozController(uint64(m.DeviceID))
	}
	if m.StationID != 0 {
		n += 1 + sozController(uint64(m.StationID))
	}
	if m.StationDeviceNo != 0 {
		n += 1 + sozController(uint64(m.StationDeviceNo))
	}
	l = len(m.Cmd0XD0)
	if l > 0 {
		n += 1 + l + sovController(uint64(l))
	}
	return n
}

func (m *BControllerUpdateChannel) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.NewChannelNo != 0 {
		n += 1 + sozController(uint64(m.NewChannelNo))
	}
	if m.StationID != 0 {
		n += 1 + sozController(uint64(m.StationID))
	}
	if m.StationDeviceNo != 0 {
		n += 1 + sozController(uint64(m.StationDeviceNo))
	}
	return n
}

func (m *BControllerNewServerAddr) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.StationID != 0 {
		n += 1 + sozController(uint64(m.StationID))
	}
	if m.ControllerChannelNo != 0 {
		n += 1 + sozController(uint64(m.ControllerChannelNo))
	}
	l = len(m.Ip)
	if l > 0 {
		n += 1 + l + sovController(uint64(l))
	}
	if m.Port != 0 {
		n += 1 + sozController(uint64(m.Port))
	}
	return n
}

func (m *BInfoReporting) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.DeviceID != 0 {
		n += 1 + sovController(uint64(m.DeviceID))
	}
	if m.Type != 0 {
		n += 1 + sovController(uint64(m.Type))
	}
	l = len(m.Iccid)
	if l > 0 {
		n += 1 + l + sovController(uint64(l))
	}
	l = len(m.SoftwareVersion)
	if l > 0 {
		n += 1 + l + sovController(uint64(l))
	}
	l = len(m.DataVersion)
	if l > 0 {
		n += 1 + l + sovController(uint64(l))
	}
	l = len(m.Time)
	if l > 0 {
		n += 1 + l + sovController(uint64(l))
	}
	if m.Battery != 0 {
		n += 1 + sovController(uint64(m.Battery))
	}
	if m.Signal != 0 {
		n += 1 + sozController(uint64(m.Signal))
	}
	if m.State != 0 {
		n += 1 + sovController(uint64(m.State))
	}
	if m.Alarmstate != 0 {
		n += 1 + sovController(uint64(m.Alarmstate))
	}
	if m.Temp != 0 {
		n += 5
	}
	if m.Rh != 0 {
		n += 5
	}
	return n
}

func (m *BVamp) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Amplitude != 0 {
		n += 1 + sovController(uint64(m.Amplitude))
	}
	if m.Duration != 0 {
		n += 1 + sovController(uint64(m.Duration))
	}
	return n
}

func (m *BAlarmReporting) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.DeviceID != 0 {
		n += 1 + sovController(uint64(m.DeviceID))
	}
	if m.Type != 0 {
		n += 1 + sovController(uint64(m.Type))
	}
	if m.Alarmstate != 0 {
		n += 1 + sovController(uint64(m.Alarmstate))
	}
	if m.Locate != nil {
		l = m.Locate.Size()
		n += 1 + l + sovController(uint64(l))
	}
	if m.Attitude != 0 {
		n += 1 + sovController(uint64(m.Attitude))
	}
	if m.Vibration != nil {
		l = m.Vibration.Size()
		n += 1 + l + sovController(uint64(l))
	}
	if m.Infrared != 0 {
		n += 1 + sovController(uint64(m.Infrared))
	}
	if m.Camera != 0 {
		n += 1 + sovController(uint64(m.Camera))
	}
	l = len(m.Reserve)
	if l > 0 {
		n += 1 + l + sovController(uint64(l))
	}
	return n
}

func (m *BAlarmClear) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.DeviceID != 0 {
		n += 1 + sovController(uint64(m.DeviceID))
	}
	if m.Response != 0 {
		n += 1 + sovController(uint64(m.Response))
	}
	return n
}

func (m *BDataUpdate) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.DeviceID != 0 {
		n += 1 + sovController(uint64(m.DeviceID))
	}
	if m.Type != 0 {
		n += 1 + sovController(uint64(m.Type))
	}
	l = len(m.DataVersion)
	if l > 0 {
		n += 1 + l + sovController(uint64(l))
	}
	l = len(m.Addr)
	if l > 0 {
		n += 1 + l + sovController(uint64(l))
	}
	l = len(m.Timerbase)
	if l > 0 {
		n += 1 + l + sovController(uint64(l))
	}
	if m.Timer != 0 {
		n += 1 + sovController(uint64(m.Timer))
	}
	if m.Attitude != 0 {
		n += 1 + sovController(uint64(m.Attitude))
	}
	if m.Dirft != 0 {
		n += 1 + sovController(uint64(m.Dirft))
	}
	if m.Vibration != nil {
		l = m.Vibration.Size()
		n += 1 + l + sovController(uint64(l))
	}
	if m.Infrared != 0 {
		n += 1 + sovController(uint64(m.Infrared))
	}
	if m.T1 != 0 {
		n += 1 + sovController(uint64(m.T1))
	}
	if m.T2 != 0 {
		n += 1 + sovController(uint64(m.T2))
	}
	if m.T3 != 0 {
		n += 1 + sovController(uint64(m.T3))
	}
	if m.N1 != 0 {
		n += 1 + sovController(uint64(m.N1))
	}
	return n
}

func (m *BShutDown) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.DeviceID != 0 {
		n += 1 + sovController(uint64(m.DeviceID))
	}
	if m.Type != 0 {
		n += 1 + sovController(uint64(m.Type))
	}
	if m.CamType != 0 {
		n += 1 + sovController(uint64(m.CamType))
	}
	return n
}

func sovController(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozController(x uint64) (n int) {
	return sovController(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *Bmsg) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowController
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Bmsg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Bmsg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 2:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field Cmd", wireType)
			}
			m.Cmd = 0
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			m.Cmd = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field No", wireType)
			}
			m.No = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.No |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Res", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.Res = v
		case 10:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Body", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthController
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthController
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Body = append(m.Body[:0], dAtA[iNdEx:postIndex]...)
			if m.Body == nil {
				m.Body = []byte{}
			}
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Optstr", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthController
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthController
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Optstr = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 12:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Optbin", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthController
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthController
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Optbin = append(m.Optbin[:0], dAtA[iNdEx:postIndex]...)
			if m.Optbin == nil {
				m.Optbin = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipController(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthController
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthController
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *BloginReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowController
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: BloginReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: BloginReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthController
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthController
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PassHash", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthController
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthController
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.PassHash = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LoginTimeStr", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthController
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthController
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.LoginTimeStr = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Sys", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthController
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthController
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Sys = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Power", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.Power = v
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ChannelNo", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.ChannelNo = v
		case 7:
			if wireType == 0 {
				var v int32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowController
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= int32(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
				m.TransferChannelNos = append(m.TransferChannelNos, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowController
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthController
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthController
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				var count int
				for _, integer := range dAtA[iNdEx:postIndex] {
					if integer < 128 {
						count++
					}
				}
				elementCount = count
				if elementCount != 0 && len(m.TransferChannelNos) == 0 {
					m.TransferChannelNos = make([]int32, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v int32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowController
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= int32(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
					m.TransferChannelNos = append(m.TransferChannelNos, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field TransferChannelNos", wireType)
			}
		case 8:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field NetworkType", wireType)
			}
			m.NetworkType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.NetworkType |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field DeviceType", wireType)
			}
			m.DeviceType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DeviceType |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field IMEI", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthController
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthController
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.IMEI = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ICCID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthController
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthController
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ICCID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 12:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DataVersion", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthController
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthController
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.DataVersion = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipController(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthController
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthController
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *BloginRes) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowController
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: BloginRes: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: BloginRes: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Code", wireType)
			}
			m.Code = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Code |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field ControllerID", wireType)
			}
			m.ControllerID = 0
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			m.ControllerID = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ServerTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthController
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthController
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ServerTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SystemPassword", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthController
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthController
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SystemPassword = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Err", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthController
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthController
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Err = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipController(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthController
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthController
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *BGPS) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowController
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: BGPS: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: BGPS: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field Lon", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.Lon = float64(math.Float64frombits(v))
		case 2:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field Lat", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.Lat = float64(math.Float64frombits(v))
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Height", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.Height = v
		default:
			iNdEx = preIndex
			skippy, err := skipController(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthController
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthController
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *BdeviceUpdate) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowController
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: BdeviceUpdate: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: BdeviceUpdate: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field DeviceID", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.DeviceID = v
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Cmd", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.Cmd = v
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.Status = v
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SystemPassCheckOK", wireType)
			}
			m.SystemPassCheckOK = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SystemPassCheckOK |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field GPS", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthController
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthController
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.GPS == nil {
				m.GPS = &BGPS{}
			}
			if err := m.GPS.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ParamVersion", wireType)
			}
			m.ParamVersion = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ParamVersion |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ParamTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthController
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthController
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ParamTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CmdTime", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthController
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthController
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CmdTime = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 9:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StationID", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.StationID = v
		case 10:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StationDeviceNo", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.StationDeviceNo = v
		case 12:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field DeviceFieldStrength", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.DeviceFieldStrength = v
		case 13:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field DeviceChannelNo", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.DeviceChannelNo = v
		default:
			iNdEx = preIndex
			skippy, err := skipController(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthController
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthController
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ControllerStatus) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowController
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ControllerStatus: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ControllerStatus: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StationID", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.StationID = v
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StationDeviceNo", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.StationDeviceNo = v
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Power", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.Power = v
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ChannelNo", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.ChannelNo = v
		case 7:
			if wireType == 0 {
				var v int32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowController
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= int32(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
				m.TransferChannelNos = append(m.TransferChannelNos, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowController
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthController
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthController
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				var count int
				for _, integer := range dAtA[iNdEx:postIndex] {
					if integer < 128 {
						count++
					}
				}
				elementCount = count
				if elementCount != 0 && len(m.TransferChannelNos) == 0 {
					m.TransferChannelNos = make([]int32, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v int32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowController
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= int32(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
					m.TransferChannelNos = append(m.TransferChannelNos, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field TransferChannelNos", wireType)
			}
		case 8:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipController(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthController
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthController
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *BdeviceUpdateResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowController
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: BdeviceUpdateResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: BdeviceUpdateResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field DeviceID", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.DeviceID = v
		case 9:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StationID", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.StationID = v
		case 10:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StationDeviceNo", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.StationDeviceNo = v
		case 11:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Cmd0XD0", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthController
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthController
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Cmd0XD0 = append(m.Cmd0XD0[:0], dAtA[iNdEx:postIndex]...)
			if m.Cmd0XD0 == nil {
				m.Cmd0XD0 = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipController(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthController
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthController
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *BControllerUpdateChannel) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowController
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: BControllerUpdateChannel: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: BControllerUpdateChannel: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field NewChannelNo", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.NewChannelNo = v
		case 9:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StationID", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.StationID = v
		case 10:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StationDeviceNo", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.StationDeviceNo = v
		default:
			iNdEx = preIndex
			skippy, err := skipController(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthController
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthController
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *BControllerNewServerAddr) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowController
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: BControllerNewServerAddr: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: BControllerNewServerAddr: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StationID", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.StationID = v
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ControllerChannelNo", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.ControllerChannelNo = v
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Ip", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthController
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthController
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Ip = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Port", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.Port = v
		default:
			iNdEx = preIndex
			skippy, err := skipController(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthController
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthController
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *BInfoReporting) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowController
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: BInfoReporting: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: BInfoReporting: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field DeviceID", wireType)
			}
			m.DeviceID = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DeviceID |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Iccid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthController
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthController
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Iccid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SoftwareVersion", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthController
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthController
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SoftwareVersion = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DataVersion", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthController
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthController
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.DataVersion = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Time", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthController
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthController
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Time = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Battery", wireType)
			}
			m.Battery = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Battery |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Signal", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			v = int32((uint32(v) >> 1) ^ uint32(((v&1)<<31)>>31))
			m.Signal = v
		case 9:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field State", wireType)
			}
			m.State = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.State |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Alarmstate", wireType)
			}
			m.Alarmstate = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Alarmstate |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 11:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field Temp", wireType)
			}
			var v uint32
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
			m.Temp = float32(math.Float32frombits(v))
		case 12:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rh", wireType)
			}
			var v uint32
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint32(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
			m.Rh = float32(math.Float32frombits(v))
		default:
			iNdEx = preIndex
			skippy, err := skipController(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthController
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthController
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *BVamp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowController
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: BVamp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: BVamp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Amplitude", wireType)
			}
			m.Amplitude = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Amplitude |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Duration", wireType)
			}
			m.Duration = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Duration |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipController(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthController
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthController
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *BAlarmReporting) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowController
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: BAlarmReporting: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: BAlarmReporting: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field DeviceID", wireType)
			}
			m.DeviceID = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DeviceID |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Alarmstate", wireType)
			}
			m.Alarmstate = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Alarmstate |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Locate", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthController
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthController
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Locate == nil {
				m.Locate = &BGPS{}
			}
			if err := m.Locate.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Attitude", wireType)
			}
			m.Attitude = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Attitude |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Vibration", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthController
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthController
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Vibration == nil {
				m.Vibration = &BVamp{}
			}
			if err := m.Vibration.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 7:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Infrared", wireType)
			}
			m.Infrared = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Infrared |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Camera", wireType)
			}
			m.Camera = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Camera |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Reserve", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthController
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthController
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Reserve = append(m.Reserve[:0], dAtA[iNdEx:postIndex]...)
			if m.Reserve == nil {
				m.Reserve = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipController(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthController
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthController
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *BAlarmClear) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowController
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: BAlarmClear: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: BAlarmClear: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field DeviceID", wireType)
			}
			m.DeviceID = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DeviceID |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Response", wireType)
			}
			m.Response = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Response |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipController(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthController
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthController
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *BDataUpdate) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowController
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: BDataUpdate: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: BDataUpdate: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field DeviceID", wireType)
			}
			m.DeviceID = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DeviceID |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DataVersion", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthController
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthController
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.DataVersion = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Addr", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthController
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthController
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Addr = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Timerbase", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthController
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthController
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Timerbase = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Timer", wireType)
			}
			m.Timer = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Timer |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Attitude", wireType)
			}
			m.Attitude = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Attitude |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Dirft", wireType)
			}
			m.Dirft = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Dirft |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Vibration", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthController
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthController
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Vibration == nil {
				m.Vibration = &BVamp{}
			}
			if err := m.Vibration.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 10:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Infrared", wireType)
			}
			m.Infrared = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Infrared |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 11:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field T1", wireType)
			}
			m.T1 = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.T1 |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 12:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field T2", wireType)
			}
			m.T2 = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.T2 |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 13:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field T3", wireType)
			}
			m.T3 = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.T3 |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 14:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field N1", wireType)
			}
			m.N1 = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.N1 |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipController(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthController
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthController
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *BShutDown) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowController
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: BShutDown: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: BShutDown: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field DeviceID", wireType)
			}
			m.DeviceID = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DeviceID |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CamType", wireType)
			}
			m.CamType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowController
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CamType |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipController(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthController
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthController
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipController(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowController
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowController
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowController
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthController
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupController
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthController
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthController        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowController          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupController = fmt.Errorf("proto: unexpected end of group")
)

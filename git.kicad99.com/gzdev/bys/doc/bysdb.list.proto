//generated by ygen-gocrud-proto. DO NOT EDIT.
//source: bysdb.proto
syntax = "proto3";
package bysdb;

import "bysdb.proto"; 
option go_package="git.kicad99.com/gzdev/bys/doc/bysdb";

// DbController list
message   DbControllerList {
	//批次号，从0开始
	uint32 BatchNo = 1;

	//rows offset,从0开始
	uint32 RowsOffset = 2;

	//本批次数据
	repeated DbController Rows = 3;
}

// DbBysMarker list
message   DbBysMarkerList {
	//批次号，从0开始
	uint32 BatchNo = 1;

	//rows offset,从0开始
	uint32 RowsOffset = 2;

	//本批次数据
	repeated DbBysMarker Rows = 3;
}

// DbControllerOnlineHistory list
message   DbControllerOnlineHistoryList {
	//批次号，从0开始
	uint32 BatchNo = 1;

	//rows offset,从0开始
	uint32 RowsOffset = 2;

	//本批次数据
	repeated DbControllerOnlineHistory Rows = 3;
}

// DbMediaInfo list
message   DbMediaInfoList {
	//批次号，从0开始
	uint32 BatchNo = 1;

	//rows offset,从0开始
	uint32 RowsOffset = 2;

	//本批次数据
	repeated DbMediaInfo Rows = 3;
}

// DbMarkerHistory list
message   DbMarkerHistoryList {
	//批次号，从0开始
	uint32 BatchNo = 1;

	//rows offset,从0开始
	uint32 RowsOffset = 2;

	//本批次数据
	repeated DbMarkerHistory Rows = 3;
}

// DbMarkerPatrolHistory list
message   DbMarkerPatrolHistoryList {
	//批次号，从0开始
	uint32 BatchNo = 1;

	//rows offset,从0开始
	uint32 RowsOffset = 2;

	//本批次数据
	repeated DbMarkerPatrolHistory Rows = 3;
}

// DbNFCPatrolLine list
message   DbNFCPatrolLineList {
	//批次号，从0开始
	uint32 BatchNo = 1;

	//rows offset,从0开始
	uint32 RowsOffset = 2;

	//本批次数据
	repeated DbNFCPatrolLine Rows = 3;
}

// DbNFCPatrolLineDetail list
message   DbNFCPatrolLineDetailList {
	//批次号，从0开始
	uint32 BatchNo = 1;

	//rows offset,从0开始
	uint32 RowsOffset = 2;

	//本批次数据
	repeated DbNFCPatrolLineDetail Rows = 3;
}

// DbNFCPatrolLineRules list
message   DbNFCPatrolLineRulesList {
	//批次号，从0开始
	uint32 BatchNo = 1;

	//rows offset,从0开始
	uint32 RowsOffset = 2;

	//本批次数据
	repeated DbNFCPatrolLineRules Rows = 3;
}

// DbNFCPatrolLineAndRules list
message   DbNFCPatrolLineAndRulesList {
	//批次号，从0开始
	uint32 BatchNo = 1;

	//rows offset,从0开始
	uint32 RowsOffset = 2;

	//本批次数据
	repeated DbNFCPatrolLineAndRules Rows = 3;
}

// DbMarkerUploadImageHistory list
message   DbMarkerUploadImageHistoryList {
	//批次号，从0开始
	uint32 BatchNo = 1;

	//rows offset,从0开始
	uint32 RowsOffset = 2;

	//本批次数据
	repeated DbMarkerUploadImageHistory Rows = 3;
}

{"CmdTest": {"AlarmTime": "界桩时间", "Channel": "本地信道", "Cmd": "指令", "ConnTime": "上线时间", "HWID": "HWID", "NO": "编号", "NoCmdYet": "30天内暂无指令", "TransferChannelNos": "通道:信道", "UserSetting": "同步数据库用户参数", "abnormal": "异常", "alarm": "报警", "alarmLock": "报警锁定", "alarmStatus": "报警状态: {status}", "altitude": "海拔", "baseStation": "基站", "batteryPower": "电量", "batteryStatus": "电池{status}", "batteryVoltage": "电池电压", "bysMarker": "界桩", "camera": "摄像头", "closeAlarm": "解除警报", "controller": "控制器", "deviceFieldStrength": "场强", "deviceID": "设备ID", "deviceState": "设备状态", "displacementAlarm": "位移报警", "emptyReason": "暂无原因", "fourG": "4G网络", "goto": "到这里", "gpsModuleStatus": "GPS模块{status}", "heartbeat": "心跳", "humidity": "湿度", "iccidRepeat": "上线界桩{markerNo}与系统内界桩{repeatMarker}ICCID重复", "iccidRepeatTitle": "ICCID重复", "imeiConflict": "上线界桩{markerNo}与系统该界桩的IMIE存在冲突", "imeiConflictTitle": "IMEI冲突", "imeiInSystem": "系统内界桩IMIE: {imei}", "infraredAlarm": "红外报警", "infraredProbe": "红外探头", "init": "初始化", "isAlarm": "是否报警", "isDumping": "是否倾倒", "isLowPowerAlarm": "是否低电报警", "isRegister": "是否注册", "isTest": "是否是测试", "isValidPosition": "北斗定位是否有效", "lastCmd": "最后指令", "lat": "纬度", "lng": "经度", "loginTimeStr": "登录时间", "lowerPower": "电量低", "markerParamIsSync": "界桩参数已同步", "markerParamUnSync": "界桩参数未同步", "markerTimeIsSync": "界桩时间已同步", "markerTimeUnSync": "界桩时间未同步", "net4GMarker": "4G界桩", "networkType": "网络类型", "normal": "正常", "offline": "离线", "offlinePanic": "失联", "online": "上线", "onlineImei": "上线界桩IMEI: {imei}", "paramTime": "界桩参数更新时间", "positionLat": "定位纬度", "positionLng": "定位经度", "positioningInfo": "定位信息", "receivingRelay": "接收中继", "removeAlarm": "解除报警", "repeater": "中继", "report": "打卡", "reportOnDebugging": "调试上报", "reportOnRegular": "定时上报", "reportOnStartup": "开机上报", "reportToAlarm": "报警上报", "reportType": "上报类型", "rtcClockStatus": "RTC时钟{status}", "softwareVersion": "软件版本", "stationDeviceNoBaseStation": "本地通道", "stationDeviceNoRepeater": "链路通道", "stationID": "基站ID", "stationNo": "基站编号", "status": "状态码", "systemPassCheckOK": "系统密码校验", "temperature": "温度", "threeAxisSensorStatus": "三轴传感器状态: {status}", "tiltAlarm": "倾斜报警", "tiltAngleVal": "倾斜角度 {value}°", "trash": "清空日志", "type": "控制器类型", "unknown": "未知", "vibrationAlarm": "震动报警", "vibrationAmplitudeVal": "震动幅度 {value}", "vibrationDurationVal": "震动时长 {value}s", "wired": "有线网络"}, "DbPerm": {"delete": "删除", "insert": "新增", "menu": "菜单", "query": "查询", "update": "修改"}, "NFCPatrol": {"Friday": "星期五", "LineName": "线路名称", "Monday": "星期一", "NFCPatrol": "NFC巡查", "NFC_DISABLED": "NFC未开启，请前往NFC设置中开启！", "NO_NFC": "设备不支持NFC！", "QuantityToCheck": "应查数目", "Saturday": "星期六", "Sunday": "星期日", "Thursday": "星期四", "Tuesday": "星期二", "Wednesday": "星期三", "allMarker": "全部界桩", "checkDate": "巡查日期", "checkEndTime": "结束巡查时间", "checkNum": "已查数目", "checkResult": "巡查结果", "checkStartTime": "开始巡查时间", "checkTime": "巡查时间", "checkTimeAbnormal": "巡查时间异常", "checkUser": "巡查用户", "day1": "一", "day2": "二", "day3": "三", "day4": "四", "day5": "五", "day6": "六", "day7": "日", "discoverNewTags": "发现新标签", "effective": "总是有效", "effectiveEnd": "失效时间", "effectiveStart": "生效时间", "effectiveType": "生效类型", "firstWriteMarkerData": "请先写入界桩数据!", "isUseName": "该名称已被使用", "isWriteTitle": "NFC写入", "lineAndRules": "线路与规则映射", "lineDetail": "线路详情", "linePointIsNotExceed": "巡查点不能超过200个", "linePointIsNotNull": "巡查点不能为空", "noCheck": "未打卡", "noNFCRecord": "该线路暂无打卡记录!", "noPatrol": "暂未打卡!", "noneData": "暂无数据！", "nowWriteMarkerNo": "当前写入：{markerNo}", "patrolCount": "巡查次数", "patrolCountExceed": "巡查次数不超过99次", "patrolPoint": "巡查点", "patrolRules": "巡查规则", "patrolStatistics": "巡查规则统计", "patrolTime": "巡查时间", "readTip": "请贴近NFC卡片开始读取!", "repeatPatrolTip": "重复打卡，请稍候重试", "ruleName": "规则名称", "rulesIsNotExceed": "规则不能超过10条", "selectedPatrolMarker": "已选巡查界桩", "statWrite": "请贴近NFC卡片开始写入！", "uploadAll": "全部上传", "uploadAllSuccess": "所有巡查都已上报！", "uploadFailed": "界桩{markerNo}已巡查,但未上传", "uploadFailedSome": "界桩{markerNos}巡查上报失败！", "uploadSuccess": "界桩{markerNo}已完成巡查上报", "userName": "巡查用户", "validTimePeriod": "有效时间段", "writeNFC": "写入", "writeSuccess": "写入成功"}, "PermTab": {"Cmd": "命令", "CmdPerm": "命令权限", "Db": "数据", "DbBysMarker": "界桩", "DbConfig": "系统配置信息", "DbController": "控制器", "DbControllerOnlineHistory": "控制器历史", "DbMarkerHistory": "界桩历史", "DbMarkerPatrolHistory": "界桩巡查历史", "DbMediaInfo": "图影相", "DbOrg": "单位", "DbPerm": "数据权限", "DbRole": "角色", "DbRolePermission": "角色权限", "DbUser": "用户", "DbUserOrgPrivilege": "单位-用户映射", "DbUserRole": "用户角色", "Delete": "删除", "Insert": "新增", "Menu": "菜单", "Query": "查询", "Sync": "数据已与后台同步", "Update": "修改"}, "abnormalStatistics": {"cardExpires4G": "4G卡到期", "daysRemaining": "剩余天数", "lowBatteryAlert": "低电量警报", "notReportedOnTime": "未按时上报", "operate": "操作", "remoteKillAndStun": "遥毙/遥晕", "removeStun": "解除遥晕", "timeoutDuration": "超时时长"}, "builtInAttr": {"admin": "超级管理员", "dataManager": "数据管理员", "devHis": "设备历史查询员", "maintain": "维护管理员", "nfcManager": "NFC管理员", "optHis": "操作记录员", "root": "默认", "tourist": "游客"}, "common": {"more": "更多", "No1": "1信道", "No2": "2信道", "RoleQuickPerm": "角色权限", "UserQuickPerm": "用户权限", "abnormalData": "异常数据", "add": "添加", "back": "返回", "baiduMap": "百度地图", "cancel": "取消", "channelDevNoSetting": "通道信道号设置", "channelNo": "通道号", "clear": "清空", "clickAgainExitApp": "再次点击退出应用", "clickAgainToExit": "再次点击退出应用", "cmdSendFailed": "命令下发失败", "cmdSendSuc": "命令下发成功", "cmdSendSucAndWaitMarkerRes": "命令已下发，等待界桩唤醒时响应命令", "confirm": "确定", "confirmExit": "你确定退出？", "continue": "继续", "delete": "删除", "details": "详情信息", "download": "下载", "downloadFailed": "下载失败", "downloadSuccess": "下载成功", "edit": "编辑", "dataEdit": "数据编辑", "exactSearch": "完全匹配", "export": "导出", "failed": "失败", "finish": "完成", "gaodeMap": "高德地图", "hideColShortcuts": "隐藏快捷按钮", "ignore": "忽略", "import": "导入", "loadBtnLabel": "点击开始加载全景图", "localChannelDevNo": "本地通道信道号", "mediaData": "查看图片", "modify": "修改", "new": "新建", "no": "否", "preview": "预览", "refresh": "刷新", "reset": "重置", "roleEdit": "用户权限", "search": "搜索", "select": "请选择", "show": "展示", "showColShortcuts": "显示快捷按钮", "success": "成功", "upload": "上传", "uploadTips": "上传图片时请区分普通图片和全景图", "yes": "是"}, "controller": {"port": "端口号", "serverAddress": "服务器地址", "target": "目标控制器", "warning": "确定修改服务器地址？", "warningCaption": "注意：修改后控制器将不再登录当前系统！"}, "controllerErrReason": {"lowPower": "电量低", "networkType": "网络类型", "offline": "离线", "timeError": "时间异常"}, "dataBaseError": {"childMarkerExist": "控制器下方有界桩数据", "controllerChannelCannotRepeated": "同一基站下控制器通道不能重复", "controllerHwidErr": "控制器ID已使用", "controllerNoErr": "控制器编号已使用", "controllerNotExist": "该控制器数据不存在", "controllerPKeyErr": "数据异常，请重试", "deleteOtherDataFirst": "请先删除该单位的{name}数据", "lackOrgPrivilege": "没有权限", "loginNameErr": "登录名已使用", "markerHwidErr": "界桩ID已使用", "markerNoErr": "界桩编号已使用", "markerNotExist": "该控界桩数据不存在", "notSetPermForAddedOrg": "该单位的权限设置失败", "orgIdErr": "单位编号已使用", "parControllerErr": "上级控制器错误", "parUnitErr": "上级单位错误", "permissionErr": "没有权限", "queueNoErr": "排队号已使用", "redLineNoErr": "红线图ID已使用", "roleNameCannotRepeated": "同一单位下角色名不能重复", "unitNotExist": "该单位数据不存在", "uploadUserNotExist": "该上传用户数据不存在", "userNoErr": "用户编号已使用", "userRoleErr": "用户角色错误"}, "date": {"days": "天", "hours": "小时", "minutes": "分钟", "nDaysAgo": "{num}天前", "notMax3month": "时间不超过3个月", "notMax3year": "时间不超过3年"}, "upgradeApp": {"checkUpdate": "检查更新", "upgradeFailed": "升级失败", "errorMessage": "错误信息：{error}", "startDownload": "开始下载", "isInstallNewApp": "已下载新版本APP ({newVersion})，是否立即安装？", "install": "立即安装"}, "export": {"noData": "没有可用数据", "openFile": "打开文件"}, "fancytree": {"removeAlarm": "解除报警"}, "form": {"4GMarkers": "4G界桩", "4GMarkersPro": "4G界桩Pro", "ControllerNo": "控制器编号", "ExpirationDate": "卡号到期日期", "GPSAlarm": "GPS故障报警", "HasInstallStone": "已安装石桩", "Maintain": "维修权限", "MarkerNo": "界桩编号", "NFCPatrol": "NFC巡查", "Operation": "用户操作", "Ping": "查询状态", "abnormalControllers": "异常控制器", "abnormalData": "异常统计", "abnormalMarkers": "异常界桩", "addr": "服务器地址", "addrPort": "服务器端口", "addrRulesFail": "服务器地址不正确", "alarm": "报警", "alarmInterval": "报警间隔(s)", "alarmNum": "报警次数", "alarmReason": "报警原因", "amplitude": "震动幅度(级)", "attitude": "倾斜角度", "availableChannels": "通道数", "availableQueueNo": "可用排队号 ", "backQueryPage": "返回查询页", "baseStation": "基站", "baseStationChannel": "控制器通道", "batteryPower": "电量", "boundaryID": "界桩ID", "boundaryModel": "界桩型号", "boundaryType": "界桩类型", "builtin": "内置", "cameraAlreadyDiedRemotely": "摄像机已遥毙", "cameraAlreadyFainted": "摄像机已遥晕", "cameraKillRemotely": "摄像机遥毙", "cameraRemoteActive": "摄像机遥活", "cameraRemoteStun": "摄像机遥晕", "changeDefaultChannelNo2One": "修改信道号至1", "changeDefaultChannelNo2Two": "修改信道号至2", "checkAlarmOnly": "只查报警", "checkCardOnly": "只查打卡", "close": "关闭", "cmdUploadTime": "指令时间", "confirmCancelKillOrStun": "确认取消{cmd}状态?", "confirmKillOrStun": "确认设置{cmd}状态?", "controller": "控制器", "controllerChannel": "控制器信道", "controllerHWID": "控制器ID", "controllerID": "控制器编号", "creator": "创建者", "customID": "编号", "data": "数据", "dataVersion": "版本", "dateRangeBottomTip": "开始与结束日期异常", "deLaySlTime": "延迟休眠时间(s)", "debugModelTime": "调试模式时间(s)", "default": "默认", "defaultNetworkType": "默认网络类型", "description": "备注", "deviceContext": "关联设备", "deviceType": "设备类型", "dirft": "位移距离(m)", "dumping": "倾倒报警", "duration": "震动持续时间(s)", "edit": "编辑", "endTime": "结束时间", "enterFilterKey": "输入关键词过滤", "errReason": "异常原因", "expirationDateFail": "不符合YYYY-MM-DD的要求", "fourG": "4G", "fsk": "FSK", "fullName": "全称", "iccidExists": "卡号已存在", "iccidRuleCheckFail": "不满足iccid的标准规范", "image": "图像", "infrared": "红外报警阈值", "infraredTooltip": "0为关闭, 1~10", "ipInfo": "IP信息", "isInstallDevice": "已安装设备", "keepAdding": "继续添加", "killRemotely": "界桩遥毙", "lastReportTime": "最后上报时间", "lastUploadTime": "最后上报时间", "lastUploadUser": "最后修改用户", "lat": "纬度", "limitedNetwork": "有线网络", "lngLat": "经纬度", "loginName": "登录名", "loginPassword": "登录密码", "lon": "经度", "long": "长", "lowPower": "低电报警", "lowPowerAlarm": "低电报警", "mapShowLevel": "地图显示级别", "markerAlreadyDiedRemotely": "界桩已遥毙", "markerAlreadyFainted": "界桩已遥晕", "markerChannel": "通信信道", "markerDayInterval": "打卡间隔(h)", "markerEmergentInterval": "报警发射间隔(s)", "markerName": "界桩名称", "markerQueueInterval": "排队发射间隔(s)", "markerQueueNo": "排队号", "markerRedLineNo": "红线图序号", "markerRemoteKillStun": "遥毙/遥晕", "markerWakeupBaseTime": "唤醒基准时间", "movedAlarm": "移动报警", "name": "名称", "noData": "空数据", "noMatchData": "没有匹配的数据", "normal": "正常", "normalImage": "普通图片", "normalMarkers": "专网界桩", "notConnectRedLine": "不连红线图", "note": "备注", "offline": "下线", "oldPwdError": "原密码错误", "oneMonth": "一个月", "oneYear": "一年", "online": "上线", "pInputUserName": "请输入用户名", "panorama": "全景图", "parentChannelCount": "上级控制器通道", "parentController": "上级控制器", "parentControllerAndChannel": "上级控制器/通道号", "parentUnit": "上级单位", "phone": "电话", "photos": "图影像", "pwdDef": "两次输入不一致", "query": "查询", "realParentController": "上行控制器 ", "receiptTime": "接收时间", "redLineGraphRules": "红线图规则", "remoteActivition": "界桩遥活", "remoteStun": "界桩遥晕", "repeater": "中继", "reportNormal": "正常打卡", "role": "角色", "rolePermission": "角色权限", "selectImages": "请选择图片/全景图/视频", "sendCmd": "发送指令", "short": "短", "shortName": "简称", "showTrackHistory": "显示历史轨迹", "sortValue": "排序值", "startTime": "开始时间", "status": "最后上报状态", "submit": "确定", "tamperAlarm": "防拆报警", "test": "测试报警", "testAlarm": "测试", "threeMonth": "三个月", "threeYear": "三年", "time": "时间", "transferSourceTit": "源数据", "transferTargetTit": "目标数据", "twoMonth": "两个月", "type": "类型", "unit": "单位", "unknown": "未知网络", "uploadTime": "上传时间", "uploadUser": "上传用户", "userName": "用户名", "userRole": "用户角色", "vibration": "震动报警阈值", "video": "视频", "wakeUpInterval": "唤醒间隔(h)"}, "historyTable": {"alarmCapture": "报警抓拍", "captureTime": "拍照时间", "captureType": "拍照类型", "clickToViewFullImage": "点击查看大图", "manualCapture": "手动抓拍", "photoUpload": "拍照上传", "sensorCapture": "红外感应抓拍", "timerCapture": "定时抓拍"}, "import": {"errorInfo": "错误信息", "notFoundController": "找不到上级控制器", "simultaneously": "ICCID卡和到期日期应同时填写"}, "languages": {"enUs": "English", "zhCN": "中文"}, "login": {"loginName": "登录名", "otherWay": "其他方式登录", "password": "密码", "remember": "记住账号", "submit": "登  录", "system": "系统号", "title": "系统登录"}, "maps": {"Kilometers": "公里", "bus": "公交", "drive": "驾车", "endPoint": "终点", "getLngLatTip": "请点击地图获取坐标经纬度", "getOff": "下车", "getOn": "上车", "lessTransfer": "少换乘", "lessWalking": "少步行", "measure": "测距", "meter": "米", "pause": "暂停", "play": "播放", "search": "查找控制器、界桩", "shortTime": "时间短", "startPoint": "起点", "stationCount": "{num}站", "stop": "停止", "switchSatelliteLayer": "切换卫星图", "switchStreetLayer": "切换街道图", "transferStation": "站内换乘", "walking": "步行", "zoom": "缩放"}, "markerStatistics": {"exportAll": "导出全部", "installed": "已安装", "markerInstallStatus": "界桩安装状态", "markerTotal": "界桩总数", "notInstallDevice": "未安装设备", "notInstallStone": "未安装石桩"}, "menus": {"MarkerNFCPatrolHistory": "NFC巡查历史", "alarmQuery": "报警查询", "boundaryMarker": "界桩", "changeParameters": "修改参数", "cmdTest": "指令日志", "command": "命令", "confirmCmd": "请确认执行{cmd}命令", "controller": "控制器", "controllerOnlineRecords": "控制器历史", "currentUpdate": "本次更新", "data": "数据", "dataStatistics": "数据统计", "deviceQuery": "设备查询", "downLoad": "App下载", "enableScrollTitle": "开启滚动标题", "faultQuery": "故障查询", "help": "帮助", "images": "图影像", "markerHistory": "界桩历史", "markerStatistics": "界桩安装统计", "markerUploadImageHistory": "图像上传历史", "modifyServerAddr": "修改服务器地址", "optionHistory": "操作记录", "organization": "单位", "patrolLines": "巡查线路", "patrolRules": "巡查规则", "photoQuery": "图影像查询", "query": "查询", "role": "角色", "settings": "设置", "setupTracking": "设置跟踪", "stopScrollTitle": "停止滚动标题", "stopTracking": "停止跟踪", "syncSettingTime": "同步遥毙/遥晕命令时间失败！", "systemDoc": "系统文档", "toggleFullscreen": "切换全屏", "trackPlayback": "轨迹回放", "user": "用户", "userSettings": "用户设置", "versionInfo": "版本信息"}, "message": {"4GMarkerOffline": "设备已自动解除报警进入睡眠！", "addFailed": "添加失败", "addSuccess": "添加成功", "alarmTime": "报警时间", "autoLoginExpired": "自动登录已失效", "cameraError": "无法拍照，请选择本地图片", "confirmPlayTrace": "当前存在多个界桩报警数据,请确认在同一轨迹中回放?", "controllerOffline": "控制器不在线", "deleteFailed": "删除失败", "deleteHasChildUnit": "{name}为其他单位的上级，需要删除所有下级单位才能删除", "deleteOrgWarn": "删除单位时，会同步删除单位下的所有数据，请确认是否删除？", "deleteSuccess": "删除成功", "duplicateData": "重复数据", "duplicateOrgID": "自编号重复：{name}", "exportPath": "导出目录: {path}", "exportSuccess": "导出成功", "failedPlanRoute": "规划线路失败", "importDataHasError": "导入的数据中有错误，请导出数据并检查错误", "importSuccess": "导入成功", "invalidRmAlarmCmd": "无效的界桩/控制器", "lackOrgAuth": "没有单位权限", "loadingData": "正在加载数据，请稍候...", "localErr": "本地错误", "locationFailed": "定位失败", "loginSuccess": "登录成功", "loginTimeError": "登录时间错误", "loginTimeout": "登录超时", "maybeOffline": "可能不在线", "modifyControllerNoWarn": "如果修改控制器名称，则需主动修改控制器硬件配置，使用新的名称进行登录", "noData": "没有数据", "noOptPerm": "没有权限", "noSuchSys": "无此系统号", "noSuchUser": "无此用户", "notAvailableSortNumber": "排队号({num})不可用", "notFoundEndPoint": "找不到终点", "notFoundStartPoint": "找不到起点", "notifyICCIDAlreadyExist": "当前上线界桩{markerNo}的ICCID与系统内{duplicateMarkerNo}界桩ICCID重复, 请确认是否更新上线界桩信息。", "notifyICCIDAndIMEIConflict": "系统内界桩{MarkerNo}的IMEI为{optStr}；界桩{MarkerNo}上报的IMEI为: {IMEI}；请确认是否更新上线界桩信息！", "offline": "{name}下线", "online": "{name}上线", "onlineController": "在线控制器", "parameterError": "参数错误", "parentUnitErr": "上级单位错误", "passwordErr": "密码错误", "paused": "已暂停", "playFinished": "播放完成", "queryFailed": "查询失败", "querySuccess": "查询成功", "recommendedWalking": "路径太短，建议步行", "refreshSuccessful": "刷新成功", "removeAlarm": "{name}已解除报警", "removeAlarmSuccess": "解除报警成功", "requestTimeout": "请求超时", "rmAlarmCmdShakeProof": "请勿频繁发送解除报警指令", "sendFailed": "指令发送失败", "sendSuccess": "指令已发送", "sureDeleteIt": "您确定要删除吗？", "syncICCIDOnSystem": "更新ICCID到上线界桩数据", "syncIMEIOnSystem": "更新IMEI到上线界桩数据", "timeout": "{action}{name}超时", "timeoutLabel": "超时", "tracePlayTooltip": "只能播放报警轨迹", "updateChannelNoFai": "修改信道号失败", "updateChannelNoSuc": "修改信道号成功", "updateFailed": "更新失败", "updateSuccess": "更新成功", "uploadFailed": "上传失败", "uploadSuccess": "上传成功", "within500Meters": "起终点距离500米内，返回线路"}, "permission": {"cmd": "命令", "db": "数据", "permissionCenter": "角色权限"}, "rules": {"IDAlreadyExists": "该ID已经存在", "IMEIExists": "IMEI已存在", "availableRange": "可用范围：{min}~{max}", "cannotEarlierStartTime": "不能比开始时间早", "cannotLaterEndTime": "不能比结束时间晚", "correctPhone": "请输入正确的电话号码", "invalidDateTime": "无效的日期时间", "invalidTime": "无效的时间", "invalidTimeDiff": "时间差不能超过一个月", "invalidTimeDiffy1": "时间差不能超过一个年", "maxLength": "最大输入{len}字符", "maxValue": "最大值:{max}", "minValue": "最小值:{min}", "mustBeBCDCode": "必须是BCD码", "mustBeIpOrDomain": "必须是IP或域名", "mustBeLat": "必须是有效的纬度", "mustBeLon": "必须是有效的经度", "mustbeGtZero": "必须大于0的整数", "required": "必填", "validIMEI": "请输入有效的IMEI", "validIMEILength": "IMEI长度应为15位", "validSystemNum": "请输入有效的系统号"}, "settingsPage": {"account": "用户详情", "ascSort": "升序", "bysMarkerFirst": "界桩在前", "chooseLogo": "选择logo", "closeAlarmAudio": "报警响铃", "closeAlarmPopupWind": "报警弹窗", "confirmNewPassword": "确认密码", "confirmUpdatePwTip": "确认修改当前用户密码?", "confirmUpdateServerTip": "确认修改当前界桩的服务器地址?", "confirmUpdateSysSettings": "确认修改当前系统设置?", "controllerFirst": "控制器在前", "descSort": "降序", "deviceSortType": "设备树排序：{type}", "deviceTreeSortRule": "排序规则：{type}", "loginName": "登录名", "newPassword": "新密码", "oldPassword": "原密码", "others": "其他", "picFormat": "图片格式应为JPG/PNG", "picSize": "图片大小应不超过100kb", "security": "安全", "serverSettings": "服务器设置", "showNotInstalledMarker": "显示未安装界桩", "submitModify": "提交修改", "sysLogo": "系统LOGO", "sysTitle": "系统标题", "systemSetting": "系统设置", "unit": "所在单位", "userName": "用户名", "userSettings": "个人设置"}, "siteTitle": "北峰BF-PMSS01智慧电子界桩系统", "sslUpdate": {"certExpired": "证书已过期", "certUpdateSuccess": "证书更新成功", "certificate": "证书文件(.crt)", "domainName": "网站域名", "expiredDate": "截止日期", "issueDate": "颁发日期", "key": "密钥文件(.key)", "noFileSelected": "请选择证书和密钥", "token": "更新密码", "updateCertTips": "上传HTTPS/SSL证书到服务器，该操作无法撤销，请谨慎操作！", "updateCertificate": "更新证书"}, "statistics": {"currentNetworkType": "当前网络类型", "off": "离线", "power": "电量"}, "tranckHisReplay": {"currentItem": "当前位置", "notrackData": "有效的定位数据为空", "speed": "播放速度"}, "userRole": {"btnBack": "上一步", "btnContinue": "下一步", "btnFinish": "完成", "check": "选择", "role": "角色名", "roleTab": "选择角色", "unitTab": "选择单位", "userRole": "用户权限"}, "userTab": {"checked": "选中", "role": "角色", "roleInfo": "角色属性", "roleSelection": "选择角色", "unit": "单位", "unitSelection": "选择单位"}, "version": {"serveRunTime": "运行时长", "serverBuildTime": "构建时间", "serverStartTime": "运行时间", "serverVersion": "服务器版本", "webBuildTime": "构建时间", "webVersion": "客户端版本"}}
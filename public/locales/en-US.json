{"CmdTest": {"AlarmTime": "Alarm Time", "Channel": "Local-ChannelNo", "Cmd": "Command", "ConnTime": "Connect Time", "HWID": "HWID", "NO": "NO", "NoCmdYet": "No orders within 30 days", "TransferChannelNos": "Transfer Channel Nos", "UserSetting": "Sync User Settings with Database", "abnormal": "Abnormal", "alarm": "Alarm", "alarmLock": "Alarm lock", "alarmStatus": "Alarm status: {status}", "altitude": "Altitude", "baseStation": "BaseStation", "batteryPower": "Battery Power", "batteryStatus": "Battery is {status}", "batteryVoltage": "Battery voltage", "bysMarker": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "camera": "Camera", "closeAlarm": "Lift Alarm", "controller": "Controller", "deviceFieldStrength": "Device Field Strength", "deviceID": "Device ID", "deviceState": "Device status", "displacementAlarm": "Displacement alarm", "emptyReason": "No reason yet", "fourG": "4G", "goto": "Go to Destination", "gpsModuleStatus": "GPS module is {status}", "heartbeat": "Heartbeat", "humidity": "<PERSON><PERSON><PERSON><PERSON>", "iccidRepeat": "The ICCID of the online boundary marker {markerNo} and the system internal boundary marker {repeatMarker} are repeated", "iccidRepeatTitle": "ICCID Repeat", "imeiConflict": "The online boundary marker {markerNo} conflicts with the IMIE of the system boundary marker", "imeiConflictTitle": "IMEI conflict", "imeiInSystem": "IMIE of the system boundary marker: {imei}", "infraredAlarm": "Infrared alarm", "infraredProbe": "Infrared probe", "init": "Init", "isAlarm": "Is Alarm", "isDumping": "Is Dumping", "isLowPowerAlarm": "Is Low Power Alarm", "isRegister": "Is Register", "isTest": "Is Test", "isValidPosition": "Is Valid Position", "lastCmd": "Last command", "lat": "Latitude", "lng": "Longitude", "loginTimeStr": "Login time", "lowerPower": "Lower Power", "markerParamIsSync": "<PERSON><PERSON> Sync", "markerParamUnSync": "<PERSON><PERSON><PERSON> Is UnSync", "markerTimeIsSync": "Marker Time Is Sync", "markerTimeUnSync": "Marker Time Is UnSync", "net4GMarker": "4G <PERSON>er", "networkType": "Network Type", "normal": "Normal", "offline": "Offline", "offlinePanic": "Offline panic", "online": "Online", "onlineImei": "IMEI of the online boundary marker: {imei}", "paramTime": "<PERSON><PERSON> Update Time", "positionLat": "Position Lat", "positionLng": "Position Lng", "positioningInfo": "Positioning info", "receivingRelay": "Receiving relay", "removeAlarm": "Remove alarm", "repeater": "<PERSON><PERSON><PERSON>", "report": "Report", "reportOnDebugging": "Debugging reporting", "reportOnRegular": "Regular reporting", "reportOnStartup": "Report on startup", "reportToAlarm": "Report to the alarm", "reportType": "Report type", "rtcClockStatus": "RTC clock is {status}", "softwareVersion": "Software version", "stationDeviceNoBaseStation": "local stationDeviceNo", "stationDeviceNoRepeater": "stationDeviceNo", "stationID": "Station ID", "stationNo": "Station No", "status": "Status code", "systemPassCheckOK": "System Pass Check", "temperature": "Temperature", "threeAxisSensorStatus": "Three-axis sensor status: {status}", "tiltAlarm": "Tilt alarm", "tiltAngleVal": "Tilt angle {value}°", "trash": "Trash", "type": "Controller Type", "unknown": "Unknown", "vibrationAlarm": "Vibration alarm", "vibrationAmplitudeVal": "Vibration amplitude {value}", "vibrationDurationVal": "Vibration duration {value}s", "wired": "Wired"}, "DbPerm": {"delete": "Delete", "insert": "Insert", "menu": "<PERSON><PERSON>", "query": "Query", "update": "Update"}, "NFCPatrol": {"Friday": "Friday", "LineName": "Line Name", "Monday": "Monday", "NFCPatrol": "NFC Patrol", "NFC_DISABLED": "NFC is not turned on, please go to NFC settings to turn it on!", "NO_NFC": "The device does not support NFC!", "QuantityToCheck": "Quantity to be checked", "Saturday": "Saturday", "Sunday": "Sunday", "Thursday": "Thursday", "Tuesday": "Tuesday", "Wednesday": "Wednesday", "allMarker": "All Markers", "checkDate": "Inspection date", "checkEndTime": "End patrol time", "checkNum": "Checked number", "checkResult": "Inspection result", "checkStartTime": "Start inspection time", "checkTime": "Inspection time", "checkTimeAbnormal": "Abnormal inspection time", "checkUser": "Check User", "day1": "Monday", "day2": "Tuesday", "day3": "Wednesday", "day4": "Thursday", "day5": "Friday", "day6": "Saturday", "day7": "Sunday", "discoverNewTags": "Discover new tags", "effective": "always valid", "effectiveEnd": "Expiration time", "effectiveStart": "effective time", "effectiveType": "effective type", "firstWriteMarkerData": "Please write the boundary marker data first!", "isUseName": "This name is already in use", "isWriteTitle": "NFC write", "lineAndRules": "Line and Rules Mapping", "lineDetail": "line details", "linePointIsNotExceed": "The number of inspection points cannot exceed 200", "linePointIsNotNull": "The patrol point cannot be null", "noCheck": "Not clocked in", "noNFCRecord": "There is no clock-in record for this line!", "noPatrol": "Haven't clocked in yet!", "noneData": "No data!", "nowWriteMarkerNo": "Current writing: {markerNo}", "patrolCount": "Number of inspections", "patrolCountExceed": "The number of patrols does not exceed 99 times", "patrolPoint": "patrol point", "patrolRules": "PatrolRules", "patrolStatistics": "Patrol Rule Statistics", "patrolTime": "Patrol time", "readTip": "Please move close to the NFC card to start reading!", "repeatPatrolTip": "Check in again, please try again later.", "ruleName": "rule name", "rulesIsNotExceed": "The rules cannot exceed 10", "selectedPatrolMarker": "Selected Patrol Marker", "statWrite": "Please move close to the NFC card to start writing!", "uploadAll": "Upload all", "uploadAllSuccess": "All inspections have been reported!", "uploadFailed": "Boundary marker {markerNo} has been inspected but not uploaded", "uploadFailedSome": "Boundary marker {markerNos} inspection report failed!", "uploadSuccess": "Boundary marker {markerNo} has completed inspection and reporting", "userName": "Patrol user", "validTimePeriod": "valid time period", "writeNFC": "write", "writeSuccess": "Write successfully"}, "PermTab": {"Cmd": "Command", "CmdPerm": "Command Permission", "Db": "Data", "DbBysMarker": "<PERSON><PERSON>", "DbConfig": "System config info", "DbController": "Controller", "DbControllerOnlineHistory": "Controller History", "DbMarkerHistory": "Marker History", "DbMarkerPatrolHistory": "Marker Patrol History", "DbMediaInfo": "Media Info", "DbOrg": "Unit", "DbPerm": "Data Permission", "DbRole": "Role", "DbRolePermission": "Role Permission", "DbUser": "User", "DbUserOrgPrivilege": "Unit-User Map", "DbUserRole": "User Role", "Delete": "Delete", "Insert": "Insert", "Menu": "<PERSON><PERSON>", "Query": "Query", "Sync": "data has been synced with service", "Update": "Update"}, "abnormalStatistics": {"cardExpires4G": "4G card expires", "daysRemaining": "Days remaining", "lowBatteryAlert": "Low battery alert", "notReportedOnTime": "Not reported on time", "operate": "Operate", "remoteKillAndStun": "Remote kill/stun", "removeStun": "Remove stun", "timeoutDuration": "Timeout duration"}, "builtInAttr": {"admin": "Admin", "dataManager": "Data Manager", "devHis": "<PERSON>ce History Manager", "maintain": "Maintain", "nfcManager": "NFC manager", "optHis": "Option History Manager", "root": "Default Unit", "tourist": "tourist"}, "common": {"more": "More", "No1": "No 1", "No2": "No 2", "RoleQuickPerm": "Role Quick Perm", "UserQuickPerm": "User Quick Perm", "abnormalData": "Abnormal data", "add": "Add", "back": "Back", "baiduMap": "<PERSON>", "cancel": "Cancel", "channelDevNoSetting": "Channel Device No Setting", "channelNo": "Channel No", "clear": "Clear", "clickAgainExitApp": "Click again to exit the app", "clickAgainToExit": "Click again to exit the app", "cmdSendFailed": "Cmd Send Failed", "cmdSendSuc": "Cmd Send Success", "cmdSendSucAndWaitMarkerRes": "The command has been issued, waiting for the boundary stake to respond to the command when it wakes up.", "confirm": "Confirm", "confirmExit": "Are you sure to exit?", "continue": "Continue", "delete": "Delete", "details": "details", "download": "Download", "downloadFailed": "Download failed", "downloadSuccess": "Download successful", "edit": "Edit", "dataEdit": "Data Edit", "exactSearch": "Exact Search", "export": "Export", "failed": "Failed", "finish": "Finish", "gaodeMap": "<PERSON>", "hideColShortcuts": "Hide Shortcuts", "ignore": "Ignore", "import": "Import", "loadBtnLabel": "Click to<br>Load<br>Panorama", "localChannelDevNo": "Local Channel Device No", "mediaData": "Media Data", "modify": "Modify", "new": "New", "no": "No", "preview": "Preview", "refresh": "refresh", "reset": "Reset", "roleEdit": "Role Edit", "search": "Search", "select": "Please select", "show": "Show", "showColShortcuts": "Show Shortcuts", "success": "Success", "upload": "Upload", "uploadTips": "When uploading images, please distinguish between ordinary images and panoramic images.", "yes": "Yes"}, "controller": {"port": "Port", "serverAddress": "Server address", "target": "Target controller", "warning": "Are you sure to modify the server address?", "warningCaption": "Warning: the controller will no longer log in to the current system after modification!"}, "controllerErrReason": {"lowPower": "Low Power", "networkType": "Network Type", "offline": "Offline", "timeError": "Time anomaly"}, "dataBaseError": {"childMarkerExist": "Controller had child marker", "controllerChannelCannotRepeated": "Channel cannot be repeated under the same base station controller", "controllerHwidErr": "Controller HWID has been used", "controllerNoErr": "Controller No has been used", "controllerNotExist": "The controller data does not exist", "controllerPKeyErr": "data exception，please try again", "deleteOtherDataFirst": "Please delete {name} data for this organization first", "lackOrgPrivilege": "Lack Org Privilege", "loginNameErr": "Login Name has been used", "markerHwidErr": "Marker HWID has been used", "markerNoErr": "Marker no has been used", "markerNotExist": "The marker data does not exist", "notSetPermForAddedOrg": "Permission setting for this organization failed", "orgIdErr": "Org Id has been used", "parControllerErr": "Parent Controller Error", "parUnitErr": "Parent Unit Error", "permissionErr": "No Permission", "queueNoErr": "Marker queue number has been used", "redLineNoErr": "Red Line Id has been used", "roleNameCannotRepeated": "Role names cannot be repeated in the same unit", "unitNotExist": "The unit data does not exist", "uploadUserNotExist": "The uploaded user data does not exist", "userNoErr": "User No has been used", "userRoleErr": "User Role Error"}, "date": {"days": "days", "hours": "hours", "minutes": "minutes", "nDaysAgo": "{num} days ago", "notMax3month": "date not max than 3 month", "notMax3year": "date not max than 3 years"}, "upgradeApp": {"checkUpdate": "Check Update", "upgradeFailed": "Upgrade Failed", "errorMessage": "Error message: {error}", "startDownload": "Start Download", "isInstallNewApp": "The new version of APP ({newVersion}) has been downloaded. Do you want to install it now?", "install": "Install Now"}, "export": {"noData": "No data available", "openFile": "Open a file"}, "fancytree": {"removeAlarm": "Remove alarm"}, "form": {"4GMarkers": "4G boundary stakes", "4GMarkersPro": "4G boundary stakes Pro", "ControllerNo": "Controller No", "ExpirationDate": "Card number expiration date", "GPSAlarm": "GPS alarm", "HasInstallStone": "Stone installed", "Maintain": "Maintain Permission", "MarkerNo": "Marker No", "NFCPatrol": "NFC patrol", "Operation": "user operation", "Ping": "Query Status", "abnormalControllers": "Abnormal Controllers", "abnormalData": "Abnormal Statistic", "abnormalMarkers": "Abnormal Markers", "addr": "Server address", "addrPort": "Server port", "addrRulesFail": "Server address is incorrect", "alarm": "Alarm", "alarmInterval": "Alarm interval(s)", "alarmNum": "Number of alarms", "alarmReason": "Alarm reason", "amplitude": "Vibration amplitude(level)", "attitude": "Slope", "availableChannels": "Available channels", "availableQueueNo": "Available Queue No ", "backQueryPage": "Back to the query page", "baseStation": "Base station", "baseStationChannel": "Controller channel", "batteryPower": "Battery Power", "boundaryID": "Boundary ID", "boundaryModel": "Boundary Model", "boundaryType": "Boundary Type", "builtin": "Built-in", "cameraAlreadyDiedRemotely": "<PERSON> already died remotely", "cameraAlreadyFainted": "Camera already fainted", "cameraKillRemotely": "Camera remote kill", "cameraRemoteActive": "Camera remote active", "cameraRemoteStun": "Camera remote stun", "changeDefaultChannelNo2One": "change default channelNo to one", "changeDefaultChannelNo2Two": "change default channelNo to two", "checkAlarmOnly": "Only check the alarm", "checkCardOnly": "Check card only", "close": "Close", "cmdUploadTime": "Cmd upload time", "confirmCancelKillOrStun": "Confirm to cancel {cmd} status?", "confirmKillOrStun": "Confirm setting {cmd} status?", "controller": "Controller", "controllerChannel": "Controller channel", "controllerHWID": "Controller ID", "controllerID": "Controller No", "creator": "Creator", "customID": "Serial number", "data": "Data", "dataVersion": "DataVersion", "dateRangeBottomTip": "Start and end dates are abnormal", "deLaySlTime": "Delay sleep time (s)", "debugModelTime": "Debug mode time (s)", "default": "<PERSON><PERSON><PERSON>", "defaultNetworkType": "Default Network Type", "description": "Description", "deviceContext": "The device context", "deviceType": "Device type", "dirft": "Displacement distance (m)", "dumping": "Dumping Alarm", "duration": "Vibration duration (s)", "edit": "Edit", "endTime": "End time", "enterFilterKey": "Enter keyword filter", "errReason": "Error Reason", "expirationDateFail": "Does not meet the requirements of YYYY-MM-DD", "fourG": "4G", "fsk": "FSK", "fullName": "Full name", "iccidExists": "Card number already exists", "iccidRuleCheckFail": "Does not meet iccid's standard specifications", "image": "Image", "infrared": "Infrared alarm threshold", "infraredTooltip": "0 means off, 1~10", "ipInfo": "IP info", "isInstallDevice": "Device installed", "keepAdding": "Keep adding", "killRemotely": "boundary stake remote kill", "lastReportTime": "Last reported time", "lastUploadTime": "Last Upload Time", "lastUploadUser": "Last Upload User", "lat": "Lat", "limitedNetwork": "Limited network", "lngLat": "Longitude and latitude", "loginName": "Login name", "loginPassword": "Login password", "lon": "Lon", "long": "long", "lowPower": "Low Power Alarm", "lowPowerAlarm": "Low power alarm", "mapShowLevel": "Display level on the map", "markerAlreadyDiedRemotely": "Already died remotely", "markerAlreadyFainted": "Already fainted", "markerChannel": "Communication channel", "markerDayInterval": "Punch time interval (hour)", "markerEmergentInterval": "Launch interval after alarm (sec.)", "markerName": "Marker name", "markerQueueInterval": "Queued launch interval (sec.)", "markerQueueNo": "Queue number", "markerRedLineNo": "Marker Red Line No", "markerRemoteKillStun": "Remote kill/stun", "markerWakeupBaseTime": "Wake up the benchmark time", "movedAlarm": "Moved alarm", "name": "Name", "noData": "Empty data", "noMatchData": "No matching data", "normal": "Normal", "normalImage": "Normal image", "normalMarkers": "Private network boundary stakes", "notConnectRedLine": "Not connect red line graph", "note": "Note", "offline": "Offline", "oldPwdError": "Old password error", "oneMonth": "one month", "oneYear": "one year", "online": "Online", "pInputUserName": "Input username please", "panorama": "Panorama", "parentChannelCount": "Parent Channel Count", "parentController": "Parent controller", "parentControllerAndChannel": "Parent controller/Parent Channel No", "parentUnit": "Parent unit", "phone": "Phone", "photos": "Images", "pwdDef": "Two passwords are inconsistent", "query": "Query", "realParentController": "Real Parent Controller", "receiptTime": "Receipt time", "redLineGraphRules": "Red line graph rules", "remoteActivition": "boundary stake Remote activition", "remoteStun": "boundary pole remote dizziness", "repeater": "<PERSON><PERSON><PERSON>", "reportNormal": "Report normally", "role": "Role", "rolePermission": "Role Permissions", "selectImages": "Please select picture / panorama / video", "sendCmd": "Send command", "short": "short", "shortName": "Short name", "showTrackHistory": "Show Track History", "sortValue": "Sort value", "startTime": "Start time", "status": "Last Status", "submit": "Confirm", "tamperAlarm": "Tamper alarm", "test": "Test Alarm", "testAlarm": "Alarm Test", "threeMonth": "three months", "threeYear": "three year", "time": "Time", "transferSourceTit": "source data", "transferTargetTit": "Target data", "twoMonth": "two months", "type": "Type", "unit": "Unit", "unknown": "Unknown", "uploadTime": "Upload time", "uploadUser": "Uploaded user", "userName": "User name", "userRole": "User role", "vibration": "Vibration alarm threshold", "video": "Video", "wakeUpInterval": "Wake-up interval (h)"}, "historyTable": {"alarmCapture": "Alarm capture", "captureTime": "Photo time", "captureType": "Photo type", "clickToViewFullImage": "Click to view full image", "manualCapture": "Manual capture", "photoUpload": "Photo upload", "sensorCapture": "Infrared sensor capture", "timerCapture": "Timer capture"}, "import": {"errorInfo": "Error info", "notFoundController": "Can't found the superior controller", "simultaneously": "ICCID card and expiration date should be filled in at the same time"}, "languages": {"enUs": "English", "zhCN": "中文"}, "login": {"loginName": "Login name", "otherWay": "Other ways to log in", "password": "Password", "remember": "Remember me", "submit": "<PERSON><PERSON>", "system": "System number", "title": "System login"}, "maps": {"Kilometers": "KM", "bus": "Bus", "drive": "Drive", "endPoint": "Ending point", "getLngLatTip": "Please click on the map to get the coordinates, longitude and latitude", "getOff": "Get off", "getOn": "Get on", "lessTransfer": "Less transfer", "lessWalking": "Less walking", "measure": "Total distance", "meter": "M", "pause": "Pause", "play": "Play", "search": "Find the controller, boundary stake", "shortTime": "Short time", "startPoint": "Starting point", "stationCount": "{num} stations", "stop": "Stop", "switchSatelliteLayer": "Switch satellite map", "switchStreetLayer": "Switch street map", "transferStation": "Transfer within the station", "walking": "Walking", "zoom": "Zoom"}, "markerStatistics": {"exportAll": "Export All", "installed": "Installed", "markerInstallStatus": "<PERSON><PERSON>", "markerTotal": "Number of markers", "notInstallDevice": "Not install device", "notInstallStone": "Not install stone"}, "menus": {"MarkerNFCPatrolHistory": "NFC patrol history", "alarmQuery": "Alarm query", "boundaryMarker": "Boundary marker", "changeParameters": "Change parameters", "cmdTest": "Command Test", "command": "Command", "confirmCmd": "Please confirm to execute the {cmd} command", "controller": "Controller", "controllerOnlineRecords": "Controller records", "currentUpdate": "This update", "data": "Data", "dataStatistics": "Data statistics", "deviceQuery": "Device query", "downLoad": "App DownLoad", "enableScrollTitle": "Turn on scrolling title", "faultQuery": "Fault query", "help": "Help", "images": "Images", "markerHistory": "Boundary marker history", "markerStatistics": "<PERSON>er Install Statistics", "markerUploadImageHistory": "Image upload history", "modifyServerAddr": "Modify server address", "optionHistory": "option history", "organization": "Organization", "patrolLines": "Inspection route", "patrolRules": "Patrol rules", "photoQuery": "Images query", "query": "Query", "role": "Role", "settings": "Settings", "setupTracking": "Setup tracking", "stopScrollTitle": "Stop scrolling title", "stopTracking": "Stop tracking", "syncSettingTime": "Failed to synchronize remote kill/remote stun command time!", "systemDoc": "System document", "toggleFullscreen": "Toggle fullscreen", "trackPlayback": "Track playback", "user": "User", "userSettings": "User Settings", "versionInfo": "Version Information"}, "message": {"4GMarkerOffline": "The device has automatically cleared the alarm and gone to sleep!", "addFailed": "Add failed", "addSuccess": "Add successfully", "alarmTime": "Alarm Time", "autoLoginExpired": "Auto login has expired", "cameraError": "Camera Error", "confirmPlayTrace": "There are currently multiple boundary marker alarm data, please confirm to play them back in the same track?", "controllerOffline": "Controller is not online", "deleteFailed": "Delete failed", "deleteHasChildUnit": "{name} is the superior of other units, you need to delete all subordinate units to delete", "deleteOrgWarn": "When a unit is deleted, all data under the unit will be deleted synchronously. Please confirm whether to delete the unit.", "deleteSuccess": "Delete successfully", "duplicateData": "Duplicate data", "duplicateOrgID": "Duplicated custom ID: {name}", "exportPath": "Export directory: {path}", "exportSuccess": "Export succeeded", "failedPlanRoute": "Failed to plan route", "importDataHasError": "There is an error in the imported data, please export the data and check for errors", "importSuccess": "Import successfully", "invalidRmAlarmCmd": "Invalid bysMarker or controller", "lackOrgAuth": "No organization authority", "loadingData": " Loading data, please wait ...", "localErr": "Local Error", "locationFailed": "Location failed", "loginSuccess": "<PERSON><PERSON> successfully", "loginTimeError": "Login time error", "loginTimeout": "Login timeout", "maybeOffline": "May be offline", "modifyControllerNoWarn": "If you want to change the controller name, you need to change the controller hardware configuration and login with the new name", "noData": "No data", "noOptPerm": "Permission denied", "noSuchSys": "No such System No", "noSuchUser": "No such user", "notAvailableSortNumber": "The queue number ({num}) is not available", "notFoundEndPoint": "No found ending point", "notFoundStartPoint": "No found starting point", "notifyICCIDAlreadyExist": "The ICCID of the current online boundary marker {markerNo} is the same as the ICCID of the boundary marker {duplicateMarkerNo} in the system. Please confirm whether to update the online boundary marker information.", "notifyICCIDAndIMEIConflict": "The IMEI of the boundary marker {MarkerNo} in the system is: {optStr}; the IMEI reported by the boundary marker {MarkerNo} is: {IMEI}; please confirm whether to update the online boundary marker information!", "offline": "{name} offline", "online": "{name} online", "onlineController": "Online controller", "parameterError": "Parameter error", "parentUnitErr": "Superior unit error", "passwordErr": "Wrong password", "paused": "Paused", "playFinished": "Play finished", "queryFailed": "Query failed", "querySuccess": "Query successfully", "recommendedWalking": "The path is too short, it is recommended to walk", "refreshSuccessful": "Refresh successful", "removeAlarm": "{name} has removed the alarm", "removeAlarmSuccess": "Remove the alarm successfully", "requestTimeout": "Request timeout", "rmAlarmCmdShakeProof": "Remove AlarmCmd Shake Proof Warning", "sendFailed": "Command failed to send", "sendSuccess": "Command sent", "sureDeleteIt": "Are you sure you want to delete it?", "syncICCIDOnSystem": "Update ICCID to online boundary stake data", "syncIMEIOnSystem": "Update IMEI to online boundary stake data", "timeout": "{action} {name} timeout", "timeoutLabel": "Time out", "tracePlayTooltip": "Only alarm tracks can be played", "updateChannelNoFai": "Update ChannelNo Failed", "updateChannelNoSuc": "Update ChannelNo Successfully", "updateFailed": "Update failed", "updateSuccess": "update successfully", "uploadFailed": "Upload failed", "uploadSuccess": "Upload successfully", "within500Meters": "Within 500 meters from the start and end points, return to the line"}, "permission": {"cmd": "Command", "db": "Data", "permissionCenter": "Permission Center"}, "rules": {"IDAlreadyExists": "The ID already exists", "IMEIExists": "IMEI already exists", "availableRange": "Available range: {min}~{max}", "cannotEarlierStartTime": "Cannot be earlier than the start time", "cannotLaterEndTime": "Can not be later than the end time", "correctPhone": "Please enter a correct phone number", "invalidDateTime": "Invalid date-time", "invalidTime": "Invalid time", "invalidTimeDiff": "time diff can not exceed 1 month", "invalidTimeDiffy1": "time diff can not exceed 1 year", "maxLength": "Maximum input {len} characters", "maxValue": "Max:{max}", "minValue": "Min:{min}", "mustBeBCDCode": "Must be BCD code", "mustBeIpOrDomain": "Must be an IP or domain name", "mustBeLat": "Must be a valid latitude", "mustBeLon": "Must be a valid longitude", "mustbeGtZero": "An integer that must be greater than 0", "required": "Required", "validIMEI": "Please enter a valid IMEI", "validIMEILength": "IMEI length should be 15 digits", "validSystemNum": "Please enter a valid system number"}, "settingsPage": {"account": "User Details", "ascSort": "Ascending", "bysMarkerFirst": "Boundary first", "chooseLogo": "Choose logo", "closeAlarmAudio": "Alarm Audio", "closeAlarmPopupWind": "Alarm Popup Wind", "confirmNewPassword": "Confirm New Password", "confirmUpdatePwTip": "Confirm to change the current user password?", "confirmUpdateServerTip": "Confirm to modify the server address of the current boundary stub?", "confirmUpdateSysSettings": "Confirm to modify the current system settings?", "controllerFirst": "Controller first", "descSort": "Descending", "deviceSortType": "Device tree sorting: {type}", "deviceTreeSortRule": "Device tree sorting rules: {type}", "loginName": "Login Name", "newPassword": "New Password", "oldPassword": "Old Password", "others": "Other Settings", "picFormat": "The picture format should be JPG/PNG", "picSize": "Image size should be no more than 100kb", "security": "Security Settings", "serverSettings": "Server settings", "showNotInstalledMarker": "Show not installed marker", "patrolReminder": "Patrol Reminder", "submitModify": "Submit Modify", "sysLogo": "System LOGO", "sysTitle": "System Title", "systemSetting": "System settings", "unit": "User Unit", "userName": "User Name", "userSettings": "Personal settings"}, "siteTitle": "Beifeng BF-PMSS01 smart electronic boundary stake system", "sslUpdate": {"certExpired": "The certificate has expired", "certUpdateSuccess": "Certificate updated successfully", "certificate": "Certificate file (.crt)", "domainName": "Website domain name", "expiredDate": "Expiration date", "issueDate": "Issue date", "key": "Key file (.key)", "noFileSelected": "Please select a certificate and key", "token": "Update password", "updateCertTips": "Upload the HTTPS/SSL certificate to the server. This operation cannot be undone, so please proceed with caution!", "updateCertificate": "Update certificate"}, "statistics": {"currentNetworkType": "Current network type", "off": "Off", "power": "Power"}, "tranckHisReplay": {"currentItem": "Current Item Index", "notrackData": "The valid positioning data is empty", "speed": "Speed"}, "userRole": {"btnBack": "Back", "btnContinue": "Continue", "btnFinish": "Finish", "check": "Check Box", "role": "Roles", "roleTab": "Select Role", "unitTab": "Select Unit", "userRole": "User Permission"}, "userTab": {"checked": "Checked", "role": "Role", "roleInfo": "Role Information", "roleSelection": "Role Selections", "unit": "Unit", "unitSelection": "Unit Selections"}, "version": {"serveRunTime": "Server Run Time", "serverBuildTime": "Server Build Time", "serverStartTime": "Server Start Time", "serverVersion": "Server Version", "webBuildTime": "Client Build Time", "webVersion": "Client Version"}, "patrolReminder": {"nearbyMarker": "You are near marker: {markerNo}"}}
window.__bys_web_config__ = {
  // 地图配置
  map: {
    // 地图缩放级别,3~22
    zoom: 13,
    // 最大缩放级别
    maxZoom: 22,
    // 最小缩放级别
    minZoom: 3,
    // 地图中心坐标 [经度，纬度]
    center: [
      113.29669146017466,
      23.187383816174417
    ],
    // 地图样式，satellite 或 streets
    style: 'streets',
    // 客户端密钥
    // apk: '93c5c850399ca62f0f16ba4f95f7688e'
    // 服务端密钥，以便服务器缓存
    apk: '2cdb1893692f09002a74458c86cde96d'
  },
  // 首次进入系统时加载的语言，支持以下语言：['zh-hans','en-us']
  language: 'zh-hans',
  // 异常打卡间隔，默认3天，即超出3天没有打卡的界桩才视为异常，需要维护
  abnormalTime: 3,
  // 界桩报警时，上传的定位数据与系统设置的经纬度相关多少米，同时显示两个图标，默认10米
  alarmDistance: 10,
  // App导出Excel数据目录
  downloads: 'downloads'
}

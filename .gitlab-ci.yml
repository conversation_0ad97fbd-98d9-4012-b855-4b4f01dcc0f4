stages:
  - lint
  - release
  - deploy

# 设置相关变量
variables:
  BuildDir: ./dist
  ServerDir: /bys
  DeployDir: $ServerDir/web
  AppDir: $ServerDir/web/app
  Server4G: E:\bys-4g
  DeployDir4G: $Server4G\web
  AppDir4G: $DeployDir4G\app

cache:
  key: $CI_PROJECT_NAME.$CI_COMMIT_REF_NAME
  paths:
    - node_modules/

.env_script:
  before_script:
    # - curl https://hosts.gitcdn.top/hosts.txt >> /etc/hosts
    # - curl https://gitlab.com/ineo6/hosts/-/raw/master/next-hosts >> /etc/hosts
    - yarn config set registry https://registry.npmmirror.com/
    - yarn config set sass_binary_site https://npmmirror.com/mirrors/node-sass/
    - yarn config set disturl https://npmmirror.com/mirrors/node/
    - yarn config set sharp_binary_host "https://npmmirror.com/mirrors/sharp"
    - yarn config set sharp_libvips_binary_host "https://npmmirror.com/mirrors/sharp-libvips"
    # - yarn add sharp

lint:
  extends: .env_script
  image: node:18-bullseye
  stage: lint
  tags:
    - docker
  only:
    - branches
  script:
    - yarn
    - yarn lint

# 自动添加tag和发布release
release:
  stage: release
  image: linfulong/semantic-release:latest
  tags:
    - docker
  only:
    - main
  except:
    - api
    - web
  script:
    - npx semantic-release
  cache: { }
  artifacts:
    expire_in: 1 day
    paths:
      - package.json

deploy-spa:
  extends: .env_script
  image: node:18-bullseye
  stage: deploy
  tags:
    - docker
  only:
    - master
  script:
    - yarn
    - yarn build
    - export bysversion=$(node -p "require('./package.json').version")
    - \cp -r $BuildDir/spa-$bysversion/* $DeployDir/
    - node ./clearDeployCache.js -s $BuildDir/spa-$bysversion/ -t $DeployDir/
    - ./latest.version.sh $AppDir/ $bysversion
  dependencies:
    - release
  artifacts:
    name: web-spa
    paths:
      - $BuildDir
  retry: 1

deploy-android:
  extends: .env_script
  image: linfulong/quasar-builder:node18-gradle7.6-jdk11
  stage: deploy
  tags:
    - docker
  only:
    - master
  script:
    - yarn
    - ./build-android.sh
    - export appName=$(node -p "require('./package.json').appName")
    - export bysversion=$(node -p "require('./package.json').version")
    - \cp -rf $BuildDir/cordova-$bysversion/android/apk/release/$appName-$bysversion.apk $AppDir/
  dependencies:
    - release
  artifacts:
    name: android-apk
    paths:
      - $BuildDir
  retry: 1

test-android:
  extends: .env_script
  image: linfulong/quasar-builder:node18-gradle7.6-jdk11
  stage: deploy
  tags:
    - docker
  rules:
    - if: $CI_COMMIT_MESSAGE =~ /^test-android/
    - if: $CI_PIPELINE_SOURCE == "web"
  script:
    - yarn
    - ./build-android.sh
  artifacts:
    name: test-android
    paths:
      - $BuildDir

# 以下为4g界桩的部署，ubuntu上编译，windows上部署
build-spa-4g:
  extends: .env_script
  image: node:18-bullseye
  stage: deploy
  tags:
    - docker
  only:
    - main
  script:
    - yarn
    - yarn build
  dependencies:
    - release
  artifacts:
    name: web-spa
    paths:
      - $BuildDir
      - package.json
  retry: 1

deploy-spa-4g:
  stage: deploy
  tags:
    - **********
  only:
    - main
  script:
    - $bysversion = (node -p "require('./package.json').version")
    - Copy-Item "$BuildDir/spa-$bysversion/*" -Destination "$DeployDir4G/" -Recurse -Force
    - node ./clearDeployCache.js -s "$BuildDir/spa-$bysversion/" -t "$DeployDir4G/"
  needs:
    - build-spa-4g

build-android-4g:
  extends: .env_script
  image: linfulong/quasar-builder:node18-gradle7.6-jdk11
  stage: deploy
  tags:
    - docker
  only:
    - main
  script:
    - yarn
    - ./build-android.sh
  dependencies:
    - release
  artifacts:
    name: android-apk
    paths:
      - $BuildDir
      - package.json
  retry: 1

deploy-android-4g:
  stage: deploy
  tags:
    - **********
  only:
    - main
  script:
    - $appName = (node -p "require('./package.json').appName")
    - $bysversion = (node -p "require('./package.json').version")
    - Copy-Item "$BuildDir/cordova-$bysversion/android/apk/release/$appName-$bysversion.apk" -Destination "$AppDir4G/" -Force
    - .\latest.version.ps1 "$AppDir4G/" $bysversion
  needs:
    - build-android-4g

/* eslint-env node */

/*
 * This file runs in a Node context (it's NOT transpiled by Babel), so use only
 * the ES6 features that are supported by your Node version. https://node.green/
 */

import { configure } from 'quasar/wrappers'
import path from 'path'
import { readdirSync, readFileSync, writeFileSync, lstatSync } from 'fs'
import { version, appId } from './package.json'
import dotenv from 'dotenv'

// 获取版本编译时间
function clientBuildTime() {
  let isoString = new Date().toISOString()
  isoString = isoString.slice(0, 10) + ' ' + isoString.slice(11, 19)
  return isoString
}

// 获取git版本hash
function clientGitTag() {
  let gitHEAD = readFileSync('.git/HEAD', 'utf-8').trim()
  let gitVersion = ''
  if (gitHEAD.startsWith('ref:')) {
    let ref = gitHEAD.slice(4).trim()
    gitVersion = readFileSync(`.git/${ref}`, 'utf-8').trim()
  } else {
    gitVersion = gitHEAD
  }

  return gitVersion
}

function getDevServer(ctx, env) {
  const protocol = env.protocol ?? 'http:'
  const hostname = env.hostname ?? '127.0.0.1'
  const port = env.port ?? '80'
  const baseUrl = `${protocol}//${hostname}:${port}`
  const proxyContext = ['/mediaUpload', '/media', '/serverVersion', '/DataServer', '/app', '/downloadCameraImage', '/sslupdate']
  const proxy = proxyContext.map(api => {
    return {
      [api]: {
        target: baseUrl,
        changeOrigin: true,
        // pathRewrite: {
        //   '^/api': ''
        // }
      }
    }
  }).reduce((p, c) => Object.assign(p, c), {})

  return {
    // https: protocol === 'https:',
    https: true,
    open: true, // opens browser window automatically
    proxy,
  }
}

// 将src目录和子级目录生成别名配置
function generateAlias() {
  const prefix = '@'
  const aliasMap = {
    [`${prefix}app`]: path.resolve(__dirname, './'),
    [`${prefix}src`]: path.resolve(__dirname, './src'),
    [`${prefix}public`]: path.resolve(__dirname, './public'),
  }

  const paths = readdirSync('./src')
  for (let i = 0; i < paths.length; i++) {
    const p = `./src/${paths[i]}`
    if (lstatSync(p).isFile()) {
      continue
    }

    aliasMap[`${prefix}${paths[i]}`] = path.resolve(__dirname, p)
  }

  return aliasMap
}

function overwriteTsconfig(alias) {
  const aliasMap = {}
  for (let key in alias) {
    const item = alias[key]
    aliasMap[key + '/*'] = [item.replace(__dirname, '.') + '/*']
  }
  //修改tsconfig.json的配置,添加上别名配置json
  const tsconfig = readFileSync(path.resolve(__dirname, 'tsconfig.json')).toString()
  const tsconfigMap = JSON.parse(tsconfig)
  Object.assign(tsconfigMap.compilerOptions, {
    baseUrl: '.',
    paths: {
      ...tsconfigMap.compilerOptions.paths,
      ...aliasMap,
    },
  })
  writeFileSync('tsconfig.json', JSON.stringify(tsconfigMap, null, 2))
}

// 生成别名配置,tsconfig.json需要配置
const alias = generateAlias()
overwriteTsconfig(alias)

function extendViteConf(viteConf) {
  Object.assign(viteConf.resolve.alias, alias)
  Object.assign(viteConf.optimizeDeps, {
    include: ['jquery'],
  })
  Object.assign(viteConf.define, {
    CLIENT_BUILD_TIME: `"${clientBuildTime()}"`,
    CLIENT_GIT_TAG: `"${clientGitTag()}"`,
    CLIENT_VERSION: `"${version}"`,
  })

  return viteConf
}

// Configuration for your app
// https://v2.quasar.dev/quasar-cli-vite/quasar-config-js
export default configure(function(ctx) {
  // Load environment variables from .env.dev
  if (ctx.dev) {
    dotenv.config({ path: path.resolve(__dirname, '.env.dev') })
  }

  return {
    eslint: {
      fix: true,
      // include: [],
      // exclude: [],
      // rawOptions: {},
      warnings: true,
      errors: true
    },

    // https://v2.quasar.dev/quasar-cli-vite/prefetch-feature
    // preFetch: true,

    // app boot file (/src/boot)
    // --> boot files are part of "main.js"
    // https://v2.quasar.dev/quasar-cli-vite/boot-files
    boot: [
      'config',
      'i18n',
      'main',
      'jQuery',
    ],

    // https://v2.quasar.dev/quasar-cli-vite/quasar-config-js#css
    css: [
      'app.scss'
    ],

    // https://github.com/quasarframework/quasar/tree/dev/extras
    extras: [
      // 'ionicons-v4',
      'mdi-v5',
      // 'fontawesome-v6',
      // 'eva-icons',
      // 'themify',
      // 'line-awesome',
      // 'roboto-font-latin-ext', // this or either 'roboto-font', NEVER both!

      'roboto-font', // optional, you are not bound to it
      'material-icons', // optional, you are not bound to it
    ],

    // Full list of options: https://v2.quasar.dev/quasar-cli-vite/quasar-config-js#build
    build: {
      target: {
        browser: ['es2019', 'edge88', 'firefox78', 'chrome87', 'safari13.1'],
        node: 'node20'
      },

      vueRouterMode: 'hash', // available values: 'hash', 'history'
      // vueRouterBase,
      // vueDevtools,
      // vueOptionsAPI: false,

      // rebuildCache: true, // rebuilds Vite/linter/etc cache on startup

      // publicPath: '/',
      // analyze: true,
      // rawDefine: {}
      // ignorePublicFolder: true,
      // minify: false,
      // polyfillModulePreload: true,
      // distDir

      extendViteConf,
      // viteVuePluginOptions: {},

      // vitePlugins: [],
      distDir: `dist/${ctx.modeName}-${version}`,
      envFolder: './',
    },

    // Full list of options: https://v2.quasar.dev/quasar-cli-vite/quasar-config-js#devServer
    devServer: getDevServer(ctx, process.env),

    // https://v2.quasar.dev/quasar-cli-vite/quasar-config-js#framework
    framework: {
      config: {
        cordova: {
          // add the dynamic top padding on iOS mobile devices
          iosStatusBarPadding: true,

          // Quasar handles app exit on mobile phone back button.
          backButtonExit: false,

          // On the other hand, the following completely
          // disables Quasar's back button management.
          backButton: false,
        }
      },

      iconSet: 'material-icons', // Quasar icon set
      lang: 'en-US', // Quasar language pack

      // For special cases outside of where the auto-import strategy can have an impact
      // (like functional components as one of the examples),
      // you can manually specify Quasar components/directives to be available everywhere:
      //
      // components: [],
      // directives: [],

      // Quasar plugins
      plugins: [
        'Notify',
        'LocalStorage',
        'SessionStorage',
        'Loading',
        'Dialog',
        'AppFullscreen',
        'BottomSheet',
      ],
    },

    // animations: 'all', // --- includes all animations
    // https://v2.quasar.dev/options/animations
    animations: [],

    // https://v2.quasar.dev/quasar-cli-vite/quasar-config-js#sourcefiles
    // sourceFiles: {
    //   rootComponent: 'src/App.vue',
    //   router: 'src/router/index',
    //   store: 'src/store/index',
    //   registerServiceWorker: 'src-pwa/register-service-worker',
    //   serviceWorker: 'src-pwa/custom-service-worker',
    //   pwaManifestFile: 'src-pwa/manifest.json',
    //   electronMain: 'src-electron/electron-main',
    //   electronPreload: 'src-electron/electron-preload'
    // },

    // https://v2.quasar.dev/quasar-cli-vite/developing-ssr/configuring-ssr
    ssr: {
      // ssrPwaHtmlFilename: 'offline.html', // do NOT use index.html as name!
      // will mess up SSR

      // extendSSRWebserverConf (esbuildConf) {},
      // extendPackageJson (json) {},

      pwa: false,

      // manualStoreHydration: true,
      // manualPostHydrationTrigger: true,

      prodPort: 3000, // The default port that the production server should use
      // (gets superseded if process.env.PORT is specified at runtime)

      middlewares: [
        'render' // keep this as last one
      ]
    },

    // https://v2.quasar.dev/quasar-cli-vite/developing-pwa/configuring-pwa
    pwa: {
      workboxMode: 'generateSW', // or 'injectManifest'
      injectPwaMetaTags: true,
      swFilename: 'sw.js',
      manifestFilename: 'manifest.json',
      useCredentialsForManifestTag: false,
      // useFilenameHashes: true,
      // extendGenerateSWOptions (cfg) {}
      // extendInjectManifestOptions (cfg) {},
      // extendManifestJson (json) {}
      // extendPWACustomSWConf (esbuildConf) {}
    },

    // Full list of options: https://v2.quasar.dev/quasar-cli-vite/developing-cordova-apps/configuring-cordova
    cordova: {
      // noIosLegacyBuildFlag: true, // uncomment only if you know what you are doing
    },

    // Full list of options: https://v2.quasar.dev/quasar-cli-vite/developing-capacitor-apps/configuring-capacitor
    capacitor: {
      hideSplashscreen: true
    },

    // Full list of options: https://v2.quasar.dev/quasar-cli-vite/developing-electron-apps/configuring-electron
    electron: {
      // extendElectronMainConf (esbuildConf)
      // extendElectronPreloadConf (esbuildConf)

      inspectPort: 5858,

      bundler: 'packager', // 'packager' or 'builder'

      packager: {
        // https://github.com/electron-userland/electron-packager/blob/master/docs/api.md#options

        // OS X / Mac App Store
        // appBundleId: '',
        // appCategoryType: '',
        // osxSign: '',
        // protocol: 'myapp://path',

        // Windows only
        // win32metadata: { ... }
      },

      builder: {
        // https://www.electron.build/configuration/configuration

        appId,
      }
    },

    // Full list of options: https://v2.quasar.dev/quasar-cli-vite/developing-browser-extensions/configuring-bex
    bex: {
      contentScripts: [
        'my-content-script'
      ],

      // extendBexScriptsConf (esbuildConf) {}
      // extendBexManifestJson (json) {}
    }
  }
})

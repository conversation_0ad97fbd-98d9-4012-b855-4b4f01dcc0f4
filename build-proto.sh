#!/usr/bin/env bash

# 重置工作目录
cd "$(dirname "$0")"

#设置proto协议文件路径变量
subModulePath=./git.kicad99.com
modulesPath=$subModulePath/ykit
protoPath=$modulesPath/database
rpcPath=$modulesPath/goutil/rpc
crudPath=$modulesPath/goutil/crud
docPath=$subModulePath/gzdev/bys/doc
outputPath=./src/ygen
yrpcmsgPath=$modulesPath/yrpcmsg

#patch protobufjs for doc
node node_modules/.bin/pbpatch
pbBin=node_modules/protobufjs-patch/node_modules/protobufjs/bin

#创建编译输出目录
mkdir -p $outputPath

#$1需要编译的文件，$2为文件路径
buildProto(){
    $pbBin/pbjs --no-create --no-verify --no-convert --no-delimited --force-long --alternate-comment-mode -t static-module -w es6 \
        -p $protoPath -p $rpcPath -p $crudPath -p $yrpcmsgPath \
        -o $outputPath/$1.js $2/$1.proto

    $pbBin/pbts -o $outputPath/$1.d.ts $outputPath/$1.js
}

#生成rpc/crud模块
buildProto rpc $rpcPath
buildProto crud $crudPath
buildProto yrpcmsg $yrpcmsgPath

#生成org/user/...等模块
protoArray=(config image org org.rpc user user.api userOrgPrivilege userPermission userSession crudlog)
for protoName in ${protoArray[*]}
do
buildProto $protoName $protoPath
fList=${protoName}.list
if [ -f "$protoPath/$fList.proto" ]; then
buildProto $fList $protoPath
fi
done

#生成bysdb模块
protoArray=(bysdb controller bys.api)
for protoName in ${protoArray[*]}
do
buildProto $protoName $docPath
fList=${protoName}.list
if [ -f "$docPath/$fList.proto" ]; then
buildProto $fList $docPath
fi
done

#生成yrpc静态模块
protoc --proto_path=$protoPath --proto_path=$rpcPath --proto_path=$crudPath --proto_path=$docPath --proto_path=$yrpcmsgPath \
--ygen-jsyrpc_out=$outputPath $protoPath/*.rpc.proto $protoPath/*.api.proto $docPath/*.rpc.proto $docPath/*.api.proto

# 添加缺失的Long类型引用
addMissLongType(){
    sed -i '1s;^;import { Long } from "protobufjs"\;\n;' $outputPath/$1
}

# bys.api.proto/yrpcmsg 使用了Long类型
addMissLongType bys.api.d.ts
addMissLongType yrpcmsg.d.ts

#使用prettier格式化生成的文件
node ./node_modules/.bin/prettier $outputPath/*  --write --no-semi --single-quote  --trailing-comma all

#将生成的文件添加到git中
git add $outputPath/*

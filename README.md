# 北峰智慧界桩监控系统客户端

> 本项目使用 [@quasar/cli](https://quasar.dev/start/quasar-cli) 脚手架进行项目搭建
> UI界面使用Quasar框架开发，一套代码，多端运行。
> App使用cordova编译打包，当前只支持Android 7.0+系统

- `master`分支为专网界桩系统(旧版)
- `main`分支为包含4G界桩功能

#### 项目主要使用的库
* 指令传输：[Websocket](https://developer.mozilla.org/en-US/docs/Web/API/WebSockets_API)
* 数据编码：[protobufjs](https://github.com/protobufjs/protobuf.js)
* UI：[Quasar](https://quasar.dev)
* 地图：[maplibre-gl](https://github.com/maplibre/maplibre-gl-js/)
* 树形结构：[Fanctree](https://github.com/mar10/fancytree)


#### 项目环境
* @quasar/cli + node + typescript + cordova + eslint

#### 克隆项目
```bash
# or https://git.kicad99.com/bys/web.git
# --recurse-submodules 该参数可以同步初始化子模块
<NAME_EMAIL>:bys/web.git --recurse-submodules

# 如果没有使用--recurse-submodules参数，clone项目后，进入项目根目录
git submodule update --init --recursive

# 如果子模块没有切换到master分支，则使用下面命令
git submodule update --remote --rebase
git submodule foreach git switch master
```

#### 数据协议
> proto协议以`git submodule`添加到项目中，需要运行`build-proto.sh`，编译出项目需要的协议模块
```bash
./build-proto.sh
```

#### 环境变量配置
- 根据开发环境或线上环境，配置对应的env文件，设置服务器地址
- 开发环境：`env.local.dev`或者`env.dev`
- 线上环境：`env.local.prod`或者`env.prod`
- env配置的参数如下
  ```text
  protocol=http:
  hostname=************
  port=8110
  ```

#### 开发运行
```bash
# web
yarn serve

# android
yarn serve:android
```

#### 编译打包

- App编译打包的docker镜像的[Dockerfile](https://gitlab.com/linfulong/dockerfiles/-/blob/main/quasar-builder/Dockerfile)
```bash
# web
yarn build

# android
./build-android.sh
```

#### Android app 调试
* 真机调试需要进入`开发者模式`，打开`USB调试`
* 调试时需要使用`adb`，将app安装到手机上运行
* 详细教程请参考`Chrome`远程调试
```bash
# android debug
yarn android:debug
```

> 项目托管于`gitlab`，使用`gitlab-ci`自动部署项目
> App打包需要使用到 `android-sdk`, `java8` 工具
> App编译和签名详情请参考：https://quasar.dev/quasar-cli/developing-capacitor-apps/publishing-to-store

#### APK 签名
```bash
# To sign the unsigned APK
echo "password" | jarsigner -verbose -sigalg SHA1withRSA -digestalg SHA1 -keystore keystore_path app-release-unsigned.apk alias_name

# To generate release APK Android/sdk/build-tools/VERSION/zipalign
zipalign -v 4 app-release-unsigned.apk app-release.apk
```
#### 项目添加tag和release发布

* 使用 [semantic-release](https://github.com/semantic-release/semantic-release) 自动化tag和release发布过程

* git提交消息格式: (type([optional scope]): description)
  * **breaking**: A big feature/refactor which includes a breaking change
  * **feat**: A new feature
  * **fix**: A bug fix
  * **refactor**: A code change that neither fixes a bug nor adds a feature
  * **config**: Updating configurations of linter, ts, webpack, babel, etc.
  * **test**: Adding missing tests or correcting existing tests
  * **docs**: changing the readme or adding additional documentation
  * **no-release**: a specific type which you can use in specific cases when no release is needed for your change

> 常用的是**fix**, **feat**

#### semantic-release项目配置

* **settings/access_tokens**: 添加token，Scopes权限选择**api**和**write_repository**

* **settings/CI/CD**：**Variables**配置页下添加**GITLAB_TOKEN**或**GL_TOKEN**变量，参数值为上一步骤的token

#### 客户端更新网站证书

- 地址栏输入页面路由路径`/ssl_update`，完整地址参考：`https://1407.cchdx.com:5302/#/ssl_update`
- 打开页面后，选择对应的`证书文件(.crt)`、`密钥文件(.key)`、`更新密码`，然后点击更新证书
- `更新密码`为服务器`setting.ini`配置文件`[https]`部分下`ssl-update-token`字段参数值

# Write current build version number to corresponding file
param(
    [Parameter(Mandatory=$true)]
    [string]$workPath,

    [Parameter(Mandatory=$true)]
    [string]$version
)

$versionnumber = "versionnumber.txt"

# Create directory if it doesn't exist
if (-not (Test-Path $workPath)) {
    New-Item -ItemType Directory -Path $workPath -Force
}

Write-Host "version number: $version"
Set-Content -Path (Join-Path $workPath $versionnumber) -Value $version

<template>
  <dialogModal
    v-model="visible"
    v-model:maximized="maximized"
    :contentHeight="dataLen ? '68vh' : 'auto'"
    :content-class="'patrol-rules-cls'"
    :title="title"
  >
    <data-edit
      :data="NFCPatrolLineRulesData"
      :columns="columns"
      :form-title="title"
      :delete="deleteData"
      :insert="insertData"
      :update="updateData"
      :import-data="batchAddData"
      @new-row="newRow"
      v-model:currentRow="currentRow"
    >
      <template #form-content>
<!--        <patrol-rule-edit v-model:model-value="currentRow"></patrol-rule-edit>-->
        <div
          class="q-ml-sm"
          :class="$q.platform.is.android ? '' : 'patrol-rule-edit-content'"
        >
          <div class="fit row wrap q-mb-sm q-col-gutter-x-sm">
            <div class="col col-xs-12 col-md-6">
              <q-select
                v-model="currentRow.OrgRID"
                :label="$t('form.unit')"
                outlined
                dense
                clearable
                lazy-rules
                :rules="rules.OrgRID"
                :options="parentOptions"
                @filter="filterParent"
                options-dense
                map-options
                emit-value
              />
            </div>
            <div class="col col-xs-12 col-md-6">
              <q-input
                v-model="currentRow.Name"
                :label="$t('NFCPatrol.ruleName')"
                :rules="rules.Name"
                lazy-rules
                outlined
                dense
                clearable
                autofocus
              />
            </div>
            <div class="col col-xs-12 col-md-6">
              <input-number
                v-model:model-value="currentRow.CheckCount"
                :label="$t('NFCPatrol.patrolCount')"
                outlined
                dense
                clearable
                :rules="rules.CheckCount"
                :min="1"
                :max="99">
              </input-number>
            </div>
            <div class="col col-xs-12 col-md-6">
              <q-input
                ref="checkStartTimeRef"
                v-model="checkStartTime"
                :label="$t('NFCPatrol.checkStartTime')"
                mask="fulltime"
                outlined
                dense
                clearable
                lazy-rules
                :rules="rules.CheckStartTime"
                @update:model-value="changeCheckStartTime"
              >
                <template v-slot:append>
                  <q-icon
                    name="access_time"
                    class="cursor-pointer"
                  >
                    <q-popup-proxy
                      transition-show="scale"
                      transition-hide="scale"
                    >
                      <q-time
                        v-model="checkStartTime"
                        with-seconds
                        format24h
                        @update:model-value="changeCheckStartTime"
                      />
                    </q-popup-proxy>
                  </q-icon>
                </template>
              </q-input>
            </div>
            <div class="col col-xs-12 col-md-6">
              <q-input
                ref="checkEndTimeRef"
                v-model:model-value="checkEndTime"
                :label="$t('NFCPatrol.checkEndTime')"
                mask="fulltime"
                outlined
                dense
                clearable
                lazy-rules
                :rules="rules.CheckEndTime"
                @update:model-value="changeCheckEndTime"
              >
                <template v-slot:append>
                  <q-icon
                    name="access_time"
                    class="cursor-pointer"
                  >
                    <q-popup-proxy
                      transition-show="scale"
                      transition-hide="scale"
                    >
                      <q-time
                        v-model="checkEndTime"
                        with-seconds
                        format24h
                        @update:model-value="changeCheckEndTime"
                      />
                    </q-popup-proxy>
                  </q-icon>
                </template>
              </q-input>
            </div>
            <div class="col col-xs-12 col-md-6">
              <q-input
                v-model="currentRow.Note"
                type="textarea"
                :label="$t('form.description')"
                outlined
                dense
                clearable
                autogrow
                lazy-rules
                :rules="rules.Note"
                :maxlength="1024"
              />
            </div>
            <div class="col col-12">
              <date-range-picker v-model:model-value="effectiveTimeRange" :disable="!currentRow.EffectiveType" :mask="DateMask">
                <template #before>
                  <q-checkbox v-model="currentRow.EffectiveType" :true-value="0" :false-value="1" :label="$t('NFCPatrol.effective')" @update:model-value="changeEffectiveType" class="h-[40px] mr-2"/>
                </template>
              </date-range-picker>
            </div>
            <div class="col col-12">
              <q-checkbox v-model="currentRow.Day1" :label="$t('NFCPatrol.day1')"/>
              <q-checkbox v-model="currentRow.Day2" :label="$t('NFCPatrol.day2')"/>
              <q-checkbox v-model="currentRow.Day3" :label="$t('NFCPatrol.day3')"/>
              <q-checkbox v-model="currentRow.Day4" :label="$t('NFCPatrol.day4')"/>
              <q-checkbox v-model="currentRow.Day5" :label="$t('NFCPatrol.day5')"/>
              <q-checkbox v-model="currentRow.Day6" :label="$t('NFCPatrol.day6')"/>
              <q-checkbox v-model="currentRow.Day7" :label="$t('NFCPatrol.day7')"/>
            </div>
          </div>
        </div>
      </template>
    </data-edit>
  </dialogModal>
</template>

<script lang="ts" setup>
  import dataEdit from '@components/dataEdit/DataEdit.vue'
  import { useDialogModal } from '@utils/vueUse/dialog'
  import { computed, ref } from 'vue'
  import { bysdb } from '@ygen/bysdb'
  import { useI18n } from 'vue-i18n'
  import { useNFCPatrolData } from '@utils/vueUse/NFCPatrolData'
  import {
    DateMask,
    isBeforeOrSame,
    TimeMask,
    toLocalTime,
    toLocalTimeFormat,
    toUtcTime,
    toUtcTimeFormat,
    utcTime,
  } from '@utils/dayjs'
  import dayjs from 'dayjs'
  import { ICallOption } from 'jsyrpc'
  import { crud } from '@ygen/crud'
  import { yrpcmsg } from 'yrpcmsg'
  import log from '@utils/log'
  import { NFCPatrolLineAndRules, NFCPatrolLineRules } from '@services/dataStore'
  import { PrpcDbNFCPatrolLineRules } from '@ygen/bysdb.rpc.yrpc'
  import { v1 as uuidV1 } from 'uuid'
  import createSelfIncreaseStr from '@utils/createSelfIncreaseStr'
  import useEditor from '@utils/vueUse/editor'
  import InputNumber from '@components/InputNumber.vue'
  import DateRangePicker from '@components/DateRangePicker.vue'
  import { isValidTime } from '@utils/validation'
  import { QInput } from 'quasar';

  const i18n = useI18n()
  const { checkDataByRules } = useEditor()
  const title = computed(() => { return i18n.t('NFCPatrol.patrolRules')})
  const currentRow = ref<bysdb.IDbNFCPatrolLineRules>({
    RID: uuidV1(),
    OrgRID: '',
    Name: '',
    Day1: true,
    Day2: true,
    Day3: true,
    Day4: true,
    Day5: true,
    Day6: false,
    Day7: false,
    CheckStartTime: '00:00:00',
    CheckEndTime: '10:00:00',
    CheckCount: 1,
    EffectiveType: 1,
    EffectiveStart: '',
    EffectiveEnd: '',
    Note: '',
    UpdatedAt: utcTime(),
  })
  const { visible, maximized, dialogModal } = useDialogModal()
  const {
    NFCPatrolLineRulesData,
    parentOptions,
    rules,
    NFCPatrolLineAndRulesData,
    filterParent,
  } = useNFCPatrolData(checkRuleName)
  const dataLen = computed(() => { return NFCPatrolLineRulesData.value.length})
  const columns = computed(() => {
    return [
      {
        name: 'OrgRID',
        field: 'OrgRID',
        label: i18n.t('form.unit'),
        sortable: true,
        align: 'left',
        format: (val) => {
          const parentOption = parentOptions.value.find(item => item.value === val)
          return `${parentOption ? parentOption.label : ''}`
        },
      },
      {
        name: 'Name',
        field: 'Name',
        label: i18n.t('NFCPatrol.ruleName'),
        sortable: true,
        sticky: true,
      },
      {
        name: 'CheckStartTime',
        field: 'CheckStartTime',
        label: i18n.t('NFCPatrol.checkStartTime'),
        sortable: true,
        noLeftBorder: true,
        format: (startTime) => toLocalTime(`${dayjs().format(DateMask)}  ${startTime}`, TimeMask),
      },
      {
        name: 'CheckEndTime',
        field: 'CheckEndTime',
        label: i18n.t('NFCPatrol.checkEndTime'),
        sortable: true,
        format: (startTime) => toLocalTime(`${dayjs().format(DateMask)}  ${startTime}`, TimeMask),
      },
      {
        name: 'CheckCount',
        field: 'CheckCount',
        label: i18n.t('NFCPatrol.patrolCount'),
        sortable: true,
      },
      {
        name: 'EffectiveType',
        field: 'EffectiveType',
        label: i18n.t('NFCPatrol.effectiveType'),
        sortable: true,
        format: (val) => val ? i18n.t('NFCPatrol.validTimePeriod') : i18n.t('NFCPatrol.effective')
      },
      {
        name: 'EffectiveStart',
        field: 'EffectiveStart',
        label: i18n.t('NFCPatrol.effectiveStart'),
        sortable: true,
        format: (val, row) => {
          if (!row.EffectiveType) {
            return ''
          }
          return toLocalTime(val, DateMask)
        },
      },
      {
        name: 'EffectiveEnd',
        field: 'EffectiveEnd',
        label: i18n.t('NFCPatrol.effectiveEnd'),
        sortable: true,
        format: (val, row) => {
          if (!row.EffectiveType) {
            return ''
          }
          return toLocalTime(val, DateMask)
        },
      },
      {
        name: 'Day1',
        field: 'Day1',
        label: i18n.t('NFCPatrol.Monday'),
        sortable: true,
        format: (val) => val ? '√' : '',
      },
      {
        name: 'Day2',
        field: 'Day2',
        label: i18n.t('NFCPatrol.Tuesday'),
        sortable: true,
        format: (val) => val ? '√' : '',
      },
      {
        name: 'Day3',
        field: 'Day3',
        label: i18n.t('NFCPatrol.Wednesday'),
        sortable: true,
        format: (val) => val ? '√' : '',
      },
      {
        name: 'Day4',
        field: 'Day4',
        label: i18n.t('NFCPatrol.Thursday'),
        sortable: true,
        format: (val) => val ? '√' : '',
      },
      {
        name: 'Day5',
        field: 'Day5',
        label: i18n.t('NFCPatrol.Friday'),
        sortable: true,
        format: (val) => val ? '√' : '',
      },
      {
        name: 'Day6',
        field: 'Day6',
        label: i18n.t('NFCPatrol.Saturday'),
        sortable: true,
        format: (val) => val ? '√' : '',
      },
      {
        name: 'Day7',
        field: 'Day7',
        label: i18n.t('NFCPatrol.Sunday'),
        sortable: true,
        format: (val) => val ? '√' : '',
      },
      {
        name: 'Note', field: 'Note', label: i18n.t('form.note'),
        classes: 'description',
      },
    ]
  })

  const getAllLineName = computed(() => { return NFCPatrolLineRulesData.value.map(item => item.Name) })

  const checkStartTime = ref(toLocalTimeFormat(currentRow.value.CheckStartTime as string))
  const checkEndTime = ref(toLocalTimeFormat(currentRow.value.CheckEndTime as string))

  const effectiveTimeRange = computed({
    get() {
      return {
        start: currentRow.value.EffectiveStart as string,
        end: currentRow.value.EffectiveEnd as string,
      }
    },
    set(val) {
      currentRow.value.EffectiveStart = val.start ? toLocalTime(val.start, DateMask) : ''
      currentRow.value.EffectiveEnd = val.end ? toLocalTime(val.end, DateMask) : ''
    }
  })

  const checkStartTimeRef = ref<InstanceType<typeof QInput>>()
  const checkEndTimeRef = ref<InstanceType<typeof QInput>>()

  function getDefFormData() {
    return {
      RID: uuidV1(),
      OrgRID: currentRow.value.OrgRID || '',
      Name: createSelfIncreaseStr((currentRow.value.Name || '') + '', getAllLineName.value as string[]),
      Day1: true,
      Day2: true,
      Day3: true,
      Day4: true,
      Day5: true,
      Day6: false,
      Day7: false,
      CheckStartTime: '00:00:00',
      CheckEndTime: '10:00:00',
      CheckCount: 1,
      EffectiveType: 1,
      EffectiveStart: '',
      EffectiveEnd: '',
      Note: '',
      UpdatedAt: utcTime(),
    }
  }

  function newRow() {
    currentRow.value = getDefFormData()
  }

  // 删除数据
  function deleteData(data: bysdb.IDbNFCPatrolLineRules): Promise<boolean> {
    // 删除线路成功后删除详情表和线路和规则映射表
    return new Promise((resolve, reject) => {
      const options: ICallOption = {
        OnResult: (res: crud.IDMLResult, rpcCmd: yrpcmsg.Ymsg, meta?: yrpcmsg.IGrpcMeta) => {
          log.info('IDbNFCPatrolLineRules Delete result', res, rpcCmd, meta)
          if (res.AffectedRow === 0) {
            reject('failed')
          } else {
            resolve(true)
            // 同步到数据容器对象
            NFCPatrolLineRules.deleteData(data.RID as string)
            // 同步规则与线路映射的本地数据
            const delLineAndRules = NFCPatrolLineAndRulesData.value.filter(item => item.RuleRID === data.RID)
            delLineAndRules.forEach(item => {
              NFCPatrolLineAndRules.deleteData(item.RID as string)
            })
          }
        },
        OnServerErr: (errRpc: yrpcmsg.Ymsg) => {
          log.error('IDbNFCPatrolLineRules Delete server error', errRpc)
          // 处理服务器响应的错误
          reject(errRpc)
        },
        OnLocalErr: (err: any) => {
          log.error('IDbNFCPatrolLineRules Delete local error', err)
          reject('local')
        },
        OnTimeout: (v: any) => {
          log.warn('IDbNFCPatrolLineRules Delete timeout', v)
          const options = {
            action: i18n.t('common.delete'),
            name: data.Name,
          }
          const reason = i18n.t('message.timeout', options)
          reject(reason)
        },
      }
      PrpcDbNFCPatrolLineRules.Delete(data, options)
    })
  }

  function insertData(data: bysdb.IDbNFCPatrolLineRules): Promise<boolean> {
    return new Promise((resolve, reject) => {
      const options: ICallOption = {
        OnResult: (res: crud.IDMLResult, rpcCmd: yrpcmsg.Ymsg, meta?: yrpcmsg.IGrpcMeta) => {
          log.info('IDbNFCPatrolLine Insert result', res, rpcCmd, meta)
          if (res.AffectedRow === 0) {
            reject('failed')
          } else {
            resolve(true)
            // 同步到数据容器对象
            NFCPatrolLineRules.setData(data.RID as string, data)
          }
        },
        OnServerErr: (errRpc: yrpcmsg.Ymsg) => {
          log.error('IDbNFCPatrolLineRules Insert server error', errRpc)
          reject(false)
        },
        OnLocalErr: (err: any) => {
          log.error('IDbNFCPatrolLineRules Insert local error', err)
          reject('local')
        },
        OnTimeout: (v: any) => {
          log.warn('IDbNFCPatrolLineRules Insert timeout', v)
          const options = {
            action: i18n.t('common.add'),
            name: data.Name,
          }
          const reason = i18n.t('message.timeout', options)
          reject(reason)
        },
      }
      PrpcDbNFCPatrolLineRules.Insert(data, options)
    })
  }

  function updateData(data: bysdb.IDbNFCPatrolLineRules): Promise<boolean> {
    return new Promise((resolve, reject) => {
      const options: ICallOption = {
        OnResult: (res: crud.IDMLResult, rpcCmd: yrpcmsg.Ymsg, meta?: yrpcmsg.IGrpcMeta) => {
          log.info('IDbNFCPatrolLineRules update result', res, rpcCmd, meta)
          if (res.AffectedRow === 0) {
            reject('failed')
          } else {
            resolve(true)
            // 同步到数据容器对象
            NFCPatrolLineRules.setData(data.RID as string, data)
          }
        },
        OnServerErr: (errRpc: yrpcmsg.Ymsg) => {
          log.error('IDbNFCPatrolLineRules update server error', errRpc)
          // 处理服务器响应的错误
          reject(false)
        },
        OnLocalErr: (err: any) => {
          log.error('IDbNFCPatrolLineRules update local error', err)
          reject('local')
        },
        OnTimeout: (v: any) => {
          log.warn('IDbNFCPatrolLineRules update timeout', v)
          const options = {
            action: i18n.t('common.modify'),
            name: data.Name,
          }
          const reason = i18n.t('message.timeout', options)
          reject(reason)
        },
      }
      PrpcDbNFCPatrolLineRules.Update(data, options)
    })
  }

  function batchAddData(dbList: bysdb.IDbNFCPatrolLineRules[], progress?: (value: number) => void) {
    const parentCache: { [key: string]: any } = {}
    return new Promise((resolve) => {
      const errorDataList: any = []
      // 使用迭代器逐个添加到数据库
      const it = dbList[Symbol.iterator]()
      // 当前处理的第几个数据
      let currentCount = 0
      const insert = async (item) => {
        // 进度信息，当前处理第几个数据
        progress?.(currentCount++)
        const { done, value } = item
        // 已经处理过每一个数据
        if (done) {
          // 已经处理过每一个数据，结束
          return resolve(errorDataList)
        }

        const data: Record<string, any> = getDefFormData()
        Object.assign(data, value)
        // 重置单位上级RID,当前OrgRID为上级单位简称，需要转换为对应的UUID
        // 查找上级是否为本地数据，暂时不处理没有权限的上级
        const parentOption = parentCache[data.OrgRID as string] ||
          parentOptions.value.find(item => item.label === data.OrgRID)
        if (!parentOption) {
          value.$info = i18n.t('message.parentUnitErr')
          errorDataList.push(value)
          // 处理下一个数据
          insert(it.next())
          return
        }
        parentCache[data.OrgRID as string] = parentOption
        data.OrgRID = parentOption.value

        // 处理开始和结束巡查时间
        if (isValidTime(data.CheckStartTime) && isValidTime(data.CheckEndTime)) {
          data.CheckStartTime = toUtcTimeFormat(data.CheckStartTime)
          data.CheckEndTime = toUtcTimeFormat(data.CheckEndTime)
        } else {
          value.$info = i18n.t('NFCPatrol.checkTimeAbnormal')
          errorDataList.push(value)
          insert(it.next())
          return
        }

        // 处理星期1-星期日
        for (let i = 1; i < 8; i++) {
          if (data['Day' + i] === '') {
            data['Day' + i] = false
            continue
          }
          data['Day' + i] = true
        }

        // 处理生效类型
        if (data.EffectiveType === i18n.t('NFCPatrol.validTimePeriod')) {
          data.EffectiveType = 1
        } else {
          data.EffectiveType = 0
        }

        //处理生效日期和失效日期
        data.EffectiveStart = toUtcTime(data.EffectiveStart + ' ' + '00:00:00')
        data.EffectiveEnd = toUtcTime(data.EffectiveEnd + ' ' + '23:59:59')
        if (data.EffectiveType === 0) {
          delete data.EffectiveEnd
          delete data.EffectiveStart
        } else if (!isBeforeOrSame(data.EffectiveStart, data.EffectiveEnd)) {
          value.$info = i18n.t('form.dateRangeBottomTip')
          errorDataList.push(value)
          // 处理下一个数据
          insert(it.next())
          return
        }

        const err: boolean | string = checkDataByRules(data, rules.value)
        if (err === true) {
          // 添加数据到数据库
          await insertData(data)
            .catch((error) => {
              value.$info = error
              errorDataList.push(value)
            })
        } else {
          value.$info = err
          errorDataList.push(value)
        }
        // 处理下一个数据
        insert(it.next())
      }
      insert(it.next())
    })
  }

  function checkRuleName(val: string) {
    return !NFCPatrolLineRulesData.value.some(rule => rule.Name === val && rule.RID !== currentRow.value.RID)
  }

  function changeEffectiveType(val) {
    if (val) {
      currentRow.value.EffectiveStart = ''
      currentRow.value.EffectiveEnd = ''
    }
  }

  function  changeCheckStartTime(val) {
    if (val && isValidTime(val)) {
      currentRow.value.CheckStartTime = toUtcTimeFormat(val)
      checkStartTimeRef.value?.resetValidation()
    }
  }

  function  changeCheckEndTime(val) {
    if (val && isValidTime(val)) {
      currentRow.value.CheckEndTime = toUtcTimeFormat(val)
      checkEndTimeRef.value?.resetValidation()
    }
  }
</script>

<style lang="scss">
  .patrol-rules-cls:not(.maximized) {
    max-width: 63vw !important;
    width: 63vw !important;
  }

  .patrol-rule-edit-content {
    max-width: 600px;
  }
</style>

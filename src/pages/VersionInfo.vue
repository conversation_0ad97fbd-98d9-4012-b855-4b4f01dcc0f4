<template>
  <modal
    v-model="visible"
    v-model:maximized="maximized"
    contentClass="version-modal"
  >
    <template v-slot:header><span>{{ title }}</span></template>

    <div class="row">
      <div
        class="col-5 text-right q-pr-md"
        v-text="$t('version.webVersion')"
      ></div>
      <div class="col-7">
        <q-badge
          inline
          align="middle"
          color="teal"
          v-text="webVersion"
        ></q-badge>
      </div>
    </div>

    <div class="row">
      <div
        class="col-5 text-right q-pr-md"
        v-text="$t('version.webBuildTime')"
      ></div>
      <div class="col-7">
        <q-badge
          inline
          align="middle"
          color="teal"
          v-text="webBuildTime"
        ></q-badge>
      </div>
    </div>

    <q-separator class="q-mt-md q-mb-md" />

    <div class="row">
      <div
        class="col-5 text-right q-pr-md"
        v-text="$t('version.serverVersion')"
      ></div>
      <div class="col-7">
        <q-badge
          inline
          align="middle"
          color="teal"
          v-text="serverVersion"
        ></q-badge>
      </div>
    </div>

    <div class="row">
      <div
        class="col-5 text-right q-pr-md"
        v-text="$t('version.serverBuildTime')"
      ></div>
      <div class="col-7">
        <q-badge
          inline
          align="middle"
          color="teal"
          v-text="serverBuildTime"
        ></q-badge>
      </div>
    </div>

    <div class="row">
      <div
        class="col-5 text-right q-pr-md"
        v-text="$t('version.serverStartTime')"
      ></div>
      <div class="col-7">
        <q-badge
          inline
          align="middle"
          color="teal"
          v-text="serverStartTime"
        ></q-badge>
      </div>
    </div>

    <div class="row">
      <div
        class="col-5 text-right q-pr-md"
        v-text="$t('version.serveRunTime')"
      ></div>
      <div class="col-7">
        <q-badge
          inline
          align="middle"
          color="teal"
          v-text="serverRunTime"
        ></q-badge>
      </div>
    </div>

  </modal>
</template>

<script lang="ts">
  import { defineComponent } from 'vue'
  import dialogMixin from '@utils/mixins/dialog'
  import { toLocalTime, getDeff, dateDeff, localTime } from '@utils/dayjs'
  import { StrPubSub } from 'ypubsub'
  import { RefreshServerVersion } from '@utils/pubSubSubject'
  import { i18n } from '@boot/i18n'
  import { resolveServerPath } from '@utils/path'

  let dateTimeTimer: number | undefined = undefined

  export default defineComponent({
    name: 'VersionInfo',
    mixins: [dialogMixin],
    data() {
      return {
        serverVersion: '',
        serverBuildTime: '',
        serverStartTime: '',
        serverRunTime: '',
      }
    },
    methods: {
      getServerVersionData() {
        fetch(resolveServerPath('/serverVersion'))
          .then(res => res.text())
          .then(this.getServerVersionDataSuccess)
          // 计算服务器运行时长
          .then(this.calcServerRunningDuration)
      },
      getServerVersionDataSuccess(data) {
        const splits = data.split(',')
        
        // 解析版本号
        this.serverVersion = splits[0].match(/bysserver ver:([\d.]+)/)[1].trim()
        
        // 解析构建时间
        const buildTimeMatch = splits[1].match(/build:([\d-]+ [\d:]+)UTC/)
        if (buildTimeMatch) {
          this.serverBuildTime = toLocalTime(buildTimeMatch[1].trim())
        } else if (splits[1].includes('build: by:not know')) {
          this.serverBuildTime = 'not know'
        }
        
        // 解析启动时间
        const startTimeMatch = splits[3].match(/Start:([\d-]+ [\d:]+)UTC/)
        if (startTimeMatch) {
          this.serverStartTime = toLocalTime(startTimeMatch[1].trim())
        }
      },
      calcServerRunningDuration() {
        this.resetDateTimeTimer()

        const begin = this.serverStartTime
        const setServerRunTime = () => {
          const deff: dateDeff = getDeff(begin, localTime())
          this.serverRunTime = '' + deff.days + i18n.global.t('date.days')
            + deff.hours + i18n.global.t('date.hours')
            + deff.minutes + i18n.global.t('date.minutes')
        }
        setServerRunTime()

        // 每隔1分钟重新计算服务器运行时长
        dateTimeTimer = window.setInterval(() => {
          setServerRunTime()
        }, 60 * 1000)
      },
      resetDateTimeTimer() {
        if (dateTimeTimer === undefined) return
        clearInterval(dateTimeTimer)
        dateTimeTimer = undefined
      }
    },
    computed: {
      title() {
        return this.$t('menus.versionInfo')
      },
      webVersion() {
        return CLIENT_VERSION
      },
      webBuildTime() {
        return toLocalTime(CLIENT_BUILD_TIME)
      },
    },
    watch: {
      // 窗口关闭时，停止计时器，打开窗口重新计算时长
      visible(value: boolean) {
        if (value) {
          if (!this.serverStartTime) return
          this.calcServerRunningDuration()
        } else {
          this.resetDateTimeTimer()
        }
      }
    },
    beforeUnmount() {
      this.resetDateTimeTimer()
      StrPubSub.unsubscribe(RefreshServerVersion, this.getServerVersionData)
    },
    beforeMount() {
      this.getServerVersionData()
      StrPubSub.subscribe(RefreshServerVersion, this.getServerVersionData)
    },
  })
</script>

<style lang="scss">
  .bys-move-layout.version-modal:not(.maximized) {
    width: 420px;
  }
</style>

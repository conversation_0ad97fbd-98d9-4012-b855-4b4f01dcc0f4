<template>
  <modal
    v-model="visible"
    v-model:maximized="maximized"
    :contentHeight="dataLen ? '68vh' : 'auto'"
    :content-class="'model-org-dlg'"
  >
    <template v-slot:header><span>{{ $t('menus.organization') }}</span></template>
    <!--
      单位管理菜单
      行选中保留项：RID（单位），用来做为添加时的默认上级单位 。
        RID -> defaultParentRID
      重复添加保留项：OrgID（编号），ParentRID（上级单位），其中编号要保留格式自增,上级单位直接继承。
        OrgID++ -> this.currentRow.OrgID, ParentRID -> this.currentRow.ParentRID

        OrgID demo:
          adc001 -> abc002
             adc -> adc1
             001 -> 002
     -->
    <data-edit
      :data="data"
      :columns="columns"
      :form-title="$t('menus.organization')"
      :can-add="canAdd"
      :can-edit="canEdit"
      :can-delete="canDelete"
      :insert="insertData"
      :update="updateData"
      :delete="deleteData"
      :import-data="batchAddData"
      v-model:currentRow="currentRow"
      :delWarnContent='delWarnContent'
      @new-row="newRow"
      @rwo-clicked="rowClicked"
      @save-multiplex-item="saveMultiplexItem"
      @clear-multiplex-item="clearMultiplexItem"
    >

      <template v-slot:form-content>
        <div class="fit row wrap form-unit">
          <div class="col col-xs-12 col-sm-6 q-gutter-sm q-col-gutter-sm">
            <q-input
              v-model="currentRow.OrgID"
              :label="$t('form.customID')"
              outlined
              dense
              clearable
              autofocus
              lazy-rules
              :rules="rules.OrgID"
              :maxlength="16"
            />
          </div>
          <div class="col col-xs-12 col-sm-6 q-gutter-sm q-col-gutter-sm">
            <q-input
              v-model="currentRow.ShortName"
              :label="$t('form.shortName')"
              outlined
              dense
              clearable
              lazy-rules
              :rules="rules.ShortName"
              :maxlength="16"
            />
          </div>
          <div class="col col-xs-12 col-sm-6 q-gutter-sm q-col-gutter-sm">
            <q-input
              v-model="currentRow.FullName"
              :label="$t('form.fullName')"
              outlined
              dense
              clearable
              lazy-rules
              :rules="rules.FullName"
              :maxlength="64"
            />
          </div>
          <div class="col col-xs-12 col-sm-6 q-gutter-sm q-col-gutter-sm">
            <q-select
              v-model="currentRow.OrgRID"
              :label="$t('form.parentUnit')"
              outlined
              dense
              lazy-rules
              :rules="rules.OrgRID"
              :options="availableParentOptions"
              @filter="filterParent"
              options-dense
              map-options
              emit-value
            />
          </div>
          <div class="col col-xs-12 col-sm-6 q-gutter-sm q-col-gutter-sm">
            <q-input
              v-model.number="currentRow.SortValue"
              type="number"
              :label="$t('form.sortValue')"
              outlined
              dense
              clearable
              lazy-rules
              :rules="rules.SortValue"
            />
          </div>
          <div class="col col-xs-12 col-sm-6 q-gutter-sm q-col-gutter-sm q-mb-md">
            <q-input
              v-model="currentRow.Note"
              type="textarea"
              :label="$t('form.note')"
              outlined
              dense
              clearable
              autogrow
              lazy-rules
              :rules="rules.Note"
              :maxlength="1024"
            />
          </div>
        </div>
      </template>
    </data-edit>
  </modal>
</template>

<script lang="ts">
  import { defineComponent } from 'vue'
  import dialogMixin from '@src/utils/mixins/dialog'
  import dataEditMixin from '@src/utils/mixins/editor'
  import { DefUuid } from '@src/config'
  import { utcTime } from '@src/utils/dayjs'
  import { v1 as uuidV1 } from 'uuid'
  import { maxLength, maxValue, minValue, required } from '@src/utils/validation'
  import { org } from '@src/ygen/org'
  import { PrpcDbOrg } from '@src/ygen/org.rpc.yrpc'
  import { ICallOption } from 'jsyrpc'
  import { yrpcmsg } from 'yrpcmsg'
  import log from '@src/utils/log'
  import { Unit, unknownUnitData } from '@services/dataStore'
  import { DataName } from '@src/store/data'
  import { crud } from '@ygen/crud'
  import { GET_DATA, NS } from '@src/store/data/methodTypes'
  import { StrPubSub } from 'ypubsub'
  import { cloneDeep } from 'lodash'
  import createSelfIncreaseStr from '@src/utils/createSelfIncreaseStr'
  import { DeleteDbOrg, InsertDbOrg, UpdateDbOrg } from '@utils/pubSubSubject'
  import { outputDBError } from '@utils/common'
  import { i18n } from '@boot/i18n'
  import { PrpcDbUserOrgPrivilege } from '@ygen/userOrgPrivilege.rpc.yrpc'
  import { user as userOrgPrivilege } from '@ygen/userOrgPrivilege'
  import { DbName, sortData } from '@utils/permission'
  import { FormRules } from '@utils/bysdb.type'

  const orgError = {
    'dborg.fkdborgparentrid': i18n.global.t('dataBaseError.parControllerErr'),
    dborg_orgid_key: i18n.global.t('dataBaseError.orgIdErr'),
    // dbbysmarker_orgrid_fkey: i18n.global.t('dataBaseError.deleteOtherDataFirst', { name: i18n.global.t('menus.boundaryMarker') }),
    // // dbconfig_orgrid_fkey: i18n.global.t('dataBaseError.deleteOtherDataFirst', { name: i18n.global.t('menus.boundaryMarker') }),
    // dbcontroller_orgrid_fkey: i18n.global.t('dataBaseError.deleteOtherDataFirst', { name: i18n.global.t('menus.controller') }),
    // // dbimage_orgrid_fkey: i18n.global.t('dataBaseError.deleteOtherDataFirst', { name: i18n.global.t('menus.boundaryMarker') }),
    // dbmediainfo_orgrid_fkey: i18n.global.t('dataBaseError.deleteOtherDataFirst', { name: i18n.global.t('menus.images') }),
    hasOrgChild: i18n.global.t('dataBaseError.deleteOtherDataFirst', { name: i18n.global.t('menus.organization') }),
    // dbrole_orgrid_fkey: i18n.global.t('dataBaseError.deleteOtherDataFirst', { name: i18n.global.t('menus.role') }),
    // dbuser_orgrid_fkey: i18n.global.t('dataBaseError.deleteOtherDataFirst', { name: i18n.global.t('menus.user') }),
    // dbuserorgprivilege_orgrid_fkey: i18n.global.t('dataBaseError.deleteOtherDataFirst',
    //   { name: i18n.global.t('menus.boundaryMarker') },
    // ),
    // dbuserrole_orgrid_fkey: i18n.global.t('dataBaseError.deleteOtherDataFirst', { name: i18n.global.t('menus.boundaryMarker') }),
    // dbusersession_orgrid_fkey: i18n.global.t('dataBaseError.deleteOtherDataFirst', { name: i18n.global.t('menus.boundaryMarker') }),
    'lack org privilege': i18n.global.t('dataBaseError.lackOrgPrivilege'),
    'No operation permission': i18n.global.t('dataBaseError.permissionErr'),
  }
  const dbuserorgprivilegeErrInfo = {
    dbuserorgprivilege_orgrid_fkey: i18n.global.t('dataBaseError.notSetPermForAddedOrg'),
  }

  export default defineComponent({
    name: 'Organization',
    mixins: [dialogMixin, dataEditMixin],
    data() {
      return {
        defaultOrgRID: '',
        defaultOrgID: '',
        visible: false,
        maximized: false,
        currentRow: {},
      }
    },
    computed: {
      delWarnContent() {
        return i18n.global.t('message.deleteOrgWarn')
      },
      dbName() {
        return DbName.DbOrg
      },
      columns() {
        return [
          {
            name: 'OrgID',
            field: 'OrgID',
            label: this.$t('form.customID'),
            align: 'left',
            sortable: true,
            sticky: true,
          },
          { name: 'ShortName', field: 'ShortName', label: this.$t('form.shortName'), align: 'left', sortable: true, noLeftBorder: true },
          { name: 'FullName', field: 'FullName', label: this.$t('form.fullName'), align: 'left', sortable: true },
          // 暂无单位类型信道
          // {
          //   name: 'OrgType', field: 'OrgType', label: this.$t('form.type'), align: 'left', sortable: true,
          //   format: (val) => {
          //     return `${val}`
          //   },
          // },
          {
            name: 'OrgRID', field: 'OrgRID', label: this.$t('form.parentUnit'), align: 'left', sortable: true,
            format: (val) => {
              const parentOption = this.parentOptions.find(item => item.value === val)
              return `${parentOption ? parentOption.label : ''}`
            },
          },
          {
            name: 'SortValue', field: 'SortValue', label: this.$t('form.sortValue'), align: 'right', sortable: true,
            sort: (a, b) => parseInt(a) - parseInt(b),
          },
          { name: 'Note', field: 'Note', label: this.$t('form.note'), align: 'left' },
        ]
      },
      rules(): FormRules {
        const _required = (val) => required(val) || this.$t('rules.required')
        const _maxLength = (val) => maxLength(val, 16) || this.$t('rules.maxLength', { len: 16 })

        return {
          OrgID: [
            val => _required(val),
            val => _maxLength(val),
          ],
          OrgRID: [
            val => _required(val),
          ],
          ShortName: [
            val => _required(val),
            val => _maxLength(val),
          ],
          FullName: [
            val => maxLength(val, 64) || this.$t('rules.maxLength', { len: 64 }),
          ],
          SortValue: [
            val => required(val) || this.$t('rules.required'),
            val => maxValue(val, 0xFFFFFFFF) || this.$t('rules.maxValue', { max: 0xFFFFFFFF }),
            val => minValue(val, 0) || this.$t('rules.minValue', { min: 0 }),
          ],
          Note: [
            val => maxLength(val, 1024) || this.$t('rules.maxLength', { len: 1024 }),
          ],
        }
      },
      parentMaps() {
        const orgList = this.originData.concat(unknownUnitData)
        const result: { [key: string]: any } = {}
        for (let i = 0; i < orgList.length; i++) {
          const orgData = orgList[i]
          result[orgData.RID] = this.findParents(orgData.OrgRID, orgList)
        }
        return result
      },
      childrenMaps() {
        const orgList = this.originData.concat(unknownUnitData)
        const result: { [key: string]: any } = {}
        for (let i = 0; i < orgList.length; i++) {
          const orgData = orgList[i]
          result[orgData.RID] = this.findChildren(orgData.RID, orgList)
        }
        return result
      },
      parentOptions() {
        const keysCache: Set<string> = new Set()
        return this.originData.concat(unknownUnitData)
          .filter(item => {
            if (keysCache.has(item.RID + '')) {
              return false
            }
            keysCache.add(item.RID + '')
            return true
          })
          .map(data => {
            // 判断该选项是否要被禁用
            let disable = this.checkUnitSelectionIsDisable(data)
            return {
              label: data.RID === DefUuid ? this.$t('form.default') : data.ShortName,
              value: data.RID,
              OrgRID: data.OrgRID,
              disable,
            }
          })
      },
      availableParentOptions() {
        return this.parentOptions.filter(item => {
          // 需要保留默认单位，避免无法匹配根单位选项
          return item.value === DefUuid || !item.disable
        })
          .filter(option => {
            const needle = this.parentFilter.toLowerCase()
            return option.label.toLowerCase().includes(needle)
          })
      },
      dataLen() {
        return this.data.length
      },
      originData(): Array<org.IDbOrg> {
        const dataList = this.$store.getters[`${NS}/${GET_DATA}`](DataName.Unit)
        return sortData<org.IDbOrg>(dataList, {
          descending: this.$store.state.Settings.descending,
          prop: 'SortValue',
        })
      },
      data() {
        const data = this.originData.filter(item => item.RID !== DefUuid)
        return Unit.sortBy(data, { sortBy: 'SortValue' })
      },
    },
    methods: {
      // 找到org的所有上级，以数组形式返回
      findParents(OrgRID: string, orgList: org.IDbOrg[]): org.IDbOrg[] {
        let list: org.IDbOrg[] = []
        for (let i = 0; i < orgList.length; i++) {
          const data = orgList[i]
          if (data.RID === OrgRID) {
            list.push(data)
            list = list.concat(this.findParents(data.OrgRID + '', orgList))
          }
        }
        return list
      },
      // 找到org的所有下级，以数组形式返回
      findChildren(RID: string, orgList: org.IDbOrg[]): org.IDbOrg[] {
        let children: org.IDbOrg[] = []
        for (let i = 0; i < orgList.length; i++) {
          const data = orgList[i]
          if (data.OrgRID === RID) {
            const ret = {
              RID: data.RID,
              OrgRID: data.OrgRID,
              ShortName: data.ShortName,
              children: this.findChildren(data.RID + '', orgList),
            }
            children.push(ret)
          }
        }

        return children
      },
      // 上级单位列表禁用自己和所有子级的单位选项，不能互为上级。
      checkUnitSelectionIsDisable(data: org.IDbOrg): boolean {
        const RID = data.RID + ''
        // 如果是默认的根单位，且用户不是根单位下的用户，则禁止选择该选项
        if (RID === DefUuid && this.$store.state.UserRID !== DefUuid) { return true }

        // 禁用自己的选项，上级不能是自己
        if (this.currentRow?.RID === RID) { return true }

        // 如果检测的单位数据的上级路径中包含当前编辑的数据，则禁用
        const parents = this.parentMaps[data.RID + ''] || []
        return parents.some(item => item.RID === this.currentRow?.RID)
      },
      getDefFormData() {
        return ({
          RID: uuidV1(),
          OrgID: '',
          OrgType: 0,
          SortValue: 100,
          ShortName: '',
          FullName: '',
          Note: '',
          // json string
          Setting: '{}',
          OrgRID: DefUuid,
          UpdatedAt: utcTime(),
          UpdatedDC: '',
          CreatorRID: this.$store.state.UserRID,
        } as org.IDbOrg)
      },
      rowClicked(rowData) {
        //传递的参数为props.row
        this.defaultOrgRID = rowData.RID
      },
      saveMultiplexItem() {
        //上级单位为此时表单选中的上级单位,编号为当前的编号
        this.defaultOrgRID = this.currentRow.OrgRID
        this.defaultOrgID = this.currentRow.OrgID
      },
      clearMultiplexItem() {
        //清除保留的OrgID
        this.defaultOrgID = ''
      },
      getAllOrgID() {
        return this.data
          .map((data: org.IDbOrg) => {
            return data.OrgID
          })
      },
      newRow() {
        this.currentRow = this.getDefFormData()
        //设置默认的上级单位
        this.currentRow.OrgRID = this.defaultOrgRID
        this.currentRow.OrgID = createSelfIncreaseStr(this.defaultOrgID, this.getAllOrgID())
      },
      insertData(data: org.IDbOrg): Promise<boolean> {
        if (!data.FullName) { data.FullName = data.ShortName }
        return new Promise((resolve, reject) => {
          const options: ICallOption = {
            OnResult: (res: crud.IDMLResult, rpcCmd: yrpcmsg.Ymsg, meta?: yrpcmsg.IGrpcMeta) => {
              log.info('IDbOrg Insert result', res, rpcCmd, meta)
              // 向用户的单位权限表添加数据
              const rollbackDbOrg = () => {
                this.deleteData(data)
              }
              const orgPrivilege: userOrgPrivilege.IDbUserOrgPrivilege = {
                RID: uuidV1(),
                UserRID: this.$store.state.UserRID,
                OrgRID: data.RID,
                IncludeChildren: 0,
                Setting: '{}',
                UpdatedAt: utcTime(),
              }
              const options: ICallOption = {
                OnResult: (res: crud.IDMLResult, rpcCmd: yrpcmsg.Ymsg, meta?: yrpcmsg.IGrpcMeta) => {
                  log.info('IDbUserOrgPrivilege Insert result', res, rpcCmd, meta)
                  resolve(true)
                  // 同步到数据容器对象
                  const RID = data.RID as string
                  const newOrg = new org.DbOrg(data)
                  Unit.setData(RID, newOrg)
                    .setDataIndex(newOrg.OrgID as string, RID)
                  // 发布删除设备树的通知
                  StrPubSub.publish(InsertDbOrg, newOrg)
                },
                OnServerErr: (errRpc: yrpcmsg.Ymsg) => {
                  rollbackDbOrg()
                  log.error('IDbUserOrgPrivilege Insert server error', errRpc)
                  // 处理服务器响应的错误
                  let reason = outputDBError(dbuserorgprivilegeErrInfo, errRpc.Optstr)
                  reject(reason)
                },
                OnLocalErr: (err: any) => {
                  rollbackDbOrg()
                  log.error('IDbUserOrgPrivilege Insert local error', err)
                  reject(err ? err.toString() : 'localErr')
                },
                OnTimeout: (v: any) => {
                  rollbackDbOrg()
                  log.warn('IDbUserOrgPrivilege Insert timeout', v)
                  const options = {
                    action: this.$t('common.add'),
                    name: data.ShortName,
                  }
                  const reason = this.$t('message.timeout', options)
                  reject(reason)
                },
              }
              PrpcDbUserOrgPrivilege.Insert(orgPrivilege, options)
            },
            OnServerErr: (errRpc: yrpcmsg.Ymsg) => {
              log.error('IDbOrg Insert server error', errRpc)
              // 处理服务器响应的错误
              let reason = outputDBError(orgError, errRpc.Optstr)
              reject(reason)
            },
            OnLocalErr: (err: any) => {
              log.error('IDbOrg Insert local error', err)
              reject('local')
            },
            OnTimeout: (v: any) => {
              log.warn('IDbOrg Insert timeout', v)
              const options = {
                action: this.$t('common.add'),
                name: data.ShortName,
              }
              const reason = this.$t('message.timeout', options)
              reject(reason)
            },
          }
          PrpcDbOrg.Insert(data, options)
        })
      },
      updateData(data: org.IDbOrg): Promise<boolean> {
        if (!data.FullName) { data.FullName = data.ShortName }
        return new Promise((resolve, reject) => {
          const options: ICallOption = {
            OnResult: (res: crud.IDMLResult, rpcCmd: yrpcmsg.Ymsg, meta?: yrpcmsg.IGrpcMeta) => {
              log.info('IDbOrg Update result', res, rpcCmd, meta)
              if (res.AffectedRow === 0) {
                reject('failed')
              } else {
                resolve(true)

                // 同步到数据容器对象
                const RID = data.RID as string
                const oldData = cloneDeep(Unit.getData(RID))
                const newOrg = new org.DbOrg(data)
                Unit.setData(RID, newOrg)
                  .setDataIndex(newOrg.OrgID as string, RID)
                // 发布删除设备树的通知
                StrPubSub.publish(UpdateDbOrg, newOrg, oldData)
              }
            },
            OnServerErr: (errRpc: yrpcmsg.Ymsg) => {
              log.error('IDbOrg Update server error', errRpc)
              // 处理服务器响应的错误
              /*let reason = this.$t('message.updateFailed')
              // dborg_orgid_key 自编号重复
              if (errRpc.Optstr.includes('dborg_orgid_key')) {
                reason = this.$t('message.duplicateOrgID', { name: data.OrgID })
              } else if (errRpc.Optstr.includes('lack org privilege')) {
                // 没有权限
                reason = this.$t('message.lackOrgAuth')
              }*/
              let reason = outputDBError(orgError, errRpc.Optstr)
              reject(reason)
            },
            OnLocalErr: (err: any) => {
              log.error('IDbOrg Update local error', err)
              reject('local')
            },
            OnTimeout: (v: any) => {
              log.warn('IDbOrg Update timeout', v)
              const options = {
                action: this.$t('common.modify'),
                name: data.ShortName,
              }
              const reason = this.$t('message.timeout', options)
              reject(reason)
            },
          }
          PrpcDbOrg.Update(data, options)
        })
      },
      deleteData(data: org.IDbOrg): Promise<boolean> {
        return new Promise((resolve, reject) => {
          const options: ICallOption = {
            OnResult: (res: crud.IDMLResult, rpcCmd: yrpcmsg.Ymsg, meta?: yrpcmsg.IGrpcMeta) => {
              log.info('IDbOrg Delete result', res, rpcCmd, meta)
              if (res.AffectedRow === 0) {
                reject('failed')
              } else {
                resolve(true)

                // 同步到数据容器对象
                Unit.deleteData(data.RID as string)
                // 发布删除设备树的通知
                StrPubSub.publish(DeleteDbOrg, data)
              }
            },
            OnServerErr: (errRpc: yrpcmsg.Ymsg) => {
              log.error('IDbOrg Delete server error', errRpc)
              // 处理服务器响应的错误
              let reason = ''
              if (errRpc.Optstr.includes(
                'update or delete on table "dborg" violates foreign key constraint "fkdborgparentrid" on table "dborg"')) {
                reason = orgError.hasOrgChild as string
              } else {
                reason = outputDBError(orgError, errRpc.Optstr)
              }
              reject(reason)
            },
            OnLocalErr: (err: any) => {
              log.error('IDbOrg Delete local error', err)
              reject('local')
            },
            OnTimeout: (v: any) => {
              log.warn('IDbOrg Delete timeout', v)
              const options = {
                action: this.$t('common.delete'),
                name: data.ShortName,
              }
              const reason = this.$t('message.timeout', options)
              reject(reason)
            },
          }
          PrpcDbOrg.Delete(data, options)
        })
      },
      batchAddData(dbOrgList: org.IDbOrg[], progress?: (value: number) => void): Promise<any> {
        return new Promise((resolve) => {
          // 缓存有异常的数据
          const errorDataList: org.IDbOrg[] = []
          // 使用迭代器逐个添加到数据库
          const it = dbOrgList[Symbol.iterator]()
          // 当前处理的第几个数据
          let currentCount = 0
          const insert = async (item) => {
            // 进度信息，当前处理第几个数据
            progress?.(currentCount++)
            const { done, value } = item
            // 已经处理过每一个数据
            if (done) {
              // 已经处理过每一个数据，结束
              return resolve(errorDataList)
            }

            // 合并默认参数
            const data: org.IDbOrg = this.getDefFormData()
            Object.assign(data, value)

            // 过滤本地单位自编号已经存在的数据，自编号唯一
            const localData = Unit.getDataByIndex(data.OrgID as string)
            if (localData) {
              value.$info = this.$t('message.duplicateData')
              errorDataList.push(value)
              // 处理下一个数据
              insert(it.next())
              return
            }

            // 重置单位上级RID,当前OrgRID为上级单位简称，需要转换为对应的UUID
            // 查找上级是否为本地数据，暂时不处理没有权限的上级
            const parentOption = this.parentOptions.find(item => item.label === data.OrgRID)
            if (!parentOption) {
              value.$info = this.$t('message.parentUnitErr')
              errorDataList.push(value)
              // 处理下一个数据
              insert(it.next())
              return
            }
            data.OrgRID = parentOption.value

            // 转换数字类型参数
            const CoverPropList = ['SortValue']
            this.coverPropToNumber(data, CoverPropList)

            const CoverStringList = ['OrgID', 'ShortName', 'FullName', 'OrgRID', 'Note']
            this.coverPropToString(data, CoverStringList)

            const err = this.checkDataByRules(data, this.rules)
            if (err === true) {
              // 添加数据到数据库
              await this.insertData(data)
                .catch((error) => {
                  value.$info = error
                  errorDataList.push(value)
                })
            } else {
              value.$info = err
              errorDataList.push(value)
            }

            // 处理下一个数据
            insert(it.next())
          }
          insert(it.next())
        })
      },
    },
  })
</script>

<style lang="scss">
  .model-org-dlg:not(.maximized) {
    min-width: 70vw;
  }

  .form-unit {
    width: 65vw;
    max-width: 560px;
  }
</style>

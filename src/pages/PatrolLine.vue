<template>
  <dialogModal
    v-model="visible"
    v-model:maximized="maximized"
    :contentHeight="dataLen ? '68vh' : 'auto'"
    :content-class="'patrol-lines-cls'"
    :title="title"
  >
    <data-edit
      :data="NFCPatrolLinesData"
      :columns="columns"
      :form-title="title"
      v-model:currentRow="currentRow"
      :insert="insertData"
      :delete="deleteData"
      :update="updateData"
      :extra-export-func="extraExportFunc"
      :import-data="batchAddData"
      @new-row="newRow"
    >
      <template #my-body-cell-details="{ value, row }">
        <q-chip
          v-show="(value?.length ?? 0) > 0"
          clickable
          outline
          dense
          color="primary"
          class="text-xs px-2"
          :label="value.length "
        >
          <q-tooltip>
            {{ detailsInfo }}
          </q-tooltip>
          <q-popup-proxy>
            <q-banner>
              <p class="my-2 text-base border-b">{{ $t('NFCPatrol.patrolPoint') }}</p>
              <div class="flex flex-wrap gap-1 w-[300px] mt-1">
                <q-chip
                  dense outline color="primary"
                  v-for="info in row?.details"
                  :key="info.MarkerRID"
                >
                  {{ getMarkerNo(info.MarkerRID) }}
                </q-chip>
              </div>
            </q-banner>
          </q-popup-proxy>
        </q-chip>
      </template>
      <template #form-content>
        <div
          class="q-ml-sm"
          :class="$q.platform.is.android ? '' : 'patrol-line-edit-content'"
        >
          <div class="fit row wrap q-mb-sm q-col-gutter-x-sm">
            <div class="col col-xs-12 col-md-6">
              <q-select
                v-model="currentRow.OrgRID"
                :label="$t('form.unit')"
                outlined
                dense
                clearable
                lazy-rules
                :rules="lineRules.OrgRID"
                :options="parentOptions"
                @filter="filterParent"
                options-dense
                map-options
                emit-value
              />
            </div>
            <div class="col col-xs-12 col-md-6">
              <q-input
                v-model="currentRow.Name"
                :label="$t('NFCPatrol.LineName')"
                :rules="lineRules.Name"
                lazy-rules
                outlined
                dense
                clearable
                autofocus
              />
            </div>
            <div class="col col-xs-12 col-md-6">
              <q-select
                v-model="linePatrolRules"
                :label="$t('NFCPatrol.patrolRules')"
                outlined
                dense
                clearable
                lazy-rules
                :options="rulesOptions"
                :rules="lineRules.rules"
                options-dense
                map-options
                emit-value
                multiple
                @clear="linePatrolRules = []"
              >
                <template #selected>
                  <span>{{ linePatrolRules[0]?.label ?? '' }}</span>
                  <q-chip
                    v-show="(linePatrolRules.length) > 1"
                    clickable
                    dense
                    outline
                    color="primary"
                    size="sm"
                    icon="add"
                  >
                    {{ linePatrolRules.length - 1 }}
                  </q-chip>
                </template>
              </q-select>
            </div>
            <div class="col col-xs-12 col-md-6 q-mb-sm">
              <q-input
                v-model="currentRow.Note"
                type="textarea"
                :label="$t('form.description')"
                outlined
                dense
                clearable
                autogrow
                lazy-rules
                :rules="lineRules.Note"
                :maxlength="1024"
              />
            </div>
            <div class="col col-12" :class="$q.platform.is.android ? '' : 'overflow-y-auto max-w-none w-max'">
              <custom-transfer
                v-model:model-value="lineDetails"
                :sourceList="transferMarkerSource"
                :titles="{ left: $t('NFCPatrol.allMarker') , right: $t('NFCPatrol.selectedPatrolMarker')}"
                :rules="lineRules.details"
              >
              </custom-transfer>
            </div>
          </div>
        </div>
      </template>
    </data-edit>
  </dialogModal>
</template>

<script lang="ts" setup>
  import { computed, defineAsyncComponent, ref, watch } from 'vue'
  import { useDialogModal } from '@utils/vueUse/dialog'
  import { bysdb } from '@ygen/bysdb'
  import {
    NFCPatrolLineAndDetailsDataMap, NFCPatrolLineAndRulesDataMap,
    NFCPatrolLineRulesDataMap,
    useNFCPatrolData
  } from '@utils/vueUse/NFCPatrolData'
  import { NFCPatrolLine, NFCPatrolLineAndRules, NFCPatrolLineDetail } from '@services/dataStore'
  import { v1 as uuidV1 } from 'uuid'
  import createSelfIncreaseStr from '@utils/createSelfIncreaseStr'
  import log from '@utils/log'
  import { ICallOption } from 'jsyrpc'
  import { crud } from '@ygen/crud'
  import { yrpcmsg } from 'yrpcmsg'
  import { PrpcDbNFCPatrolLine, PrpcDbNFCPatrolLineAndRules, PrpcDbNFCPatrolLineDetail } from '@ygen/bysdb.rpc.yrpc'
  import { sysUtcTime, utcTime } from '@utils/dayjs'
  import { useI18n } from 'vue-i18n'
  import dataEdit from '@components/dataEdit/DataEdit.vue'
  import { columnsProcess } from '@utils/xlsx'
  import useEditor from '@utils/vueUse/editor'

  const { visible, maximized, dialogModal } = useDialogModal()
  const i18n = useI18n()
  const {
    NFCPatrolLines,
    parentOptions,
    NFCPatrolLineAndRulesData,
    NFCPatrolLineDetails,
    bysMarkerData,
    getMarkerNo,
    lineRules,
    NFCPatrolLineRulesData,
    filterParent,
    rulesOptions,
  } = useNFCPatrolData(customCheckLineName)

  const { checkDataByRules } = useEditor()
  const CustomTransfer = defineAsyncComponent(() => import('@components/CustomTransfer.vue'))
  const title = computed(() => { return i18n.t('menus.patrolLines')})
  const NFCPatrolLinesData = computed(() => {
    return NFCPatrolLines.value.map(item => {
      return {
        ...item,
        details: NFCPatrolLineAndDetailsDataMap.value[item.RID as string] ?? [],
        rules: NFCPatrolLineRulesDataMap.value[item.RID as string] ?? []
      }
    })
  })
  const currentRow = ref<bysdb.IDbNFCPatrolLine>({})

  const detailsInfo = computed(() => {return i18n.t('common.details') })
  const columns = computed(() => {
    return [
      {
        name: 'OrgRID',
        field: 'OrgRID',
        label: i18n.t('form.unit'),
        sortable: true,
        align: 'left',
        format: (val) => {
          const parentOption = parentOptions.value.find(item => item.value === val)
          return `${parentOption ? parentOption.label : ''}`
        },
      },
      {
        name: 'Name',
        field: 'Name',
        label: i18n.t('NFCPatrol.LineName'),
        sortable: true,
        sticky: true,
      },
      {
        name: 'details',
        field: 'details',
        label: i18n.t('NFCPatrol.patrolPoint'),
        sortable: true,
        noLeftBorder: true,
      },
      {
        name: 'rules',
        field: 'rules',
        label: i18n.t('NFCPatrol.patrolRules'),
        sortable: true,
        format: (val) => {
          return val.map(item => item.Name).join(', ')
        }
      },
      {
        name: 'Note', field: 'Note', label: i18n.t('form.note'),
        classes: 'description',
      },

    ]
  })
  const extraColumns = computed(() => {
    return [
      {
        name: 'details',
        field: 'details',
        label: i18n.t('NFCPatrol.patrolPoint'),
        sortable: true,
        noLeftBorder: true,
      },
      {
        name: 'rules',
        field: 'rules',
        label: i18n.t('NFCPatrol.patrolRules'),
        sortable: true,
      },
    ]
  })
  const dataLen = computed(() => { return NFCPatrolLines.value.length})

  const getAllLineName = computed(() => { return NFCPatrolLines.value.map(item => item.Name) })

  const transferMarkerSource = computed(() => {
    return bysMarkerData.value.map(m => {
      return {
        key: m.RID,
        label: m.MarkerNo,
        selected: false,
        disable: false,
      }
    })
  })
  const lineDetails = ref<Record<string, any>[]>([])
  const linePatrolRules = ref<Record<string, any>[]>([])

  watch(() => currentRow.value.RID, newVal => {
    lineDetails.value = NFCPatrolLineAndDetailsDataMap.value[newVal as string]?.map(item => {
      return {
        key: item.MarkerRID,
        label: getMarkerNo(item.MarkerRID as string),
        selected: false,
        disable: false,
      }
    }) ?? []

    linePatrolRules.value = NFCPatrolLineRulesDataMap.value[newVal as string]?.map(rule => {
      return {
        label: rule.Name,
        key: rule.RID
      }
    }) ?? []
  }, { deep: true })

  function getDefFormData(): bysdb.IDbNFCPatrolLine {
    return {
      RID: uuidV1(),
      OrgRID: currentRow.value.OrgRID || '',
      Name: createSelfIncreaseStr((currentRow.value.Name || '') + '', getAllLineName.value as string[]),
      Note: '',
      UpdatedAt: utcTime(),
    }
  }

  function newRow() {
    currentRow.value = getDefFormData()
  }

  // 添加巡查线路详情
  function insertDetailData(data: bysdb.IDbNFCPatrolLineDetail[]): Promise<boolean>[] {
    const insertDataPromise: Promise<any>[] = []
    data.forEach(item => {
      const promise = new Promise((resolve, reject) => {
        const options: ICallOption = {
          OnResult: (res: crud.IDMLResult, rpcCmd: yrpcmsg.Ymsg, meta?: yrpcmsg.IGrpcMeta) => {
            log.info('IDbNFCPatrolLineDetail Insert result', res, rpcCmd, meta)
            NFCPatrolLineDetail.setData(item.RID as string, item)
            resolve(true)
          },
          OnServerErr: (errRpc: yrpcmsg.Ymsg) => {
            log.error('IDbNFCPatrolLineDetail Insert server error', errRpc)
            reject(false)
          },
          OnLocalErr: (err: any) => {
            log.error('IDbNFCPatrolLineDetail Insert local error', err)
            reject('local')
          },
          OnTimeout: (v: any) => {
            log.warn('IDbNFCPatrolLineDetail Insert timeout', v)
            const options = {
              action: i18n.t('common.add'),
              name: i18n.t('NFCPatrol.patrolPoint') + i18n.t('common.details'),
            }
            const reason = i18n.t('message.timeout', options)
            reject(reason)
          },
        }
        PrpcDbNFCPatrolLineDetail.Insert(item, options)
      })
      insertDataPromise.push(promise)
    })
    return insertDataPromise
  }

  // 添加巡查线路
  function insertPatrolLineData(data: bysdb.IDbNFCPatrolLine): Promise<boolean> {
    return new Promise(async (resolve, reject) => {
      const options: ICallOption = {
        OnResult: (res: crud.IDMLResult, rpcCmd: yrpcmsg.Ymsg, meta?: yrpcmsg.IGrpcMeta) => {
          log.info('IDbNFCPatrolLine Insert result', res, rpcCmd, meta)
          if (res.AffectedRow === 0) {
            reject('failed')
          } else {
            resolve(true)
            // 同步到数据容器对象
            NFCPatrolLine.setData(data.RID as string, data)
            // 发布插入数据通知
          }
        },
        OnServerErr: (errRpc: yrpcmsg.Ymsg) => {
          log.error('IDbNFCPatrolLine Insert server error', errRpc)
          reject(false)
        },
        OnLocalErr: (err: any) => {
          log.error('IDbNFCPatrolLine Insert local error', err)
          reject('local')
        },
        OnTimeout: (v: any) => {
          log.warn('IDbNFCPatrolLine Insert timeout', v)
          const options = {
            action: i18n.t('common.add'),
            name: data.Name,
          }
          const reason = i18n.t('message.timeout', options)
          reject(reason)
        },
      }
      PrpcDbNFCPatrolLine.Insert(data, options)
    })
  }

  // 添加巡查线路和规则映射
  function insertPatrolLineAndRulesData(lineAndRules: bysdb.IDbNFCPatrolLineAndRules[]): Promise<boolean>[] {
    const insertLineAndRulesPromise: Promise<any>[] = []
    lineAndRules.forEach(item => {
      const promise = new Promise((resolve, reject) => {
        const options: ICallOption = {
          OnResult: (res: crud.IDMLResult, rpcCmd: yrpcmsg.Ymsg, meta?: yrpcmsg.IGrpcMeta) => {
            log.info('IDbNFCPatrolLineAndRules Insert result', res, rpcCmd, meta)
            NFCPatrolLineAndRules.setData(item.RID as string, item)
            resolve(true)
          },
          OnServerErr: (errRpc: yrpcmsg.Ymsg) => {
            log.error('IDbNFCPatrolLineAndRules Insert server error', errRpc)
            reject(false)
          },
          OnLocalErr: (err: any) => {
            log.error('IDbNFCPatrolLineAndRules Insert local error', err)
            reject('local')
          },
          OnTimeout: (v: any) => {
            log.warn('IDbNFCPatrolLineAndRules Insert timeout', v)
            const options = {
              action: i18n.t('common.add'),
              name: i18n.t('NFCPatrol.lineAndRules'),
            }
            const reason = i18n.t('message.timeout', options)
            reject(reason)
          },
        }
        PrpcDbNFCPatrolLineAndRules.Insert(item, options)
      })
      insertLineAndRulesPromise.push(promise)
    })
    return insertLineAndRulesPromise
  }

  // 添加数据
  function insertData(data: bysdb.IDbNFCPatrolLine, opts?: {
    details: bysdb.IDbNFCPatrolLineDetail[],
    lineAndRules: bysdb.IDbNFCPatrolLineAndRules[]
  }): Promise<boolean> {
    return new Promise(resolve => {
      insertPatrolLineData(data)
        .then(async () => {
          // 添加详情
          resolve(true)
          const UpdatedAt = utcTime()
          const details = opts?.details ?? lineDetails.value.map(item => {
            return {
              RID: uuidV1(),
              LineRID: data.RID,
              MarkerRID: item.key,
              UpdatedAt,
              OrgRID: data.OrgRID
            }
          }) ?? []
          const lineAndRules = opts?.lineAndRules ?? linePatrolRules.value.map(item => {
            return {
              RID: uuidV1(),
              LineRID: data.RID,
              RuleRID: item.key,
              UpdatedAt,
              OrgRID: data.OrgRID
            }
          }) ?? []
          const insertDetailPromise = insertDetailData(details)
          const insertLineAndRulesPromise = insertPatrolLineAndRulesData(lineAndRules)
          Promise.allSettled(insertDetailPromise)
            .then(res => {
              log.info('details add success', res)
            })
          Promise.allSettled(insertLineAndRulesPromise)
            .then(res => {
              log.info('ineAndRules add success', res)
            })
        })
    })
  }

  // 删除线路详情表
  function deleteLineDetails(details: bysdb.IDbNFCPatrolLineDetail[]): Promise<boolean>[] {
    const deleteDetailPromise: Promise<any>[] = []
    details.forEach(item => {
      const promise = new Promise((resolve, reject) => {
        const options: ICallOption = {
          OnResult: (res: crud.IDMLResult, rpcCmd: yrpcmsg.Ymsg, meta?: yrpcmsg.IGrpcMeta) => {
            log.info('IDbNFCPatrolLineDetail Delete result', res, rpcCmd, meta)
            if (res.AffectedRow === 0) {
              reject('failed')
            } else {
              resolve(true)
              // 同步到数据容器对象
              NFCPatrolLineDetail.deleteData(item.RID as string)
            }
          },
          OnServerErr: (errRpc: yrpcmsg.Ymsg) => {
            log.error('IDbNFCPatrolLineDetail Delete server error', errRpc)
            // 处理服务器响应的错误
            reject(false)
          },
          OnLocalErr: (err: any) => {
            log.error('IDbNFCPatrolLineDetail Delete local error', err)
            reject('local')
          },
          OnTimeout: (v: any) => {
            log.warn('IDbNFCPatrolLineDetail Delete timeout', v)
            const options = {
              action: i18n.t('common.delete'),
              name: i18n.t('NFCPatrol.lineDetail'),
            }
            const reason = i18n.t('message.timeout', options)
            reject(reason)
          },
        }
        PrpcDbNFCPatrolLineDetail.Delete(item, options)
      })
      deleteDetailPromise.push(promise)
    })
    return deleteDetailPromise
  }

  // 删除线路与规则映射数据
  function deleteLineAndRules(lineAndRules: bysdb.IDbNFCPatrolLineAndRules[]): Promise<boolean>[] {
    const deleteLineAndRulesPromise: Promise<any>[] = []
    lineAndRules.forEach(item => {
      const promise = new Promise((resolve, reject) => {
        const options: ICallOption = {
          OnResult: (res: crud.IDMLResult, rpcCmd: yrpcmsg.Ymsg, meta?: yrpcmsg.IGrpcMeta) => {
            log.info('IDbNFCPatrolLineAndRules Delete result', res, rpcCmd, meta)
            if (res.AffectedRow === 0) {
              reject('failed')
            } else {
              resolve(true)
              // 同步到数据容器对象
              NFCPatrolLineAndRules.deleteData(item.RID as string)
            }
          },
          OnServerErr: (errRpc: yrpcmsg.Ymsg) => {
            log.error('IDbNFCPatrolLineAndRules Delete server error', errRpc)
            // 处理服务器响应的错误
            reject(false)
          },
          OnLocalErr: (err: any) => {
            log.error('IDbNFCPatrolLineAndRules Delete local error', err)
            reject('local')
          },
          OnTimeout: (v: any) => {
            log.warn('IDbNFCPatrolLineAndRules Delete timeout', v)
            const options = {
              action: i18n.t('common.delete'),
              name: i18n.t('NFCPatrol.lineAndRules'),
            }
            const reason = i18n.t('message.timeout', options)
            reject(reason)
          },
        }
        PrpcDbNFCPatrolLineAndRules.Delete(item, options)
      })
      deleteLineAndRulesPromise.push(promise)
    })
    return deleteLineAndRulesPromise
  }

  // 删除数据
  function deleteData(data: bysdb.IDbNFCPatrolLine): Promise<boolean> {
    return new Promise((resolve, reject) => {
      const options: ICallOption = {
        OnResult: (res: crud.IDMLResult, rpcCmd: yrpcmsg.Ymsg, meta?: yrpcmsg.IGrpcMeta) => {
          log.info('IDbNFCPatrolLine Delete result', res, rpcCmd, meta)
          if (res.AffectedRow === 0) {
            reject('failed')
          } else {
            resolve(true)
            // 同步到数据容器对象
            const rid = data.RID as string
            NFCPatrolLine.deleteData(rid)
            // 删除线路成功后删除本地详情表和线路和规则映射表 数据库数据是通过外键级联删除的
            NFCPatrolLineDetails.value.filter(v => v.LineRID === rid).forEach(item => {
              NFCPatrolLineDetail.deleteData(item.RID as string)
            })
            NFCPatrolLineAndRulesData.value.filter(v => v.LineRID === rid).forEach(item => {
              NFCPatrolLineAndRules.deleteData(item.RID as string)
            })
          }
        },
        OnServerErr: (errRpc: yrpcmsg.Ymsg) => {
          log.error('IDbNFCPatrolLine Delete server error', errRpc)
          // 处理服务器响应的错误
          reject(false)
        },
        OnLocalErr: (err: any) => {
          log.error('IDbNFCPatrolLine Delete local error', err)
          reject('local')
        },
        OnTimeout: (v: any) => {
          log.warn('IDbNFCPatrolLine Delete timeout', v)
          const options = {
            action: i18n.t('common.delete'),
            name: data.Name,
          }
          const reason = i18n.t('message.timeout', options)
          reject(reason)
        },
      }
      PrpcDbNFCPatrolLine.Delete(data, options)
    })
  }

  // 更新巡查线路
  function updateLineData(data: bysdb.IDbNFCPatrolLine): Promise<boolean> {
    return new Promise((resolve, reject) => {
      data.UpdatedAt = sysUtcTime()
      const options: ICallOption = {
        OnResult: (res: crud.IDMLResult, rpcCmd: yrpcmsg.Ymsg, meta?: yrpcmsg.IGrpcMeta) => {
          log.info('IDbNFCPatrolLine update result', res, rpcCmd, meta)
          if (res.AffectedRow === 0) {
            reject('failed')
          } else {
            resolve(true)
            // 同步到数据容器对象
            NFCPatrolLine.setData(data.RID as string, data)
          }
        },
        OnServerErr: (errRpc: yrpcmsg.Ymsg) => {
          log.error('IDbNFCPatrolLine update server error', errRpc)
          // 处理服务器响应的错误
          reject(false)
        },
        OnLocalErr: (err: any) => {
          log.error('IDbNFCPatrolLine update local error', err)
          reject('local')
        },
        OnTimeout: (v: any) => {
          log.warn('IDbNFCPatrolLine update timeout', v)
          const options = {
            action: i18n.t('common.modify'),
            name: data.Name,
          }
          const reason = i18n.t('message.timeout', options)
          reject(reason)
        },
      }
      PrpcDbNFCPatrolLine.Update(data, options)
    })
  }

  // 更新线路和规则映射
  function updateLineAndRules() {
    const rid = currentRow.value.RID as string
    // const needDelRules = NFCPatrolLineRulesDataMap.value[rid].filter(item => !linePatrolRules.value.some(v => v.key === item.RID))
    const needDelLineAndRules = NFCPatrolLineAndRulesDataMap.value[rid].filter(item => !linePatrolRules.value.some(v => v.key === item.RuleRID))
    // const needAddRules = linePatrolRules.value.filter(item => !NFCPatrolLineRulesDataMap.value[rid]?.some(v => v.RID === item.key)).map(item => {
    //   return NFCPatrolLineRules.getData(item.key)
    // })
    const UpdatedAt = utcTime()
    const needAddLineAndRules = linePatrolRules.value.filter(item => !NFCPatrolLineAndRulesDataMap.value[rid].some(v => v.RuleRID === item.key)).map(item => {
      return {
        RID: uuidV1(),
        LineRID: rid,
        RuleRID: item.key,
        OrgRID: currentRow.value.OrgRID,
        UpdatedAt,
      }
    })

    const delLineAndRulesPromise = deleteLineAndRules(needDelLineAndRules)
    const addLineAndRulesPromise = insertPatrolLineAndRulesData(needAddLineAndRules)

    Promise.allSettled(delLineAndRulesPromise)
      .then(res => {
        log.info('lineAndRules delete success', res)
      })
    Promise.allSettled(addLineAndRulesPromise)
      .then(res => {
        log.info('lineAndRules add success', res)
      })
  }

  // 更新巡查线路详情表
  function updateLineDetailData() {
    const needDelDetail = NFCPatrolLineAndDetailsDataMap.value[currentRow.value.RID as string]?.filter(item => !lineDetails.value.some(v => v.key === item.MarkerRID))
    const UpdatedAt = utcTime()
    const needAddDetail = lineDetails.value?.filter(item => !NFCPatrolLineAndDetailsDataMap.value[currentRow.value.RID as string]?.some(v => v.MarkerRID === item.key)).map(item => {
      return {
        RID: uuidV1(),
        LineRID: currentRow.value.RID,
        MarkerRID: item.key,
        UpdatedAt,
        OrgRID: currentRow.value.OrgRID
      }
    }) ?? []
    const delDetailPromise = deleteLineDetails(needDelDetail)
    const addDetailPromise = insertDetailData(needAddDetail)

    Promise.allSettled(delDetailPromise)
      .then(res => {
        log.info('delete detail success', res)
      })
    Promise.allSettled(addDetailPromise)
      .then(res => {
        log.info('add detail success', res)
      })
  }

  function updateData(data: bysdb.IDbNFCPatrolLine): Promise<boolean> {
    return new Promise((resolve, reject) => {
      updateLineData(data)
        .then(res => {
          log.info('update line success', res)
          // 更新巡查详情表
          updateLineDetailData()
          // 更新线路和规则映射表
          updateLineAndRules()
          resolve(true)
        })
        .catch(err => {
          log.info('update line fail', err)
          reject(false)
        })
    })
  }

  function customCheckLineName(val: string) {
    return !NFCPatrolLines.value.some(line => line.Name === val && line.RID !== currentRow.value.RID)
  }

  // 针对巡查点和巡查规则的额外导出处理
  function extraExportFunc(source, _row) {
    source.details = source.details.map(item => getMarkerNo(item.MarkerRID)).join(',')
    source.rules = source.rules.map(item => item.Name).join(',')
    return columnsProcess(extraColumns.value, source, _row)
  }

  function batchAddData(dbList: bysdb.IDbNFCPatrolLine[], progress?: (value: number) => void) {
    const parentCache: { [key: string]: any } = {}
    return new Promise((resolve) => {
      const errorDataList: any = []
      // 使用迭代器逐个添加到数据库
      const it = dbList[Symbol.iterator]()
      // 当前处理的第几个数据
      let currentCount = 0
      const insert = async (item) => {
        // 进度信息，当前处理第几个数据
        progress?.(currentCount++)
        const { done, value } = item
        // 已经处理过每一个数据
        if (done) {
          // 已经处理过每一个数据，结束
          return resolve(errorDataList)
        }

        const data: Record<string, any> = getDefFormData()
        Object.assign(data, value)
        // 重置单位上级RID,当前OrgRID为上级单位简称，需要转换为对应的UUID
        // 查找上级是否为本地数据，暂时不处理没有权限的上级
        const parentOption = parentCache[data.OrgRID as string] ||
          parentOptions.value.find(item => item.label === data.OrgRID)
        if (!parentOption) {
          value.$info = i18n.t('message.parentUnitErr')
          errorDataList.push(value)
          // 处理下一个数据
          insert(it.next())
          return
        }
        parentCache[data.OrgRID as string] = parentOption
        data.OrgRID = parentOption.value

        // 处理详情表
        const names = data.details.split(',')
        const UpdatedAt = utcTime()
        data.details = []
        for (let i = 0; i < names.length; i++) {
          const m = bysMarkerData.value.find(item => item.MarkerNo === names[i])
          if (m) {
            data.details.push({
              RID: uuidV1(),
              LineRID: data.RID,
              MarkerRID: m.RID,
              OrgRID: data.OrgRID,
              UpdatedAt,
            })
          }
        }

        // 处理线路与规则映射表
        const ruleNames = data.rules?.split(',') ?? []
        data.rules = NFCPatrolLineRulesData.value.filter(r => ruleNames.some(item => item === r.Name))
        const lineAndRules = data.rules.map(item => {
          return {
            RID: uuidV1(),
            LineRID: data.RID,
            RuleRID: item.RID,
            OrgRID: data.OrgRID,
            UpdatedAt,
          }
        })

        const err: boolean | string = checkDataByRules(data, lineRules.value)
        if (err === true) {
          // 添加数据到数据库
          await insertData(data, { details: data.details, lineAndRules })
            .catch((error) => {
              value.$info = error
              errorDataList.push(value)
            })
        } else {
          value.$info = err
          errorDataList.push(value)
        }
        // 处理下一个数据
        insert(it.next())
      }
      insert(it.next())
    })
  }

</script>

<style lang="scss">
  .patrol-lines-cls:not(.maximized) {
    max-width: 63vw !important;
    width: 63vw !important;
  }

  .patrol-line-edit-content {
    max-width: 800px;
  }

  .lineDetail {
    display: flex;
    flex-wrap: wrap;
  }
</style>

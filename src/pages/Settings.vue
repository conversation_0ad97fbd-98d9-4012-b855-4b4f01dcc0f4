<template>
  <dialog-modal
    v-model="visible"
    v-model:maximized="maximized"
    contentHeight="auto"
    content-class="setting-page"
    :title="title"
  >
    <q-splitter
      class="full-height border rounded-sm"
      v-model="splitterModel"
    >

      <template v-slot:before>
        <q-tabs
          v-model="tab"
          vertical
          dense
          class="text-teal"
        >
          <q-tab
            name="userSettings"
            :label="$t('settingsPage.userSettings')"
            icon="settings"
          />
          <q-tab
            name="systemSettings"
            :label="$t('settingsPage.systemSetting')"
            icon="tune"
          />
          <q-tab
            name="serveSettings"
            :label="$t('settingsPage.serverSettings')"
            icon="dns"
            v-if="haveMaintainPerm"
          />
          <q-tab
            name="security"
            :label="$t('settingsPage.security')"
            icon="vpn_key"
          />
        </q-tabs>
      </template>

      <template v-slot:after>
        <q-tab-panels
          v-model="tab"
          animated
          keep-alive
          transition-prev="jump-up"
          transition-next="jump-up"
          class="full-height"
        >
          <q-tab-panel
            name="userSettings"
            class="user-settings-panel"
          >
            <user-settings></user-settings>
          </q-tab-panel>

          <q-tab-panel
            name="security"
            class="security-panel"
          >
            <change-password></change-password>
          </q-tab-panel>

          <q-tab-panel
            name="systemSettings"
            class="system-settings-panel"
          >
            <system-settings></system-settings>
          </q-tab-panel>

          <q-tab-panel
            name="serveSettings"
            class="serve-settings-panel"
            v-if="haveMaintainPerm"
          >
            <marker-serve-settings></marker-serve-settings>
          </q-tab-panel>
        </q-tab-panels>
      </template>
    </q-splitter>
  </dialog-modal>
</template>

<script lang="ts" setup>
  import { ref, computed, defineAsyncComponent } from 'vue'
  import { useI18n } from 'vue-i18n'
  import { useDialogModal } from '@utils/vueUse/dialog'
  import { isHaveMaintainPerm } from '@utils/common'

  const UserSettings = defineAsyncComponent(() => import('@components/settings/UserSettings.vue'))
  const SystemSettings = defineAsyncComponent(() => import('@components/settings/SystemSettings.vue'))
  const ChangePassword = defineAsyncComponent(() => import('@components/settings/ChangePassword.vue'))
  const MarkerServeSettings = defineAsyncComponent(() => import('@components/settings/MarkerServeSettings.vue'))

  const i18n = useI18n()
  const tab = ref('userSettings')
  const splitterModel = ref(25)
  const title = computed(() => i18n.t('menus.userSettings'))
  const haveMaintainPerm = computed(() => {
    return isHaveMaintainPerm()
  })
  const { visible, maximized, dialogModal } = useDialogModal()
</script>

<style lang="scss">
  .setting-page {
    width: 480px;
    height: 360px;
    @apply flex;
    @apply flex-col;


    .bys-move-page-container {
      @apply flex-auto;
    }
  }
</style>

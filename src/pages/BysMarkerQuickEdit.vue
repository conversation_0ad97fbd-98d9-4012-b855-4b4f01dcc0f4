<template>
  <modal
    v-model="visible"
    v-model:maximized="maximized"
    :max-height="'80vh'"
    :content-class="'marker-edit-dlg'"
    :supportMaximize="false"
    :dblClick="false"
    ref="modal"
  >
    <template v-slot:header><span>{{ title }}</span></template>
    <q-form ref="editorForm">
      <div class="marker-edit-dlg-cls">
        <bys-marker-edit
          v-model="markerData"
          :updateRowData="updateRowData"
          class="marker-edit-dlg-form"
          ref="bysMarkerEdit"
        />
        <div class="text-center marker-edit-dlg-btn">
          <q-btn
            class="bg-primary text-white button-letter w-48"
            :label="$t('form.submit')"
            :disable="!canEdit"
            @click="updateMarkerData"
          />
        </div>
      </div>
    </q-form>
  </modal>
</template>

<script lang="ts">
  import { defineComponent } from 'vue'
  import BysMarkerEdit from '@components/bysMarkerEdit/BysMarkerEdit.vue'
  import bysMarkerEdit from '@utils/mixins/bysMarkerEdit'
  import dialogMixin from '@utils/mixins/dialog'
  import PermissionMixin from '@utils/mixins/permission'
  import { DbName } from '@utils/permission'
  import { bysdb } from '@ygen/bysdb'
  import { deleteOneICCIDAndIMEI } from '@services/controller'

  export default defineComponent({
    name: 'BysMarkerQuickEdit',
    mixins: [bysMarkerEdit, dialogMixin, PermissionMixin],
    data() {
      return {
        markerData: {} as bysdb.DbBysMarker,
        isVisible: true,
        updateRowData: false,
      }
    },
    components: {
      BysMarkerEdit,
    },
    methods: {
      updateMarkerData() {
        const settings = this.$refs.bysMarkerEdit?.getMarkerSettings()
        if (settings){
          this.markerData.MarkerSettings = JSON.stringify(settings)
        }
        this.$refs.editorForm
          .validate()
          .then((valid: boolean) => {
            valid && this.updateData(this.markerData)
              .then(() => {
                this.$q.notify({
                  message: this.$t('message.updateSuccess') as string,
                  color: 'positive',
                  icon: 'check_circle',
                })
                this.isVisible = false
                // 同步删除IMEI和ICCID预选框中已被选择使用掉的
                deleteOneICCIDAndIMEI(this.markerData.IMEI)
              })
              .catch((reason: string) => {
                this.$q.notify({
                  message: reason,
                  color: 'red',
                  icon: 'error',
                  position: 'top',
                })
              })
          })
      },
    },
    computed: {
      dbName() {
        return DbName.DbBysMarker
      },
      title() {
        return this.$t('form.edit')
      },
    },
  })
</script>

<style lang="scss">
  .marker-edit-dlg:not(.maximized) {
    display: flex;
    flex-direction: column;
  }

  .marker-edit-dlg,
  .marker-edit-dlg .marker-edit-dlg-cls {
    width: fit-content !important;
    max-width: unset !important;
  }

  .marker-edit-dlg-cls {
    overflow: auto;
    display: flex;
    flex-direction: column;
  }

  .marker-edit-dlg-cls .marker-edit-dlg-form {
    flex: auto;
    overflow: auto;
  }

  .marker-edit-dlg-cls .marker-edit-dlg-btn {
    flex: none;
    margin-top: 10px;
  }

  .marker-edit-dlg .bys-move-header-wrapper {
    height: 32px;
    background-color: #027BE3;
    z-index: 99;

    .bys-move-header {
      position: fixed;
      width: 100%;
    }
  }
</style>

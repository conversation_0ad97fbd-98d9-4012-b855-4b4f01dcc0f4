<template>
  <!-- visible,maximized in dataEdit mixins -->
  <modal
    v-model="visible"
    v-model:maximized="maximized"
    :contentHeight="dataLen ? '68vh' : 'auto'"
    :content-class="'marker-page-cls'"
  >
    <template #header>
      <span v-text="title"></span>
    </template>
    <!--
    界桩管理菜单
    行选中保留项：OrgRID（单位），用来做为添加时的默认单位 。
      OrgRID -> defaultOrgID -> currentRow.OrgRID
    重复添加保留项：MarkerNo（编号）,OrgRID（单位）,MarkerHWID(界桩ID),ControllerRID(上级控制器)，其中编号、界桩ID名称要保留格式自增,单位、上级控制器直接继承。
    -->
    <data-edit
      :data="bysMarkerData"
      :renderChildRow="renderChildRow"
      :columns="columns"
      :form-title="title"
      :can-add="canAdd"
      :can-edit="canEdit"
      :can-delete="canDelete"
      :insert="insertData"
      :update="finalUpdateData"
      :delete="deleteData"
      :import-data="batchAddData"
      v-model:currentRow="currentRow"
      v-model:isUpdateRowData="updateRowData"
      :filter-columns='filterColumns'
      :extraExportFunc="extraExportFunc"
      :extraImportFunc="extraImportFunc"
      :renderPreviewChildRow="renderPreviewChildRow"
      :tbodyTrClasses="markerDisabledTrClassFunc"
      @new-row="newRow"
      @rwo-clicked="rowClicked"
      @save-multiplex-item="saveMultiplexItem"
      @clear-multiplex-item="clearMultiplexItem"
      extraTableData
      id="bys-marker-data-edit"
      ref="dataEdit"
    >
      <template #form-content>
        <bys-marker-edit
          v-model="currentRow"
          :updateRowData="updateRowData"
          ref="bysMarkerEdit"
        />
      </template>

    </data-edit>
  </modal>
</template>

<script lang="ts">
  import dialogMixin from '@utils/mixins/dialog'
  import dataEditMixin from '@utils/mixins/editor'
  import { DateMask, dayjsFormat, getDateString, getTimeString, sysUtcTime, TimeMask, toLocalTime, toUtcTime, utcTime } from '@utils/dayjs'
  import { v1 as uuidV1 } from 'uuid'
  import { bysdb } from '@ygen/bysdb'
  import { PrpcDbBysMarker } from '@ygen/bysdb.rpc.yrpc'
  import { ICallOption } from 'jsyrpc'
  import { yrpcmsg } from 'yrpcmsg'
  import log from '@utils/log'
  import { BysMarker } from '@services/dataStore'
  import { crud } from '@ygen/crud'
  import { getMapCenter } from '@utils/map'
  import { StrPubSub } from 'ypubsub'
  import createSelfIncreaseStr from '@utils/createSelfIncreaseStr'
  import { DeleteDbBysMarker, InsertDbBysMarker } from '@utils/pubSubSubject'
  import { getGCJ02LngLat, setGCJ02LngLat, toWGS84 } from '@utils/gcoord'
  import bysMarkerEdit from '@utils/mixins/bysMarkerEdit'
  import { outputDBError, MarkerType, checkIs4GMarker } from '@utils/common'
  import { i18n } from '@boot/i18n'
  import { DbName } from '@utils/permission'
  import { columnsProcess } from '@utils/xlsx'
  import { defineComponent, h } from 'vue'
  import { useBysMarkerData } from '@utils/vueUse/bysMarkerData'
  import BysMarkerEdit from '@components/bysMarkerEdit'
  import { QCard, QCardSection, QList, QItem, QItemLabel, QItemSection } from 'quasar'
  import { deleteOneICCIDAndIMEI } from '@src/services/controller'
  import { getMarkerDefaultParams } from '@src/utils/vueUse/useBysMarkerEdit'

  const bysMarkerError = {
    controllerrid_fkey: i18n.global.t('dataBaseError.parControllerErr'),
    orgrid_fkey: i18n.global.t('dataBaseError.parUnitErr'),
    controllerrid_controllerchannel_markerqueueno_key: i18n.global.t('dataBaseError.queueNoErr'),
    markerhwid_key: i18n.global.t('dataBaseError.markerHwidErr'),
    markerno_key: i18n.global.t('dataBaseError.markerNoErr'),
    markerredlineno_key: i18n.global.t('dataBaseError.redLineNoErr'),
    'No operation permission': i18n.global.t('dataBaseError.permissionErr'),
  }

  const { bysMarkerData } = useBysMarkerData()

  export default defineComponent({
    name: 'BysMarker',
    mixins: [dialogMixin, dataEditMixin, bysMarkerEdit],
    data() {
      return {
        defaultOrgRID: '',
        defaultMarkerNo: '',
        defaultMarkerHWID: '',
        defaultControllerRID: '',
        currentRow: {} as bysdb.IDbBysMarker,
        updateRowData: false,
      }
    },
    computed: {
      bysMarkerData() {
        return bysMarkerData.value
      },
      dbName() {
        return DbName.DbBysMarker
      },
      title() {
        return this.$t('menus.boundaryMarker')
      },
      regularColumns() {
        return [
          {
            name: 'ControllerRID', field: 'ControllerRID', label: this.$t('form.parentController'), sortable: true,
            format: (val) => {
              const controller = this.controllerData.find(item => item.RID === val)
              return `${controller ? controller.ControllerNo : ''}`
            },
          },
          {
            name: 'ControllerChannel', field: 'ControllerChannel', label: this.$t('form.baseStationChannel'),
          },
          {
            name: 'MarkerChannel', field: 'MarkerChannel', label: this.$t('form.markerChannel'),
          },
          {
            name: 'MarkerQueueNo', field: 'MarkerQueueNo', label: this.$t('form.markerQueueNo'),
          },
          {
            name: 'MarkerQueueInterval', field: 'MarkerQueueInterval', label: this.$t('form.markerQueueInterval'),
          },
          {
            name: 'MarkerEmergentInterval',
            field: 'MarkerEmergentInterval',
            label: this.$t('form.markerEmergentInterval'),
          },
        ]
      },
      marker4gColumns(): Array<{ [key: string]: any }> {
        return [
          {
            name: 'addr', field: 'addr', label: this.$t('form.addr'), sortable: true
          },
          {
            name: 'attitude', field: 'attitude', label: this.$t('form.attitude') + '（ ° ）', sortable: true,
          },
          {
            name: 'dirft', field: 'dirft', label: this.$t('form.dirft'), sortable: true,
          },
          {
            name: 'amplitude',
            // 此处为4g界桩参数列配置，row为MarkerSettings
            field: (MarkerSettings) => {
              return MarkerSettings.vibration.amplitude
            },
            label: this.$t('form.amplitude'), sortable: true,
            customFieldTarget: (MarkerSettings) => {
              if (!MarkerSettings.vibration) {
                MarkerSettings.vibration = {}
              }
              return MarkerSettings.vibration
            },
          },
          {
            name: 'duration',
            // 此处为4g界桩参数列配置，row为MarkerSettings
            field: (MarkerSettings) => {
              return MarkerSettings.vibration.duration
            },
            label: this.$t('form.duration'), sortable: true,
            customFieldTarget: (MarkerSettings) => {
              if (!MarkerSettings.vibration) {
                MarkerSettings.vibration = {}
              }
              return MarkerSettings.vibration
            },
          },
          {
            name: 't1', field: 't1', label: this.$t('form.deLaySlTime'), sortable: true
          },
          {
            name: 't2', field: 't2', label: this.$t('form.debugModelTime'), sortable: true
          },
          {
            name: 't3', field: 't3', label: this.$t('form.alarmInterval'), sortable: true
          },
          {
            name: 'n1', field: 'n1', label: this.$t('form.alarmNum'), sortable: true
          },
          {
            name: 'ICCID', field: 'ICCID', label: 'ICCID',
          },
          {
            name: 'ExpirationDate', field: 'ExpirationDate', label: this.$t('form.ExpirationDate'), isTime: true,
            format: (val) => {
              return val ? dayjsFormat(val, DateMask) : val
            },
          },
          {
            name: 'IMEI', field: 'IMEI', label: 'IMEI',
          }
        ]
      },
      columns(): Array<{ [key: string]: any }> {
        return [
          {
            name: 'MarkerNo',
            field: 'MarkerNo',
            label: this.$t('form.markerName'),
            sortable: true,
            sticky: true,
            align: 'left',
          },
          {
            name: 'OrgRID', field: 'OrgRID', label: this.$t('form.unit'), sortable: true, noLeftBorder: true,
            format: (val) => {
              const parentOption = this.parentOptions.find(item => item.value === val)
              return `${parentOption ? parentOption.label : ''}`
            },
          },
          { name: 'MarkerHWID', field: 'MarkerHWID', label: this.$t('form.boundaryID'), sortable: true },
          {
            name: 'MarkerModel', field: 'MarkerModel', label: this.$t('form.boundaryModel'), sortable: true,
            format: (val) => {
              const typeOption = this.MarkerModelOptions.find(item => item.value === val)
              return `${typeOption ? typeOption.label : ''}`
            },
          },
          {
            name: 'MarkerType', field: 'MarkerType', label: this.$t('form.boundaryType'), sortable: true,
            format: (val) => {
              return this.MarkerTypeOptions.find(item => item.value === val)?.label ?? (val + '')
            },
          },
          {
            name: 'MarkerWakeupBaseTime',
            field: 'MarkerWakeupBaseTime',
            label: this.$t('form.markerWakeupBaseTime'),
            format: (val) => {
              return toLocalTime(getDateString() + ' ' + val, TimeMask) ?? ''
            },
            isTime: true,
          },
          {
            name: 'MarkerDayInterval', field: 'MarkerDayInterval', label: this.$t('form.markerDayInterval'),
          },
          {
            name: 'Lon', field: 'Lon', label: this.$t('form.lon'),
            format: (val: number) => {
              return this.formatLngLat(val)
            },
          },
          {
            name: 'Lat', field: 'Lat', label: this.$t('form.lat'),
            format: (val: number) => {
              return this.formatLngLat(val)
            },
          },
          { name: 'MapShowLevel', field: 'MapShowLevel', label: this.$t('form.mapShowLevel') },
          // {
          //   name: 'MarkerRedLineNo',
          //   field: 'MarkerRedLineNo',
          //   label: this.$t('form.markerRedLineNo'),
          // },
          {
            name: 'MarkerDisabled',
            field: 'MarkerDisabled',
            label: this.$t('form.markerRemoteKillStun'),
            format: (val) => {
              // 0:遥活 1:遥闭  2:遥晕
              const labels = {
                1: this.$t('form.killRemotely'),
                2: this.$t('form.remoteStun'),
              }
              return labels[val] ?? ''
            },
          },
          { name: 'MarkerDescription', field: 'MarkerDescription', label: this.$t('form.description') },
        ]
      },
      filterColumns() {
        return ['MarkerNo', 'MarkerHWID', 'ControllerRID', 'OrgRID', 'MarkerDescription']
      },
      //控制列的显示与隐藏
      visibleColumns() {
        //隐藏红线图序号
        /*const excludeFields: string[] = ['MarkerRedLineNo']
        return this.columns.filter(col => !excludeFields.includes(col.field))
          .map(col => col.name)*/
        return this.columns.map(col => col.name)
      },
      dataLen() {
        return this.bysMarkerData.length
      },
      getAllMarkerNo() {
        return this.bysMarkerData
          .map((data: bysdb.IDbBysMarker) => {
            return data.MarkerNo
          })
      },
      getAllMarkerHWID() {
        return this.bysMarkerData
          .map((data: bysdb.IDbBysMarker) => {
            return data.MarkerHWID
          })
      },
    },
    methods: {
      finalUpdateData(data: bysdb.IDbBysMarker): Promise<boolean> {
        const settings = this.$refs.bysMarkerEdit?.getMarkerSettings()
        if (settings) {
          data.MarkerSettings = JSON.stringify(settings)
        }
        return this.updateData(data)
      },
      markerDisabledTrClassFunc(row) {
        return row.MarkerDisabled !== 0 ? 'tr-marker-disabled-class' : ''
      },
      is4gMarker(markerType) {
        return checkIs4GMarker(markerType)
      },
      /**
       * 导入xlsx表格数据的额外处理
       * @param {Record<string, any>} row 完成一行xlsx表格数据部分转化为符合要求的格式
       * @param {Record<string, any>} rowJsonData 一行xlsx表格数据转换成的对象， key: col.label, val: xlsx value
       * @return {Record<string, any>} 对rowJsonData进行额外处理添加到row中并返回row
       */
      extraImportFunc(row: Record<string, any>, rowJsonData: Record<string, any>): object {
        const rowMarkerType = this.MarkerTypeOptions.find(item => item.label === row.MarkerType)?.value ?? MarkerType.Regular
        const timerColumns = [
          {
            name: 'timerbase', field: 'timerbase', label: this.$t('form.markerWakeupBaseTime'),
            format: (val) => {
              return toLocalTime(getDateString() + ' ' + val, TimeMask) ?? ''
            },
            isTime: true,
          },
          {
            name: 'timer', field: 'timer', label: this.$t('form.markerDayInterval'), sortable: true,
          },
        ]
        const is4gMarker = this.is4gMarker(rowMarkerType)
        const columns = is4gMarker ? [...this.marker4gColumns, ...timerColumns] : this.regularColumns
        const MarkerSettings: { [key: string]: any } = {}
        columns.forEach(col => {
          // 这里判断field是否为方法，那field或者name的字符串作为属性名（key）
          const key = typeof col.field === 'function' ? col.name : col.field
          const originTarget = (key === 'ICCID' || key === 'ExpirationDate') || !is4gMarker ? row : MarkerSettings
          const target = col.customFieldTarget?.(originTarget) ?? originTarget
          target[key] = rowJsonData[col.label]
        })
        row.MarkerSettings = JSON.stringify(MarkerSettings)
        return row
      },
      /**
       * 导出表格数据的额外处理函数
       * @param {Object} source - 表格的一行源数据
       * @param {Object} _row - 完成部分处理的xlsx表格数据
       * @return {Object} 对源数据处理完后保存在_row并返回
       */
      extraExportFunc(source, _row) {
        // 4g界桩独有的iccid卡的数据存在source里面，导出处理只针对MarkerSettings（4g界桩的独有）
        const { ICCID, ExpirationDate } = source
        const columns = this.is4gMarker(source.MarkerType) ? this.marker4gColumns : this.regularColumns
        const rowData = this.is4gMarker(source.MarkerType) ? Object.assign(JSON.parse(source.MarkerSettings), {
          ICCID,
          ExpirationDate
        }) : source
        return columnsProcess(columns, rowData, _row)
      },
      renderChild(row, rowVal, markerType) {
        const { ICCID, ExpirationDate, IMEI } = row
        const rows = this.is4gMarker(markerType) ? Object.assign(JSON.parse(row.MarkerSettings), {
          ICCID,
          ExpirationDate,
          IMEI,
        }) : row
        const columns = this.is4gMarker(markerType) ? this.marker4gColumns : this.regularColumns
        const listItem = (label, val) => {
          return h(QItem, {
            dense: true
          }, () => [h(QItemSection, () => [
            h(QItemLabel, { caption: true }, () => label),
            h(QItemLabel, () => val)
          ])]
          )
        }
        const listItems = columns.map(item => {
          return listItem(item.label, rowVal(rows, item.field, item))
        })
        const lists = h(QList, {
          class: ['markerDetail'],
        }, () => listItems)
        const card = h(QCard, {
          flat: true ,
          class: ['card-margin']
        }, () => [h(QCardSection, () => [lists])])
        return card
      },
      renderPreviewChildRow({row}) {
        const rowVal = (row, field, col) => {
          return typeof col.field === 'function' ?
            col.field(row) : row[field]
        }
        return this.renderChild(row, rowVal, row.MarkerType === this.$t('form.4GMarkers') ? 1 : 0)
      },
      renderChildRow({ row }) {
        const rowVal = (row, field, col) => {
          return typeof col.field === 'function' ?
            col.field(row) :
            typeof col.format === 'function' ? col.format(row[field], row) : row[field]
        }
        return this.renderChild(row, rowVal, row.MarkerType)
      },
      rowClicked(rowData) {
        this.defaultOrgRID = rowData.OrgRID
      },
      saveMultiplexItem() {
        this.defaultOrgRID = this.currentRow.OrgRID
        this.defaultMarkerNo = this.currentRow.MarkerNo
        this.defaultControllerRID = this.currentRow.ControllerRID
        this.defaultMarkerHWID = this.currentRow.MarkerHWID
      },
      clearMultiplexItem() {
        this.defaultMarkerNo = ''
        this.defaultControllerRID = ''
        this.defaultMarkerHWID = ''
      },
      getDefFormData() {
        const now = new Date().setHours(1, 0, 0, 0)
        const data: bysdb.IDbBysMarker = new bysdb.DbBysMarker({
          RID: uuidV1(),
          OrgRID: '',
          MarkerNo: '',
          MarkerDescription: '',
          ControllerRID: '',
          ControllerChannel: 1,
          MarkerHWID: 0,
          MarkerModel: 1,
          Lon: 0,
          Lat: 0,
          Setting: '{}',
          MapShowLevel: 12,
          MarkerQueueNo: 1,
          MarkerParamTime: sysUtcTime(),
          MarkerDayInterval: 24,
          MarkerQueueInterval: 20,
          MarkerEmergentInterval: 60,
          MarkerChannel: 1,
          MarkerWakeupBaseTime: getTimeString(now, true),
          MarkerDisabled: 0,
          UpdatedAt: utcTime(),
          UpdatedDC: '',
          HasInstallDevice: false,
          // MarkerRedLineNo: 1000,
          MarkerType: 0,
          MarkerSettings: '{}',
          ICCID: '',
          ExpirationDate: '',
        })
        // 默认的经、纬坐标为地图中心坐标
        const { lng, lat } = (getMapCenter() || {}) as { lng: number; lat: number }
        const centerWGS84 = toWGS84([lng, lat])
        Object.assign(data, { Lon: centerWGS84[0], Lat: centerWGS84[1] })

        return data
      },
      newRow() {
        this.updateRowData = false
        this.currentRow.OrgRID = this.currentRow.OrgRID || this.defaultOrgRID
        this.currentRow.MarkerNo = createSelfIncreaseStr((this.currentRow.MarkerNo || '') + '', this.getAllMarkerNo)
        this.currentRow.MarkerHWID = parseInt(
          createSelfIncreaseStr((this.currentRow.MarkerHWID || 0) + '', this.getAllMarkerHWID),
        )
        this.markerQueueNoChange(this.currentRow.MarkerQueueNo + 1, false)
        // 变更红线图规则后，该字段不再处理
        // this.currentRow.MarkerRedLineNo = this.currentRow.MarkerHWID * 1000

        if (this.currentRow.RID) {
          delete this.currentRow.RID
        }

        this.currentRow = Object.assign(this.getDefFormData(), this.currentRow)
        this.currentRow.Setting = '{}' //将模板界桩的setting配置清空，防止新设置的界桩默认就是遥晕遥毙
      },

      insertData(data: bysdb.IDbBysMarker): Promise<boolean> {
        return new Promise((resolve, reject) => {
          const options: ICallOption = {
            OnResult: (res: crud.IDMLResult, rpcCmd: yrpcmsg.Ymsg, meta?: yrpcmsg.IGrpcMeta) => {
              log.info('IDbBysMarker Insert result', res, rpcCmd, meta)
              if (res.AffectedRow === 0) {
                reject('failed')
              } else {
                resolve(true)

                // 同步到数据容器对象
                const RID = data.RID as string
                const GPS = getGCJ02LngLat(data)
                const newMarker = new bysdb.DbBysMarker(data)
                setGCJ02LngLat(newMarker, GPS)
                BysMarker.setData(RID, newMarker)
                  .setDataIndex(newMarker.MarkerHWID + '', RID)
                // 发布插入数据通知
                StrPubSub.publish(InsertDbBysMarker, newMarker)
                // 同步删除IMEI和ICCID预选框中已被选择使用掉的
                deleteOneICCIDAndIMEI(newMarker.IMEI)
              }
            },
            OnServerErr: (errRpc: yrpcmsg.Ymsg) => {
              log.error('IDbBysMarker Insert server error', errRpc)
              /*this.$q.notify({
                message: errRpc.Optstr,
                type: 'warning',
                position: 'top',
              })*/
              // 处理服务器响应的错误
              // let reason = this.$t('message.addFailed')
              // // dbbysmarker_markerno_key 自编号重复
              // if (errRpc.Optstr.includes('dbbysmarker_markerno_key')) {
              //   reason = this.$t('message.duplicateOrgID', { name: data.MarkerNo })
              // }
              let reason = outputDBError(bysMarkerError, errRpc.Optstr)
              reject(reason)
            },
            OnLocalErr: (err: any) => {
              log.error('IDbBysMarker Insert local error', err)
              reject('local')
            },
            OnTimeout: (v: any) => {
              log.warn('IDbBysMarker Insert timeout', v)
              const options = {
                action: this.$t('common.add'),
                name: data.MarkerNo,
              }
              const reason = this.$t('message.timeout', options)
              reject(reason)
            },
          }
          PrpcDbBysMarker.Insert(data, options)
        })
      },
      deleteData(data: bysdb.IDbBysMarker): Promise<boolean> {
        return new Promise((resolve, reject) => {
          const options: ICallOption = {
            OnResult: (res: crud.IDMLResult, rpcCmd: yrpcmsg.Ymsg, meta?: yrpcmsg.IGrpcMeta) => {
              log.info('IDbBysMarker Delete result', res, rpcCmd, meta)
              if (res.AffectedRow === 0) {
                reject('failed')
              } else {
                resolve(true)
                // 同步到数据容器对象
                BysMarker.deleteData(data.RID as string)
                // 发布删除数据通知
                StrPubSub.publish(DeleteDbBysMarker, data)
              }
            },
            OnServerErr: (errRpc: yrpcmsg.Ymsg) => {
              log.error('IDbBysMarker Delete server error', errRpc)
              // 处理服务器响应的错误
              let reason = outputDBError(bysMarkerError, errRpc.Optstr)
              reject(reason)
            },
            OnLocalErr: (err: any) => {
              log.error('IDbBysMarker Delete local error', err)
              reject('local')
            },
            OnTimeout: (v: any) => {
              log.warn('IDbBysMarker Delete timeout', v)
              const options = {
                action: this.$t('common.delete'),
                name: data.MarkerNo,
              }
              const reason = this.$t('message.timeout', options)
              reject(reason)
            },
          }
          PrpcDbBysMarker.Delete(data, options)
        })
      },
      batchAddData(dbList: bysdb.IDbBysMarker[], progress?: (value: number) => void): Promise<any> {
        const duplicateData: { [key: string]: bysdb.IDbBysMarker } = {}
        const parentCache: { [key: string]: any } = {}
        const controllerCache: { [key: string]: bysdb.IDbController } = {}
        const markerModelCache: { [key: string]: any } = {}
        return new Promise((resolve) => {
          // 缓存有异常的数据
          const errorDataList: bysdb.IDbBysMarker[] = []
          // 使用迭代器逐个添加到数据库
          const it = dbList[Symbol.iterator]()
          // 当前处理的第几个数据
          let currentCount = 0
          const insert = async (item) => {
            // 进度信息，当前处理第几个数据
            progress?.(currentCount++)
            const { done, value } = item
            // 已经处理过每一个数据
            if (done) {
              // 已经处理过每一个数据，结束
              return resolve(errorDataList)
            }

            // 合并默认参数
            const data: bysdb.IDbBysMarker = this.getDefFormData()
            const MarkerSettings = getMarkerDefaultParams()
            Object.assign(data, value)
            const oldMarkerSettings = JSON.parse(data.MarkerSettings ?? '{}')
            // 处理界桩的类型
            data.MarkerType = this.MarkerTypeOptions.find(item => item.label === data.MarkerType)?.value
            // 过滤本地已经存在的数据
            // const localData = BysMarker.getDataByIndex(data.MarkerHWID + '')
            const localData = duplicateData[data.MarkerNo + ''] ||
              this.bysMarkerData.find(item => item.ControllerNo === data.MarkerNo)
            if (localData) {
              duplicateData[data.MarkerNo + ''] = localData
              value.$info = this.$t('message.duplicateData')
              errorDataList.push(value)
              // 处理下一个数据
              insert(it.next())
              return
            }
            // 重置单位上级RID,当前OrgRID为上级单位简称，需要转换为对应的UUID
            // 查找上级是否为本地数据，暂时不处理没有权限的上级
            const parentOption = parentCache[data.OrgRID as string] ||
              this.parentOptions.find(item => item.label === data.OrgRID)
            if (!parentOption) {
              value.$info = this.$t('message.parentUnitErr')
              errorDataList.push(value)
              // 处理下一个数据
              insert(it.next())
              return
            }
            parentCache[data.OrgRID as string] = parentOption
            data.OrgRID = parentOption.value

            if (data.MarkerType === MarkerType.Regular) {
              // 处理上级控制器UUID ControllerRID填写的是控制器的ID
              // const controller: bysdb.IDbController | undefined = Controller.getDataByIndex(data.ControllerRID + '')
              const controller = controllerCache[data.ControllerRID + ''] ||
                this.controllerData.find(item => item.ControllerNo === data.ControllerRID)
              if (!controller) {
                value.$info = this.$t('import.notFoundController')
                errorDataList.push(value)
                // 处理下一个数据
                insert(it.next())
                return
              }
              controllerCache[data.ControllerRID + ''] = controller
              data.ControllerRID = controller.RID
              // 控制器通道数不能超出控制器的定义范围
              if (data.ControllerChannel && data.ControllerChannel > (controller.ChannelCount as number)) {
                data.ControllerChannel = controller.ChannelCount
              }
              // 当前界桩只能2个信道
              if (data.MarkerChannel && data.MarkerChannel > 2) {
                data.MarkerChannel = 2
              }
            } else {
              // 4g界桩需要合并一次4g界桩的默认参数
              Object.assign(MarkerSettings, oldMarkerSettings)
              // 处理4g界桩deviceID
              MarkerSettings.deviceID = data.MarkerHWID
              // 如果是4g界桩，还需要将MarkerSettings的唤醒基准也转化为utc
              MarkerSettings!.timerbase = MarkerSettings!.timerbase ? toUtcTime(
                getDateString() + ' ' + MarkerSettings!.timerbase,
                TimeMask,
              ) : getTimeString(new Date().setHours(1, 0, 0, 0), true)
              data.MarkerSettings = JSON.stringify(MarkerSettings)
              // iccid和到期日期必须同时存在
              if (data.ICCID && !data.ExpirationDate) {
                value.$info = this.$t('import.simultaneously')
                errorDataList.push(value)
                // 处理下一个数据
                insert(it.next())
                return
              }
              // 存在到期日期参数时， 去掉前面的'`'， xlsx中时间参数第一个字符添加了一个'\'' xlsx.ts 157行
              if (data.ExpirationDate && data.ExpirationDate.startsWith('`')) {
                data.ExpirationDate = data.ExpirationDate.slice(1)
              }
            }

            // 处理界桩类型
            const modelOption = markerModelCache[data.MarkerModel + ''] || this.MarkerModelOptions.find(
              item => item.label === data.MarkerModel)
            if (!modelOption) {
              // 不存在默认为'长'类型
              data.MarkerModel = 1
            }
            markerModelCache[data.MarkerModel + ''] = modelOption
            data.MarkerModel = modelOption.value

            // 唤醒基准时间转utc
            data.MarkerWakeupBaseTime = data.MarkerWakeupBaseTime ? toUtcTime(
              getDateString() + ' ' + data.MarkerWakeupBaseTime,
              TimeMask,
            ) : getTimeString(new Date().setHours(1, 0, 0, 0), true)

            // 处理界桩是否被遥毙
            data.MarkerDisabled = data.MarkerDisabled === this.$t('common.yes') ? 1 : 0

            // 转换数字类型参数
            const CoverPropList = [
              'ControllerChannel', 'Lon', 'Lat', 'MarkerHWID', 'MarkerModel', 'MapShowLevel',
              'MarkerQueueNo', 'MarkerDayInterval', 'MarkerQueueInterval', 'MarkerEmergentInterval',
              'MarkerChannel',
              // 'MarkerDisabled',
            ]
            this.coverPropToNumber(data, CoverPropList)

            const CoverPropStringList = [
              'MarkerNo',
              'MarkerDescription',
              'ControllerRID',
              'MarkerParamTime',
              'MarkerWakeupBaseTime',
              'OrgRID',
            ]
            this.coverPropToString(data, CoverPropStringList)
            const markerSettingsValid = (data, key) => {
              const props = ['addr', 'timerbase', 'timer', 'attitude', 'dirft', 't1', 't2', 't3', 'n1']
              const vibration = ['amplitude', 'duration']
              if (props.includes(key)) {
                return MarkerSettings[key]
              } else if (vibration.includes(key)) {
                return MarkerSettings.vibration[key]
              }
              return data[key]
            }
            let err: boolean | string = this.checkDataByRules(data, this.formRules[data.MarkerType as number], (checkIs4GMarker(data.MarkerType)) ? markerSettingsValid : undefined)
            if (err === true) {
              // 添加数据到数据库
              await this.insertData(data)
                .catch((error) => {
                  value.$info = error
                  errorDataList.push(value)
                })
            } else {
              value.$info = err
              errorDataList.push(value)
            }

            // 处理下一个数据
            insert(it.next())
          }
          insert(it.next())
        })
      },
    },
    components: {
      BysMarkerEdit
    },
  })
</script>

<style lang="scss">
  #bys-marker-data-edit {
    tr.tr-marker-disabled-class {
      background-color: #fffcdf;

      td[class*="sticky-column-"] {
        background-color: #fffcdf;
      }
    }

    tr.tr-marker-disabled-class.selected-row {
      background-color: $blue-3;

      td[class*="sticky-column-"] {
        background-color: $blue-3;
      }
    }
  }

  .marker-page-cls:not(.maximized) {
    max-width: 63vw !important;
    width: 63vw !important;
  }

  .card-margin {
    margin: 5px;
  }

  .markerDetail {
    display: flex;
    flex-wrap: wrap;
  }
</style>

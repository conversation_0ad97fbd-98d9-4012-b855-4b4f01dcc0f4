<template>
  <dialog-modal
    v-model="visible"
    maximized
    contentClass="NFC-patrol-modal"
    :title="$t('NFCPatrol.NFCPatrol')"
  >
    <template #header-actions>
      <q-btn
        flat
        unelevated
        icon="edit_document"
        class="text-white"
        :disable="noNFC || !haveMaintainPerm"
        @click="writeNFCVisible = !writeNFCVisible"
        :label="$t('NFCPatrol.writeNFC')"
      ></q-btn>
      <q-btn
        flat
        unelevated
        icon="upload"
        class="text-white upload-all"
        :disable="uploadAllRecords || isUploading"
        @click="uploadAll"
        :label="$t('NFCPatrol.uploadAll')"
      >
        <q-badge
          v-if="!uploadAllRecords"
          color="red"
          floating
          transparent>{{ unUploadRecords.length }}</q-badge>
      </q-btn>
    </template>

    <q-card
      flat
      v-if="readRecords.length === 0"
      class="flex justify-center items-center h-full"
    >
      <q-item class="flex flex-col items-center gap-1">
        <q-item-section>
          <q-avatar size="128px">
            <q-icon
              name="mdi-nfc"
              color="purple"
            ></q-icon>
          </q-avatar>
        </q-item-section>
        <q-item-section class="text-h6">
          {{ $t('NFCPatrol.readTip') }}
        </q-item-section>
      </q-item>
    </q-card>

    <q-list
      v-else
      bordered
      separator
      class="h-full w-full overflow-auto rounded-md nfc-record-container"
    >

      <template
        v-for="(record, index) in readRecords"
        :key="record.id + record.readTime"
      >
        <q-item class="flex-col gap-2">
          <div :class="['triangle', record.uploaded ? 'triangle-positive' : '']">
            <q-icon
              v-if="!record.uploading"
              class="triangle-upload-tag"
              :name="record.uploaded ? 'task_alt' : 'info'">
            </q-icon>
            <q-circular-progress
              v-else
              indeterminate
              rounded
              size="16px"
              color="grey-1"
              class="triangle-upload-tag relative top-[44px]"
            ></q-circular-progress>
          </div>
          <q-item-section class="flex-row justify-between gap-2">
            <q-item-label class="flex gap-2 items-center text-caption text-weight-light2 text-italic text-grey-8">
              <span>[{{ index + 1 }}]</span>
              <span class="mr-1">{{ dayjsFormat(record.readTime, TimeMask) }}</span>
              <span>{{ record.id }}</span>
            </q-item-label>
            <q-btn
              v-if="!record.uploaded"
              dense
              round
              unelevated
              color="warning"
              size="sm"
              :disable="record.uploaded || isUploading"
              icon="publish"
              @click.stop.prevent="manualUploadRecord(record)"
              class="absolute right-[10px] top-[46px]"
            />
          </q-item-section>

          <q-item-section class="grid grid-cols-2">
            <div class="flex items-center mb-[5px]">
              <q-item-label caption>{{ $t('form.unit') }}</q-item-label>
              <q-item-label class="!mt-0 ml-[5px]">{{ record.orgShortName }}</q-item-label>
            </div>
            <div class="flex items-center mb-[5px]">
              <q-item-label caption>{{ $t('form.markerName') }}</q-item-label>
              <q-item-label class="!mt-0 ml-[5px] flex items-center">
                {{ record.extra?.MarkerNo }}
                <!-- <q-btn
                  v-if="false"
                  icon="iconfont bys-details"
                  color="light"
                  size="xs"
                  flat
                  round
                  dense>
                  <q-popup-proxy>
                    <q-banner>
                      <p>{{ $t('form.boundaryModel') }}: {{ record.extra?.MarkerModel === 1 ? $t('form.long') : $t('form.short') }}</p>
                      <p>{{ $t('form.boundaryID') }}: {{ record.extra?.MarkerHWID }}</p>
                      <p>{{ $t('form.markerWakeupBaseTime') }}: {{ toLocalTime(getDateString() + ' ' + record.extra?.MarkerWakeupBaseTime, TimeMask) }}</p>
                      <p>{{ $t('form.markerDayInterval') }}: {{ record.extra?.MarkerDayInterval }}</p>
                      <p>{{ $t('form.lon') }}: {{ record.extra?.Lon }}</p>
                      <p>{{ $t('form.lat') }}: {{ record.extra?.Lat }}</p>
                      <p>{{ $t('form.attitude') }}: {{ record.paramsWith4g?.attitude }}</p>
                      <p>{{ $t('form.dirft') }}: {{ record.paramsWith4g?.dirft }}</p>
                      <p>{{ $t('form.amplitude') }}: {{ record.paramsWith4g?.vibration.amplitude }}</p>
                      <p>{{ $t('form.duration') }}: {{ record.paramsWith4g?.vibration.duration }}</p>
                      <p>{{ $t('form.deLaySlTime') }}: {{ record.paramsWith4g?.t1 }}</p>
                      <p>{{ $t('form.debugModelTime') }}: {{ record.paramsWith4g?.t2 }}</p>
                      <p>{{ $t('form.alarmInterval') }}: {{ record.paramsWith4g?.t3 }}</p>
                      <p>{{ $t('form.alarmNum') }}: {{ record.paramsWith4g?.n1 }}</p>
                    </q-banner>
                  </q-popup-proxy>
                </q-btn> -->
              </q-item-label>
            </div>
            <div class="col-span-2 flex items-center mb-[6px]">
              <q-item-label caption>ICCID</q-item-label>
              <q-item-label class="!mt-0 ml-[5px]">{{ record.extra?.ICCID ?? $t('NFCPatrol.noneData') }}</q-item-label>
            </div>
            <div class="col-span-2 flex items-center mb-[6px]">
              <q-item-label caption>{{ $t('form.ExpirationDate') }}</q-item-label>
              <q-item-label class="!mt-0 ml-[5px] flex gap-1 items-center">
                {{ expirationDateFormat(record.extra?.ExpirationDate) ?? $t('NFCPatrol.noneData')}}
                <div v-if="record.extra?.timeLeft" :class="['time-left', `time-left-${record.extra?.timeLeft > 3 ? 'orange' : 'red'}`]">{{ record.extra?.timeLeft}}</div>
              </q-item-label>
            </div>
          </q-item-section>
        </q-item>
      </template>
    </q-list>
  </dialog-modal>
  <q-dialog
    :model-value="writeNFCVisible"
    :seamless="false"
    persistent
    @hide="hideDialog">
      <q-card v-if="!isWrite">
        <q-card-section class="bg-primary text-white !py-[4px] flex">
          <span>{{ $t('NFCPatrol.isWriteTitle') }}</span>
          <q-space></q-space>
            <q-btn
              flat
              v-close-popup
              round
              dense
              size="sm"
              icon="close"
            />
        </q-card-section>
        <div class="p-4 flex flex-col justify-end items-center">
          <q-select
            v-model="writeData"
            :label="$t('form.markerName')"
            outlined
            dense
            clearable
            :options="markerOptions"
            options-dense
            map-options
            emit-value
            use-input
            ref="markerSelect"
            @update:model-value="selectAlready"
            @filter="filterOptions">
          </q-select>
          <q-btn color="primary" class="mt-4 px-10" :disable="!writeData" @click="isWrite = true">{{ $t('userRole.btnContinue') }}</q-btn>
        </div>
      </q-card>
      <q-card v-else>
        <q-card-section class="bg-white text-white !py-[4px] flex">
          <q-space></q-space>
          <q-btn
            flat
            round
            dense
            color="black"
            size="md"
            icon="reply"
            @click="isWrite = false"
          />
          <q-btn
            flat
            v-close-popup
            round
            dense
            color="black"
            size="md"
            icon="close"
          />
        </q-card-section>
        <div class="flex-col px-2 pt-2 pb-12">
          <q-item class="flex flex-col items-center gap-1">
            <q-item-section>
              <q-avatar size="128px">
                <q-icon
                  name="mdi-nfc"
                  color="purple"
                ></q-icon>
              </q-avatar>
            </q-item-section>
            <q-item-section class="text-h6 flex flex-row">
              <span>{{$t('NFCPatrol.statWrite') }}</span>
              <span> {{ $t('NFCPatrol.nowWriteMarkerNo', { markerNo: writeDataMarkerNo }) }} </span>
            </q-item-section>
          </q-item>
        </div>
      </q-card>
  </q-dialog>
</template>

<script
  setup
  lang="ts"
>
  import { computed, effect, ref, shallowReactive, watch, nextTick } from 'vue'
  import { NFC_Tag, useAndroidNFC, readHexId, IV, KEY } from '@utils/vueUse/nfc'
  import { useDialogModal } from '@utils/vueUse/dialog'
  import { TimeMask, localTime, dayjsFormat, toUtcTime, DateMask, getDiffFromExpirationDate, getDiffMin, dayjs } from '@utils/dayjs'
  import { useStore } from 'vuex'
  import { GET_DATA, NS } from '@store/data/methodTypes'
  import { DataName } from '@src/store'
  import { bysdb } from '@ygen/bys.api'
  import { Store } from '@src/store'
  import { ICallOption } from 'jsyrpc'
  import { bysproto } from '@app/src/ygen/bys.api'
  import { yrpcmsg } from '@app/src/ygen/yrpcmsg'
  import log from '@utils/log'
  import { RpcBysApi } from '@ygen/bys.api.yrpc'
  import { isHaveMaintainPerm, checkIs4GMarker } from '@utils/common'
  import { Notify, QSelect } from 'quasar'
  import { decrypt } from '@utils/crypto'
  import { useI18n } from 'vue-i18n'
  import storage from '@utils/storage'
  import { BysMarker } from '@services/dataStore'

  interface NFCPatrolRecord {
    id: string
    readTime: string
    payload: string
    uploaded: boolean
    extra?: Record<string, any>,
    orgShortName?: string,
    uploading: boolean,
    // paramsWith4g?: Record<string, any>,
  }

  const store = useStore()
  const i18n = useI18n()

  let writeDataMarkerNo = ''
  const markerSelect = ref<InstanceType<typeof QSelect>>()
  const readRecords = ref<Array<NFCPatrolRecord>>([])
  // 正在上传的标识
  const isUploading = ref(false)
  const unUploadRecords = computed(() => {
    return readRecords.value.filter(item => item.uploaded === false)
  })
  const uploadAllRecords = computed(() => {
    return unUploadRecords.value.length === 0
  })
  // 写入NFC 界桩RID数组集合
  const filterMarkerNo = ref('')
  const markerOptions = computed(() => {
    const options = store.getters[`${NS}/${GET_DATA}`](DataName.BysMarker)
     .filter(m => checkIs4GMarker(m.MarkerType)).map(m => {
      return {
        label: m.MarkerNo,
        value: m.RID,
      }
    })
    // 根据输入内容过滤数据
    if (filterMarkerNo.value) {
      const needle = filterMarkerNo.value.toLowerCase()
      return options.filter(option => option.label.toLowerCase().includes(needle))
    }
    return options
  })
  // 与服务器是否连接
  const isConnected = computed(() => { return store.state.isConnected })
  const haveMaintainPerm = computed(() =>{ return isHaveMaintainPerm()})

  const { stream, isWrite, writeData, writeFinish, noNFC } = useAndroidNFC()
  const { decodePayload } = ndef.textHelper
  // 控制打开写入选择弹窗
  const writeNFCVisible = ref(false)

  function manualUploadRecord(record: NFCPatrolRecord) {
    uploadToServer(record)
      .then(() => {
        record.uploading = false
        record.uploaded = true
      })
      .catch(err => {
        log.error('手动上传记录失败:', err)
        record.uploading = false
        Notify.create({
          type: 'negative',
          message: i18n.t('message.uploadFailed')
        })
      })
  }

  // 将打卡记录上传到服务器
  function uploadToServer(record: NFCPatrolRecord): Promise<Record<string, any>> {
    return new Promise((resolve, reject) => {
      const NFCHistory: bysdb.IDbMarkerPatrolHistory = {
        NFCID: record.id,
        ActionTime: toUtcTime(record.readTime),
        MarkerHWID: record?.extra?.MarkerHWID,
        UserID: Store.state.UserRID,
        OrgRID: Store.state.UserOrgRID
      }
      const options: ICallOption = {
        OnResult: (res: bysproto.IBmsg, rpcCmd: yrpcmsg.Ymsg, meta?: yrpcmsg.IGrpcMeta) => {
          log.info('markerUploadNFCRecord result', res, rpcCmd, meta)
          resolve({
            record,
            isUpload: true
          })
          isUploading.value = false
        },
        OnServerErr: (errRpc: yrpcmsg.Ymsg) => {
          log.error('markerUploadNFCRecord server error', JSON.stringify(errRpc))
          reject({
            record,
            isUpload: false
          })
          isUploading.value = false
        },
        OnLocalErr: (err: any) => {
          log.error('markerUploadNFCRecord local error', err)
          reject({
            record,
            isUpload: false
          })
          isUploading.value = false
        },
        OnTimeout: (v: any) => {
          log.warn('markerUploadNFCRecord timeout', v)
          reject({
            record,
            isUpload: false
          })
          isUploading.value = false
        },
      }
      record.uploading = true
      isUploading.value = true
      RpcBysApi.MarkerUploadNFCRecord(NFCHistory, options)
    })
  }

  // 格式化到期日期
  function expirationDateFormat(time) {
    if (!time) return null
    return dayjsFormat(time, DateMask)
  }

  // 单位简称
  function getOrgShortName(marker): string {
    return store.getters[`${NS}/${GET_DATA}`](DataName.Unit, marker?.OrgRID)?.ShortName ?? ''
  }

  // 上传所有未成功上传的巡查打卡
  function uploadAll() {
    const uploadAllRecords = unUploadRecords.value.map(record => uploadToServer(record))
    Promise.allSettled(uploadAllRecords)
      .then(res => {
        const uploadFailRecords = res.filter(item => item.status === 'rejected')
        // 所有巡查都已上报
        if (uploadFailRecords.length === 0) {
          res.forEach(item => {
            // @ts-ignore
            item.value.record.uploading = false
            // @ts-ignore
            item.value.record.uploaded = true
          })
          return
        }
        // 遍历出上报成功和失败的
        res.forEach(item => {
          if (item.status === 'fulfilled') {
            item.value.record.uploading = false,
            item.value.record.uploaded = true
          } else {
            item.reason.record.uploading = false,
            item.reason.record.uploaded = false
          }
        })
      })
      .catch(err => {
        log.error('批量上传记录失败:', err)
        Notify.create({
          type: 'negative',
          message: i18n.t('message.uploadFailed')
        })
      })
  }

  // NFC写入弹窗隐藏
  function hideDialog() {
    writeNFCVisible.value = false
    writeData.value = ''
    isWrite.value = false
  }

  // 将最后一条打卡记录保存在本地
  function saveLastPatrol(record: NFCPatrolRecord) {
    storage.save('lastNFCPatrol', record)
  }

  function filterOptions(val: string, update: Function) {
    filterMarkerNo.value = val
    update()
  }

  function selectAlready(val) {
    nextTick(() => {
      writeDataMarkerNo = BysMarker.getData(val)?.MarkerNo ?? ''
      markerSelect.value?.blur()
    })
  }

  // 当服务器重新连接时，上传全部未上传的打卡记录
  watch(isConnected, (val) => {
    if (val === true && !uploadAllRecords.value) {
      const uploadHandler = () => {
        uploadAll()
      }
      // 判断 isUploading 是否为 false，为 false 则执行上传操作
      if (!isUploading.value) {
        setTimeout(uploadHandler, 100)
      } else {
        // 如果 isUploading 为 true，则等待其变为 false 后再执行上传操作
        const checkIsUploading = () => {
          if (!isUploading.value) {
            uploadHandler()
          } else {
            setTimeout(checkIsUploading, 100) // 继续等待
          }
        }
        checkIsUploading()
      }
    }
  })

  effect(() => {
    stream.value.then(async (data: NFC_Tag) => {
      const msg = data.ndefMessage[0]
      const payload = decodePayload(msg.payload).trim()
      const rid = decrypt(payload, KEY, IV)
      const dbMarker = store.getters[`${NS}/${GET_DATA}`](DataName.BysMarker, rid)
      const timeLeft = getDiffFromExpirationDate(dbMarker.ExpirationDate)
      const marker = Object.assign( { timeLeft: timeLeft > 10 ? '' : timeLeft }, dbMarker)
      // let paramsWith4g = {}
      // try {
      //   paramsWith4g = JSON.parse(marker.MarkerSettings)
      // } catch (error) {
      //   log.info('json parse error', error)
      // }
      const record: NFCPatrolRecord = shallowReactive({
        id: readHexId(data.id, ''),
        readTime: localTime(),
        payload,
        uploaded: false,
        extra: marker,
        uploading: false,
        orgShortName: getOrgShortName(marker),
        // paramsWith4g,
      })
      const lastNFCPatrol = storage.fetch('lastNFCPatrol')
      if (lastNFCPatrol?.id === record.id) {
        const diff = getDiffMin(lastNFCPatrol.readTime, dayjs.utc(record.readTime))
        if (diff <= 3) {
          Notify.create({
            message: i18n.t('NFCPatrol.repeatPatrolTip'),
            icon: 'warning',
            type: 'warning'
          })
          return
        }
      }
      readRecords.value?.push(record)
      // 自动上传到服务器上
      const uploadInfo = await uploadToServer(record)
      record.uploading = false
      if (uploadInfo.isUpload) {
        record.uploaded = true
      }
      // 存在未上传的 在一次上传成功后，也全部上传
      if (!uploadAllRecords.value) {
        uploadAll()
      }
      saveLastPatrol(record)
    })
    .catch(err => {
      log.error('读取NFC标签失败:', err)
    })
  })
  effect(() => {
    writeFinish.value.then((bool: boolean) => {
      writeNFCVisible.value = false
      if (bool) {
        Notify.create({
          type: 'positive',
          message: i18n.t('NFCPatrol.writeSuccess')
        })
      }
    })
    .catch(err => {
      log.error('写入NFC标签失败:', err)
    })
  })

  const { visible, dialogModal } = useDialogModal()
</script>

<style lang="scss">
  .triangle{
    position: absolute;
    right: 0;
    top: 0;
    width: 0;
    height: 0;
    border-bottom: 36px solid transparent;
    border-right: 36px solid $red;

    .triangle-upload-tag{
      // position: absolute;
      font-size: 18px;
      color: #fff;
      left: 16px;
      top: 0;
    }
  }
  .triangle-positive {
    border-right: 36px solid $positive;
  }
  .time-left {
    position: relative;
    padding: 2px 6px;
    border-radius: 5px;
  }
  .time-left-red {
    color: #fff;
    background-color: $negative;
  }
  .time-left-orange {
    color: #fff;
    background-color: $warning;
  }

  .upload-all {
    margin-right: 6px;

    .q-btn__content .q-badge {
      right: 0px;
      font-size: 10px;
      margin-top: 2px;
      padding: 2px 4px;
    }
  }
  .NFC-patrol-write-modal {
    .q-card__section.bys-move-page-container {
      min-height: 150px !important;
    }
  }
</style>

<template>
  <modal
    v-model="visible"
    v-model:maximized="maximized"
    :content-class="'controller-edit-dlg'"
    :supportMaximize="false"
    :dblClick="false"
    :max-height="'70vh'"
    ref="modal"
  >
    <template v-slot:header><span>{{ title }}</span></template>
    <q-form ref="editorForm">
      <div class="controller-edit-dlg-cls">
        <controller-edit
          v-model="controllerData"
          class="controller-edit-dlg-form"
        />
        <div class="text-center controller-edit-dlg-btn">
          <q-btn
            class="bg-primary text-white button-letter w-48"
            :label="$t('form.submit')"
            :disable="!canEdit"
            @click="updateControllerData"
          />
        </div>
      </div>
    </q-form>
  </modal>
</template>

<script lang="ts">
  import { defineComponent } from 'vue'
  import ControllerEdit from '@src/components/controllerEdit/ControllerEdit.vue'
  import dialogMixin from '@utils/mixins/dialog'
  import { DbName } from '@utils/permission'
  import PermissionMixin from '@utils/mixins/permission'
  import controllerMixin from '@utils/mixins/controller'
  import { updateControllerStationDeviceNoRelation } from '@services/controller'

  export default defineComponent({
    name: 'ControllerQuickEdit',
    mixins: [dialogMixin, PermissionMixin, controllerMixin],
    data() {
      return {
        isVisible: false,
        controllerData: {},
      }
    },
    methods: {
      async updateControllerData() {
        const valid = await this.$refs.editorForm.validate()
        if (!valid) { return }

        const res = await this.warningControllerNoChange(this.controllerData)
        if (!res) { return }

        return this.updateData(this.controllerData)
          .then(() => {
            this.$q.notify({
              message: this.$t('message.updateSuccess') as string,
              color: 'positive',
              icon: 'check_circle',
            })
            this.isVisible = false
            updateControllerStationDeviceNoRelation()
          })
          .catch((reason: string) => {
            this.$q.notify({
              message: reason,
              color: 'red',
              icon: 'error',
              position: 'top',
            })
          })
      },
    },
    components: {
      ControllerEdit,
    },
    computed: {
      dbName() {
        return DbName.DbController
      },
      title() {
        return `${this.$t('menus.controller')}/${this.$t('form.edit')}`
      },
    },
  })
</script>

<style lang="scss">
  .controller-edit-dlg:not(.maximized) {
    display: flex;
    flex-direction: column;
  }

  .controller-edit-dlg,
  .controller-edit-dlg .controller-edit-dlg-cls {
    width: fit-content !important;
    max-width: unset !important;
  }

  .controller-edit-dlg-cls {
    overflow: auto;
    display: flex;
    flex-direction: column;
  }

  .controller-edit-dlg-cls .controller-edit-dlg-form {
    flex: auto;
    overflow: auto;
  }

  .controller-edit-dlg-cls .controller-edit-dlg-btn {
    flex: none;
    margin-top: 10px;
  }

  /*.controller-edit-dlg-btn {*/
  /*  position: sticky;*/
  /*  bottom: 0;*/
  /*}*/

  .controller-edit-dlg .bys-move-header-wrapper {
    height: 32px;
    background-color: #027BE3;
    z-index: 99;

    .bys-move-header {
      position: fixed;
      width: 100%;
    }
  }
</style>

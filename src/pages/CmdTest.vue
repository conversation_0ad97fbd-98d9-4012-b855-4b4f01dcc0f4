<template>
  <modal
    v-model="visible"
    v-model:maximized="maximized"
    :contentHeight="'auto'"
    contentClass="log-modal"
    ref="cmdTest"
  >
    <template #header>
      <span v-text="title"></span>
    </template>
    <div class="test-log-panel">
      <div class="row full-width test-log-panel-select-panel">
        <div class="test-log-panel-select-input">
          <q-input
            class="full-width"
            v-model="filterInput"
            outlined
            dense
            clearable
          >
            <template v-slot:append>
              <q-icon name="search" />
            </template>
          </q-input>
        </div>
        <q-space />
        <q-btn
          class="clear-log-btn"
          icon="delete"
          dense
          round
          color="negative"
          @click="logList = []"
        >
          <q-tooltip>
            {{ $t('CmdTest.trash') }}
          </q-tooltip>
        </q-btn>
      </div>
      <q-virtual-scroll
        class="test-log-panel-content"
        :items="allLogData"
        ref="virtualScroll"
        @virtual-scroll="onVirtualScroll"
        separator
      >
        <template v-slot="{ item, index }">
          <q-item
            class="q-py-xs q-px-none"
            :key="index"
            dense
          >
            <template v-if="item.tmp">
              <p>{{ item.argv.join(', ') }}</p>
            </template>
            <q-item-section
              v-else
              avatar
              class="q-pr-sm full-width"
            >
              <div class="row q-mb-sm full-width">
                <q-chip
                  :color="titleColor[item.color].color"
                  :text-color="titleColor[item.color]['text-color']"
                  :label="item.title"
                  dense
                  class="col-auto"
                />
                <q-space />
                <q-item-label class="col-auto self-center">
                  {{ item.time }}
                </q-item-label>
              </div>
              <q-item-label> #{{ index }} - {{ item.label }}</q-item-label>
              <q-img
                v-if="!!item.url"
                :src="item.url"
                :ratio="16/9"
                spinner-color="primary"
                fit="contain"
                class="cursor-pointer w-48 q-my-sm"
                @click.stop.prevent="previewImageWithLightbox(item.url)"
              >
                <q-tooltip>
                  {{ $t('historyTable.clickToViewFullImage') }}
                </q-tooltip>
              </q-img>
            </q-item-section>
          </q-item>
        </template>
      </q-virtual-scroll>
    </div>
    <teleport to="body">
      <lightbox
        class="upload-image-preview-lightbox"
        :visible="visibleLightbox"
        :imgs="lightboxUrl"
        @hide="onHideLightbox"
      >
      </lightbox>
    </teleport>
  </modal>
</template>

<script lang="ts">
  import dialogMixin from '@utils/mixins/dialog'
  import { StrPubSub } from 'ypubsub'
  import {
    BysMarkerDumpingAlarm,
    ControllerAlarm,
    ControllerHeartbeat,
    ControllerOffline,
    ControllerOfflinePanic,
    ControllerOnline,
    DeviceAlarmTest,
    DeviceReportInfo,
    MarkerPhotographUpload,
    NotifyICCIDAlreadyExist,
    NotifyICCIDAndIMEIConflict,
    RemoveBysMarkerAlarm,
  } from '@utils/pubSubSubject'
  import { bysdb } from '@ygen/bysdb'
  import { bysproto } from '@ygen/controller'
  import { localTime, wrapperInvalidDate } from '@utils/dayjs'
  import { appLang } from '@src/config'
  import { i18n } from '@boot/i18n'
  import { Controller } from '@services/dataStore'
  import {
    calcControllerPower,
    calcMarkerPower, checkIs4GMarker,
    CmdCode,
    ControllerDeviceType,
    ControllerLowPower,
    decode4gMarkerAlarmState,
    decode4gMarkerInfoReportState,
    exNetworkType,
    MarkerType,
  } from '@utils/common'
  import dayjs from 'dayjs'
  import { GET_DATA_BY_INDEX, NS } from '@store/data/methodTypes'
  import { DataName } from '@src/store'
  import { BysMarkerAndUpdateInfo, ControllerAndUpdateCmd, DeviceUpdateInfo, ICustData } from '@utils/bysdb.type'
  import { QVirtualScroll } from 'quasar'
  import { checkUpdateCmdTimeIsSync, getControllerByChannelNo } from '@services/controller'
  import { defineComponent } from 'vue'
  import Lightbox from 'vue-easy-lightbox';

  enum LogTypePrefix {
    Controller = 'C',
    BysMarker = 'M'
  }

  enum LogTypeColor {
    Online = 'online',
    Offline = 'offline',
    OfflinePanic = 'offlinePanic',
    HeartBeat = 'heartBeat',
    // 此处已存在相同的枚举值
    // ControllerAlarm = 'devAlarm',
    DevReport = 'devReport',
    DevAlarm = 'devAlarm',
    DevAlarmTest = 'devAlarmTest',
    DevRemoveAlarmTest = 'devRemoveAlarmTest',
    DevPhotoUpload = 'devPhotoUpload',
    ICCIDAndIMEIRepeat = 'ICCIDAndIMEIRepeat'
  }

  const CamImageType = {
    1: i18n.global.t('historyTable.sensorCapture'),
    2: i18n.global.t('historyTable.alarmCapture'),
    3: i18n.global.t('historyTable.timerCapture'),
    4: i18n.global.t('historyTable.manualCapture'),
  }

  export default defineComponent({
    name: 'CmdTest',
    mixins: [dialogMixin],
    data() {
      return {
        showSelectPanel: false,
        logList: [],
        titleColor: {
          online: {
            'color': 'info',
            'text-color': 'white',
          },
          offline: {
            'color': 'grey-8',
            'text-color': 'white',
          },
          offlinePanic: {
            'color': 'negative',
            'text-color': 'white',
          },
          heartBeat: {
            'color': 'info',
            'text-color': 'deep-orange-4',
          },
          devReport: {
            'color': 'positive',
            'text-color': 'white',
          },
          devAlarm: {
            'color': 'negative',
            'text-color': 'white',
          },
          devAlarmTest: {
            'color': 'negative',
            'text-color': 'white',
          },
          devRemoveAlarmTest: {
            'color': 'info',
            'text-color': 'white',
          },
          devPhotoUpload: {
            'color': 'light-green-5',
            'text-color': 'white',
          },
          ICCIDAndIMEIRepeat: {
            'color': 'orange-5',
            'text-color': 'white',
          },
        },
        /*//是否选中了界桩/控制器
        isSelectHardware: false,
        isMarker: true,
        isController: false,
        //是否选中了二级列表
        isSelectSecond: false,
        secondSelect: '',*/

        filterInput: '',
        isForceStickyLastItem: true,

        // 图片预览显示控制
        visibleLightbox: false,
        lightboxUrl: '',
      }
    },
    components: { Lightbox },
    methods: {
      getLogLabel(data: any[]) {
        return data.join(this.isEnUs ? ', ' : '，')
      },
      getJudgeRet(data) {
        return data ? this.$t('common.yes') : this.$t('common.no')
      },
      isCheckOk(data) {
        return data === 0 ? this.$t('common.success') : this.$t('common.failed')
      },
      geolocation(...argv: any[]) {
        this.appendLog({
          tmp: true,
          argv: [
            localTime(),
            ...argv,
          ],
        })
      },
      // 时间全部转化为本地时间
      addControllerOnlineNote(controller: ControllerAndUpdateCmd | bysdb.IDbBysMarker, loginReq: bysproto.IBloginReq) {
        if (loginReq.DeviceType === ControllerDeviceType.Net4g) {
          const dbMarker = controller as bysdb.IDbBysMarker
          const list: string[] = [
            `${this.label4GMarker[dbMarker.MarkerType as MarkerType]} ${this.$t('CmdTest.online')}`,
            `${this.$t('form.markerName')} ${dbMarker.MarkerNo}`,
            `${this.$t('CmdTest.HWID')} ${dbMarker.MarkerHWID}`,
            `${this.$t('CmdTest.loginTimeStr')} ${wrapperInvalidDate(loginReq.LoginTimeStr as string)}`,
            `${this.$t('CmdTest.batteryPower')} ${calcControllerPower((loginReq.Power ?? 0) / 10)}`,
            `IMEI ${loginReq.IMEI}`,
            `ICCID ${loginReq.ICCID}`,
          ]

          this.appendLog({
            No: dbMarker.MarkerNo,
            HWID: dbMarker.MarkerHWID,
            title: LogTypePrefix.BysMarker + this.$t('CmdTest.online'),
            color: LogTypeColor.Online,
            time: localTime(),
            label: this.getLogLabel(list),
          })
          return
        }

        const data = controller as ControllerAndUpdateCmd
        let temArray = [
          `${this.$t('CmdTest.controller')} ${this.$t('CmdTest.online')}`,
          `${this.$t('CmdTest.NO')} ${data.ControllerNo}`,
          `${this.$t('CmdTest.HWID')} ${data.ControllerHWID}`,
          `${this.$t('CmdTest.loginTimeStr')} ${wrapperInvalidDate(loginReq.LoginTimeStr as string)}`,
          // 刚登录时,是没有data.controllerState?.StationDeviceNo字段属性
          // `${data.controllerState?.StationDeviceNo === 0? this.$t('CmdTest.stationDeviceNoBaseStation'): this.$t(
          //   'CmdTest.stationDeviceNoRepeater')}${data.controllerState?.StationDeviceNo}`,
          `${this.$t('CmdTest.Channel')} ${data.ChannelCount}`,
          `${this.$t('CmdTest.networkType')} ${exNetworkType[loginReq.NetworkType as number]?.() ?? loginReq.NetworkType}`,
          `${this.$t('CmdTest.TransferChannelNos')} [${this.getTransferNoStr(data.controllerState?.TransferChannelNos)}]`,
          `${this.$t('CmdTest.batteryPower')} ${calcControllerPower(data.controllerState?.Power ?? 0)}`,
        ]
        if ((data.controllerState?.Power ?? 0) <= ControllerLowPower) {
          temArray.push(
            `${this.$t('CmdTest.lowerPower')}`,
          )
        }
        this.appendLog({
          No: data.ControllerNo,
          HWID: data.ControllerHWID,
          title: LogTypePrefix.Controller + this.$t('CmdTest.online'),
          color: LogTypeColor.Online,
          time: localTime(),
          label: this.getLogLabel(temArray),
        })
      },
      addControllerOfflineNote(cmdData: ControllerAndUpdateCmd | bysdb.IDbBysMarker, loginReq: bysproto.IBloginReq) {
        // 判断是否为4g界桩下线
        if (loginReq.DeviceType === ControllerDeviceType.Net4g) {
          const dbMarker = cmdData as bysdb.IDbBysMarker
          let temArray = [
            `${this.label4GMarker[dbMarker.MarkerType as MarkerType]} ${this.$t('CmdTest.offline')}`,
            `${this.$t('form.markerName')} ${dbMarker.MarkerNo}`,
            `${this.$t('CmdTest.HWID')} ${dbMarker.MarkerHWID}`,
            `IMEI ${dbMarker.IMEI}`,
            `ICCID ${dbMarker.ICCID}`,
          ]
          this.appendLog({
            No: dbMarker.MarkerNo,
            HWID: dbMarker.MarkerHWID,
            title: LogTypePrefix.Controller + this.$t('CmdTest.offline'),
            color: LogTypeColor.Offline,
            time: localTime(),
            label: this.getLogLabel(temArray),
          })
          return
        }

        const data = cmdData as ControllerAndUpdateCmd
        let temArray = [
          `${this.$t('CmdTest.controller')} ${this.$t('CmdTest.offline')}`,
          `${this.$t('CmdTest.NO')} ${data.ControllerNo}`,
          `${this.$t('CmdTest.HWID')} ${data.ControllerHWID}`,
          `${this.$t('CmdTest.type')} ${this.ControllerType[data.ControllerType as number]}`,
          `${this.$t('CmdTest.Channel')} ${data.ChannelCount}`,
          `${this.$t('CmdTest.ConnTime')} ${wrapperInvalidDate(data.controllerState?.ConnectTime ?? '')}`,
        ]
        this.appendLog({
          No: data.ControllerNo,
          HWID: data.ControllerHWID,
          title: LogTypePrefix.Controller + this.$t('CmdTest.offline'),
          color: LogTypeColor.Offline,
          time: localTime(),
          label: this.getLogLabel(temArray),
        })
      },
      addControllerOfflinePanicNode(data: bysdb.IDbController) {
        let labArray = [
          `${this.$t('CmdTest.controller')} ${this.$t('CmdTest.offlinePanic')}`,
          `${this.$t('CmdTest.NO')} ${data.ControllerNo}`,
          `${this.$t('CmdTest.HWID')} ${data.ControllerHWID}`,
        ]
        this.appendLog({
          No: data.ControllerNo,
          HWID: data.ControllerHWID,
          title: LogTypePrefix.Controller + this.$t('CmdTest.offlinePanic'),
          color: LogTypeColor.OfflinePanic,
          time: localTime(),
          label: this.getLogLabel(labArray),
        })
      },
      getTransferNoStr(transferNoArray: number[] | null | undefined) {
        if (!transferNoArray) {
          return ''
        }
        const transferNoLen: number = transferNoArray.length
        let transferNoLenArray: Array<string> = []
        for (let i = 0; i < transferNoLen; i += 2) {
          transferNoLenArray.push(`${transferNoArray[i]}:${transferNoArray[i + 1]}`)
        }

        return transferNoLenArray.join(' , ')
      },
      addControllerHeartbeatNode(data: ControllerAndUpdateCmd) {
        let temArray = [
          `${this.$t('CmdTest.controller')} ${this.$t('CmdTest.heartbeat')}`,
          `${this.$t('CmdTest.NO')} ${data.ControllerNo}`,
          `${this.$t('CmdTest.HWID')} ${data.ControllerHWID}`,
          `${this.$t('CmdTest.type')} ${this.ControllerType[data.ControllerType as number]}`,
          `${data.controllerState?.StationDeviceNo === 0 ? this.$t('CmdTest.stationDeviceNoBaseStation') : this.$t(
            'CmdTest.stationDeviceNoRepeater')}${data.controllerState?.StationDeviceNo}`,
          `${this.$t('CmdTest.networkType')} ${exNetworkType[data.controllerState?.NetworkType ?? 0]?.() ?? data.controllerState?.NetworkType}`,
          `${this.$t('CmdTest.Channel')} ${data.controllerState?.ChannelNo}`,
          `${this.$t('CmdTest.TransferChannelNos')} [${this.getTransferNoStr(
            data.controllerState?.TransferChannelNos)}]`,
          `${this.$t('CmdTest.batteryPower')} ${calcControllerPower(data.controllerState?.Power ?? 0)}`,
        ]
        if ((data.controllerState?.Power ?? 0) <= ControllerLowPower) {
          temArray.push(
            `${this.$t('CmdTest.lowerPower')}`,
          )
        }
        this.appendLog({
          No: data.ControllerNo,
          HWID: data.ControllerHWID,
          title: LogTypePrefix.Controller + this.$t('CmdTest.heartbeat'),
          color: LogTypeColor.HeartBeat,
          time: localTime(),
          label: this.getLogLabel(temArray),
        })
      },
      addControllerAlarmNode(data: ControllerAndUpdateCmd) {
        let temArray = [
          `${this.$t('CmdTest.controller')} ${this.$t('CmdTest.alarm')}`,
          `${this.$t('CmdTest.NO')} ${data.ControllerNo}`,
          `${this.$t('CmdTest.HWID')} ${data.ControllerHWID}`,
          `${this.$t('CmdTest.type')} ${this.ControllerType[data.ControllerType as number]}`,
          `${data.controllerState?.StationDeviceNo === 0 ? this.$t('CmdTest.stationDeviceNoBaseStation') : this.$t(
            'CmdTest.stationDeviceNoRepeater')}${data.controllerState?.StationDeviceNo}`,
          `${this.$t('CmdTest.networkType')} ${exNetworkType[data.controllerState?.NetworkType ?? 0]?.() ?? data.controllerState?.NetworkType}`,
          `${this.$t('CmdTest.Channel')} ${data.controllerState?.ChannelNo}`,
          `${this.$t('CmdTest.TransferChannelNos')} [${this.getTransferNoStr(
            data.controllerState?.TransferChannelNos)}]`,
          `${this.$t('CmdTest.batteryPower')} ${calcControllerPower(data.controllerState?.Power ?? 0)}`,
        ]
        if ((data.controllerState?.Power ?? 0) <= ControllerLowPower) {
          temArray.push(
            `${this.$t('CmdTest.lowerPower')}`,
          )
        }

        if (data.controllerState?.status?.timeError) {
          temArray.push(
            `${this.$t('controllerErrReason.timeError')}`,
          )
        }

        this.appendLog({
          No: data.ControllerNo,
          HWID: data.ControllerHWID,
          title: LogTypePrefix.Controller + this.$t('CmdTest.alarm'),
          color: LogTypeColor.DevAlarm,
          time: localTime(),
          label: this.getLogLabel(temArray),
        })
      },
      addBysMarkerReportInfoNode(data: BysMarkerAndUpdateInfo, deviceUpdate: DeviceUpdateInfo | bysproto.IBInfoReporting) {
        if (checkIs4GMarker(data.MarkerType)) {
          deviceUpdate = deviceUpdate as bysproto.IBInfoReporting
          const reportLabels = {
            1: this.$t('CmdTest.reportOnStartup'),
            2: this.$t('CmdTest.reportOnDebugging'),
            3: this.$t('CmdTest.reportOnRegular')
          }
          const stateMode = decode4gMarkerInfoReportState(deviceUpdate.state)
          const labelList: string[] = [
            `${this.label4GMarker[data.MarkerType as MarkerType]} ${this.$t('CmdTest.report')}(${reportLabels[deviceUpdate.type]})`,
            `${this.$t('CmdTest.Cmd')} ${CmdCode.InfoReport}`,
            `${this.$t('form.markerName')} ${data.MarkerNo}`,
            `${this.$t('CmdTest.deviceID')} ${deviceUpdate.deviceID}`,
            `${this.$t('CmdTest.AlarmTime')} ${wrapperInvalidDate(deviceUpdate.time as string)}`,

            `ICCID ${deviceUpdate.iccid}`,
            `${this.$t('CmdTest.softwareVersion')} ${deviceUpdate.softwareVersion}`,
            `${this.$t('CmdTest.paramTime')} ${wrapperInvalidDate(deviceUpdate.dataVersion as string)}`,
            `${this.$t('CmdTest.deviceFieldStrength')} ${deviceUpdate.signal}dbm`,
            `${this.$t('CmdTest.temperature')} ${deviceUpdate.temp?.toFixed(1) ?? 0}°`,
            `${this.$t('CmdTest.humidity')} ${deviceUpdate.rh?.toFixed(1) ?? 0}%`,
            `${this.$t('CmdTest.batteryPower')} ${(deviceUpdate.battery / 1000).toFixed(1)}v`,
            `${this.$t('CmdTest.status')} ${[deviceUpdate.state, deviceUpdate.alarmstate].toString()}`,
          ]
          // 只显示异常的状态文本
          if (stateMode.battery) {
            labelList.push(this.$t('CmdTest.batteryStatus', { status: this.$t('CmdTest.abnormal') }))
          }
          if (stateMode.rtcClock) {
            labelList.push(this.$t('CmdTest.rtcClockStatus', { status: this.$t('CmdTest.abnormal') }))
          }
          if (stateMode.gpsModule) {
            labelList.push(this.$t('CmdTest.gpsModuleStatus', { status: this.$t('CmdTest.abnormal') }))
          }
          if (stateMode.alarmLock) {
            labelList.push(this.$t('CmdTest.alarmLock'))
          }
          if (stateMode.axis3Sensor) {
            labelList.push(this.$t('CmdTest.threeAxisSensorStatus', { status: this.$t('CmdTest.abnormal') }))
          }
          if (stateMode.camera) {
            labelList.push(this.$t('CmdTest.camera', { status: this.$t('CmdTest.abnormal') }))
          }
          if (stateMode.infraredProbe) {
            labelList.push(this.$t('CmdTest.infraredProbe', { status: this.$t('CmdTest.abnormal') }))
          }

          const alarmState = decode4gMarkerAlarmState(deviceUpdate.alarmstate)
          const alarmStateLabels: string[] = []
          if (alarmState.displacementAlarm) {
            alarmStateLabels.push(this.$t('CmdTest.displacementAlarm'))
          }
          if (alarmState.vibrationAlarm) {
            alarmStateLabels.push(this.$t('CmdTest.vibrationAlarm'))
          }
          if (alarmState.tiltAlarm) {
            alarmStateLabels.push(this.$t('CmdTest.tiltAlarm'))
          }
          if (alarmState.infraredAlarm) {
            alarmStateLabels.push(this.$t('CmdTest.infraredAlarm'))
          }
          labelList.push(alarmStateLabels.toString())

          let markerDataVersion = ''
          try {
            const MarkerSettings = JSON.parse(data.MarkerSettings ?? '{}') as bysproto.IBDataUpdate
            markerDataVersion = MarkerSettings.dataVersion ?? markerDataVersion
            // eslint-disable-next-line no-empty
          } catch (error) {
          }
          if (!markerDataVersion || dayjs(deviceUpdate.dataVersion as string).isBefore(markerDataVersion as string)) {
            labelList.push(`${i18n.global.t('CmdTest.markerParamUnSync')}`)
          } else {
            labelList.push(`${i18n.global.t('CmdTest.markerParamIsSync')}`)
          }

          this.appendLog({
            No: data.MarkerNo,
            HWID: data.MarkerHWID,
            title: LogTypePrefix.BysMarker + this.$t('CmdTest.report'),
            color: LogTypeColor.DevReport,
            time: localTime(),
            label: this.getLogLabel(labelList),
          })
          return
        }

        deviceUpdate = deviceUpdate as DeviceUpdateInfo
        const controller: bysdb.IDbController | undefined = Controller.getDataByIndex(deviceUpdate.StationID + '')
        const StatusInfo = deviceUpdate.StatusInfo ?? {}
        const relayController = getControllerByChannelNo(deviceUpdate.StationID ?? 0, deviceUpdate.StationDeviceNo ?? 0)
        const temArray = [
          `${this.$t('CmdTest.bysMarker')} ${this.$t('CmdTest.report')}`,
          `${this.$t('CmdTest.Cmd')} ${deviceUpdate.Cmd}`,
          `${this.$t('CmdTest.NO')} ${data.MarkerNo}`,
          // `${this.$t('CmdTest.HWID')} ${data.MarkerHWID}`,
          `${this.$t('CmdTest.deviceID')} ${deviceUpdate.DeviceID}`,
          `${this.$t('CmdTest.type')} ${this.ControllerType[controller?.ControllerType as number]}`,
          `${this.$t('CmdTest.stationID')} ${deviceUpdate.StationID}/${this.realControllerName(
            deviceUpdate.StationID)}`,
        ]
        if (relayController) {
          temArray.push(
            `${this.$t('CmdTest.receivingRelay')} ${relayController.ControllerHWID}/${relayController.ControllerNo}`)
        }
        const label2 = [
          `${this.$t('CmdTest.stationDeviceNoBaseStation')} ${deviceUpdate.StationDeviceNo}`,
          `${this.$t('CmdTest.Channel')} ${deviceUpdate.DeviceChannelNo}`,
          `${this.$t('CmdTest.deviceFieldStrength')} ${deviceUpdate.DeviceFieldStrength}dBm`,
          `${this.$t('CmdTest.systemPassCheckOK')} ${this.isCheckOk(deviceUpdate.SystemPassCheckOK)}`,
          `${this.$t('CmdTest.status')} ${deviceUpdate.Status}`,
          `${calcMarkerPower(StatusInfo.batteryPower as string)}`,
          //`${this.$t('CmdTest.isAlarm')} ${this.getJudgeRet(StatusInfo.isAlarm)}`,
          `${this.$t('CmdTest.isDumping')} ${this.getJudgeRet(StatusInfo.isDumping)}`,
          `${this.$t('CmdTest.isLowPowerAlarm')} ${this.getJudgeRet(StatusInfo.isLowPowerAlarm)}`,
          `${this.$t('CmdTest.isRegister')} ${this.getJudgeRet(StatusInfo.isRegister)}`,
          `${this.$t('CmdTest.isTest')} ${this.getJudgeRet(StatusInfo.isTest)}`,
          `${this.$t('CmdTest.isValidPosition')} ${this.getJudgeRet(StatusInfo.isValidPosition)}`,
          `${this.$t('CmdTest.paramTime')} ${wrapperInvalidDate(deviceUpdate.ParamTime as string)}`,
          `${this.$t('CmdTest.AlarmTime')} ${wrapperInvalidDate(deviceUpdate.CmdTime as string)}`,
        ]

        this.appendLog({
          No: data.MarkerNo,
          HWID: data.MarkerHWID,
          title: LogTypePrefix.BysMarker + this.$t('CmdTest.report'),
          color: LogTypeColor.DevReport,
          time: localTime(),
          label: this.getLogLabel(temArray.concat(label2)),
        })
      },
      addBysMarkerAlarmNode(data: BysMarkerAndUpdateInfo, deviceUpdate: DeviceUpdateInfo | bysproto.IBAlarmReporting) {
        if (checkIs4GMarker(data.MarkerType)) {
          deviceUpdate = deviceUpdate as bysproto.IBAlarmReporting
          const reportLabels = {
            1: this.$t('CmdTest.reportOnStartup'),
            2: this.$t('CmdTest.reportOnDebugging'),
            3: this.$t('CmdTest.reportToAlarm')
          }
          const labelList: string[] = [
            `${this.label4GMarker[data.MarkerType as MarkerType]} ${this.$t('CmdTest.alarm')}(${reportLabels[deviceUpdate.type]})`,
            `${this.$t('form.markerName')} ${data.MarkerNo}`,
            `${this.$t('CmdTest.deviceID')} ${deviceUpdate.deviceID}`,
          ]
          // 定位
          if (deviceUpdate.locate) {
            const { Lon, Lat, Height } = deviceUpdate.locate
            labelList.push(`${this.$t('CmdTest.positioningInfo')}(${this.$t('CmdTest.lng')}: ${Lon}, ${this.$t('CmdTest.lat')}: ${Lat}, ${this.$t('CmdTest.altitude')}: ${Height}m)`)
          }
          // 三轴报警
          const alarmState = decode4gMarkerAlarmState(deviceUpdate.alarmstate)
          const alarmStateLabels: string[] = []
          if (alarmState.displacementAlarm) {
            alarmStateLabels.push(this.$t('CmdTest.displacementAlarm'))
          }
          if (alarmState.vibrationAlarm) {
            alarmStateLabels.push(this.$t('CmdTest.vibrationAlarm'))
          }
          if (alarmState.tiltAlarm) {
            alarmStateLabels.push(this.$t('CmdTest.tiltAlarm'))
          }
          if (alarmState.infraredAlarm) {
            alarmStateLabels.push(this.$t('CmdTest.infraredAlarm'))
          }
          labelList.push(this.$t('CmdTest.threeAxisSensorStatus', { status: alarmStateLabels.toString() }))
          // 倾斜角度
          labelList.push(this.$t('CmdTest.tiltAngleVal', { value: deviceUpdate.attitude }))
          // 震动信息
          if (deviceUpdate.vibration) {
            const { amplitude, duration } = deviceUpdate.vibration
            labelList.push(this.$t('CmdTest.vibrationAmplitudeVal', { value: amplitude }))
            labelList.push(this.$t('CmdTest.vibrationDurationVal', { value: duration }))
          }
          if (alarmState.infraredAlarm) {
            const { infrared } = deviceUpdate
            labelList.push(`${this.$t('form.infrared')}: ${infrared}`)
          }

          this.appendLog({
            No: data.MarkerNo,
            HWID: data.MarkerHWID,
            title: LogTypePrefix.BysMarker + this.$t('CmdTest.alarm'),
            color: LogTypeColor.DevAlarm,
            time: localTime(),
            label: this.getLogLabel(labelList),
          })
          return
        }

        deviceUpdate = deviceUpdate as DeviceUpdateInfo
        const controller: bysdb.IDbController | undefined = Controller.getDataByIndex(deviceUpdate.StationID + '')
        const StatusInfo = deviceUpdate.StatusInfo ?? {}
        const GPS = deviceUpdate.GPS ?? {}
        let power = calcMarkerPower(StatusInfo.batteryPower as string)
        const relayController = getControllerByChannelNo(deviceUpdate.StationID ?? 0, deviceUpdate.StationDeviceNo ?? 0)

        const labArray = [
          `${this.$t('CmdTest.bysMarker')} ${this.$t('CmdTest.alarm')}`,
          `${this.$t('CmdTest.Cmd')} ${deviceUpdate.Cmd}`,
          `${this.$t('CmdTest.NO')} ${data.MarkerNo}`,
          // `${this.$t('CmdTest.HWID')} ${data.MarkerHWID}`,
          `${this.$t('CmdTest.deviceID')} ${deviceUpdate.DeviceID}`,
          `${this.$t('CmdTest.type')} ${this.ControllerType[controller?.ControllerType as number]}`,
          `${this.$t('CmdTest.stationID')} ${deviceUpdate.StationID}/${this.realControllerName(
            deviceUpdate.StationID)}`,
        ]
        if (relayController) {
          labArray.push(
            `${this.$t('CmdTest.receivingRelay')} ${relayController.ControllerHWID}/${relayController.ControllerNo}`)
        }
        const label2 = [
          `${this.$t('CmdTest.stationDeviceNoBaseStation')} ${deviceUpdate.StationDeviceNo}`,
          `${this.$t('CmdTest.Channel')} ${deviceUpdate.DeviceChannelNo}`,
          `${this.$t('CmdTest.deviceFieldStrength')}  ${deviceUpdate.DeviceFieldStrength}dBm`,
          `${this.$t('CmdTest.systemPassCheckOK')} ${this.isCheckOk(deviceUpdate.SystemPassCheckOK)}`,
          `${this.$t('CmdTest.status')} ${deviceUpdate.Status}`,
          `${this.$t('CmdTest.batteryPower')} ${parseInt(StatusInfo.batteryPower as string, 16) === 0 ? this.$t(
            'CmdTest.lowerPower') : power}`,
          //`${this.$t('CmdTest.isAlarm')} ${this.getJudgeRet(StatusInfo.isAlarm)}`,
          `${this.$t('CmdTest.isDumping')} ${this.getJudgeRet(StatusInfo.isDumping)}`,
          // `${this.$t('CmdTest.isLowPowerAlarm')} ${this.getJudgeRet(StatusInfo.isLowPowerAlarm)}`,
          `${this.$t('CmdTest.isRegister')} ${this.getJudgeRet(StatusInfo.isRegister)}`,
          `${this.$t('CmdTest.isTest')} ${this.getJudgeRet(StatusInfo.isTest)}`,
          `${this.$t('CmdTest.isValidPosition')} ${this.getJudgeRet(StatusInfo.isValidPosition)}`,
        ]
        if (StatusInfo.isValidPosition) {
          label2.push(`${this.$t('CmdTest.positionLng')} ${GPS.Lon ?? ''}`)
          label2.push(`${this.$t('CmdTest.positionLat')} ${GPS.Lat ?? ''}`)
        }
        // 处理界桩参数同步
        label2.push(`${this.$t('CmdTest.paramTime')} ${wrapperInvalidDate(deviceUpdate.ParamTime as string)}`)
        if (dayjs(deviceUpdate.ParamTime as string).isSameOrAfter(data.MarkerParamTime as string)) {
          label2.push(`${i18n.t('CmdTest.markerParamIsSync')}`)
        } else {
          label2.push(`${i18n.t('CmdTest.markerParamUnSync')}`)
        }
        // 处理界桩报警时间
        labArray.push(`${this.$t('CmdTest.AlarmTime')} ${wrapperInvalidDate(deviceUpdate.CmdTime as string)}`)
        if (checkUpdateCmdTimeIsSync(deviceUpdate.CmdTime as string)) {
          label2.push(`${i18n.t('CmdTest.markerTimeIsSync')}`)
        } else {
          label2.push(`${i18n.t('CmdTest.markerTimeUnSync')}`)
        }

        this.appendLog({
          No: data.MarkerNo,
          HWID: data.MarkerHWID,
          title: LogTypePrefix.BysMarker + this.$t('CmdTest.alarm'),
          color: LogTypeColor.DevAlarm,
          time: localTime(),
          label: this.getLogLabel(labArray.concat(label2)),
        })
      },
      realControllerName(StationID) {
        const controller = this.$store.getters[`${NS}/${GET_DATA_BY_INDEX}`](DataName.Controller, StationID)
        return controller?.ControllerNo ?? ''
      },
      addBysMarkerAlarmTestNode(data: BysMarkerAndUpdateInfo, deviceUpdate: DeviceUpdateInfo) {
        //收到报警，即为倾倒报警
        const controller: bysdb.IDbController | undefined = Controller.getDataByIndex(deviceUpdate.StationID + '')
        const StatusInfo = deviceUpdate.StatusInfo ?? {}
        const GPS = deviceUpdate.GPS ?? {}
        let power = calcMarkerPower(StatusInfo.batteryPower as string)
        const relayController = getControllerByChannelNo(deviceUpdate.StationID ?? 0, deviceUpdate.StationDeviceNo ?? 0)
        const labArray = [
          `${this.$t('CmdTest.bysMarker')} ${this.$t('CmdTest.init')}`,
          `${this.$t('CmdTest.Cmd')} ${deviceUpdate.Cmd}`,
          `${this.$t('CmdTest.NO')} ${data.MarkerNo}`,
          // `${this.$t('CmdTest.HWID')} ${data.MarkerHWID}`,
          `${this.$t('CmdTest.deviceID')} ${deviceUpdate.DeviceID}`,
          `${this.$t('CmdTest.type')} ${this.ControllerType[controller?.ControllerType as number]}`,
          `${this.$t('CmdTest.stationID')} ${deviceUpdate.StationID}/${this.realControllerName(
            deviceUpdate.StationID)}`,
        ]
        if (relayController) {
          labArray.push(
            `${this.$t('CmdTest.receivingRelay')} ${relayController.ControllerHWID}/${relayController.ControllerNo}`)
        }
        const label2 = [
          `${this.$t('CmdTest.stationDeviceNoBaseStation')} ${deviceUpdate.StationDeviceNo}`,
          `${this.$t('CmdTest.Channel')} ${deviceUpdate.DeviceChannelNo}`,
          `${this.$t('CmdTest.deviceFieldStrength')}  ${deviceUpdate.DeviceFieldStrength}dBm`,
          `${this.$t('CmdTest.systemPassCheckOK')} ${this.isCheckOk(deviceUpdate.SystemPassCheckOK)}`,
          `${this.$t('CmdTest.status')} ${deviceUpdate.Status}`,
          `${this.$t('CmdTest.batteryPower')} ${parseInt(StatusInfo.batteryPower as string, 16) === 0 ? this.$t(
            'CmdTest.lowerPower') : power}`,
          `${this.$t('CmdTest.isAlarm')} ${this.getJudgeRet(StatusInfo.isAlarm)}`,
          `${this.$t('CmdTest.isDumping')} ${this.getJudgeRet(StatusInfo.isDumping)}`,
          `${this.$t('CmdTest.isLowPowerAlarm')} ${this.getJudgeRet(StatusInfo.isLowPowerAlarm)}`,
          `${this.$t('CmdTest.isRegister')} ${this.getJudgeRet(StatusInfo.isRegister)}`,
          `${this.$t('CmdTest.isTest')} ${this.getJudgeRet(StatusInfo.isTest)}`,
          `${this.$t('CmdTest.isValidPosition')} ${this.getJudgeRet(StatusInfo.isValidPosition)}`,
        ]
        if (StatusInfo.isValidPosition) {
          label2.push(`${this.$t('CmdTest.positionLng')} ${GPS.Lon ?? ''}`)
          label2.push(`${this.$t('CmdTest.positionLat')} ${GPS.Lat ?? ''}`)
        }
        // 处理界桩参数同步
        label2.push(`${this.$t('CmdTest.paramTime')} ${wrapperInvalidDate(deviceUpdate.ParamTime as string)}`)
        if (dayjs(deviceUpdate.ParamTime as string).isSameOrAfter(data.MarkerParamTime as string)) {
          label2.push(`${i18n.t('CmdTest.markerParamIsSync')}`)
        } else {
          label2.push(`${i18n.t('CmdTest.markerParamUnSync')}`)
        }
        // 处理界桩报警时间
        label2.push(`${this.$t('CmdTest.AlarmTime')} ${wrapperInvalidDate(deviceUpdate.CmdTime as string)}`)
        if (checkUpdateCmdTimeIsSync(deviceUpdate.CmdTime as string)) {
          label2.push(`${i18n.t('CmdTest.markerTimeIsSync')}`)
        } else {
          label2.push(`${i18n.t('CmdTest.markerTimeUnSync')}`)
        }

        this.appendLog({
          No: data.MarkerNo,
          HWID: data.MarkerHWID,
          title: LogTypePrefix.BysMarker + this.$t('CmdTest.init'),
          color: LogTypeColor.DevAlarmTest,
          time: localTime(),
          label: this.getLogLabel(labArray.concat(label2)),
        })
      },
      onVirtualScroll(detail) {
        this.isStickyLastItem = detail.index === 0
      },
      appendLog(logItem) {
        if (this.isStickyLastItem) {
          setTimeout(() => {
            this.$nextTick(() => {
              const virtualScroll = this.$refs.virtualScroll as QVirtualScroll
              if (!virtualScroll) {
                return
              }

              virtualScroll.scrollTo(virtualScroll.items?.length as number)
            })
          })
        }

        this.logList.push(logItem)
      },
      clearLogListFront() {
        // 被删除的日志
        let deleteLogList: any[] = []
        // 只保留最后500条数据
        const index = this.logList.length - 500

        if (index > 0) {
          deleteLogList = this.logList.slice(0, index)
          this.logList = this.logList.slice(index)
        }

        // 删除日志中可能包含的blob url
        for (let i = 0; i < deleteLogList.length; i++) {
          const item = deleteLogList[i]
          if (item.url) {
            URL.revokeObjectURL(item.url)
          }
        }

        this.clearLogList()
      },
      clearLogList() {
        // 60分钟清理一次
        setTimeout(this.clearLogListFront, 60 * 60 * 1000)
      },
      addBysMarkerRemoveAlarmTestNode(data: BysMarkerAndUpdateInfo) {
        let temArray = [
          `${this.label4GMarker[data.MarkerType as MarkerType]} ${this.$t('CmdTest.removeAlarm')}`,
          `${this.$t('form.markerName')} ${data.MarkerNo}`,
          `${this.$t('CmdTest.HWID')} ${data.MarkerHWID}`,
          `IMEI ${data.IMEI}`,
          `ICCID ${data.ICCID}`,
        ]
        this.appendLog({
          No: data.MarkerNo,
          HWID: data.MarkerHWID,
          title: LogTypePrefix.BysMarker + this.$t('CmdTest.removeAlarm'),
          color: LogTypeColor.DevRemoveAlarmTest,
          time: localTime(),
          label: this.getLogLabel(temArray),
        })
      },
      addMarkerPhotographUploadNode(data: bysdb.IDbBysMarker, imageBytes: Uint8Array, camImageData: bysdb.CamImageData, custData: ICustData) {
        const time = localTime(dayjs(camImageData.timestamp * 1000))
        let temArray = [
          `${this.label4GMarker[data.MarkerType as MarkerType]} ${this.$t('historyTable.photoUpload')}`,
          `${this.$t('form.markerName')}: ${data.MarkerNo}`,
          `${this.$t('CmdTest.HWID')}: ${data.MarkerHWID}`,
          `${this.$t('form.uploadTime')}: ${time}`,
          `IMEI: ${data.IMEI}`,
          `ICCID: ${data.ICCID}`,
          `${this.$t('form.batteryPower')}: ${camImageData.battery.toFixed(2)}v`,
          `${this.$t('historyTable.captureType')}: ${CamImageType[custData.type]}`,
        ]
        const bodyBlob = new Blob([imageBytes], { type: 'image/jpg' })
        const url = URL.createObjectURL(bodyBlob)
        this.appendLog({
          No: data.MarkerNo,
          HWID: data.MarkerHWID,
          title: LogTypePrefix.BysMarker + this.$t('historyTable.photoUpload'),
          color: LogTypeColor.DevPhotoUpload,
          time: localTime(),
          label: this.getLogLabel(temArray),
          url,
        })
      },
      previewImageWithLightbox(url: string) {
        if (!url) {
          return
        }
        this.lightboxUrl = url
        this.visibleLightbox = true
      },
      onHideLightbox() {
        this.lightboxUrl = ''
        this.visibleLightbox = false
      },
      addNotifyICCIDAlreadyExistNode(repeatICCIDMarker: BysMarkerAndUpdateInfo, loginMarker: BysMarkerAndUpdateInfo, loginReq: bysproto.IBloginReq) {
        const list: string[] = [
          `${this.label4GMarker[loginMarker.MarkerType as MarkerType]} ${this.$t('CmdTest.online')}`,
          `${this.$t('CmdTest.iccidRepeat', {
            markerNo: loginMarker.MarkerNo,
            repeatMarker: repeatICCIDMarker.MarkerNo
          })}`,
          `${this.$t('CmdTest.loginTimeStr')} ${wrapperInvalidDate(loginReq.LoginTimeStr as string)}`,
          `IMEI ${loginReq.IMEI}`,
          `ICCID ${loginReq.ICCID}`,
        ]
        this.appendLog({
          No: loginMarker.MarkerNo,
          HWID: loginMarker.MarkerHWID,
          title: LogTypePrefix.BysMarker + '-' + this.$t('CmdTest.iccidRepeatTitle'),
          color: LogTypeColor.ICCIDAndIMEIRepeat,
          time: localTime(),
          label: this.getLogLabel(list)
        })
      },
      addNotifyICCIDAndIMEIConflictNode(marker: BysMarkerAndUpdateInfo, loginReq: bysproto.IBloginReq, data: bysproto.IBmsg) {
        const list: string[] = [
          `${this.label4GMarker[marker.MarkerType as MarkerType]} ${this.$t('CmdTest.online')}`,
          `${this.$t('CmdTest.imeiConflict', { markerNo: marker.MarkerNo })}`,
          `${this.$t('CmdTest.loginTimeStr')} ${wrapperInvalidDate(loginReq.LoginTimeStr as string)}`,
          `${this.$t('CmdTest.imeiInSystem', { imei: data.Optstr })}`,
          `${this.$t('CmdTest.onlineImei', { imei: loginReq.IMEI })}`,
          `ICCID ${loginReq.ICCID}`,
        ]
        this.appendLog({
          No: marker.MarkerNo,
          HWID: marker.MarkerHWID,
          title: LogTypePrefix.BysMarker + '-' + this.$t('CmdTest.imeiConflictTitle'),
          color: LogTypeColor.ICCIDAndIMEIRepeat,
          time: localTime(),
          label: this.getLogLabel(list)
        })
      },
    },
    mounted() {
      StrPubSub.subscribe(ControllerOnline, this.addControllerOnlineNote)
      StrPubSub.subscribe(ControllerOffline, this.addControllerOfflineNote)
      StrPubSub.subscribe(ControllerOfflinePanic, this.addControllerOfflinePanicNode)
      StrPubSub.subscribe(ControllerHeartbeat, this.addControllerHeartbeatNode)
      StrPubSub.subscribe(ControllerAlarm, this.addControllerAlarmNode)
      StrPubSub.subscribe(DeviceReportInfo, this.addBysMarkerReportInfoNode)
      StrPubSub.subscribe(BysMarkerDumpingAlarm, this.addBysMarkerAlarmNode)
      StrPubSub.subscribe(DeviceAlarmTest, this.addBysMarkerAlarmTestNode)
      StrPubSub.subscribe('geolocation', this.geolocation)
      StrPubSub.subscribe(RemoveBysMarkerAlarm, this.addBysMarkerRemoveAlarmTestNode)
      StrPubSub.subscribe(MarkerPhotographUpload, this.addMarkerPhotographUploadNode)
      StrPubSub.subscribe(NotifyICCIDAlreadyExist, this.addNotifyICCIDAlreadyExistNode)
      StrPubSub.subscribe(NotifyICCIDAndIMEIConflict, this.addNotifyICCIDAndIMEIConflictNode)
      //每5个小时清理一次
      this.clearLogList()
    },
    beforeUnmount() {
      StrPubSub.unsubscribe(ControllerOnline, this.addControllerOnlineNote)
      StrPubSub.unsubscribe(ControllerOffline, this.addControllerOfflineNote)
      StrPubSub.unsubscribe(ControllerOfflinePanic, this.addControllerOfflinePanicNode)
      StrPubSub.unsubscribe(ControllerHeartbeat, this.addControllerHeartbeatNode)
      StrPubSub.unsubscribe(ControllerAlarm, this.addControllerAlarmNode)
      StrPubSub.unsubscribe(DeviceReportInfo, this.addBysMarkerReportInfoNode)
      StrPubSub.unsubscribe(BysMarkerDumpingAlarm, this.addBysMarkerAlarmNode)
      StrPubSub.unsubscribe(DeviceAlarmTest, this.addBysMarkerAlarmTestNode)
      StrPubSub.unsubscribe(RemoveBysMarkerAlarm, this.addBysMarkerRemoveAlarmTestNode)
      StrPubSub.unsubscribe(MarkerPhotographUpload, this.addMarkerPhotographUploadNode)
      StrPubSub.unsubscribe(NotifyICCIDAlreadyExist, this.addNotifyICCIDAlreadyExistNode)
      StrPubSub.unsubscribe(NotifyICCIDAndIMEIConflict, this.addNotifyICCIDAndIMEIConflictNode)
    },
    computed: {
      ControllerType() {
        return {
          1: i18n.global.t('CmdTest.repeater'),
          2: i18n.global.t('CmdTest.baseStation'),
          Net4gMarker: i18n.global.t('CmdTest.net4GMarker'),
        }
      },
      /*filterPanelClasses() {
        return {
          'is-active': this.showSelectPanel,
        }
      },*/
      title() {
        return this.$t('menus.cmdTest')
      },
      isEnUs() {
        return this.locale === appLang.enUs
      },
      allLogData() {
        if (this.filterInput) {
          return this.logList.filter(item => (item.No + item.HWID).includes(this.filterInput.trim()))
        }
        return this.logList
      },
      label4GMarker() {
        return {
          [MarkerType.Net4G]: this.$t('CmdTest.net4GMarker'),
          [MarkerType.Net4GPro]: this.$t('form.4GMarkersPro'),
          [MarkerType.Regular]: this.$t('form.normalMarkers'),
        }
      }
    },
  })
</script>

<style lang="scss">
  @import "quasar/src/css/variables";

  .test-log-panel {
    max-height: 100%;
  }

  .log-modal .bys-move-page-container {
    max-height: unset !important;
  }

  .log-modal:not(.maximized) {
    width: 75vw;
    max-width: 75vw;
  }

  .log-modal:not(.maximized) .test-log-panel {
    height: 75vh;
  }

  .log-modal .test-log-panel {
    /*max-height: 100%;*/
    width: 100%;
    display: flex;
    flex-direction: column;

    .test-log-panel-select,
    .test-log-panel-select-btn,
    .test-log-panel-content {
      width: 100%;
    }

    .test-log-panel-select-panel {
      flex: none;
      border-bottom: 1px solid #f5f5f5;
      padding-bottom: 3px;
      /*padding-bottom: 10px;
                      background: linear-gradient(to bottom, #ffffff calc(100% - 12px), #f5f5f5 calc(100% - 11px), #ffffff);*/
      position: relative;

      &:after {
        content: "";
        display: block;
        position: absolute;
        bottom: -10px;
        height: 10px;
        width: 100%;
        background: linear-gradient(to bottom, #f5f5f5, #ffffff);
        z-index: -1;
      }

      .test-log-panel-select-input {
        flex: auto;
        width: 60%;
      }

      .clear-log-btn {
        flex: none;
        align-self: center;
      }
    }

    .test-log-panel-content {
      flex: auto;
    }
  }
</style>

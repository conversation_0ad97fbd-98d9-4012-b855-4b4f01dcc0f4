<template>
  <dialogModal
    v-model="visible"
    v-model:maximized="maximized"
    :contentHeight="maxHeight ? '60vh' : 'auto'"
    :title="title"
    content-class="marker-nfc-patrol-history"
    ref="markerNfcPatrolHistory"
    @hide="maxHeight = false"
  >
    <query-history
      ref="formContent"
      :default-time-diff="defaultTimeDiff"
      :time-max-range="timeMaxRange"
      :export-name="title"
      :table-columns="columns"
      :child-columns="expandTableColumns"
      :time-limit-sql-name="timeField"
      :can-query="true"
      :timeMask="DateMask"
      :noTimeIcon="false"
      :row-key="rowKey"
      exportExcelV2
      child-table-key="CheckResult"
      :render-child-row="renderChildRow"
      use-operate
      @submit-clicked="onSubmit"
      @query-canceled="queryCanceled"
    >
      <template #form>
        <div class="query-form">
          <q-select
            v-model="LineRid"
            :label="$t('NFCPatrol.LineName')"
            outlined
            dense
            clearable
            lazy-rules
            :rules="[]"
            :options="lineRidOptions"
            @filter="filterLineRID"
            options-dense
            map-options
            emit-value
            use-input
          >
          </q-select>
        </div>
      </template>
      <template v-slot:my-body-cell-Checked="{ row }">
        <q-chip size="12px" dense
                :color="row.CheckResult.length === row.Checked ? 'green' : row.Checked === 0 ? 'red' : 'orange'"
                text-color="white" :label="row.Checked"/>
      </template>
      <template v-slot:my-body-cell-last="{ row }">
        <q-btn
          class="q-ml-sm"
          color="primary"
          icon="history"
          size="sm"
          flat
          round
          dense
          :disable="row.Checked === 0"
          @click="playTrace(row)"
        >
          <q-tooltip v-if="row.Checked !== 0">
            {{ $t('menus.trackPlayback') }}
          </q-tooltip>
        </q-btn>
      </template>
    </query-history>
  </dialogModal>

  <TracePlayer
    v-model="traceVisible"
    :play-data="checkResultTrace"
    :not-play-data="unCheckResultTrace"
    :getPopupHtml="processPopupHtml"
    :createHTMLElement="createHTMLElement"
    :sortPlayData="sortPlayData"
    :sortNotPlayData="sortNotPlayData"
  >
  </TracePlayer>
</template>

<script lang="ts" setup>
  import { useDialogModal } from '@utils/vueUse/dialog'
  import {
    computed,
    onBeforeMount,
    onBeforeUnmount,
    ref,
    h,
    nextTick,
  } from 'vue'
  import QueryHistory from '@components/queryHistory/QueryHistory.vue'
  import { crud } from '@ygen/crud'
  import { useI18n } from 'vue-i18n'
  import { defaultQueryFinished } from '@services/queryData'
  import { bysdb } from '@ygen/bysdb'
  import { BysMarker, NFCPatrolLine, NFCPatrolLineRules, Unit, User } from '@services/dataStore'
  import { StrPubSub } from 'ypubsub'
  import { QueryPatrolStatistics } from '@utils/pubSubSubject'
  import { ICallOption, TRpcStream } from 'jsyrpc'
  import { yrpcmsg } from '@ygen/yrpcmsg'
  import log from '@utils/log'
  import * as BysApiY from '@ygen/bys.api'
  import { RpcBysApi } from '@ygen/bys.api.yrpc'
  import { Notify, QBtn, QChip, QTable } from 'quasar'
  import { DateMask, TimeMask, toLocalTime } from '@utils/dayjs'
  import { getGCJ02LngLat } from '@utils/gcoord'
  import { doc } from '@ygen/bys.api'
  import { org } from '@ygen/org'
  import TracePlayer from '@components/tracePlayer/TracePlayer.vue'
  import { TracePlayerData } from '@components/tracePlayer/types'

  type MyTracePlayerData = TracePlayerData<doc.IPatrolCheckItem>

  const i18n = useI18n()
  const { t } = i18n
  const { visible, maximized, dialogModal } = useDialogModal()
  const maxHeight = ref(false)
  const title = computed(() => { return i18n.t('NFCPatrol.patrolStatistics') })
  let queryNormal = true
  let queryRet: Array<doc.IPatrolStatisticsQueryResult[]> = []
  let queryCancelHandlers: Array<() => void> = []
  const timeField = 'ActionTime'
  const timeMaxRange = computed(() => {
    return {
      value: 3,
      uint: 'month',
      text: i18n.t('date.notMax3month'),
    }
  })
  const defaultTimeDiff = {
    value: 1,
    uint: 'day',
  }
  const formContent = ref<InstanceType<typeof QueryHistory>>()
  const LineRid = ref('')
  const lineFilter = ref('')
  const expandTableMaxHeight = ref('400px')

  const expandTableColumns = computed(() => {
    return [
      { name: 'Index', field: '$index', label: '#', align: 'right', sortable: true, style: 'width: 70px' },
      {
        name: 'MarkerHWID', field: 'MarkerHWID', label: i18n.t('NFCPatrol.patrolPoint'), sortable: true,
        style: 'width: 100px',
        format: (val) => {
          return getMarkerNo(val)
        }
      },
      {
        name: 'CheckResult', field: 'CheckResult', label: i18n.t('NFCPatrol.checkResult'), sortable: true,
        style: (row) => `color: ${row.CheckResult === 0 ? 'green' : 'red'}; width: 100px`,
        format: (val) => {
          return val === 0 ? i18n.t('CmdTest.normal') : i18n.t('NFCPatrol.noCheck')
        }
      },
      {
        name: 'CheckTime', field: 'CheckTime', label: i18n.t('NFCPatrol.checkTime'), sortable: true,
        style: 'width: 200px',
        format: (val) => {
          return val ? toLocalTime(val, TimeMask) : ''
        }
      },
      {
        name: 'CheckUserRID', field: 'CheckUserRID', label: i18n.t('NFCPatrol.checkUser'), sortable: true,
        style: 'width: 150px',
        format: (val) => {
          return User.getData(val)?.UserName ?? ''
        }
      },
    ]
  })

  const columns = computed(() => {
    return [
      {
        name: 'LineRid', field: 'LineRid', label: i18n.t('NFCPatrol.LineName'), sortable: true, sticky: true,
        format: (val: string) => {
          const line = NFCPatrolLine.getData(val) as bysdb.IDbNFCPatrolLine
          return line.Name
        },
      },
      {
        name: 'ShouldCheckCount',
        field: 'ShouldCheckCount',
        label: i18n.t('NFCPatrol.QuantityToCheck'),
        sortable: true,
        noLeftBorder: true,
      },
      {
        name: 'Checked',
        field: 'Checked',
        label: i18n.t('NFCPatrol.checkNum'),
        sortable: true,
      },
      {
        name: 'CheckDate', field: 'CheckDate', label: i18n.t('NFCPatrol.checkDate'), sortable: true,
      },
      {
        name: 'RuleRID', field: 'RuleRID', label: i18n.t('NFCPatrol.patrolRules'), sortable: true,
        format: (val: string) => {
          const rule = NFCPatrolLineRules.getData(val) as bysdb.IDbNFCPatrolLineRules
          return rule.Name
        },
      },
      {
        name: 'last', field: '', label: '', sticky: true
      }
    ]
  })

  const lineRidOptions = computed(() => {
    const options = NFCPatrolLine.getDataList().map(item => {
      return {
        value: item.RID,
        label: item.Name,
      }
    })
    // 根据输入内容过滤数据
    if (lineFilter.value) {
      return options.filter(option => {
        const needle = lineFilter.value.toLowerCase()
        return option.label?.toLowerCase().includes(needle)
      })
    }

    return options
  })

  const traceVisible = ref(false)
  const checkResultTrace = ref<MyTracePlayerData[]>([])
  const unCheckResultTrace = ref<MyTracePlayerData[]>([])
  let currentRow: doc.IPatrolStatisticsQueryResult = {}

  function rowKey(row) {
    return row.LineRid + '-' + row.RuleRID + '-' + row.CheckDate
  }

  function onSubmit(where: crud.IWhereItem[]) {
    const queryAlreadyCallBack = function () {
      queryNormal && defaultQueryFinished()
    }
    queryRet = []
    queryNormal = false
    maxHeight.value = true

    const options: ICallOption = {
      OnResult: (data) => {
        queryNormal = true
        data.Checked = data.CheckResult.filter(i => i.CheckResult === 0).length
        log.info('NFCPatrolStatistics result', data)
        StrPubSub.publish(QueryPatrolStatistics, data)
      },
      OnServerErr: (errRpc: yrpcmsg.Ymsg) => {
        log.error('NFCPatrolStatistics server error', JSON.stringify(errRpc))
      },
      OnLocalErr: (err: any) => {
        log.error('NFCPatrolStatistics local error', err)
      },
      OnStreamFinished: (rpcCmd: yrpcmsg.Ymsg) => {
        log.info('NFCPatrolStatistics finish', rpcCmd)
        queryCancelHandlers = []
        queryAlreadyCallBack()
      },
      OnTimeout: (v: any) => {
        log.warn('NFCPatrolStatistics timeout', v)
      },
    }
    const req: BysApiY.doc.PatrolStatisticsQuery = {
      StartTime: toLocalTime(where[0].FieldValue, DateMask) + ' 00:00:00',
      EndTime: toLocalTime(where[1].FieldValue, DateMask) + ' 23:59:59',
      LineRid: LineRid.value
    }
    const stream: TRpcStream = RpcBysApi.NFCPatrolStatistics(req, options)
    queryCancelHandlers.push(stream.cancel)

    nextTick(() => {
      expandTableMaxHeight.value = (formContent.value?.$el.querySelector('.history-table .q-table__middle').clientHeight - 110) + 'px'
    })
  }

  function queryDataRet(datas: []) {
    queryRet = queryRet.concat(Object.freeze(datas))
    // @ts-ignore
    formContent.value.queryRet = queryRet
  }

  function queryCanceled() {
    for (let i = queryCancelHandlers.length - 1; i >= 0; i--) {
      queryCancelHandlers[i]()
    }
    queryCancelHandlers = []
    queryNormal = false
    maxHeight.value = false
  }

  function filterLineRID(val: string, update: Function) {
    lineFilter.value = val
    update()
  }

  function renderChildRow({ row }) {
    const checkResult = row.CheckResult.map((item, index) => {
      item.$index = index + 1
      return item
    })

    return h(QTable, {
      rows: checkResult,
      columns: expandTableColumns.value,
      rowKey: (row) => row.MarkerHWID + '' + row.$index,
      hideBottom: true,
      flat: true,
      virtualScroll: true,
      bordered: true,
      pagination: {
        rowsPerPage: 0,
      },
      rowsPerPageOptions: [0],
      virtualScrollStickySizeStart: 0,
      class: 'expand-table',
      style: {
        maxHeight: expandTableMaxHeight.value
      }
    })
  }

  function getMarkerNo(hwId: string) {
    const marker = BysMarker.getDataByIndex(hwId)
    return marker?.MarkerNo
  }

  function playTrace(row: doc.IPatrolStatisticsQueryResult) {
    currentRow = row
    const CheckResult = row.CheckResult ?? []
    const checkData: MyTracePlayerData[] = []
    const uncheckData: MyTracePlayerData[] = []
    for (let i = 0; i < CheckResult.length; i++) {
      const item = CheckResult[i]
      const marker = BysMarker.getDataByIndex(item.MarkerHWID + '')
      if (!marker) continue

      const data: MyTracePlayerData = {
        lonLat: getGCJ02LngLat(marker) as [number, number],
        OrgRID: marker.OrgRID as string,
        displayName: marker.MarkerNo as string,
        MarkerHWID: item.MarkerHWID as number,
        source: item,
      }
      if (item.CheckResult === 0) {
        checkData.push(data)
      } else {
        uncheckData.push(data)
      }
    }
    checkResultTrace.value = checkData
    unCheckResultTrace.value = uncheckData
    if (checkResultTrace.value.length === 0) {
      Notify.create({
        message: i18n.t('NFCPatrol.noNFCRecord'),
        color: 'negative',
        position: 'top',
      })
      return
    }
    traceVisible.value = true
  }

  function createHTMLElement() {
    const container = document.createElement('div')
    container.classList.add('line-info-list')

    const children = [
      {
        classes: '',
        getLabelContent: () => i18n.t('NFCPatrol.LineName') + ': ',
        getValueContent: () => NFCPatrolLine.getData(currentRow.LineRid as string)?.Name,
      },
      {
        classes: '',
        getLabelContent: () => i18n.t('NFCPatrol.patrolRules') + ': ',
        getValueContent: () => NFCPatrolLineRules.getData(currentRow.RuleRID + '')?.Name,
      },
      {
        classes: '',
        getLabelContent: () => i18n.t('NFCPatrol.QuantityToCheck') + ': ',
        getValueContent: () => currentRow.ShouldCheckCount,
      },
      {
        classes: '',
        getLabelContent: () => i18n.t('NFCPatrol.checkNum') + ': ',
        // @ts-ignore
        getValueContent: () => currentRow.Checked,
      },
    ]
    const childrenDom = children.map(item => {
      const child = document.createElement('div')
      child.classList.add('line-info-list-item')
      if (item.classes) {
        child.classList.add(item.classes)
      }

      const childLabel = document.createElement('div')
      childLabel.classList.add('section-label')
      childLabel.innerHTML = item.getLabelContent()

      const childValue = document.createElement('div')
      childValue.classList.add('section-content')
      childValue.innerHTML = item.getValueContent()

      child.append(childLabel, childValue)

      return child
    })
    container.append(...childrenDom)

    return container
  }

  function sortPlayData(rows: MyTracePlayerData[]) {
    rows.sort((a, b) => {
      const aCheckTime = a.source.CheckTime || ''
      const bCheckTime = b.source.CheckTime || ''
      return aCheckTime.localeCompare(bCheckTime)
    })
  }

  function sortNotPlayData(rows: MyTracePlayerData[]) {
    rows.sort((a, b) => {
      const aMarkerHWID = a.MarkerHWID + ''
      const bMarkerHWID = b.MarkerHWID + ''
      return aMarkerHWID.localeCompare(bMarkerHWID)
    })
  }

  function processPopupHtml(data: MyTracePlayerData) {
    const orgData = Unit.getData(data.OrgRID + '') as org.IDbOrg | undefined
    const ShortName = orgData?.ShortName ?? data.OrgRID
    const marker: bysdb.IDbBysMarker | undefined = BysMarker.getDataByIndex(data.MarkerHWID + '')
    const MarkerNo = marker?.MarkerNo ?? data.MarkerHWID
    const user = User.getData(data.source.CheckUserRID + '')
    const UserName = user?.UserName ?? data.source.CheckUserRID
    return (`<div>
        <div><span class="text-caption">${t('form.unit')}</span> <span>${ShortName}</span></div>
        <div><span class="text-caption">${t('form.markerName')}</span> <span>${MarkerNo}</span></div>
        ${data.source.CheckResult === 1 ? `
        <div><span class="text-caption"><span>${i18n.t('NFCPatrol.noPatrol')}</span></div>
        ` : `
        <div><span class="text-caption">${t('NFCPatrol.userName')}</span> <span>${UserName}</span></div>
        <div><span class="text-caption">${t('NFCPatrol.patrolTime')}</span> <span>${toLocalTime(data.source.CheckTime)}</span></div>`}
      </div>`)
  }

  onBeforeMount(() => {
    StrPubSub.subscribe(QueryPatrolStatistics, queryDataRet)
  })

  onBeforeUnmount(() => {
    StrPubSub.unsubscribe(QueryPatrolStatistics, queryDataRet)
  })
</script>

<style lang="scss">
  .marker-nfc-patrol-history {
    .history-panel {
      height: 100%;

      .history-table {
        &.q-table--dense .q-table {

          th,
          td {
            padding: 2px 4px;
          }
        }

        .sticky-column-last {
          position: sticky !important;
          right: 0;
          background: #FFFFFF;
        }
      }

      .history-table > :nth-child(2) > :first-child > :first-child tr th {
        z-index: 99;
      }
    }

    .q-tab-panel {
      padding: unset
    }

    .query-form {

      // 表单有验证条件的下边距为20px，该样式主要同步没有验证条件的表单项样式
      & > *:not(:last-child) {
        padding-bottom: 20px;
      }
    }

    &:not(.maximized) {
      width: auto !important;
      max-width: unset !important;

      .history-panel .history-table {
        width: 60vw !important;
      }

      .query-form {
        min-width: 400px;
      }
    }
  }

  .expand-table {
    margin: 10px;
    overflow: auto;
  }

  .line-info-list {
    background-color: rgba(0, 0, 0, 0.6);
    color: #fff;
    position: fixed;
    width: 200px;
    margin-top: 10px;
    padding: 10px;
    border-radius: 6px;

    .line-info-list-item {
      display: flex;
      margin: 5px;

      .section-content {
        margin-left: 5px;
      }
    }
  }
</style>

<template>
  <!-- visible,maximized in dataEdit mixins -->
  <modal
    v-model="visible"
    v-model:maximized="maximized"
    :contentHeight="dataLen ? '68vh' : 'auto'"
    ref="modal"
  >
    <template v-slot:header><span>{{ title }}</span></template>
    <!--
     角色管理菜单
     行选中保留项：OrgRID（单位），用来做为添加时的默认单位 。
      OrgRID -> defaultOrgID -> currentRow.OrgRID
     重复添加保留项：RoleName（名称）,OrgRID（单位），其中角色名称要保留格式自增,单位直接继承。
    -->
    <data-edit
      :data="data"
      :columns="columns"
      :form-title="title"
      :can-add="canAdd"
      :can-edit="canEdit"
      :can-delete="canDelete"
      :insert="insertData"
      :update="updateData"
      :delete="deleteData"
      :import-data="batchAddData"
      v-model:currentRow="currentRow"
      @new-row="newRow"
      @rwo-clicked="rowClicked"
      @save-multiplex-item="saveMultiplexItem"
      @clear-multiplex-item="clearMultiplexItem"
      @hide="hidePage"
      ref="roleDataEdit"
    >

      <template v-slot:form-content>
        <div class="fit row wrap form-role">
          <div class="col col-xs-12 col-sm-6 q-gutter-sm q-col-gutter-sm">
            <q-input
              v-model="currentRow.RoleName"
              :label="$t('form.name')"
              outlined
              dense
              clearable
              autofocus
              lazy-rules
              :rules="rules.RoleName"
              :maxlength="16"
            />
          </div>
          <div class="col col-xs-12 col-sm-6 q-gutter-sm q-col-gutter-sm">
            <!-- parentOptions,filterParent in dataEdit mixins -->
            <q-select
              v-model="currentRow.OrgRID"
              :label="$t('form.unit')"
              outlined
              dense
              clearable
              lazy-rules
              :rules="rules.OrgRID"
              :options="parentOptions"
              @filter="filterParent"
              options-dense
              map-options
              emit-value
            />
          </div>
          <div class="col col-xs-12 col-sm-6 q-gutter-sm q-col-gutter-sm">
            <q-input
              v-model.number="currentRow.SortValue"
              type="number"
              :label="$t('form.sortValue')"
              outlined
              dense
              clearable
              lazy-rules
              :rules="rules.SortValue"
            />
          </div>
        </div>
      </template>

      <template v-slot:form-footer="{ isLoading, editStatus, dataEditStatus, continueEdit, confirm }">
        <div class="col col-xs-12 justify-center flex gap-2">
          <q-btn
            class="q-mx-sm"
            color="primary"
            size="md"
            form
            :label="$t('permission.permissionCenter')"
            @click="onPermCenterClick()"
          />

          <template v-if="editStatus === dataEditStatus.add">
            <q-btn
              class="q-mx-sm"
              color="info"
              :label="$t('form.keepAdding')"
              size="md"
              :loading="isLoading"
              :disable="!canAdd || isLoading"
              @click="continueEdit"
            />
            <q-btn
              class="q-mx-sm"
              color="primary"
              :label="$t('common.confirm')"
              size="md"
              :loading="isLoading"
              :disable="!canAdd || isLoading"
              @click="customConfirm(confirm)"
            />
          </template>
          <q-btn
            v-else
            class="q-mx-sm"
            color="primary"
            :label="$t('common.confirm')"
            size="md"
            :loading="isLoading"
            :disable="!canEdit || isLoading"
            @click="customConfirm(confirm)"
          />
        </div>
      </template>

      <!-- 操作列控件 -->
      <template v-slot:data-actions="{ props, editDataRow, deleteDataRow }">
        <q-btn
          class="data-action-btn"
          icon="security"
          color="orange-6"
          size="sm"
          flat
          round
          dense
          @click="quickShowInnerPage(props.row)"
        >
          <q-tooltip>
            {{ $t('common.RoleQuickPerm') }}
          </q-tooltip>
        </q-btn>
        <q-btn
          class="q-ml-sm data-action-btn"
          color="primary"
          icon="create"
          size="sm"
          flat
          round
          dense
          :disable="rowIsDisabled(props.row)"
          @click="editDataRow(props.row)"
        >
          <q-tooltip>
            {{ $t('common.edit') }}
          </q-tooltip>
        </q-btn>
        <q-btn
          class="q-ml-sm data-action-btn"
          color="negative"
          icon="delete"
          size="sm"
          flat
          round
          dense
          :disable="disabledRowDelete(props.row)"
          @click="deleteDataRow(props.row)"
        >
          <q-tooltip>
            {{ $t('common.delete') }}
          </q-tooltip>
        </q-btn>
      </template>
    </data-edit>

    <!--权限弹窗相关-->
    <q-dialog
      persistent
      :class="{ 'z-max': true }"
      v-model="isShowPermDlg"
      @hide="hideRolePermPage"
    >
      <q-card class="full-width">
        <q-bar class="sticky-header bg-primary text-white">
          <div>{{ $t('permission.permissionCenter') }}/{{ currentRoleName }}</div>
          <q-space />
          <q-btn
            dense
            flat
            icon="close"
            v-close-popup
          />
        </q-bar>
        <q-card-section class="q-pa-sm">
          <!--数据库权限控制区-->
          <div class="row no-wrap overflow-hidden full-width card-section-height">
            <q-tabs
              v-model="curTab"
              dense
              vertical
              class="bg-grey-2 inner-page-tabs"
              active-color="primary"
              @update:model-value="tabChange"
            >
              <q-tab
                v-for="tab in tabs"
                :key="tab"
                :name="tab"
                :label="tab === 'tabDb' ? $t('PermTab.Db') : $t('PermTab.Cmd')"
              />
            </q-tabs>
            <q-separator vertical></q-separator>
            <div
              ref="scrollArea"
              class="full-height overflow-auto col-grow"
            >
              <q-scroll-observer
                @scroll="scrollHandler"
                debounce="300"
              >
              </q-scroll-observer>

              <tabDb
                ref="tabDb"
                :roleRID="currentRow.RID"
              />
              <tabCmd
                ref="tabCmd"
                :roleRID="currentRow.RID"
              />
            </div>
          </div>
          <!--数据库权限控制区-->
        </q-card-section>
      </q-card>
    </q-dialog>
    <!--权限弹窗相关-->
  </modal>
</template>

<script lang="ts">
  import { defineComponent } from 'vue'
  import dialogMixin from '@src/utils/mixins/dialog'
  import dataEditMixin from '@src/utils/mixins/editor'
  import { utcTime } from '@src/utils/dayjs'
  import { v1 as uuidV1 } from 'uuid'
  import { maxLength, maxValue, minValue, required } from '@src/utils/validation'
  import { user as userPermission } from '@ygen/userPermission'
  import { PrpcDbRole, PrpcDbRolePermission } from '@ygen/userPermission.rpc.yrpc'
  import { ICallOption } from 'jsyrpc'
  import { yrpcmsg } from 'yrpcmsg'
  import log from '@src/utils/log'
  import { getLocalUser, Role, RolePermission, roleUpdateIsMyRole, UserRole } from '@src/services/dataStore'
  import { DataName } from '@src/store/data'
  import { crud } from '@ygen/crud'
  import { GET_DATA, NS } from '@src/store/data/methodTypes'
  import { user } from '@ygen/user'
  import createSelfIncreaseStr from '@src/utils/createSelfIncreaseStr'
  import { QueryFinishStatus, queryOneRolePermissions, queryRoleRelateData } from '@src/services/queryData'
  import { StrPubSub } from 'ypubsub'
  import { DefUuid } from '@src/config'
  import { scroll } from 'quasar'
  import { UpdateDbRole } from '@utils/pubSubSubject'
  import { PrpcDbUserRole } from '@ygen/user.rpc.yrpc'
  import { BuiltInAttrs, BuiltInAttrTranslate, outputDBError } from '@utils/common'
  import { i18n } from '@boot/i18n'
  import { cloneDeep } from 'lodash'
  import { BaseRoleRids, DbName } from '@utils/permission'
  import { FormRules, LocalRole } from '@utils/bysdb.type'
  import DbTab from '@components/rolePermInnerTabs/DbTab.vue'
  import CmdTab from '@components/rolePermInnerTabs/CmdTab.vue'

  const roleError = {
    dbrole_orgrid_fkey: i18n.global.t('dataBaseError.parUnitErr'),
    privilege: i18n.global.t('dataBaseError.lackOrgPrivilege'),
    'No operation permission': i18n.global.t('dataBaseError.permissionErr'),
    'role name already exists': i18n.global.t('dataBaseError.roleNameCannotRepeated'),
  }
  // 开始请求角色相关的数据
  if (!QueryFinishStatus[DataName.Role]) {
    queryRoleRelateData()
  }
  //滚动条相关函数
  const { setVerticalScrollPosition } = scroll

  export default defineComponent({
    name: 'Role',
    mixins: [dialogMixin, dataEditMixin],
    data() {
      return {
        defaultOrgRID: '',
        defaultRoleName: '',
        currentRow: {},
        /*权限弹窗相关*/
        isShowPermDlg: false,
        tabs: ['tabDb', 'tabCmd'],
        curTab: 'tabDb',
        /*权限弹窗相关*/
        isUnRepeatInsert: true,
        isInserted: false,
      }
    },
    components: {
      /*权限弹窗相关*/
      tabDb: DbTab,
      tabCmd: CmdTab,
      /*权限弹窗相关*/
    },
    computed: {
      title(): string {
        return this.$t('menus.role') as string
      },
      columns(): Array<{ [key: string]: any }> {
        return [
          {
            name: 'RoleName', field: 'RoleName', label: this.$t('form.name'), sortable: true, sticky: true,
            format: (val) => {
              return `${BuiltInAttrs.includes(val) ? BuiltInAttrTranslate()[val] : val}`
            },
          },
          {
            name: 'OrgRID', field: 'OrgRID', label: this.$t('form.unit'), sortable: true, noLeftBorder: true,
            format: (val, row) => {
              // 内置角色的单位都是默认
              if (row.IsBuiltIn === 1) {
                return this.$t('form.default')
              }
              return this.getParentOrgName(val)
            },
          },
          { name: 'SortValue', field: 'SortValue', label: this.$t('form.sortValue'), sortable: true },
          {
            name: 'Creator', field: 'Creator', label: this.$t('form.creator'),
            format: (val, row) => {
              // 内置角色的创建者显示为内置
              if (row.IsBuiltIn === 1) {
                return this.$t('form.builtin')
              }
              // 找到创建都用户，显示其名称
              const user = getLocalUser(val)
              return `${user ? user.UserName : ''}`
            },
          },
        ]
      },
      rules(): FormRules {
        const _required = (val) => required(val) || this.$t('rules.required')
        const _maxLength = (val) => maxLength(val, 16) || this.$t('rules.maxLength', { len: 16 })

        return {
          RoleName: [
            val => _required(val),
            val => _maxLength(val),
          ],
          OrgRID: [
            val => _required(val),
          ],
          SortValue: [
            val => maxValue(val, 0xFFFFFFFF) || this.$t('rules.maxValue', { max: 0xFFFFFFFF }),
            val => minValue(val, 0) || this.$t('rules.minValue', { min: 0 }),
          ],
        }
      },
      dataLen(): number {
        return this.data.length
      },
      data(): Array<userPermission.IDbRole> {
        const data = this.$store.getters[`${NS}/${GET_DATA}`](DataName.Role)
          // 所有用户都有该角色，默认不显示出来
          .filter(item => !BaseRoleRids.includes(item.RID))
        return Role.sortBy(data, { sortBy: 'SortValue' })
      },
      users(): Array<user.IDbUser> {
        const data = this.$store.getters[`${NS}/${GET_DATA}`](DataName.User)
        return Role.sortBy(data, { sortBy: 'UserName' })
      },
      /*与权限有关*/

      //获取用户名
      currentRoleName(): string | undefined {
        const roles = this.$store.getters[`${NS}/${GET_DATA}`](DataName.Role)
        const name = roles.find(item => item.RID === this.currentRow.RID)?.RoleName
        return `${BuiltInAttrs.includes(name) ? BuiltInAttrTranslate()[name] : name}`
      },
      //所有权限-字符映射表
      permNameMap() {
        return {
          /*Db*/
          'Menu': this.$t('DbPerm.menu'),
          'Query': this.$t('DbPerm.query'),
          'Update': this.$t('DbPerm.update'),
          'Insert': this.$t('DbPerm.insert'),
          'Delete': this.$t('DbPerm.delete'),

          'DbOrg': this.$t('menus.organization'),
          'DbUser': this.$t('menus.user'),
          'DbRole': this.$t('menus.role'),
          'DbController': this.$t('menus.controller'),
          'DbBysMarker': this.$t('menus.boundaryMarker'),
          'DbMediaInfo': this.$t('menus.images'),
          'DbControllerOnlineHistory': this.$t('menus.controllerOnlineRecords'),
          'DbMarkerHistory': this.$t('menus.markerHistory'),
          /*Db*/
        }
      },
      permData(): Array<userPermission.IDbPermission> {
        return this.$store.getters[`${NS}/${GET_DATA}`](DataName.Permission)
      },
      currentRolePermData(): Array<userPermission.IDbRolePermission> {
        return this.$store.getters[`${NS}/${GET_DATA}`](DataName.RolePermission)
          .filter(item => item.RoleRID === this.currentRow.RID)
      },
      /*与权限有关*/
      // 当前用户的所有用户角色关系数据
      currentUserRoles(): Array<user.IDbUserRole> {
        return this.$store.getters[`${NS}/${GET_DATA}`](DataName.UserRole)
          .filter(data => data.UserRID === this.$store.state.UserRID)
      },
      dbName() {
        return DbName.DbRole
      },
    },
    methods: {
      rowIsBuiltIn(row: userPermission.IDbRole): boolean {
        return row.IsBuiltIn === 1
      },
      rowIsNoPerm(row: userPermission.IDbRole): boolean {
        // 通过角色RID查找到所有指向当前登录用户的UserRole数据
        return !this.currentUserRoles.find(item => item.RoleRID === row.RID)
      },
      rowIsDisabled(row: userPermission.IDbRole): boolean {
        // log.info('rowIsDisabled', row)
        return !this.canEdit || this.rowIsBuiltIn(row) ||
          this.rowIsNoPerm(row)
      },
      disabledRowDelete(row: userPermission.IDbRole): boolean {
        return !this.canDelete || this.rowIsBuiltIn(row) ||
          this.rowIsNoPerm(row)
      },
      /*权限弹窗相关*/
      //tab与page同步滚动
      hidePage() {
        this.isUnRepeatInsert = true
        this.isInserted = false
      },
      hideRolePermPage() {
        //如果是编辑按钮进入，则不用修改
        if (this.isInserted) {
          this.$refs.roleDataEdit?.newRow()
        }
      },
      tabChange(name) {
        const el = this.$refs[name].$el
        //-10是两个component的margin
        setVerticalScrollPosition(this.$refs.scrollArea, el.offsetTop - 10, 350)
      },
      scrollHandler(e) {
        const { position } = e
        //界面前1/3有这个dom元素则默认进入到该tab
        const val = this.$refs.scrollArea.offsetHeight / 3
        for (let k in this.$refs) {
          if (!k.startsWith('tab-')) {
            continue
          }
          const el = this.$refs[k].$el
          if (position.top >= el.offsetTop - val && this.tab !== k) {
            this.curTab = k
          }
        }
      },
      requestRolePermissions() {
        if (!this.currentRow) { return }
        const rolePermission = RolePermission.getDataList()
          .filter(item => item.RoleRID === this.currentRow.RID)
        if (rolePermission.length) { return }

        queryOneRolePermissions(this.currentRow)
          .catch((err) => {
            log.error('queryOneRolePermissions error:', this.currentRow, err)
          })
      },
      //弹窗控制函数
      customConfirm(confirm) {
        if (this.isInserted) {
          //如果newRow中已经插入到数据库，则跳过
          if (this.$refs.roleDataEdit) {
            this.$refs.roleDataEdit.visible = false
          }
          return
        }
        confirm()
      },
      quickShowInnerPage(row) {
        this.currentRow = row
        this.onPermCenterClick()
      },
      onPermCenterClick() {
        if (this.isUnRepeatInsert) {
          this.isShowPermDlg = true
          // 如果该角色没有数据，则请求回来
          this.requestRolePermissions()
          return
        }

        this.$refs.roleDataEdit.validate()
          .then(() => {
            this.insertData(this.currentRow)
              .then(() => {
                this.$q.notify({
                  message: this.$t('message.addSuccess') as string,
                  color: 'positive',
                  icon: 'check_circle',
                  position: 'top',
                })
                this.isShowPermDlg = true
                this.isUnRepeatInsert = true
                this.isInserted = true
              })
              .catch((reason: string) => {
                this.$q.notify({
                  message: reason,
                  color: 'red',
                  icon: 'error',
                  position: 'top',
                })
              })
          })
      },
      /*权限弹窗相关*/
      rowClicked(rowData) {
        //默认单位
        this.defaultOrgRID = rowData.OrgRID
      },
      saveMultiplexItem() {
        this.defaultOrgRID = this.currentRow.OrgRID
        this.defaultRoleName = this.currentRow.RoleName
      },
      clearMultiplexItem() {
        this.defaultRoleName = ''
      },
      getAllRoleName() {
        return this.data
          .map((data: userPermission.IDbRole) => {
            return data.RoleName
          })
      },
      getDefFormData() {
        const data: userPermission.IDbRole = {
          RID: uuidV1(),
          OrgRID: '',
          RoleName: '',
          Creator: this.$store.state.UserRID,
          IsBuiltIn: 0,
          SortValue: 100,
          Setting: '{}',
          UpdatedAt: utcTime(),
          UpdatedDC: '',
        }
        return data
      },
      newRow() {
        this.isUnRepeatInsert = false
        this.currentRow = this.getDefFormData()
        //设置默认的单位
        this.currentRow.OrgRID = this.defaultOrgRID === DefUuid ? '' : this.defaultOrgRID
        //设置对应格式自增的角色名称
        this.currentRow.RoleName = createSelfIncreaseStr(this.defaultRoleName, this.getAllRoleName())
      },
      insertUserRoleData(data: user.IDbUserRole): Promise<boolean> {
        return new Promise((resolve, reject) => {
          const options: ICallOption = {
            OnResult: (res: crud.IDMLResult, rpcCmd: yrpcmsg.Ymsg, meta?: yrpcmsg.IGrpcMeta) => {
              log.info('IDbUserRole Insert result', res, rpcCmd, meta)
              if (res.AffectedRow === 0) {
                reject('failed')
              } else {
                // 同步到数据容器对象
                const RID = data.RID as string
                UserRole.setData(RID, data)

                resolve(true)
              }
            },
            OnServerErr: (errRpc: yrpcmsg.Ymsg) => {
              log.error('IDbUserRole Insert server error', errRpc)
              // 处理服务器响应的错误
              let reason = outputDBError(roleError, errRpc.Optstr)
              // // dborg_orgid_key 自编号重复
              // if (errRpc.Optstr.includes('dborg_orgid_key')) {
              //   reason = this.$t('message.duplicateOrgID', { name: data.RoleName })
              // }
              reject(reason)
            },
            OnLocalErr: (err: any) => {
              log.error('IDbUserRole Insert local error', err)
              reject('local')
            },
            OnTimeout: (v: any) => {
              log.warn('IDbUserRole Insert timeout', v)
              const reason = this.$t('message.requestTimeout')
              reject(reason)
            },
          }
          PrpcDbUserRole.Insert(data, options)
        })
      },
      insertIntoRolePerm(data: userPermission.IDbRolePermission): Promise<boolean> {
        return new Promise((resolve, reject) => {
          let options: ICallOption = {
            OnResult: (res: crud.IDMLResult, rpcCmd: yrpcmsg.Ymsg, meta?: yrpcmsg.IGrpcMeta) => {
              log.info('IDbRolePermission Insert result', res, rpcCmd, meta)
              if (res.AffectedRow === 0) {
                reject()
              } else {
                resolve(true)
              }
            },
            OnServerErr: (errRpc: yrpcmsg.Ymsg) => {
              log.error('IDbRolePermission Insert server error', errRpc)
              // 处理服务器响应的错误
              let reason = this.$t('message.addFailed')
              this.$q.notify({
                message: reason as string,
                position: 'top',
                type: 'negative',
              })
            },
            OnLocalErr: (err: any) => {
              log.error('IDbRolePermission Insert local error', err)
              this.$q.notify({
                message: err.toString(),
                position: 'top',
                type: 'negative',
              })
            },
            OnTimeout: (v: any) => {
              log.warn('IDbRolePermission Insert timeout', v)
              this.$q.notify({
                message: this.$t('message.requestTimeout') as string,
                position: 'top',
                type: 'warning',
              })
            },
          }
          PrpcDbRolePermission.Insert(data, options)
        })
      },
      addRoleOrgPrivilegePermission(roleData) {
        return new Promise((resolve, reject) => {
          const PermissionRID = this.$store.state.data.Permission.index['DbUserOrgPrivilege.Query']
          if (!PermissionRID) { return reject('no DbUserOrgPrivilege.Query permission') }

          let data: userPermission.IDbRolePermission
          data = {
            RID: uuidV1(),
            RoleRID: roleData.RID,
            PermissionRID,
            UpdatedAt: utcTime(),
          }

          this.insertIntoRolePerm(data)
            .then((res) => {
              window.console.log('insertIntoRolePerm,', res)
              //sync store
              RolePermission.setData(data.RID as string, data)
              return res
            })
            .then(resolve)
            .catch(reject)
        })
      },
      // 创建角色数据时，需要添加用户角色表数据，否则登录用户没有该角色权限
      insertData(data: userPermission.IDbRole): Promise<boolean> {
        return new Promise((resolve, reject) => {
          // 插入角色数据
          const _insertData = (data: userPermission.IDbRole) => {
            return new Promise((resolve, reject) => {
              const options: ICallOption = {
                OnResult: (res: crud.IDMLResult, rpcCmd: yrpcmsg.Ymsg, meta?: yrpcmsg.IGrpcMeta) => {
                  log.info('IDbRole Insert result', res, rpcCmd, meta)
                  if (res.AffectedRow === 0) {
                    reject('failed')
                  } else {
                    // 同步到数据容器对象
                    const RID = data.RID as string
                    const newRole = new userPermission.DbRole(data) as LocalRole
                    newRole.isMyRole = true
                    Role.setData(RID, newRole)

                    resolve(true)
                  }
                },
                OnServerErr: (errRpc: yrpcmsg.Ymsg) => {
                  log.error('IDbRole Insert server error', errRpc)
                  // 处理服务器响应的错误
                  let reason = outputDBError(roleError, errRpc.Optstr)
                  // // dborg_orgid_key 自编号重复
                  // if (errRpc.Optstr.includes('dborg_orgid_key')) {
                  //   reason = this.$t('message.duplicateOrgID', { name: data.RoleName })
                  // }
                  reject(reason)
                },
                OnLocalErr: (err: any) => {
                  log.error('IDbRole Insert local error', err)
                  reject('local')
                },
                OnTimeout: (v: any) => {
                  log.warn('IDbRole Insert timeout', v)
                  const options = {
                    action: this.$t('common.add'),
                    name: data.RoleName,
                  }
                  const reason = this.$t('message.timeout', options)
                  reject(reason)
                },
              }
              PrpcDbRole.Insert(data, options)
            })
          }
          _insertData(data)
            // 插入用户角色表
            .then(() => {
              const userRoleData: user.IDbUserRole = {
                RID: uuidV1(),
                // 该OrgRID必须为登录用户的单位，否则用户登录时无法请求到该数据
                OrgRID: this.$store.state.UserOrgRID,
                UserRID: this.$store.state.UserRID,
                RoleRID: data.RID,
                UpdatedAt: utcTime(),
              }
              return this.insertUserRoleData(userRoleData)
                .then(() => {
                  return this.addRoleOrgPrivilegePermission(data)
                })
                .then(resolve)
                .catch((err) => {
                  // 无法插入dbUserRole表，则删除已经插入的角色数据
                  this.deleteData(data)
                  reject(err)
                })
            })
            .catch(reject)
        })
      },
      updateData(data: userPermission.IDbRole): Promise<boolean> {
        return new Promise((resolve, reject) => {
          const options: ICallOption = {
            OnResult: (res: crud.IDMLResult, rpcCmd: yrpcmsg.Ymsg, meta?: yrpcmsg.IGrpcMeta) => {
              log.info('IDbRole Update result', res, rpcCmd, meta)
              if (res.AffectedRow === 0) {
                reject('failed')
              } else {
                // 同步到数据容器对象
                const RID = data.RID as string
                const oldData: LocalRole | undefined = cloneDeep(Role.getData(RID))
                const newData: LocalRole = new userPermission.DbRole(data)
                roleUpdateIsMyRole(newData, oldData)
                Role.setData(RID, newData)
                StrPubSub.publish(UpdateDbRole, newData, oldData)
                resolve(true)
              }
            },
            OnServerErr: (errRpc: yrpcmsg.Ymsg) => {
              log.error('IDbRole Update server error', errRpc)
              // 处理服务器响应的错误
              let reason = outputDBError(roleError, errRpc.Optstr)
              // // dborg_orgid_key 自编号重复
              // if (errRpc.Optstr.includes('dborg_orgid_key')) {
              //   reason = this.$t('message.duplicateOrgID', { name: data.RoleName })
              // }
              reject(reason)
            },
            OnLocalErr: (err: any) => {
              log.error('IDbRole Update local error', err)
              reject('local')
            },
            OnTimeout: (v: any) => {
              log.warn('IDbRole Update timeout', v)
              const options = {
                action: this.$t('common.modify'),
                name: data.RoleName,
              }
              const reason = this.$t('message.timeout', options)
              reject(reason)
            },
          }
          PrpcDbRole.Update(data, options)
        })
      },
      deleteData(data: userPermission.IDbRole): Promise<boolean> {
        return new Promise((resolve, reject) => {
          const options: ICallOption = {
            OnResult: (res: crud.IDMLResult, rpcCmd: yrpcmsg.Ymsg, meta?: yrpcmsg.IGrpcMeta) => {
              log.info('IDbRole Delete result', res, rpcCmd, meta)
              if (res.AffectedRow === 0) {
                reject('failed')
              } else {
                // 同步到数据容器对象
                const key = data.RID as string
                Role.deleteData(key)

                // 角色被删除后，同步删除用户角色数据
                const userRoleData: user.IDbUserRole[] = UserRole.getDataList()
                userRoleData.filter(v => v.RoleRID === key)
                  .forEach(v => {
                    UserRole.deleteData(v.RID as string)
                  })

                resolve(true)
              }
            },
            OnServerErr: (errRpc: yrpcmsg.Ymsg) => {
              log.error('IDbRole Delete server error', errRpc)
              // 处理服务器响应的错误
              let reason = outputDBError(roleError, errRpc.Optstr)
              // // fkdborgparentrid 该单位为其他单位的上级，需要删除所有下级单位才能删除
              // if (errRpc.Optstr.includes('fkdborgparentrid')) {
              //   reason = this.$t('message.deleteHasChildUnit', { name: data.RoleName })
              // }
              reject(reason)
            },
            OnLocalErr: (err: any) => {
              log.error('IDbRole Delete local error', err)
              reject('local')
            },
            OnTimeout: (v: any) => {
              log.warn('IDbRole Delete timeout', v)
              const options = {
                action: this.$t('common.delete'),
                name: data.RoleName,
              }
              const reason = this.$t('message.timeout', options)
              reject(reason)
            },
          }
          PrpcDbRole.Delete(data, options)
        })
      },
      batchAddData(dbList: userPermission.IDbRole[], progress?: (value: number) => void): Promise<any> {
        const parentCache: { [key: string]: any } = {}
        const CreatorCache: { [key: string]: any } = {}
        return new Promise((resolve) => {
          // 缓存有异常的数据
          const errorDataList: userPermission.IDbRole[] = []
          // 使用迭代器逐个添加到数据库
          const it = dbList[Symbol.iterator]()
          // 当前处理的第几个数据
          let currentCount = 0
          const insert = async (item) => {
            // 进度信息，当前处理第几个数据
            progress?.(currentCount++)
            const { done, value } = item
            // 已经处理过每一个数据
            if (done) {
              // 已经处理过每一个数据，结束
              return resolve(errorDataList)
            }

            // 合并默认参数
            const data: userPermission.IDbRole = this.getDefFormData()
            Object.assign(data, value)

            // // 角色允许重复
            // // 过滤本地已经存在的数据
            // const localData = Role.getDataByIndex(data.RoleName as string)
            // if (localData) {
            //   value.$info = this.$t('message.duplicateData')
            //   errorDataList.push(value)
            //   // 处理下一个数据
            //   insert(it.next())
            //   return
            // }

            // 重置单位上级RID,当前OrgRID为上级单位简称，需要转换为对应的UUID
            // 查找上级是否为本地数据，暂时不处理没有权限的上级
            const parentOption = parentCache[data.OrgRID as string] ||
              this.parentOptions.find(item => item.label === data.OrgRID)
            if (!parentOption) {
              value.$info = this.$t('message.parentUnitErr')
              errorDataList.push(value)
              // 处理下一个数据
              insert(it.next())
              return
            }
            parentCache[data.OrgRID as string] = parentOption
            data.OrgRID = parentOption.value

            // 处理创建者数据
            const creatorOption: user.IDbUser | undefined = CreatorCache[data.Creator + ''] ||
              this.users.find(item => item.label === data.OrgRID)
            if (creatorOption) {
              CreatorCache[data.Creator + ''] = creatorOption
              data.Creator = creatorOption.RID
            } else {
              data.Creator = this.$store.state.UserRID
            }

            // 转换数字类型参数
            const CoverPropList = ['SortValue']
            this.coverPropToNumber(data, CoverPropList)

            const CoverPropStringList = ['OrgRID', 'RoleName']
            this.coverPropToString(data, CoverPropStringList)

            const err = this.checkDataByRules(data, this.rules)
            if (err === true) {
              // 添加数据到数据库
              await this.insertData(data)
                .catch((error) => {
                  value.$info = error
                  errorDataList.push(value)
                })
            } else {
              value.$info = err
              errorDataList.push(value)
            }
            // 处理下一个数据
            insert(it.next())
          }
          insert(it.next())
        })
      },
    },
  })
</script>

<style lang="scss">
  .form-role {
    width: 65vw;
    max-width: 560px;
  }

  .sticky-header {
    position: sticky;
    top: 0;
  }

  .card-section-height {
    height: 50vh;
  }

  .inner-page-tabs {
    width: 100px;
  }
</style>

<template>
  <fancytree
    class="devices-tree"
    :id="deviceTreeId"
    @tree-init="treeInit"
    @select="select"
    @dblclick="onDblclick"
    :contextMenu="contextMenu"
    :options="deviceTreeOptions"
    @contextMenu-select="contextMenuSelectHandler"
    ref="deviceTree"
  />
</template>

<script lang="ts">
  import { defineComponent, nextTick } from 'vue'
  import { Fancytree } from '@components/fancytree'
  import { StrPubSub } from 'ypubsub'
  import { org } from '@ygen/org'
  import { BysMarker, Controller, Unit, BysMarkerAbnormalRidSet } from '@services/dataStore'
  import globalConfig, { DefUuid } from '@src/config'
  import {
    createBysMarkerNodeTitle,
    createBysMarkerStatusCls,
    createControllerNodeTitle,
    createSliceBysMarkerNodes,
    createSliceControllerNodes,
    createTreeUnitNodes,
    deleteNode,
    deviceTreeId,
    //getAllSelectNodes,
    insertNode,
    lazyLoadBysMarkerNodes,
    lazyLoadControllerNodes,
    lazyLoadUnitNodes,
    redrawViewport,
    refreshUnitCounter,
    renderNodeTitle,
    sortNodeChildren,
    sortTree,
    updateNode,
    updateParentsNodeTitle,
    clearNotInstallDeviceBysMarkers,
    reloadNotInstallDeviceBysMarkers,
    updateUnitNodeBadgeInfo,
    lazyLoadChildren,
  } from '@utils/tree'
  import { bysdb } from '@ygen/bysdb'
  import { DataName } from '@src/store'
  import {
    createControllerMarkerFeature,
    createDeviceMarkerFeature,
    flyTo,
    CustomLayerSourcesV2,
    filterMarkerByTreeNodeSelect,
    pointFeatureMap2List,
    setPointLayerData, getLngLatFromBysMarker,
  } from '@utils/map'
  import { QueryFinishStatus, syncMarkerDataAndPartialUpdateSetting } from '@services/queryData'
  import {
    checkBysMarkerIsAlarm,
    checkReportingIsPowerOn,
    ControllerDeviceType,
    isHaveMaintainPerm,
    MarkerType,
    queryAllControllerData,
    secondConfirmDialog,
    nodeState,
    checkoutAbnormalBysMarker,
    updateMarkerStatusAfterAction,
    checkIs4GMarker,
  } from '@utils/common'
  import {
    BysMarkerDumpingAlarm,
    CleanDeviceTree,
    ControllerOffline,
    ControllerOnline,
    ControllerStateChanged,
    DeleteDbBysMarker,
    DeleteDbController,
    DeleteDbOrg,
    DeviceAlarmTest,
    InitDeviceTreeFinish,
    InsertDbBysMarker,
    InsertDbController,
    InsertDbOrg,
    Jump2Node,
    MarkerHasSomeAbnormal,
    OpenMenu,
    QueryBysMarkerFinish,
    QueryControllerFinish,
    QueryUnitFinish,
    RemoveBysMarkerAlarm,
    removeBysMarkerAlarmCmd,
    ShowNotInstalledMarker,
    SortDeviceTree,
    UpdateBysMarkerTreeNodeBattery,
    UpdateDbBysMarker,
    UpdateDbController,
    UpdateDbOrg,
    ReloginDataQueryStarted,
  } from '@utils/pubSubSubject'
  import {
    changeDefaultChannelNo,
    isLonginController,
    markerSetRemoteKillOrActive,
    pingController
  } from '@services/controller'
  import { BysMarkerAndUpdateInfo, ControllerAndUpdateCmd } from '@utils/bysdb.type'
  import { contextMenuType } from '@components/fancytree/common'
  import { countMarkers, countUnitMarker, deleteMarkerCount, updateAllParOrgNode } from '@utils/markerCounter'
  import { DbName } from '@utils/permission'
  import { getGCJ02LngLat } from '@utils/gcoord'
  import { bysproto } from '@ygen/controller'
  import { sysUtcTime, utcTime } from '@utils/dayjs'

  let deviceTree: Fancytree.Fancytree | undefined = undefined


  enum ContextmenuCmd {
    RemoveAlarm = 'RemoveAlarm',
    ChangeDefaultChannelNo2One = 'ChangeDefaultChannelNo2One',
    ChangeDefaultChannelNo2Two = 'ChangeDefaultChannelNo2Two',
    Ping = 'Ping',
    ChangeServerAddress = 'ChangeServerAddress',
    KillRemotely = 'KillRemotely',
    RemoteStun = 'RemoteStun',
    RemoteActivition = 'RemoteActivition',
    CameraRemoteStun = 'CameraRemoteStun',
    CameraRemoteActive = 'CameraRemoteActive',
  }

  // 在线状态图标
  const controllerDefaultNetworkType = {
    1: 'bys-network',
    2: 'bys-network-4G',
    3: 'bys-repeater',
  }

  export default defineComponent({
    name: 'DevicesTree',
    data() {
      return {
        selected: [],
        deviceTreeId,
      }
    },
    computed: {
      contextMenu() {
        return {
          menu: [],
          beforeOpen: (event, ui) => {
            const node = $.ui.fancytree.getNode(ui.target)
            if (!node) {
              return false
            }
            // 默认的公共菜单
            let menu: Array<contextMenuType> = []
            let ChangeServerAddress = true
            if (node.data.customType === DataName.BysMarker && (checkIs4GMarker(node.data.customMarkerType))) {
              const bysMarker = BysMarker.getData(node.key) as BysMarkerAndUpdateInfo
              // 解除报警菜单
              const isAlarming =  checkBysMarkerIsAlarm(bysMarker)
              if (isAlarming) {
                menu.push({ title: this.$t('fancytree.removeAlarm'), cmd: ContextmenuCmd.RemoveAlarm })
              }
              // 遥毙/遥晕/遥活菜单，已经遥毙时，没有遥晕/遥活菜单
              if (bysMarker.HasInstallDevice) {
                if (bysMarker.MarkerDisabled === 0) {
                  menu.push({ title: this.$t('form.remoteStun'), cmd: ContextmenuCmd.RemoteStun })
                  menu.push({ title: this.$t('form.killRemotely'), cmd: ContextmenuCmd.KillRemotely })
                  // 如果是4G界桩Pro，添加摄像机遥晕菜单
                  if (bysMarker.MarkerType === MarkerType.Net4GPro) {
                    if (bysMarker.CameraDisabled === 0) {
                      menu.push({ title: this.$t('form.cameraRemoteStun'), cmd: ContextmenuCmd.CameraRemoteStun })
                    } else if (bysMarker.CameraDisabled === 2) {
                      menu.push({ title: this.$t('form.cameraRemoteActive'), cmd: ContextmenuCmd.CameraRemoteActive })
                    }
                  }
                } else if (bysMarker.MarkerDisabled === 2) {
                  menu.push({ title: this.$t('form.remoteActivition'), cmd: ContextmenuCmd.RemoteActivition })
                  menu.push({ title: this.$t('form.killRemotely'), cmd: ContextmenuCmd.KillRemotely })
                }
              }
            } else if (node.data.customType === DataName.Controller) {
              if (isHaveMaintainPerm()) {
                const modifyServerAddrCmd = {
                  title: this.$t('menus.modifyServerAddr'),
                  cmd: ContextmenuCmd.ChangeServerAddress,
                }
                menu = [
                  {
                    title: this.$t('form.changeDefaultChannelNo2One'),
                    cmd: ContextmenuCmd.ChangeDefaultChannelNo2One,
                  },
                  {
                    title: this.$t('form.changeDefaultChannelNo2Two'),
                    cmd: ContextmenuCmd.ChangeDefaultChannelNo2Two,
                  },
                  { title: this.$t('form.Ping'), cmd: ContextmenuCmd.Ping },
                  modifyServerAddrCmd,
                ]
                const controller = Controller.getData(node.key) as ControllerAndUpdateCmd
                const isOnline = controller.controllerState?.online ?? false
                const isLogin = isLonginController(controller.controllerState?.NetworkType as number)
                ChangeServerAddress = isOnline && isLogin
              }
            }
            this.$refs.deviceTree?.replaceMenu(menu)
            this.$refs.deviceTree?.enableEntry(ContextmenuCmd.ChangeServerAddress, ChangeServerAddress)
            node.setActive()
            ui.extraData.node = node
          },
        }
      },
      deviceTreeOptions() {
        return {
          lazyLoad: this.lazyLoad,
        }
      },
      // 创建图层Feature方法策略属性
      createFeatureMethods() {
        return {
          [DataName.Controller]: createControllerMarkerFeature,
          [DataName.BysMarker]: createDeviceMarkerFeature,
        }
      },
    },
    methods: {
      lazyLoad(event, data) {
        if (!data.node) {
          data.result = []
          return
        }

        // 通过key查找该节点的所有直接子级单位、界桩、控制器节点
        const bysMarkerNodes = lazyLoadBysMarkerNodes(data.node.key)
        const controllerNodes = lazyLoadControllerNodes(data.node.key)
        const unitNodes = lazyLoadUnitNodes(data.node.key)
        data.result = [...bysMarkerNodes, ...controllerNodes, ...unitNodes]

        // 对懒加载节点的子级节点进行排序
        this.$nextTick(() => {
          sortNodeChildren(data.node)
        })
      },
      removeBysMarkerAlarmCmd(node: Fancytree.FancytreeNode) {
        const bysMarker = BysMarker.getData(node.key) as bysdb.IDbBysMarker | undefined
        const controller = Controller.getData(bysMarker?.ControllerRID as string) as bysdb.IDbController | undefined

        StrPubSub.publish(removeBysMarkerAlarmCmd, bysMarker, controller)
      },
      async markerRemoteKillOrActive(shutDownType: number, node: Fancytree.FancytreeNode) {
        const dbMarker = BysMarker.getData(node.key) as bysdb.IDbBysMarker | undefined
        if (!dbMarker) {
          return
        }
        const isOk = await markerSetRemoteKillOrActive(shutDownType, shutDownType, dbMarker)
        if (isOk) {
          let setting = {}
          if (shutDownType !== 0 ) {
            setting = {
              MarkerDisabledTime: sysUtcTime(),
              CameraDisabledTime: sysUtcTime()
            }
          }
          syncMarkerDataAndPartialUpdateSetting(dbMarker.RID as string, setting)
        }
      },
      async cameraRemoteStun(cmdVal: number, node: Fancytree.FancytreeNode) {
        const bysMarker = BysMarker.getData(node.key) as BysMarkerAndUpdateInfo | undefined
        if (!bysMarker) {
          return
        }
        const isOk = await markerSetRemoteKillOrActive(bysMarker.MarkerDisabled ?? 0, cmdVal, bysMarker)
        if (isOk) {
          let setting = {}
          if (cmdVal !== 0) {
            setting = {
              CameraDisabledTime: sysUtcTime()
            }
          }
          syncMarkerDataAndPartialUpdateSetting(bysMarker.RID as string, setting)
        }
      },
      async contextMenuSelectHandler(uiCmd: Record<string, any>, node: Fancytree.FancytreeNode) {
        const htmlMsg = '<i class="q-icon text-warning notranslate material-icons" aria-hidden="true" role="presentation" style="font-size: 2rem; padding-right: 0.5rem;">warning</i>'
        const isOk = await secondConfirmDialog(htmlMsg + this.$t('menus.confirmCmd', { cmd: uiCmd.item[0].innerText }), this.$t('common.confirm'), this.$t('common.cancel'), { html: true})
        if (isOk) {
          if (uiCmd.cmd === ContextmenuCmd.KillRemotely) {
            this.markerRemoteKillOrActive(1, node)
            return
          }
          if (uiCmd.cmd === ContextmenuCmd.RemoteStun) {
            this.markerRemoteKillOrActive(2, node)
            return
          }
          if (uiCmd.cmd === ContextmenuCmd.RemoteActivition) {
            this.markerRemoteKillOrActive(0, node)
            return
          }

          if (uiCmd.cmd === ContextmenuCmd.CameraRemoteStun) {
            // 添加一个摄像机遥晕的方法在此处执行
            this.cameraRemoteStun(2, node)
            return
          }

          if (uiCmd.cmd === ContextmenuCmd.CameraRemoteActive) {
            // 添加一个摄像机遥活的方法在此处执行
            this.cameraRemoteStun(0, node)
            return
          }

          if (uiCmd.cmd === ContextmenuCmd.RemoveAlarm) {
            this.removeBysMarkerAlarmCmd(node)
            return
          }
          if (uiCmd.cmd === ContextmenuCmd.ChangeDefaultChannelNo2One ||
            uiCmd.cmd === ContextmenuCmd.ChangeDefaultChannelNo2Two) {//修改对界桩的通道的信道
            const controller = Controller.getData(node.key) as bysdb.IDbController | undefined
            if (!controller) {
              return
            }
            const channel = (uiCmd.cmd === ContextmenuCmd.ChangeDefaultChannelNo2One ? 1 : 2)
            changeDefaultChannelNo(controller as bysdb.IDbController, channel)
            return
          }
          if (uiCmd.cmd === ContextmenuCmd.Ping) {
            const controller = Controller.getData(node.key) as bysdb.IDbController | undefined
            if (!controller) {
              return
            }
            pingController(controller)
            return
          }
          if (uiCmd.cmd === ContextmenuCmd.ChangeServerAddress) {
            const controller = Controller.getData(node.key) as ControllerAndUpdateCmd
            StrPubSub.publish(OpenMenu, 'ControllerServerAddress', (vm) => {
              vm.formData.controller = controller.ControllerHWID
              vm.visible = true
            })

            return
          }
        }
      },
      // 单位节点同步操作方法
      deleteDbOrgNode(data: org.IDbOrg) {
        const RID = data.RID as string
        deleteNode(deviceTree, RID)
        deleteMarkerCount(RID)
        updateAllParOrgNode()
        refreshUnitCounter()
      },
      updateDbOrgNode(data: org.IDbOrg, oldData: org.IDbOrg) {
        const RID = data.RID as string
        countUnitMarker(RID)
        updateAllParOrgNode()
        updateNode(deviceTree, data, oldData, {
          dbName: DataName.Unit,
        })
        refreshUnitCounter()
      },
      insertDbOrgNode(data: org.IDbOrg) {
        insertNode(deviceTree, data, {
          dbName: DataName.Unit,
        })
      },
      updateUnitCounterByOrgRID(OrgRID: string) {
        const data = Unit.getParent(OrgRID)
        if (!data) {
          return
        }
        this.updateDbOrgNode(data, data)
        updateParentsNodeTitle(deviceTree, data)
      },
      // 界桩操作方法
      deleteDbBysMarkerNode(data: bysdb.IDbBysMarker) {
        deleteNode(deviceTree, data.RID as string)
        this.updateUnitCounterByOrgRID(data.OrgRID + '')
      },
      updateDbBysMarkerNode(data: BysMarkerAndUpdateInfo, oldData: BysMarkerAndUpdateInfo) {
        updateNode(deviceTree, data, oldData, {
          dbName: DataName.BysMarker,
          icon: 'iconfont bys-jiezhuang ' + createBysMarkerStatusCls(data),
        })
        this.updateUnitCounterByOrgRID(data.OrgRID + '')
        if (oldData.OrgRID !== data.OrgRID) {
          this.updateUnitCounterByOrgRID(oldData.OrgRID + '')
        }

        const isAbnormalAndInstall = data.HasInstallDevice ? checkoutAbnormalBysMarker(data, utcTime()) : false
        if (isAbnormalAndInstall) {
          this.changeMarkerNodeIcon(data.RID as string, nodeState.abnormal)
        }

        // filterMarkerByTreeNodeSelect(DataName.BysMarker)
      },
      insertDbBysMarkerNode(data: bysdb.IDbBysMarker) {
        insertNode(deviceTree, data, { dbName: DataName.BysMarker })
        this.updateUnitCounterByOrgRID(data.OrgRID + '')
      },
      // 控制器操作方法
      deleteDbControllerNode(data: bysdb.IDbController) {
        deleteNode(deviceTree, data.RID as string)
      },
      updateDbControllerNode(data: bysdb.IDbController, oldData: bysdb.IDbController) {
        updateNode(deviceTree, data, oldData, {
          dbName: DataName.Controller,
        })
      },
      insertDbControllerNode(data: bysdb.IDbController) {
        insertNode(deviceTree, data, { dbName: DataName.Controller })
      },
      getParentUnitRid(RID): string {
        if (RID !== undefined) {
          return (Unit.getData(RID) as org.IDbOrg)?.OrgRID ?? ''
        }
        return ''
      },
      expandNode(key: string) {
        let node = deviceTree?.getNodeByKey(key)
        if (!node) {
          const pRid = this.getParentUnitRid(key)
          this.expandNode(pRid)
        }
        node = deviceTree?.getNodeByKey(key)
        node?.setExpanded()
      },
      jump2Node(key: string, dbName = DbName.DbBysMarker) {
        const OrgRID = (dbName === DbName.DbController ? Controller.getData(key)?.OrgRID
          : BysMarker.getData(key)?.OrgRID) ?? ''
        this.expandNode(OrgRID)
        setTimeout(() => {
          deviceTree?.getNodeByKey(key)?.setActive(true)
        }, 100)
      },
      treeInit(tree) {
        deviceTree = tree
        StrPubSub.publish(InitDeviceTreeFinish)

        StrPubSub.subscribe(Jump2Node, this.jump2Node)
        StrPubSub.subscribe(DeleteDbOrg, this.deleteDbOrgNode)
        StrPubSub.subscribe(UpdateDbOrg, this.updateDbOrgNode)
        StrPubSub.subscribe(InsertDbOrg, this.insertDbOrgNode)

        StrPubSub.subscribe(DeleteDbBysMarker, this.deleteDbBysMarkerNode)
        StrPubSub.subscribe(UpdateDbBysMarker, this.updateDbBysMarkerNode)
        StrPubSub.subscribe(InsertDbBysMarker, this.insertDbBysMarkerNode)

        StrPubSub.subscribe(DeleteDbController, this.deleteDbControllerNode)
        StrPubSub.subscribe(UpdateDbController, this.updateDbControllerNode)
        StrPubSub.subscribe(InsertDbController, this.insertDbControllerNode)
      },
      async select(event, data) {
        // 加载懒加载的子节点
        await lazyLoadChildren(data.node)
        // 所有子级节点的key
        const childrenKeys: string[] = []
        // 待查找子级节点的父节点队列
        const nodes: Fancytree.FancytreeNode[] = [data.node]
        while (nodes.length > 0) {
          const node = nodes.shift()
          if (node) {
            childrenKeys.push(node.key)

            const children = node.getChildren() ?? []
            nodes.push(...children)
          }
        }

        // 更新地图图层数据源属性
        const isSelected = data.node.isSelected()
        for (let layer in CustomLayerSourcesV2) {
          const layerFeatureMap = CustomLayerSourcesV2[layer]
          layerFeatureMap.forEach((feature) => {
            if (childrenKeys.includes(feature.properties.RID)) {
              feature.properties.TreeNodeSelected = isSelected
            }
          })

          // 更新数据源
          setPointLayerData(layer, pointFeatureMap2List(layerFeatureMap))
          // 更新地图图层过滤器setFilter
          filterMarkerByTreeNodeSelect(layer)
        }
      },
      flyToMarker(lngLat: [number, number]) {
        flyTo(lngLat, globalConfig.map?.getZoom())
      },
      gotoBysMarker(node) {
        // 找到原始数据，得到经纬度坐标
        const data = BysMarker.getData(node.key) as BysMarkerAndUpdateInfo | undefined
        if (!data) {
          return
        }

        // 以得到的经纬度坐标重置地图中心点，实现跳转
        const lngLat = getLngLatFromBysMarker(data)
        this.flyToMarker(lngLat)
      },
      gotoControllerMarker(node) {
        // 找到原始数据，得到经纬度坐标
        const data = Controller.getData(node.key) as bysdb.IDbController | undefined
        if (!data) {
          return
        }
        // 以得到的经纬度坐标重置地图中心点，实现跳转
        this.flyToMarker(getGCJ02LngLat(data))
      },
      getExecDblclickEvent(customType: DataName = DataName.BysMarker): Function {
        const Events = {
          [DataName.Controller]: this.gotoControllerMarker,
          [DataName.BysMarker]: this.gotoBysMarker,
        }
        return Events[customType] || (() => undefined)
      },
      onDblclick(event, data) {
        const node = data?.node
        if (!node) {
          return
        }

        // 根据类型执行不同的逻辑
        const customType = node.data?.customType
        this.getExecDblclickEvent(customType)(node)
      },

      waitDeviceTreeInit() {
        return new Promise((resolve) => {
          if (deviceTree) {
            return resolve(true)
          }

          // 没有初始化树，等待树的初始化
          StrPubSub.subscribeOnce(InitDeviceTreeFinish, () => {
            resolve(true)
          })
        })
      },
      // 初始树各类型数据节点
      initUnitNodes() {
        this.waitDeviceTreeInit()
          .then(() => {
            const unitData: Array<org.IDbOrg> = Unit.getDataList().filter(item => item.RID !== DefUuid)
            const unitNodes = createTreeUnitNodes(unitData)
            deviceTree?.rootNode?.addChildren?.(unitNodes)
            sortTree(deviceTree)
          })
      },
      initControllerNodes() {
        // 如果没有维护权限，则设备树形不显示控制器节点
        if (!isHaveMaintainPerm()) {
          return
        }
        this.waitDeviceTreeInit()
          .then(() => {
            const controllers: Array<bysdb.IDbController> = Controller.getDataList()
            createSliceControllerNodes(deviceTree, controllers)
            sortTree(deviceTree)
            setTimeout(() => {
              this.initAllControllerIcon()
            }, 0)
          })
      },
      initBysMarkerNodes() {
        // 按单位统计界桩数量
        countMarkers()
        refreshUnitCounter()
        this.waitDeviceTreeInit()
          .then(() => {
            createSliceBysMarkerNodes(deviceTree!, BysMarker.getDataList())
            sortTree(deviceTree)
            // 初始化时已经处理未安装的界桩，不需要重复处理
            // this.toggleShowNotInstalledMarker()
          })
      },

      changeMarkerNodeIcon(key: string, state: nodeState) {
        if (!deviceTree) {
          return
        }

        let node = deviceTree.getNodeByKey(key)
        if (!node) return
        // @ts-ignore
        node.icon = 'iconfont bys-jiezhuang ' + state
        if (state === 'warning' || state === 'removeWarning') {
          // @ts-ignore
          node.selected = true
          node.unselectable = true
          node.unselectableStatus = true
        } else {
          node.unselectable = undefined
          node.unselectableStatus = undefined
        }
        node.renderTitle()
      },

      changeControllerIcon(key: string, networkType, isOnline = false, isError = false) {
        /***
         * 1.有线
         * 2.4G
         * 3.FSK（中继）
         *
         * 4.有问题的控制器
         */
        if (!deviceTree) {
          return
        }
        let node = deviceTree.getNodeByKey(key)
        if (!node) return
        const cls = `iconfont ${controllerDefaultNetworkType[networkType]} `

        //添加背景色
        let icon = ''
        if (isOnline) {
          if (isError) {
            icon = cls + nodeState.onlineError
          } else {
            icon = cls + nodeState.online
          }
        } else {
          icon = cls + nodeState.offlineError
        }
        // @ts-ignore
        node.icon = icon
        node.renderTitle()
      },

      //订阅online / offline事件，改变信道号
      changeControllerTitle(controller: bysdb.IDbController) {
        if (!deviceTree) {
          return
        }
        let node = deviceTree.getNodeByKey(controller.RID as string)
        if (!node) return
        node.title = createControllerNodeTitle(controller)
        node.renderTitle()
      },
      onlineController(controller: bysdb.IDbController | bysdb.IDbBysMarker, loginReq: bysproto.IBloginReq) {
        if (loginReq.DeviceType === ControllerDeviceType.Net4g) {
          // todo 4g界桩更新节点在线状态
        } else {
          this.changeControllerTitle(controller)
        }
      },
      offlineController(controller: bysdb.IDbController | bysdb.IDbBysMarker, loginReq: bysproto.IBloginReq) {
        if (loginReq.DeviceType === ControllerDeviceType.Net4g) {
          // todo 4g界桩更新节点在线状态
        } else {
          this.changeControllerTitle(controller)
        }
      },

      initAllControllerIcon() {
        const allControllers: Array<ControllerAndUpdateCmd> = queryAllControllerData()
        for (const item of allControllers) {
          this.changeControllerIcon(item.RID as string, item.DefaultNetworkType, item.controllerState?.online,
            item.Error?.err,
          )
        }
      },

      controllerStateChanged(controller: ControllerAndUpdateCmd) {
        this.changeControllerIcon(controller.RID as string,
          controller.controllerState?.NetworkType ?? controller.DefaultNetworkType,
          controller.controllerState?.online,
          controller.Error?.err,
        )
      },

      bysMarkerAlarmSig(bysMarker: BysMarkerAndUpdateInfo) {
        // 4g界桩的开机上报的指令，不作为报警处理
        if (checkIs4GMarker(bysMarker.MarkerType)) {
          if (checkReportingIsPowerOn(bysMarker.markerInfo ?? {}, true)) return
        }

        if (bysMarker?.removeAlarm) {
          return
        }
        if (!checkBysMarkerIsAlarm(bysMarker)) {
          this.changeMarkerNodeIcon(bysMarker?.RID as string, nodeState.removeWarning)
          return
        }
        this.changeMarkerNodeIcon(bysMarker?.RID as string, nodeState.warning)
      },
      removeBysMarkerAlarmSig(bysMarker: BysMarkerAndUpdateInfo) {
        const isAbnormal = checkoutAbnormalBysMarker(bysMarker, utcTime())
        this.changeMarkerNodeIcon(bysMarker.RID as string, isAbnormal ? nodeState.abnormal : nodeState.rmAlarm)
      },
      removeBysMarkerAlarmCmdSig(bysMarker: bysdb.IDbBysMarker) {
        this.changeMarkerNodeIcon(bysMarker?.RID as string, nodeState.removeWarning)
      },
      updateBysMarkerTreeNodeBattery(bysMarker: BysMarkerAndUpdateInfo) {
        if (!deviceTree) {
          return
        }
        const key = bysMarker.RID as string
        const node = deviceTree.getNodeByKey(key)
        const title = createBysMarkerNodeTitle(bysMarker)
        const icon = 'iconfont bys-jiezhuang ' + createBysMarkerStatusCls(bysMarker)
        // @ts-ignore
        node && (node.icon = icon)
        renderNodeTitle(node, title)
      },
      toggleShowNotInstalledMarker() {
        if (!deviceTree) {
          return
        }

        if (this.$store.state.Settings.showNotInstalledMarker) {
          // 加载未安装的界桩节点
          reloadNotInstallDeviceBysMarkers(deviceTree)
        } else {
          // 清除未安装界榔节点
          clearNotInstallDeviceBysMarkers(deviceTree)
        }

        // 更新单位节点数据统计
        updateUnitNodeBadgeInfo(deviceTree)
        filterMarkerByTreeNodeSelect(DataName.BysMarker)
      },
      changeDeviceTreeNodeState(dbMarker: BysMarkerAndUpdateInfo, state: nodeState) {
        this.changeMarkerNodeIcon(dbMarker.RID as string, state)
      },
      subscribeDataEvents() {
        // 取消之前的订阅，避免重复订阅
        StrPubSub.unsubscribe(QueryUnitFinish, this.initUnitNodes)
        StrPubSub.unsubscribe(QueryControllerFinish, this.initControllerNodes)
        StrPubSub.unsubscribe(QueryBysMarkerFinish, this.initBysMarkerNodes)
        
        // 根据当前状态决定是直接初始化还是订阅事件
        if (QueryFinishStatus[DataName.Unit]) {
          this.initUnitNodes()
        } else {
          StrPubSub.subscribe(QueryUnitFinish, this.initUnitNodes)
        }

        if (QueryFinishStatus[DataName.Controller]) {
          this.initControllerNodes()
        } else {
          StrPubSub.subscribe(QueryControllerFinish, this.initControllerNodes)
        }

        if (QueryFinishStatus[DataName.BysMarker]) {
          this.initBysMarkerNodes()
        } else {
          StrPubSub.subscribe(QueryBysMarkerFinish, this.initBysMarkerNodes)
        }
      },
    },
    beforeMount(): void {
      this.subscribeDataEvents()
      
      StrPubSub.subscribe(CleanDeviceTree, () => {
        // @ts-ignore
        deviceTree?.clear()
        redrawViewport(deviceTree)
      })

      StrPubSub.subscribe(SortDeviceTree, () => {
        sortTree(deviceTree)
      })

      StrPubSub.subscribe(ControllerStateChanged, this.controllerStateChanged)

      StrPubSub.subscribe(BysMarkerDumpingAlarm, this.bysMarkerAlarmSig)
      StrPubSub.subscribe(DeviceAlarmTest, this.bysMarkerAlarmSig)
      StrPubSub.subscribe(removeBysMarkerAlarmCmd, this.removeBysMarkerAlarmCmdSig)
      StrPubSub.subscribe(RemoveBysMarkerAlarm, this.removeBysMarkerAlarmSig)
      StrPubSub.subscribe(ControllerStateChanged, this.changeControllerTitle)
      StrPubSub.subscribe(ControllerOnline, this.onlineController)
      StrPubSub.subscribe(ControllerOffline, this.offlineController)
      StrPubSub.subscribe(UpdateBysMarkerTreeNodeBattery, this.updateBysMarkerTreeNodeBattery)
      StrPubSub.subscribe(ShowNotInstalledMarker, this.toggleShowNotInstalledMarker)

      StrPubSub.subscribe(MarkerHasSomeAbnormal, this.changeDeviceTreeNodeState)
      
      // 不再使用LoginSuccess事件，改用ReloginDataQueryStarted事件
      // 监听重新登录数据查询开始事件，确保在数据重置后再订阅
      StrPubSub.subscribe(ReloginDataQueryStarted, () => {
        this.subscribeDataEvents()
      })
    },
    mounted() {
      // 针对从404页面返回到首页的情况做出的处理
      nextTick(() => {
        if (BysMarkerAbnormalRidSet.size > 0) {
          this.waitDeviceTreeInit().
            then(() => {
              for (const rid of BysMarkerAbnormalRidSet) {
                const data = BysMarker.getData(rid)
                updateMarkerStatusAfterAction(data)
              }
            })
        }
      })
    },
    beforeUnmount(): void {
      StrPubSub.unsubscribe(InitDeviceTreeFinish)
      StrPubSub.unsubscribe(QueryUnitFinish)
      StrPubSub.unsubscribe(QueryBysMarkerFinish)
      StrPubSub.unsubscribe(QueryControllerFinish)
      StrPubSub.unsubscribe(ReloginDataQueryStarted)

      StrPubSub.unsubscribe(DeleteDbOrg, this.deleteDbOrgNode)
      StrPubSub.unsubscribe(UpdateDbOrg, this.updateDbOrgNode)
      StrPubSub.unsubscribe(InsertDbOrg, this.insertDbOrgNode)

      StrPubSub.unsubscribe(DeleteDbBysMarker, this.deleteDbBysMarkerNode)
      StrPubSub.unsubscribe(UpdateDbBysMarker, this.updateDbBysMarkerNode)
      StrPubSub.unsubscribe(InsertDbBysMarker, this.insertDbBysMarkerNode)

      StrPubSub.unsubscribe(DeleteDbController, this.deleteDbControllerNode)
      StrPubSub.unsubscribe(UpdateDbController, this.updateDbControllerNode)
      StrPubSub.unsubscribe(InsertDbController, this.insertDbControllerNode)

      StrPubSub.unsubscribe(CleanDeviceTree)
      StrPubSub.unsubscribe(SortDeviceTree)

      StrPubSub.unsubscribe(ControllerStateChanged, this.controllerStateChanged)
      StrPubSub.unsubscribe(BysMarkerDumpingAlarm, this.bysMarkerAlarmSig)
      StrPubSub.unsubscribe(DeviceAlarmTest, this.bysMarkerAlarmSig)
      StrPubSub.unsubscribe(removeBysMarkerAlarmCmd, this.removeBysMarkerAlarmCmdSig)
      StrPubSub.unsubscribe(RemoveBysMarkerAlarm, this.removeBysMarkerAlarmSig)

      StrPubSub.unsubscribe(ControllerStateChanged, this.changeControllerTitle)
      StrPubSub.unsubscribe(ControllerOnline, this.onlineController)
      StrPubSub.unsubscribe(ControllerOffline, this.offlineController)

      StrPubSub.unsubscribe(UpdateBysMarkerTreeNodeBattery, this.updateBysMarkerTreeNodeBattery)
      StrPubSub.unsubscribe(ShowNotInstalledMarker, this.toggleShowNotInstalledMarker)

      StrPubSub.unsubscribe(MarkerHasSomeAbnormal, this.changeDeviceTreeNodeState)

      deviceTree = undefined
    },
    components: {
      Fancytree,
    },
    watch: {
      '$i18n.locale'(/*val, oldVal*/) {
        // 重新加载树节点的tooltips
        refreshUnitCounter()
      },
    },
  })
</script>

<style lang="scss">
  @import "quasar/src/css/variables.sass";
  @import "quasar/src/css/core/colors.sass";
  @import "quasar/src/css/core/size.sass";
  @import "quasar/src/components/badge/QBadge.sass";

  @keyframes animate_demo {
    50% {
      color: $negative;
    }

    100% {
      color: rgba(0, 255, 0, 1);
    }
  }

  .warning {
    animation: animate_demo 1s ease infinite;
  }

  .removeWarning {
    color: $deep-orange-14;
  }

  .onlineError {
    color: $yellow-9;
  }


  .offlineError {
    color: $dark;
  }


  .online {
    color: rgba(0, 255, 0, 1);
  }

  .uninstall {
    color: #ba68c8;
  }

  .uninstall-stone {
    color: gray;
  }

  .marker-blue {
    color: #1afa30;
  }

  .abnormal {
    color: orange;
  }

  .unit-badge {
    @extend .q-ml-xs;
    @extend .q-badge;
    @extend .bg-primary;
  }

  .installed-stone-badge {
    @extend .q-ml-xs;
    @extend .q-badge;
    @extend .bg-purple-4;
  }

  .installed-device-badge {
    @extend .unit-badge;
    @extend .bg-green-4;
  }
</style>

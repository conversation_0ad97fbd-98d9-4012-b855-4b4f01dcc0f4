<template>
  <modal
    v-model="visible"
    v-model:maximized="maximized"
    :contentHeight="maxHeight ? '60vh' : 'auto'"
    content-class="marker-history-modal"
    ref="markerHistory"
    @hide="maxHeight = false"
  >
    <template #header>
      <span v-text="title"></span>
    </template>
    <query-history
      ref="form-content"
      :default-time-diff="defaultTimeDiff"
      :time-max-range="timeMaxRange"
      :export-name="title"
      :table-columns="columns"
      :filter-columns='filterColumns'
      :time-limit-sql-name="timeField"
      :can-query="canQuery"
      :extraExportFunc="extraExportFunc"
      @submit-clicked="onSubmit"
      @query-canceled="queryCanceled"
    >
      <template #form>
        <div class="query-form">
          <q-select
            v-model="orgRID"
            :label="$t('form.parentUnit')"
            outlined
            dense
            clearable
            lazy-rules
            :rules="[]"
            :options="orgRIDOptions"
            @filter="filterOrgRID"
            options-dense
            map-options
            emit-value
            @update:model-value="orgRIDChange"
          />
          <q-select
            v-model="controllerRID"
            :label="$t('form.controllerID')"
            outlined
            dense
            clearable
            lazy-rules
            :rules="[]"
            :options="controllerRIDOptions"
            @filter="filterControllerRID"
            options-dense
            map-options
            emit-value
            @update:model-value="controllerRIDChange"
          >
          </q-select>
          <q-select
            v-model="markerRID"
            :label="$t('form.markerName')"
            outlined
            dense
            clearable
            lazy-rules
            :rules="[]"
            :options="markerRIDOptions"
            @filter="filterMarkerRID"
            options-dense
            map-options
            emit-value
            use-input
            @update:model-value="markerChange"
          >
            <template v-slot:before>
              <q-checkbox v-model='extractSearchMarker'>
                <q-tooltip>
                  {{ $t('common.exactSearch') }}
                </q-tooltip>
              </q-checkbox>
            </template>
          </q-select>
          <div class="q-gutter-sm">
            <q-checkbox
              v-model="onlyAlarm"
              :label="$t('form.checkAlarmOnly')"
              color="red"
              @update:model-value="(val) => { val && (onlyReport = !val) }"
            />
            <q-checkbox
              v-model="onlyReport"
              :label="$t('form.checkCardOnly')"
              color="cyan"
              @update:model-value="(val) => { val && (onlyAlarm = !val) }"
            />
          </div>
        </div>
      </template>

      <template v-slot:my-body-cell-Status="{ value, row, col }">
        <span>{{ value }}</span>
        <q-btn
          icon="iconfont bys-details"
          v-if="row?.detailInfo && col?.name === 'Status'"
          color="light"
          size="sm"
          flat
          round
          dense
        >
        <q-tooltip>
          {{ details }}
        </q-tooltip>
          <q-popup-proxy>
            <q-banner>
              <span
                v-for="info in row?.detailInfo"
                :key="info"
                class="column q-my-sm"
              >{{ info }}</span>
            </q-banner>
          </q-popup-proxy>
        </q-btn>
      </template>

      <template #top-actions>
        <q-btn
            class="q-ml-sm"
            dense
            size="md"
            color="primary"
            :disable="!onlyAlarm"
            :label="$t('menus.trackPlayback')"
            @click="playTrace"
          >
            <q-tooltip v-if="!onlyAlarm">
              {{ $t('message.tracePlayTooltip') }}
            </q-tooltip>
          </q-btn>
      </template>
    </query-history>
  </modal>

  <TracePlayer
    v-model="traceVisible"
    :play-data="traceData"
    :getPopupHtml="processPopupHtml"
    :sortPlayData="sortPlayData"
  >
  </TracePlayer>
</template>

<script lang="ts">
  import dialogMixin from '@utils/mixins/dialog'
  import historyMixin from '@utils/mixins/history'
  import { org } from '@ygen/org'
  import { GET_DATA, NS } from '@store/data/methodTypes'
  import { DataName } from '@src/store'
  import { bysdb } from '@ygen/bysdb'
  import { BysMarker, Controller, Unit } from '@services/dataStore'
  import {
    analysisMarkerStatus,
    CmdCode,
    deferred,
    getObjAllAttrs,
    getReporting,
    IControllerOptions,
    MarkerType,
    secondConfirmDialog,
    checkIs4GMarker
  } from '@utils/common'
  import { crud } from '@ygen/crud'
  import { defaultQueryFinished, QueryBatchV2 } from '@services/queryData'
  import { PrpcDbMarkerHistory } from '@ygen/bysdb.rpc.yrpc'
  import { StrPubSub } from 'ypubsub'
  import { QueryMarkerHistory } from '@utils/pubSubSubject'
  import { wrapperInvalidDate } from '@utils/dayjs'
  import { fixedNumber, toGCJ02 } from '@utils/gcoord'
  import { DbName } from '@utils/permission'
  import PermissionMixin from '@utils/mixins/permission'
  import { defineComponent } from 'vue'
  import { useBysMarkerData } from '@utils/vueUse/bysMarkerData'
  import { TracePlayerData } from '@components/tracePlayer/types'
  import TracePlayer from '@components/tracePlayer/TracePlayer.vue'
  import { md5StringArray } from '@utils/crypto'

  const { bysMarkerData } = useBysMarkerData()
  let queryNormal = true
  let queryCancelHandlers: Array<() => void> = []
  let queryRet: Array<bysdb.IDbMarkerHistory> = []
  let searchMarkerType = 0

  type MyTracePlayerData = TracePlayerData<bysdb.IDbMarkerHistory>

  export default defineComponent({
    name: 'MarkerHistory',
    mixins: [dialogMixin, PermissionMixin, historyMixin],
    data() {
      return {
        extractSearchMarker: false,

        maxHeight: false,
        defaultTimeDiff: {
          value: 1,
          uint: 'month',
        },
        timeMaxRange: {
          value: 3,
          uint: 'month',
          text: this.$t('date.notMax3month'),
        },
        timeField: 'ActionTime',

        orgRID: '',
        controllerRID: '',
        markerRID: '',
        onlyAlarm: false,
        onlyReport: false,

        orgFilter: '',
        controllerFilter: '',
        deviceFilter: '',

        // 轨迹回放
        traceVisible: false,
        traceData: [] as MyTracePlayerData[]
      }
    },
    components: {
      TracePlayer,
    },
    computed: {
      details() {
        return this.$t('common.details')
      },
      bysMarkerData() {
        return bysMarkerData.value
      },
      dbName() {
        return DbName.DbMarkerHistory
      },
      title() {
        return this.$t('PermTab.DbMarkerHistory')
      },

      controllerData() {
        return this.$store.getters[`${NS}/${GET_DATA}`](DataName.Controller)
      },
      controllerRIDOptions() {
        let options: Array<IControllerOptions> = this.controllerData
          .filter(item => {
            if (this.orgRID) {
              return item.OrgRID === this.orgRID
            }
            return true
          })
          .map((data: bysdb.IDbController) => {
            return {
              label: data.ControllerNo,
              value: data.RID,
              ControllerHWID: data.ControllerHWID,
              OrgRID: data.OrgRID,
              orderKey: data.ControllerHWID,
            }
          })

        options.sort((a: IControllerOptions, b: IControllerOptions) => a.orderKey - b.orderKey)

        if (this.controllerFilter) {
          return options.filter(option => {
            const needle = this.controllerFilter.toLowerCase()
            // return this.extractSearchController? option.label === needle: option.label.toLowerCase().includes(needle)
            return option.label.toLowerCase().includes(needle)
          })
        }
        return options
      },

      markerRIDOptions() {
        let options: Array<IControllerOptions> = this.bysMarkerData
          .filter(item => {
            let flag = true
            if (this.orgRID) {
              flag = item.OrgRID === this.orgRID
            }
            if (this.controllerRID) {
              flag = flag && item.ControllerRID === this.controllerRID && item.MarkerType === MarkerType.Regular
            }
            return flag
          })
          .map((data: bysdb.IDbBysMarker) => {
            return {
              label: data.MarkerNo,
              value: data.RID,
              MarkerHWID: data.MarkerHWID,
              ControllerRID: data.ControllerRID,
              OrgRID: data.OrgRID,
              orderKey: data.MarkerHWID,
              markerType: data.MarkerType,
            }
          })

        options.sort((a, b) => a.orderKey - b.orderKey)

        // 根据输入内容过滤数据
        if (this.deviceFilter) {
          return options.filter(option => {
            const needle = this.deviceFilter.toLowerCase()
            return this.extractSearchMarker ? option.label === needle : option.label.toLowerCase().includes(needle)
            // return option.label.toLowerCase().includes(needle)
          })
        }

        return options
      },

      columns() {
        return [
          {
            name: 'OrgRID',
            field: 'OrgRID',
            label: this.$t('form.unit'),
            sortable: true,
            format: (val: string) => {
              const orgData = Unit.getData(val) as org.IDbOrg | undefined
              return orgData?.ShortName ?? val
            },
          },
          {
            name: 'MarkerHWID', field: 'MarkerHWID', label: this.$t('form.markerName'), sortable: true,
            sticky: true,
            format: (val: number) => {
              const controller: bysdb.IDbBysMarker | undefined = BysMarker.getDataByIndex(val + '')
              return controller?.MarkerNo ?? val
            },
          },
          { name: 'ActionTime', field: 'ActionTime', label: this.$t('form.receiptTime'), sortable: true ,noLeftBorder: true},
          {
            name: 'CmdTime', field: 'CmdTime', label: this.$t('form.cmdUploadTime'), sortable: true,
            format: (val: string) => {
              return wrapperInvalidDate(val)
            },
          },
          {
            name: 'Status', field: 'Status', label: this.$t('form.status'),
            format: (val: number, row) => {
              return analysisMarkerStatus(row)
            },
          },
          {
            name: 'Lon', field: 'Lon', label: this.$t('form.lon'),
            format: (val: number) => {
              return fixedNumber(val, 7)
            },
          },
          {
            name: 'Lat', field: 'Lat', label: this.$t('form.lat'),
            format: (val: number) => {
              return fixedNumber(val, 7)
            },
          },
          {
            name: 'Controller',
            field: 'ControllerID',
            label: this.$t('form.controllerID'),
            sortable: true,
            format: (val: number, row: bysdb.IDbMarkerHistory) => {
              if (row.ActionCode !== 2) {
                return ''
              }
              // 基站数据与实际接收中继相同时，只显示基站
              const bsController: bysdb.IDbController | undefined = Controller.getDataByIndex(val + '')
              const bsLabel = bsController?.ControllerNo ?? val
              if (row.RecvControllerID === val) {
                return bsLabel
              }

              // 实际接收的控制器ID不为0, 则添加实际的接收控制器
              const RecvControllerID = row.RecvControllerID
              const recvController: bysdb.IDbController | undefined = Controller.getDataByIndex(RecvControllerID + '')
              return `${bsLabel} / ${recvController?.ControllerNo ?? RecvControllerID}`
            },
          },
          {
            name: 'ControllerChannel', field: 'ControllerChannel', label: this.$t('form.controllerChannel'),
            format: (val: number, row: bysdb.IDbMarkerHistory) => {
              const markerType = this.markerRIDOptions.find(item => item.MarkerHWID === row.MarkerHWID)?.MarkerType ?? MarkerType.Regular
              if (checkIs4GMarker(markerType)) {
                return ''
              }
              return val
            }
          },
        ]
      },
      filterColumns() {
        return ['OrgRID', 'MarkerHWID', 'Controller', 'Status']
      },
    },
    methods: {
      /**
       * 导出表格数据的额外处理函数
       * @param {Object} source - 表格的一行源数据
       * @param {Object} _row - 完成部分处理的xlsx表格数据
       * @return {Object} 对源数据处理完后保存在_row并返回
       */
       extraExportFunc(source, _row) {
        // 对最后上报状态添加详细信息
        source.detailInfo.splice(0, 1)
        const detailInfo = source.detailInfo.join(',')
        _row[this.$t('form.status')] = _row[this.$t('form.status')] + `(${detailInfo})`
        return _row
      },
      markerChange(val) {
        if (!val) {
          searchMarkerType = MarkerType.Regular
          return
        }

        searchMarkerType = this.markerRIDOptions.find(item => item.value === val)?.markerType ?? MarkerType.Regular
      },
      createWhereItems(): crud.IWhereItem[] {
        const where: crud.IWhereItem[] = []

        if (this.orgRID) {
          // 添加单位查询
          const whereItem: crud.IWhereItem = {
            Field: 'OrgRID',
            FieldValue: this.orgRID,
            FieldCompareOperator: '=',
          }
          where.push(whereItem)
        }
        if (this.controllerRID) {
          const _controller = Controller.getData(this.controllerRID) as bysdb.IDbController
          if (_controller) {
            const whereItem: crud.IWhereItem = {
              Field: 'ControllerID',
              FieldValue: _controller.ControllerHWID + '',
              FieldCompareOperator: '=',
            }
            where.push(whereItem)
          }
        }
        if (this.markerRID) {
          const whereItem: crud.IWhereItem = {
            Field: 'MarkerHWID',
            FieldValue: '' + (this.bysMarkerData.find(item => item.RID === this.markerRID).MarkerHWID ?? ''),
            FieldCompareOperator: '=',
          }
          where.push(whereItem)
        }

        return where
      },
      createOrderByItems(): string[] {
        return [`${this.timeField} asc`]
      },
      createRetColumns(): string[] {
        const obj: Object = new bysdb.DbMarkerHistory()
        return getObjAllAttrs(obj)
      },
      getTimeColumn(): string[] {
        return this.$refs['form-content'].createTimeColumns()
      },
      async onSubmit(where: crud.IWhereItem[]) {
        const queryAlreadyCallBack = function() {
          queryNormal !== true && defaultQueryFinished()
        }
        queryRet = []
        queryNormal = false
        this.maxHeight = true
        const orderByItems = this.createOrderByItems()
        const timeColumns = this.getTimeColumn()
        const retColumns = this.createRetColumns()
        const defaultWhereItems = this.createWhereItems().concat(where)
        const submitFunc = (whereItem?: crud.IWhereItem): Promise<boolean> => {
          const promiseDeferred = deferred<boolean>()
          const cancel = QueryBatchV2(
            PrpcDbMarkerHistory.QueryBatch,
            {
              Where: whereItem ? defaultWhereItems.concat([whereItem]) : defaultWhereItems,
              OrderBy: orderByItems,
              ResultColumn: retColumns,
              TimeColumn: timeColumns,
            },
            promiseDeferred,
            {
              OnResult: (data) => {
                window.requestIdleCallback(() => {
                  StrPubSub.publish(QueryMarkerHistory, data.Rows)
                })
              },
            }
          )
          queryCancelHandlers.push(cancel)
          return promiseDeferred
        }
        const ActionCodeWhereItems: crud.IWhereItem[] = []
        if (this.onlyAlarm || this.onlyReport) { // 判断是打卡或者报警历史
          if (this.markerRID) { // 存在界桩RID, 判断查询常规界桩还是4g界桩
            ActionCodeWhereItems.push({
              Field: 'ActionCode',
              FieldValue: '' + (searchMarkerType === MarkerType.Regular // 判断常规界桩还是4g界桩
                ? (this.onlyAlarm ? CmdCode.D2 : CmdCode.D1) // 判断常规界桩打卡或报警
                : (this.onlyAlarm ? CmdCode.AlarmReport : CmdCode.InfoReport) // 判断4g界桩打卡或报警
              ),
              FieldCompareOperator: '=',
            })
          } else { // 不存在界桩RID，则常规和4g界桩的打卡或报警历史都查
            ActionCodeWhereItems.push({
              Field: 'ActionCode',
              FieldValue: '' + (this.onlyAlarm ? CmdCode.D2 : CmdCode.D1),
              FieldCompareOperator: '=',
            })
            ActionCodeWhereItems.push({
              Field: 'ActionCode',
              FieldValue: '' + (this.onlyAlarm ? CmdCode.AlarmReport : CmdCode.InfoReport),
              FieldCompareOperator: '=',
            })
          }
        }

        let execFns = [submitFunc]
        if (ActionCodeWhereItems.length > 0) { // 数组大于0时为查找界桩的打卡或报警历史
          execFns = ActionCodeWhereItems.map(item => (() => submitFunc(item)))
        }
        const promiseAllSets = execFns.map(func => func())
        Promise.all(promiseAllSets)
          .then(queryPromiseRes => {
            queryNormal = queryPromiseRes.find(bool => bool !== true) ?? true
          })
          .finally(() => {
            queryAlreadyCallBack()
          })
      },
      queryDataRet(datas: []) {
        queryRet = queryRet.concat(Object.freeze(datas))
        this.$refs['form-content'].queryRet = queryRet
      },
      queryCanceled() {
        for (let i = queryCancelHandlers.length - 1; i >= 0; i--) {
          queryCancelHandlers[i]()
        }
        queryCancelHandlers = []
        queryNormal = false
        this.maxHeight = false
      },

      filterOrgRID(val: string, update: Function) {
        this.orgFilter = val
        update()
      },
      orgRIDChange() {
        this.controllerRID = ''
        this.markerRID = ''
      },

      filterControllerRID(val: string, update: Function) {
        this.controllerFilter = val
        update()
      },
      controllerRIDChange() {
        this.markerRID = ''
      },
      filterMarkerRID(val: string, update: Function) {
        this.deviceFilter = val
        update()
      },
      async playTrace() {
        const hwIdArr = queryRet.map(item => item.MarkerHWID)
        const hwIdSet = new Set(hwIdArr)
        if (hwIdSet.size > 1) {
          const isOk = await secondConfirmDialog(this.$t('message.confirmPlayTrace'))
          if (!isOk) return
        }
        this.traceData = []
        // 历史定位点计算的hash值，如果有重复的hash值，则不添加
        // hash(界桩Id+定位时间+经度+纬度)
        const hashSet = new Set()
        for (let i = 0; i < queryRet.length; i++) {
          const item = queryRet[i]
          if (item.Lon == 0 || !item.Lon || item.Lat == 0 || !item.Lat) {
            continue
          }
          const marker = BysMarker.getDataByIndex(item.MarkerHWID + '')
          if (!marker) continue

          // 上一个点和当前点相同，则不再添加,只替换源信息
          const hash = md5StringArray([item.MarkerHWID + '', item.CmdTime + '', item.Lon + '', item.Lat + ''])
          if (hashSet.has(hash)) {
            this.traceData[this.traceData.length - 1].source = item
            continue
          }
          const lonLat = toGCJ02([item.Lon, item.Lat]) as [number, number]
          const data: MyTracePlayerData = {
            lonLat,
            OrgRID: marker.OrgRID as string,
            displayName: marker.MarkerNo as string,
            MarkerHWID: marker.MarkerHWID as number,
            source: item,
          }
          this.traceData.push(data)
          hashSet.add(hash)
        }
        this.traceVisible = true
      },
      processPopupHtml(data: MyTracePlayerData) {
        const orgData = Unit.getData(data.OrgRID + '') as org.IDbOrg | undefined
        const ShortName = orgData?.ShortName ?? data.OrgRID
        const sourceData = data.source
        const marker: bysdb.IDbBysMarker | undefined = BysMarker.getDataByIndex(data.MarkerHWID + '')
        const MarkerNo = marker?.MarkerNo ?? data.MarkerHWID
        const detailInfo = getReporting(sourceData.ActionCode as number, sourceData.ReportInfo as string) ?? []
        const detailHtml = detailInfo.map((info: string) => `<div><span class="text-caption">${info}</span></div>`).join('')
        return (`<div>
        <div><span class="text-caption">${this.$t('form.unit')}</span> <span>${ShortName}</span></div>
        <div><span class="text-caption">${this.$t('form.markerName')}</span> <span>${MarkerNo}</span></div>
        <div><span class="text-caption">ICCID</span> <span>${marker?.ICCID}</span></div>
        <div><span class="text-caption">IMEI</span> <span>${marker?.IMEI}</span></div>
        <div><span class="text-caption">${this.$t('message.alarmTime')}</span> <span>${wrapperInvalidDate(sourceData.CmdTime as string)}</span></div>
        ${detailHtml}
      </div>`)
      },
      sortPlayData(rows: MyTracePlayerData[]) {
        rows.sort((a, b) => {
          const aCmdTime = a.source.CmdTime || ''
          const bCmdTime = b.source.CmdTime || ''
          return aCmdTime.localeCompare(bCmdTime)
        })
      }
    },
    beforeMount() {
      StrPubSub.subscribe(QueryMarkerHistory, this.queryDataRet)
    },
    beforeUnmount() {
      StrPubSub.subscribe(QueryMarkerHistory, this.queryDataRet)
    },
  })
</script>

<style lang="scss">
  .marker-history-modal {
    .history-panel {
      height: 100%;

      .history-table {
        &.q-table--dense .q-table {

          th,
          td {
            padding: 2px 4px;
          }
        }

        .sticky-column-last {
          position: sticky !important;
          right: 0;
          background: #fffff3;
        }
      }
    }

    .q-tab-panel {
      padding: unset
    }

    .query-form {

      // 表单有验证条件的下边距为20px，该样式主要同步没有验证条件的表单项样式
      &>*:not(:last-child) {
        padding-bottom: 20px;
      }
    }
  }

  .marker-history-modal:not(.maximized) {
    width: auto !important;
    max-width: unset !important;

    .history-panel .history-table {
      width: 60vw !important;
    }

    .query-form {
      min-width: 400px;
    }
  }

  .popup-proxy-details {
    max-width: 60vw;
  }

  .q-icon.detail-info {
    font-size: 18px;
    border-radius: 4px;
    padding: 0 6px;
    margin: 0 2px;
  }

  .q-icon.detail-info:hover {
    font-size: 18px;
    border-radius: 4px;
    padding: 0 6px;
    margin: 0 2px;
    background-color: $blue-1;
  }
</style>

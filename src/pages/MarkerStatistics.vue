<template>
  <!-- visible,maximized in dataEdit mixins -->
  <modal
    v-model="visible"
    :contentHeight="dataLen ? '68vh' : 'auto'"
    :content-class="'marker-statistics-page-cls'"
    v-model:maximized="maximized"
  >
    <template v-slot:header><span>{{ title }}</span></template>
    <q-table
      class="sticky-header-column-virtscroll-table full-height full-width"
      dense
      flat
      bordered
      virtual-scroll
      v-model:pagination="pagination"
      :rows-per-page-options="rowsPerPageOptions"
      :virtual-scroll-sticky-size-start="0"
      row-key="RID"
      :rows="markerData"
      :columns="columns"
      :filter="filter"
      separator="cell"
    >
      <template v-slot:top>
        <div class="col-xs-12 col-sm-8 row q-gutter-sm items-center no-wrap py-[5px]">
          <q-checkbox 
            dense
            v-model="hasInstallStone" 
            :label="$t('markerStatistics.notInstallDevice')" color="orange"
            @update:model-value="changeCheckBox"
          />
          <q-checkbox 
            dense
            v-model="notInstalled" 
            :label="$t('markerStatistics.notInstallStone')" color="red"
            @update:model-value="changeCheckBox"
          />
          <q-checkbox 
            dense
            v-model="hasInstallDevice" 
            :label="$t('markerStatistics.installed')"
            @update:model-value="changeCheckBox" 
          />
        </div>
        <div class="col-xs-12 col-sm-4 flex no-wrap gap-4 items-center">
          <div class="flex-none">
            <q-btn
            color="primary"
            :label="$t('common.export')"
            size="sm"
            @click="exportAll"
          />
          </div>
          <q-input
            v-model="filter"
            dense
            debounce="300"
            color="primary"
            :placeholder="$t('common.search')"
          >
            <template v-slot:append>
              <q-icon name="search" />
            </template>
          </q-input>
        </div>
      </template>
    </q-table>
  </modal>
</template>

<script lang="ts">
  import { defineComponent } from 'vue'
  import { bysdb } from '@ygen/bysdb'
  import { DbName } from '@utils/permission'
  import dataEditMixin from '@utils/mixins/editor'
  import dialogMixin from '@utils/mixins/dialog'
  import { useBysMarkerData } from '@utils/vueUse/bysMarkerData'
  import dayjs from 'dayjs'
  import { exportData2Excel } from '@utils/xlsx'
  import { DefUuid } from '@src/config'
  import { org } from '@ygen/org'
  import { GET_DATA, NS } from '@store/data/methodTypes'
  import { DataName } from '@src/store'
  import { ExtendsQTableColumn } from '@utils/bysdb.type'

  const rowsPerPageOptions = [0]
  const { bysMarkerData } = useBysMarkerData()

  export default defineComponent({
    name: 'MarkerStatistics',
    mixins: [dataEditMixin, dialogMixin],
    data() {
      return {
        pagination: {
          rowsPerPage: rowsPerPageOptions[0],
        },
        exportName: this.$t('markerStatistics.installed'),
        filter: '',
        hasInstallDevice: false,
        hasInstallStone: true,
        notInstalled: false,
      }
    },
    computed: {
      hasDeviceMarker() {
        return this.filterBysMarkerData(true, true)
      },
      hasStoneMarker() {
        return this.filterBysMarkerData(false, true)
      },
      notInstallMarker() {
        return this.filterBysMarkerData(false, false)
      },
      bysMarkerData() {
        const data: bysdb.DbBysMarker[] = []
        if (this.hasInstallDevice && this.hasInstallStone && this.notInstalled) {
          data.push(...this.hasDeviceMarker, ...this.hasStoneMarker, ...this.notInstallMarker)
          return data
        }
        if (this.hasInstallDevice) {
          data.push(...this.hasDeviceMarker)
        }
        if (this.hasInstallStone) {
          data.push(...this.hasStoneMarker)
        }
        if (this.notInstalled) {
          data.push(...this.notInstallMarker)
        }
        return data
      },
      dbName() {
        return DbName.DbBysMarker
      },
      title() {
        return this.$t('menus.markerStatistics')
      },
      rowsPerPageOptions() {
        return rowsPerPageOptions
      },
      markerData() {
        return this.bysMarkerData
      },
      parentOptions(): Array<{ [key: string]: any }> {
        return this.unitData
          .map(data => {
            return {
              label: data.RID === DefUuid ? this.$t('form.default') : data.ShortName,
              value: data.RID,
            }
          })
      },
      unitData(): Array<org.IDbOrg> {
        return this.$store.getters[`${NS}/${GET_DATA}`](DataName.Unit).filter(item => item.RID !== DefUuid)
      },
      MarkerModelOptions() {
        return [
          { label: this.$t('form.long'), value: 1 },
          { label: this.$t('form.short'), value: 2 },
        ]
      },
      columns(): ExtendsQTableColumn {
        return [
          {
            name: 'OrgRID', field: 'OrgRID', label: this.$t('form.unit'), sortable: true,
            format: (val) => {
              const parentOption = this.parentOptions.find(item => item.value === val)
              return `${parentOption ? parentOption.label : ''}`
            },
          },
          {
            name: 'MarkerNo',
            field: 'MarkerNo',
            label: this.$t('form.markerName'),
            sortable: true,
            sticky: true,
          },
          { name: 'MarkerHWID', field: 'MarkerHWID', label: this.$t('form.boundaryID'), sortable: true },
          {
            name: 'MarkerModel', field: 'MarkerModel', label: this.$t('form.boundaryModel'), sortable: true,
            format: (val) => {
              const typeOption = this.MarkerModelOptions.find(item => item.value === val)
              return `${typeOption ? typeOption.label : ''}`
            },
          },
          {
            name: 'HasInstallStone',
            field: 'HasInstallStone',
            label: this.$t('markerStatistics.markerInstallStatus'),
            format: (val: number, row: bysdb.IDbBysMarker) => {
              if (row.HasInstallDevice) {
                return this.$t('markerStatistics.installed')
              }
              if (row.HasInstallStone) {
                return this.$t('markerStatistics.notInstallDevice')
              }
              return this.$t('markerStatistics.notInstallStone')
            },
          },
        ]
      },
      dataLen() {
        return this.bysMarkerData.length
      },
      installed() {
        return this.bysMarkerData.filter(item => item.HasInstallStone ? item.HasInstallDevice ? true : false : false)
      },
      unInstalled() {
        return this.bysMarkerData.filter(item => item.HasInstallStone ? item.HasInstallDevice ? false : true : true)
      },
      installedLen() {
        return this.installed.length
      },
      unInstallLen() {
        return this.unInstalled.length
      },
    },
    methods: {
      changeCheckBox() {
        if (!this.hasInstallDevice && !this.hasInstallStone && !this.notInstalled) {
          this.hasInstallStone = true
        }
      },
      filterBysMarkerData(hasInstallDevice: boolean, hasInstallStone: boolean) {
        return bysMarkerData.value.filter(marker => marker.HasInstallDevice === hasInstallDevice && marker.HasInstallStone === hasInstallStone)
      },
      exportAll() {
        const fileName = `${this.$t('markerStatistics.markerInstallStatus')}-${this.$t(
          'common.export')}-${dayjs().format('YYYYMMDD-HHmmss')}`
        exportData2Excel({ fileName, data: this.markerData, columns: this.columns })
      },
    },
  })
</script>

<style lang="scss">
  .marker-statistics-page-cls:not(.maximized) {
    max-width: 60vw !important;
    width: 60vw !important;
  }
</style>

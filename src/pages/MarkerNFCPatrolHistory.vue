<template>
  <modal
    v-model="visible"
    v-model:maximized="maximized"
    :contentHeight="maxHeight ? '60vh' : 'auto'"
    :title="title"
    content-class="marker-nfc-patrol-history"
    ref="markerNfcPatrolHistory"
    @hide="maxHeight = false"
  >
    <query-history
      ref="form-content"
      :default-time-diff="defaultTimeDiff"
      :time-max-range="timeMaxRange"
      :export-name="title"
      :table-columns="columns"
      :filter-columns='filterColumns'
      :time-limit-sql-name="timeField"
      :can-query="canQuery"
      @submit-clicked="onSubmit"
      @query-canceled="queryCanceled"
    >
      <template #form>
        <div class="query-form">
          <q-select
            v-model="orgRID"
            :label="$t('form.parentUnit')"
            outlined
            dense
            clearable
            lazy-rules
            :hide-bottom-space="false"
            :rules="[]"
            :options="orgRIDOptions"
            @filter="filterOrgRID"
            options-dense
            map-options
            emit-value
            @update:model-value="orgRIDChange"
          />
          <q-select
            v-model="markerRID"
            :label="$t('form.markerName')"
            outlined
            dense
            clearable
            lazy-rules
            :hide-bottom-space="false"
            :rules="[]"
            :options="markerRIDOptions"
            @filter="filterMarkerRID"
            options-dense
            map-options
            emit-value
            use-input
          >
          </q-select>

          <q-select
            v-model="userRID"
            :label="$t('menus.user')"
            outlined
            dense
            clearable
            lazy-rules
            :rules="[]"
            :options="userRIDOptions"
            @filter="filterUserRID"
            options-dense
            map-options
            emit-value
            use-input
          />
        </div>
      </template>

      <template #top-actions>
        <q-btn
          class="q-ml-sm"
          dense
          size="md"
          color="primary"
          :label="$t('menus.trackPlayback')"
          :disable="!queryRet.length"
          @click="playTrace"
        />
      </template>
    </query-history>
  </modal>

  <TracePlayer
    v-model="traceVisible"
    :play-data="traceData"
    :getPopupHtml="processPopupHtml"
    :sortPlayData="sortPlayData"
  >
  </TracePlayer>
</template>

<script lang="ts">
  import dialogMixin from '@utils/mixins/dialog'
  import historyMixin from '@utils/mixins/history'
  import { org } from '@ygen/org'
  import { bysdb } from '@ygen/bysdb'
  import { BysMarker, Unit, User } from '@services/dataStore'
  import {
    deferred,
    getObjAllAttrs,
    IControllerOptions,
  } from '@utils/common'
  import { crud } from '@ygen/crud'
  import { defaultQueryFinished, QueryBatchV2 } from '@services/queryData'
  import { PrpcDbMarkerPatrolHistory } from '@ygen/bysdb.rpc.yrpc'
  import { StrPubSub } from 'ypubsub'
  import { QueryMarkerPatrolHistory } from '@utils/pubSubSubject'
  import { DbName } from '@utils/permission'
  import PermissionMixin from '@utils/mixins/permission'
  import { defineComponent, shallowRef } from 'vue'
  import { useBysMarkerData } from '@utils/vueUse/bysMarkerData'
  import { GET_DATA, NS } from '@store/data/methodTypes'
  import { DataName } from '@src/store'
  import { user } from '@ygen/user'
  import { getGCJ02LngLat } from '@utils/gcoord'
  import TracePlayer from '@components/tracePlayer/TracePlayer.vue'
  import { TracePlayerData } from '@components/tracePlayer/types'

  type MyTracePlayerData = TracePlayerData<bysdb.IDbMarkerPatrolHistory>

  const { bysMarkerData } = useBysMarkerData()
  let queryNormal = true
  let queryCancelHandlers: Array<() => void> = []
  const queryRet = shallowRef<bysdb.DbMarkerPatrolHistory[]>([])

  export default defineComponent({
    name: 'MarkerNFCPatrolHistory',
    mixins: [dialogMixin, PermissionMixin, historyMixin],
    data() {
      return {
        maxHeight: false,
        defaultTimeDiff: {
          value: 1,
          uint: 'month',
        },
        timeMaxRange: {
          value: 3,
          uint: 'month',
          text: this.$t('date.notMax3month'),
        },
        timeField: 'ActionTime',

        orgRID: '',
        markerRID: '',

        orgFilter: '',
        deviceFilter: '',

        userRID: '',
        userFilter: '',

        traceVisible: false,
        traceData: [] as MyTracePlayerData[],
        queryRet,
      }
    },
    computed: {
      userData() {
        return this.$store.getters[`${NS}/${GET_DATA}`](DataName.User)
      },
      userRIDOptions() {
        const users = this.orgRID ? this.userData.filter(item => item.OrgRID === this.orgRID) : this.userData
        const options = users
          .map((data: user.DbUser) => {
            return {
              label: data.LoginName,
              value: data.RID,
            }
          })
        if (!this.userFilter) {
          return options
        }

        return options.filter(option => {
          const needle = this.userFilter.toLowerCase()
          return option.label.toLowerCase().includes(needle)
        })
      },
      bysMarkerData() {
        return bysMarkerData.value
      },
      dbName() {
        return DbName.DbMarkerPatrolHistory
      },
      title() {
        return this.$t('menus.MarkerNFCPatrolHistory')
      },
      markerRIDOptions() {
        let options: Array<IControllerOptions> = this.bysMarkerData
          .filter(item => {
            let flag = true
            if (this.orgRID) {
              flag = item.OrgRID === this.orgRID
            }

            return flag
          })
          .map((data: bysdb.IDbBysMarker) => {
            return {
              label: data.MarkerNo,
              value: data.RID,
              MarkerHWID: data.MarkerHWID,
              OrgRID: data.OrgRID,
              orderKey: data.MarkerHWID,
              markerType: data.MarkerType,
            }
          })

        options.sort((a, b) => a.orderKey - b.orderKey)

        // 根据输入内容过滤数据
        if (this.deviceFilter) {
          return options.filter(option => {
            const needle = this.deviceFilter.toLowerCase()
            return option.label.toLowerCase().includes(needle)
          })
        }

        return options
      },

      columns() {
        return [
          {
            name: 'OrgRID',
            field: 'OrgRID',
            label: this.$t('form.unit'),
            sortable: true,
            format: (val: string) => {
              const orgData = Unit.getData(val) as org.IDbOrg | undefined
              return orgData?.ShortName ?? val
            },
          },
          {
            name: 'MarkerHWID', field: 'MarkerHWID', label: this.$t('form.markerName'), sortable: true,
            sticky: true,
            format: (val: number) => {
              const marker: bysdb.IDbBysMarker | undefined = BysMarker.getDataByIndex(val + '')
              return marker?.MarkerNo ?? val
            },
          },
          {
            name: 'NFCID', field: 'NFCID', label: 'NFC ID', sortable: true, noLeftBorder: true,
          },
          {
            name: 'UserID', field: 'UserID', label: this.$t('NFCPatrol.userName'), sortable: true,
            format: (val: string) => {
              const user = User.getData(val)
              return user?.UserName ?? val
            },
          },
          {
            name: 'ActionTime', field: 'ActionTime', label: this.$t('NFCPatrol.patrolTime'), sortable: true,
          },
        ]
      },
      filterColumns() {
        return ['OrgRID', 'MarkerHWID', 'NFCID', 'UserID']
      },
    },
    methods: {
      filterUserRID(val: string, update: Function) {
        this.userFilter = val
        update()
      },
      createWhereItems(): crud.IWhereItem[] {
        const where: crud.IWhereItem[] = []

        if (this.orgRID) {
          // 添加单位查询
          const whereItem: crud.IWhereItem = {
            Field: 'OrgRID',
            FieldValue: this.orgRID,
            FieldCompareOperator: '=',
          }
          where.push(whereItem)
        }

        if (this.markerRID) {
          const whereItem: crud.IWhereItem = {
            Field: 'MarkerHWID',
            FieldValue: '' + (this.bysMarkerData.find(item => item.RID === this.markerRID).MarkerHWID ?? ''),
            FieldCompareOperator: '=',
          }
          where.push(whereItem)
        }

        if (this.userRID) {
          // 添加单位查询
          const whereItem: crud.IWhereItem = {
            Field: 'UserID',
            FieldValue: this.userRID,
            FieldCompareOperator: '=',
          }
          where.push(whereItem)
        }

        return where
      },
      createOrderByItems(): string[] {
        return [`${this.timeField} asc`]
      },
      createRetColumns(): string[] {
        const obj: Object = new bysdb.DbMarkerPatrolHistory()
        return getObjAllAttrs(obj)
      },
      getTimeColumn(): string[] {
        return this.$refs['form-content'].createTimeColumns()
      },
      async onSubmit(where: crud.IWhereItem[]) {
        const queryAlreadyCallBack = function () {
          queryNormal !== true && defaultQueryFinished()
        }
        queryRet.value = []
        queryNormal = false
        this.maxHeight = true
        const orderByItems = this.createOrderByItems()
        const timeColumns = this.getTimeColumn()
        const retColumns = this.createRetColumns()
        const defaultWhereItems = this.createWhereItems().concat(where)
        const submitFunc = (whereItem?: crud.IWhereItem): Promise<boolean> => {
          const promiseDeferred = deferred<boolean>()
          const cancel = QueryBatchV2(
            PrpcDbMarkerPatrolHistory.QueryBatch,
            {
              Where: whereItem ? defaultWhereItems.concat([whereItem]) : defaultWhereItems,
              OrderBy: orderByItems,
              ResultColumn: retColumns,
              TimeColumn: timeColumns,
            },
            promiseDeferred,
            {
              OnResult: (data) => {
                window.requestIdleCallback(() => {
                  StrPubSub.publish(QueryMarkerPatrolHistory, data.Rows)
                })
              },
            }
          )
          queryCancelHandlers.push(cancel)
          return promiseDeferred
        }
        const ActionCodeWhereItems: crud.IWhereItem[] = []

        let execFns = [submitFunc]
        if (ActionCodeWhereItems.length > 0) { // 数组大于0时为查找界桩的打卡或报警历史
          execFns = ActionCodeWhereItems.map(item => (() => submitFunc(item)))
        }
        const promiseAllSets = execFns.map(func => func())
        Promise.all(promiseAllSets)
          .then(queryPromiseRes => {
            queryNormal = queryPromiseRes.find(bool => bool !== true) ?? true
          })
          .finally(() => {
            queryAlreadyCallBack()
          })
      },
      queryDataRet(datas: []) {
        queryRet.value = queryRet.value.concat(Object.freeze(datas))
        if (this.$refs['form-content']) {
          this.$refs['form-content'].queryRet = queryRet.value
        }
      },
      queryCanceled() {
        for (let i = queryCancelHandlers.length - 1; i >= 0; i--) {
          queryCancelHandlers[i]()
        }
        queryCancelHandlers = []
        queryNormal = false
        this.maxHeight = false
      },

      filterOrgRID(val: string, update: Function) {
        this.orgFilter = val
        update()
      },
      orgRIDChange() {
        this.markerRID = ''
      },

      filterMarkerRID(val: string, update: Function) {
        this.deviceFilter = val
        update()
      },
      processPopupHtml(data: MyTracePlayerData) {
        const orgData = Unit.getData(data.OrgRID + '') as org.IDbOrg | undefined
        const ShortName = orgData?.ShortName ?? data.OrgRID
        const sourceData = data.source
        const marker: bysdb.IDbBysMarker | undefined = BysMarker.getDataByIndex(data.MarkerHWID + '')
        const MarkerNo = marker?.MarkerNo ?? data.MarkerHWID
        const user = User.getData(sourceData.UserID + '')
        const UserName = user?.UserName ?? sourceData.UserID
        return (`<div>
        <div><span class="text-caption">${this.$t('form.unit')}</span> <span>${ShortName}</span></div>
        <div><span class="text-caption">${this.$t('form.markerName')}</span> <span>${MarkerNo}</span></div>
        <div><span class="text-caption">NFCID</span> <span>${sourceData.NFCID}</span></div>
        <div><span class="text-caption">${this.$t('NFCPatrol.userName')}</span> <span>${UserName}</span></div>
        <div><span class="text-caption">${this.$t('NFCPatrol.patrolTime')}</span> <span>${sourceData.ActionTime}</span></div>
      </div>`)
      },
      playTrace() {
        for (let i = 0; i < queryRet.value.length; i++) {
          const item = queryRet.value[i]
          const marker = BysMarker.getDataByIndex(item.MarkerHWID + '')
          if (!marker) continue

          const lonLat = getGCJ02LngLat(marker)
          const data: MyTracePlayerData = {
            lonLat: lonLat as [number, number],
            OrgRID: marker.OrgRID as string,
            displayName: marker.MarkerNo as string,
            MarkerHWID: marker.MarkerHWID as number,
            source: item
          }
          this.traceData.push(data)
        }

        this.traceVisible = true
      },
      sortPlayData(rows: MyTracePlayerData[]) {
        rows.sort((a, b) => {
          const aCheckTime = a.source.ActionTime || ''
          const bCheckTime = b.source.ActionTime || ''
          return aCheckTime.localeCompare(bCheckTime)
        })
      }
    },
    components: {
      TracePlayer,
    },
    beforeMount() {
      StrPubSub.subscribe(QueryMarkerPatrolHistory, this.queryDataRet)
    },
    beforeUnmount() {
      StrPubSub.subscribe(QueryMarkerPatrolHistory, this.queryDataRet)
    },
  })
</script>

<style lang="scss">
  .marker-nfc-patrol-history {
    .history-panel {
      height: 100%;

      .history-table {
        &.q-table--dense .q-table {

          th,
          td {
            padding: 2px 4px;
          }
        }
      }
    }

    .q-tab-panel {
      padding: unset
    }

    .query-form {

      // 表单有验证条件的下边距为20px，该样式主要同步没有验证条件的表单项样式
      & > *:not(:last-child) {
        padding-bottom: 20px;
      }
    }

    &:not(.maximized) {
      width: auto !important;
      max-width: unset !important;

      .history-panel .history-table {
        width: 60vw !important;
      }

      .query-form {
        min-width: 400px;
      }
    }
  }

  .gps-trace-dialog {
    .maplibregl-ctrl-group.gps-track-player-control .q-btn {
      border-top: none;
    }
  }
</style>

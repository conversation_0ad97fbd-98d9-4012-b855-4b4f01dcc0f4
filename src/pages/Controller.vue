<template>
  <!-- visible,maximized in dataEdit mixins -->
  <modal
    v-model="visible"
    v-model:maximized="maximized"
    :contentHeight="dataLen ? '68vh' : 'auto'"
    :content-class="'controller-page-cls'"
  >
    <template v-slot:header><span>{{ title }}</span></template>
    <!--
    控制器管理菜单
    行选中保留项：OrgRID（单位），用来做为添加时的默认单位 。
      OrgRID -> defaultOrgID -> currentRow.OrgRID
    重复添加保留项：ControllerNo（编号）,OrgRID（单位），ParentRID（上级控制器）,ControllerHWID（控制器ID），其中编号名称要保留格式自增,单位、上级控制器直接继承。
    -->
    <data-edit
      :data="data"
      :columns="columns"
      :form-title="title"
      :can-add="canAdd"
      :can-edit="canEdit"
      :can-delete="canDelete"
      :insert="insertData"
      :update="wrapperUpdateData"
      :delete="deleteData"
      :import-data="batchAddData"
      v-model:currentRow="currentRow"
      :filter-columns='filterColumns'
      @new-row="newRow"
      @rwo-clicked="rowClicked"
      @save-multiplex-item="saveMultiplexItem"
      @clear-multiplex-item="clearMultiplexItem"
    >
      <template v-slot:form-content>
        <div class="fit row wrap">
          <controller-edit v-model="currentRow" />
        </div>
      </template>
    </data-edit>
  </modal>
</template>

<script lang="ts">
  import { defineComponent } from 'vue'
  import dialogMixin from '@src/utils/mixins/dialog'
  import dataEditMixin from '@src/utils/mixins/editor'
  import controllerMixin from '@utils/mixins/controller'
  import { utcTime } from '@src/utils/dayjs'
  import { v1 as uuidV1 } from 'uuid'
  import { isEmpty } from '@src/utils/validation'
  import { bysdb } from '@ygen/bysdb'
  import { PrpcDbController } from '@ygen/bysdb.rpc.yrpc'
  import { ICallOption } from 'jsyrpc'
  import { yrpcmsg } from 'yrpcmsg'
  import log from '@src/utils/log'
  import { Controller } from '@services/dataStore'
  import { crud } from '@ygen/crud'
  import { getMapCenter } from '@src/utils/map'
  import { StrPubSub } from 'ypubsub'
  import createSelfIncreaseStr from '@src/utils/createSelfIncreaseStr'
  import { DeleteDbController, InsertDbController } from '@utils/pubSubSubject'
  import { exNetworkType, outputDBError } from '@utils/common'
  import { getGCJ02LngLat, setGCJ02LngLat, toWGS84 } from '@utils/gcoord'
  import ControllerEdit from '@components/controllerEdit'
  import { DbName } from '@utils/permission'
  import { i18n } from '@boot/i18n'

  const controllerError = {
    dbcontroller_orgrid_fkey: i18n.global.t('dataBaseError.parUnitErr'),
    controllerhwid_key: i18n.global.t('dataBaseError.controllerHwidErr'),
    controller_pkey: i18n.global.t('dataBaseError.controllerPKeyErr'),
    controllerno_key: i18n.global.t('dataBaseError.controllerNoErr'),
    'No operation permission': i18n.global.t('dataBaseError.permissionErr'),
    'dbbysmarker_controllerrid_fkey': i18n.global.t('dataBaseError.childMarkerExist'),
  }

  export default defineComponent({
    name: 'Controller',
    mixins: [dialogMixin, dataEditMixin, controllerMixin],
    data() {
      return {
        defaultOrgID: '',
        defaultControllerNo: '',
        defaultParentRID: '',
        defaultControllerHWID: '',
        currentRow: {} as bysdb.IDbController,
      }
    },
    computed: {
      dbName() {
        return DbName.DbController
      },
      title() {
        return this.$t('menus.controller')
      },
      columns() {
        return [
          {
            name: 'ControllerNo',
            field: 'ControllerNo',
            label: this.$t('form.customID'),
            sortable: true,
            sticky: true,
          },
          {
            name: 'OrgRID', field: 'OrgRID', label: this.$t('form.unit'), sortable: true, noLeftBorder: true,
            format: (val) => {
              const parentOption = this.parentOptions.find(item => item.value === val)
              return `${parentOption ? parentOption.label : ''}`
            },
          },
          {
            name: 'ControllerType', field: 'ControllerType', label: this.$t('form.type'), sortable: true,
            format: (val) => {
              const typeOption = this.ControllerTypeOptions.find(item => item.value === val)
              return `${typeOption ? typeOption.label : ''}`
            },
          },
          {
            name: 'ControllerHWID', field: 'ControllerHWID', label: this.$t('form.controllerHWID'), sortable: true,
          },
          {
            name: 'DefaultNetworkType', field: 'DefaultNetworkType', label: this.$t('form.defaultNetworkType'),
            sortable: true,
            format: (val) => {
              return exNetworkType[val]?.() ?? val
            },
          },
          {
            name: 'Lon', field: 'Lon', label: this.$t('form.lon'),
            format: (val: number) => {
              return this.formatLngLat(val)
            },
          },
          {
            name: 'Lat', field: 'Lat', label: this.$t('form.lat'),
            format: (val: number) => {
              return this.formatLngLat(val)
            },
          },
          { name: 'MapShowLevel', field: 'MapShowLevel', label: this.$t('form.mapShowLevel') },
          {
            name: 'ChannelCount', field: 'ChannelCount', label: this.$t('form.availableChannels'),
          },
          {
            name: 'ParentRID', field: 'ParentRID', label: this.$t('form.parentControllerAndChannel'), sortable: true,
            format: (val, row) => {
              // 中继
              if (row.ControllerType === 1) {
                const Option = this.AllControllerOptions.find(item => item.value === val)
                return `${Option ? Option.label : ''} / ${row.ParentChannelNo ?? ''}`
              }
              // 基站默认没有上级
              return ''
            },
          },
          {
            name: 'ControllerDescription', field: 'ControllerDescription', label: this.$t('form.description'),
            classes: 'description',
            align: 'left',
          },
        ]
      },
      filterColumns() {
        return ['ControllerNo', 'ControllerHWID', 'OrgRID', 'ControllerDescription']
      },
      dataLen() {
        return this.data.length
      },
      isEmpty() {
        return isEmpty
      },
    },
    methods: {
      async wrapperUpdateData(data: bysdb.IDbController) {
        // 返回一个特殊的拒绝原因，以便取消消息显示
        const res = await this.warningControllerNoChange(data)
        if (!res) { return Promise.reject('-400') }
        return this.updateData(data)
      },
      rowClicked(rowData) {
        this.defaultOrgRID = rowData.OrgRID
      },
      saveMultiplexItem() {
        this.defaultOrgRID = this.currentRow.OrgRID
        this.defaultControllerNo = this.currentRow.ControllerNo
        this.defaultParentRID = this.currentRow.ParentRID
        this.defaultControllerHWID = this.currentRow.ControllerHWID
      },
      clearMultiplexItem() {
        this.defaultControllerNo = ''
        this.defaultParentRID = ''
        this.defaultControllerHWID = ''
      },
      getAllControllerNo() {
        return this.data
          .map((data: bysdb.IDbController) => {
            return data.ControllerNo
          })
      },
      getAllControllerHWID() {
        return this.data
          .map((data: bysdb.IDbController) => {
            return data.ControllerHWID
          })
      },
      getDefFormData() {
        const data: bysdb.IDbController = {
          RID: uuidV1(),
          OrgRID: '',
          ControllerNo: '',
          ControllerDescription: '',
          ControllerType: 1,
          ParentRID: '',
          Lon: 0,
          Lat: 0,
          ControllerHWID: 1,
          ChannelCount: 1,
          Setting: '{}',
          UpdatedAt: utcTime(),
          UpdatedDC: '',
          MapShowLevel: 12,
          DefaultNetworkType: 1,
          ParentChannelNo: 1,
        }
        // 默认的经、纬坐标为地图中心坐标
        const { lng, lat } = (getMapCenter() || {}) as { lng: number; lat: number }
        const centerWGS84 = toWGS84([lng, lat])
        Object.assign(data, { Lon: centerWGS84[0], Lat: centerWGS84[1] })

        return data
      },
      newRow() {
        this.currentRow = this.getDefFormData()
        //默认单位
        this.currentRow.OrgRID = this.defaultOrgRID
        //默认上级控制器
        this.currentRow.ParentRID = this.defaultParentRID
        //默认编号，默认ID
        if (this.defaultControllerNo) {
          this.currentRow.ControllerNo = createSelfIncreaseStr(this.defaultControllerNo, this.getAllControllerNo())
        }
        if (this.defaultControllerHWID) {
          this.currentRow.ControllerHWID = createSelfIncreaseStr(this.defaultControllerHWID + '',
            this.getAllControllerHWID(),
          )
        }
      },
      insertData(data: bysdb.IDbController): Promise<boolean> {
        return new Promise((resolve, reject) => {
          const options: ICallOption = {
            OnResult: (res: crud.IDMLResult, rpcCmd: yrpcmsg.Ymsg, meta?: yrpcmsg.IGrpcMeta) => {
              log.info('IDbController Insert result', res, rpcCmd, meta)
              if (res.AffectedRow === 0) {
                reject('failed')
              } else {
                resolve(true)
                // 同步到数据容器对象
                const RID = data.RID as string
                const GPS = getGCJ02LngLat(data)
                const newController = new bysdb.DbController(data)
                setGCJ02LngLat(newController, GPS)
                Controller.setData(RID, newController)
                  .setDataIndex(newController.ControllerHWID + '', RID)
                // 发布插入数据通知
                StrPubSub.publish(InsertDbController, newController)
              }
            },
            OnServerErr: (errRpc: yrpcmsg.Ymsg) => {
              log.error('IDbController Insert server error', errRpc)
              // 处理服务器响应的错误
              let reason = outputDBError(controllerError, errRpc.Optstr)
              if (errRpc.Optstr.includes('controller_pkey')) {
                //重置rid
                this.currentRow.RID = uuidV1()
              }
              // 同一基站下控制器通道不能重复
              const r = this.processControllerChannelCannotRepeated(errRpc.Optstr)
              if (r) { reason = r }

              reject(reason)
            },
            OnLocalErr: (err: any) => {
              log.error('IDbController Insert local error', err)
              reject('local')
            },
            OnTimeout: (v: any) => {
              log.warn('IDbController Insert timeout', v)
              const options = {
                action: this.$t('common.add'),
                name: data.ControllerNo,
              }
              const reason = this.$t('message.timeout', options)
              reject(reason)
            },
          }
          PrpcDbController.Insert(data, options)
        })
      },
      deleteData(data: bysdb.IDbController): Promise<boolean> {
        return new Promise((resolve, reject) => {
          const options: ICallOption = {
            OnResult: (res: crud.IDMLResult, rpcCmd: yrpcmsg.Ymsg, meta?: yrpcmsg.IGrpcMeta) => {
              log.info('IDbController Delete result', res, rpcCmd, meta)
              if (res.AffectedRow === 0) {
                reject('failed')
              } else {
                resolve(true)
                // 同步到数据容器对象
                Controller.deleteData(data.RID as string)
                // 发布删除数据通知
                StrPubSub.publish(DeleteDbController, data)
              }
            },
            OnServerErr: (errRpc: yrpcmsg.Ymsg) => {
              log.error('IDbController Delete server error', errRpc)
              // 处理服务器响应的错误
              let reason = outputDBError(controllerError, errRpc.Optstr)
              // // fkdborgparentrid 该单位为其他单位的上级，需要删除所有下级单位才能删除
              // if (errRpc.Optstr.includes('fkdborgparentrid')) {
              //   reason = this.$t('message.deleteHasChildUnit', { name: data.ControllerNo })
              // }
              reject(reason)
            },
            OnLocalErr: (err: any) => {
              log.error('IDbController Delete local error', err)
              reject('local')
            },
            OnTimeout: (v: any) => {
              log.warn('IDbController Delete timeout', v)
              const options = {
                action: this.$t('common.delete'),
                name: data.ControllerNo,
              }
              const reason = this.$t('message.timeout', options)
              reject(reason)
            },
          }
          PrpcDbController.Delete(data, options)
        })
      },
      batchAddData(dbList: bysdb.IDbController[], progress?: (value: number) => void): Promise<any> {
        const duplicateData: { [key: string]: any } = {}
        const parentCache: { [key: string]: any } = {}
        const typesCache: { [key: string]: any } = {}
        const networkTypeCache: { [key: string]: any } = {}
        return new Promise((resolve) => {
          // 缓存有异常的数据
          const errorDataList: bysdb.IDbController[] = []
          // 使用迭代器逐个添加到数据库
          const it = dbList[Symbol.iterator]()
          // 当前处理的第几个数据
          let currentCount = 0
          const insert = async (item) => {
            // 进度信息，当前处理第几个数据
            progress?.(currentCount++)
            const { done, value } = item
            // 已经处理过每一个数据
            if (done) {
              // 已经处理过每一个数据，结束
              return resolve(errorDataList)
            }

            // 合并默认参数
            const data: bysdb.IDbController = this.getDefFormData()
            Object.assign(data, value)

            // 通过控制器的编号判断数据是否重复，过滤本地已经存在的数据
            // const localData = Controller.getDataByIndex(data.ControllerHWID + '')
            const localData = duplicateData[data.ControllerNo + ''] ||
              this.data.find(item => item.ControllerNo === data.ControllerNo)
            if (localData) {
              duplicateData[data.ControllerNo + ''] = localData
              value.$info = this.$t('message.duplicateData')
              errorDataList.push(value)
              // 处理下一个数据
              insert(it.next())
              return
            }

            // 重置单位上级RID,当前OrgRID为上级单位简称，需要转换为对应的UUID
            // 查找上级是否为本地数据，暂时不处理没有权限的上级
            const parentOption = parentCache[data.OrgRID as string] ||
              this.parentOptions.find(item => item.label === data.OrgRID)
            if (!parentOption) {
              value.$info = this.$t('message.parentUnitErr')
              errorDataList.push(value)
              // 处理下一个数据
              insert(it.next())
              return
            }
            parentCache[data.OrgRID as string] = parentOption
            data.OrgRID = parentOption.value

            // 控制器类型，默认基站:2
            const typeOption = typesCache[data.ControllerType + ''] ||
              this.ControllerTypeOptions.find(item => item.label === data.ControllerType)
            if (typeOption) {
              typesCache[data.ControllerType + ''] = typeOption
              data.ControllerType = typeOption.value
            } else {
              data.ControllerType = 2
            }
            // 中继需要处理上级控制器
            if (data.ControllerType === 1) {
              // 处理上级控制器，和控制器通道
              const parentStr: string[] = (data.ParentRID || '').split('/').map(s => s.trim())
              const [ParentRID, ParentChannelNo]: string[] = parentStr
              if (parentStr.length >= 2) {
                // 找不到上级的控制器，则不能插入数据
                // const controller = Controller.getDataByIndex(ParentRID) as bysdb.IDbController | undefined
                const controller = this.data.find(item => item.ControllerNo === ParentRID)
                if (!controller) {
                  value.$info = this.$t('import.notFoundController')
                  errorDataList.push(value)
                  // 处理下一个数据
                  insert(it.next())
                  return
                }
                data.ParentRID = controller.RID ?? ''
              } else {
                data.ParentRID = ''
              }
              // 中继对应的上级控制器的通道
              data.ParentChannelNo = ~~ParentChannelNo || 1
              // 控制器可用信道数，中继控制器为1,基站按实际情况
              data.ChannelCount = 1
            } else {
              data.ParentRID = ''
            }

            // 链路网络类型，默认为有线网络：1
            const networkTypeOption = networkTypeCache[data.DefaultNetworkType + ''] ||
              this.NetworkType.find(item => item.label === data.DefaultNetworkType)
            if (networkTypeOption) {
              networkTypeCache[data.DefaultNetworkType + ''] = networkTypeOption
              data.DefaultNetworkType = networkTypeOption.value
            } else {
              data.DefaultNetworkType = 1
            }

            // 转换数字类型参数
            const CoverPropList = ['Lon', 'Lat', 'ControllerHWID', 'ChannelCount', 'MapShowLevel']
            this.coverPropToNumber(data, CoverPropList)

            const CoverPropStringList = ['ControllerNo', 'ControllerDescription', 'ParentRID', 'OrgRID']
            this.coverPropToString(data, CoverPropStringList)
            const err: boolean | string = this.checkDataByRules(data, this.rules)
            if (err === true) {
              // 添加数据到数据库
              await this.insertData(data)
                .catch((error) => {
                  value.$info = error
                  errorDataList.push(value)
                })
            } else {
              value.$info = err
              errorDataList.push(value)
            }

            // 处理下一个数据
            insert(it.next())
          }
          insert(it.next())
        })
      },
    },
    components: {
      ControllerEdit,
    },
  })
</script>

<style lang="scss">
  .controller-page-cls:not(.maximized) {
    max-width: 60vw !important;
    width: 60vw !important;
  }
</style>

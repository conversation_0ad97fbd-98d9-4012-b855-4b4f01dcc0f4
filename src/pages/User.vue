<template>
  <!-- visible,maximized in dataEdit mixins -->
  <modal
    v-model="visible"
    v-model:maximized="maximized"
    :contentHeight="dataLen ? '68vh' : 'auto'"
    :content-class="'model-user-dlg'"
  >
    <template v-slot:header><span>{{ $t('menus.user') }}</span></template>

    <data-edit
      :data="data"
      :columns="columns"
      :form-title="$t('menus.user')"
      :can-add="canAdd"
      :can-edit="canEdit"
      :can-delete="canDelete"
      :insert="insertData"
      :update="updateData"
      :delete="deleteData"
      :import-data="batchAddData"
      :renderChildRow="renderChildRow"
      v-model:currentRow="currentRow"
      @new-row="newRow"
      @rwo-clicked="rowClicked"
      @save-multiplex-item="saveMultiplexItem"
      @clear-multiplex-item="clearMultiplexItem"
      @hide="hidePage"
      ref="userDataEdit"
    >

      <template v-slot:form-content>
        <div class="fit row wrap form-user q-col-gutter-x-sm">
          <div class="col col-xs-12 col-sm-6">
            <q-input
              v-model="currentRow.UserID"
              :label="$t('form.customID')"
              outlined
              dense
              clearable
              autofocus
              lazy-rules
              :rules="rules.UserID"
              :maxlength="16"
            />
          </div>
          <div class="col col-xs-12 col-sm-6">
            <q-input
              v-model="currentRow.UserName"
              :label="$t('form.userName')"
              outlined
              dense
              clearable
              lazy-rules
              :rules="rules.UserName"
              :maxlength="16"
            />
          </div>
          <div class="col col-xs-12 col-sm-6">
            <!-- parentOptions,filterParent in currentRowEdit mixins -->
            <q-select
              v-model="currentRow.OrgRID"
              :label="$t('form.unit')"
              outlined
              dense
              clearable
              lazy-rules
              :rules="rules.OrgRID"
              :options="parentOptions"
              @filter="filterParent"
              options-dense
              map-options
              emit-value
            />
          </div>
          <div class="col col-xs-12 col-sm-6">
            <q-input
              v-model="currentRow.LoginName"
              :label="$t('form.loginName')"
              outlined
              dense
              clearable
              lazy-rules
              :rules="[]"
              :maxlength="16"
              class="mb-3"
            />
          </div>
          <div class="col col-xs-12 col-sm-6">
            <q-input
              v-model="currentRow.LoginPass"
              type="password"
              :label="$t('form.loginPassword')"
              outlined
              dense
              clearable
              lazy-rules
              :rules="[]"
              :maxlength="16"
              class="mb-3"
            />
          </div>
          <div class="col col-xs-12 col-sm-6">
            <q-input
              v-model="currentRow.Phone"
              :label="$t('form.phone')"
              outlined
              dense
              clearable
              lazy-rules
              :rules="rules.Phone"
              :maxlength="32"
              class="mb-3"
            />
          </div>
          <div class="col col-12 ">
            <q-input
              type="textarea"
              v-model="currentRow.Note"
              :label="$t('form.note')"
              outlined
              dense
              clearable
              autogrow
              lazy-rules
              :rules="rules.Note"
              :maxlength="1024"
            />
          </div>
        </div>
      </template>


      <template v-slot:form-footer="{ isLoading, editStatus, dataEditStatus, continueEdit, confirm }">
        <div class="col col-xs-12 justify-center flex gap-2">
          <q-btn
            class="q-mx-sm w-24"
            color="primary"
            :label="$t('common.roleEdit')"
            size="md"
            :disable="!canEdit"
            @click="roleEdit"
          />

          <template v-if="editStatus === dataEditStatus.add">
            <q-btn
              class="q-mx-sm w-24"
              color="info"
              :label="$t('form.keepAdding')"
              size="md"
              :loading="isLoading"
              :disable="!canAdd || isLoading"
              @click="continueEdit"
            />
            <q-btn
              class="q-mx-sm w-24"
              color="primary"
              :label="$t('common.confirm')"
              size="md"
              :loading="isLoading"
              :disable="!canAdd || isLoading"
              @click="customConfirm(confirm)"
            />
          </template>
          <q-btn
            v-else
            class="q-mx-sm w-24"
            color="primary"
            :label="$t('common.confirm')"
            size="md"
            :loading="isLoading"
            :disable="!canEdit || isLoading"
            @click="customConfirm(confirm)"
          />
        </div>
      </template>

      <!-- 操作列控件 -->
      <template v-slot:data-actions="{ props, editDataRow, deleteDataRow }">
        <q-btn
          class="btn-perm data-action-btn"
          icon="security"
          color="orange-6"
          size="sm"
          flat
          round
          dense
          :disable="!canEdit"
          @click="quickShowInnerPage(props.row)"
        >
          <q-tooltip>
            {{ $t('common.UserQuickPerm') }}
          </q-tooltip>
        </q-btn>
        <q-btn
          class="q-ml-sm data-action-btn"
          color="primary"
          icon="create"
          size="sm"
          flat
          round
          dense
          :disable="!canEdit"
          @click="editDataRow(props.row)"
        >
          <q-tooltip>
            {{ $t('common.edit') }}
          </q-tooltip>
        </q-btn>
        <q-btn
          class="q-ml-sm data-action-btn"
          color="negative"
          icon="delete"
          size="sm"
          flat
          round
          dense
          :disable="!canDelete"
          @click="deleteDataRow(props.row)"
        >
          <q-tooltip>
            {{ $t('common.delete') }}
          </q-tooltip>
        </q-btn>
      </template>
    </data-edit>
    <q-dialog
      v-model="UserRoleDlg"
      persistent
      :class="{ 'z-max': true }"
      @hide="hideUserRolePage"
    >
      <q-card class="full-width">
        <q-bar class="sticky-header bg-primary text-white z-max">
          <div>{{ $t('userRole.userRole') }}/{{ getUserName }}</div>
          <q-space />
          <q-btn
            flat
            icon="close"
            v-close-popup
          />
        </q-bar>
        <q-card-section class="q-pa-sm">
          <!--用户控制区-->
          <div class="row no-wrap overflow-hidden full-width card-section-height">
            <q-tabs
              v-model="curTab"
              dense
              vertical
              class="bg-grey-2 inner-page-tabs"
              active-color="primary"
              @update:model-value="tabChange"
            >
              <q-tab
                v-for="tab in tabs"
                :key="tab"
                :name="tab"
                :label="tab === 'tabUnit' ? $t('userTab.unit') : $t('userTab.role')"
              />
            </q-tabs>
            <q-separator vertical></q-separator>
            <div
              ref="scrollArea"
              class="full-height overflow-auto col-grow"
            >
              <q-scroll-observer
                @scroll="scrollHandler"
                debounce="300"
              >
              </q-scroll-observer>
              <tabUnit
                ref="tabUnit"
                :userRID="currentRow.RID"
              />
              <tabRole
                ref="tabRole"
                :userRID="currentRow.RID"
              />
            </div>
          </div>
          <!--用户控制区-->
        </q-card-section>
      </q-card>
    </q-dialog>
  </modal>
</template>

<script lang="ts">
  import { defineComponent, h } from 'vue'
  import dialogMixin from '@src/utils/mixins/dialog'
  import dataEditMixin from '@src/utils/mixins/editor'
  import { utcTime } from '@src/utils/dayjs'
  import { v1 as uuidV1 } from 'uuid'
  import { isEmpty, maxLength, required } from '@src/utils/validation'
  import { user } from '@src/ygen/user'
  import { PrpcDbUser } from '@src/ygen/user.rpc.yrpc'
  import { ICallOption } from 'jsyrpc'
  import { yrpcmsg } from 'yrpcmsg'
  import log from '@src/utils/log'
  import dataStore, { Role, User, UserRole } from '@src/services/dataStore'
  import { DataName } from '@src/store/data'
  import { crud } from '@ygen/crud'
  import { GET_DATA, NS } from '@src/store/data/methodTypes'
  import { encodeUserPassword } from '@utils/crypto'
  import { BaseRoleRids, DbName, sortData } from '@utils/permission'
  import { user as userPermission } from '@ygen/userPermission'
  import createSelfIncreaseStr from '@src/utils/createSelfIncreaseStr'
  import { QueryFinishStatus, queryRoleRelateData } from '@src/services/queryData'
  import { scroll, QChip } from 'quasar'
  import { BuiltInAttrs, BuiltInAttrTranslate, outputDBError } from '@utils/common'
  import { i18n } from '@boot/i18n'
  import { cloneDeep } from 'lodash'
  import { StrPubSub } from 'ypubsub'
  import { UpdateDbUser } from '@utils/pubSubSubject'
  import { FormRules } from '@utils/bysdb.type'
  import { org } from '@ygen/org'
  import RoleTab from '@components/userRole/RoleTab.vue'
  import UnitTab from '@components/userRole/UnitTab.vue'

  const userError = {
    dbuser_orgrid_fkey: i18n.global.t('dataBaseError.parUnitErr'),
    dbuser_loginname_key: i18n.global.t('dataBaseError.loginNameErr'),
    dbuser_userid_key: i18n.global.t('dataBaseError.userNoErr'),
    dbuserrole_userrid_fkey: i18n.global.t('dataBaseError.userRoleErr'),
    'No operation permission': i18n.global.t('dataBaseError.permissionErr'),
    'insert DBUser-Role failed.Rollback DBUser': i18n.global.t('dataBaseError.permissionErr'),
  }
  // 开始请求角色相关的数据
  if (!QueryFinishStatus[DataName.Role]) {
    queryRoleRelateData()
  }
  //滚动条相关函数
  const { setVerticalScrollPosition } = scroll
  export default defineComponent({
    name: 'User',
    mixins: [dialogMixin, dataEditMixin],
    data() {
      return {
        defaultUserID: '',
        defaultOrgRID: '',
        defaultUserName: '',
        currentRow: {},
        UserRoleDlg: false,
        /*用户角色管理弹窗*/
        curTab: 'tabUnit',
        tabs: ['tabUnit', 'tabRole'],
        /*用户角色管理弹窗*/
        isUnRepeatInsert: true,
        isInserted: false,
      }
    },
    computed: {
      dbName() {
        return DbName.DbUser
      },
      columns() {
        return [
          {
            name: 'UserID',
            field: 'UserID',
            label: this.$t('form.customID'),
            sortable: true,
            sticky: true,
            align: 'left',
          },
          { name: 'UserName', field: 'UserName', label: this.$t('form.userName'), sortable: true, noLeftBorder: true },
          {
            name: 'OrgRID', field: 'OrgRID', label: this.$t('form.unit'), sortable: true,
            format: (val) => {
              const parentOption = this.parentOptions.find(item => item.value === val)
              return `${parentOption ? parentOption.label : ''}`
            },
          },
          // {
          //   name: 'UserType', field: 'UserType', label: this.$t('form.type'), sortable: true,
          //   format: (val) => {
          //     return `${val}`
          //   },
          // },
          { name: 'LoginName', field: 'LoginName', label: this.$t('form.loginName'), sortable: true },
          { name: 'Phone', field: 'Phone', label: this.$t('form.phone') },
          {
            name: 'Note', field: 'Note', label: this.$t('form.note'),
            classes: 'description',
            align: 'left',
          },
        ]
      },
      rules(): FormRules {
        const _required = (val) => required(val) || this.$t('rules.required')
        const _maxLength = (val) => maxLength(val, 16) || this.$t('rules.maxLength', { len: 16 })

        return {
          UserID: [
            val => _required(val),
            val => _maxLength(val),
          ],
          UserName: [
            val => _required(val),
            val => _maxLength(val),
          ],
          OrgRID: [
            val => _required(val),
          ],
          Phone: [
            // val => isEmpty(val) || isPhone(val) || this.$t('rules.correctPhone'),
          ],
          Note: [
            val => maxLength(val, 1024) || this.$t('rules.maxLength', { len: 1024 }),
          ],
        }
      },
      dataLen() {
        return this.data.length
      },
      data() {
        const data = this.$store.getters[`${NS}/${GET_DATA}`](DataName.User)
          .filter(item => item.RID !== this.$store.state.UserRID)
        return User.sortBy(data, { sortBy: 'UserID' })
      },
      isEmpty() {
        return isEmpty
      },
      //找出store中用户的所有用户-角色
      userRoleData() {
        return this.$store.getters[`${NS}/${GET_DATA}`](DataName.UserRole)
      },
      //找出store中所有角色
      allRoleData() {
        return sortData(this.$store.getters[`${NS}/${GET_DATA}`](DataName.Role), { prop: 'RoleName' })
      },
      allUserOrgPrivilege() {
        return this.$store.getters[`${NS}/${GET_DATA}`](DataName.UserOrgPrivilege)
      },
      unitData() {
        const dataList = this.$store.getters[`${NS}/${GET_DATA}`](DataName.Unit)
        return sortData<org.IDbOrg>(dataList, {
          descending: this.$store.state.Settings.descending,
          prop: 'SortValue',
        })
      },
      getUserName() {
        return this.$store.getters[`${NS}/${GET_DATA}`](DataName.User)
          .find(item => item.RID === this.currentRow.RID)?.UserName
      },
    },
    methods: {
      hidePage() {
        this.isUnRepeatInsert = true
        this.isInserted = false
      },
      hideUserRolePage() {
        //如果是编辑按钮进入，则不用修改
        if (this.isInserted) {
          this.$refs.userDataEdit?.newRow()
        }
      },
      customConfirm(confirm) {
        if (this.isInserted) {
          //如果newRow中已经插入到数据库，则跳过
          if (this.$refs.userDataEdit) {
            this.$refs.userDataEdit.visible = false
          }
          return
        }
        confirm()
      },
      /*用户角色弹窗*/
      quickShowInnerPage(row) {
        this.currentRow = row
        this.roleEdit()
      },
      //编辑角色
      roleEdit() {
        if (this.isUnRepeatInsert) {
          this.UserRoleDlg = true
          return
        }
        this.$refs.userDataEdit?.validate()
          .then(() => {
            this.insertData(this.currentRow)
              .then(() => {
                this.$q.notify({
                  message: this.$t('message.addSuccess') as string,
                  color: 'positive',
                  icon: 'check_circle',
                  position: 'top',
                })
                this.UserRoleDlg = true
                this.isUnRepeatInsert = true
                this.isInserted = true
              })
              .catch((reason: string) => {
                this.$q.notify({
                  message: reason,
                  color: 'red',
                  icon: 'error',
                  position: 'top',
                })
              })
          })
      },
      tabChange(name) {
        const el = this.$refs[name].$el
        //-10是两个component的margin
        setVerticalScrollPosition(this.$refs.scrollArea, el.offsetTop - 10, 350)
      },
      scrollHandler(e) {
        const { position } = e
        //界面前1/3有这个dom元素则默认进入到该tab
        const val = this.$refs.scrollArea.offsetHeight / 3
        for (let k in this.$refs) {
          if (!k.startsWith('tab-')) {
            continue
          }
          const el = this.$refs[k].$el
          if (position.top >= el.offsetTop - val && this.tab !== k) {
            this.curTab = k
          }
        }
      },
      /*用户角色弹窗*/

      rowClicked(rowData) {
        //默认单位
        this.defaultOrgRID = rowData.OrgRID
      },
      saveMultiplexItem() {
        this.defaultUserID = this.currentRow.UserID
        this.defaultOrgRID = this.currentRow.OrgRID
        this.defaultUserName = this.currentRow.UserName
      },
      clearMultiplexItem() {
        this.defaultUserID = ''
        this.defaultUserName = ''
      },
      getAllUserID() {
        return this.data
          .map((data: user.IDbUser) => {
            return data.UserID
          })
      },
      getAllUserName() {
        return this.data
          .map((data: user.IDbUser) => {
            return data.UserName
          })
      },
      getDefFormData() {
        const data: user.IDbUser = {
          RID: uuidV1(),
          UserID: '',
          UserName: '',
          OrgRID: '',
          UserType: 0,
          Phone: '',
          LoginName: '',
          LoginPass: '',
          // Image: DefUuid,
          Setting: '{}',
          UpdatedAt: utcTime(),
          UpdatedDC: '',
        }
        return data
      },
      newRow() {
        //设置标记为必须插入  其他则不用插入
        this.isUnRepeatInsert = false
        this.currentRow = this.getDefFormData()
        //设置默认的单位
        this.currentRow.OrgRID = this.defaultOrgRID
        //设置对应格式自增的编号、用户名
        this.currentRow.UserID = createSelfIncreaseStr(this.defaultUserID, this.getAllUserID())
        this.currentRow.UserName = createSelfIncreaseStr(this.defaultUserName, this.getAllUserName())
      },
      insertData(data: user.IDbUser): Promise<boolean> {
        return new Promise((resolve, reject) => {
          // 需要对密码进行加密编码
          if (data.LoginName) {
            data.LoginPass = encodeUserPassword(data.LoginName as string, data.LoginPass as string)
          } else {
            data.LoginPass = ''
          }
          const options: ICallOption = {
            OnResult: (res: crud.IDMLResult, rpcCmd: yrpcmsg.Ymsg, meta?: yrpcmsg.IGrpcMeta) => {
              log.info('IDbUser Insert result', res, rpcCmd, meta)
              if (res.AffectedRow === 0) {
                reject('failed')
              } else {
                // 同步到数据容器对象
                const RID = data.RID as string
                const newUser = new user.DbUser(data)
                User.setData(RID, newUser)
                  .setDataIndex(newUser.UserName as string, RID)

                resolve(true)
              }
            },
            OnServerErr: (errRpc: yrpcmsg.Ymsg) => {
              log.error('IDbUser Insert server error', errRpc)
              // 处理服务器响应的错误
              let reason = outputDBError(userError, errRpc.Optstr)
              // dborg_orgid_key 自编号重复
              /*if (errRpc.Optstr.includes('dborg_orgid_key')) {
                reason = this.$t('message.duplicateOrgID', { name: data.UserID })
              }*/
              reject(reason)
            },
            OnLocalErr: (err: any) => {
              log.error('IDbUser Insert local error', err)
              reject('local')
            },
            OnTimeout: (v: any) => {
              log.warn('IDbUser Insert timeout', v)
              const options = {
                action: this.$t('common.add'),
                name: data.UserName,
              }
              const reason = this.$t('message.timeout', options)
              reject(reason)
            },
          }
          PrpcDbUser.Insert(data, options)
        })
      },
      updateData(data: user.IDbUser): Promise<boolean> {
        return new Promise((resolve, reject) => {
          const oldData = dataStore.User.getData(data.RID as string) as user.IDbUser
          if (!data.LoginName) {
            // 没有登录名，则无需设置密码
            data.LoginPass = ''
          } else if (oldData.LoginPass !== data.LoginPass) {
            // 当前密码与更新前密码不一致，则更新密码
            data.LoginPass = encodeUserPassword(data.LoginName as string, data.LoginPass as string)
          }
          const options: ICallOption = {
            OnResult: (res: crud.IDMLResult, rpcCmd: yrpcmsg.Ymsg, meta?: yrpcmsg.IGrpcMeta) => {
              log.info('IDbUser Update result', res, rpcCmd, meta)
              if (res.AffectedRow === 0) {
                reject('failed')
              } else {
                // 同步到数据容器对象
                const RID = data.RID as string
                const oldData = cloneDeep(User.getData(RID))
                const newUser = new user.DbUser(data)
                User.setData(RID, newUser)
                  .setDataIndex(newUser.UserName as string, RID)

                StrPubSub.publish(UpdateDbUser, newUser, oldData)
                resolve(true)
              }
            },
            OnServerErr: (errRpc: yrpcmsg.Ymsg) => {
              log.error('IDbUser Update server error', errRpc)
              // 处理服务器响应的错误
              let reason = outputDBError(userError, errRpc.Optstr)
              // dborg_orgid_key 自编号重复
              /*if (errRpc.Optstr.includes('dborg_orgid_key')) {
                reason = this.$t('message.duplicateOrgID', { name: data.UserID })
              }*/
              reject(reason)
            },
            OnLocalErr: (err: any) => {
              log.error('IDbUser Update local error', err)
              reject('local')
            },
            OnTimeout: (v: any) => {
              log.warn('IDbUser Update timeout', v)
              const options = {
                action: this.$t('common.modify'),
                name: data.UserName,
              }
              const reason = this.$t('message.timeout', options)
              reject(reason)
            },
          }
          PrpcDbUser.Update(data, options)
        })
      },
      deleteData(data: user.IDbUser): Promise<boolean> {
        return new Promise((resolve, reject) => {
          const options: ICallOption = {
            OnResult: (res: crud.IDMLResult, rpcCmd: yrpcmsg.Ymsg, meta?: yrpcmsg.IGrpcMeta) => {
              log.info('IDbUser Delete result', res, rpcCmd, meta)
              if (res.AffectedRow === 0) {
                reject('failed')
              } else {
                // 同步到数据容器对象
                const key = data.RID as string
                User.deleteData(key)

                // 用户被删除后，同步删除用户角色数据
                const userRoleData: user.IDbUserRole[] = UserRole.getDataList()
                userRoleData.filter(v => v.UserRID === key)
                  .forEach(v => {
                    UserRole.deleteData(v.RID as string)
                  })

                resolve(true)
              }
            },
            OnServerErr: (errRpc: yrpcmsg.Ymsg) => {
              log.error('IDbUser Delete server error', errRpc)
              // 处理服务器响应的错误
              let reason = outputDBError(userError, errRpc.Optstr)
              // // fkdborgparentrid 该单位为其他单位的上级，需要删除所有下级单位才能删除
              // if (errRpc.Optstr.includes('fkdborgparentrid')) {
              //   reason = this.$t('message.deleteHasChildUnit', { name: data.UserName })
              // }
              reject(reason)
            },
            OnLocalErr: (err: any) => {
              log.error('IDbUser Delete local error', err)
              reject('local')
            },
            OnTimeout: (v: any) => {
              log.warn('IDbUser Delete timeout', v)
              const options = {
                action: this.$t('common.delete'),
                name: data.UserName,
              }
              const reason = this.$t('message.timeout', options)
              reject(reason)
            },
          }
          PrpcDbUser.Delete(data, options)
        })
      },
      batchAddData(dbList: user.IDbUser[], progress?: (value: number) => void): Promise<any> {
        return new Promise((resolve) => {
          // 缓存有异常的数据
          const errorDataList: user.IDbUser[] = []
          // 使用迭代器逐个添加到数据库
          const it = dbList[Symbol.iterator]()
          // 当前处理的第几个数据
          let currentCount = 0
          const insert = async (item) => {
            // 进度信息，当前处理第几个数据
            progress?.(currentCount++)
            const { done, value } = item
            // 已经处理过每一个数据
            if (done) {
              // 已经处理过每一个数据，结束
              return resolve(errorDataList)
            }

            // 合并默认参数
            const data: user.IDbUser = this.getDefFormData()
            Object.assign(data, value)

            // 过滤本地已经存在的数据
            const localData = User.getDataByIndex(data.UserID as string)
            if (localData) {
              value.$info = this.$t('message.duplicateData')
              errorDataList.push(value)
              // 处理下一个数据
              insert(it.next())
              return
            }

            // 重置单位上级RID,当前OrgRID为上级单位简称，需要转换为对应的UUID
            // 查找上级是否为本地数据，暂时不处理没有权限的上级
            const parentOption = this.parentOptions.find(item => item.label === data.OrgRID)
            if (!parentOption) {
              value.$info = this.$t('message.parentUnitErr')
              errorDataList.push(value)
              // 处理下一个数据
              insert(it.next())
              return
            }
            data.OrgRID = parentOption.value

            // // 转换数字类型参数
            // const CoverPropList = ['UserType']
            // this.coverPropToNumber(data, CoverPropList)

            const CoverTOStringList = ['UserID', 'OrgRID', 'UserName', 'LoginName', 'Phone', 'Note']
            this.coverPropToString(data, CoverTOStringList)
            const err = this.checkDataByRules(data, this.rules)
            if (err === true) {
              // 添加数据到数据库
              await this.insertData(data)
                .catch((error) => {
                  value.$info = error
                  errorDataList.push(value)
                })
            } else {
              value.$info = err
              errorDataList.push(value)
            }

            // 处理下一个数据
            insert(it.next())
          }
          insert(it.next())
        })
      },
      renderChildRow({ row }) {
        // 通过用户RID查找到所有的已分配的用户角色关系数据
        const userRoles: user.IDbUserRole[] = this.userRoleData
          .filter(v => (v.UserRID === row.RID && !BaseRoleRids.includes(v.RoleRID + '')))
        // 通过用户角色关系数据查找所有的角色数据
        const roles: userPermission.IDbRole[] = userRoles
          .map(v => {
            return Role.getData(v.RoleRID as string) as userPermission.IDbRole | undefined
          })
          .filter(v => v !== undefined) as userPermission.IDbRole[]
        // 生成子行数据VNode节点
        // const h = this.$createElement
        const renderChip = (role: userPermission.IDbRole) => {
          return h(QChip, {
            color: 'primary',
            textColor: 'white',
            // icon: 'bookmark',
            size: '12px',
            clickable: true,
            dense: true,
            outline: true,
          }, () => BuiltInAttrs.includes(role.RoleName as string)
            ? BuiltInAttrTranslate()[role.RoleName as string]
            : role.RoleName as string)
        }
        const roleTitle = () => {
          return h('span', {
            class: {
              'role-title': true,
            },
          }, `${this.$t('form.role')}: `)
        }

        return roles.length === 0 ? '' : h('div', {
          class: {
            'user-row-child': true,
          },
        }, [roleTitle(), ...roles.map(v => renderChip(v))])
      },
    },
    components: {
      /*权限弹窗相关*/
      // tabRole: () => import('@components/userRole/RoleTab.vue'),
      // tabUnit: () => import('@components/userRole/UnitTab.vue'),
      tabRole: RoleTab,
      tabUnit: UnitTab,
      /*权限弹窗相关*/
    },
  })
</script>

<style lang="scss">
  .model-user-dlg:not(.maximized) {
    max-width: 700px !important;
  }

  .form-user {
    width: 65vw;
    max-width: 560px;
    padding-left: 8px;
  }

  .sticky-header {
    position: sticky;
    top: 0;
  }

  .card-section-height {
    height: 50vh;
  }

  .inner-page-tabs {
    width: 100px;
  }
</style>

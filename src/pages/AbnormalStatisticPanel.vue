<template>
  <modal
    v-model="visible"
    v-model:maximized="maximized"
    :contentHeight="'60vh'"
    content-class="abnormal-marker-statistic-panel q-pa-none"
    ref="modal"
  >
    <template #header>
      <span v-text="title"></span>
    </template>

    <template #header-bottom>
      <q-tabs
        v-if="hasPermission"
        v-model="panel"
        dense
        align="justify"
        indicator-color="orange"
        class="bg-primary text-white"
      >
        <q-tab
          class="statistic-content-tab"
          name="bysMarker"
          :label="$t('form.abnormalMarkers')"
        />
        <q-tab
          class="statistic-content-tab"
          name="controller"
          :label="$t('form.abnormalControllers')"
        />
      </q-tabs>
    </template>
    <q-tab-panels
      v-model="panel"
      animated
      infinite
      keep-alive
      class="q-pa-none"
    >
      <q-tab-panel
        name="bysMarker"
        class="q-pa-none full-height"
      >
        <abnormal-marker />
      </q-tab-panel>

      <q-tab-panel
        v-if="hasPermission"
        name="controller"
        class="q-pa-none full-height"
      >
        <abnormal-controller />
      </q-tab-panel>
    </q-tab-panels>
  </modal>
</template>


<script lang="ts">
  import dialogMixin from '@utils/mixins/dialog'
  import { AbnormalController, AbnormalMarker } from '@components/abnormalStatistic'
  import { isHaveMaintainPerm } from '@utils/common'
  import { defineComponent } from 'vue'

  export default defineComponent({
    name: 'AbnormalStatisticPanel',
    mixins: [dialogMixin],
    data() {
      return {
        panel: 'bysMarker',
      }
    },
    components: {
      AbnormalController,
      AbnormalMarker,
    },
    computed: {
      title() {
        return this.$t('form.abnormalData')
      },
      hasPermission() {
        return isHaveMaintainPerm()
      },
    },
  })
</script>

<style lang="scss">
  .abnormal-marker-statistic-panel:not(.maximized) {
    width: 55vw;
    max-width: unset !important;
  }

  $stickyBgColor: #fff;

  .abnormal-marker-table {
    max-width: 100vw;

    &.q-table--dense .q-table {

      th,
      td {
        padding: 2px 4px;
      }
    }

    thead {
      tr th {
        position: sticky;
        background-color: $stickyBgColor;
        // 表格头部数据区的粘贴层叠要比内容区的高
        z-index: 2;
        top: 0;
      }
    }
  }
</style>

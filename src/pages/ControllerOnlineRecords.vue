<template>
  <modal
    v-model="visible"
    v-model:maximized="maximized"
    :contentHeight="maxHeight ? '60vh' : 'auto'"
    content-class="controller-history-modal"
    ref="controllerHistory"
    @hide="maxHeight = false"
  >
    <template #header>
      <span v-text="title"></span>
    </template>
    <query-history
      ref="form-content"
      :default-time-diff="defaultTimeDiff"
      :time-max-range="timeMaxRange"
      :export-name="title"
      :table-columns="columns"
      :filter-columns='filterColumns'
      :time-limit-sql-name="timeField"
      :can-query="canQuery"
      :extraExportFunc="extraExportFunc"
      @submit-clicked="onSubmit"
      @query-canceled="queryCanceled"
    >
      <template #form>
        <div class="query-form">
          <q-select
            v-model="orgRID"
            :label="$t('form.parentUnit')"
            outlined
            dense
            clearable
            lazy-rules
            :rules="[]"
            :options="orgRIDOptions"
            @filter="filterOrgRID"
            options-dense
            map-options
            emit-value
            @update:model-value="orgRIDChange"
          />
          <!-- 以前的控制器历史是把4G界桩的上线历史也当作控制器历史展示, 也可以展示选择4G界桩来筛选查看历史-->
          <!--<q-select
            v-model="modelValue"
            :label="$t('form.controllerID')"
            outlined
            dense
            clearable
            lazy-rules
            :rules="[]"
            :options="RIDOptions"
            @filter="filterControllerRID"
            @clear="clearControllerRID"
            options-dense
            map-options
            emit-value
            use-input
          >
            <template v-slot:option="scope">
              <q-expansion-item
                expand-separator
                default-opened
                dense
                header-class="text-weight-bold"
                :label="scope.opt.label"
              >
                <template
                  v-for="child in scope.opt.children"
                  :key="child.label"
                >
                  <q-item
                    clickable
                    v-ripple
                    v-close-popup
                    dense
                    @click="controllerRIDChange(child)"
                    :class="{ 'bg-light-blue-1': controllerRID === child.label }"
                  >
                    <q-item-section>
                      <q-item-label
                        v-html="child.label"
                        class="q-ml-md"
                      ></q-item-label>
                    </q-item-section>
                  </q-item>
                </template>
              </q-expansion-item>
            </template>
          </q-select>-->
          <q-select
            v-model="controllerRID"
            :label="$t('form.controllerID')"
            outlined
            dense
            clearable
            lazy-rules
            :rules="[]"
            :options="controllerRIDOptions"
            @filter="filterControllerRID"
            @clear="clearControllerRID"
            options-dense
            map-options
            emit-value
            use-input
          >
          </q-select>
        </div>
      </template>

      <template v-slot:my-body-cell-Controller="{ value }">
        <q-icon
          :name="value?.icon"
          :class="[value?.customClass]"
        >
          <q-tooltip
            anchor="top middle"
            :offset="[0, 30]"
          >
            {{ value?.deviceType }}
          </q-tooltip>
        </q-icon>
        <span>{{ value?.value }}</span>
      </template>
    </query-history>
  </modal>
</template>

<script lang="ts">
  import dialogMixin from '@utils/mixins/dialog'
  import historyMixin from '@utils/mixins/history'
  import { org } from '@ygen/org'
  import { GET_DATA, NS } from '@store/data/methodTypes'
  import { DataName } from '@src/store'
  import { bysdb } from '@ygen/bysdb'
  import { Controller, Unit, BysMarker } from '@services/dataStore'
  import { deferred, getObjAllAttrs, IControllerOptions, checkIs4GMarker } from '@utils/common'
  import { crud } from '@ygen/crud'
  import { defaultQueryFinished, QueryBatchV2 } from '@services/queryData'
  import { PrpcDbControllerOnlineHistory } from '@ygen/bysdb.rpc.yrpc'
  import { StrPubSub } from 'ypubsub'
  import { QueryControllerHistory } from '@utils/pubSubSubject'
  import { DbName } from '@utils/permission'
  import PermissionMixin from '@utils/mixins/permission'
  import { defineComponent } from 'vue'

  enum DeviceType {
    Controller,
    Net4GMarker,
  }
  let queryNormal = true
  let queryCancelHandlers: Array<() => void> = []
  let queryRet = []
  let deviceType: number = DeviceType.Controller

  export default defineComponent({
    name: 'ControllerOnlineRecords',
    mixins: [dialogMixin, PermissionMixin, historyMixin],
    data() {
      return {
        maxHeight: false,
        defaultTimeDiff: {
          value: 1,
          uint: 'month',
        },
        timeMaxRange: {
          value: 3,
          uint: 'month',
          text: this.$t('date.notMax3month'),
        },
        timeField: 'ActionTime',

        orgRID: '',
        controllerRID: '',
        modelValue: '',

        orgFilter: '',
        controllerFilter: '',
      }
    },
    computed: {
      dbName() {
        return DbName.DbControllerOnlineHistory
      },
      title() {
        return this.$t('menus.controllerOnlineRecords')
      },

      controllerData() {
        return this.$store.getters[`${NS}/${GET_DATA}`](DataName.Controller)
      },
      net4gMarkerData() {
        return BysMarker.getDataList().filter(item => checkIs4GMarker(item.MarkerType))
      },
      net4gMarkerRIDOptions() {
        let options: Array<IControllerOptions> = this.net4gMarkerData
          .filter(item => {
            if (this.orgRID) {
              return item.OrgRID === this.orgRID
            }
            return true
          })
          .map((data: bysdb.IDbBysMarker) => {
            return {
              label: data.MarkerNo,
              value: data.RID,
              ControllerHWID: data.MarkerHWID,
              OrgRID: data.OrgRID,
              orderKey: data.MarkerHWID,
              deviceType: DeviceType.Net4GMarker,
            }
          })

        options.sort((a: IControllerOptions, b: IControllerOptions) => a.orderKey - b.orderKey)

        if (this.controllerFilter) {
          return options.filter(option => {
            const needle = this.controllerFilter.toLowerCase()
            return option.label.toLowerCase().includes(needle)
          })
        }

        return options
      },
      controllerRIDOptions() {
        let options: Array<IControllerOptions> = this.controllerData
          .filter(item => {
            if (this.orgRID) {
              return item.OrgRID === this.orgRID
            }
            return true
          })
          .map((data: bysdb.IDbController) => {
            return {
              label: data.ControllerNo,
              value: data.RID,
              ControllerHWID: data.ControllerHWID,
              OrgRID: data.OrgRID,
              orderKey: data.ControllerHWID,
              deviceType: DeviceType.Controller,
            }
          })

        options.sort((a: IControllerOptions, b: IControllerOptions) => a.orderKey - b.orderKey)

        if (this.controllerFilter) {
          return options.filter(option => {
            const needle = this.controllerFilter.toLowerCase()
            return option.label.toLowerCase().includes(needle)
          })
        }

        return options
      },
      RIDOptions() {
        return [
          {
            label: this.$t('form.controller'),
            children: this.controllerRIDOptions
          },
          {
            label: this.$t('form.4GMarkers'),
            children: this.net4gMarkerRIDOptions
          },
        ]
      },
      actionOptions() {
        return [
          { label: this.$t('form.online'), value: 1 },
          { label: this.$t('form.offline'), value: 2 },
          { label: this.$t('CmdTest.heartbeat'), value: 11 },
          { label: this.$t('CmdTest.alarm'), value: 16 },
        ]
      },
      columns() {
        return [
          {
            name: 'OrgRID',
            field: 'OrgRID',
            label: this.$t('form.unit'),
            sortable: true,
            format: (val: string) => {
              const orgData = Unit.getData(val) as org.IDbOrg | undefined
              return orgData?.ShortName ?? val
            },
          },
          {
            name: 'Controller',
            field: 'ControllerHWID',
            label: this.$t('form.controllerID'),
            sortable: true,
            sticky: true,
            format: (val: number, row) => {
              if (row.DeviceType) {
                const marker: bysdb.IDbBysMarker | undefined = BysMarker.getDataByIndex(val + '')
                return {
                  icon: 'iconfont bys-jiezhuang',
                  value: marker?.MarkerNo ?? val,
                  customClass: 'table-cell-device-marker',
                  deviceType: this.$t('form.4GMarkers')
                }
              }
              const controller: bysdb.IDbController | undefined = Controller.getDataByIndex(val + '')
              return {
                icon: 'iconfont bys-base-station',
                value: controller?.ControllerNo ?? val,
                customClass: 'table-cell-device-controller',
                deviceType: this.$t('form.controller')
              }
            },
          },
          { name: 'ControllerHWID', field: 'ControllerHWID', label: this.$t('form.controllerHWID'), sortable: true, noLeftBorder: true },
          {
            name: 'ActionCode', field: 'ActionCode', label: this.$t('form.type'),
            format: (val: number) => {
              const opt = this.actionOptions.find(opt => opt.value === val)
              return opt?.label ?? val
            },
          },
          { name: 'ActionTime', field: 'ActionTime', label: this.$t('form.time'), sortable: true },
          { name: 'IpInfo', field: 'IpInfo', label: this.$t('form.ipInfo') },
        ]
      },
      filterColumns() {
        return ['OrgRID', 'Controller', 'ControllerHWID']
      },
    },
    methods: {
      /**
       * 导出表格数据的额外处理函数
       * @param {Object} source - 表格的一行源数据
       * @param {Object} _row - 完成部分处理的xlsx表格数据
       * @return {Object} 对源数据处理完后保存在_row并返回
       */
      extraExportFunc(source, _row) {
        // 对控制器编号添加4g界桩和控制器区分
        const { deviceType, value } = _row[this.$t('form.controllerID')]
        _row[this.$t('form.controllerID')] = value + `(${deviceType})`
        return _row
      },
      createWhereItems(): crud.IWhereItem[] {
        // 只查控制器, 不查4g界桩的上下线历史
        const where: crud.IWhereItem[] = [
          {
            Field: 'DeviceType',
            FieldValue: '' + DeviceType.Controller,
            FieldCompareOperator: '=',
          }
        ]
        if (this.orgRID) {
          // 添加单位查询
          const whereItem: crud.IWhereItem = {
            Field: 'OrgRID',
            FieldValue: this.orgRID,
            FieldCompareOperator: '=',
          }
          where.push(whereItem)
        }
        if (this.controllerRID) {
          const options = deviceType === DeviceType.Controller ? this.controllerRIDOptions : this.net4gMarkerRIDOptions
          const whereItem: crud.IWhereItem = {
            Field: 'ControllerHWID',
            FieldValue: '' + (options.find(item => item.value === this.controllerRID).ControllerHWID ?? ''),
            FieldCompareOperator: '=',
          }
          where.push(whereItem)
          // 查询条件 是控制器还是4g界桩
          // const whereItemDeviceType: crud.IWhereItem = {
          //   Field: 'DeviceType',
          //   FieldValue: '' + deviceType,
          //   FieldCompareOperator: '=',
          // }
          // where.push(whereItemDeviceType)
        }
        return where
      },
      createOrderByItems(): string[] {
        return [`${this.timeField} asc`]
      },
      createRetColumns(): string[] {
        const obj: Object = new bysdb.DbControllerOnlineHistory()
        return getObjAllAttrs(obj)
      },
      getTimeColumn(): string[] {
        return this.$refs['form-content'].createTimeColumns()
      },
      onSubmit(where: crud.IWhereItem[]) {
        const queryAlreadyCallBack = function() {
          queryNormal && defaultQueryFinished()
        }
        queryNormal = false
        this.maxHeight = true
        queryRet = []
        const orderByItems = this.createOrderByItems()
        const timeColumns = this.getTimeColumn()
        const retColumns = this.createRetColumns()
        const defaultWhereItems = this.createWhereItems().concat(where)
        const submitFunc = (whereItem?: crud.IWhereItem): Promise<boolean> => {
          const promiseDeferred = deferred<boolean>()
          const cancel = QueryBatchV2(
            PrpcDbControllerOnlineHistory.QueryBatch,
            {
              Where: whereItem ? defaultWhereItems.concat([whereItem]) : defaultWhereItems,
              OrderBy: orderByItems,
              ResultColumn: retColumns,
              TimeColumn: timeColumns,
            },
            promiseDeferred,
            {
              OnResult: (data) => {
                window.requestIdleCallback(() => {
                  StrPubSub.publish(QueryControllerHistory, data.Rows)
                })
              },
            }
          )
          queryCancelHandlers.push(cancel)
          return promiseDeferred
        }
        let execFns = [submitFunc]
        const promiseAllSets = execFns.map(func => func())
        Promise.all(promiseAllSets)
          .then(queryPromiseRes => {
            queryNormal = queryPromiseRes.find(bool => bool !== true) ?? true
          })
          .finally(() => {
            queryAlreadyCallBack()
          })
      },
      queryDataRet(datas: []) {
        queryRet = queryRet.concat(Object.freeze(datas))
        this.$refs['form-content'].queryRet = queryRet
      },
      queryCanceled() {
        for (let i = queryCancelHandlers.length - 1; i >= 0; i--) {
          queryCancelHandlers[i]()
        }
        queryCancelHandlers = []
        queryNormal = false
        this.maxHeight = false
      },

      filterOrgRID(val: string, update: Function) {
        this.orgFilter = val
        update()
      },
      orgRIDChange() {
        this.controllerRID = ''
      },

      filterControllerRID(val: string, update: Function) {
        this.controllerFilter = val
        update()
      },
      controllerRIDChange(child) {
        this.controllerRID = child?.value
        this.modelValue = child?.label
        deviceType = child?.deviceType
      },
      clearControllerRID() {
        this.controllerRID = ''
        this.modelValue = ''
      },
    },
    beforeMount() {
      StrPubSub.subscribe(QueryControllerHistory, this.queryDataRet)
    },
    beforeUnmount() {
      StrPubSub.unsubscribe(QueryControllerHistory, this.queryDataRet)
    },
  })
</script>

<style lang="scss">
  .controller-history-modal {
    .history-panel {
      height: 100%;

      .history-table {
        &.q-table--dense .q-table {

          th,
          td {
            padding: 2px 4px;
          }
        }

        .sticky-column-last {
          position: sticky !important;
          right: 0;
          background: #fffff3;
        }
      }
    }

    .q-tab-panel {
      padding: unset
    }

    .query-form {

      // 表单有验证条件的下边距为20px，该样式主要同步没有验证条件的表单项样式
      &>*:not(:last-child) {
        padding-bottom: 20px;
      }
    }

    .table-cell-device-marker {
      margin: 0 6px;
      color: $blue-4;
    }

    .table-cell-device-controller {
      margin: 0 6px;
      color: $green-4;
    }
  }

  .controller-history-modal:not(.maximized) {
    width: auto !important;
    max-width: unset !important;

    .history-panel .history-table {
      width: 60vw !important;
    }

    .query-form {
      min-width: 400px;
    }
  }

  .popup-proxy-details {
    max-width: 60vw;
  }
</style>

<template>
  <modal
    v-model="visible"
    v-model:maximized="maximized"
    :supportMaximize="false"
    :dblClick="false"
    contentHeight="auto"
    contentClass="controller-server-address-modal"
    ref="controllerServerAddress"
  >
    <template #header>
      <span>{{ $t('menus.modifyServerAddr') }}</span>
    </template>
    <div class="controller-server-address-content">
      <q-form
        class=""
        @submit='confirmModifyServerAddress'
      >
        <q-select
          v-model="formData.controller"
          :label="$t('controller.target')"
          outlined
          dense
          :options="onlineControllers"
          @filter="filterController"
          option-label='ControllerNo'
          option-value='ControllerHWID'
          options-dense
          map-options
          emit-value
          use-input
          lazy-rules
          :rules="rules.controller"
        />

        <q-input
          v-model="formData.ip"
          outlined
          dense
          :label="$t('controller.serverAddress')"
          lazy-rules
          :rules="rules.ip"
        />

        <q-input
          v-model.number="formData.port"
          type="number"
          outlined
          dense
          :label="$t('controller.port')"
          lazy-rules
          :rules="rules.port"
        />

        <div class='full-width row justify-evenly'>
          <q-btn
            :label="$t('common.confirm')"
            color="primary"
            class='q-px-md'
            type='submit'
          />
        </div>
      </q-form>

      <!--      <q-list bordered padding>-->
      <!--        <q-item-label header>General</q-item-label>-->

      <!--        <q-virtual-scroll-->
      <!--          style="max-height: 300px;"-->
      <!--          :items="controllers"-->
      <!--          separator-->
      <!--        >-->
      <!--          <template v-slot="{ item, index }">-->
      <!--            <q-item tag="label"-->
      <!--                    :key="index"-->
      <!--                    dense v-ripple>-->
      <!--              <q-item-section side top>-->
      <!--                <q-checkbox v-model="selection" :val="item.ControllerHWID"/>-->
      <!--              </q-item-section>-->
      <!--              <q-item-section>-->
      <!--                <q-item-label>{{ item.ControllerNo }}</q-item-label>-->
      <!--              </q-item-section>-->
      <!--            </q-item>-->
      <!--          </template>-->
      <!--        </q-virtual-scroll>-->
      <!--      </q-list>-->
    </div>
  </modal>
</template>

<script lang="ts">
  import dialogMixin from '@utils/mixins/dialog'
  import { GET_DATA, NS } from '@store/data/methodTypes'
  import { DataName } from '@src/store'
  import { Controller } from '@services/dataStore'
  import { bysdb } from '@ygen/bysdb'
  import { genRangeRule, isDomain, isIPValid, required } from '@utils/validation'
  import { Dialog } from 'quasar'
  import { isLonginController, modifyControllerServerAddr } from '@services/controller'
  import { doc } from '@ygen/bys.api'
  import { StrPubSub } from 'ypubsub'
  import { ControllerOffline } from '@utils/pubSubSubject'
  import { ControllerAndUpdateCmd } from '@utils/bysdb.type'
  import { bysproto } from '@ygen/controller'
  import { ControllerDeviceType } from '@utils/common'
import { defineComponent } from 'vue'

  export default defineComponent({
    name: 'ControllerServerAddress',
    mixins: [dialogMixin],
    data() {
      return {
        visible: true,
        formData: {
          controller: 0,
          ip: '',
          port: 2233,
        },
        controllerFilter: '',
      }
    },
    methods: {
      confirmModifyServerAddress() {
        Dialog.create({
          title: this.$t('controller.warning') as string,
          message: this.$t('controller.warningCaption') as string,
          class: 'z-top modify-controller-server-address-warning',
          ok: {
            push: true,
            color: 'negative',
          },
          cancel: {
            push: true,
            color: 'secondary',
          },
          persistent: true,
        }).onOk(() => {
          // 发送指令到服务器
          const targets: doc.IControllerTarget[] = [
            { StationID: this.formData.controller, ControllerChannelNo: 0 },
          ]
          const addr: doc.IControllerNewServerAddr = {
            Target: targets,
            Ip: this.formData.ip,
            Port: this.formData.port,
          }
          modifyControllerServerAddr(addr)
        })
      },
      filterController(val: string, update: Function) {
        this.controllerFilter = val
        update()
      },
      listenOfflineController(data: bysdb.IDbController, loginReq: bysproto.IBloginReq) {
        // 4g界桩，不处理
        if (loginReq.DeviceType === ControllerDeviceType.Net4g) return

        if (this.formData.controller === data.ControllerHWID) {
          this.formData.controller = this.onlineControllers[0]?.ControllerHWID ?? 0
        }
      },
    },
    computed: {
      controllers(): ControllerAndUpdateCmd[] {
        const data = this.$store.getters[`${NS}/${GET_DATA}`](DataName.Controller)
        return Controller.sortBy(data, { sortBy: 'ControllerHWID' })
      },
      onlineControllers() {
        return this.controllers
          .filter(controller => {
            const isOnline = controller.controllerState?.online ?? false
            return isOnline && isLonginController(controller.controllerState?.NetworkType as number)
          })
          .filter(controller => {
            const needle = this.controllerFilter.toLowerCase()
            return controller.ControllerNo.toLowerCase().includes(needle)
          })
      },
      rules() {
        const _required = (val) => required(val) || this.$t('rules.required')
        const portRangeRule = genRangeRule(0, 0xFFFF)

        return {
          controller: [
            val => _required(val),
          ],
          ip: [
            val => _required(val),
            val => (isIPValid(val) || isDomain(val)) || this.$t('rules.mustBeIpOrDomain'),
          ],
          port: [
            val => _required(val),
            val => portRangeRule(val) || this.$t('rules.availableRange', { min: 0, max: 0xFFFF }),
          ],
        }
      },
    },
    beforeMount() {
      StrPubSub.subscribe(ControllerOffline, this.listenOfflineController)
    },
    beforeUnmount() {
      StrPubSub.unsubscribe(ControllerOffline, this.listenOfflineController)
    },
  })
</script>

<style lang="scss">
  @import "quasar/src/css/variables";

  .q-dialog-plugin.modify-controller-server-address-warning {
    .q-dialog__message {
      font-style: italic;
      color: $red-6;
    }
  }
</style>

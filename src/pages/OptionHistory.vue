<template>
  <modal
    v-model="visible"
    v-model:maximized="maximized"
    :contentHeight="maxHeight ? '60vh' : 'auto'"
    contentClass="op-history-modal"
    ref="opHistory"
    @hide="maxHeight = false"
  >
    <template #header>
      <span v-text="title"></span>
    </template>
    <query-history
      ref="form-content"
      :default-time-diff="defaultTimeDiff"
      :time-max-range="timeMaxRange"
      :export-name="title"
      :table-columns="columns"
      :time-limit-sql-name="timeField"
      :can-query="canQuery"
      @submit-clicked="onSubmit"
      @query-canceled="queryCanceled"
    >
      <template #form>
        <div class="query-form">
          <q-select
            v-model="orgRID"
            :label="$t('form.parentUnit')"
            outlined
            dense
            clearable
            lazy-rules
            :rules="[]"
            :options="orgRIDOptions"
            @filter="filterOrgRID"
            options-dense
            map-options
            emit-value
            @update:model-value="orgRIDChange"
          />
          <q-select
            v-model="userRID"
            :label="$t('menus.user')"
            outlined
            dense
            clearable
            lazy-rules
            :rules="[]"
            :options="userRIDOptions"
            @filter="filterUserRID"
            options-dense
            map-options
            emit-value
            use-input
          />
        </div>
      </template>

      <template v-slot:my-body-cell-Details="{ row }">
        <q-btn
          icon="iconfont bys-details"
          color="light"
          size="sm"
          flat
          round
          dense
        >
          <q-tooltip>
            {{ details }}
          </q-tooltip>
          <q-popup-proxy>
            <q-banner>
              <div class="popup-proxy-details">
                <p class='text-h6 q-mb-sm'>{{ row.Operation }}</p>
                <div
                  class='popup-proxy-details-content'
                  v-html="praseJson2Table(row)"
                >
                </div>
              </div>
            </q-banner>
          </q-popup-proxy>
        </q-btn>
      </template>
    </query-history>
  </modal>
</template>

<script lang="ts">
  import dialogMixin from '@utils/mixins/dialog'
  import historyMixin from '@utils/mixins/history'
  import { GET_DATA, NS } from '@store/data/methodTypes'
  import { DataName } from '@src/store'
  import { DefUuid } from '@src/config'
  import { org } from '@ygen/org'
  import { user } from '@ygen/user'
  import { defaultQueryFinished, QueryBatchV2 } from '@services/queryData'
  import { crudlog } from '@ygen/crudlog'
  import log from 'loglevel'
  import { bysdb } from '@ygen/bysdb'
  import { i18n } from '@boot/i18n'
  import { user as userOrgP } from '@ygen/userOrgPrivilege'
  import { BysMarker, Controller } from '@services/dataStore'
  import { user as userPerm } from '@ygen/userPermission'
  import { deferred, getObjAllAttrs, getOrgByRID, getRoleByRID, getUserByRID } from '@utils/common'

  import { crud } from '@ygen/crud'
  import { PrpcDbCrudLog } from '@ygen/crudlog.rpc.yrpc'
  import { StrPubSub } from 'ypubsub'
  import { QueryOptionHistory } from '@utils/pubSubSubject'
  import PermissionMixin from '@utils/mixins/permission'
  import { TranslateResult } from 'vue-i18n'
  import { doc } from '@ygen/bys.api'
  // import TranslateResult = VueI18n.TranslateResult
  import { defineComponent } from 'vue'
  import { config } from '@ygen/config'

  const mask = 'YYYY-MM-DD HH:mm'

  const prase = (content: string) => {
    try {
      return {
        err: false,
        data: JSON.parse(content),
      }
    } catch (err) {
      log.error('json prase err')
    }
    return {
      err: true,
      data: content,
    }
  }

  interface OptItem {
    [key: string]: any

    data: { [key: string]: any }
    getDetail: (content: string, operation?: string) => string
    label: string | TranslateResult
  }

  interface Opt {
    [key: string]: OptItem
  }

  const dbConfigConfkeyLabels = {
    appLogo: () => i18n.global.t('settingsPage.sysLogo'),
    systemTitle: () => i18n.global.t('settingsPage.sysTitle'),
  }

  const opt: Opt = {
    DbController: {
      data: {},
      getDetail: function(content: string, operation?: string) {
        if (!this.data[content]) {
          const ret = prase(content)
          if (!ret.err) {
            this.data[content] = new bysdb.DbController(ret.data)
            if (operation === 'UpdateIp') {
              return this.getUpdateIpLabel(content)
            }
            return ` "${this.data[content].ControllerNo ?? '?'}"`
          }
        } else {
          if (operation === 'UpdateIp') {
            return this.getUpdateIpLabel(content)
          }
          return ` "${this.data[content].ControllerNo ?? '?'}"`
        }
        return content
      },
      label: i18n.global.t('PermTab.DbController'),
      getUpdateIpLabel: function(content: string) {
        const d = this.data[content] as doc.IControllerNewServerAddr
        const targets: doc.IControllerTarget[] = d.Target ?? []
        const label = targets.map(target => {
          return Controller.getDataByIndex(target.StationID + '')?.ControllerNo ?? ''
        }).join(',')
        return ` "${label}" ${i18n.global.t('controller.serverAddress')}`
      },
    },

    DbBysMarker: {
      data: {},
      getDetail: function(content: string) {
        if (!this.data[content]) {
          const ret = prase(content)
          if (!ret.err) {
            this.data[content] = new bysdb.DbBysMarker(ret.data)
            return ` "${this.data[content].MarkerNo ?? '?'}"`
          }
        } else {
          return ` "${this.data[content].MarkerNo ?? '?'}"`
        }
        return content
      },
      label: i18n.global.t('PermTab.DbBysMarker'),
    },

    DbMediaInfo: {
      //创建/删除/修改 图影像
      data: {},
      getDetail: function(content: string) {
        if (!this.data[content]) {
          const ret = prase(content)
          if (!ret.err) {
            this.data[content] = new bysdb.DbMediaInfo(ret.data)
            this.data[content].resultStr = ''
            if (this.data[content].MarkerRID) {
              this.data[content].resultStr = ` '${i18n.global.t('form.MarkerNo')}:${(BysMarker.getData(
                this.data[content].MarkerRID) as bysdb.DbBysMarker)?.MarkerNo ?? '?'} ${this.data.MediaDescription ??
                ''}'`
            } else {
              this.data[content].resultStr = ` '${i18n.global.t('form.ControllerNo')}:${(Controller.getData(
                this.data[content].ControllerRID) as bysdb.DbController)?.ControllerNo ??
                '?'} ${this.data[content].MediaDescription ??
                ''}'`
            }
            return this.data[content].resultStr
          }
        } else {
          return this.data[content].resultStr
        }
        return content
      },
      label: i18n.global.t('PermTab.DbMediaInfo'),
    },

    DbOrg: {
      //创建/删除/修改 单位
      data: {},
      getDetail: function(content: string) {
        if (!this.data[content]) {
          const ret = prase(content)
          if (!ret.err) {
            this.data[content] = new org.DbOrg(ret.data)
            return ` "${this.data[content].ShortName ?? '?'}"`
          }
        } else {
          return ` "${this.data[content].ShortName ?? '?'}"`
        }
        return content
      },
      label: i18n.global.t('PermTab.DbOrg'),
    },

    DbUser: {
      //创建/删除/修改 用户
      data: {},
      getDetail: function(content: string) {
        if (!this.data[content]) {
          const ret = prase(content)
          if (!ret.err) {
            this.data[content] = new user.DbUser(ret.data)
            return ` "${this.data[content].UserName ?? '?'}"`
          }
        } else {
          return ` "${this.data[content].UserName ?? '?'}"`
        }
        return content
      },
      label: i18n.global.t('PermTab.DbUser'),
    },

    DbRole: {
      //... 角色
      data: {},
      getDetail: function(content: string) {
        if (!this.data[content]) {
          const ret = prase(content)
          if (!ret.err) {
            this.data[content] = new userPerm.DbRole(ret.data)
            return ` "${this.data[content].RoleName ?? '?'}"`
          }
        } else {
          return ` "${this.data[content].RoleName ?? '?'}"`
        }
        return content
      },
      label: i18n.global.t('PermTab.DbRole'),
    },

    DbUserRole: {
      //... 用户角色
      data: {},
      getDetail: function(content: string) {
        if (!this.data[content]) {
          const ret = prase(content)
          if (!ret.err) {
            this.data[content] = new user.DbUserRole(ret.data)
            return ` '${i18n.global.t('PermTab.DbUser')}: ${(getUserByRID(
              this.data[content].UserRID as string) as user.DbUser)?.UserName ?? '?'}'`
          }
        } else {
          return ` '${i18n.global.t('PermTab.DbUser')}: ${(getUserByRID(
            this.data[content].UserRID as string) as user.DbUser)?.UserName ?? '?'}'`
        }
        return content
      },
      label: i18n.global.t('PermTab.DbUserRole'),
    },

    DbUserOrgPrivilege: {
      //用户-单位
      data: {},
      getDetail: function(content: string) {
        if (!this.data[content]) {
          const ret = prase(content)
          if (!ret.err) {
            this.data[content] = new userOrgP.DbUserOrgPrivilege(ret.data)
            const torg: org.DbOrg = getOrgByRID(this.data[content].OrgRID) as org.DbOrg
            const tuser: user.DbUser = getUserByRID(this.data[content].UserRID) as user.DbUser
            return this.data[content].tempRet = ` "${torg?.ShortName ?? '?'}-${tuser?.UserName ?? '?'}"`
          }
        } else {
          return this.data[content].tempRet
        }
        return content
      },
      label: i18n.global.t('PermTab.DbUserOrgPrivilege'),
    },

    DbRolePermission: {
      //给角色增加/删除权限
      data: {},
      getDetail: function(content: string) {
        if (!this.data[content]) {
          const ret = prase(content)
          if (!ret.err) {
            this.data[content] = new userOrgP.DbUserOrgPrivilege(ret.data)
            //getRoleByRID
            this.data[content].role = getRoleByRID(this.data[content].RoleRID) as userPerm.DbRole
            return ` "${i18n.global.t('PermTab.DbRole')}${this.data[content].role?.RoleName ?? '?'}"`
          }
        } else {
          return ` "${i18n.global.t('PermTab.DbRole')}${this.data[content].role?.RoleName ?? '?'}"`
        }
        return content
      },
      label: i18n.global.t('PermTab.DbRolePermission'),
    },

    DbConfig: {
      data: {},
      label: i18n.global.t('PermTab.DbConfig'),
      getDetail: function(content: string) {
        let data = this.data[content]
        if (!data) {
          const ret = prase(content)
          if (ret.err) {
            return content
          } else {
            this.data[content] = data = new config.DbConfig(ret.data)
          }
        }

        const label = dbConfigConfkeyLabels[data.ConfKey]?.()
        return ` "${label ?? '?'}"`
      },
    },
  }

  const operation = {
    Insert: i18n.global.t('PermTab.Insert'),
    Delete: i18n.global.t('PermTab.Delete'),
    Update: i18n.global.t('PermTab.Update'),
    UpdateIp: i18n.global.t('PermTab.Update'),
    PartialUpdate: i18n.global.t('PermTab.Update'),
  }

  let queryCancelHandlers: Array<() => void> = []
  let queryNormal = true

  export default defineComponent({
    name: 'OptionHistory',
    mixins: [dialogMixin, PermissionMixin, historyMixin],
    props: {
      // 格式化时间参数
      mask: {
        type: String,
        default: mask,
      },
    },
    data() {
      return {
        defaultTimeDiff: {
          value: 1,
          uint: 'month',
        },
        timeMaxRange: {
          value: 3,
          uint: 'year',
          text: this.$t('date.notMax3year'),
        },

        timeField: 'UpdatedAt',
        orgRID: '',
        userRID: '',
        orgFilter: '',
        userFilter: '',
        queryRet: [],
        maxHeight: false,
        rowIndex: 0,
      }
    },
    computed: {
      dbName() {
        return 'DbCrudLog'
      },
      title() {
        return this.$t('menus.optionHistory')
      },
      userData() {
        return this.$store.getters[`${NS}/${GET_DATA}`](DataName.User)
      },
      userRIDOptions() {
        let users = this.userData
        if (this.orgRID) {
          users = users.filter(item => item.OrgRID === this.orgRID)
        }
        const options = users
          .map((data: user.DbUser) => {
            return {
              label: data.LoginName,
              value: data.RID,
            }
          })
        if (!this.userFilter) { return options }

        return options.filter(option => {
          const needle = this.userFilter.toLowerCase()
          return option.label.toLowerCase().includes(needle)
        })
      },
      columns() {
        return [
          { name: this.timeField, field: this.timeField, label: this.$t('form.time'), align: 'left', sortable: true },
          {
            name: 'UserRID',
            field: 'UserRID',
            label: this.$t('menus.user'),
            align: 'left',
            sortable: true,
            sticky: true,
            format: (val: string) => {
              return this.userData.find(item => item.RID === val)?.LoginName ?? val
            },
          },
          {
            name: 'Operation', field: 'Operation', label: this.$t('form.Operation'), align: 'left', sortable: true, noLeftBorder: true,
            format: (val: string, row: crudlog.IDbCrudLog) => {
              const [type, op] = row.Operation?.split('.', 2) ?? []
              let ret = operation[op] + (opt[type]?.label ?? '')
              ret += opt[type]?.getDetail(row.Req + '', op)
              return ret ?? ''
            },
          },
          {
            name: 'OrgRID',
            field: 'OrgRID',
            label: this.$t('form.unit'),
            align: 'left',
            sortable: true,
            format: (val: string) => {
              if (val === DefUuid) { return this.$t('builtInAttr.root') }
              return this.orgData.find(item => item.RID === val)?.ShortName ?? val
            },
          },
          { name: 'IpInfo', field: 'Ipinfo', label: this.$t('form.ipInfo'), align: 'left' },
          { name: 'Details', field: 'Details', label: '#' },
        ]
      },
      exportName() {
        return this.title
      },
      details() {
        return this.$t('common.details')
      },
    },
    methods: {
      onRowClick(props) {
        //修改样式(被选中)
        this.rowIndex = props.rowIndex
      },
      createWhereItems(): crud.IWhereItem[] {
        const where: crud.IWhereItem[] = []
        if (this.userRID) {
          // 添加单位查询
          const whereItem: crud.IWhereItem = {
            Field: 'UserRID',
            FieldValue: this.userRID,
            FieldCompareOperator: '=',
          }
          where.push(whereItem)
        }
        if (this.orgRID) {
          // 添加单位查询
          const whereItem: crud.IWhereItem = {
            Field: 'OrgRID',
            FieldValue: this.orgRID,
            FieldCompareOperator: '=',
          }
          where.push(whereItem)
        }
        return where
      },
      createOrderByItems(): string[] {
        return [`${this.timeField} asc`]
      },
      createRetColumns(): string[] {
        const obj: Object = new crudlog.DbCrudLog()
        return getObjAllAttrs(obj)
      },
      getTimeColumn(): string[] {
        return this.$refs['form-content'].createTimeColumns()
      },
      onSubmit(where: crud.IWhereItem[]) {
        /*//组合自己的专属条件
        this.createWhereItems().push(where)
        //按照要求对结果排序
        this.createOrderByItems()
        //需要转换时差的列
        this.getTimeColumn()
        //设定某个结果列
        this.createRetColumns()*/
        //设定查询结束cb func
        const queryAlreadyCallBack = function() {
          queryNormal && defaultQueryFinished()
        }
        this.queryRet = []
        this.maxHeight = true
        queryNormal = false
        const orderByItems = this.createOrderByItems()
        const timeColumns = this.getTimeColumn()
        const retColumns = this.createRetColumns()
        const defaultWhereItems = this.createWhereItems().concat(where)
        const submitFunc = (whereItem?: crud.IWhereItem): Promise<boolean> => {
          const promiseDeferred = deferred<boolean>()
          const cancel = QueryBatchV2(
            PrpcDbCrudLog.QueryBatch,
            {
              Where: whereItem ? defaultWhereItems.concat([whereItem]) : defaultWhereItems,
              OrderBy: orderByItems,
              ResultColumn: retColumns,
              TimeColumn: timeColumns,
            },
            promiseDeferred,
            {
              OnResult: (data) => {
                window.requestIdleCallback(() => {
                  StrPubSub.publish(QueryOptionHistory, data.Rows)
                })
              },
            }
          )
          queryCancelHandlers.push(cancel)
          return promiseDeferred
        }
        let execFns = [submitFunc]
        const promiseAllSets = execFns.map(func => func())
        Promise.all(promiseAllSets)
          .then(queryPromiseRes => {
            queryNormal = queryPromiseRes.find(bool => bool !== true) ?? true
          })
          .finally(() => {
            queryAlreadyCallBack()
          })
      },
      queryDataRet(datas: []) {
        this.queryRet = this.queryRet.concat(Object.freeze(datas))
        this.$refs['form-content'].queryRet = this.queryRet
      },
      queryCanceled() {
        for (let i = queryCancelHandlers.length - 1; i >= 0; i--) {
          queryCancelHandlers[i]()
        }
        queryCancelHandlers = []
        queryNormal = false
        this.maxHeight = false
      },

      filterOrgRID(val: string, update: Function) {
        this.orgFilter = val
        update()
      },
      filterUserRID(val: string, update: Function) {
        this.userFilter = val
        update()
      },
      orgRIDChange() {
        this.userRID = ''
      },
      praseJson2Table(row: Record<string, any>) {
        let ret = ''
        const obj = prase(row.Req).data
        // 部分更新api
        if (row.ReqOption) {
          const keyColumn: string[] = prase(row.ReqOption).data.KeyColumn
          const reqOption = keyColumn.join(',')
          ret += `<p class='details-item q-my-xs'>${this.$t('menus.currentUpdate')}: ${reqOption}</p>`
          keyColumn.forEach(key => {
            ret += `<p class='details-item q-my-xs'>${key}: ${obj[key]}</p>`
          })
          return ret
        }
        // 全部更新api
        Object.keys(obj).forEach(function(key) {
          let v = obj[key]
          ret += `<p class='details-item q-my-xs'>${key}: ${v}</p>`
        })
        return ret
      },
    },
    beforeMount() {
      StrPubSub.subscribe(QueryOptionHistory, this.queryDataRet)
    },
    beforeUnmount() {
      StrPubSub.subscribe(QueryOptionHistory, this.queryDataRet)
    },
  })
</script>

<style lang="scss">
  $stickyBgColor: #fff;

  .op-history-modal {
    .q-table__container {
      .q-table__top {
        flex: none;
      }
    }
  }

  .op-history-modal .query-form {

    // 表单有验证条件的下边距为20px，该样式主要同步没有验证条件的表单项样式
    &>*:not(:last-child) {
      padding-bottom: 20px;
    }
  }

  .op-history-modal:not(.maximized) {
    width: auto !important;
    max-width: unset !important;

    .history-panel .history-table {
      width: 60vw !important;
    }

    .query-form {
      min-width: 400px;
    }
  }

  .history-panel.q-tab-panels.q-panel-parent {
    .q-table--dense .q-table {
      thead tr th {
        position: sticky;
        z-index: 1;
        background-color: #ffffff
      }

      thead tr:first-child th {
        top: 0;
      }
    }
  }

  .popup-proxy-details {
    max-width: 60vw;
  }
</style>

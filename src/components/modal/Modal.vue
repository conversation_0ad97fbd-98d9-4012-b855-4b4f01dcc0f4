<template>
  <q-dialog
    v-model="_visible"
    persistent
    :maximized="_maximized"
    transition-show="slide-up"
    transition-hide="slide-down"
    :class="contentClasses"
    ref="wrapper"
    :seamless="isSeamless"
    @hide="onHide"
    @show="onShow"
  >
    <q-card
      class="bys-move-layout"
      ref="container"
      :class="[contentClass, { 'maximized': _maximized }]"
      :style="containerStyles"
      @click="showToTop"
    >
      <q-card-section class="q-pa-none q-ma-none bys-move-header-wrapper">
        <q-bar
          class="bys-move-header bg-primary text-white"
          ref="header"
          v-touch-pan.prevent.mouse="onPan"
          @dblclick="dblClickMaximize"
        >
          <q-toolbar-title>
            <template v-if="$slots['header']">
              <slot name="header"></slot>
            </template>
            <template v-else>
              <span class="modal-header-title">
                {{ title }}
              </span>
            </template>
          </q-toolbar-title>

          <template v-if="$slots['header-actions']">
            <slot name="header-actions"></slot>
          </template>

          <template v-if="supportMaximize">
            <q-btn
              dense
              flat
              icon="fullscreen_exit"
              @click="_maximized = false"
              v-if="_maximized && !isXs"
            />
            <q-btn
              dense
              flat
              icon="fullscreen"
              @click="_maximized = true"
              v-if="!_maximized && !isXs"
            />
          </template>

          <q-btn
            flat
            v-close-popup
            round
            dense
            icon="close"
          />
        </q-bar>

        <template v-if="$slots['header-bottom']">
          <slot name="header-bottom"></slot>
        </template>
      </q-card-section>

      <q-card-section
        class="bys-move-page-container full-width"
        ref="content"
        :style="contentStyles"
      >
        <slot></slot>
      </q-card-section>
    </q-card>
  </q-dialog>
</template>

<script lang="ts">
  import { QBar, TouchPanValue } from 'quasar'
  import { defineComponent, ref, getCurrentInstance, computed, ComponentPublicInstance, ComponentInternalInstance } from 'vue'

  interface ModalComputed {
    _maximized: boolean
  }

  export default defineComponent({
    name: 'Modal',
    model: {
      prop: 'visible',
      event: 'visible',
    },
    props: {
      visible: {
        type: Boolean,
        default: false,
      },
      title: {
        type: String,
        default: '',
      },
      supportMaximize: {
        type: Boolean,
        default: true,
      },
      maximized: {
        type: Boolean,
        default: false,
      },
      dblClick: {
        type: Boolean,
        default: true,
      },
      contentHeight: {
        type: String,
        default: 'auto',
      },
      contentClass: {
        type: String,
        default: '',
      },
      maxHeight: {
        type: String,
        default: '68vh',
      },
      minHeight: {
        type: String,
        default: '200px',
      },
      modalClass: {
        type: String,
        default: '',
      },
      isSeamless: {
        type: Boolean,
        default: true,
      },
    },
    data() {
      return {
        // 对话框当前坐标
        translateOffset: {
          x: 0,
          y: 0,
        },
      }
    },
    setup(props){
      const instance = getCurrentInstance() as ComponentInternalInstance
      const proxy = instance.proxy as ComponentPublicInstance<ModalComputed>
      const header = ref<InstanceType<typeof QBar>>()

      const contentStyles= computed(()=>{
        let headerOffsetHeight = 32
        if (header.value) {
          headerOffsetHeight = header.value.$el.parentNode.offsetHeight
        }
        const styles: { [key: string]: string } = {
          maxHeight: proxy._maximized ? 'unset' : props.maxHeight,
          minHeight: props.minHeight,
        }
        if (props.contentHeight) {
          styles.height = proxy._maximized ? `calc(100% - ${headerOffsetHeight}px)` : props.contentHeight
        }

        return styles
      })

      return { header, contentStyles }
    },
    computed: {
      contentClasses() {
        const cls = ['bys-move', ...this.modalClass.split(' ')]
        if (this.$q.platform.is.mobile) {
          cls.push('is-mobile')
        }
        return cls
      },
      _visible: {
        get() {
          return this.visible
        },
        set(val) {
          this.$emit('visible', val)
        },
      },
      _maximized: {
        get() {
          return this.maximized
        },
        set(val) {
          this.$emit('update:maximized', val)
        },
      },
      containerStyles() {
        const { x, y } = this.translateOffset
        return {
          transform: this.maximized ? 'unset' : `translate(${x}px, ${y}px)`,
        }
      },
      isXs() {
        return this.$q.screen.xs
      },
      wrapperRef() {
        return this.$refs.wrapper as any
      },
      headerRef() {
        return this.$refs.header as any
      },
    },
    watch: {
      // 移动端
      '$q.screen.xs': {
        immediate: true,
        handler(val) {
          if (val) {
            this._maximized = val
          }
        },
      },
    },
    methods: {
      onPan(details: Parameters<NonNullable<TouchPanValue>>[0]) {
        const { delta, position, isFirst } = details
        const { x = 0, y = 0 } = delta ?? {}
        const { top = 0, left = 0 } = position ?? {}

        if (isFirst) {
          this.showToTop()
        }

        // top
        if (top <= 0) {
          return
        }
        // bottom
        if (top >= window.innerHeight) {
          return
        }
        // left
        if (left <= 0) {
          return
        }
        // right
        if (left >= window.innerWidth) {
          return
        }

        this.translateOffset = {
          x: this.translateOffset.x + x,
          y: this.translateOffset.y + y,
        }
      },
      dblClickMaximize() {
        // 如果是移动端，则没有最大、最小化
        if (this.isXs || !this.dblClick) {
          return
        }
        this._maximized = !this._maximized
      },
      onShow() {
        this.$emit('show')
        this.showToTop()
      },
      onHide() {
        this.$emit('hide')
      },
      // 调整所有对话框的层叠顺序，将当前的对话框显示最前面
      showToTop() {
        if (!this.wrapperRef) {
          return
        }
        let dialogs: any = document.body.querySelectorAll('.q-dialog.bys-move')
        dialogs = Array.prototype.slice.call(dialogs)
        if (dialogs.length <= 1) { return }
        for (let i = 0; i < dialogs.length; i++) {
          dialogs[i].classList.remove('z-top')
        }
        let elNode = this.wrapperRef.contentEl.parentNode
        while (elNode) {
          if (elNode.id.startsWith('q-portal--')) {
            break
          }
          elNode = elNode.parentNode
        }

        if (!elNode|| document.body.lastChild === elNode) return
        elNode.children[0].classList.add('z-top')
      },
    },
  })
</script>

<style lang="scss">
  @import "quasar/src/css/core/visibility.sass";

  div[id ^="q-portal--dialog"]>.q-dialog:not(.bys-move),
  div[id ^="q-portal--menu"]>.q-menu {
    @apply z-top;
  }

  .bys-move-page-container {
    overflow-y: auto;
  }
</style>

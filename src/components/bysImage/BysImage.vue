<template>
  <q-img
    :src="imgUrl"
    :ratio="4 / 3"
    fit="contain"
    class="bys-image"
    spinner-color="primary"
  >
    <div class="absolute-top flex !px-2 !py-1 text-xs">
      <div class="flex-auto">
        <div>{{ $t('form.uploadUser') }}： {{ nodes.Author }}</div>
        <div v-if="nodes.UploadTime">{{ $t('form.uploadTime') }}： {{ nodes.UploadTime }}</div>
        <div v-if="nodes.LastUploadUser">{{ $t('form.lastUploadUser') }}： {{ nodes.LastUploadUser }}</div>
        <div v-if="nodes.LastUploadTime">{{ $t('form.lastUploadTime') }}： {{ nodes.LastUploadTime }}</div>
        <div v-if="nodes.Description">{{ $t('form.description') }}： {{ nodes.Description }}</div>
      </div>

      <q-btn
        round
        flat
        size="md"
        color="white"
        icon="aspect_ratio"
        @click="showLightbox"
      />
    </div>
  </q-img>

  <teleport to="body">
    <lightbox
      :visible="isFullScreen"
      :imgs="imgUrl"
      class="tlightbox"
      @hide="handleHide"
    />
  </teleport>
</template>

<script lang="ts">
  import { defineComponent } from 'vue'
  import Lightbox from 'vue-easy-lightbox'

  export default defineComponent({
    name: 'BysImage',
    props: {
      imgUrl: {
        type: String,
        required: true,
      },
      nodes: {
        type: Object,
        default() {
          return {}
        },
      },
    },
    data() {
      return {
        isFullScreen: false,
      }
    },
    methods: {
      showLightbox() {
        this.isFullScreen = true
      },
      handleHide() {
        this.isFullScreen = false
      },
    },
    components: {
      Lightbox,
    },
  })
</script>

<style lang="scss">
  .vel-modal {
    @apply bg-slate-600;
  }
</style>

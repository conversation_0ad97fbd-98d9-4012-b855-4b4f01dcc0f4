<template>
  <q-dialog
    persistent
    :modelValue="traceVisible"
    maximized
    transition-show="slide-up"
    transition-hide="slide-down"
    class="z-top gps-trace-dialog"
  >
    <q-card>
      <MaplibreGL
        :map-options="mapOptions"
        @init="onMapInit"
      >
        <q-btn
          ref="closeMapRef"
          class="custom-control-btn closeMap"
          icon="close"
          flat
          size="sm"
          :ripple="false"
          @click="closeMap"
        />
      </MaplibreGL>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts" generic="T extends Record<string, any> = any">
  import MaplibreGL from '@components/MaplibreGL.vue'
  import { ref, shallowReactive, customRef } from 'vue'
  import { webConfig } from '@src/config'
  import storage, { MapInfoKey } from '@utils/storage'
  import { QBtn } from 'quasar'
  import { deferred } from '@utils/common'
  import maplibreGl, { Map } from 'maplibre-gl'
  import { CustomControl, GpsTrackIntroControl, GpsTrackPlayerControl } from '@utils/map'
  import { TracePlayerData } from './types'

  const props = withDefaults(
    defineProps<{
      modelValue: boolean,
      playData: TracePlayerData<T>[],
      notPlayData?: TracePlayerData<T>[],
      getPopupHtml: (data: TracePlayerData<T>) => string,
      createHTMLElement?: () => HTMLElement,
      sortPlayData?: (dataList: TracePlayerData<T>[]) => void,
      sortNotPlayData?: (dataList: TracePlayerData<T>[]) => void,
    }>(),
    {
      notPlayData: () => {
        return []
      },
      createHTMLElement: () => document.createElement('div'),
    }
  )

  const traceVisible = customRef<boolean>((track: () => void, trigger: () => void) => ({
    get() {
      track()
      return props.modelValue
    },
    set(value: boolean) {
      trigger()
      emits('update:model-value', value)
    },
  }))

  const emits = defineEmits<{
    'update:model-value': [val: boolean],
  }>()

  Object.assign(webConfig.map, storage.fetch(MapInfoKey))
  const mapOptions = shallowReactive<Partial<maplibreGl.MapOptions>>({
    center: [0, 0],
    zoom: 12,
    minZoom: 1,
    maxZoom: 18,
    ...webConfig.map,
  })

  const closeMapRef = ref<QBtn>()
  // let localeMap: Map | null = null
  let mapReady = deferred<boolean>()

  let gpsTrackControl = new GpsTrackPlayerControl<T>({
    getTrackPopupHtml: props.getPopupHtml,
  })

  async function onMapInit(map: Map) {
    // localeMap = map
    mapReady = deferred<boolean>()
    map.setCenter(webConfig.map?.center)
    map.addControl(
      new CustomControl({
        buttons: [
          closeMapRef.value?.$el,
        ],
        onAdded(container) {
          container.parentNode?.insertBefore(container, container.parentNode?.firstChild)
        }
      }),
      'top-right',
    )
    map.addControl(gpsTrackControl, 'top-left')
    map.addControl(new GpsTrackIntroControl(
      props.createHTMLElement()
    ), 'top-left')

    map.once('load', () => {
      mapReady.resolve(true)
    })

    // 对需要显示的轨迹数据进行排序
    if (props.sortPlayData) {
      props.sortPlayData(props.playData)
    }
    if (props.sortNotPlayData) {
      props.sortNotPlayData(props.notPlayData)
    }

    // 按打卡时间排序
    await mapReady
    gpsTrackControl.setData(props.playData, props.notPlayData)
  }

  const closeMap = () => {
    // if (localeMap) {
    //   localeMap.off('click', onMapClick)
    // }
    emits('update:model-value', false)
    gpsTrackControl.map?.removeControl(gpsTrackControl)
  }
</script>

<style scoped lang="scss">
</style>

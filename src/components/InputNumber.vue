<template>
  <q-input
    ref="inputNumber"
    v-model="modelLabel"
    class="bf-input-number"
    :debounce="debounce"
    :label="label"
    :label-color="labelColor"
    :color="color"
    :bg-color="bgColor"
    :rules="rules"
    :lazy-rules="lazyRules"
    :loading="loading"
    :clearable="clearable"
    :filled="filled"
    :outlined="outlined"
    :borderless="borderless"
    :standout="standout"
    :rounded="rounded"
    :square="square"
    :dense="dense"
    :disable="disable"
    :readonly="readonly"
    :autofocus="autofocus"
    :reactive-rules="reactiveRules"
    @clear="handleClear"
    @blur="handleBlur"
    @focus="handleFocus"
    @update:model-value="(value) => modelLabelOnChange(value)"
  >
    <template #append>
      <div class="column q-ml-xs input-number-control">
        <q-btn
          size="sm"
          class="q-pa-none input-number-btn"
          icon="arrow_drop_up"
          dense
          unelevated
          :disable="disableIncrease"
          @click="increase"
        ></q-btn>
        <q-btn
          size="sm"
          class="q-pa-none input-number-btn border-1"
          icon="arrow_drop_down"
          dense
          unelevated
          :disable="disableDecrease"
          @click="decrease"
        ></q-btn>
      </div>
    </template>
    <template v-if="$slots.after" #after>
      <slot name="after"></slot>
    </template>
    <template v-if="$slots.before" #before>
      <slot name="before"></slot>
    </template>
    <template v-if="$slots.prepend" #prepend>
      <slot name="prepend"></slot>
    </template>
    <template #default>
      <slot name="default">
      </slot>
    </template>
  </q-input>
</template>

<script setup lang="ts">
  import { debounce as lodashDebounce } from 'lodash'
  import { QInput, useFormChild, ValidationRule } from 'quasar'
  import { ref, watch, nextTick, computed } from 'vue'

  /* 定义接收的props参数 */
  const props = withDefaults(
    defineProps<{
      modelValue: number | null | undefined
      min?: number
      max?: number
      step?: number
      // 是否只能输入 step 的倍数
      stepStrictly?: boolean
      format?: (value: number) => string
      transform?: (value: string | number) => number | undefined
      // 小数点精度
      precision?: number

      // q-input 组件原生的props
      // https://quasar.dev/vue-components/input#qinput-api
      // vue3中defineProps不能接受复杂的接口类型，所以这里只摘取部分属性
      label?: string
      rules?: ValidationRule[]
      lazyRules?: boolean | 'ondemand'
      reactiveRules?: boolean
      labelColor?: string
      color?: string
      bgColor?: string
      loading?: boolean
      clearable?: boolean
      filled?: boolean
      outlined?: boolean
      borderless?: boolean
      standout?: boolean | string
      rounded?: boolean
      square?: boolean
      dense?: boolean
      disable?: boolean
      readonly?: boolean
      autofocus?: boolean
      debounce?: string | number
    }>(),
    {
      min: 0,
      max: 0xffffffff, // 默认最大4个字节
      step: 1,
      stepStrictly: true,
      precision: 0,
      format: (value: number) => value?.toString(),

      debounce: 800,
      reactiveRules: false,
    }
  )

  /* 定义组件内表单绑定数据 */
  const inputNumber = ref<QInput | null>(null)
  const modelLabel = ref<string | number | null>(null)
  const model = ref<number>(props.modelValue ?? props.min ?? 0)

  /* 定义向父组件传递的事件 */
  const emits = defineEmits(['update:model-value', 'clear', 'blur', 'focus'])
  const updateModelValue = (value: number) => {
    emits('update:model-value', value)
  }
  const emitClear = (value: number) => {
    emits('clear', value)
  }
  const emitBlur = (evt: Event) => {
    emits('blur', evt)
  }
  const emitFocus = (evt: Event) => {
    emits('focus', evt)
  }

  const syncModelLabel = (value: number) => {
    updateModelValue(value)
    nextTick(() => {
      modelLabel.value = props.format(value)
    })
  }

  const transformValue = (value: number, step: number, stepStrictly?: boolean, precision?: number) => {
    // 必须是步进值的整数倍
    if (stepStrictly) {
      value = Math.round(value / step) * step
    } else if (precision !== undefined) {
      // 处理小数点精度
      value = Number(value.toFixed(precision))
    }

    return value
  }

  // 手动输入内容，尝试转换成数字，如果不是数字，则使用原值
  const modelLabelOnChange = lodashDebounce((value: string | number | null) => {
    const oldModelValue = model.value
    const num = Number(value)
    const transformData = props.transform?.(value as string | number) ?? num
    if (isNaN(transformData)) {
      syncModelLabel(model.value)
      return
    }

    model.value = transformValue(
      transformData > props.max ? props.max : transformData < props.min ? props.min : transformData,
      props.step,
      props.stepStrictly,
      props.precision
    )

    // 输入小数字符串时，取整后model.value不变导致modelLabel.value与model.value不一致，需要手动更新
    if (num !== oldModelValue) {
      syncModelLabel(model.value)
    }
  }, 350)

  // 监听格式化方法变更，重新格式化内容
  watch(
    () => props.format,
    (fn?: (value: number) => string) => {
      modelLabel.value = fn?.(model.value) ?? modelLabel.value
    }
  )

  // 同步父组件传递的值
  watch(
    () => props.modelValue,
    (value: number | null | undefined) => {
      model.value = value as number
    },
    { immediate: true }
  )
  // 监听实际值变化，格式化显示内容，并向父组件发送更新
  watch(() => model.value, syncModelLabel, { immediate: true })

  const disableIncrease = computed(() => {
    return props.readonly || model.value >= props.max
  })

  const disableDecrease = computed(() => {
    return props.readonly || model.value <= props.min
  })

  /* 按钮组事件 */
  // 增加
  const increase = () => {
    let value = model.value + props.step
    if (value > props.max) {
      value = props.max
    }

    model.value = transformValue(value, props.step, props.stepStrictly, props.precision)
  }
  // 减少
  const decrease = () => {
    let value = model.value - props.step
    if (value < props.min) {
      value = props.min
    }

    model.value = transformValue(value, props.step, props.stepStrictly, props.precision)
  }

  /* 向父组件传递的事件 */
  // 失去焦点
  const handleBlur = (evt: Event) => {
    emitBlur(evt)
  }
  // 获得焦点
  const handleFocus = (evt: Event) => {
    emitFocus(evt)
  }
  // 清空
  const handleClear = (value: number) => {
    emitClear(value)
  }

  const validate: () => boolean | Promise<boolean> = () => {
    return inputNumber.value?.validate() ?? true
  }
  const resetValidation = () => {
    inputNumber.value?.resetValidation()
  }

  useFormChild({
    validate, // Function; Can be async;
    // Should return a Boolean (or a Promise resolving to a Boolean)
    resetValidation, // Optional function which resets validation
    requiresQForm: true, // should it error out if no parent QForm is found?
  })

  defineExpose({
    validate,
    resetValidation,
  })
</script>

<style lang="scss">
  .bf-input-number {
    .input-number-control {
      opacity: 0;
    }

    &:hover .input-number-control,
    &:focus .input-number-control,
    &.q-field--focused .input-number-control {
      opacity: 1;
    }
  }
</style>

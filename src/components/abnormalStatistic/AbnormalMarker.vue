<template>
  <div class="abnormal-marker-container">
    <q-tabs
      v-model="tab"
      inline-label
      class="shadow-2"
    >
      <q-tab
        name="notReport"
        :label="$t('abnormalStatistics.notReportedOnTime')"
        icon="report_problem"
        class="text-purple"
      >
        <q-badge
          color="red"
          rounded
          floating
          v-show="notReportMarkers.length"
          class="text-xs"
        />
      </q-tab>
      <q-tab
        name="lowBattery"
        :label="$t('abnormalStatistics.lowBatteryAlert')"
        icon="battery_alert"
        class="text-orange"
      >
        <q-badge
          color="red"
          rounded
          floating
          v-show="lowBatteryMarkers.length"
          class="text-xs"
        />
      </q-tab>
      <q-tab
        name="remoteKill"
        :label="$t('abnormalStatistics.remoteKillAndStun')"
        icon="report_off"
        class="text-red"
      >
        <q-badge
          color="red"
          rounded
          floating
          v-show="remoteKillMarkers.length"
          class="text-xs"
        />
      </q-tab>
      <q-tab
        name="cardExpires"
        :label="$t('abnormalStatistics.cardExpires4G')"
        icon="alarm"
        class="text-teal"
      >
        <q-badge
          color="red"
          rounded
          floating
          v-show="cardExpiresMarkers.length"
          class="text-xs"
        />
      </q-tab>
    </q-tabs>
    <q-separator />
    <q-tab-panels
      v-model="tab"
      class="abnormal-marker-q-tab-panels"
      animated
      keep-alive
    >
      <q-tab-panel name="notReport">
        <show-data
          :columns="notReportColumns"
          :rows="notReportMarkers"
          :export-name-prefix="exportNamePrefix"
          @refresh="refreshData"
        >
          <template v-slot:my-body-cell-TimeoutDuration="{ value, row }">
            <q-badge
              v-if="value"
              :color="getActionTimeColors(row.markerInfo?.MarkerCmdTime ?? '')"
              :label="value"
            />
            <template v-else>{{ '' }}</template>
          </template>
        </show-data>
      </q-tab-panel>
      <q-tab-panel name="lowBattery">
        <show-data
          :columns="lowBatteryColumns"
          :rows="lowBatteryMarkers"
          :export-name-prefix="exportNamePrefix"
          @refresh="refreshData"
        >
          <template v-slot:my-body-cell-Battery="{ value }">
            <q-badge
              :color="getDaysLowBatteryColors(value)"
              :label="value"
            />
          </template>
        </show-data>
      </q-tab-panel>
      <q-tab-panel name="remoteKill">
        <show-data
          :columns="remoteKillColumns"
          :rows="remoteKillMarkers"
          :export-name-prefix="exportNamePrefix"
          @refresh="forceUpdateRefreshData"
        >
          <template v-slot:my-body-cell-KillStatus="{ row }">
            <q-badge
              :color="killStatusColors[row.MarkerDisabled !== 0 ? row.MarkerDisabled : row.CameraDisabled]"
              :label="getKillStatusLabel(row)"
            />
          </template>
          <template v-slot:my-body-cell-removeStun="{ row }">
            <q-btn
              v-if="row.MarkerDisabled !== 1"
              color="primary"
              dense
              size="sm"
              @click="markerRemoveStun(row)"
            >
              {{ $t('abnormalStatistics.removeStun') }}
            </q-btn>
          </template>
        </show-data>
      </q-tab-panel>
      <q-tab-panel name="cardExpires">
        <show-data
          :columns="cardExpiresColumns"
          :rows="cardExpiresMarkers"
          :export-name-prefix="exportNamePrefix"
          @refresh="forceUpdateRefreshData"
        >
          <template v-slot:my-body-cell-MarkerHWID="{ value, row }">
            <span>{{ value }}</span>
            <q-icon
              name="report_problem"
              color="warning"
              class="ml-1"
              size="xs"
              v-if="!row.HasInstallDevice">
              <q-tooltip :offset="[10, 10]">
                {{ row.HasInstallStone ? $t('markerStatistics.notInstallDevice') : $t('markerStatistics.notInstallStone') }}
              </q-tooltip>
            </q-icon>
          </template>
          <template v-slot:my-body-cell-DaysRemaining="{ value }">
            <q-badge
              :color="getDaysRemainingColors(value)"
              :label="value"
            />
          </template>
        </show-data>
      </q-tab-panel>
    </q-tab-panels>
  </div>
</template>

<script lang="ts">
  import { BysMarker, Unit } from '@services/dataStore'
  import { org } from '@ygen/org'
  import { bysdb } from '@ygen/bysdb'
  import {
    calcMarkerPower,
    checkIs4GMarker,
    checkoutAbnormalBysMarker,
    CmdCode,
    DeviceStatus,
    isHaveMaintainPerm,
    MarkerLowPower4g,
    secondConfirmDialog
  } from '@utils/common'
  import {
    getSubtractTime,
    utcTime,
    wrapperInvalidDate,
    dayjs,
    DateMask,
    toLocalTime,
    getDiffFromExpirationDate, getDeffDay
  } from '@utils/dayjs'
  import {QueryAbnormalMarkers, syncMarkerDataAndPartialUpdateSetting } from '@services/queryData'
  import { BysMarkerAndUpdateInfo } from '@utils/bysdb.type'
  import { cloneDeep, debounce } from 'lodash'
  import ShowData from './ShowData.vue'
  import { doc } from '@ygen/bys.api'
  import { defineComponent } from 'vue'
  import { useBysMarkerData } from '@utils/vueUse/bysMarkerData'
  import { markerSetRemoteKillOrActive } from '@src/services/controller'

  const { bysMarkerData } = useBysMarkerData()

  export default defineComponent({
    name: 'AbnormalMarker',
    props: {
      exportName: {
        type: String,
        default: 'marker',
      },
    },
    components: {
      ShowData,
    },
    data() {
      return {
        tab: 'notReport',
        abnormalMarkerData: [] as Array<BysMarkerAndUpdateInfo>,
      }
    },
    computed: {
      haveMaintainPerm() {
        return isHaveMaintainPerm()
      },
      bysMarkerData() {
        return bysMarkerData.value
      },
      cmdCodeLabels() {
        return {
          // 常规界桩
          [CmdCode.D1]: this.$t('CmdTest.report'),
          [CmdCode.D2]: this.$t('CmdTest.alarm'),
          [CmdCode.D3]: this.$t('CmdTest.init'),

          // 4g界桩
          [CmdCode.InfoReport]: this.$t('CmdTest.report'),
          [CmdCode.AlarmReport]: this.$t('CmdTest.alarm'),
        }
      },
      // 4g界桩的上报类型
      reportTypeLabels() {
        return {
          [CmdCode.InfoReport]: {
            1: this.$t('CmdTest.reportOnStartup'),
            2: this.$t('CmdTest.reportOnDebugging'),
            3: this.$t('CmdTest.reportOnRegular'),
          },
          [CmdCode.AlarmReport]: {
            1: this.$t('CmdTest.reportOnStartup'),
            2: this.$t('CmdTest.reportOnDebugging'),
            3: this.$t('CmdTest.reportToAlarm'),
          },
        }
      },
      installedDeviceMarkers() {
        return this.bysMarkerData.filter(item => item.HasInstallDevice)
      },
      exportNamePrefix() {
        return `${this.exportName}-${this.tab}`
      },
      commonColumns() {
        return [
          { name: '$index', field: '$index', label: '#', align: 'right' },
          {
            name: 'OrgRID',
            field: 'OrgRID',
            label: this.$t('form.unit'),
            align: 'center',
            sortable: true,
            format: (val: string) => {
              const orgData = Unit.getData(val) as org.IDbOrg | undefined
              return orgData?.ShortName ?? val
            },
          },
          {
            name: 'MarkerHWID',
            field: 'MarkerHWID',
            label: this.$t('form.markerName'),
            align: 'center',
            sortable: true,
            format: (val: number) => {
              const controller: bysdb.IDbBysMarker | undefined = BysMarker.getDataByIndex(val + '')
              return controller?.MarkerNo ?? val
            },
          },
        ]
      },
      notReportColumns() {
        return [
          ...this.commonColumns,
          {
            name: 'LastCmd',
            field: (row) => row.markerInfo?.MarkerUploadCmd ?? 0,
            label: this.$t('CmdTest.lastCmd'),
            align: 'center',
            sortable: true,
            format: (val: number, row: BysMarkerAndUpdateInfo) => {
              const label = this.cmdCodeLabels[val] ?? ''
              if (checkIs4GMarker(row.markerInfo?.MarkerType)) {
                const typeLabels = this.reportTypeLabels[val]
                const cmdType = (val === CmdCode.AlarmReport ? row.markerInfo?.AlarmReporting : row.markerInfo?.InfoReporting)?.type ?? 0
                const description = typeLabels[cmdType]
                if (description) {
                  return `${label} (${description})`
                }
              }
              return label
            },
          },
          {
            name: 'ActionTime',
            field: (row) => row.markerInfo?.MarkerCmdTime ?? '',
            label: this.$t('form.lastUploadTime'),
            align: 'center',
            sortable: true,
            format: (val: string) => {
              return wrapperInvalidDate(val)
            }
          },
          {
            name: 'TimeoutDuration',
            field: (row) => row.markerInfo?.MarkerCmdTime ?? '',
            label: this.$t('abnormalStatistics.timeoutDuration'),
            align: 'center',
            sortable: true,
            customClass: 'sticky-column-last',
            format: (val: string) => {
              // 计算出是多少天前
              const diffVal = getDeffDay(dayjs(val), dayjs.utc(new Date()))
              if (isNaN(diffVal)) return ''
              return this.$t('date.nDaysAgo', { num: diffVal })
            },
          },
        ]
      },
      notReportMarkers(): Array<BysMarkerAndUpdateInfo> {
        if (!this.haveMaintainPerm) return []
        // 过滤出3天内没有打卡的界桩
        const limitTime = getSubtractTime(utcTime(), 3, 'day')
        const isAbnormalTime = (time) => {
          return dayjs(time).isBefore(limitTime)
        }
        return this.abnormalMarkerData
          .filter(item => {
            const MarkerCmdTime = item.markerInfo?.MarkerCmdTime ?? '2000-01-01 00:00:00'
            return isAbnormalTime(MarkerCmdTime)
          })
          .sort((a, b) => {
            const aTime = a.markerInfo?.MarkerCmdTime ?? ''
            const bTime = b.markerInfo?.MarkerCmdTime ?? ''
            if (!aTime && bTime) return 1
            if (aTime && !bTime) return -1
            // 降序
            return bTime.localeCompare(aTime)
          })
      },
      lowBatteryColumns() {
        return [
          ...this.commonColumns,
          {
            name: 'Battery',
            field: (row) => row.markerInfo ?? {},
            label: this.$t('CmdTest.batteryPower'),
            align: 'center',
            sortable: true,
            format: (val: doc.IMarkerInfo, row: BysMarkerAndUpdateInfo) => {
              if (checkIs4GMarker(row.MarkerType)) {
                const { InfoReporting } = val
                let { battery } = InfoReporting ?? {}
                battery = battery ?? 0
                if (battery === 0) return '0v'

                const power = ((battery ?? 0) / 1000).toFixed(1)
                return `${power}v`
              }

              const powerStr = DeviceStatus.batteryPower(val.Status ?? 0)
              return calcMarkerPower(powerStr)
            },
          },
        ]
      },
      lowBatteryMarkers(): Array<BysMarkerAndUpdateInfo> {
        if (!this.haveMaintainPerm) return []
        return this.abnormalMarkerData
          .filter(item => {
            if (checkIs4GMarker(item.markerInfo?.MarkerType)) {
              const { InfoReporting } = item.markerInfo
              const { battery } = InfoReporting ?? {}
              return (battery as number) <= MarkerLowPower4g
            } else {
              return DeviceStatus.isLowPowerAlarm(item.markerInfo?.Status ?? 0)
            }
          })
      },
      killStatusColors() {
        return {
          0: 'positive',
          1: 'negative',
          2: 'orange',
        }
      },
      killLabels() {
        return {
          1: this.$t('form.killRemotely'),
          2: this.$t('form.remoteStun'),
        }
      },
      cameraDisableLabels() {
        return {
          2: this.$t('form.cameraRemoteStun'),
        }
      },
      remoteKillColumns() {
        return [
          ...this.commonColumns,
          {
            name: 'KillDateTime',
            field: 'Setting',
            label: this.$t('form.time'),
            align: 'center',
            sortable: true,
            format: (val: string) => {
              try {
                const setting = JSON.parse(val)
                if (!setting?.MarkerDisabledTime) return ''
                return toLocalTime(setting.MarkerDisabledTime)
              } catch (e) {
                return ''
              }
            },
          },
          {
            name: 'KillStatus',
            field: '',
            label: this.$t('abnormalStatistics.remoteKillAndStun'),
            align: 'center',
            sortable: true,
            customClass: 'sticky-column-last',
          },
          {
            name: 'removeStun',
            align: 'center',
            label: this.$t('abnormalStatistics.operate'),
          }
        ]
      },
      remoteKillMarkers(): Array<BysMarkerAndUpdateInfo> {
        if (!this.haveMaintainPerm) return []
        return this.bysMarkerData.filter(item => (item.MarkerDisabled as number) > 0 || item.CameraDisabled == 2)
      },
      cardExpiresColumns() {
        return [
          ...this.commonColumns,
          {
            name: 'ICCID',
            field: 'ICCID',
            label: 'ICCID',
            align: 'center',
            sortable: true,
          },
          {
            name: 'ExpirationDate',
            field: 'ExpirationDate',
            label: this.$t('form.ExpirationDate'),
            align: 'center',
            sortable: true,
            format: (val: string) => {
              return dayjs(val).format(DateMask)
            },
          },
          {
            name: 'DaysRemaining',
            field: 'ExpirationDate',
            label: this.$t('abnormalStatistics.daysRemaining'),
            align: 'center',
            sortable: true,
            customClass: 'sticky-column-last',
            format: (val: string) => {
              // 计算出是多少天前
              return getDiffFromExpirationDate(val)
            },
          },
        ]
      },
      cardExpiresMarkers(): Array<BysMarkerAndUpdateInfo> {
        // 只统计10天内到期的4G卡
        const beforeDay10 = getSubtractTime(utcTime(), -10, 'day')
        return this.bysMarkerData
          .filter(item => !!item.ICCID && dayjs(beforeDay10).isSameOrAfter(item.ExpirationDate))
          .sort((a, b) => {
            const aTime = a.ExpirationDate ?? ''
            const bTime = b.ExpirationDate ?? ''
            if (!aTime && bTime) return 1
            if (aTime && !bTime) return -1
            // 升序，到期剩余天数越小则显示在前面
            return aTime.localeCompare(bTime)
          })
      },
    },
    methods: {
      getKillStatusLabel(data: bysdb.IDbBysMarker) {
        const { MarkerDisabled, CameraDisabled } = data
        // 界桩遥晕遥毙优先显示 然后判断是否为摄像机遥晕
        if (MarkerDisabled && MarkerDisabled !== 0) {
          return this.killLabels[MarkerDisabled] ?? MarkerDisabled + ''
        }
        if (CameraDisabled && CameraDisabled !== 0) {
          return this.cameraDisableLabels[CameraDisabled] ?? CameraDisabled + ''
        }
        return ''
      },
      getActionTimeColors(actionTime: string): string {
        const time = toLocalTime(actionTime)
        const diffVal = dayjs.utc(utcTime()).diff(dayjs.utc(time), 'day')
        if (diffVal >= 15) return 'negative'
        if (diffVal >= 3) return 'orange'
        return 'teal'
      },
      // 4g界桩为具体的电量值，batteryLabel: 1% or 2v
      getDaysLowBatteryColors(batteryLabel: string): string {
        const battery = Number(batteryLabel.slice(0, -1))
        if (isNaN(battery)) return 'info'

        // 没有电量了
        if (battery <= 0) return 'negative'
        return 'orange'
      },
      getDaysRemainingColors(days: number): string {
        if (days > 3) return 'teal'
        if (days > 0) return 'orange'
        return 'negative'
      },
      setAbnormalMarkerData(list: Array<BysMarkerAndUpdateInfo>) {
        const now = utcTime()
        const markers = list.filter(item => checkoutAbnormalBysMarker(item ?? {}, now))
        this.abnormalMarkerData = Object.freeze(cloneDeep(markers)) as BysMarkerAndUpdateInfo[]
      },
      refreshData(showNotify = true) {
        QueryAbnormalMarkers(() => {
          this.setAbnormalMarkerData(this.installedDeviceMarkers)
          if (showNotify) {
            this.$q.notify({
              type: 'positive',
              message: this.$t('message.refreshSuccessful') as string,
            })
          }
        })
      },
      // 统计界桩遥毙/遥晕，不需要请求服务器，直接强制更新vue实例
      forceUpdateRefreshData() {
        this.$q.notify({
          type: 'positive',
          message: this.$t('message.refreshSuccessful') as string,
        })
      },
      // 解除遥晕
      async markerRemoveStun(data: BysMarkerAndUpdateInfo) {
        const msg = this.$t('form.confirmCancelKillOrStun', { cmd: data.MarkerDisabled !== 0 ? this.$t('form.remoteStun') : this.$t('form.cameraRemoteStun') })
        const htmlMsg = '<i class="q-icon text-warning notranslate material-icons" aria-hidden="true" role="presentation" style="font-size: 2rem;padding-right: 0.5rem;">warning</i>'
        const send = await secondConfirmDialog(htmlMsg + msg, this.$t('form.sendCmd'), this.$t('common.cancel'), { html: true })
        if (!send) return 
        const ok = await markerSetRemoteKillOrActive(0, 0, data)
        if (!ok) {
          return
        }
        syncMarkerDataAndPartialUpdateSetting(data.RID as string, {})
      },
    },
    watch: {
      installedDeviceMarkers: {
        deep: true,
        handler(val) {
          this.setAbnormalMarkerData(val)
        },
      },
    },
    beforeMount() {
      this.setAbnormalMarkerData = debounce(this.setAbnormalMarkerData, 350)
      this.refreshData(false)
      this.tab = this.haveMaintainPerm ? 'notReport' : 'remoteKill'
    },
  })
</script>

<style lang="scss">
  .abnormal-marker-container {
    display: flex;
    flex-direction: column;

    &>.q-tabs {
      flex: none;
    }

    &>.q-tab-panels {
      flex: auto;

      .q-tab-panel {
        padding: 16px 0;

        .sticky-column-last {
          position: sticky !important;
          background-color: #fff;
          right: 0 !important;
          z-index: 3 !important;
        }
      }
    }
  }
</style>

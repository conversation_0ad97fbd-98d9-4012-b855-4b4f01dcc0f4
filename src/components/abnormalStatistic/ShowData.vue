<template>
  <q-table
    :rows="wrapRows"
    :columns="wrapColumns"
    bordered
    flat
    dense
    class="abnormal-marker-table full-width"
    v-model:pagination="pagination"
    :rows-per-page-options="rowsPerPageOptions"
    virtual-scroll
    :virtual-scroll-sticky-size-start="0"
    :filter="filter"
    row-key="RID"
    separator="cell"
  >
    <template v-slot:top>
      <div class="row full-width">
        <q-btn
          class="q-ml-sm"
          color="primary"
          dense
          :label="$t('common.export')"
          @click="exportTable"
        />
        <q-btn
          class="q-ml-sm"
          color="primary"
          dense
          :label="$t('common.refresh')"
          @click="refreshData"
        />
        <q-space />
        <q-input
          v-model="filter"
          dense
          debounce="300"
          color="primary"
          :placeholder="$t('common.search')"
        >
          <template v-slot:append>
            <q-icon name="search" />
          </template>
        </q-input>
      </div>
    </template>
    <template v-slot:header="props">
      <q-tr :props="props">
        <q-th
          v-for="col in props.cols"
          :key="col.name"
          :props="props"
          :class="col?.customClass ?? ''"
        >
          {{ col.label }}
        </q-th>
      </q-tr>
    </template>
    <template v-slot:body="props">
      <q-tr :props="props" @dblclick="onRowDblclick(props.row)">
        <q-td
          :props="props"
          v-for="(col,) in props.cols"
          :key="col.name"
          :class="col?.customClass ?? ''"
        >
          <slot
            :name="'my-body-cell-' + col.name"
            v-bind="{ ...props, col, value: col.value }"
          >
            <template v-if="col.name === '$index'"> {{ props.rowIndex + 1 }}</template>
            <template v-else> {{ col.value }}</template>
          </slot>
        </q-td>
      </q-tr>
    </template>
  </q-table>
</template>

<script lang="ts">
  import { cloneDeep } from 'lodash'
  import dayjs from 'dayjs'
  import { exportData2Excel } from '@utils/xlsx'
  import { defineComponent } from 'vue'
  import { flyTo } from '@src/utils/map'
  import globalConfig from '@src/config'
  import { setGCJ02LngLat } from '@src/utils/gcoord'

  // 每页显示行数
  const rowsPerPageOptions = [0]

  export default defineComponent({
    name: 'ShowData',
    props: {
      columns: {
        type: Array,
        required: true,
      },
      rows: {
        type: Array,
        required: true,
      },
      exportNamePrefix: {
        type: String,
        default: 'abnormal-statistics',
      },
    },
    data() {
      return {
        filter: '',
        pagination: {
          rowsPerPage: rowsPerPageOptions[0],
        },
      }
    },
    computed: {
      rowsPerPageOptions() {
        return rowsPerPageOptions
      },
      wrapColumns() {
        return this.columns
      },
      wrapRows() {
        return Object.freeze(cloneDeep(this.rows))
      }
    },
    methods: {
      refreshData() {
        this.$emit('refresh')
      },
      exportTable() {
        const fileName = `${this.exportNamePrefix}-${dayjs().format('YYYYMMDD-HHmmss')}`
        exportData2Excel({ fileName, data: this.wrapRows, columns: this.wrapColumns })
      },
      onRowDblclick(row) {
        if (!row.GCJ02LngLat) {
          setGCJ02LngLat(row)
        }
        flyTo(row.GCJ02LngLat, globalConfig.map?.getZoom())
      },
    },
  })
</script>

<style lang="scss">
  $stickyBgColor: #fff;

  .abnormal-marker-table {
    max-width: 100vw;

    .q-btn {
      flex: none;
      height: fit-content;
      align-self: center;
    }

    .q-input {
      width: 330px;
      max-width: 80vw;
    }

    &.q-table--dense .q-table {

      th,
      td {
        padding: 2px 4px;
      }
    }

    thead {
      tr th {
        position: sticky;
        background-color: $stickyBgColor;
        // 表格头部数据区的粘贴层叠要比内容区的高
        z-index: 2;
        top: 0;
      }
    }
  }
</style>

<template>
  <q-table
    :rows="abnormalControllerData"
    :columns="columns"
    bordered
    flat
    dense
    class="abnormal-controller-table full-height full-width"
    v-model:pagination="pagination"
    :rows-per-page-options="rowsPerPageOptions"
    virtual-scroll
    :virtual-scroll-sticky-size-start="0"
    :filter="filter"
    row-key="RID"
    separator="cell"
  >
    <template v-slot:top>
      <div class="full-width row">
        <q-btn
          class="q-ml-sm"
          color="primary"
          :label="$t('common.export')"
          dense
          @click="exportTable"
        />
        <q-btn
          class="q-ml-sm"
          color="primary"
          :label="$t('common.refresh')"
          dense
          @click="refreshData"
        />
        <q-space/>
        <q-input
          v-model="filter"
          dense
          debounce="300"
          color="primary"
          :placeholder="$t('common.search')"
        >
          <template v-slot:append>
            <q-icon name="search"/>
          </template>
        </q-input>
      </div>
    </template>
    <template v-slot:header="props">
      <q-tr :props="props">
        <q-th
          v-for="col in props.cols"
          :key="col.name"
          :class="{ 
            ['sticky-column-' + col.name]: col.sticky,
            ['no-left-border-' + col.name]: col.noLeftBorder
          }"
          :props="props"
        >
          {{ col.label }}
        </q-th>
      </q-tr>
    </template>
    <template v-slot:body="props">
      <q-tr :props="props" @dblclick="onRowDblclick(props.row)">
        <q-td
          v-for="(col, index) in props.cols"
          :key="col.name + index"
          :class="{ 
            ['sticky-column-' + col.name]: col.sticky ,
            ['no-left-border-' + col.name]: col.noLeftBorder
          }"
        >
          <template v-if="index === 0"> {{ props.rowIndex + 1 }}</template>
          <template v-else> {{ col.value }}</template>
        </q-td>
      </q-tr>
    </template>
  </q-table>
</template>

<script lang="ts">
  import { computed, ref, getCurrentInstance, ComputedRef, reactive } from 'vue'
  import { GET_DATA, NS } from '@src/store/data/methodTypes'
  import { DataName } from '@src/store/data'
  import { exportData2Excel } from '@src/utils/xlsx'
  import { localTime, toLocalTime } from '@src/utils/dayjs'
  import { bysdb } from '@ygen/bysdb'
  import dataEditMixin from '@utils/mixins/editor'
  import dayjs from 'dayjs'
  import { QueryAbnormalControllers } from '@src/services/queryData'
  import { calcControllerPower, ControllerAbnormalReason, exNetworkType } from '@utils/common'
  import { ExtendsQTableColumn } from '@utils/bysdb.type'
  import { useStore } from 'vuex'
  import { useI18n } from 'vue-i18n'
  import { flyTo } from '@src/utils/map'
  import globalConfig from '@src/config'
  import { setGCJ02LngLat } from '@src/utils/gcoord'
  // 每页显示行数
  const rowsPerPageOptions = [0]
  export default {
    name: 'AbnormalController',
    mixins: [dataEditMixin],
    setup() {
      const store = useStore()
      const i18n = useI18n()
      const { proxy } = getCurrentInstance() as any

      const filter = ref('')
      const pagination = reactive({ rowsPerPage: rowsPerPageOptions[0] })
      const exportName = ref(localTime())

      const ControllerTypeOptions = computed(() => {
        return [
          { label: i18n.t('form.repeater'), value: 1 },
          { label: i18n.t('form.baseStation'), value: 2 },
        ]
      })

      const AllControllerOptions = computed(() => {
        return allControllerData.value
          .map((data: bysdb.IDbController) => {
            return {
              label: data.ControllerNo,
              value: data.RID,
              ControllerType: data.ControllerType,
              ControllerHWID: data.ControllerHWID,
            }
          })
      })

      // const ControllerRIDOptions = computed(() => {
      //   return AllControllerOptions.value
      //     .filter((option: { [key: string]: any }) => {
      //       // 过滤中继类型，只能是基站控制器
      //       return option.ControllerType === 2
      //     })
      //     .filter(option => {
      //       const needle = controllerFilter.toLowerCase()
      //       return option.label.toLowerCase().includes(needle)
      //     })
      // })

      const errReason = computed(() => {
        return {
          [ControllerAbnormalReason.LowPower]: i18n.t('controllerErrReason.lowPower'),
          [ControllerAbnormalReason.NetworkType]: i18n.t('controllerErrReason.networkType'),
          [ControllerAbnormalReason.NotFskButNotOnline]: i18n.t('controllerErrReason.offline'),
          [ControllerAbnormalReason.BaseStationNotOnline]: i18n.t('controllerErrReason.offline'),
          [ControllerAbnormalReason.TimeError]: i18n.t('controllerErrReason.timeError'),
        }
      })

      const columns: ComputedRef<ExtendsQTableColumn<{ sticky?: boolean }>> = computed(() => {
        return [
          { name: 'Index', field: '$index', label: '#', align: 'right' },
          {
            name: 'ControllerNo',
            field: 'ControllerNo',
            label: i18n.t('form.controllerID'),
            sortable: true,
            sticky: true,
          },
          {
            name: 'ControllerHWID', field: 'ControllerHWID', label: i18n.t('form.controllerHWID'), sortable: true, noLeftBorder: true, 
          },
          {
            name: 'DefaultNetworkType', field: 'DefaultNetworkType', label: i18n.t('form.defaultNetworkType'),
            sortable: true,
            format: (val) => {
              return exNetworkType[val]?.() ?? val
            },
          },
          //当前网络类型
          {
            name: 'controllerState', field: 'controllerState', label: i18n.t('statistics.currentNetworkType'),
            sortable: true,
            format: (val) => {
              if (val?.online) {
                return exNetworkType[val?.NetworkType]?.() ?? val?.NetworkType
              }
              return i18n.t('statistics.off')
            },
          },
          //电量
          {
            name: 'Power', field: 'controllerState', label: i18n.t('statistics.power'),
            sortable: true,
            format: (val) => {
              return calcControllerPower(val?.Power ?? 0)

              //洪总提到 如果电量小于 ControllerLowPower 就显示成 ControllerLowPower 2021-5-7
              // const pw = calcControllerPower(val?.Power ?? 0)
              // return (val?.Power ?? 0) < ControllerLowPower? ControllerLowPower: pw
            },
          },
          {
            name: 'errReason', field: 'Error', label: i18n.t('form.errReason'), sortable: true,
            format: (val) => {
              let reason: Array<string> = []
              const status: Array<string> = val?.status
              for (let status1 of status) {
                reason.push(errReason.value[status1])
              }
              return reason.join(', ')
            },
          },
          //最后数据时间 LastDataTime
          {
            name: 'LastDataTime', field: 'controllerState', label: i18n.t('form.lastUploadTime'),
            sortable: true,
            format: (val) => {
              return val?.LastDataTime ? toLocalTime(val?.LastDataTime) : ''
            },
          },
          {
            name: 'OrgRID', field: 'OrgRID', label: i18n.t('form.unit'), sortable: true,
            format: (val) => {
              const parentOption = proxy?.parentOptions.find(item => item.value === val)
              return `${parentOption ? parentOption.label : ''}`
            },
          },
          {
            name: 'ControllerType', field: 'ControllerType', label: i18n.t('form.type'), sortable: true,
            format: (val) => {
              const typeOption = ControllerTypeOptions.value.find(item => item.value === val)
              return `${typeOption ? typeOption.label : ''}`
            },
          },
          {
            name: 'ParentRID', field: 'ParentRID', label: i18n.t('form.parentControllerAndChannel'), sortable: true,
            format: (val, row) => {
              if (row.ControllerType === 1) {
                const Option = AllControllerOptions.value.find(item => item.value === val)
                return `${Option ? Option.label : ''} / ${row.ParentChannelNo ?? ''}`
              }
              return ''
            },
          },
        ]
      })

      const abnormalControllerData = computed(() => {
        return allControllerData.value.filter(item => item.Error?.err === true)
      })

      const allControllerData = computed(() => {
        return store.getters[`${NS}/${GET_DATA}`](DataName.Controller)
      })

      function refreshData() {
        QueryAbnormalControllers()
      }

      function exportTable() {
        const fileName = `${exportName.value}-${dayjs().format('YYYYMMDD-HHmmss')}`
        exportData2Excel({ fileName, data: abnormalControllerData, columns: columns })
      }

      function onRowDblclick(row) {
        if (!row.GCJ02LngLat) {
          setGCJ02LngLat(row)
        }
        flyTo(row.GCJ02LngLat, globalConfig.map?.getZoom())
      }

      return {
        refreshData,
        pagination,
        columns,
        abnormalControllerData,
        rowsPerPageOptions,
        exportTable,
        filter,
        onRowDblclick,
      }
    }
  }
</script>

<style lang="scss">
  $stickyBgColor: #fff;

  .abnormal-controller-table {
    max-width: 100vw;

    .q-btn {
      flex: none;
      height: fit-content;
      align-self: center;
    }

    .q-input {
      width: 330px;
      max-width: 80vw;
    }

    &.q-table--dense .q-table {

      th,
      td {
        padding: 2px 4px;
      }
    }

    [class*="sticky-column-"] {
      position: sticky;
      z-index: 1;
      background-color: $stickyBgColor;
      border-right: 1px solid #e3e3e3;
    }

    [class*="no-left-border-"] {
      border-left: none;
    }
    &.q-table--dense .q-table__middle table thead tr th[class *="sticky-column-"] {
      z-index: 3;
      left: 0;
    }
    tbody {
      tr td[class*="sticky-column-"] {
        position: sticky;
        z-index: 2;
        left: 0;
      }
    }

    thead {
      tr th {
        position: sticky;
        background-color: $stickyBgColor;
        // 表格头部数据区的粘贴层叠要比内容区的高
        z-index: 2;
        top: 0;
      }
    }
  }
</style>

<template>
  <q-table
    class="sticky-header-column-virtscroll-table full-height full-width"
    dense
    flat
    bordered
    virtual-scroll
    v-model:pagination="pagination"
    :rows-per-page-options="rowsPerPageOptions"
    :virtual-scroll-sticky-size-start="0"
    row-key="RID"
    :filter-method="filterMethod"
    :rows="wrapperData"
    :columns="wrapperColumns"
    :visible-columns="wrapVisibleColumns"
    :filter="filter"
    separator="cell"
    :loading="loading"
    ref="qTable"
  >
    <template v-slot:loading>
      <q-inner-loading showing>
        <q-circular-progress
          show-value
          class="text-light-blue q-ma-md"
          :value="progressValue"
          size="lg"
          color="light-blue"
          indeterminate
        ></q-circular-progress>
      </q-inner-loading>
    </template>

    <template v-slot:top>
      <div class="col-xs-12 col-sm-7 row q-gutter-sm">
        <q-btn
          class="q-mr-sm"
          color="primary"
          :label="$t('common.add')"
          size="sm"
          :disable="!canAdd"
          @click="newDataRow"
        />
        <q-btn
          class="q-mr-sm"
          color="primary"
          :label="$t('common.export')"
          size="sm"
          @click="exportTable"
        />
        <q-btn-dropdown
          v-if="allowImport"
          split
          no-caps
          class="q-mr-sm"
          color="primary"
          size="sm"
          :disable="!canAdd"
          @click="onClickImport"
        >
          <template v-slot:label>
            <div class="row items-center no-wrap">
              <q-input
                @update:model-value="val => { importFile(val) }"
                outlined
                type="file"
                class="hidden"
                ref="checkFile"
                :accept="importFileAccept"
              />
              {{ importDataList.length === 0 ? $t('common.import') : $t('common.upload') }}
            </div>
          </template>

          <q-list
            dense
            class="import-data-menu"
          >
            <q-item
              clickable
              v-close-popup
              :disable="importDataList.length === 0"
              @click="preview"
            >
              <q-item-section avatar>
                <q-icon
                  name="visibility"
                  color="primary"
                />
              </q-item-section>
              <q-item-section>
                <q-item-label>{{ $t('common.preview') }}</q-item-label>
              </q-item-section>
            </q-item>
            <q-item
              clickable
              v-close-popup
              :disable="importDataList.length === 0"
              @click="resetImportStatus"
            >
              <q-item-section avatar>
                <q-icon
                  name="clear_all"
                  color="red"
                />
              </q-item-section>
              <q-item-section>
                <q-item-label>{{ $t('common.clear') }}</q-item-label>
              </q-item-section>
            </q-item>
          </q-list>
        </q-btn-dropdown>
        <q-btn
          v-if="$q.screen.lt.sm"
          color="primary"
          size="sm"
          :label="isShowColShortcuts ? $t('common.hideColShortcuts') : $t('common.showColShortcuts')"
          @click="isShowColShortcutsClicked"
        />
        <!--          <q-space/>-->
      </div>

      <div class="col-xs-12 col-sm-5 q-mt-xs">
        <q-input
          v-model="filter"
          dense
          debounce="300"
          color="primary"
          :placeholder="$t('common.search')"
        >
          <template v-slot:append>
            <q-icon name="search" />
          </template>
        </q-input>
      </div>

      <!-- 对话框表单编辑数据，添加/更新 -->
      <q-dialog
        v-model="visible"
        persistent
        transition-show="slide-up"
        transition-hide="slide-down"
        ref="form"
        @hide="cancelEdit"
        class="z-top"
      >
        <q-card class="data-edit-card">
          <q-bar class="bg-primary text-white data-edit-bar">
            <q-toolbar-title>
              <h6 class="modal-header-title">
                <slot name="header">
                  <span>
                    {{ formTitle ? `${formTitle} / ` : '' }}
                    {{ $t(`common.${dataEditStatus[editStatus]}`) }}
                  </span>
                </slot>
              </h6>
            </q-toolbar-title>
            <q-space></q-space>
            <q-btn
              flat
              v-close-popup
              round
              dense
              icon="close"
            />
          </q-bar>

          <q-card-section class="data-edit-section">
            <q-form
              ref="editorForm"
              class="data-edit-section-form"
            >
              <slot name="form-content">
                No form to {{ $t(`common.${dataEditStatus[editStatus]}`) }}
              </slot>
            </q-form>
            <div class="data-edit-section-footer">
              <slot
                name="form-footer"
                :isLoading="isLoading"
                :confirm="confirmForm"
                :continueEdit="continueEdit"
                :editStatus="editStatus"
                :dataEditStatus="dataEditStatus"
              >
                <div class="col col-xs-12 text-center data-edit-section-btn">
                  <template v-if="editStatus === dataEditStatus.add">
                    <q-btn
                      class="q-px-lg q-mr-md w-32"
                      color="info"
                      :label="$t('form.keepAdding')"
                      size="md"
                      :loading="isLoading"
                      :disable="!canAdd || isLoading"
                      @click="continueEdit"
                    />
                    <q-btn
                      class="q-px-lg button-letter w-32"
                      color="primary"
                      :label="$t('common.confirm')"
                      size="md"
                      :loading="isLoading"
                      :disable="!canAdd || isLoading"
                      @click="confirmForm"
                    />
                  </template>
                  <q-btn
                    v-else
                    class="q-px-lg button-letter w-48"
                    color="primary"
                    :label="$t('common.confirm')"
                    size="md"
                    :disable="!canEdit || isLoading"
                    @click="confirmForm"
                  />
                </div>
              </slot>
            </div>
          </q-card-section>
        </q-card>
      </q-dialog>

      <!-- 导入预览 -->
      <q-dialog
        v-if="allowImport && previewIsLoaded"
        v-model="showPreview"
        persistent
        class="preview-dlg"
      >
        <q-card class="row column preview-card">
          <q-bar class="bys-move-header bg-primary text-white">
            <q-toolbar-title>
              <h6 class="modal-header-title">
                {{ formTitle ? `${formTitle} / ` : '' }}
                {{ $t('common.preview') }}
              </h6>
            </q-toolbar-title>
            <q-space></q-space>
            <q-btn
              v-close-popup
              flat
              round
              dense
              icon="close"
            />
          </q-bar>
          <q-card-section class="row items-center full-width full-height overflow-hidden q-pa-sm">
            <q-table
              class="preview-table overflow-auto"
              dense
              flat
              bordered
              virtual-scroll
              :virtual-scroll-sticky-size-start="0"
              :rows-per-page-options="[0]"
              v-model:pagination="previewPagination"
              :rows="importDataList"
              :columns="importDataColumns"
              separator="cell"
              :style="previewTableStyle"
              row-key="MarkerHWID"
            >
            <template v-slot:body="previewProps">
              <q-tr
              :props="previewProps"
              :key="previewProps.row.MarkerHWID"
            >
              <q-td
                v-for="(col, index) in previewProps.cols"
                :key="col.name"
                :props="previewProps"
              >
                <template v-if="index === 0">
                  <q-btn
                    v-if="renderPreviewChildRow"
                    size="xs"
                    color="accent"
                    round
                    dense
                    @click="previewProps.expand = !previewProps.expand"
                    :icon="previewProps.expand ? 'remove' : 'add'"
                    class="expand-btn q-mr-xs"
                    :key="previewProps.row.MarkerHWID"
                  />
                  <span
                    v-text="col.value"
                    class="index-value"
                  ></span>
                </template>
                <template v-else> {{ col.value }}</template>
              </q-td>
              </q-tr>
              <template v-if="renderPreviewChildRow">
                <q-tr
                  v-show="previewProps.expand"
                  :props="previewProps"
                  :key="`child_${previewProps.row.MarkerHWID}`"
                  class="q-virtual-scroll--with-prev"
                >
                  <q-td
                    colspan="100%"
                    class="text-left"
                  >
                    <rowChildVNode
                      :vnodes="renderPreviewChildRow(previewProps)"
                      v-if="previewProps.expand"
                    />
                  </q-td>
                </q-tr>
              </template>
            </template>
            </q-table>
          </q-card-section>
        </q-card>
      </q-dialog>

      <!--   <q-resize-observer debounce="300" @resize="onResize"/>-->
    </template>

    <template v-slot:header="props">
      <q-tr :props="props">
        <q-th
          v-for="col in props.cols"
          :key="col.name"
          :props="props"
          :class="{
            ['sticky-column-' + col.name]: col.sticky,
            ['no-left-border-' + col.name]: col.noLeftBorder
          }"
          :ref="$q.screen.xs && col.sticky ? 'stickyColumn' : ''"
        >
          {{ col.label }}
        </q-th>

        <!--最后以列的表头-->
        <q-th
          v-if="$q.screen.gt.xs || isShowColShortcuts"
          class="sticky-column-last"
        />
      </q-tr>
    </template>

    <template v-slot:body="props">
      <q-tr
        :props="props"
        :class="[
          { 'selected-row': props.rowIndex === rowIndex },
          typeof tbodyTrClasses === 'function' ? tbodyTrClasses(props.row) : tbodyTrClasses
        ]"
        :ref="'row' + props.rowIndex"
        @click="onRowClick(props)"
        :key="`${props.row.RID}`"
      >
        <q-td
          v-for="(col, index) in props.cols"
          :key="col.name"
          :props="props"
          :class="{
            ['sticky-column-' + col.name]: col.sticky,
            ['no-left-border-' + col.name]: col.noLeftBorder,
          }"
          :ref="$q.screen.xs && col.sticky ? 'stickyColumn' : ''"
        >
          <template v-if="index === 0">
            <q-btn
              v-if="renderChildRow"
              size="xs"
              color="accent"
              round
              dense
              @click="props.expand = !props.expand"
              :icon="props.expand ? 'remove' : 'add'"
              class="expand-btn q-mr-xs"
            />
            <span
              v-text="col.value"
              class="index-value"
            ></span>
          </template>
          <template v-else-if="$slots['my-body-cell-' + col.name]">
            <slot :name="'my-body-cell-' + col.name" v-bind="{...props, col, value: col.value}"></slot>
          </template>
          <template v-else> {{ col.value }}</template>
        </q-td>

        <!-- 最后一列的操作控件 -->
        <q-td
          auto-width
          class="sticky-column-last"
          v-if="$q.screen.gt.xs || isShowColShortcuts"
        >
          <slot
            name="data-actions"
            :props="props"
            :editDataRow="editDataRow"
            :deleteDataRow="deleteDataRow"
          >
            <q-btn
              class="q-ml-sm"
              color="primary"
              icon="create"
              size="sm"
              flat
              round
              dense
              :disable="!canEdit"
              @click="editDataRow(props.row)"
            >
              <q-tooltip>
                {{ $t('common.edit') }}
              </q-tooltip>
            </q-btn>
            <q-btn
              class="q-ml-sm"
              color="negative"
              icon="delete"
              size="sm"
              flat
              round
              dense
              :disable="!canDelete"
              @click="deleteDataRow(props.row)"
            >
              <q-tooltip>
                {{ $t('common.delete') }}
              </q-tooltip>
            </q-btn>
          </slot>
        </q-td>
      </q-tr>
      <template v-if="renderChildRow">
        <q-tr
          v-show="props.expand"
          :props="props"
          :key="`child_${props.row.RID}`"
          class="q-virtual-scroll--with-prev"
        >
          <q-td
            colspan="100%"
            class="text-left"
          >
            <rowChildVNode
              :vnodes="renderChildRow(props)"
              v-if="props.expand"
            />
          </q-td>
        </q-tr>
      </template>
    </template>
  </q-table>
</template>

<script lang="ts">
  import { PropType, defineComponent, defineAsyncComponent } from 'vue'
  import log from '@utils/log'
  import { exportData2Excel, unWrapExcelData } from '@utils/xlsx'
  import { cloneDeep } from 'lodash'
  import { dataEditStatus } from '@utils/common'
  import dayjs from 'dayjs'

  // 每页显示行数
  // const rowsPerPageOptions = [50, 100, 200, 500, 1000, 5000, 0]
  const rowsPerPageOptions = [0]
  const deleteWarning = defineAsyncComponent(() => import('@components/deleteWarning/DeleteWarning.vue'))

  export default defineComponent({
    name: 'DataEdit',
    props: {
      data: {
        type: Array,
        required: true,
      },
      columns: {
        type: Array,
        required: true,
      },
      visibleColumns: {
        type: Array,
      },
      sortBy: {
        type: String,
        default: '',
      },
      descending: {
        type: Boolean,
        default: false,
      },
      formTitle: {
        type: String,
        default: '',
      },
      renderChildRow: {
        type: Function,
      },
      renderPreviewChildRow: {
        type: Function,
      },
      onPermDBClick: {
        type: Function,
      },
      onPermCmdClick: {
        type: Function,
      },
      insert: {
        type: Function,
      },
      update: {
        type: Function,
      },
      delete: {
        type: Function,
      },
      importData: {
        type: Function,
        default() {
          return new Promise(() => {/**/ })
        },
      },
      allowImport: {
        type: Boolean,
        default: true,
      },
      currentRow: {
        type: Object,
        required: true,
      },
      canAdd: {
        type: Boolean,
        default: true,
      },
      canEdit: {
        type: Boolean,
        default: true,
      },
      canDelete: {
        type: Boolean,
        default: true,
      },
      filterColumns: {
        type: Array,
        required: false,
      },
      delWarnContent: {
        type: String,
        default: '',
      },
      /**
       * 导出表格数据的额外处理函数
       * @param {Object} source - 表格的一行源数据
       * @param {Object} _row - 完成部分处理的xlsx表格数据
       * @return {Object} 对源数据处理完后保存在_row并返回
       */
      extraExportFunc: {
        type: Function,
      },
      /**
       * 导入xlsx表格数据的额外处理
       * @param {Object} row - 完成一行xlsx表格数据转化为符合要求的格式
       * @param {Object} rowJsonData - 一行xlsx表格数据转换成的对象
       * @return {Object} 对item进行额外处理添加到row中并返回row
       */
      extraImportFunc: {
        type: Function,
      },
      tbodyTrClasses: {
        type: [String, Function] as PropType<string | ((row: Record<string, any>) => string)>,
        default: ''
      },
    },
    data() {
      return {
        isShowColShortcuts: false,
        rowIndex: 0,
        filter: '',
        pagination: {
          rowsPerPage: rowsPerPageOptions[0],
          sortBy: this.sortBy,
          descending: this.descending,
        },
        visible: false,
        editStatus: dataEditStatus.show,
        isLoading: false,
        isKeepAdding: false,

        // 标记是否加载预览组件
        previewIsLoaded: false,
        showPreview: false,
        importDataList: [],
        previewPagination: {
          rowsPerPage: 0,
        },
        loading: false,
        progressValue: 0,
      }
    },
    computed: {
      dataEditStatus() {
        return dataEditStatus
      },
      rowsPerPageOptions() {
        return rowsPerPageOptions
      },
      wrapperData() {
        return cloneDeep(this.data).map((data, index) => {
          data.$index = index + 1
          return data
        })
      },
      wrapperColumns() {
        if (this.$q.screen.gt.xs) {
          return [
            { name: 'Index', field: '$index', label: '#', align: 'left' },
          ].concat(this.columns)
        }
        return this.columns
      },
      wrapVisibleColumns() {
        if (this.visibleColumns) {
          return this.visibleColumns
        }
        return this.wrapperColumns.map(col => col.name)
      },
      importDataColumns() {
        // 预览表格不需要进行格式化，需要删除
        return cloneDeep(this.columns)
          .map(item => {
            if (item.format) {
              delete item.format
            }
            return item
          })
      },
      previewTableStyle() {
        return {
          maxHeight: '75vh',
        }
      },
      importFileAccept() {
        return '.xlsx,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      },
      // userRoles():userPermission.IDbUserRole{
      //   return this.$
      // }
    },
    methods: {
      isShowColShortcutsClicked() {
        this.isShowColShortcuts = !this.isShowColShortcuts
      },
      clickFirstRowOnMounted() {
        // 首行未加载，使用requestAnimationFrame重复执行，直到首行加载完成后，触发首行点击事件
        if (!this.$refs.row0) {
          return window.requestAnimationFrame(this.clickFirstRowOnMounted)
        }
        this.$refs.row0?.$el.click()
      },
      resetRowClick() {
        //当用户点击 确认/取消 按钮的时候默认清除之前的保留值
      },
      onRowClick(props) {
        //修改样式(被选中)
        this.rowIndex = props.rowIndex
        //将点击行的相关数据传给父组件，在父组件生成新的DataEdit表单的时候自动填充某些项目
        this.$emit('rwo-clicked', props.row)
      },
      // onResize() {
      //   // 找到所有需要固定列
      //   const stickyColumns = this.wrapperColumns.filter(col => col.sticky)
      //   let left = 0
      //   for (let i = 0; i < stickyColumns.length; i++) {
      //     // 找到固定列对应的元素
      //     const col = stickyColumns[i]
      //     const columns = this.$el.querySelectorAll(`.sticky-column-${col.name}`)
      //     if (!columns || columns.length === 0) {
      //       continue
      //     }
      //
      //     columns.forEach(ref => {
      //       // 设置元素定位的left值
      //       ref.style.left = left + (left === 0? '': 'px')
      //     })
      //
      //     // 计算下一列的left值
      //     left += columns[0].offsetWidth || 0
      //   }
      // },

      // find index in data
      findDataIndex(rid: string): number {
        return this.data.findIndex(item => item.RID === rid)
      },
      // cancel data editing
      cancelEdit() {
        this.$emit('hide')
        this.afterAction()
      },
      afterAction() {
        this.isKeepAdding = false
        this.isLoading = false
        this.visible = false
        this.editStatus = dataEditStatus.show
      },
      addToDatabase(): Promise<boolean> {
        return this.insert(this.currentRow)
          .then(() => {
            this.$q.notify({
              message: this.$t('message.addSuccess') as string,
              color: 'positive',
              icon: 'check_circle',
              position: 'top',
            })

            if (this.isKeepAdding) {
              this.isLoading = false
            } else {
              this.afterAction()
            }
            return true
          })
          .catch((reason: string) => {
            this.isLoading = false
            this.$q.notify({
              message: reason,
              color: 'red',
              icon: 'error',
              position: 'top',
            })
          })
      },
      updateDatabase(): Promise<boolean> {
        return this.update(this.currentRow)
          .then(() => {
            this.$q.notify({
              message: this.$t('message.updateSuccess') as string,
              color: 'positive',
              icon: 'check_circle',
              position: 'top',
            })
            this.afterAction()
            return true
          })
          .catch((reason: string) => {
            this.isLoading = false
            if (['-400'].includes(reason)) {
              return
            }
            this.$q.notify({
              message: reason,
              color: 'red',
              icon: 'error',
              position: 'top',
            })
          })
      },
      confirmEdit(): Promise<boolean> {
        this.isLoading = true
        if (this.editStatus === dataEditStatus.add) {
          return this.addToDatabase()
        }

        if (this.editStatus === dataEditStatus.edit) {
          return this.updateDatabase()
        }
        return Promise.resolve(false)
      },
      confirmForm() {
        this.isKeepAdding = false
        this.$refs.editorForm
          .validate()
          .then((valid: boolean) => {
            valid && this.confirmEdit()
          })
        //清除之前保留的连续填写表单的复用项
        this.$emit('clear-multiplex-item')
      },
      newRow() {
        // reset form validator
        this.$refs.editorForm.resetValidation()
        //复用表单项
        this.$emit('save-multiplex-item')
        // next data to add
        this.emitNewRow()
      },
      continueEdit() {
        this.isKeepAdding = true
        this.$refs.editorForm
          .validate()
          .then((valid: boolean) => {
            if (valid) {
              return this.confirmEdit()
            }
            return false
          })
          .then((isNext: boolean) => {
            if (isNext) {
              this.newRow()
            }
          })
      },
      emitNewRow() {
        this.syncUpdateRowData()
        this.$emit('new-row')
      },
      newDataRow() {
        this.editStatus = dataEditStatus.add
        this.visible = true
        this.emitNewRow()
      },
      syncCurrentRow(row: { [key: string]: any }) {
        // 同步当前编辑的数据时，把原型链上的默认值拷贝到编辑的对象上
        const data = { ...row }
        // 由服务器更新的字段
        const excludeProps = ['UpdatedAt', 'UpdatedDC']
        for (let k in row) {
          if ((k in data) || excludeProps.includes(k)) {
            continue
          }
          data[k] = row[k]
        }
        this.$emit('update:currentRow', data)
      },
      syncUpdateRowData(status = false) {
        this.$emit('update:isUpdateRowData', status)
      },
      editDataRow(row: { [key: string]: any }) {
        this.editStatus = dataEditStatus.edit
        this.visible = true
        this.syncCurrentRow(row)
        this.syncUpdateRowData(true)
      },
      // delete one row data
      confirmDelete(row: { [key: string]: any }) {
        const index = this.findDataIndex(row.RID)
        if (index === -1) {
          return
        }
        this.clickFirstRowOnMounted()
        this.delete(row)
          .then(() => {
            this.$q.notify({
              message: this.$t('message.deleteSuccess') as string,
              color: 'positive',
              icon: 'check_circle',
              position: 'top',
            })
            this.afterAction()
          })
          .catch((reason: string) => {
            this.isLoading = false
            this.$q.notify({
              message: reason,
              color: 'red',
              icon: 'error',
              position: 'top',
            })
          })
      },
      deleteDataRow(row: { [key: string]: any }) {
        // this.syncCurrentRow(row)
        this.$q.dialog({
          component: deleteWarning,
          componentProps: {
            content: this.delWarnContent,
          },
        }).onOk(() => {
          this.confirmDelete(row)
        }).onCancel(() => {
          this.afterAction()
        })
      },
      // import data from xlsx
      resetCheckFile() {
        // 重置input内容，以便下次能选择重复的文件
        this.$refs.checkFile.$el.querySelector('input').value = ''
      },
      onClickImport() {
        if (this.importDataList.length === 0) {
          this.$refs.checkFile.$el.click()
        } else {
          this.confirmImportData()
        }
      },
      preview() {
        // 进行预览，确定后再添加到数据库中
        if (!this.previewIsLoaded) {
          this.previewIsLoaded = true
        }
        this.showPreview = true
      },
      confirmImportData() {
        this.loading = true
        // 再调用父组件传递处理数据的方法，逐个数据添加到数据库中
        this.importData(this.importDataList, (value: number) => {
          this.progressValue = +(value / this.importDataList.length).toFixed(1) * 100
        })
          .then((errDataList: any[]) => {
            this.resetImportStatus()
            if (errDataList.length === 0) {
              return this.$q.notify({
                message: this.$t('message.importSuccess') as string,
                type: 'positive',
                icon: 'cloud_done',
                position: 'top',
              })
            }
            // 提示用户是否需要下载异常的数据，提示后才能下载
            this.$q.dialog({
              html: true,
              message: `<div class="q-avatar" style="font-size: 38px;">
                          <div class="q-avatar__content row flex-center overflow-hidden bg-warning">
                            <i aria-hidden="true" role="presentation" class="material-icons q-icon notranslate">
                              warning
                            </i>
                          </div>
                         </div>
                         <span class="q-ml-sm">${this.$t('message.importDataHasError')}</span>`,
              class: 'custom-dialog-plugin z-top',
              persistent: true,
              ok: {
                color: 'secondary',
                dense: true,
              },
              cancel: {
                flat: true,
                dense: true,
                color: 'negative',
              },
            }).onOk(() => {
              const columns = this.importDataColumns.concat(
                [{ name: 'Info', field: '$info', label: this.$t('import.errorInfo') }],
              )
              const fileName = `${this.formTitle}-${this.$t('common.abnormalData')}`
              exportData2Excel({ fileName, data: errDataList, columns: columns })
            })
          })
      },
      resetImportStatus() {
        this.loading = false
        this.importDataList = []
        this.resetCheckFile()
      },
      importFile(files: FileList) {
        if (files.length === 0) {
          return
        }
        const file: File = files[0]
        const Reader: FileReader = new FileReader()
        Reader.onload = () => {
          try {
            this.importDataList = unWrapExcelData(Reader.result as ArrayBuffer, this.columns, this.extraImportFunc)
          } catch (e) {
            log.error('importData error:', e)
          }
        }
        Reader.readAsArrayBuffer(file)
      },
      // export data to csv
      exportTable() {
        const fileName = `${this.formTitle}-${dayjs().format('YYYYMMDD-HHmmss')}`
        exportData2Excel({ fileName, data: this.wrapperData, columns: this.wrapperColumns }, this.extraExportFunc)
      },
      async validate() {
        const valid: boolean = await this.$refs.editorForm.validate()
        if (valid) {
          return Promise.resolve()
        }

        return Promise.reject()
      },
      filterMethod(rows: any[], terms: string | Object, cols: any[], getCellValue) {
        const ret: Array<any> = []
        let filterColumns: Array<any> = []

        if ((this.filterColumns?.length ?? 0) === 0) {
          //use default filter method
          filterColumns.push(...cols)
        } else {
          filterColumns = cols.filter(col => this.filterColumns.includes(col.name))
        }

        log.info('_filterColumns', filterColumns)
        for (let row of rows) {
          for (let col of filterColumns) {
            if ((getCellValue(col, row) + '').includes(this.filter)) {
              ret.push(row)
              break
            }
          }
        }
        return ret
      },
    },
    components: {
      // 渲染子行数据，支持VNode,String
      rowChildVNode: {
        functional: true,
        render: (h) => h.$attrs.vnodes,
      },
    },
    mounted() {
      this.$nextTick(() => {
        this.clickFirstRowOnMounted()
        // const ev = throttle(this.tableScrollChanged, 150)
      })
    },
  })
</script>

<style lang="scss">
  @import "quasar/src/css/core/visibility";

  .data-edit-card {
    display: flex;
    flex-direction: column;

    .data-edit-bar {
      flex: none;
    }

    .data-edit-section {
      overflow: auto;
      padding: 16px 8px;

      .data-edit-section-form {
        flex: auto;
        // overflow: auto !important;
      }

      .data-edit-section-footer {
        flex: none;
        margin-top: 10px;
      }
    }
  }

  .data-edit-card,
  .data-edit-card .data-edit-section {
    width: fit-content !important;
    max-width: unset !important;
  }

  .data-edit-section,
  .marker-edit-dlg-cls,
  .controller-edit-dlg-cls {
    .q-field--with-bottom {
      padding-bottom: 12px;

      .q-field__bottom {
        padding-top: 2px;
      }
    }
  }

  $stickyBgColor: #fff;

  .sticky-header-column-virtscroll-table {
    /* height or max-height is important */
    height: 410px;
    max-width: 100vw;

    &.q-table--dense .q-table {

      th,
      td {
        padding: 2px 4px;
      }
    }

    [class*="sticky-column-"] {
      position: sticky;
      z-index: 1;
      background-color: $stickyBgColor;
      border-right: 1px solid #e3e3e3;
    }

    [class*="no-left-border-"] {
      border-left: none;
    }

    thead {
      tr th {
        position: sticky;
        z-index: 1;
        top: 0;
        background-color: $stickyBgColor;
      }

      tr th[class *="sticky-column-"] {
        z-index: 3;
        left: 0;
      }

      .sticky-column-last {
        position: sticky !important;
        background-color: $stickyBgColor;
        right: 0 !important;
        z-index: 3 !important;
      }
    }

    tbody {
      tr td[class*="sticky-column-"] {
        position: sticky;
        z-index: 2;
        left: 0;
      }

      .sticky-column-last {
        position: sticky !important;
        background-color: $stickyBgColor;
        right: 0 !important;
        z-index: 2 !important;
      }
    }

    tr.selected-row {
      background-color: $blue-3;

      td[class*="sticky-column-"] {
        background-color: $blue-3;
      }
    }

    .q-virtual-scroll__content {
      tr:last-child td {
        border-bottom: 1px solid rgba(0, 0, 0, 0.12);
      }
    }

    &.q-table__container>.q-inner-loading {
      z-index: 10;
    }

    .q-virtual-scroll__content {
      tr td {
        line-height: 28px;

        &.sticky-column-last {
          height: 28px;
        }
      }
    }

    .q-btn.data-action-btn.disabled {
      color: #171616 !important;

      .q-icon {
        color: #171616 !important;
      }
    }

    .q-table__top,
    .q-table__bottom {
      flex: none;
    }
  }

  .bys-move-layout.is-mobile {
    .sticky-header-column-virtscroll-table {
      thead {
        tr th[class *="sticky-column-"] {
          right: unset;
          z-index: 1;
        }
      }

      tbody {

        tr td[class*="sticky-column-"],
        tr td:first-child {
          position: unset;
        }
      }
    }
  }

  .import-data-menu {
    .q-item__section--avatar {
      min-width: unset;
    }
  }

  .preview-card {
    .bys-move-header {
      flex: none;
    }
  }

  @media (min-width: 600px) {
    .preview-card.q-card {
      max-width: 60vw;
    }
  }

  .preview-table {
    .q-table {
      thead {
        tr th {
          position: sticky;
          background-color: $stickyBgColor;
          // 表格头部数据区的粘贴层叠要比内容区的高
          z-index: 1;
          top: 0;
        }
      }
    }
  }

  .q-dialog-plugin.custom-dialog-plugin {
    width: unset;

    .q-card__actions {
      justify-content: center;
    }
  }

  .description {
    max-width: 120px;
    word-break: keep-all;
    overflow: hidden;
    white-space: normal;
    text-overflow: ellipsis;
  }

  .q-dialog.fullscreen.preview-dlg {
    z-index: 7000 !important;
  }
</style>

<template>
  <q-tab-panels
    v-model="panel"
    keep-alive
    animated
    :swipeable="false"
    class="history-panel"
  >
    <q-tab-panel name="form">
      <q-form
        class="query-form"
        @submit="onSubmit"
      >
        <q-input
          v-model="startTime"
          :label="$t('form.startTime')"
          outlined
          dense
          ref="start-time"
          :rules="rules.startTime"
        >
          <template v-slot:append>
            <q-icon
              name="event"
              class="cursor-pointer"
            >
              <q-popup-proxy
                ref="startDateProxy"
                transition-show="scale"
                transition-hide="scale"
              >
                <q-date
                  v-model="startTime"
                  :mask="timeMask"
                />
              </q-popup-proxy>
            </q-icon>
            <q-icon
              v-if="noTimeIcon"
              name="access_time"
              class="cursor-pointer"
            >
              <q-popup-proxy
                ref="startTimeProxy"
                transition-show="scale"
                transition-hide="scale"
              >
                <q-time
                  v-model="startTime"
                  :mask="timeMask"
                  format24h
                />
              </q-popup-proxy>
            </q-icon>
          </template>
        </q-input>
        <q-input
          v-model="endTime"
          :label="$t('form.endTime')"
          outlined
          dense
          ref="end-time"
          :rules="rules.endTime"
        >
          <template v-slot:append>
            <q-icon
              name="event"
              class="cursor-pointer"
            >
              <q-popup-proxy
                ref="endDateProxy"
                transition-show="scale"
                transition-hide="scale"
              >
                <q-date
                  v-model="endTime"
                  :mask="timeMask"
                />
              </q-popup-proxy>
            </q-icon>
            <q-icon
              v-if="noTimeIcon"
              name="access_time"
              class="cursor-pointer"
            >
              <q-popup-proxy
                ref="endTimeProxy"
                transition-show="scale"
                transition-hide="scale"
              >
                <q-time
                  v-model="endTime"
                  :mask="timeMask"
                  format24h
                />
              </q-popup-proxy>
            </q-icon>
          </template>
        </q-input>
        <slot name="form"/>
        <div class="form-actions text-center">
          <q-btn
            class="q-px-md button-letter min-w-48"
            :label="$t('form.query')"
            type="submit"
            color="primary"
            :disable="!canQuery"
          >
          </q-btn>
        </div>
      </q-form>
    </q-tab-panel>
    <q-tab-panel name="table">
      <q-table
        :rows="queryRet"
        :columns="wrapperColumns"
        bordered
        flat
        dense
        class="history-table full-height full-width"
        virtual-scroll
        :filter-method='filterMethod'
        v-model:pagination="pagination"
        :rows-per-page-options="rowsPerPageOptions"
        :virtual-scroll-sticky-size-start="0"
        :row-key="rowKey"
        :filter="filter"
        @update:pagination="updatePagination"
        separator="cell"
        ref="qTable"
      >
        <template v-slot:top>
          <q-btn
            color="primary"
            dense
            size="md"
            :label="$t('form.backQueryPage')"
            @click="back2PrePanel"
          />
          <q-btn
            class="q-ml-sm"
            dense
            size="md"
            color="primary"
            :label="$t('common.export')"
            :disable="!queryRet.length"
            @click="exportTable"
          />
          <slot name="top-actions"></slot>
          <q-space/>
          <q-input
            v-model="filter"
            dense
            debounce="300"
            color="primary"
            :placeholder="$t('common.search')"
          >
            <template v-slot:append>
              <q-icon name="search"/>
            </template>
          </q-input>
        </template>

        <template v-slot:header="props">
          <q-tr :props="props">
            <q-th
              v-for="col in props.cols"
              :key="col.name"
              :class="{
                  ['sticky-column-' + col.name]: col.sticky,
                  ['no-left-border-' + col.name]: col.noLeftBorder
                }"
              :props="props"
              :ref="$q.screen.xs && col.sticky ? 'stickyColumn' : ''"
            >
              {{ col.label }}
            </q-th>
          </q-tr>
        </template>

        <template v-slot:body="props">
          <q-tr
            :props="props"
            :class="[
                { 'selected-row': props.rowIndex === rowIndex},
              ]"
            :key="props.rowIndex"
            @click="onRowClick(props)"
          >
            <q-td
              v-for="(col, index) in props.cols"
              :key="col.name"
              :props="props"
              :class="{
                  ['sticky-column-' + col.name]: col.sticky ,
                  ['no-left-border-' + col.name]: col.noLeftBorder
                }"
              :ref="$q.screen.xs && col.sticky ? 'stickyColumn' : ''"
            >
              <slot :name="'my-body-cell-' + col.name" v-bind="{...props, col, value: col.value}">
                <template v-if="index === 0">
                  <q-btn
                    v-if="renderChildRow"
                    size="xs"
                    color="accent"
                    round
                    dense
                    @click="props.expand = !props.expand"
                    :icon="props.expand ? 'remove' : 'add'"
                    class="expand-btn q-mr-xs"
                  />
                  <span
                     v-text="col.value ?? props.rowIndex + 1"
                    class="index-value"
                  ></span>
                </template>
                <template v-else> {{ col.value }}</template>
              </slot>
              <!--                <template v-if="index === 0">-->
              <!--                  {{ props.rowIndex + 1 }}-->
              <!--                </template>-->
              <!--                <template v-else> {{ col.value }}</template>-->
              <!--                <slot name="status" :row="props.row" :col="col"></slot>-->
            </q-td>
          </q-tr>
          <template v-if="renderChildRow">
            <q-tr
              v-show="props.expand"
              :props="props"
              :key="`child_${props.row.RID}`"
              class="q-virtual-scroll--with-prev"
            >
              <q-td
                colspan="100%"
                class="text-left"
              >
                <rowChildVNode
                  :vnodes="renderChildRow(props)"
                  v-if="props.expand"
                />
              </q-td>
            </q-tr>
          </template>
        </template>
      </q-table>
    </q-tab-panel>
  </q-tab-panels>
</template>

<script lang="ts">
  import { defineComponent } from 'vue'
  import dayjs from 'dayjs'
  import { required } from '@src/utils/validation'
  import { exportData2Excel, exportData2ExcelV2 } from '@utils/xlsx'
  import { crud } from '@ygen/crud'
  import { toUtcTime } from '@utils/dayjs'
  import { PropType } from 'vue'
  import { ExtendsQTableColumn } from '@app/src/utils/bysdb.type'

  const mask = 'YYYY-MM-DD HH:mm'
  // 每页显示行数
  const rowsPerPageOptions = [0]

  export default defineComponent({
    name: 'QueryHistory',
    props: {
      timeMask: {
        type: String,
        default: mask,
      },
      //默认起止时间差
      defaultTimeDiff: {
        type: Object,
        default() {
          return {
            value: 7,
            uint: 'day',
          }
        },
      },
      timeMaxRange: {
        type: Object,
        default() {
          return {
            value: 1,
            uint: 'month',
            text: '',
          }
        },
      },
      tableColumns: {
        type: Array as PropType<NonNullable<ExtendsQTableColumn>>,
        default() {
          return []
        },
      },
      filterColumns: {
        type: Array,
        default() {
          return []
        },
      },
      //数据库中时间戳的列名
      timeLimitSqlName: {
        type: String,
        default: 'UpdatedAt',
      },
      exportName: {
        type: String,
        default: 'export-',
      },
      canQuery: {
        type: Boolean,
        required: true,
      },
      /**
       * 导出表格数据的额外处理函数
       * @param {Object} source - 表格的一行源数据
       * @param {Object} _row - 完成部分处理的xlsx表格数据
       * @return {Object} 对源数据处理完后保存在_row并返回
       */
      extraExportFunc: {
        type: Function,
      },
      renderChildRow: {
        type: Function,
      },
      noTimeIcon: {
        type: Boolean,
        default: true,
      },
      rowKey: {
        type: [String, Function],
        default: 'RID',
      },
      childColumns: {
        type: Array as PropType<NonNullable<ExtendsQTableColumn>>,
        default() {
          return []
        },
      },
      exportExcelV2: {
        type: Boolean,
        default: false
      },
      childTableKey: {
        type: String,
      }
    },
    components: {
      // 渲染子行数据，支持VNode,String
      rowChildVNode: {
        functional: true,
        render: (h) => h.$attrs.vnodes,
      },
    },
    data() {
      return {
        panel: 'form',
        // 默认查询一周的数据
        startTime: dayjs(new Date())
          .subtract(this.defaultTimeDiff.value, this.defaultTimeDiff.uint).format(this.timeMask),
        endTime: dayjs(new Date()).format(this.timeMask),

        //查询的结果集
        queryRet: [],
        columns: this.tableColumns as NonNullable<ExtendsQTableColumn>,
        pagination: {
          rowsPerPage: rowsPerPageOptions[0],
        },
        filter: '',
        rowIndex: 0,
      }
    },
    computed: {
      rules(): { [key: string]: any[] } {
        return {
          startTime: [
            val => required(val) || this.$t('rules.required'),
            val => dayjs(val).isValid() || this.$t('rules.invalidDateTime'),
            val => dayjs(val).isBefore(this.endTime) || this.$t('rules.cannotLaterEndTime'),
            val => this.validTimeDiff(val, this.endTime),
          ],
          endTime: [
            val => required(val) || this.$t('rules.required'),
            val => dayjs(val).isValid() || this.$t('rules.invalidDateTime'),
            val => dayjs(this.startTime).isBefore(val) || this.$t('rules.cannotEarlierStartTime'),
            val => this.validTimeDiff(this.startTime, val),
          ],
        }
      },
      wrapperColumns(): NonNullable<ExtendsQTableColumn> {
        return [
          { name: 'Index', field: '$index', label: '#', align: 'left', style: 'width: 70px' },
        ].concat(this.columns)
      },
      rowsPerPageOptions() {
        return rowsPerPageOptions
      },

    },
    methods: {
      updatePagination(newPagination) {
        this.$emit('update:pagination', newPagination)
      },
      //点击行事件
      onRowClick(props) {
        //修改样式(被选中)
        this.rowIndex = props.rowIndex
      },
      filterMethod(rows: readonly any[], terms: string | any, cols: readonly any[], getCellValue: (col: any, row: any) => any): readonly any[] {
        const ret: Array<any> = []
        let _filterColumns: Array<any> = [...cols]
        if (this.filterColumns.length) {
          _filterColumns = cols.filter(col => this.filterColumns!.includes(col.name))
        }

        for (let row of rows) {
          for (let col of _filterColumns) {
            if ((getCellValue(col, row) + '').includes(this.filter)) {
              ret.push(row)
              break
            }
          }
        }

        return ret
      },
      inputChanged() {
        this.$refs['start-time']?.validate()
        this.$refs['end-time']?.validate()
      },
      onSubmit() {
        this.queryRet = []
        this.panel = 'table'
        const where: crud.IWhereItem[] = [
          {
            Field: this.timeLimitSqlName,
            FieldValue: toUtcTime(this.startTime),
            FieldCompareOperator: '>=',
          },
          {
            Field: this.timeLimitSqlName,
            FieldValue: toUtcTime(this.endTime),
            FieldCompareOperator: '<=',
          },
        ]
        this.$emit('submit-clicked', where)
      },
      //接口  生成需要数据库返回时转换的时间参数的param，
      // eg:['ActionTime','480'，‘RecvTime','-60']
      createTimeColumns(): string[] {
        const timezoneOffset = -1 * new Date().getTimezoneOffset() + ''
        const TimeColumn: string[] = []
        TimeColumn.push(this.timeLimitSqlName)
        TimeColumn.push(timezoneOffset)
        return TimeColumn
      },
      back2PrePanel() {
        this.queryRet = []
        //返回查询界面
        this.panel = 'form'
        //取消stream
        this.$emit('query-canceled')
      },
      exportTable() {
        const fileName = `${this.exportName}-${dayjs().format('YYYYMMDD-HHmmss')}`
        if (this.exportExcelV2) {
          exportData2ExcelV2({
            fileName,
            data: this.queryRet,
            columns: this.wrapperColumns,
            childKey: this.childTableKey,
            childColumns: this.childColumns
          })
          return
        }
        exportData2Excel({ fileName, data: this.queryRet, columns: this.wrapperColumns }, this.extraExportFunc)
      },

      validTimeDiff(start, end) {
        return dayjs(end).diff(start, this.timeMaxRange.uint, true) <= this.timeMaxRange.value ||
          this.timeMaxRange.text
      },

    },
    watch: {
      startTime: function () {
        this.inputChanged()
      },
      endTime: function () {
        this.inputChanged()
      },
    },
  })
</script>

<style lang="scss">
  $stickyBgColor: #fff;

  .history-panel {
    height: 100%;

    .q-tab-panel {
      padding: unset;
    }

    .history-table {
      & > .q-table__top {
        flex: none;
      }

      &.q-table--dense .q-table {
        thead tr th {
          position: sticky;
          z-index: 1;
          background-color: $stickyBgColor;
        }

        thead tr:first-child th {
          top: 0;
        }

        th,
        td {
          padding: 2px 4px;
        }
      }

      tr.selected-row {
        background-color: $blue-3;

        td[class*="sticky-column-"] {
          background-color: $blue-3;
        }
      }

      [class*="sticky-column-"] {
        position: sticky;
        z-index: 1;
        background-color: $stickyBgColor;
        border-right: 1px solid #e3e3e3;
      }

      [class*="no-left-border-"] {
        border-left: none;
      }

      &.q-table--dense .q-table__middle table thead tr th[class *="sticky-column-"] {
        z-index: 3;
        left: 0;
      }

      tbody {
        tr td[class*="sticky-column-"] {
          position: sticky;
          z-index: 2;
          left: 0;
        }
      }

      tr.selected-row {
        background-color: $blue-3;

        td[class*="sticky-column-"] {
          background-color: $blue-3;
        }
      }

      .q-virtual-scroll__content {
        tr:last-child td {
          border-bottom: 1px solid rgba(0, 0, 0, 0.12);
        }
      }
    }
  }

  .bys-move-layout.is-mobile {
    .history-table {
      thead {
        tr th[class *="sticky-column-"] {
          right: unset;
          z-index: 1;
        }
      }

      tbody {

        tr td[class*="sticky-column-"],
        tr td:first-child {
          position: unset;
        }
      }
    }
  }
</style>

<template>
  <div
    class="q-ml-sm"
    :class="$q.platform.is.android ? '' : 'controller-edit-content'"
  >
    <div class="fit row wrap z-top q-mb-sm q-col-gutter-x-sm">
      <div class="col col-xs-12 col-sm-6 col-md-4">
        <q-input
          v-model="currentRow.ControllerNo"
          :label="$t('form.customID')"
          outlined
          dense
          clearable
          autofocus
          lazy-rules
          :rules="rules.ControllerNo"
          :maxlength="16"
        />
      </div>
      <div class="col col-xs-12 col-sm-6 col-md-4">
        <q-select
          v-model="currentRow.OrgRID"
          :label="$t('form.unit')"
          outlined
          dense
          clearable
          lazy-rules
          :rules="rules.OrgRID"
          :options="parentOptions"
          @filter="filterParent"
          options-dense
          map-options
          emit-value
        />
      </div>
      <div class="col col-xs-12 col-sm-6 col-md-4">
        <q-select
          v-model="currentRow.ControllerType"
          :label="$t('form.type')"
          outlined
          dense
          lazy-rules
          :rules="rules.ControllerType"
          :options="ControllerTypeOptions"
          options-dense
          map-options
          emit-value
          @update:model-value="controllerTypeInput"
        />
      </div>
      <div class="col col-xs-12 col-sm-6 col-md-4">
        <q-input
          v-model.number="currentRow.ControllerHWID"
          type="number"
          :label="$t('form.controllerHWID')"
          outlined
          dense
          clearable
          :rules="rules.ControllerHWID"
          :maxlength="16"
        />
      </div>
      <div class="col col-xs-12 col-sm-6 col-md-4">
        <q-select
          v-model="currentRow.ParentRID"
          :label="$t('form.parentController')"
          outlined
          dense
          clearable
          :disable='!isRepeater'
          lazy-rules
          :rules="rules.ParentRID"
          :options="ControllerRIDOptions"
          @filter="filterControllerRID"
          options-dense
          map-options
          emit-value
          use-input
          ref="parentRID"
          @update:model-value="changeParentController"
        />
      </div>
      <div class="col col-xs-12 col-sm-6 col-md-4 pb-[12px]">
        <q-select
          v-show="currentRow.ControllerType === 2"
          v-model="currentRow.ChannelCount"
          :label="$t('form.availableChannels')"
          outlined
          dense
          lazy-rules
          :rules="rules.ChannelCount"
          :options="availableChannelsOptions"
          :disable="isRepeater"
          options-dense
          map-options
          emit-value
        />
        <q-select
          v-show="currentRow.ControllerType === 1"
          v-model="currentRow.ParentChannelNo"
          :label="$t('form.parentChannelCount')"
          outlined
          dense
          lazy-rules
          :rules="rules.ParentChannelCount"
          :options="availableParentChannelsOptions"
          options-dense
          map-options
          emit-value
          ref="ParentChannelNo"
        />
      </div>
      <div class="col col-xs-12 col-sm-6 col-md-4">
        <q-input
          v-model.number="Lon"
          :label="$t('form.lon')"
          outlined
          dense
          clearable
          lazy-rules
          :rules="rules.Lon"
          :maxlength="16"
        >
          <template v-slot:append>
            <q-btn
              round
              dense
              flat
              icon="place"
              @click="lonLatVisible = true"
            ></q-btn>
          </template>
        </q-input>
      </div>
      <div class="col col-xs-12 col-sm-6 col-md-4">
        <q-input
          v-model.number="Lat"
          :label="$t('form.lat')"
          outlined
          dense
          clearable
          lazy-rules
          :rules="rules.Lat"
          :maxlength="16"
        >
          <template v-slot:append>
            <q-btn
              round
              dense
              flat
              icon="place"
              @click="lonLatVisible = true"
            ></q-btn>
          </template>
        </q-input>
      </div>
      <div class="col col-xs-12 col-sm-6 col-md-4">
        <q-select
          v-model="currentRow.DefaultNetworkType"
          :label="$t('form.defaultNetworkType')"
          outlined
          dense
          lazy-rules
          :rules="rules.DefaultNetworkType"
          :options="NetworkType"
          options-dense
          map-options
          emit-value
          class="mb-3"
        />
      </div>
      <div class="col col-xs-12 col-sm-6 col-md-4">
        <q-input
          v-model.number="currentRow.MapShowLevel"
          type="number"
          :label="$t('form.mapShowLevel')"
          outlined
          dense
          clearable
          lazy-rules
          :rules="rules.MapShowLevel"
        />
      </div>
      <div class="col col-12">
        <q-input
          v-model="currentRow.ControllerDescription"
          type="textarea"
          :label="$t('form.description')"
          outlined
          dense
          clearable
          autogrow
          lazy-rules
          :rules="rules.ControllerDescription"
          :maxlength="4096"
        />
      </div>
    </div>
    <LonLat
      v-if="lonLatVisible"
      :modelValue="currentRow.GCJ02LngLat"
      :isMarker="false"
      :data="currentRow"
      v-model:visible="lonLatVisible"
      @update:model-value="getLngLatFromMap"
    ></LonLat>
  </div>
</template>

<script lang="ts">
  import { defineAsyncComponent, defineComponent } from 'vue'
  import dataEditMixin from '@utils/mixins/editor'
  import controllerMixin from '@utils/mixins/controller'
  import { ControllerTypes } from '@utils/common'
  import { TLngLat } from '@utils/map'
  import { ControllerAndUpdateCmd } from '@utils/bysdb.type'
  import { setGCJ02LngLat, setLngLat, toWGS84 } from '@utils/gcoord'

  export default defineComponent({
    name: 'ControllerEdit',
    mixins: [dataEditMixin, controllerMixin],
    props: {
      modelValue: {
        type: Object,
        required: true,
      },
    },
    data() {
      return {
        currentRow: {} as ControllerAndUpdateCmd,
        controllerFilter: '',
        maxChannelNumber: 5,
        lonLatVisible: false,
      }
    },
    computed: {
      Lon: {
        get() {
          return this.currentRow.Lon ?? ''
        },
        set(val) {
          this.currentRow.Lon = val
        },
      },
      Lat: {
        get() {
          return this.currentRow.Lat ?? ''
        },
        set(val) {
          this.currentRow.Lat = val
        },
      },
      ControllerRIDOptions() {
        return this.AllControllerOptions
          .filter((option: { [key: string]: any }) => {
            // 过滤中继类型，只能是基站控制器
            return option.ControllerType === ControllerTypes.baseController
          })
          .filter(option => {
            const needle = this.controllerFilter.toLowerCase()
            return option.label.toLowerCase().includes(needle)
          })
      },
      availableChannelsOptions() {
        const options: Array<{ [key: string]: any }> = []
        for (let i = 1; i <= this.maxChannelNumber; i++) {
          options.push({ label: i, value: i })
        }
        return options
      },
      availableParentChannelsOptions() {
        const maxLen = this.availableParentChannelCounts
        const options: Array<{ [key: string]: any }> = []
        for (let i = 1; i <= maxLen; i++) {
          options.push({ label: i, value: i })
        }
        return options
      },
      isRepeater() {
        return this.currentRow.ControllerType === 1
      },
    },
    methods: {
      controllerTypeInput() {
        if (this.isRepeater) {
          this.currentRow.ChannelCount = 1
        } else {
          // 重置上级控制器表单验证
          this.$refs.parentRID.resetValidation()
          this.currentRow.ParentRID = ''
        }
      },
      filterControllerRID(val: string, update: Function) {
        this.controllerFilter = val
        update()
      },
      changeParentController(val) {
        if (!val) {
          this.currentRow.ParentChannelNo = 1
          this.$refs.ParentChannelNo?.resetValidation()
          return
        }

        this.$refs.ParentChannelNo?.validate()
      },
      getLngLatFromMap(lngLat: TLngLat) {
        // 等待用户选择地图经纬坐标
        setLngLat(this.currentRow, toWGS84(lngLat))
        setGCJ02LngLat(this.currentRow, lngLat)
      },
    },
    watch: {
      modelValue: {
        deep: true,
        immediate: true,
        handler(newVal) {
          if (!newVal.GCJ02LngLat) {
            setGCJ02LngLat(newVal)
          }
          this.currentRow = newVal
        },
      },
      currentRow: {
        deep: true,
        handler(newVal) {
          this.$emit('input', newVal)
        },
      },
    },
    components: {
      LonLat: defineAsyncComponent(() => import('@components/LonLat.vue')),
    }
  })
</script>

<style lang="scss">
  .controller-edit-content {
    width: 60vw;
  }
</style>

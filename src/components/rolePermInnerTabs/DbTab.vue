<template>
  <div class="q-mb-md">
    <q-bar class="bg-primary text-white">{{ $t('PermTab.DbPerm') }}</q-bar>
    <fieldset
      v-for="permRow in permTabData"
      :key="permRow.title"
      class="left-page-fieldset q-mx-md"
    >
      <legend v-text="$t('PermTab.' + permRow.title)" />
      <div class="row">
        <div :class="isMobile ? 'w-5' : 'w-16'"></div>
        <div
          v-for="permCol in permRow.cols"
          :key="permCol.type"
          class="col-2 self-center"
          v-text="$t('PermTab.' + permCol.type)"
        />
      </div>
      <div class="row">
        <div :class="isMobile ? 'w-5' : 'w-16'"></div>
        <q-checkbox
          v-for="permCol in permRow.cols"
          :key="permCol.type"
          v-model="permCol.isChecked"
          :disable="isNoPermission || checkboxDisable(permCol, permRow)"
          @update:model-value="checkboxClicked(permCol, permRow)"
          class="col-2"
        />
      </div>
    </fieldset>
  </div>
</template>

<script lang="ts">
  import { defineComponent } from 'vue'
  import { StrPubSub } from 'ypubsub'
  import { QueryFinishStatus } from '@src/services/queryData'
  import { DataName } from '@src/store'
  import { checkRoleAndUserError, DbName, DbPermShowRows } from '@src/utils/permission'
  import { GET_DATA, NS } from '@src/store/data/methodTypes'
  import { user as userPermission } from '@ygen/userPermission'
  import { ICallOption } from 'jsyrpc'
  import { crud } from '@ygen/crud'
  import log from '@src/utils/log'
  import { yrpcmsg } from 'yrpcmsg'
  import { PrpcDbRolePermission } from '@ygen/userPermission.rpc.yrpc'
  import { v1 as uuidV1 } from 'uuid'
  import { utcTime } from '@src/utils/dayjs'
  import { RolePermission } from '@src/services/dataStore'
  import { QueryRolePermissionFinish } from '@src/utils/pubSubSubject'
  import { emptyFn } from '@utils/common'
  import RolePermissionMixin from '@utils/mixins/rolePermission'

  //Db权限管理组件需要显示的列
  enum DbPermShowCols {
    Menu = 'Menu',
    Query = 'Query',
    Insert = 'Insert',
    Delete = 'Delete',
    Update = 'Update',
  }

  interface PermCol {
    isChecked: boolean
    //对应的权限的RID
    PermissionRID: string
    //用于checkbox校验
    type: string
    //对应roleperm的RID (若无，则表示没有此权限映射)
    rolePermRID: string
  }

  interface PermRow {
    title: string
    cols: { [optionType: string]: PermCol }
  }

  const HistoryTabsNotShow: String[] = [DbPermShowRows.DbMarkerPatrolHistory, DbPermShowRows.DbMarkerHistory, DbPermShowRows.DbControllerOnlineHistory]
  const HistoryTabNotShowCols: String[] = [DbPermShowCols.Insert, DbPermShowCols.Delete, DbPermShowCols.Update]

  export default defineComponent({
    mixins: [RolePermissionMixin],
    name: 'DbTab',
    props: {
      roleRID: String,
    },
    data() {
      return {
        permTabData: [] as PermRow[],
      }
    },
    computed: {
      isMobile() {
        return this.$q.platform.is.mobile
      },
      permData(): userPermission.IDbPermission[] {
        return this.$store.getters[`${NS}/${GET_DATA}`](DataName.Permission)
      },
      permDataClass(): { [key: string]: userPermission.IDbPermission } {
        const data = {}
        this.permData.forEach(item => {
          if (!data[item.PermissionType]) {
            data[item.PermissionType] = []
          }
          data[item.PermissionType].push(item)
        })
        return data
      },
      currentRolePermData(): userPermission.IDbRolePermission[] {
        return this.$store.getters[`${NS}/${GET_DATA}`](DataName.RolePermission)
          .filter(item => item.RoleRID === this.roleRID)
      },
    },
    methods: {
      splitPermVal(permVal, colType = null) {
        let [name, value] = permVal.split('.')
        // 需要特殊处理，交换数据
        if (colType === DbPermShowCols.Menu) {
          [name, value] = [value, name]
        }
        // 以对象形式返回
        return { name, value }
      },
      splicePermVal(name, type) {
        return name + '.' + type
      },
      findPerm(name, type) {
        if (type === DbPermShowCols.Menu) {
          return this.permDataClass['menu'].find(
            item => this.splitPermVal(item.PermissionValue).value === name)
        }
        return this.permDataClass['db'].find(item => item.PermissionValue === this.splicePermVal(name, type))
      },
      initDbTabData() {
        this.permTabData = []
        for (let name in DbPermShowRows) {
          let permRow: PermRow = {
            title: name,
            cols: {},
          }
          for (let type in DbPermShowCols) {
            if (HistoryTabsNotShow.includes(name) && HistoryTabNotShowCols.includes(type)) {
              continue
            }            //找到在permission中的一条数据
            const permRID = this.findPerm(name, type)?.RID
            //查找RolePermission这一项数据
            const rolePerm = this.currentRolePermData.find(item => item.PermissionRID === permRID)
            let permCol: PermCol = {
              isChecked: rolePerm ? true : false,
              PermissionRID: permRID,
              type: type,
              rolePermRID: rolePerm?.RID,
            }
            permRow.cols[type] = permCol
          }
          this.permTabData.push(permRow)
        }
      },

      checkboxDisable(permCol: PermCol, permRow: PermRow) {
        // 如果登录的用户没有对应的权限，则禁用对应的选项
        // if (!checkOperationPermission(permRow.title as DbName, permCol.type as OperationType)) { return true }

        // 当menu为false时，该行其他选项为disable、
        const modifyActionList: string[] = [DbPermShowCols.Update, DbPermShowCols.Insert, DbPermShowCols.Delete]
        const dbActionList: string[] = [...modifyActionList, DbPermShowCols.Query]
        if (!permRow.cols[DbPermShowCols.Menu].isChecked && dbActionList.includes(permCol.type)) {
          return true
        }

        // history只允许menu和query。不允许insert、delete、、update
        const historyTableList: string[] = [DbPermShowRows.DbMarkerHistory, DbPermShowRows.DbControllerOnlineHistory]
        if (historyTableList.includes(permRow.title) && modifyActionList.includes(permCol.type)) {
          return true
        }

        // 如果是控制器，则判断界桩是否有设置查询权限，如果有则禁用
        if (permRow.title === DbPermShowRows.DbController) {
          const bysMarkerPermData = this.findSpecifyPermRow(DbPermShowRows.DbBysMarker)
          return bysMarkerPermData?.cols[DbPermShowCols.Query]?.isChecked && !modifyActionList.includes(permCol.type)
        }

        return false
      },
      addData(data: userPermission.IDbRolePermission) {
        //insert + sync store + go back
        return this.insertIntoRolePerm(data)
          .then(() => {
            //sync store
            RolePermission.setData(data.RID as string, data)
          })
      },
      delData(data: userPermission.IDbRolePermission) {
        //delete + sync store + go back
        return this.deleteRolePerm(data)
          .then(() => {
            //sync store
            RolePermission.deleteData(data.RID as string)
          })
      },

      /*同步数据*/
      //同步页面数据
      syncPermCheckStatus(permRow: PermRow, data: userPermission.IDbRolePermission, type: DbPermShowCols | string,
        isChecked: boolean) {
        const index = this.permTabData.findIndex(item => item.title === permRow.title)
        if (index < 0 || this.permTabData[index]?.cols) { return }

        this.$set(this.permTabData[index].cols, type, {
          ...this.permTabData[index].cols[type],
          rolePermRID: data.RID,
          isChecked: isChecked,
        })
      },
      findSpecifyPermRow(name: string): PermRow | undefined {
        return this.permTabData.find(row => row.title === name)
      },
      syncControllerPermWithMarker(permRow: PermRow, permCol: PermCol) {
        const dbMenuQuery: string[] = [DbPermShowCols.Menu, DbPermShowCols.Query]
        if (permCol.isChecked && dbMenuQuery.includes(permCol.type)) {
          const controllerPermRow = this.findSpecifyPermRow(DbName.DbController)
          if (controllerPermRow) {
            const actionFn = (opt: string, optCol: PermCol) => {
              const data: userPermission.IDbRolePermission = {
                RID: uuidV1(),
                RoleRID: this.roleRID,
                PermissionRID: optCol.PermissionRID,
                UpdatedAt: utcTime(),
              }
              this.addData(data)
                .then(() => {
                  this.syncPermCheckStatus(controllerPermRow, data, opt, permCol.isChecked)
                })
                .catch(emptyFn)
            }
            this.$nextTick(() => {
              for (let i = 0; i < dbMenuQuery.length; i++) {
                const opt = dbMenuQuery[i]
                const optCol = controllerPermRow.cols[opt]
                if (optCol.isChecked) { continue }

                actionFn(opt, optCol)
              }
            })
          }
        }
      },
      cancelAllPermWithMenu(permRow: PermRow, permCol: PermCol) {
        const colKeys = Object.keys(permRow.cols)
        for (let i = 0; i < colKeys.length; i++) {
          const key = colKeys[i]
          const col: PermCol = permRow.cols[key]
          if (col.type === permCol.type || !col.isChecked) { continue }
          // 找出角色对应的权限数据
          let rolePermission: userPermission.IDbRolePermission | undefined = this.currentRolePermData.find(
            item => item.PermissionRID === col.PermissionRID)
          if (rolePermission) {
            this.delData(rolePermission).catch(emptyFn)
              .then(() => {
                rolePermission && this.syncPermCheckStatus(permRow, rolePermission, col.type, permCol.isChecked)
              })
          } else {
            rolePermission = {
              RID: '',
            }
            this.syncPermCheckStatus(permRow, rolePermission, col.type as DbPermShowCols, permCol.isChecked)
          }
        }
      },
      checkboxClicked(permCol: PermCol, permRow: PermRow) {
        let data: userPermission.IDbRolePermission = {
          RID: permCol.isChecked ? uuidV1() : permCol.rolePermRID,
          RoleRID: this.roleRID,
          PermissionRID: permCol.PermissionRID,
          UpdatedAt: utcTime(),
        }
        if (permCol.isChecked) {
          // this.syncPermCheckStatus(permRow, data, DbPermShowCols.Menu, permCol.isChecked)
          this.addData(data)
            .then(() => {
              // 还需要添加query
              const queryCol = permRow.cols[DbPermShowCols.Query]
              if (permCol.type === DbPermShowCols.Menu && !queryCol.isChecked) {
                data = {
                  RID: uuidV1(),
                  RoleRID: this.roleRID,
                  PermissionRID: queryCol.PermissionRID,
                  UpdatedAt: utcTime(),
                }
                this.addData(data)
                  .then(() => {
                    this.syncPermCheckStatus(permRow, data, DbPermShowCols.Query, permCol.isChecked)
                  })
                  .catch(emptyFn)
              }
            })
            .then(() => {
              // 有界桩的权限，必须要有控制器的菜单、查询权限
              if (permRow.title === DbName.DbBysMarker) {
                this.syncControllerPermWithMarker(permRow, permCol)
              }
            })
            .catch(() => {
              this.syncPermCheckStatus(permRow, data, permCol.type, !permCol.isChecked)
            })

          return
        }

        this.delData(data)
          .then(() => {
            // 如果是菜单或查询权限，则要同步取消其他权限
            const dbMenuQuery: string[] = [DbPermShowCols.Menu, DbPermShowCols.Query]
            dbMenuQuery.includes(permCol.type) && this.cancelAllPermWithMenu(permRow, permCol)
          })
          .catch(() => {
            this.syncPermCheckStatus(permRow, data, permCol.type, !permCol.isChecked)
          })
      },
      //API请求
      insertIntoRolePerm(data: userPermission.IDbRolePermission): Promise<boolean> {
        return new Promise((resolve, reject) => {
          let options: ICallOption = {
            OnResult: (res: crud.IDMLResult, rpcCmd: yrpcmsg.Ymsg, meta?: yrpcmsg.IGrpcMeta) => {
              log.info('IDbRolePermission Insert result', res, rpcCmd, meta)
              if (res.AffectedRow === 0) {
                reject()
              } else {
                resolve(true)
              }
            },
            OnServerErr: (errRpc: yrpcmsg.Ymsg) => {
              log.error('IDbRolePermission Insert server error', data, errRpc)
              // 处理服务器响应的错误
              let reason = checkRoleAndUserError(errRpc)
              this.$q.notify({
                message: reason as string,
                position: 'top',
                type: 'negative',
              })
            },
            OnLocalErr: (err: any) => {
              log.error('IDbRolePermission Insert local error', err)
              this.$q.notify({
                message: err.toString(),
                position: 'top',
                type: 'negative',
              })
            },
            OnTimeout: (v: any) => {
              log.warn('IDbRolePermission Insert timeout', v)
              this.$q.notify({
                message: this.$t('message.requestTimeout') as string,
                position: 'top',
                type: 'warning',
              })
            },
          }
          PrpcDbRolePermission.Insert(data, options)
        })
      },
      deleteRolePerm(data: userPermission.IDbRolePermission): Promise<boolean> {
        return new Promise((resolve, reject) => {
          let options: ICallOption = {
            OnResult: (res: crud.IDMLResult, rpcCmd: yrpcmsg.Ymsg, meta?: yrpcmsg.IGrpcMeta) => {
              log.info('IDbRolePermission Delete result', res, rpcCmd, meta)
              if (res.AffectedRow === 0) {
                reject()
              } else {
                resolve(true)
              }
            },
            OnServerErr: (errRpc: yrpcmsg.Ymsg) => {
              log.error('IDbRolePermission Delete server error', data, errRpc)
              // 处理服务器响应的错误
              let reason = checkRoleAndUserError(errRpc, this.$t('message.deleteFailed'))
              this.$q.notify({
                message: reason as string,
                position: 'top',
                type: 'negative',
              })
            },
            OnLocalErr: (err: any) => {
              log.error('IDbRolePermission Delete local error', err)
              this.$q.notify({
                message: err.toString(),
                position: 'top',
                type: 'negative',
              })
            },
            OnTimeout: (v: any) => {
              log.warn('IDbRolePermission Delete timeout', v)
              this.$q.notify({
                message: this.$t('message.requestTimeout') as string,
                position: 'top',
                type: 'warning',
              })
            },
          }
          PrpcDbRolePermission.Delete(data, options)
        })
      },
      /*同步数据*/
    },
    watch: {
      currentRolePermData() {
        this.initDbTabData()
      },
    },
    beforeMount() {
      // 判断是否完成角色权限数据请求，如果没有完成，则在完成请求时，生成界面数据
      if (!QueryFinishStatus[DataName.RolePermission]) {
        StrPubSub.subscribeOnce(QueryRolePermissionFinish, this.initDbTabData)
        return
      }
      this.initDbTabData()
    },
    beforeUnmount() {
      StrPubSub.unsubscribe(QueryRolePermissionFinish, this.initDbTabData)
    },
  })
</script>

<style type="scss">
  .left-page-fieldset {
    border: 1px dotted;
  }
</style>

<template>
  <div class="q-mb-md">
    <q-bar class="bg-primary text-white">{{ $t('PermTab.CmdPerm') }}</q-bar>
    <div
      class="row self-center justify-center"
      v-for="item of permRows"
      :key="item.permRID"
    >
      <span class="q-mt-sm q-ml-md q-space">{{ $t('form.' + item.name) }}</span>
      <q-checkbox
        class="q-mr-md"
        v-model="item.isChecked"
        :disable="isNoPermission"
        @update:model-value="checkboxClick(item)"
      />
    </div>
  </div>
</template>

<script lang="ts">
  import { DataName } from '@src/store'
  import { GET_DATA, NS } from '@store/data/methodTypes'
  import { defineComponent } from 'vue'
  import { QueryFinishStatus } from '@src/services/queryData'
  import { StrPubSub } from 'ypubsub'
  import { QueryRolePermissionFinish } from '@utils/pubSubSubject'
  import log from 'loglevel'
  import { user as userPermission } from '@ygen/userPermission'
  import { RolePermission } from '@src/services/dataStore'
  import { ICallOption } from 'jsyrpc'
  import { crud } from '@ygen/crud'
  import { yrpcmsg } from 'yrpcmsg'
  import { PrpcDbRolePermission } from '@ygen/userPermission.rpc.yrpc'
  import { v1 as uuidV1 } from 'uuid'
  import { utcTime } from '@utils/dayjs'
  import RolePermissionMixin from '@utils/mixins/rolePermission'
  import { checkRoleAndUserError } from '@utils/permission'

  interface PermRow {
    permRID: string,
    rolePermRID: string,

    isChecked: boolean,
    name: string
  }

  export default defineComponent({
    mixins: [RolePermissionMixin],
    name: 'CmdTab',
    props: {
      roleRID: String,
    },
    data() {
      return {
        permRows: [] as PermRow[],
      }
    },
    computed: {
      permData(): Array<userPermission.IDbPermission> {
        return this.$store.getters[`${NS}/${GET_DATA}`](DataName.Permission)
      },
      cmdPermData(): Array<userPermission.IDbPermission> {
        return this.permData.filter(item => item.PermissionType === 'cmd')
      },
      currentRolePermData(): Array<userPermission.IDbRolePermission> {
        return this.$store.getters[`${NS}/${GET_DATA}`](DataName.RolePermission)
          .filter(item => item.RoleRID === this.roleRID)
      },
    },
    methods: {
      splitPermVal(permVal) {
        let [name, value] = permVal.split('.')
        // 以对象形式返回
        return { name, value }
      },
      splicePermVal(name, type) {
        return name + '.' + type
      },
      initPage() {
        for (const perm of this.cmdPermData) {
          const permSlice = this.splitPermVal(perm.PermissionName)
          const rolePermItem = this.currentRolePermData.find(item => item.PermissionRID === perm.RID)
          this.permRows.push(
            {
              permRID: perm.RID,
              rolePermRID: rolePermItem?.RID ?? '',
              isChecked: !!rolePermItem,
              name: permSlice.value,
            },
          )
        }
      },

      //API请求
      insertIntoRolePerm(data: userPermission.IDbRolePermission): Promise<boolean> {
        return new Promise((resolve, reject) => {
          let options: ICallOption = {
            OnResult: (res: crud.IDMLResult, rpcCmd: yrpcmsg.Ymsg, meta?: yrpcmsg.IGrpcMeta) => {
              log.info('IDbRolePermission Insert result', res, rpcCmd, meta)
              if (res.AffectedRow === 0) {
                reject()
              } else {
                resolve(true)
              }
            },
            OnServerErr: (errRpc: yrpcmsg.Ymsg) => {
              log.error('IDbRolePermission Insert server error', data, errRpc)
              // 处理服务器响应的错误
              let reason = checkRoleAndUserError(errRpc)
              this.$q.notify({
                message: reason as string,
                position: 'top',
                type: 'negative',
              })
            },
            OnLocalErr: (err: any) => {
              log.error('IDbRolePermission Insert local error', err)
              this.$q.notify({
                message: err.toString(),
                position: 'top',
                type: 'negative',
              })
            },
            OnTimeout: (v: any) => {
              log.warn('IDbRolePermission Insert timeout', v)
              this.$q.notify({
                message: this.$t('message.requestTimeout') as string,
                position: 'top',
                type: 'warning',
              })
            },
          }
          PrpcDbRolePermission.Insert(data, options)
        })
      },
      deleteRolePerm(data: userPermission.IDbRolePermission): Promise<boolean> {
        return new Promise((resolve, reject) => {
          let options: ICallOption = {
            OnResult: (res: crud.IDMLResult, rpcCmd: yrpcmsg.Ymsg, meta?: yrpcmsg.IGrpcMeta) => {
              log.info('IDbRolePermission Delete result', res, rpcCmd, meta)
              if (res.AffectedRow === 0) {
                reject()
              } else {
                resolve(true)
              }
            },
            OnServerErr: (errRpc: yrpcmsg.Ymsg) => {
              log.error('IDbRolePermission Delete server error', data, errRpc)
              // 处理服务器响应的错误
              let reason = checkRoleAndUserError(errRpc, this.$t('message.deleteFailed'))
              this.$q.notify({
                message: reason as string,
                position: 'top',
                type: 'negative',
              })
            },
            OnLocalErr: (err: any) => {
              log.error('IDbRolePermission Delete local error', err)
              this.$q.notify({
                message: err.toString(),
                position: 'top',
                type: 'negative',
              })
            },
            OnTimeout: (v: any) => {
              log.warn('IDbRolePermission Delete timeout', v)
              this.$q.notify({
                message: this.$t('message.requestTimeout') as string,
                position: 'top',
                type: 'warning',
              })
            },
          }
          PrpcDbRolePermission.Delete(data, options)
        })
      },
      addData(data: userPermission.IDbRolePermission) {
        //insert + sync store + go back
        return this.insertIntoRolePerm(data)
          .then(() => {
            //sync store
            RolePermission.setData(data.RID as string, data)
          })
      },
      delData(data: userPermission.IDbRolePermission) {
        //delete + sync store + go back
        return this.deleteRolePerm(data)
          .then(() => {
            //sync store
            RolePermission.deleteData(data.RID as string)
          })
      },
      syncRolePermission(data: userPermission.IDbRolePermission, permItem: PermRow) {
        const index = this.permRows.findIndex(item => item.name === permItem.name)
        if (index < 0) { return }

        this.$set(this.permRows, index, {
          ...this.permRows[index],
          rolePermRID: data.RID,
        })
      },
      checkboxClick(item: PermRow) {
        const data: userPermission.IDbRolePermission = {
          RID: item.isChecked ? uuidV1() : item.rolePermRID,
          RoleRID: this.roleRID,
          PermissionRID: item.permRID,
          UpdatedAt: utcTime(),
        }
        if (item.isChecked) {
          this.addData(data)
            .then(() => {
              // 同步当前组件的数据
              this.syncRolePermission(data, item)
            })
            .catch(() => {
              item.isChecked = !this.isChecked
            })
        } else {
          this.delData(data)
            .catch(() => {
              item.isChecked = !this.isChecked
            })
        }
      },
    },
    beforeMount() {
      // 判断是否完成角色权限数据请求，如果没有完成，则在完成请求时，生成界面数据
      if (!QueryFinishStatus[DataName.RolePermission]) {
        StrPubSub.subscribeOnce(QueryRolePermissionFinish, this.initPage)
        return
      }
      this.initPage()
    },
    beforeUnmount() {
      StrPubSub.unsubscribe(QueryRolePermissionFinish, this.initPage)
    },
  })
</script>

<style type="scss"></style>

<template>
  <q-dialog
    persistent
    :modelValue="props.visible"
    maximized
    transition-show="slide-up"
    transition-hide="slide-down"
    class="z-top"
  >
    <q-card>
      <Maplibre-GL
        :map-options="mapOptions"
        @init="onMapInit"
      >
        <div
          class="select-lonlat-tips"
          v-text="$t('maps.getLngLatTip')"
        ></div>
        <q-btn
          ref="closeMapRef"
          class="custom-control-btn closeMap"
          icon="close"
          flat
          size="sm"
          :ripple="false"
          @click="closeMap"
        />
      </Maplibre-GL>
    </q-card>
  </q-dialog>
</template>

<script
  lang="ts"
  setup
  name="LonLat"
>
  import {webConfig } from '@src/config'
  import MaplibreGL from '@components/MaplibreGL.vue'
  import { shallowReactive, ref, computed, onBeforeUnmount } from 'vue'
  import { GeoJSONSourceSpecification, Map as MapGl, MapMouseEvent, MapTouchEvent } from 'maplibre-gl'
  import { QBtn } from 'quasar'
  import {
    addGeometryToFeatures,
    createControllerMarkerFeature,
    createDeviceMarkerFeature,
    CustomControl, 
    CustomGeojsonFeature, 
    CustomImagePrefix, 
    CustomImages, 
    getMarkerLayerOptions,
    markerAssets,
    pointFeatureMap2List,
    TLngLat
  } from '@utils/map'
  import storage, { MapInfoKey } from '@utils/storage'
  import { BysMarkerAndUpdateInfo } from '@utils/bysdb.type'
  import { bysdb } from '@ygen/bysdb'
  import { DataName } from '@src/store'
  import { merge } from 'lodash'
  import { utcTime } from '@utils/dayjs'
  import { checkoutAbnormalBysMarker } from '@utils/common'

  const props = defineProps<{
    visible: boolean
    modelValue: TLngLat
    data: BysMarkerAndUpdateInfo | bysdb.IDbController
    isMarker: boolean
  }>()

  // const emit = defineEmits(['update:visible', 'getLngLat'])
  const emit = defineEmits<{
    (e: 'update:visible', value: boolean): void
    (e: 'update:model-value', value: TLngLat): void
  }>()

  Object.assign(webConfig.map, storage.fetch(MapInfoKey))
  const mapOptions = shallowReactive({
    center: [0, 0],
    zoom: 12,
    minZoom: 1,
    maxZoom: 18,
    ...webConfig.map,
  })
  const closeMapRef = ref<QBtn>()
  const mapCenter = computed(() => {
    return props.modelValue ?? webConfig.map?.center ?? [0, 0]
  })

  function emitSelectLonLat(lngLat: TLngLat) {
    emit('update:model-value', lngLat)
    closeMap()
  }

  function onMapClick(event: MapMouseEvent | MapTouchEvent) {
    event.preventDefault()
    // 从高德底图获取经纬度，得到的是GCJ02坐标
    const lngLat: TLngLat = event.lngLat.toArray() as TLngLat
    emitSelectLonLat(lngLat)
  }

  let localeMap: MapGl | null = null
  function onMapInit(map: MapGl) {
    localeMap = map
    map.setCenter(mapCenter.value)
    map.getCanvas().style.cursor = 'crosshair'
    map.addControl(
      new CustomControl({
        buttons: [
          closeMapRef.value?.$el,
        ],
        onAdded(container) {
          container.parentNode?.insertBefore(container, container.parentNode?.firstChild)
        }
      }),
      'top-right',
    )
    map.once('click', onMapClick)

    map.on('load', () => {
      if (!localeMap) {
        return
      }
      const id = props.isMarker ? DataName.BysMarker : DataName.Controller
      let imageName = id + ''
      if (props.isMarker) {
        const device = props.data as BysMarkerAndUpdateInfo
        if (device.HasInstallDevice) {
          imageName += checkoutAbnormalBysMarker(device, utcTime()) ? 'abnormal' : ''
        } else {
          imageName += device.HasInstallStone ? 'uninstalled' : 'uninstalled-stone'
        }
      } else {
        imageName += props.data.ControllerType === 1 ? 'rp' : ''
      }
      const path = CustomImages[imageName]
      const name = CustomImagePrefix + path
      if (!localeMap.hasImage(name)) {
        map.loadImage(markerAssets[path])
        .then(res => {
          localeMap?.addImage(name, res.data)
        })
      }

      const features: Map<string, CustomGeojsonFeature> = new Map()
      const geometry = props.isMarker ?createDeviceMarkerFeature(props.data) : createControllerMarkerFeature(props.data)
      addGeometryToFeatures(features, geometry)
      const sourceData: GeoJSONSourceSpecification = {
        type: 'geojson',
        data: {
          type: 'FeatureCollection',
          features: pointFeatureMap2List(features)
        }
      }
      localeMap.addSource(id, merge({ type: 'geojson' }, sourceData))
      const layerData = {
        id,
        ...getMarkerLayerOptions(id)
      }
      const layer = merge(
        {
          type: 'symbol',
          source: layerData.source || layerData.id
        },
        layerData
      )
      localeMap.addLayer(layer)
    })
  }

  const closeMap = () => {
    if (localeMap) {
      localeMap.off('click', onMapClick)
    }
    emit('update:visible', false)
  }

  onBeforeUnmount(() => {
    localeMap = null
  })
</script>

<style lang="scss">
  .maplibregl-container-wrapper .select-lonlat-tips {
    position: fixed;
    left: 50vw;
    top: 10px;
    transform: translateX(-50%);
    border-radius: 10px;
    z-index: 99;
    padding: 10px;
    width: 230px;
    font-size: 17px;
    background-color: rgba(0, 0, 0, .8);
    color: #ffff;
    font-weight: bold;
    text-align: center;
  }
</style>

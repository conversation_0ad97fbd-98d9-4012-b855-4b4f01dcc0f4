<template>
  <div
    class="bf-divider"
    :class="['bf-divider-title--' + align]"
  >
    <span
      class="bf-divider-title text-caption q-py-xs q-px-sm"
      v-text="title"
    />
  </div>
</template>

<script lang="ts">
  import { defineComponent } from 'vue'

  const Aligns = ['left', 'center', 'right']

  export default defineComponent({
    name: 'Divider',
    props: {
      title: {
        type: String,
        default: 'divider title',
      },
      align: {
        type: String,
        default: 'center',
        validator(value: string): boolean {
          return Aligns.includes(value)
        },
      },
    },
  })
</script>

<style lang="scss" scoped>
  .bf-divider {
    display: table;
    white-space: nowrap;
    text-align: center;
    height: auto;
    line-height: 1;
    background: 0 0;
    /*color: #17233d;*/
    margin: 12px 0;

    &:before,
    &:after {
      content: '';
      display: table-cell;
      position: relative;
      top: 50%;
      width: 50%;
      background-repeat: no-repeat;
      border-top: 1px solid #e8eaec;
      transform: translateY(50%);
    }

    &-title {
      display: inline-block;
      padding: 0 12px;
    }

    &-title--left:before {
      width: 5%;
    }

    &-title--left:after {
      width: 95%;
    }

    &-title--center:before,
    &-title--center:after {
      width: 50%;
    }

    &-title--right:before {
      width: 95%;
    }

    &-title--right:after {
      width: 5%;
    }
  }
</style>

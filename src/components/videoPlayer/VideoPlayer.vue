<template>
  <div
    class="video-player-content"
    ref="mediaPlayer"
  >
    <div class="video-player-title row text-xs">
      <div class="q-space">
        <p>{{ $t('form.uploadUser') }}： {{ nodes.Author }}</p>
        <p v-if="nodes.UploadTime">{{ $t('form.uploadTime') }}： {{ nodes.UploadTime }}</p>
        <p v-if="nodes.Description">{{ $t('form.description') }}： {{ nodes.Description }}</p>
        <p v-if="nodes.LastUploadUser">{{ $t('form.lastUploadUser') }}： {{ nodes.LastUploadUser }}</p>
        <p v-if="nodes.LastUploadTime">{{ $t('form.lastUploadTime') }}： {{ nodes.LastUploadTime }}</p>
      </div>
      <div
        class="ctrl"
        @click="clickFullScreen"
      ><span class="material-icons">aspect_ratio</span></div>
    </div>
    <q-video
      :ratio="16/9"
      :src="source"
      class="video-media-player"
    ></q-video>
  </div>
</template>

<script lang="ts">
  import { defineComponent } from 'vue'

  export default defineComponent({
    name: 'VideoPlayer',
    props: {
      source: {
        type: String,
        require: true,
      },
      nodes: {
        type: Object,
        default() {
          return {}
        },
      },
    },
    methods: {
      clickFullScreen() {
        this.$q.fullscreen.toggle(this.$refs.mediaPlayer)
      },
    },
  })
</script>

<style lang="scss">
  .video-player-content {
    position: relative;
    min-height: 150px !important;
  }

  .video-player-title {
    position: absolute;
    top: 0;
    z-index: 999;
    width: 100%;
    padding-left: 5px;
    background-color: rgba(0, 0, 0, 0.47);
    color: #ffffff;

    p {
      margin: 0;
    }
  }

  .video-player-title .ctrl {
    margin: 2px 8px;
    width: 30px;
    height: 30px;
    text-align: center;
    display: inline-block;
    cursor: pointer;
    padding-bottom: 4px;
    font-size: 22px;
  }

  .video-media-player {
    height: 100%;
  }

</style>

<template>
  <q-card
    class="controller-popup custom-mapbox-popup"
    flat
  >
    <q-card-section class="q-pa-none full-width">
      <q-item-label>{{ $t('form.unit') }}: {{ parentName }}</q-item-label>
      <q-item-label>{{ $t('form.customID') }}: {{ ControllerNo }}</q-item-label>
      <q-item-label>{{ $t('form.controllerHWID') }}: {{ ControllerHWID }}</q-item-label>
      <q-item-label v-if="controllerName">{{ $t('form.parentController') }}: {{ controllerName }}</q-item-label>
      <q-item-label v-if="lastReportTime">{{ $t('form.lastReportTime') }}: {{ lastReportTime }}
      </q-item-label>
      <q-item-label v-if="batteryPower">{{ $t('form.batteryPower') }}: {{ batteryPower }}</q-item-label>
      <template v-if="isMaintainPerm">
        <q-list style="max-width: 200px;">
          <q-expansion-item
            dense
            dense-toggle
            expand-separator
            :label="$t('common.channelDevNoSetting')"
            class="controller-popup-expand-item"
          >
            <q-card>
              <q-card-section class="controller-popup-expand-card">
                <div
                  v-for="channel in validChannel"
                  :key="channel"
                  class="self-center flex"
                  :title="channel === uploadChannelNo ? `${$t('common.localChannelDevNo')}` : ''"
                >
                  {{ $t('common.channelNo') }} {{ channel }}:
                  <q-space />
                  <q-radio
                    dense
                    class="controller-popup-expand-radio"
                    v-model="channelOption[channel]"
                    :val="1"
                    :label="$t('common.No1')"
                    @update:model-value="(val) => {
                      channelNoChange(channel, val)
                    }"
                  />
                  <q-radio
                    dense
                    class="controller-popup-expand-radio"
                    v-model="channelOption[channel]"
                    :val="2"
                    :label="$t('common.No2')"
                    @update:model-value="(val) => {
                      channelNoChange(channel, val)
                    }"
                  />
                </div>

              </q-card-section>
            </q-card>
          </q-expansion-item>
        </q-list>
      </template>
    </q-card-section>

    <q-card-actions class="q-pa-none full-width q-pt-md">
      <q-btn-group
        flat
        spread
        class='custom-mapbox-popup-btn-group full-width'
      >
        <q-btn
          v-if="hasControllerEditPerm"
          size='sm'
          color="primary"
          @click="quickOpenEditPage"
          :label="$t('common.edit')"
        />
        <q-btn
          size='sm'
          color="primary"
          @click="openMediaInfo"
          :label="$t('common.mediaData')"
        />

        <q-btn
          v-if='!modifyServerAddressPerm'
          size='sm'
          color="primary"
          @click="wakeUpBaiduMap"
          :label="$t('CmdTest.goto')"
        />
        <template v-else>
          <q-btn-dropdown
            auto-close
            split
            size='sm'
            color="primary"
            :label="$t('CmdTest.goto')"
            @click="wakeUpBaiduMap"
          >
            <q-list
              padding
              dense
              class='custom-mapbox-popup-dropdown-list'
            >
              <q-item
                clickable
                @click="openControllerServerAddrDialog"
                :disable='!isOnline'
              >
                <q-item-section>
                  <q-item-label>{{ $t('menus.modifyServerAddr') }}</q-item-label>
                </q-item-section>
              </q-item>
            </q-list>
          </q-btn-dropdown>
        </template>
      </q-btn-group>
    </q-card-actions>
  </q-card>
</template>

<script lang="ts">
  import { defineComponent } from 'vue'
  import { StrPubSub } from 'ypubsub'
  import { Controller } from '@src/services/dataStore'
  import { bysdb } from '@ygen/bysdb'
  import { GET_DATA, NS } from '@src/store/data/methodTypes'
  import { DataName } from '@src/store/data'
  import { OpenMenu, WakeupBaiduMap } from '@src/utils/pubSubSubject'
  import { ControllerTypes, isHaveMaintainPerm } from '@utils/common'
  import { changeBetweenChannelNo, isLonginController } from '@src/services/controller'
  import { getGCJ02LngLat } from '@utils/gcoord'
  import { PermissionType } from '@utils/permission'

  let channel2Dev = {}

  export default defineComponent({
    name: 'ControllerMarkerPopupLeftClick',
    props: {
      RID: {
        type: String,
        required: true,
      },
    },
    data() {
      return {
        photo: '0',
        photoFullscreen: false,
        channelOption: {},
      }
    },
    methods: {
      openControllerServerAddrDialog() {
        StrPubSub.publish(OpenMenu, 'ControllerServerAddress', (vm) => {
          vm.formData.controller = this.data.ControllerHWID
          vm.visible = true
        })
      },
      quickOpenEditPage() {
        StrPubSub.publish(OpenMenu, 'ControllerQuickEdit', (vm) => {
          vm.controllerData = { ...this.data }
          vm.isVisible = true
          vm.isBysMarker = false
          vm.isController = true
        })
      },
      wakeUpBaiduMap() {
        let item = {
          lonlat: (this.data?.Lon ?? 0) + ' ' + (this.data?.Lat ?? 0),
          name: this.data?.ControllerNo,
        }
        StrPubSub.publish(WakeupBaiduMap, item)
      },
      openMediaInfo() {
        StrPubSub.publish(OpenMenu, 'MediaInfo', (vm) => {
          vm.HWIDFilter = this.ControllerHWID
          vm.HWIDIsMarker = false
          vm.currentRow.ControllerRID = this.RID
          vm.isBysMarker = false
          vm.isController = true
        })
      },
      initChannelDevNo() {
        this.channelOption = channel2Dev = {}
        if (this.channelNos) {
          for (let i = 0; i < this.channelNos.length; i += 2) {
            channel2Dev[this.channelNos[i]] = this.channelNos[i + 1]
          }
        }
      },
      channelNoChange(channel, val) {
        //`是否确认修改通道 ${channel} 的信道至${val}`
        this.$q.dialog({
          html: true,
          message: `<div class="q-avatar" style="font-size: 38px;">
                          <div class="q-avatar__content row flex-center overflow-hidden bg-warning">
                            <i aria-hidden="true" role="presentation" class="material-icons q-icon notranslate">
                              warning
                            </i>
                          </div>
                         </div>
                         <span class="q-ml-sm">是否修改通道 ${channel} 的信道至${val}</span>`,

          class: 'custom-dialog-plugin',
          persistent: true,
          ok: {
            dense: true,
          },
          cancel: {
            dense: true,
          },
          focus: 'cancel',
        })
          .onOk(() => {
            !!this.data && changeBetweenChannelNo(this.data, channel, val)
          })
          .onCancel(() => {
            this.channelOption[channel] = val === 1 ? 2 : 1
          })
      },
    },
    computed: {
      controllerMenuPerm() {
        return this.$store.getters[`${NS}/${GET_DATA}`](DataName.Permission)
          .find(v => v.PermissionType === PermissionType.menu && v.PermissionName === 'Data.DbController')
      },
      hasControllerEditPerm() {
        return !!this.controllerMenuPerm
      },
      //上行数据用到的通道
      uploadChannelNo() {
        return this.data?.controllerState?.ChannelNo ?? 0
      },
      //获取通道的信道号
      channelNos() {
        return this.data?.controllerState?.TransferChannelNos ?? 0
      },
      //通道数
      controllerChannelNos() {
        return this.data?.ChannelCount ?? 1
      },
      validChannel() {
        const channel: Array<number> = []
        for (let i = 0; i < this.channelNos.length; i += 2) {
          if (this.channelNos[i] !== this.stationDeviceNo) {//对基站而言 本地通道不是链路通道
            channel.push(this.channelNos[i])
          }
        }
        return channel
      },
      isMaintainPerm() {
        //有维护权限&&基站
        return this.data?.ControllerType === ControllerTypes.baseController && isHaveMaintainPerm() && this.channelNos
      },
      modifyServerAddressPerm() {
        return this.data?.ControllerType && isHaveMaintainPerm()
      },
      isOnline() {
        const isOnline = this.data?.controllerState?.online ?? false
        return isOnline && isLonginController(this.data?.controllerState?.NetworkType as number)
      },
      data(): bysdb.IDbController | undefined {
        return this.$store.getters[`${NS}/${GET_DATA}`](DataName.Controller, this.RID)
      },
      ControllerNo() {
        return this.data?.ControllerNo ?? ''
      },
      ControllerHWID() {
        return this.data?.ControllerHWID ?? ''
      },
      parentName() {
        return Controller.getParentName(this.data?.OrgRID)
      },
      controllerName() {
        const controller = Controller.getData(this.data?.ParentRID) as bysdb.IDbController | undefined
        return controller?.ControllerNo ?? ''
      },
      stationDeviceNo() {
        return this.data?.controllerState?.StationDeviceNo ?? 0
      },
      coordinate() {
        return getGCJ02LngLat(this.data ?? {}).join(',')
      },
      lastReportTime() {
        const time = this.data?.controllerState?.LastDataTime
        return time ? time : false
      },
      batteryPower() {
        const pw = this.data?.controllerState?.Power
        if (!pw || !(pw instanceof Number)) { return false }
        const power = pw.toFixed(1)
        return power ? `${power} V` : false
      },
    },
    watch: {
      controllerChannelNos: {
        immediate: true,
        handler(newVal) {
          //初始化通道-信道号
          this.initChannelDevNo()
          for (let i = 0; i < newVal; i++) {
            //设置通道-信道号
            this.channelOption[i] = channel2Dev[i] ?? 0
          }
        },
      },
      channelNos: {
        deep: true,
        handler() {
          this.initChannelDevNo()
          for (let i = 0; i < this.controllerChannelNos; i++) {
            //设置通道-信道号
            this.channelOption[i] = channel2Dev[i] ?? 0
          }
        },
      },
    },
  })
</script>

<style lang="scss">
  @import "src/css/map.popup";

  .bysMarker-photo-carousel {
    .absolute-full.custom-caption {
      .info-icon {
        top: 8px;
        left: 8px;
      }

      .photo-info {
        background-color: rgba(0, 0, 0, .2);
        color: #fff;
        padding: 0 40px;
      }
    }
  }

  .controller-popup-expand-item .q-item {
    padding: 0;
  }

  .controller-popup-expand-card {
    padding: 0 !important;
  }

  .controller-popup-expand-radio {
    padding-left: 12px;
  }

  .controller-popup-expand-radio .q-radio__inner {
    font-size: 30px;
  }

  .controller-popup-expand-radio .q-radio__label {
    font-size: 12px;
  }

  .q-dialog-plugin.custom-dialog-plugin {
    width: unset;

    .q-card__actions {
      justify-content: center;
    }
  }
</style>

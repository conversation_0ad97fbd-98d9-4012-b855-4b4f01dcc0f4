<template>
  <div
    ref="mapContainer"
    class="maplibregl-container-wrapper"
  >
    <q-resize-observer @resize="onResize" />
    <!--地图中心十字架-->
    <div class="map-center-symbol"></div>

    <!-- 一些需要辅助的slot元素 -->
    <slot></slot>
  </div>
</template>

<script
  lang="ts"
  setup
>
  import { ref, onBeforeUnmount, onMounted, computed, effect, watch } from 'vue'
  import maplibreGl from 'maplibre-gl'
  import { MapInfoControl, MapStyleControl, MapStyleName, useGaodeMapStyle } from '@utils/map'
  import { useI18n } from 'vue-i18n'
  import { appLang, webConfig } from '@src/config'
  import { zhCNLocales, enUSLocales } from '@utils/maplibreLocals'

  interface MaplibreGLProps {
    mapOptions?: Partial<maplibreGl.MapOptions>
    controls?: Array<{ control: maplibreGl.IControl, position?: maplibreGl.ControlPosition }>
  }

  const props = defineProps<MaplibreGLProps>()
  const emit = defineEmits<{
    (e: 'init', value: maplibreGl.Map): void
  }>()
  const i18n = useI18n()
  const getGaodeMapStyle = useGaodeMapStyle()

  const mapContainer = ref<HTMLElement>()
  let LocalMap: maplibreGl.Map | undefined
  const defaultMapOptions: Partial<maplibreGl.MapOptions> = {
    minZoom: 1,
    maxZoom: 18,
    center: [113.30739234951989, 23.13472086044206],
  }

  const mapStyleType = ref(webConfig.map.style || MapStyleName.Satellite)
  const mapStyle = computed<maplibreGl.StyleSpecification>(() => {
    return getGaodeMapStyle(mapStyleType.value, i18n.locale.value)
  })

  function onResize() {
    LocalMap?.resize()
  }

  function initListeners() {
    // nol impl
    if (!LocalMap) return

    LocalMap.on('load', () => {
      updateBuiltinControlI18n()
    })
  }

  // 默认的控件
  const fullscreenControl = new maplibreGl.FullscreenControl()
  const navigationControl = new maplibreGl.NavigationControl()
  function initControls() {
    LocalMap!.addControl(fullscreenControl, 'top-right')
    LocalMap!.addControl(navigationControl, 'top-right')

    // 样式控件
    LocalMap!.addControl(new MapStyleControl({
      currentStyle: mapStyleType.value,
    }), 'top-right')
    // 地图缩放级别控件
    LocalMap!.addControl(new MapInfoControl(), 'bottom-left')

    props.controls?.forEach((item) => {
      LocalMap!.addControl(item.control, item.position)
    })
  }

  const mapLocale = computed<Record<string, string>>(() => {
    return i18n.locale.value === appLang.zhCN ? zhCNLocales : enUSLocales
  })

  function initMap() {
    const mapOptions = Object.assign({}, defaultMapOptions, props.mapOptions)
    LocalMap = new maplibreGl.Map({
      ...mapOptions,
      // 下列参数不允许修改
      container: mapContainer.value!,
      style: mapStyle.value,
      attributionControl: false,
      locale: mapLocale.value,
    })

    LocalMap.getCanvas().style.cursor = 'default'
    emit('init', LocalMap)
  }

  // 重置内置控件翻译
  function updateBuiltinControlI18n() {
    if (!LocalMap) return
    Object.assign(LocalMap._locale, mapLocale.value)
    fullscreenControl._updateTitle()
    navigationControl._setButtonTitle(navigationControl._zoomInButton, 'ZoomIn')
    navigationControl._setButtonTitle(navigationControl._zoomOutButton, 'ZoomOut')
    navigationControl._setButtonTitle(navigationControl._compass, 'ResetBearing')
  }
  watch(i18n.locale, (/* locale: string */) => {
    updateBuiltinControlI18n()
  })

  effect(() => {
    props.mapOptions?.center && LocalMap?.setCenter(props.mapOptions?.center)
    props.mapOptions?.zoom && LocalMap?.setZoom(props.mapOptions?.zoom)
  })

  onMounted(() => {
    initMap()
    initControls()
    initListeners()
  })

  onBeforeUnmount(() => {
    LocalMap = undefined
  })

  defineExpose({
    getMap: () => LocalMap,
  })
</script>

<style lang="scss">
  @import 'maplibre-gl/dist/maplibre-gl.css';
  @import "quasar/src/css/variables.sass";

  .maplibregl-container-wrapper,
  .maplibregl-container-wrapper>.maplibregl-canvas-container {
    @apply h-full;
    @apply w-full;
  }

  .maplibregl-container-wrapper {
    z-index: 6000;

    @apply bg-slate-200;

    .map-center-symbol {
      $symbol-color: #d50000;
      $symbol-width: 40px;

      position: absolute;
      top: 50%;
      left: 50%;
      margin-left: -20.5px;
      margin-top: -20.5px;
      z-index: 10;

      &:before,
      &:after {
        content: "";
        display: table;
        position: absolute;
        top: 0;
        left: 0;
      }

      &:before {
        border-bottom: 1px solid $symbol-color;
        width: $symbol-width;
        margin-top: 20.5px;
      }

      &:after {
        border-right: 1px solid $symbol-color;
        height: $symbol-width;
        margin-left: 20.5px;
      }
    }

    .maplibregl-ctrl-group {
      .q-btn>.q-btn__content {
        @apply h-full;
      }
    }

    .maplibregl-popup .maplibregl-popup-close-button {
      padding: 0px 6px;
    }
  }
</style>@app/src/utils/maplibreLocals

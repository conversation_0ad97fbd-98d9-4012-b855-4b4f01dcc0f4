<template>
  <q-input
    v-model="filter"
    :label="$q.lang.label.search"
    :debounce="200"
    clearable
    dense
    borderless
    ref="filter"
  >
    <template v-slot:prepend>
      <q-icon
        name="search"
        color="primary"
      />
    </template>
    <template v-slot:after>
      <q-btn
        icon="menu"
        color="primary"
        class="cursor-pointer"
        round
        dense
        flat
      >
        <q-menu
          fit
          :offset="[0, 5]"
          transition-show="jump-down"
          transition-hide="jump-up"
          :class="{ 'z-max': true }"
        >
          <q-list class="fancytree-actions-menu">
            <q-item
              dense
              clickable
              @click="setSelectAll"
            >
              <q-item-section avatar>
                <q-icon
                  :name="isSelectAll ? 'check_box_outline_blank' : 'library_add_check'"
                  class="bys-selectall"
                  color="primary"
                />
              </q-item-section>
            </q-item>
            <q-item
              dense
              clickable
              @click="setExpandAll"
            >
              <q-item-section avatar>
                <q-icon
                  :name="isExpandAll ? 'unfold_less' : 'unfold_more'"
                  color="primary"
                />
              </q-item-section>
            </q-item>
          </q-list>
        </q-menu>
      </q-btn>
    </template>
  </q-input>
</template>

<script lang="ts">
  import { defineComponent } from 'vue'

  export default defineComponent({
    name: 'TreeFilter',
    props: {
      selectAll: { type: Boolean, default: true },
    },
    data() {
      return {
        filter: '',
        isSelectAll: this.selectAll,
        isExpandAll: true,
      }
    },
    methods: {
      setSelectAll() {
        this.$parent.tree?.selectAll(this.isSelectAll = !this.isSelectAll)
      },
      setExpandAll() {
        this.$parent.tree?.expandAll(this.isExpandAll = !this.isExpandAll)
      },
      filterNodes() {
        if (!this.filter) {
          return this.$parent.tree?.clearFilter()
        }
        this.$parent.tree?.filterNodes(this.filter, { ...this.$parent.tree?.options?.filter })
      },
    },
    watch: {
      filter() {
        this.filterNodes()
      },
    },
  })
</script>

<style lang="scss"></style>

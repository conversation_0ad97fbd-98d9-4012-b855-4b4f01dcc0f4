<template>
  <section class="fancytree-wrapper ext-fancytree full-height">
    <q-resize-observer @resize="onResize" />
    <!--    <tree-filter></tree-filter>-->
    <!--    <q-separator/>-->
    <div class="fancytree-grid-container fancytree-wrapper-container">
      <table
        :id="id"
        :class="[id]"
        ref="fancyTree"
      >
        <colgroup>
          <col width="*" />
        </colgroup>
        <thead>
          <tr>
            <th></th>
          </tr>
        </thead>
      </table>
      <div
        class='fancytree-ext-table-scroll-bar'
        ref='scrollBar'
      ></div>
    </div>
  </section>
</template>

<script lang="ts">
  import { defineComponent } from 'vue'
  import common from './common'
  import PlainScrollbar from './plain-scrollbar'
  import mobileScroller from './mobile-scroller'
  import 'jquery'
  import 'jquery.fancytree'

  export default defineComponent({
    name: 'ExtFancytree',
    mixins: [common],
    data() {
      return {
        oneNodeHeight: 22,
      }
    },
    methods: {
      onResize() {
        this.tree?.adjustViewportSize?.()
      },
      getViewportCount() {
        let count = Math.floor((this.$refs.fancyTree?.offsetHeight ?? 0) / this.oneNodeHeight)
        if (count === 10) { count++ }
        return count
      },
      updateTreeScrollbars(tree) {
        const scrollbar = tree.verticalScrollbar
        if (!scrollbar) {
          return
        }

        scrollbar.set({
          start: tree.viewport.start,
          total: tree.visibleNodeList.length,
          visible: tree.viewport.count,
        }, true)
      },
      init() {
        this.tree = $(this.$fancyTree).fancytree({
          ...this.treeOptions,
          extensions: ['filter', 'grid'],
          table: {
            indentation: 20,
            nodeColumnIdx: 0,
          },
          viewport: {
            enabled: true,
            count: this.getViewportCount(),
          },
          updateViewport: (event, data) => {
            this.updateTreeScrollbars(data.tree)
          },
          preInit: (event, data) => {
            const tree = data.tree
            tree.verticalScrollbar = new PlainScrollbar({
              // numberOfItems: { start: 0, total: 100, visible: 10 },
              orientation: 'vertical',
              onSet: (numberOfItems) => {
                tree.setViewport({
                  start: Math.round(numberOfItems.start),
                  count: this.getViewportCount(),
                })
              },
              scrollbarElement: this.$refs.scrollBar,
            })
          },
          init: (event, data) => {
            this.treeOptions.init?.(event, data)
            this.$nextTick(() => {
              mobileScroller(data.tree)
              // @ts-ignore
              data.tree.adjustViewportSize()
            })
          },
        })
      },
    },
  })
</script>

<style lang="scss">
  @import "./common";
  @import './plain-scrollbar.css';
  @import "quasar/src/css/variables";

  .ext-fancytree {

    .fancytree-grid-container {
      height: calc(100% - 40px);
      position: relative;
    }

    .fancytree-ext-grid.fancytree-ext-table {

      tbody tr,
      tbody tr:hover {
        line-height: 1;
        height: 22px;
        padding: 0;
        outline: none;
        background-color: unset;
      }

      tbody tr td {
        outline: none;
        border: none;
        background-color: unset;
        padding: 0;
        margin: 0;
      }

      /* 样式的行数据，则没有任何样式 */
      tbody tr:not([class]):hover,
      tbody tr:not([class]):focus {
        border: none;
        outline: none;
        background-color: unset;
      }

      tr:not([class *='fancytree-has-children']) {

        /*.fancytree-node .fancytree-expander:after {*/
        /*  display: block;*/
        /*  content: '';*/
        /*  width: 100%;*/
        /*  height: 100%;*/
        /*  background-image: url('jquery.fancytree/dist/skin-win8/vline.gif');*/
        /*  background-position: 0 0;*/
        /*  background-repeat: repeat-y;*/
        /*}*/
        .fancytree-node {
          .fancytree-expander {
            background-image: none;
            position: relative;

            $borderStyle: 1px solid $grey-5;

            &:before,
            &:after {
              display: block;
              content: '';
              position: absolute;
              left: 50%;
            }

            &:before {
              width: 50%;
              top: 50%;
              right: 0;
              border-top: $borderStyle;
            }

            &:after {
              top: 0;
              bottom: -50%;
              border-left: $borderStyle;
            }

          }
        }

        &.fancytree-lastsib,
        &:last-child {
          .fancytree-node {
            .fancytree-expander {
              &:after {
                bottom: 50%;
              }
            }
          }
        }
      }
    }
  }
</style>

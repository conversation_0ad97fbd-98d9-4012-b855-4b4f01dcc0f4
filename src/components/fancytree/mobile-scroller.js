function touchX(event) {
  return event.changedTouches[0].clientX
}

function touchY(event) {
  return event.changedTouches[0].clientY
}

function checkInvalidTarget(target) {
  return [
    'fancytree-expander',
    'fancytree-checkbox',
    'fancytree-icon',
    'fancytree-title',
  ].some(clsName => target.classList.contains(clsName))
}

export default function (tree, oneItemHeight = 22) {
  let touchStartPos = -1
  let touchStartTime = -1
  let ty = -1
  let dy = -1
  let speed = 1
  let speedTimer = null

  /**
   * 用于缓动的变量
   */
  let lastY = 0 // 上一次位置
  let lastMoveTime = 0

  const endFn = (e) => {
    // /**
    //  * 缓动代码
    //  */
    // let nowTime = e.timeStamp || Date.now()
    // let t = nowTime - lastMoveTime
    // // 时间差在300ms内则启用惯性滚动
    // if (t <= 300) {
    //   let nowY = touchY(e)
    //   let moveY = nowY - lastY
    //   let v = moveY / t //最后一段时间手指划动速度
    //   let f = 0
    //   let timer = true
    //   let row = Math.floor((dy / oneItemHeight) * speed)
    //
    //   const move = () => {
    //     console.log('endFn move', v, f, row)
    //     f = Math.min(Math.abs(v) / 32, 0.5)//重点
    //
    //     if (v > 0.2) {
    //       v -= f
    //     } else if (v < -0.2) {
    //       v += f
    //     } else {
    //       timer = false
    //       v = 0
    //       // return
    //     }
    //
    //     let start = tree.viewport.start + row * (v > 0? 1: -1)
    //     if (start > tree.visibleNodeList.length) {
    //       start = tree.visibleNodeList.length
    //     }
    //     if (start < 0) {
    //       start = 0
    //     }
    //     tree.setViewport({
    //       start,
    //     })
    //
    //     row -= row * Math.abs(v)
    //
    //     timer && requestAnimationFrame(move)
    //   }
    //   move()
    // }

    touchStartPos = -1
    touchStartTime = -1
    ty = -1
    dy = -1
  }

  tree.$container.on('touchstart', (e) => {
    if (e.changedTouches.length === 0) {
      return
    }

    if (checkInvalidTarget(e.changedTouches[0].target)) {
      return
    }

    e.preventDefault()
    touchStartPos = tree.viewport.start
    touchStartTime = e.timeStamp
    ty = touchY(e)

    // 累计滚动速度系数
    if (speedTimer) {
      clearTimeout(speedTimer)
    }
    speedTimer = setTimeout(() => {
      speed = 1
    }, 3000)
    speed = Math.min(speed * 1.6, 100)

    /**
     * 缓动代码
     */
    lastMoveTime = e.timeStamp || Date.now()
    lastY = ty
  })
  tree.$container.on('touchmove', (e) => {
    // 没有事件对象元素或者没有标记开始滑动则停止执行事件
    if (e.changedTouches.length === 0 || touchStartPos < 0) {
      return
    }
    // 过滤100ms内的重复事件
    if (e.timeStamp - touchStartTime < 100) {
      return
    }

    let viewport = tree.viewport
    dy = (touchY(e) - ty) * -1

    if (dy < 0 && viewport.start === 0) {
      return true
    }
    if (dy > 0 && tree.isViewportBottom()) {
      return true
    }

    let row = Math.floor((dy / oneItemHeight) * speed)

    let start = tree.viewport.start + row
    if (start > tree.visibleNodeList.length) {
      start = tree.visibleNodeList.length
    }
    tree.setViewport({
      start,
    })

    touchStartTime = e.timeStamp
    ty = touchY(e)
  })
  tree.$container.on('touchcancel', endFn)
  tree.$container.on('touchend', endFn)
}

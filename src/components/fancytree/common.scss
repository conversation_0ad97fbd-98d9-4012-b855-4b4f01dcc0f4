@import "jquery.fancytree/dist/skin-win8/ui.fancytree.css";
@import "jquery-ui/themes/base/all.css";

.fancytree-wrapper {
  position: relative;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  outline: none;

  .q-separator.q-separator--horizontal.col-grow {
    flex-grow: 0;
  }

  .fancytree-wrapper-container {
    // 搜索框40px，分隔线1px
    height: 100%;

    .fancytree-container {
      flex: auto;
      border: none;
      outline: none;
      width: 100%;
      height: 100%;
      overflow: auto;

      .fancytree-active,
      .fancytree-selected,
      .fancytree-active:hover,
      .fancytree-selected:hover,
      &.fancytree-treefocus .fancytree-selected,
      &.fancytree-treefocus .fancytree-active,
      &.fancytree-plain .fancytree-selected .fancytree-title {
        background-color: unset;
        outline: none;
      }

      &.fancytree-plain .fancytree-selected .fancytree-title {
        border-color: transparent;
      }

      .fancytree-node {
        display: inline-flex;
        align-items: center;

        & > * {
          flex: none;
        }

        & .fancytree-custom-icon {
          width: auto;
          height: 20px;
          margin-top: unset;
          align-self: end;
        }
      }

      .fancytree-node .fancytree-title {
        border-radius: 4px;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      //.fancytree-node,
      //.fancytree-title {
      //  display: inline-flex;
      //  align-items: center;
      //
      //  & .fancytree-expander,
      //  & .fancytree-checkbox {
      //    flex: none;
      //  }
      //}

      .fancytree-node .fancytree-title:hover,
      .fancytree-focused.fancytree-node .fancytree-title {
        border-color: #719acb;
        background: linear-gradient(180deg, #f2f9fd, #c4e8fa);
      }

      .fancytree-node,
      .fancytree-focused .fancytree-title {
        outline: none;
      }

      &.fancytree-plain .fancytree-node.fancytree-active .fancytree-title,
      .fancytree-node:hover .fancytree-title {
        background-color: #eff9fe;
        border-color: #70c0e7;
      }
    }

  }
}

.fancytree-actions-menu {
  .q-item__section--avatar {
    min-width: initial;
    padding-right: initial;
  }
}

.ui-helper-hidden.ui-contextmenu {
  z-index: 1001;
}

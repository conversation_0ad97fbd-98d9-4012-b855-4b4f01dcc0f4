import { defineComponent } from 'vue'
import { debounce } from 'quasar'
import 'jquery'
import 'jquery.fancytree'
import 'jquery.fancytree/dist/modules/jquery.fancytree.filter'
import 'jquery.fancytree/dist/modules/jquery.fancytree.grid'
import 'jquery-ui'
import 'jquery-ui/ui/safe-active-element'
import 'jquery-ui/ui/widgets/menu'
import 'ui-contextmenu'
// import TreeFilter from './TreeFilter.vue'

export default defineComponent({
  props: {
    treeSelMode: { type: Object },
    id: {
      type: String,
      default: Date.now() + '',
    },
    contextMenu: {
      type: Object,
    },
    options: {
      type: Object,
      default() {
        return {}
      },
    },
  },
  computed: {
    $fancyTree() {
      return this.$refs.fancyTree
    },
    treeOptions() {
      return {
        extensions: ['filter'],
        source: [],
        // autoScroll: true,
        // autoCollapse: true,
        checkbox: true,
        selectMode: 3,
        debugLevel: 0,
        sortChildren: true,
        filter: {
          hideExpanders: true, // 如果所有子节点都通过筛选隐藏，隐藏扩展
          // leavesOnly: true, // 是否只搜索叶子节点
          nodata: true, // 没有符合条件的节点，显示 nodata 提示
          // mode: 'hide', // "hide" or "dimm" 默认为hide,只显示符合条件的节点
        },
        strings: {
          noData: this.$q.lang.table.noResults,
        },
        nodata: false,

        init: (event, data) => {
          const treeContainer = data.tree.$container[0]
          treeContainer.classList.add('fancytree-connectors')
          this.tree = data.tree
          this.$emit('tree-init', data.tree)
        },

        select: debounce((...args: any[]) => {
          this.$emit('select', ...args)
        }, 100),
        dblclick: debounce((...args: any[]) => {
          this.$emit('dblclick', ...args)
        }, 100),
        click: debounce((...args: any[]) => {
          this.$emit('click', ...args)
        }, 100),
        ...this.options,
        ...this.treeSelMode,
      }
    },
  },
  methods: {
    init() {
      $(this.$fancyTree).fancytree(this.treeOptions)
      this.initContextmenu()
    },
    enableEntry(cmd: string, enable = true) {
      // @ts-ignore
      $(this.$fancyTree).contextmenu('enableEntry', cmd, enable)
    },
    showEntry(cmd: string, enable = true) {
      // @ts-ignore
      $(this.$fancyTree).contextmenu('showEntry', cmd, enable)
    },
    replaceMenu(replacedMenu: Array<contextMenuType>) {
      // @ts-ignore
      $(this.$fancyTree).contextmenu('replaceMenu', replacedMenu)
    },
    initContextmenu(contextMenu = this.contextMenu) {
      const option = {
        delegate: '.fancytree-node .fancytree-title',
        autoFocus: true,
        beforeOpen: (event, ui) => {
          const node = $.ui.fancytree.getNode(ui.target)
          if (!node) { return false }
          node.setActive()
          ui.extraData.node = node
        },
        close: (event, ui) => {
          const node = $.ui.fancytree.getNode(ui.target) || ui.extraData.node
          node && node.setFocus()
        },
        select: (event, ui) => {
          const node = $.ui.fancytree.getNode(ui.target) || ui.extraData.node
          if (!node) { return }
          this.$emit('contextMenu-select', ui, node)
        },
        ...contextMenu,
      }
      // @ts-ignore
      $(this.$fancyTree).contextmenu(option)
    },
  },
  watch: {
    contextMenu: {
      deep: true,
      handler(menu) {
        this.initContextmenu(menu)
      },
    },
    '$i18n.locale'() {
      this.initContextmenu()
      this.tree?.setOption?.('strings.noData', this.$q.lang.table.noResults)
    },
  },
  // components: {
  //   TreeFilter,
  // },
  mounted(): void {
    this.$nextTick(() => {
      this.init()
    })
  },
})

export interface contextMenuType {
  title: string,
  cmd: string,

  [key: string]: any
}

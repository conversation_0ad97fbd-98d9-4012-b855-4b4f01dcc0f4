<template>
  <section class="fancytree-wrapper full-height">
    <!--    <tree-filter :selectAll="isSelectAll"></tree-filter>-->
    <!--    <q-separator/>-->
    <div
      class="fancytree-wrapper-container"
      :id="id"
      ref="fancyTree"
    ></div>
  </section>
</template>

<script lang="ts">
  import { defineComponent } from 'vue'
  import common from './common'

  export default defineComponent({
    name: 'Fancytree',
    mixins: [common],
    props: {
      selectAll: { type: Boolean, default: true },
    },
    data() {
      return {
        isSelectAll: this.selectAll,
      }
    },
  })
</script>

<style lang="scss">
  @import "./common";
</style>

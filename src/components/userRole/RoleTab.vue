<template>
  <div>
    <q-bar class="bg-primary text-white">{{ $t('userTab.roleSelection') }}</q-bar>
    <div class="q-ma-sm">
      <div class="row">
        <div class="col-8 q-space">{{ $t('userTab.roleInfo') }}</div>
        <div class="col-2">{{ $t('userTab.checked') }}</div>
      </div>
      <q-separator />
      <div
        v-for="item in roleDataRow"
        :key="item.roleRID"
      >
        <div class="row">
          <div class="col-8 q-space text-left self-center">
            {{ getRoleLabel(item) }}
          </div>
          <q-checkbox
            class="col-2"
            v-model="item.checked"
            @update:model-value="onCheckboxClick(item)"
          />
        </div>
        <q-separator />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
  import { defineComponent } from 'vue'
  import { QueryFinishStatus } from '@src/services/queryData'
  import { DataName } from '@src/store'
  import { StrPubSub } from 'ypubsub'
  import { GET_DATA, NS } from '@store/data/methodTypes'
  import { user } from '@src/ygen/user'
  import { utcTime } from '@src/utils/dayjs'
  import { v1 as uuidV1 } from 'uuid'
  import { ICallOption } from 'jsyrpc'
  import { crud } from '@ygen/crud'
  import { yrpcmsg } from 'yrpcmsg'
  import { PrpcDbUserRole } from '@ygen/user.rpc.yrpc'
  import log from '@src/utils/log'
  import { unknownUnitData, UserRole } from '@src/services/dataStore'
  import { QueryRoleFinish } from '@utils/pubSubSubject'
  import { BuiltInAttrs, BuiltInAttrTranslate } from '@utils/common'
  import { org } from '@ygen/org'
  import { user as userPermission } from '@ygen/userPermission'
  import { BaseRoleRids, checkRoleAndUserError } from '@utils/permission'

  interface RoleDataRow {
    //在遍历所有role的时候已经获取
    roleRID: string
    roleName: string
    OrgRID: string

    //是否存在于user-role的标志
    RID: string
    checked: boolean
  }

  export default defineComponent({
    name: 'RoleTab',
    props: {
      userRID: String,
    },
    data() {
      return {
        roleDataRow: [] as RoleDataRow[],
      }
    },
    computed: {
      unitDataList(): Array<org.IDbOrg> {
        return this.$store.getters[`${NS}/${GET_DATA}`](DataName.Unit)
      },
      roleDataList(): Array<userPermission.IDbRole> {
        return this.$store.getters[`${NS}/${GET_DATA}`](DataName.Role)
          // 所有用户都有该角色，默认不显示出来
          .filter(item => !BaseRoleRids.includes(item.RID))
      },
      userRoleDataList(): Array<user.IDbUserRole> {
        return this.$store.getters[`${NS}/${GET_DATA}`](DataName.UserRole)
      },
      currentUserRoleDataList(): Array<user.IDbUserRole> {
        return this.userRoleDataList.filter(item => item.UserRID === this.userRID)
      },
      currentUser(): user.IDbUser | undefined {
        return this.$store.getters[`${NS}/${GET_DATA}`](DataName.User, this.userRID)
      },
      // 登录用户的所有用户角色关系数据数组
      loginUserUserRoles(): Array<user.IDbUserRole> {
        return this.userRoleDataList.filter(item => item.UserRID === this.$store.state.UserRID)
      },
      // 打出登录用户的所有有权限的角色数据
      loginUserRoles(): Array<userPermission.IDbRole> {
        return this.roleDataList.filter(item => this.loginUserUserRoles.some(v => v.RoleRID === item.RID))
      },
    },
    methods: {
      formatBuildInAttrs(str: string): string {
        return BuiltInAttrs.includes(str) ? BuiltInAttrTranslate()[str] : str
      },
      getUnitNameByOrgRID(orgRID): string {
        const ret: org.IDbOrg = this.unitDataList.concat(unknownUnitData).find(item => item.RID === orgRID)
        return ret?.ShortName ?? ''
      },
      getRoleLabel(item: RoleDataRow): string {
        return `${this.formatBuildInAttrs(this.getUnitNameByOrgRID(item.OrgRID))} / ${this.formatBuildInAttrs(item.roleName)}`
      },
      initRoleTabData() {
        // 只能分配当前登录用户所拥有的角色
        this.roleDataRow = this.loginUserRoles.map(role => {
          const userRoleItem = this.currentUserRoleDataList.find(temp => temp.RoleRID === role.RID)
          const roleData: RoleDataRow = {
            roleRID: role.RID,
            roleName: role.RoleName,
            OrgRID: role.OrgRID,
            RID: userRoleItem?.RID ?? '',
            checked: !!userRoleItem,
          }
          return roleData
        })
      },
      syncRoleDataStatus(data: user.IDbUserRole, roleOption: RoleDataRow) {
        const index = this.roleDataRow.findIndex(item => item.RID === roleOption.RID)
        if (index < 0) { return }

        this.$set(this.roleDataRow, index, {
          ...this.roleDataRow[index],
          RID: data.RID,
        })
      },
      onCheckboxClick(roleOption: RoleDataRow) {
        // insert
        if (roleOption.checked) {
          const userRole: user.IDbUserRole = {
            RID: uuidV1(),
            OrgRID: this.currentUser?.OrgRID,
            UserRID: this.userRID,
            RoleRID: roleOption.roleRID,
            UpdatedAt: utcTime(),
          }
          this.insertIntoUserRole(userRole)
            .then(() => {
              //同步store
              UserRole.setData(userRole.RID as string, userRole)
              this.syncRoleDataStatus(userRole, roleOption)
            })
            .catch(() => {
              roleOption.checked = !roleOption.checked
            })

          return
        }

        // delete
        const userRole = this.currentUserRoleDataList.find(item => item.RoleRID === roleOption.roleRID)
        if (!userRole) { return }

        this.deleteIntoUserRole(userRole)
          .then(() => {
            //同步store
            UserRole.deleteData(userRole.RID as string)
          })
          .catch(() => {
            roleOption.checked = !roleOption.checked
          })
      },

      /*API请求*/
      insertIntoUserRole(data: user.IDbUserRole): Promise<boolean> {
        return new Promise((resolve, reject) => {
          let options: ICallOption = {
            OnResult: (res: crud.IDMLResult, rpcCmd: yrpcmsg.Ymsg, meta?: yrpcmsg.IGrpcMeta) => {
              log.info('PrpcDbUserRole Insert result', res, rpcCmd, meta)
              if (res.AffectedRow === 0) {
                reject()
              } else {
                resolve(true)
              }
            },
            OnServerErr: (errRpc: yrpcmsg.Ymsg) => {
              log.error('PrpcDbUserRole Insert server error', data, errRpc)
              // 处理服务器响应的错误
              let reason = checkRoleAndUserError(errRpc)
              this.$q.notify({
                message: reason as string,
                position: 'top',
                type: 'negative',
              })
            },
            OnLocalErr: (err: any) => {
              log.error('PrpcDbUserRole Insert local error', err)
              this.$q.notify({
                message: err.toString(),
                position: 'top',
                type: 'negative',
              })
            },
            OnTimeout: (v: any) => {
              log.warn('PrpcDbUserRole Insert timeout', v)
              this.$q.notify({
                message: this.$t('message.requestTimeout') as string,
                position: 'top',
                type: 'warning',
              })
            },
          }
          PrpcDbUserRole.Insert(data, options)
        })
      },
      deleteIntoUserRole(data: user.IDbUserRole): Promise<boolean> {
        return new Promise((resolve, reject) => {
          let options: ICallOption = {
            OnResult: (res: crud.IDMLResult, rpcCmd: yrpcmsg.Ymsg, meta?: yrpcmsg.IGrpcMeta) => {
              log.info('PrpcDbUserRole Delete result', res, rpcCmd, meta)
              if (res.AffectedRow === 0) {
                reject()
              } else {
                resolve(true)
              }
            },
            OnServerErr: (errRpc: yrpcmsg.Ymsg) => {
              log.error('PrpcDbUserRole Delete server error', data, errRpc)
              // 处理服务器响应的错误
              let reason = checkRoleAndUserError(errRpc, this.$t('message.deleteFailed'))
              this.$q.notify({
                message: reason as string,
                position: 'top',
                type: 'negative',
              })
            },
            OnLocalErr: (err: any) => {
              log.error('PrpcDbUserRole Delete local error', err)
              this.$q.notify({
                message: err.toString(),
                position: 'top',
                type: 'negative',
              })
            },
            OnTimeout: (v: any) => {
              log.warn('PrpcDbUserRole Delete timeout', v)
              this.$q.notify({
                message: this.$t('message.requestTimeout') as string,
                position: 'top',
                type: 'warning',
              })
            },
          }
          PrpcDbUserRole.Delete(data, options)
        })
      },
      /*API请求*/
    },
    watch: {
      roleDataList() {
        this.initRoleTabData()
      },
      currentUserRoleDataList() {
        this.initRoleTabData()
      },
      unitDataList() {
        this.initRoleTabData()
      },
    },
    beforeMount() {
      // 判断是否完成角色数据请求，如果没有完成，则在完成请求时，生成界面数据
      if (!QueryFinishStatus[DataName.Role]) {
        StrPubSub.subscribeOnce(QueryRoleFinish, this.initRoleTabData)
        return
      }
      this.initRoleTabData()
    },
    beforeUnmount() {
      StrPubSub.unsubscribe(QueryRoleFinish, this.initRoleTabData)
    },
  })
</script>

<style type="scss"></style>


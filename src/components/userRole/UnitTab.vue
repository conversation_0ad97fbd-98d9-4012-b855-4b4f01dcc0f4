<template>
  <div>
    <q-bar class="bg-primary text-white">{{ $t('userTab.unitSelection') }}</q-bar>
    <fancytree
      class="unit-fancy-cls"
      :options="deviceTreeOptions"
      @tree-init="treeInit"
      @select="selected"
      :treeSelMode="{ selectMode: 2 }"
    />
  </div>
</template>

<script lang="ts">
  import { defineComponent } from 'vue'
  import { Fancytree } from '@src/components/fancytree'
  import { Unit, UserOrgPrivilege } from '@src/services/dataStore'
  import { DefUuid } from '@src/config'
  import { createTreeUnitNodes, lazyLoadUnitNodes, sortNodeChildren } from '@src/utils/tree'
  import { org } from '@ygen/org'
  import { GET_DATA, NS } from '@src/store/data/methodTypes'
  import { DataName } from '@src/store/data'
  import { user as orgPrivilege } from '@ygen/userOrgPrivilege'
  import log from '@src/utils/log'
  import { utcTime } from '@src/utils/dayjs'
  import { v1 as uuidV1 } from 'uuid'
  import { ICallOption } from 'jsyrpc'
  import { crud } from '@ygen/crud'
  import { yrpcmsg } from 'yrpcmsg'
  import { PrpcDbUserOrgPrivilege } from '@ygen/userOrgPrivilege.rpc.yrpc'
  import { user } from '@ygen/user'
  import { checkRoleAndUserError } from '@utils/permission'
  import { StrPubSub } from 'ypubsub'
  import { DeleteUserOrgPrivilege, InsertUserOrgPrivilege, UpdateUserOrgPrivilege } from '@utils/pubSubSubject'

  let deviceTree: any = undefined
  export default defineComponent({
    name: 'UnitTab',
    props: {
      userRID: {
        type: String,
        required: true,
      },
    },
    computed: {
      deviceTreeOptions() {
        return {
          lazyLoad: this.lazyLoad,
        }
      },
      allCurrentUserOrgPrivilege(): Array<orgPrivilege.IDbUserOrgPrivilege> {
        return this.$store.getters[`${NS}/${GET_DATA}`](DataName.UserOrgPrivilege)
          .filter(item => item.UserRID === this.userRID)
      },
      userData(): user.IDbUser | undefined {
        return this.$store.getters[`${NS}/${GET_DATA}`](DataName.User, this.userRID)
      },
    },
    methods: {
      lazyLoad(event, data) {
        if (!data.node) {
          data.result = []
          return
        }

        // 通过key查找该节点的所有直接子级单位、界桩、控制器节点
        const unitNodes = lazyLoadUnitNodes(data.node.key)
        data.result = [...unitNodes]

        // 对懒加载节点的子级节点进行排序
        this.$nextTick(() => {
          sortNodeChildren(data.node)
        })
      },
      /*Api请求*/
      insertIntoUserOrgPrivilege(data: orgPrivilege.IDbUserOrgPrivilege): Promise<boolean> {
        return new Promise((resolve, reject) => {
          let options: ICallOption = {
            OnResult: (res: crud.IDMLResult, rpcCmd: yrpcmsg.Ymsg, meta?: yrpcmsg.IGrpcMeta) => {
              log.info('UserOrgPrivilege Insert result', res, rpcCmd, meta)
              if (res.AffectedRow === 0) {
                reject()
              } else {
                resolve(true)
              }
            },
            OnServerErr: (errRpc: yrpcmsg.Ymsg) => {
              log.error('UserOrgPrivilege Insert server error', data, errRpc)
              // 处理服务器响应的错误
              let reason = checkRoleAndUserError(errRpc)
              this.$q.notify({
                message: reason as string,
                position: 'top',
                type: 'negative',
              })
            },
            OnLocalErr: (err: any) => {
              log.error('UserOrgPrivilege Insert local error', err)
              this.$q.notify({
                message: err.toString(),
                position: 'top',
                type: 'negative',
              })
            },
            OnTimeout: (v: any) => {
              log.warn('UserOrgPrivilege Insert timeout', v)
              this.$q.notify({
                message: this.$t('message.requestTimeout') as string,
                position: 'top',
                type: 'warning',
              })
            },
          }
          PrpcDbUserOrgPrivilege.Insert(data, options)
        })
      },
      deleteUserOrgPrivilege(data: orgPrivilege.IDbUserOrgPrivilege): Promise<boolean> {
        return new Promise((resolve, reject) => {
          let options: ICallOption = {
            OnResult: (res: crud.IDMLResult, rpcCmd: yrpcmsg.Ymsg, meta?: yrpcmsg.IGrpcMeta) => {
              log.info('UserOrgPrivilege Delete result', res, rpcCmd, meta)
              if (res.AffectedRow === 0) {
                reject()
              } else {
                resolve(true)
              }
            },
            OnServerErr: (errRpc: yrpcmsg.Ymsg) => {
              log.error('UserOrgPrivilege Delete server error', data, errRpc)
              // 处理服务器响应的错误
              let reason = checkRoleAndUserError(errRpc, this.$t('message.deleteFailed'))
              this.$q.notify({
                message: reason as string,
                position: 'top',
                type: 'negative',
              })
            },
            OnLocalErr: (err: any) => {
              log.error('UserOrgPrivilege Delete local error', err)
              this.$q.notify({
                message: err.toString(),
                position: 'top',
                type: 'negative',
              })
            },
            OnTimeout: (v: any) => {
              log.warn('UserOrgPrivilege Delete timeout', v)
              this.$q.notify({
                message: this.$t('message.requestTimeout') as string,
                position: 'top',
                type: 'warning',
              })
            },
          }
          PrpcDbUserOrgPrivilege.Delete(data, options)
        })
      },
      /*Api请求*/
      getUserOrgPrivilege(orgRID): orgPrivilege.IDbUserOrgPrivilege | undefined {
        return this.allCurrentUserOrgPrivilege.find(item => item.OrgRID === orgRID)
      },
      resetNodeLastSelected(node, isSelected: boolean) {
        if (!node) { return }
        node.setSelected(isSelected)
      },
      selected(event, data) {
        if (!data.node) { return }

        // 当前操作的树节点
        const selectedNode = data.node
        const isSelected = selectedNode.isSelected()
        let orgPrivilegeData: orgPrivilege.IDbUserOrgPrivilege | undefined = this.getUserOrgPrivilege(selectedNode.key)

        // 当前节点的权限数据，如果不存在且节点为选中状态，则添加权限
        if (isSelected && !orgPrivilegeData) {
          orgPrivilegeData = {
            RID: uuidV1(),
            UserRID: this.userRID,
            OrgRID: selectedNode.key,
            IncludeChildren: 0,
            Setting: '{}',
            UpdatedAt: utcTime(),
          }
          this.insertIntoUserOrgPrivilege(orgPrivilegeData)
            .then(() => {
              orgPrivilegeData && UserOrgPrivilege.setData(orgPrivilegeData.RID as string, orgPrivilegeData)
            })
            .catch(() => {
              this.resetNodeLastSelected(selectedNode, !isSelected)
            })

          return
        }

        // 如果当前节点没有选中且有权限数据，则删除权限
        if (!isSelected && orgPrivilegeData) {
          this.deleteUserOrgPrivilege(orgPrivilegeData)
            .then(() => {
              orgPrivilegeData && UserOrgPrivilege.deleteData(orgPrivilegeData.RID as string)
            })
            .catch(() => {
              this.resetNodeLastSelected(selectedNode, !isSelected)
            })

          return
        }
      },
      treeInit(tree) {
        const unitData: Array<org.IDbOrg> = Unit.getDataList().filter(item => item.RID !== DefUuid)
        const unitNodes = createTreeUnitNodes(unitData)
        tree?.rootNode?.addChildren?.(unitNodes)
        tree?.selectAll(false)
        deviceTree = tree

        // 根据角色的权限情况判断是否选中
        let selected: string[] = this.allCurrentUserOrgPrivilege.map(item => item.OrgRID + '')
        selected.forEach(orgRid => {
          tree.getNodeByKey(orgRid)?.setSelected(true)
        })

        // 检测用户的单位是否被选中，如果没有，则默认选中
        const userParentData = Unit.getData(this.userData?.OrgRID)
        if (userParentData) {
          tree.getNodeByKey(userParentData.RID)?.setSelected(true)
        }
      },

      gotInsertUserOrgPrivilege(privilege: orgPrivilege.IDbUserOrgPrivilege) {
        if (privilege.UserRID !== this.userRID) { return }
        deviceTree.getNodeByKey(privilege.OrgRID)?.setSelected(true)
      },
      gotUpdateUserOrgPrivilege() {
        //do nothing.
      },
      gotDeleteUserOrgPrivilege(privilege: orgPrivilege.IDbUserOrgPrivilege) {
        if (privilege.UserRID !== this.userRID) { return }
        deviceTree.getNodeByKey(privilege.OrgRID)?.setSelected(false)
      },
    },
    components: {
      Fancytree,
    },
    beforeMount() {
      StrPubSub.subscribe(InsertUserOrgPrivilege, this.gotInsertUserOrgPrivilege)
      StrPubSub.subscribe(UpdateUserOrgPrivilege, this.gotUpdateUserOrgPrivilege)
      StrPubSub.subscribe(DeleteUserOrgPrivilege, this.gotDeleteUserOrgPrivilege)
    },
    beforeUnmount() {
      StrPubSub.unsubscribe(InsertUserOrgPrivilege, this.gotInsertUserOrgPrivilege)
      StrPubSub.unsubscribe(UpdateUserOrgPrivilege, this.gotUpdateUserOrgPrivilege)
      StrPubSub.unsubscribe(DeleteUserOrgPrivilege, this.gotDeleteUserOrgPrivilege)
    },
  })
</script>

<style type="scss">
  .unit-fancy-cls {
    max-height: 45vh;
    overflow: auto;
  }
</style>

<template>
  <div class="flex no-wrap date-range-picker" :class="$q.platform.is.android ? 'flex-col items-start' : ''">
    <template v-if="$slots.before">
      <slot name="before"></slot>
    </template>
    <div class="grow date-pick-wrapper">
      <div
        class="date-pick-control flex gap-4 px-2 no-wrap"
        :class="[disable ? '' : 'date-pick-control-focus']"
        tabindex="0"
        @focus="handleFocus"
        @blur="handleBlur">
        <q-input
          ref="startRef"
          v-model="dateRange.from"
          dense
          no-error-icon
          :disable="disable"
          :rules="baseRules.startRules"
          bottom-slots
          @blur="inputRefBlur"
          @update:model-value="startDateUpdate"
          class="date-pick w-auto">
          <template v-slot:prepend>
            <q-icon name="event">
              <q-popup-proxy
                transition-show="scale"
                transition-hide="scale"
              >
                <q-date
                  v-model="dateRange"
                  :mask="mask"
                  range
                  :options="dateOptions"
                  :disable="disable"
                  class="date-range-dialog"
                  @update:model-value="dateRangeUpdate"
                >
                  <template v-if="$slots.dateDefault">
                    <slot name="dateDefault"></slot>
                  </template>
                  <div v-else class="flex gap-2">
                    <q-btn :label="$t('form.oneMonth')" color="primary" dense flat v-close-popup @click="setDateRange(1, 'month')"/>
                    <q-btn :label="$t('form.twoMonth')" color="primary" dense flat v-close-popup @click="setDateRange(3, 'month')"/>
                    <q-btn :label="$t('form.threeMonth')" color="primary" dense flat v-close-popup @click="setDateRange(6, 'month')"/>
                    <q-btn :label="$t('form.oneYear')" color="primary" dense flat v-close-popup @click="setDateRange(1, 'year')"/>
                    <q-btn :label="$t('form.threeYear')" color="primary" dense flat v-close-popup @click="setDateRange(3, 'year')"/>
                  </div>
                </q-date>
              </q-popup-proxy>
            </q-icon>
          </template>
        </q-input>
        <div class="leading-[40px]" :style="{ color: disable ? '#acacac': '#757575' }">
          <template v-if="$slots.rangeSeparator">
            <slot name="rangeSeparator"></slot>
          </template>
          <span v-else>~</span>
        </div>
        <q-input
          ref="endRef"
          v-model:model-value="dateRange.to"
          dense
          :disable="disable"
          bottom-slots
          :rules="baseRules.endRules"
          @blur="inputRefBlur"
          @update:model-value="endDateUpdate"
          class="date-pick w-auto">
          <template v-if="$slots.dateRangeAppend">
            <slot name="dateRangeAppend"></slot>
          </template>
          <template #append>
            <q-btn
              rounded
              dense
              flat
              unelevated
              class="clear-btn"
              size="md"
              icon="cancel"
              v-show="(dateRange.from || dateRange.to) && !disable"
              @click="clearDateRange"
            />
          </template>
        </q-input>
      </div>
      <div class="bottom-tip">
        <transition name="bottom-tip">
          <template v-if="$slots.bottomSlots">
            <slot name="bottomSlots"></slot>
          </template>
          <div v-else v-show="!isNormal">
            <span>{{ bottomSlotsContent }}</span>
          </div>
        </transition>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, customRef, computed, watch, nextTick } from 'vue'
  import { DateMask, dayjs, dayjsFormat, isBeforeOrSame, toLocalTime, toUtcTime } from '@utils/dayjs'
  import { ManipulateType } from 'dayjs'
  import { QInput, ValidationRule } from 'quasar'
  import { isValidDay } from '@utils/validation'
  import { i18n } from '@boot/i18n'

  interface IDateRange {
    start: string
    end: string
  }

  /* 定义接收的props参数 */
  const props = withDefaults(
    defineProps<{
      modelValue: {
        start: string
        end: string
      }
      mask?: string
      disable?: boolean
      rules?: {
        startRules: ValidationRule[],
        endRules: ValidationRule[]
      },
      bottomSlotsContent?: string,
    }>(),
    {
      mask: DateMask,
      bottomSlotsContent: i18n.global.t('form.dateRangeBottomTip')
    }
  )

  const endRef = ref<InstanceType<typeof QInput>>()
  const startRef = ref<InstanceType<typeof QInput>>()
  const mask = props.mask
  const dateRange = customRef(() => {
    return {
      get() {
        return {
          from: props.modelValue.start ? toLocalTime(props.modelValue.start, mask) : '',
          to: props.modelValue.end ? toLocalTime(props.modelValue.end, mask) : '',
        }
      },
      set(newVal) {
        // 点击以选中日期范围内的日期会返回null，得手动清空范围重新选择
        if (newVal === null) {
          emits('update:model-value', { start: '', end: ''})
          return
        }
        // 开始日期和结束日期选择同一天时，只会返回这天的字符串
        if (typeof newVal === 'string') {
          emits('update:model-value', { start: toUtcTime(newVal+' '+'00:00:00'), end: toUtcTime(newVal+' '+'23:59:59') })
          return
        }
        emits('update:model-value', {
          start: newVal.from ? toUtcTime(newVal.from+' '+'00:00:00') : '',
          end: newVal.to ? toUtcTime(newVal.to+' '+'23:59:59') : '',
        })
      }
    }
  })
  const baseRules = computed(() => {
    return {
      startRules: [
        (val) => (!!val && !!dateRange.value.to) || (!val === !dateRange.value.to),
        (val) => isValidDay(val),
        (val) => (!val && !dateRange.value.to) || isBeforeOrSame(val, dateRange.value.to),
      ],
      endRules: [
        (val) => (!dateRange.value.from && !val) || (!!dateRange.value.from && !!val),
        (val) => isValidDay(val),
        (val) => (!dateRange.value.from && !val) || isBeforeOrSame( dateRange.value.from, val),
      ]
    }
  })

  const isNormal = ref<boolean>(true)

  function dateOptions(date) {
    // date picker组件的options年月日只支持格式为YYYY/MM/DD
    return date >= toLocalTime(dayjs(), 'YYYY/MM/DD')
  }

  function setDateRange(num: number, addMask: ManipulateType) {
    const today = dayjs()
    dateRange.value = { from: dayjsFormat(today, mask), to: dayjsFormat(today.add(num, addMask), mask)}
    validate()
  }

  function clearDateRange() {
    emits('clear')
    dateRange.value = { from: '', to: ''}
    endRef.value?.blur()
  }

  function validate() {
    nextTick(() => {
      const endNormal = endRef.value?.validate() as boolean
      const startNoral = startRef.value?.validate() as boolean
      isNormal.value = endNormal && startNoral
    })
  }

  function startDateUpdate(val) {
    if (isValidDay(val)) {
      const nowDate = dayjs(new Date()).format(props.mask)
      dateRange.value = { from: isBeforeOrSame(val, nowDate) ? nowDate : val, to: dateRange.value.to }
      validate()
    }
  }

  function endDateUpdate(val) {
    if (isValidDay(val)) {
      const nowDate = dayjs(new Date()).format(props.mask)
      dateRange.value = { from: dateRange.value.from, to: isBeforeOrSame(val, nowDate) ? nowDate : val }
      validate()
    }
  }

  function inputRefBlur() {
    validate()
  }

  function dateRangeUpdate() {
    validate()
  }

  watch(() => props.disable, (val) => {
    if (val) {
      startRef.value?.resetValidation()
      endRef.value?.resetValidation()
      isNormal.value = true
    }
  })

    /* 定义向父组件传递的事件 */
  const emits = defineEmits<{
    'update:model-value': [val: IDateRange],
    blur: [evt: Event],
    focus: [evt: Event],
    clear,
  }>()
  function handleBlur(evt: Event) {
    emits('blur', evt)
  }
  function handleFocus(evt: Event) {
    emits('focus', evt)
  }
</script>

<style lang="scss">
  .date-range-picker {
    width: auto;

    .date-pick-control {
      position: relative;
      border-radius: 4px;

      &::after {
        content: "";
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        pointer-events: none;
        transform: scale3d(1, 1, 1);
        height: inherit;
        border-radius: inherit;
        border: 1px solid #dcdee2;
      }
      &:has(.q-field--error)::after {
        border: 2px solid $negative;
      }
      &:focus-within {
        &.date-pick-control-focus::after {
          border: 2px solid $primary;
          transition: border-color 0.36s cubic-bezier(0.4, 0, 0.2, 1);
        }
      }
      &:focus-within {
        &.date-pick-control-focus:has(.q-field--error)::after {
          border: 2px solid $negative;
          transition: border-color 0.36s cubic-bezier(0.4, 0, 0.2, 1);
        }
      }

      // & > :first-child {
      //   .q-field__inner .q-field__control .q-field__append {
      //     display: none;
      //   }
      // }

      .date-pick  {
        padding-bottom: 0;
        .clear-btn {
          color: #757575;
        }
        .q-field__inner {
          .q-field__control::before {
            border: none;
          }
          .q-field__control::after {
            height: 0 !important;
          }
          input {
            text-align: center;
          }
          .q-field__bottom {
            display: none;
          }
        }
      }
    }
    .bottom-tip {
      height: 12px;
      font-size: 11px;
      line-height: 11px;
      padding: 0 12px;
      padding-top: 1px;
      color: $negative;
    }
    .bottom-tip-leave-active {
      transform: translateY(-100%);
      color: #b3b3b3;
      transition: opacity 0.5s ease-in-out, transform 0.5s ease-in-out;
    }
    .bottom-tip-enter-active {
      transition: opacity 0.5s ease-in-out, transform 0.5s ease-in-out;
    }
    .bottom-tip-enter-from {
      opacity: 0;
      transform: translateY(-100%);
    }
    .bottom-tip-leave-to {
      opacity: 0;
    }
  }

  .date-range-dialog.q-date {
    width: 350px;
    .q-date__main {
      display: flex;
      flex-direction: column;
      flex-wrap: nowrap;
    }
    .q-date__actions {
      border-right: 1px solid #dcdee2;
      padding: 0 10px 10px;
    }
  }
</style>

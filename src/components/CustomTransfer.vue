<template>
  <div class="non-selectable transfer" :class="$q.platform.is.android ? 'flex-col' : 'flex' ">
    <div class="list-panel left-panel">
      <div class="panel-header">
        <q-checkbox v-model="leftSelectAll" dense :label="title.left" @update:model-value="leftSelectAllChange"
                    :disable="leftDisableSelectAll"></q-checkbox>
        <div class="select-info">

          <template v-if="leftListView.length > 0">
            <span>{{ leftCurrentSelected.size }}</span>
            /
          </template>
          <span>{{ leftListView.length }}</span>
        </div>
      </div>

      <div v-if="filterable" class="panel-filter">
        <q-input outlined dense clearable v-model="leftFilter" :debounce="500" :placeholder="$t('form.enterFilterKey')"
                 :maxlength="16">
          <template v-slot:prepend>
            <q-icon name="search"/>
          </template>
        </q-input>
      </div>

      <q-scroll-area :thumb-style="thumbStyle" :id="leftScrollAreaId" class="panel-scroll-area">
        <q-virtual-scroll v-if="leftListView.length > 0" :scroll-target="leftScrollTarget" :items="leftListView"
                          v-slot="{ item, index }">
          <q-item :key="index" dense>
            <q-checkbox v-model="item[propConfig.selected]" dense :label="item[propConfig.label]"
                        @update:model-value="(value, evt) => leftSelectItem(index, item, evt)"
                        :disable="item[propConfig.disable]"/>
          </q-item>
        </q-virtual-scroll>
        <div v-else class="text-center text-subtitle1">
          <template v-if="!!leftFilter">{{ $t('form.noMatchData') }}</template>
          <template v-else>{{ $t('form.noData') }}</template>
        </div>
      </q-scroll-area>
    </div>

    <template v-if="$slots.buttons">
      <div class="action-panel">
        <slot
          name="buttons"
          :disable="{
            right: leftCurrentSelected.size === 0,
            left: rightCurrentSelected.size === 0,
          }"
          :clickFunc="{
            right: moveToRight,
            left: moveToLeft,
          }"
        >
        </slot>
      </div>
    </template>
    <div v-else class="action-panel flex-none flex justify-center items-center" :class="$q.platform.is.android ? 'flex-row m-2 action-panel-mobile' : 'flex-col'">
      <q-btn class="move-right" color="primary" icon="chevron_right" @click="moveToRight"
             :disable="leftCurrentSelected.size === 0"></q-btn>

      <q-btn class="move-left" color="primary" icon="chevron_left" @click="moveToLeft"
             :disable="rightCurrentSelected.size === 0"></q-btn>
    </div>

    <div class="list-panel right-panel" :class="validRes.pass ? '' : 'valid-err'">
      <div class="panel-header">
        <q-checkbox v-model="rightSelectAll" dense :label="title.right" @update:model-value="rightSelectAllChange"
                    :disable="rightListView.length === 0"></q-checkbox>

        <div class="select-info">
          <template v-if="rightListView.length > 0">
            <span>{{ rightCurrentSelected.size }}</span>
            /
          </template>
          <span>{{ rightListView.length }}</span>
        </div>
      </div>

      <div v-if="filterable" class="panel-filter">
        <q-input outlined dense clearable v-model="rightFilter" :debounce="500" :placeholder="$t('form.enterFilterKey')"
                 :maxlength="16">
          <template v-slot:prepend>
            <q-icon name="search"/>
          </template>
        </q-input>
      </div>

      <q-scroll-area :thumb-style="thumbStyle" :id="rightScrollAreaId" class="panel-scroll-area">
        <q-virtual-scroll v-if="rightListView.length>0" :scroll-target="rightScrollTarget" :items="rightListView"
                          v-slot="{ item, index }">
          <q-item :key="index" dense>
            <q-item-section>
              <q-checkbox v-model="item[propConfig.selected]" dense :label="item[propConfig.label]"
                          @update:model-value="(value, evt) => rightSelectItem(index, item, evt)"/>
            </q-item-section>
          </q-item>
        </q-virtual-scroll>
        <div v-else class="text-center text-subtitle1">
          <template v-if="!!rightFilter">{{ $t('form.noMatchData') }}</template>
          <template v-else>{{ $t('form.noData') }}</template>
        </div>
      </q-scroll-area>
    </div>
  </div>
  <div v-if="!validRes.pass" class="text-right text-negative">{{ validRes.tip.join(',') }}</div>
</template>

<script lang="ts" setup generic="T extends Record<string, any>">
  import { computed, onMounted, ref, shallowReactive } from 'vue'
  import { useI18n } from 'vue-i18n'
  import { useFormChild, ValidationRule } from 'quasar'

  interface ListPropConfig {
    key?: 'string',
    label?: 'string',
    selected?: 'string',
    disable?: 'string'
  }

  const i18n = useI18n()

  /* 定义接收的props参数 */
  const props = withDefaults(
    defineProps<{
      sourceList: T[]
      modelValue: T[]
      titles?: {
        left?: string
        right?: string
      }
      filterable?: boolean
      listPropConfig?: ListPropConfig
      transferPanelHeight?: string
      rules?: ValidationRule[]
    }>(),
    {
      titles: () => {
        return {}
      },
      filterable: true,
      transferPanelHeight: '350px',
    }
  )

  const emits = defineEmits<{
    'update:model-value': [val: Record<string, any>]
  }>()

  const propConfig = computed(() => {
    return {
      key: 'key',
      label: 'label',
      selected: 'selected',
      disable: 'disable', ...props.listPropConfig
    }
  })
  const source = computed(() => {
    return props.sourceList.filter(item => !props.modelValue.some(v => v[propConfig.value.key] === item[propConfig.value.key]))
  })
  const title = computed(() => {
    return {
      left: i18n.t('form.transferSourceTit'),
      right: i18n.t('form.transferTargetTit'),
      ...props.titles
    }
  })
  const thumbStyle: Partial<CSSStyleDeclaration> = {
    borderRadius: '8px',
    backgroundColor: '#027be3',
    width: '8px',
    opacity: '0.75'
  }

  function useTransferPanel(source) {
    const list = ref(source)
    const filter = ref('')
    const listView = computed(() => {
      if (!filter.value) return list.value
      return list.value.filter((item) => {
        return item[propConfig.value.label]
          .toLocaleLowerCase()
          .includes(filter.value.toLocaleLowerCase())
      })
    })
    const currentSelected = shallowReactive(new Set())
    const selectAll = ref<boolean | null>(false)
    // let lastSelected = undefined
    let lastSelectedIndex = -1

    function selectAllChange(selected) {
      for (let i = 0; i < listView.value.length; i++) {
        const data = listView.value[i]
        if (data[propConfig.value.disable]) continue

        data[propConfig.value.selected] = selected
        if (selected) {
          currentSelected.add(data)
        } else {
          currentSelected.delete(data)
        }
      }

      // 全选状态变更，取消Shift键快捷功能缓存参数
      // lastSelected = undefined
      lastSelectedIndex = -1
    }

    // 支持按住shift键进行快速选择，类似于文件资源管理器的功能
    function selectItem(index, item, evt) {
      if (item[propConfig.value.selected]) {
        currentSelected.add(item)
        // 按shift快速选择，将两个节点之间的所以节点都设置选中状态
        if (lastSelectedIndex !== -1 && evt.shiftKey) {
          const min = Math.min(index, lastSelectedIndex)
          const max = Math.max(index, lastSelectedIndex)
          for (let i = min; i < max; i++) {
            const val = listView.value[i]
            if (val[propConfig.value.disable]) continue

            val[propConfig.value.selected] = true
            currentSelected.add(val)
          }
        }
      } else {
        currentSelected.delete(item)
        // 按shift快速选择，将两个节点之间的所以节点都取消选中状态
        if (lastSelectedIndex !== -1 && evt.shiftKey) {
          const min = Math.min(index, lastSelectedIndex)
          const max = Math.max(index, lastSelectedIndex)
          for (let i = min; i < max; i++) {
            const val = listView.value[i]
            if (val[propConfig.value.disable]) continue

            val[propConfig.value.selected] = false
            currentSelected.delete(val)
          }
        }
      }

      // 处理全选状态
      // 如果选中的数量与列表数量一致，则全选
      if (currentSelected.size === listView.value.length) {
        selectAll.value = true
      } else if (currentSelected.size === 0) {
        // 如果没有选中的节点，则为全不选
        selectAll.value = false
      } else {
        // 需要判断列表中所有的选项是否都选中，是则为全选，否则为半选
        const allSelected = listView.value.every((item) => {
          // 被禁用选项视为选中处理
          return item[propConfig.value.selected] || item[propConfig.value.disable]
        })
        selectAll.value = allSelected ? true : null
      }

      // lastSelected = item
      lastSelectedIndex = index
    }

    return {
      filter,
      list,
      listView,
      selectAll,
      selectAllChange,
      selectItem,
      currentSelected
    }
  }

  const {
    filter: leftFilter,
    list: leftList,
    listView: leftListView,
    selectAll: leftSelectAll,
    selectAllChange: leftSelectAllChange,
    selectItem: leftSelectItem,
    currentSelected: leftCurrentSelected
  } = useTransferPanel(source.value)

  const {
    filter: rightFilter,
    list: rightList,
    listView: rightListView,
    selectAll: rightSelectAll,
    selectAllChange: rightSelectAllChange,
    selectItem: rightSelectItem,
    currentSelected: rightCurrentSelected
  } = useTransferPanel(props.modelValue)

  function moveSelectedItem(from, to, currentSelected) {
    for (const item of currentSelected) {
      item[propConfig.value.selected] = false
      to.value.push(item)
    }

    from.value = from.value.filter((item) => !currentSelected.has(item))
    to.value.sort((a, b) => a[propConfig.value.key] - b[propConfig.value.key])
  }

  // 重置选择状态
  function afterMoved(from, fromSelected, to, toSelected) {
    fromSelected.clear()
    from.value = false
    to.value = toSelected.size > 0 ? null : false
  }

  // 左侧全选按钮禁用属性
  // 右移时，需要判断左侧列表的选项是否全部为禁用
  // 左侧时，直接取消禁用
  const leftDisableSelectAll = ref(false)

  function moveToRight() {
    moveSelectedItem(leftList, rightList, leftCurrentSelected)
    afterMoved(
      leftSelectAll,
      leftCurrentSelected,
      rightSelectAll,
      rightCurrentSelected
    )

    // 左侧列表全选是否禁用
    leftDisableSelectAll.value =
      leftListView.value.length === 0 ||
      leftListView.value.every((item) => item[propConfig.value.disable])
    syncModelValue()
    validate()
  }

  function moveToLeft() {
    moveSelectedItem(rightList, leftList, rightCurrentSelected)
    afterMoved(
      rightSelectAll,
      rightCurrentSelected,
      leftSelectAll,
      leftCurrentSelected
    )
    leftDisableSelectAll.value = false
    syncModelValue()
    validate()
  }

  function syncModelValue() {
    emits('update:model-value', rightList.value)
  }

  const transferId = Math.random().toString(16).slice(2, 12)
  const leftScrollAreaId = `${transferId}--left-scroll`
  const leftScrollTarget = `#${leftScrollAreaId} > .scroll`
  const rightScrollAreaId = `${transferId}--right-scroll`
  const rightScrollTarget = `#${rightScrollAreaId} > .scroll`

  const validRes = ref({
    pass: true,
    tip: [] as string[],
  })

  function validate(): boolean | Promise<boolean> {
    const validRulesRes = props.rules?.map(rule => {
      // @ts-ignore
      return rule(rightList.value)
    })
    validRes.value.pass = !validRulesRes?.some(bool => bool !== true)
    if (!validRes.value.pass) {
      validRes.value.tip = validRulesRes?.filter(bool => bool !== true) ?? []
    }
    return validRes.value.pass
  }

  function resetValidation() {
    rightList.value = []
    validRes.value = {
      pass: true,
      tip: [],
    }
    syncModelValue()
  }

  useFormChild({
    validate, // Function; Can be async;
    // Should return a Boolean (or a Promise resolving to a Boolean)
    resetValidation, // Optional function which resets validation
    requiresQForm: true, // should it error out if no parent QForm is found?
  })

  onMounted(() => {
    syncModelValue()
  })
</script>

<style lang="scss">
  .transfer {
    --transfer-panel-height: v-bind('transferPanelHeight');

    $border: 1px solid #ccc;
    $borderRadius: 6px;
    $spaceX: 16px;
    $spaceY: 12px;
    $gap: 1rem;

    //display: flex;
    gap: $gap;

    .list-panel {
      flex: auto;
      border: $border;
      border-radius: $borderRadius;
      display: flex;
      flex-direction: column;
      gap: calc($gap / 2);

      .panel-header {
        padding: $spaceY $spaceX;
        border-bottom: $border;
        border-radius: $borderRadius $borderRadius 0 0;
        background-color: rgb(245, 247, 250);
        display: flex;
        justify-content: space-between;
        gap: $gap;
      }

      .panel-filter {
        margin: 0 $spaceX;
      }

      .panel-scroll-area {
        min-height: 32px;
        height: var(--transfer-panel-height);
      }
    }

    .action-panel {
      gap: $gap;
    }
    .action-panel-mobile button .q-btn__content {
      transform: rotate(90deg);
    }

    .right-panel.valid-err {
      border: 2px solid $negative;
    }
  }


</style>

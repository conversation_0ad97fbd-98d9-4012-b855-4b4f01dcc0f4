import { Store } from '@src/store'
import { UserSettings } from '@store/state'
import { User } from '@services/dataStore'
import { crud } from '@ygen/crud'
import { user } from '@ygen/user'
import { RpcUser } from '@ygen/user.api.yrpc'
import { TrpcMeta, ICallOption, rpcCon } from 'jsyrpc'
import log from 'loglevel'
import { yrpcmsg } from 'yrpcmsg'
import { i18n } from '@boot/i18n'
import { Notify } from 'quasar'
import { String2UInt8Array } from '@utils/crypto'
import { cloneDeep } from 'lodash'

// 保存配置
export function useUpdateSystemSettings() {
  function publishCurdUserData(userData: user.IDbUser) {
    const msg: yrpcmsg.Ymsg = new yrpcmsg.Ymsg({
      Sid: String2UInt8Array(Store.state.SessionRID as string),
      Body: user.DbUser.encode(userData as user.IDbUser).finish(),
    })
    const bytes = yrpcmsg.Ymsg.encode(msg).finish()
    rpcCon.NatsPublish('user.' + Store.state.UserRID, bytes)
  }

  function partialUpdate(data: user.IDbUser, param: crud.IDMLParam): Promise<user.IDbUser> {
    return new Promise((resolve, reject) => {
      const rpcMeta = new TrpcMeta()
      rpcMeta.SetProtoMsg('DMLParam', new crud.DMLParam(param))
      const options: ICallOption = {
        OnResult: (res: crud.IDMLResult, rpcCmd: yrpcmsg.Ymsg, meta?: yrpcmsg.IGrpcMeta) => {
          log.info('IDbUser Update result', res, rpcCmd, meta)
          if (res.AffectedRow === 0) {
            return reject(res.AffectedRow)
          }
          Notify.create({
            message: i18n.global.t('message.updateSuccess') as string,
            color: 'primary',
          })
          // 同步到数据容器对象
          const RID = data.RID as string
          const newData = new user.DbUser(data)
          User.setData(RID, newData)
            .setDataIndex(newData.UserName as string, RID)

          resolve(newData)
          publishCurdUserData(newData)
        },
        OnServerErr: (errRpc: yrpcmsg.Ymsg) => {
          // 处理服务器响应的错误
          let reason = i18n.global.t('message.updateFailed')
          // dborg_orgid_key 自编号重复
          if (errRpc.Optstr.includes('dborg_orgid_key')) {
            reason = i18n.global.t('message.duplicateOrgID', { name: data.UserID })
          }
          //dbuser_loginname_key
          if (errRpc.Optstr.includes('dbuser_loginname_key')) {
            reason = i18n.global.t('dataBaseError.loginNameErr', { name: data.LoginName })
          }
          log.error('IDbUser Update server error', errRpc, reason)
          reject(reason)
        },
        OnLocalErr: (err: any) => {
          log.error('IDbUser Update local error', err)
          reject(err.toString())
        },
        OnTimeout: (v: any) => {
          const options = {
            action: i18n.global.t('common.modify'),
            name: data.UserName,
          }
          const reason = i18n.global.t('message.timeout', options)
          log.warn('IDbUser Update timeout', v, reason)
          reject(reason)
        },
        rpcMeta,
      }
      RpcUser.UpdateMySetting(data, options)
    })
  }

  const UserRID = Store.state.UserRID

  function updateSettings(settings: Partial<UserSettings>) {
    const userData = User.getData(UserRID) as user.IDbUser | undefined
    if (!userData) {
      log.warn('submitSystemSettings user data not found')
      return Promise.reject('user data not found')
    }

    const userSettings = Store.state.Settings
    const newUserSettings = {
      ...userSettings,
      ...settings,
    }
    const newData = {
      ...userData,
      Setting: JSON.stringify(newUserSettings),
    }

    const param: crud.IDMLParam = {
      KeyColumn: ['Setting'],
    }

    return partialUpdate(newData, param)
      .then((dbUser) => {
        // 更新数据
        Store.commit({
          type: 'syncSettings',
          value: cloneDeep(newUserSettings),
        })

        return dbUser
      })
  }

  return {
    updateSettings,
    partialUpdate,
  }
}

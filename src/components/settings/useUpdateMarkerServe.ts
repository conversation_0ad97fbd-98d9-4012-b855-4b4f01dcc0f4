import { bysdb } from '@src/ygen/bys.api'
import { ICallOption, TrpcMeta } from 'jsyrpc'
import { crud } from '@ygen/crud'
import { yrpcmsg } from 'yrpcmsg'
import log from 'loglevel'
import { BysMarker } from '@src/services/dataStore'
import { PrpcDbBysMarker } from '@src/ygen/bysdb.rpc.yrpc'
import { i18n } from '@boot/i18n'
import { Notify } from 'quasar'


export function useUpdateSystemSettings() {

  function partialUpdateMarkerSetting(data: bysdb.IDbBysMarker, param: crud.IDMLParam): Promise<boolean> {
    return new Promise((resolve, reject) => {
      const rpcMeta = new TrpcMeta()
      rpcMeta.SetProtoMsg('DMLParam', new crud.DMLParam(param))
      const options: ICallOption = {
        OnResult(res: crud.IDMLResult, rpcCmd: yrpcmsg.Ymsg, meta?: yrpcmsg.IGrpcMeta) {
          log.info('IDbBysMarker Update Serve success', res, rpcCmd, meta)
          Notify.create({
            message: i18n.global.t('message.updateSuccess') as string,
            color: 'positive',
          })
          BysMarker.setPartData(data.RID as string, { MarkerSettings: data.MarkerSettings })
          resolve(true)
        },
        OnServerErr: (errRpc: yrpcmsg.Yempty) => {
          log.info('IDbBysMarker Update Serve failed', errRpc)
          Notify.create({
            message: i18n.global.t('message.updateFailed') as string,
            color: 'negative',
          })
          reject(false)
        },
        rpcMeta,
      }
      PrpcDbBysMarker.PartialUpdate(data, options)
    })
  }

  function partialUpdate(dbMarker): Promise<boolean> {
    return new Promise((resolve, reject) => {
      const param: crud.IDMLParam = {
        KeyColumn: ['MarkerSettings'],
      }
      partialUpdateMarkerSetting(dbMarker, param)
        .then((res) => {
          resolve(res)
        })
        .catch((err) => {
          reject(err)
        })
    })
  }

  return {
    partialUpdate,
  }
}
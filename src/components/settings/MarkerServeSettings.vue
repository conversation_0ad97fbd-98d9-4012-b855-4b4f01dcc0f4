<template>
  <q-form @submit="submitForm">
    <div class="col col-xs-12 col-sm-6  col-md-4 mb-4">
      <q-select
        v-model="markerRID"
        :label="$t('form.markerName')"
        outlined
        dense
        clearable
        lazy-rules
        :options="markerOptions"
        @filter="filterMarkerRID"
        options-dense
        map-options
        emit-value
        use-input
      />
    </div>
    <!--服务器地址-->
    <div class="col col-xs-12 col-sm-6  col-md-4">
      <q-input
        v-model="addr"
        :label="$t('form.addr')"
        outlined
        dense
        clearable
        lazy-rules
        :readonly="!markerRID"
        :rules="paramsWith4gAddrRules.addr"
      />
    </div>
    <!--服务器端口-->
    <div class="col col-xs-12 col-sm-6  col-md-4 pb-[12px]">
      <input-number
        v-model:model-value="port"
        :label="$t('form.addrPort')"
        outlined
        dense
        clearable
        :readonly="!markerRID"
        toolTipText="1 ~ 65535"
        :min="1"
        :max="65535">
      </input-number>
    </div>
    <div class="text-center">
      <q-btn
        class="bg-primary text-white"
        dense
        type="submit"
        :disable="!markerRID"
        :label="$t('settingsPage.submitModify')"
      />
    </div>
  </q-form>
</template>

<script lang="ts" setup>
  import { checkIs4GMarker } from '@app/src/utils/common'
  import { useBysMarkerData } from '@app/src/utils/vueUse/bysMarkerData'
  import { useBysMarkerEdit } from '@app/src/utils/vueUse/useBysMarkerEdit'
  import log from '@src/utils/log'
  import { computed, ref, watch, defineAsyncComponent } from 'vue'
  import { useI18n } from 'vue-i18n'
  import { useUpdateSystemSettings } from './useUpdateMarkerServe'
  import { bysdb } from '@app/src/ygen/bysdb'
  import { utcTime } from '@app/src/utils/dayjs'
  import { secondConfirmDialog } from '@app/src/utils/common'

  const i18n = useI18n()
  const InputNumber = defineAsyncComponent(() => import('@components/InputNumber.vue'))

  const markerRID = ref('')
  const { bysMarkerData } = useBysMarkerData()
  const { partialUpdate } = useUpdateSystemSettings()
  const currentRow = computed(() => {
    return bysMarkerData.value.find(item => item.RID === markerRID.value)
  })
  const { paramsWith4gAddrRules } = useBysMarkerEdit(currentRow)

  const options = bysMarkerData.value.filter(m => checkIs4GMarker(m.MarkerType)).map(item => {
    return {
      label: item.MarkerNo,
      value: item.RID
    }
  })
  const markerOptions = ref(options)

  function jsonParse(jsonData: string) {
    let data = {}
    try {
      data = JSON.parse(jsonData)
    } catch(e) {
      // json error
      log.info('serve settings json error')
    }
    return data
  }

  const addr = ref('')
  const port = ref()

  function filterMarkerRID(val: string, update: Function) {
    if (val === '') {
      update(() => {
        markerOptions.value = options
      })
      return
    }

    update(() => {
      const needle = val.toLowerCase()
      markerOptions.value = options.filter(v => v.label.toLowerCase().indexOf(needle) > -1)
    })
  }

  watch(markerRID, () => {
    const paramsWith4g: Record<string, any> = jsonParse(currentRow.value?.MarkerSettings || '{}')
    const addrAndPort = paramsWith4g?.addr?.split(':') || ['', '']
    addr.value = addrAndPort[0]
    port.value = addrAndPort[1] ?? '10000'
  })

  async function submitForm() {
    const htmlMsg = '<i class="q-icon text-warning notranslate material-icons" aria-hidden="true" role="presentation" style="font-size: 2rem;padding-right: 0.5rem;">warning</i>'
    const isOk = await secondConfirmDialog(htmlMsg + i18n.t('settingsPage.confirmUpdateServerTip'), i18n.t('common.confirm'), i18n.t('common.cancel'), { html: true })
    if (isOk) {
      const dbMarker: bysdb.DbBysMarker = bysMarkerData.value.find(item => item.RID === markerRID.value)
        const MarkerSettings: Record<string, any> = jsonParse(dbMarker.MarkerSettings)
        MarkerSettings.addr = addr.value + ':' + port.value
        MarkerSettings.dataVersion = utcTime()
        const marker = Object.assign({}, dbMarker)
        marker.MarkerSettings = JSON.stringify(MarkerSettings)
        partialUpdate(marker)
    }
  }
</script>

<template>
  <q-form
    class="change-password-form"
    @submit="submitChanged"
  >
    <q-input
      outlined
      dense
      class="setting-pg-input"
      v-model="userLoginName"
      :label="$t('settingsPage.loginName')"
      :maxlength="16"
    />
    <q-input
      outlined
      dense
      v-model="form.oldPassword"
      class="q-mt-sm setting-pg-input"
      lazy-rules
      :rules="rules.oldPassword"
      clearable
      hide-bottom-space
      type="password"
      :label="$t('settingsPage.oldPassword')"
      :maxlength="16"
    />
    <q-input
      outlined
      dense
      v-model="form.newPassword"
      class="q-mt-sm setting-pg-input"
      clearable
      @update:model-value="inputNewPassword"
      type="password"
      :label="$t('settingsPage.newPassword')"
      :maxlength="16"
    />
    <q-input
      outlined
      dense
      v-model="form.confirmPassword"
      class="q-mt-sm setting-pg-input"
      lazy-rules
      :rules="rules.confirmPassword"
      clearable
      type="password"
      :label="$t('settingsPage.confirmNewPassword')"
      :maxlength="16"
      ref="confirmPasswordRef"
    />
    <div class="text-center">
      <q-btn
        class="q-mt-md bg-primary text-white"
        dense
        type="submit"
        :label="$t('settingsPage.submitModify')"
      />
    </div>
  </q-form>
</template>

<script setup lang="ts">
  import { computed, ref, reactive } from 'vue'
  import { useStore } from 'vuex'
  import { useI18n } from 'vue-i18n'
  import { required } from '@utils/validation'
  import { QInput, useQuasar } from 'quasar'
  import { GET_DATA, NS } from '@store/data/methodTypes'
  import { DataName } from '@src/store'
  import { encodeUserPassword } from '@utils/crypto'
  import { useUpdateSystemSettings } from './useUpdateSettings'
  import { User } from '@services/dataStore'
  import { user } from '@ygen/user'
  import { crud } from '@app/src/ygen/crud'
  import storage, { StoreLoginInfo } from '@app/src/utils/storage'
  import { secondConfirmDialog } from '@utils/common'

  const store = useStore()
  const i18n = useI18n()
  const $q = useQuasar()

  const loginName = computed(() => {
    return store.getters[`${NS}/${GET_DATA}`](DataName.User)
      .find(item => item.RID === store.state.UserRID)?.LoginName ?? ''
  })
  const confirmPasswordRef = ref<QInput>()
  const userLoginName = ref(loginName.value)
  const form = reactive({
    oldPassword: '',
    newPassword: '',
    confirmPassword: '',
  })

  function useRules() {
    const getOldPass = computed(() => {
      return store.getters[`${NS}/${GET_DATA}`](DataName.User)
        .find(item => item.RID === store.state.UserRID)?.LoginPass
    })
    function isCheckNewPwd(val) {
      return form.newPassword === val
    }
    function isTrueOldPwd(newPwd) {
      return encodeUserPassword(loginName.value, newPwd) === getOldPass.value
    }
    return computed(() => {
      const rules: { [key: string]: any[] } = {
        oldPassword: [
          val => required(val) || i18n.t('rules.required'),
          val => isTrueOldPwd(val) || i18n.t('form.oldPwdError'),
        ],
        confirmPassword: [],
      }

      if (form.newPassword) {
        rules.confirmPassword = [
          val => required(val) || i18n.t('rules.required'),
          val => isCheckNewPwd(val) || i18n.t('form.pwdDef'),
        ]
      }

      return rules
    })
  }

  const rules = useRules()

  function inputNewPassword(val) {
    if (val) {
      // 如果确认密码框有内容，则自动校验两次输入的密码是否一致
      confirmPasswordRef.value?.validate()
    } else {
      // 新密码没有输入时，重置确认密码输入框
      form.confirmPassword = ''
      confirmPasswordRef.value?.resetValidation()
    }
  }

  const { partialUpdate } = useUpdateSystemSettings()
  const submitChanged = async () => {
    if (!userLoginName.value) {
      $q.notify({
        message: `${i18n.t('form.pInputUserName')}`,
      })
      return
    }

    const UserRID = store.state.UserRID
    let currentUser = User.getData(UserRID) as user.IDbUser | undefined
    if (!currentUser) {
      return
    }

    currentUser = { ...currentUser }
    if (currentUser.LoginName !== userLoginName.value && form.newPassword === '') {
      // 只修改用户名
      currentUser.LoginName = userLoginName.value
      currentUser.LoginPass = encodeUserPassword(currentUser.LoginName as string, form.oldPassword)
    } else if ((currentUser.LoginName === userLoginName.value && form.newPassword !== '') ||
      (currentUser.LoginName !== userLoginName.value && form.newPassword !== '')) {
      //只修改密码 | 修改用户名和密码
      currentUser.LoginName = userLoginName.value
      currentUser.LoginPass = encodeUserPassword(currentUser.LoginName as string, form.newPassword)
    } else {
      //什么都不修改
      return
    }

    const param: crud.IDMLParam = {
      KeyColumn: ['LoginName', 'LoginPass'],
    }

    const isOk = await secondConfirmDialog(i18n.t('settingsPage.confirmUpdatePwTip'))
    if (isOk) {
      partialUpdate(currentUser, param)
        .then(() => {
          const loginInfo = storage.fetch(StoreLoginInfo)
          if (!loginInfo) { return }
          storage.save(StoreLoginInfo, {
            ...loginInfo as object,
            password: ''.padStart(form.newPassword.length, '*'),
            name: userLoginName.value,
          })
        })
        .catch((err) => {
          $q.notify({
            type: 'warning',
            icon: 'warning',
            message: err,
            position: 'top',
          })
        })
    }
  }

</script>

<style lang="scss"></style>

<template>
  <q-form @submit="submitSystemSettings">
    <div
      class="q-item-choose-logo flex flex-col sm:flex-row  items-start justify-between no-wrap gap-4 flex-none q-mb-md"
    >
      <div class="q-item-section-choose flex-none">
        <q-btn
          class="bg-primary text-white q-mb-md"
          :label="$t('settingsPage.chooseLogo')"
          @click="openFileInput"
        />
        <q-file
          v-model="selectFile"
          ref="fileInput"
          v-show="false"
          accept="image/jpeg,image/png"
          :max-file-size="1024 * 100"
          @update:model-value="chooseLogo"
          @rejected="onRejected"
        />
        <q-item-section class="logo-size-tips column">
          <span>{{ $t('settingsPage.picFormat') }}</span>
          <span>{{ $t('settingsPage.picSize') }}</span>
        </q-item-section>
      </div>

      <q-card class="q-pa-xs preview-logo no-box-shadow rounded-borders flex-auto w-full">
        <img
          :src="logoSrc"
          alt=""
          width="100%"
          height="100%"
          @click="previewLogo = true"
        />
        <q-badge
          color="grey-6"
          floating
          rounded
          transparent
          class="q-pa-none delete-logo row justify-center"
          @click.stop="systemSettings.appLogo = ''"
          v-if="systemSettings.appLogo"
        >
          <q-icon
            name="close"
            color="white"
            class="text-bold"
          ></q-icon>
        </q-badge>
      </q-card>

      <q-dialog v-model="previewLogo">
        <q-card class="q-pa-md">
          <img :src="logoSrc" />
        </q-card>
      </q-dialog>
    </div>
    <q-input
      class="sys-title-input"
      v-model="systemTitle"
      type="textarea"
      :label="$t('settingsPage.sysTitle')"
      maxlength="32"
      outlined
      dense
      clearable
      autogrow
      counter
    />
    <div class="text-center">
      <q-btn
        class="bg-primary text-white"
        dense
        type="submit"
        :label="$t('settingsPage.submitModify')"
      />
    </div>
  </q-form>
</template>

<script
  lang="ts"
  setup
>
  import { ref, computed, reactive } from 'vue'
  import { Notify, QFile } from 'quasar'
  import DefaultAppLogo from '@assets/app-logo.jpg'
  import { useI18n } from 'vue-i18n'
  import { useStore } from 'vuex'
  import { State } from '@store/state'
  import { config } from '@ygen/config'
  import { v1 as uuidV1 } from 'uuid'
  import { configSetData } from '@services/queryData'
  import { ICallOption } from 'jsyrpc'
  import { utcTime } from '@utils/dayjs'
  import { crud } from '@ygen/crud'
  import { Config } from '@services/dataStore'
  import { yrpcmsg } from '@ygen/yrpcmsg'
  import log from '@utils/log'
  import { RpcDbConfig } from '@app/src/ygen/config.rpc.yrpc'

  const i18n = useI18n()
  const store = useStore<State>()

  const topParentOrg = computed(() => store.state.topParentOrg)
  const originSystemSettings = computed(() => store.state.systemSettings)
  const systemSettings = reactive({
    appLogo: originSystemSettings.value?.appLogo?.ConfValue || '',
    systemTitle: originSystemSettings.value?.systemTitle?.ConfValue || '',
  })
  const previewLogo = ref(false)
  const selectFile = ref<File>()

  const logoSrc = computed(() => {
    return systemSettings.appLogo || DefaultAppLogo
  })
  const systemTitle = computed({
    get() {
      return systemSettings.systemTitle || i18n.t('siteTitle')
    },
    set(value) {
      systemSettings.systemTitle = value ?? ''
    }
  })

  // 文件选择
  function useFileInput() {
    const fileInput = ref<QFile>()

    function openFileInput() {
      fileInput.value?.pickFiles()
    }

    function chooseLogo(val: File) {
      const reader = new FileReader()
      reader.onload = () => {
        systemSettings.appLogo = reader.result as string
      }
      reader.readAsDataURL(val)
    }

    function onRejected() {
      Notify.create({
        type: 'negative',
        icon: 'error',
        message: i18n.t('settingsPage.picSize'),
        position: 'top',
      })
    }

    return {
      fileInput,
      openFileInput,
      chooseLogo,
      onRejected,
    }
  }
  const {
    fileInput,
    openFileInput,
    chooseLogo,
    onRejected,
  } = useFileInput()

  const storeUpdateKeyMap: Record<string, (value?: config.IDbConfig) => void> = {
    appLogo: (value?: config.IDbConfig) => {
      store.commit('updateAppLogo', { value })
    },
    systemTitle: (value?: config.IDbConfig) => {
      store.commit('updateSystemTitle', { value })
    },
  }
  const systemSettingActionFailedLabels = computed(() => ({
    appLogo: i18n.t('settingsPage.sysLogo'),
    systemTitle: i18n.t('settingsPage.sysTitle'),
  }))

  const submitSystemSettings = async () => {
    const keys = Object.keys(systemSettings)
    const actionFailedLabels: Array<string> = []
    const actionFn = (ConfKey: string, ConfValue: string, originDbData?: config.IDbConfig): Promise<boolean> => {
      return new Promise((resolve, reject) => {
        let actionName: keyof typeof RpcDbConfig = 'Insert'
        const dbData = new config.DbConfig({
          RID: originDbData?.RID || uuidV1(),
          OrgRID: topParentOrg.value.RID,
          ConfKey,
          ConfValue,
          UpdatedAt: utcTime(),
        })
        const option: ICallOption = {
          OnResult(res: crud.DMLResult) {
            log.info('submitSystemSettings OnResult', actionName, res, dbData)
            // 操作成功，同步store数据
            if (res.AffectedRow === 1) {
              resolve(true)
              if (actionName === 'Delete') {
                Config.deleteData(dbData.RID)
                storeUpdateKeyMap[ConfKey]?.()
                return
              }

              configSetData(dbData)
              storeUpdateKeyMap[ConfKey]?.(dbData)
            } else {
              reject('dbError')
            }
          },
          OnServerErr(errRpc: yrpcmsg.Ymsg) {
            log.error('submitSystemSettings OnServerErr', actionName, errRpc, dbData)
            reject('OnServerErr')
          },
          OnLocalErr(err: any) {
            log.error('submitSystemSettings OnLocalErr', actionName, err, dbData)
            reject('OnLocalErr')
          },
          OnTimeout(v: any) {
            log.error('submitSystemSettings OnTimeout', actionName, v, dbData)
            reject('OnTimeout')
          },
        }

        // 判断是插入、更新、删除
        if (!ConfValue && originDbData) {
          // 删除
          actionName = 'Delete'
          RpcDbConfig.Delete(dbData, option)
          return
        }

        // 更新
        if (originDbData) {
          actionName = 'Update'
          RpcDbConfig.Update(dbData, option)
          return
        }

        // 插入
        actionName = 'Insert'
        RpcDbConfig.Insert(dbData, option)
      })
    }

    for (let i = 0; i < keys.length; i++) {
      const key = keys[i]
      const data = systemSettings[key]

      // 忽略相同数据的配置
      const originDbData = originSystemSettings.value?.[key]
      if (data === originDbData?.ConfValue) continue

      // 将数据缓存到DbConfig数据表中
      const isOk = await actionFn(key, data, originDbData).catch(() => false)
      if (!isOk) {
        const label = systemSettingActionFailedLabels.value[key]
        label && actionFailedLabels.push(label)
      }
    }

    // 有配置修改失败，提示提示
    if (actionFailedLabels.length > 0) {
      Notify.create({
        type: 'warning',
        message: `${i18n.t('message.updateFailed')} (${actionFailedLabels.join(', ')})`
      })
      return
    }

    // 操作成功
    Notify.create({
      message: i18n.t('message.updateSuccess') as string,
      type: 'positive',
    })
  }
</script>

<style
  scoped
  lang="scss"
>
  .q-item-choose-logo {
    .q-item-section-choose {
      .logo-size-tips {
        font-size: 12px;
        color: $grey-6;
      }
    }

    .preview-logo {
      border: 1px solid $grey-4;
      cursor: pointer;

      .delete-logo {
        width: 22px;
        height: 22px;
        font-size: 18px;
        border-radius: 50%;
        // badge floating属性存在 就是绝对定位
        top: -8px;
        right: -8px;
      }
    }

    .delete-logo:hover {
      background-color: #757575 !important;
    }

    @media (max-width: 321px) {
      .preview-logo {
        margin-left: 0;

        img {
          max-width: 208px;
          height: auto !important;
        }
      }
    }
  }

  .sys-title-input.q-input--with-bottom {
    padding-bottom: 16px;
  }
</style>

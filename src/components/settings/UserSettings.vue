<template>
  <div class="user-settings-container">
    <div class="col col-xs-12 col-sm-6 q-gutter-sm q-col-gutter-sm">
      <q-checkbox
        v-model="descending"
        dense
        :label="sortTypeLabel"
      />
    </div>
    <div class="col col-xs-12 col-sm-6 q-gutter-sm q-col-gutter-sm">
      <q-checkbox
        v-model="controllerFirst"
        dense
        :label="sortRuleLabel"
      />
    </div>
    <div class="col col-xs-12 col-sm-6 q-gutter-sm q-col-gutter-sm">
      <q-checkbox
        v-model="showNotInstalledMarker"
        dense
        :label="showNotInstalledMarkerLabel"
      />
    </div>
    <div class="col col-xs-12 col-sm-6 q-gutter-sm q-col-gutter-sm">
      <q-checkbox
        v-model="closePopupWind"
        dense
        :label="isDisPopupWind"
      />
    </div>
    <div class="col col-xs-12 col-sm-6 q-gutter-sm q-col-gutter-sm">
      <q-checkbox
        v-model="closeAlarmAudio"
        dense
        :label="isDisAlarmAudio"
      />
    </div>
    <div class="col col-xs-12 col-sm-6 q-gutter-sm q-col-gutter-sm">
      <q-checkbox
        v-model="patrolReminder"
        dense
        :label="patrolReminderLabel"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { State, UserSettings } from '@store/state'
  import { cloneDeep } from 'lodash'
  import { computed, nextTick } from 'vue'
  import { useStore } from 'vuex'
  import { useI18n } from 'vue-i18n'
  import { StrPubSub } from 'ypubsub'
  import { PatrolReminderChanged, ShowNotInstalledMarker, SortDeviceTree, StopAlarmAudio } from '@utils/pubSubSubject'
  import { useUpdateSystemSettings } from './useUpdateSettings'

  const store = useStore<State>()
  const i18n = useI18n()

  const userSettings = computed(() => {
    return store.state.Settings
  })

  const sortTypeLabel = computed(() => {
    return i18n.t('settingsPage.deviceSortType', { type: i18n.t('settingsPage.descSort') })
  })

  const descending = computed({
    get() {
      return userSettings.value.descending ?? false
    },
    set(val) {
      syncSettings({ descending: val })
    },
  })

  function controllerFirstAfter(settings: Partial<UserSettings>, oldSettings: Partial<UserSettings>) {
    // 判断是否要对设备树排序
    const checkProps = ['controllerFirst', 'descending']
    const needSortTree = checkProps.some(name => settings[name] !== oldSettings[name])

    nextTick(() => {
      // 重新排序设备树
      needSortTree && StrPubSub.publish(SortDeviceTree)
    })
  }
  const controllerFirst = computed({
    get() {
      return userSettings.value.controllerFirst ?? false
    },
    set(val) {
      syncSettings({ controllerFirst: val }, controllerFirstAfter)
    },
  })
  const sortRuleLabel = computed(() => {
    return i18n.t('settingsPage.deviceTreeSortRule', { type: i18n.t('settingsPage.controllerFirst') })
  })

  function showNotInstalledMarkerAfter(settings: Partial<UserSettings>, oldSettings: Partial<UserSettings>) {
    nextTick(() => {
      // 切换显示未安装设备的界桩
      if (settings.showNotInstalledMarker !== oldSettings.showNotInstalledMarker) {
        StrPubSub.publish(ShowNotInstalledMarker)
      }
    })
  }
  const showNotInstalledMarker = computed({
    get() {
      return userSettings.value.showNotInstalledMarker ?? false
    },
    set(val) {
      syncSettings({ showNotInstalledMarker: val }, showNotInstalledMarkerAfter)
    },
  })
  const showNotInstalledMarkerLabel = computed(() => {
    return i18n.t('settingsPage.showNotInstalledMarker')
  })

  const closePopupWind = computed({
    get() {
      return userSettings.value.closePopupWind ?? true
    },
    set(val) {
      syncSettings({ closePopupWind: val })
    },
  })
  const isDisPopupWind = computed(() => {
    return i18n.t('settingsPage.closeAlarmPopupWind')
  })

  const closeAlarmAudio = computed({
    get() {
      return userSettings.value.closeAlarmAudio ?? true
    },
    set(val) {
      if (!val) {
        StrPubSub.publish(StopAlarmAudio)
      }
      syncSettings({ closeAlarmAudio: val })
    },
  })
  const isDisAlarmAudio = computed(() => {
    return i18n.t('settingsPage.closeAlarmAudio')
  })

  const patrolReminder = computed({
    get() {
      return userSettings.value.patrolReminder ?? false
    },
    set(val) {
      syncSettings({ patrolReminder: val }, () => {
        StrPubSub.publish(PatrolReminderChanged, val)
      })
    },
  })
  const patrolReminderLabel = computed(() => {
    return i18n.t('settingsPage.patrolReminder')
  })

  const { updateSettings } = useUpdateSystemSettings()
  const syncSettings = (settings: Partial<UserSettings>, afterUpdated?: (settings: Partial<UserSettings>, oldSettings: Partial<UserSettings>) => void) => {
    const oldSettings = cloneDeep(store.state.Settings)
    updateSettings(settings)
      .then(() => {
        afterUpdated?.(settings, oldSettings)
      })
  }
</script>

<style lang="scss"></style>

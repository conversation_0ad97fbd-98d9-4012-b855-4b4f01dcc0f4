<template>
  <div
    class="pannellum-content full-width"
    ref='pannellumView'
  >
    <div class="absolute-top z-[100] flex !px-2 !py-1 text-white text-ellipsis pannellum-controls text-xs">
      <div class="flex-auto">
        <div>{{ $t('form.uploadUser') }}： {{ nodes.Author }}</div>
        <div v-if="nodes.UploadTime">{{ $t('form.uploadTime') }}： {{ nodes.UploadTime }}</div>
        <div v-if="nodes.LastUploadUser">{{ $t('form.lastUploadUser') }}： {{ nodes.LastUploadUser }}</div>
        <div v-if="nodes.LastUploadTime">{{ $t('form.lastUploadTime') }}： {{ nodes.LastUploadTime }}</div>
        <div v-if="nodes.Description">{{ $t('form.description') }}： {{ nodes.Description }}</div>
      </div>

      <q-btn
        round
        flat
        size="md"
        color="white"
        icon="aspect_ratio"
        @click="clickFullScreen"
      />
    </div>

    <div class='pannellum-view'></div>
  </div>
</template>

<script lang="ts">
  import { defineComponent } from 'vue'
  import '@src/localCdn/pannellum/pannellum.css'
  import '@src/localCdn/pannellum/libpannellum.js'
  import '@src/localCdn/pannellum/pannellum.js'

  export default defineComponent({
    name: 'Pannellum',
    props: {
      imgUrl: {
        type: String,
        required: true,
      },
      nodes: {
        type: Object,
        default() {
          return {}
        },
      },
    },
    data() {
      return {}
    },
    methods: {
      clickFullScreen() {
        this.viewer.toggleFullscreen(() => {
          this.$q.fullscreen.toggle(this.$refs.pannellumView)
        })
      },
      initViewer() {
        const loadBtnLabel = this.$t('common.loadBtnLabel')
        // 不在data()中初始化，避免代理
        this.params = {
          type: 'equirectangular',
          panorama: this.imgUrl,
          showControls: false,
          //'preview': '/images/tocopilla-preview.jpg',
          mouseZoom: 'fullscreenonly',
          compass: false,
          // autoLoad: options?.autoLoad ?? false,
        }
        this.viewer = window.pannellum.viewer(this.$refs.pannellumView, this.params, loadBtnLabel)
      }
    },
    mounted() {
      this.$nextTick(() => {
        this.initViewer()
      })
    },
    beforeUnmount() {
      this.viewer?.destroy()
      this.viewer = null
    },
  })
</script>

<style lang="scss">
  .pannellum-content {
    position: relative;
    min-height: 360px;

    .pannellum-controls {
      background-color: rgba(0, 0, 0, 0.47);
    }
  }
</style>

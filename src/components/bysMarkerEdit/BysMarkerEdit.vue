<template>
  <div
    class="q-ml-sm"
    :class="$q.platform.is.android ? '' : 'marker-edit-content'"
  >
    <div class="fit row wrap q-mb-sm q-col-gutter-x-sm">
      <!--名称 单位 id 模型 类型-->
      <div class="col col-xs-12 col-sm-6  col-md-4">
        <q-input
          v-model="currentRow.MarkerNo"
          :label="$t('form.markerName')"
          outlined
          dense
          clearable
          autofocus
          lazy-rules
          :rules="rules.MarkerNo"
          :maxlength="16"
        />
      </div>
      <div class="col col-xs-12 col-sm-6  col-md-4">
        <!-- parentOptions,filterParent in dataEdit mixins -->
        <q-select
          v-model="currentRow.OrgRID"
          :label="$t('form.unit')"
          outlined
          dense
          clearable
          lazy-rules
          :rules="rules.OrgRID"
          :options="parentOptions"
          @filter="filterParent"
          options-dense
          map-options
          emit-value
        />
      </div>
      <div class="col col-xs-12 col-sm-6  col-md-4 pb-[12px]">
        <input-number
          v-model:model-value="currentRow.MarkerHWID"
          :label="$t('form.boundaryID')"
          outlined
          dense
          clearable
          :maxlength="16"
          :min="1"
          :max="268435455"
          @update:model-value="deviceIDChange"
        >
          <template #default>
            <q-tooltip :delay="500" :offset="[0 ,1]">1 ~ 268435455</q-tooltip>
          </template>
        </input-number>
      </div>
      <div class="col col-xs-12 col-sm-6  col-md-4">
        <q-select
          v-model="currentRow.MarkerModel"
          :label="$t('form.boundaryModel')"
          outlined
          dense
          lazy-rules
          :rules="rules.MarkerModel"
          :options="MarkerModelOptions"
          options-dense
          map-options
          emit-value
        />
      </div>
      <div class="col col-xs-12 col-sm-6  col-md-4">
        <q-select
          v-model="currentRow.MarkerType"
          :label="$t('form.boundaryType')"
          outlined
          dense
          lazy-rules
          :rules="rules.MarkerType"
          :options="MarkerTypeOptions"
          options-dense
          map-options
          emit-value
          @update:model-value="markerTypeChange"
        />
      </div>

      <template v-if="is4GMarkerType">
        <div class="col col-xs-12 col-sm-6 col-md-4">
          <q-input
            v-model="MarkerWakeupBaseTime"
            :label="$t('form.markerWakeupBaseTime')"
            mask="fulltime"
            outlined
            dense
            clearable
            lazy-rules
            :readonly="!haveMaintainPerm"
            :rules="rules.MarkerWakeupBaseTime"
            class="mb-3"
          >
            <template v-slot:append>
              <q-icon
                name="access_time"
                :class="[haveMaintainPerm ? 'cursor-pointer' : '']"
              >
                <q-popup-proxy
                  transition-show="scale"
                  transition-hide="scale"
                  v-if="haveMaintainPerm"
                >
                  <q-time
                    v-model="MarkerWakeupBaseTime"
                    with-seconds
                    format24h
                  />
                </q-popup-proxy>
              </q-icon>
            </template>
          </q-input>
        </div>
        <div class="col col-xs-12 col-sm-6 col-md-4 pb-[12px]">
          <input-number
            v-model:model-value="currentRow.MarkerDayInterval"
            :label="$t('form.wakeUpInterval')"
            outlined
            dense
            clearable
            :min="1"
            :max="24"
            :readonly="!haveMaintainPerm"
          >
            <template #default>
              <q-tooltip :delay="500" :offset="[0 ,1]">1 ~ 24</q-tooltip>
            </template>
          </input-number>
        </div>

        <div class="col col-xs-12 col-sm-6 col-md-4 pb-[12px]">
          <input-number
            v-model:model-value="paramsWith4g.attitude"
            :label="$t('form.attitude')"
            outlined
            dense
            clearable
            :min="15"
            :max="90"
            :readonly="!haveMaintainPerm"
          >
            <template #default>
              <q-tooltip :delay="500" :offset="[0 ,1]">15 ~ 90</q-tooltip>
            </template>
          </input-number>
        </div>
        <div class="col col-xs-12 col-sm-6 col-md-4 pb-[12px]">
          <input-number
            v-model:model-value="paramsWith4g.dirft"
            :label="$t('form.dirft')"
            outlined
            dense
            clearable
            :min="10"
            :max="100"
            :readonly="!haveMaintainPerm"
          >
            <template #default>
              <q-tooltip :delay="500" :offset="[0 ,1]">10 ~ 100</q-tooltip>
            </template>
          </input-number>
        </div>
        <div class="col col-xs-12 col-sm-6 col-md-4 pb-[12px]">
          <input-number
            v-model:model-value="paramsWith4g.vibration.amplitude"
            :label="$t('form.amplitude')"
            outlined
            dense
            clearable
            :min="1"
            :max="10"
            :readonly="!haveMaintainPerm"
          >
            <template #default>
              <q-tooltip :delay="500" :offset="[0 ,1]">1 ~ 10</q-tooltip>
            </template>
          </input-number>
        </div>
        <div class="col col-xs-12 col-sm-6 col-md-4 pb-[12px]">
          <input-number
            v-model:model-value="paramsWith4g.vibration.duration"
            :label="$t('form.duration')"
            outlined
            dense
            clearable
            :min="1"
            :max="60"
            :readonly="!haveMaintainPerm"
          >
            <template #default>
              <q-tooltip :delay="500" :offset="[0 ,1]">1 ~ 60</q-tooltip>
            </template>
          </input-number>
        </div>
        <div class="col col-xs-12 col-sm-6 col-md-4 pb-[12px]" v-if="is4GMarkerPro">
          <input-number
            v-model:model-value="paramsWith4g.infrared"
            :label="$t('form.infrared')"
            outlined
            dense
            clearable
            :min="0"
            :max="10"
            :readonly="!haveMaintainPerm"
          >
            <template #default>
              <q-tooltip :delay="500" :offset="[0 ,1]"> {{ $t('form.infraredTooltip') }} </q-tooltip>
            </template>
          </input-number>
        </div>
        <div class="col col-xs-12 col-sm-6 col-md-4 pb-[12px]">
          <input-number
            v-model:model-value="paramsWith4g.t1"
            :label="$t('form.deLaySlTime')"
            outlined
            dense
            clearable
            :min="10"
            :max="60"
            :readonly="!haveMaintainPerm"
          >
            <template #default>
              <q-tooltip :delay="500" :offset="[0 ,1]">10 ~ 60</q-tooltip>
            </template>
          </input-number>
        </div>
        <div class="col col-xs-12 col-sm-6 col-md-4 pb-[12px]">
          <input-number
            v-model:model-value="paramsWith4g.t2"
            :label="$t('form.debugModelTime')"
            outlined
            dense
            clearable
            :min="120"
            :max="240"
            :readonly="!haveMaintainPerm"
          >
            <template #default>
              <q-tooltip :delay="500" :offset="[0 ,1]">120 ~ 240</q-tooltip>
            </template>
          </input-number>
        </div>
        <div class="col col-xs-12 col-sm-6 col-md-4 pb-[12px]">
          <input-number
            v-model:model-value="paramsWith4g.t3"
            :label="$t('form.alarmInterval')"
            outlined
            dense
            clearable
            :min="10"
            :max="60"
            :readonly="!haveMaintainPerm"
          >
            <template #default>
              <q-tooltip :delay="500" :offset="[0 ,1]">10 ~ 60</q-tooltip>
            </template>
          </input-number>
        </div>
        <div class="col col-xs-12 col-sm-6 col-md-4 pb-[12px]">
          <input-number
            v-model:model-value="paramsWith4g.n1"
            :label="$t('form.alarmNum')"
            outlined
            dense
            clearable
            :min="10"
            :max="60"
            :readonly="!haveMaintainPerm"
          >
            <template #default>
              <q-tooltip :delay="500" :offset="[0 ,1]">10 ~ 60</q-tooltip>
            </template>
          </input-number>
        </div>
        <div class="col col-xs-12 col-sm-6 col-md-4">
          <q-select
            :model-value="currentRow.IMEI"
            label="IMEI"
            outlined
            dense
            lazy-rules
            counter
            fill-input
            clearable
            use-input
            hide-selected
            input-debounce="0"
            :options="IMEIOptions"
            :readonly="!haveMaintainPerm"
            :rules="rules.IMEI"
            @input-value="setIMEI"
            @clear="currentRow.IMEI = ''"
          >
            <template v-slot:option="scope">
              <q-item v-bind="scope.itemProps" dense @click="chooseIMEIAndICCID(scope.opt)">
                <q-item-section>
                  <q-item-label class="flex justify-between">
                    <span>{{ scope.opt.IMEI }}</span>
                    <span class="opt-time">{{ toLocalTime(scope.opt.LoginTimeStr, TimeMask) }}</span>
                  </q-item-label>
                  <q-item-label caption>{{ scope.opt.ICCID }}</q-item-label>
                </q-item-section>
              </q-item>
            </template>
          </q-select>
        </div>
        <div class="col col-xs-12 col-sm-6 col-md-4">
          <q-select
            :model-value="currentRow.ICCID"
            label="ICCID"
            outlined
            dense
            clearable
            lazy-rules
            counter
            fill-input
            use-input
            hide-selected
            input-debounce="0"
            :options="ICCIDOptions"
            :rules="rules.ICCID"
            ref="iccid"
            :readonly="!haveMaintainPerm"
            @clear="currentRow.ICCID = ''"
            @input-value="setICCID"
          >
            <template v-slot:option="scope">
              <q-item v-bind="scope.itemProps" dense class="flex justify-between"
                      @click="chooseIMEIAndICCID(scope.opt)">
                <q-item-section>
                  <q-item-label class="flex justify-between">
                    <span>{{ scope.opt.ICCID }}</span>
                    <span class="opt-time">{{ toLocalTime(scope.opt.LoginTimeStr, TimeMask) }}</span>
                  </q-item-label>
                  <q-item-label caption>{{ scope.opt.IMEI }}</q-item-label>
                </q-item-section>
              </q-item>
            </template>
          </q-select>
        </div>
        <div class="col col-xs-12 col-sm-6 col-md-4">
          <q-input
            v-model="ExpirationDate"
            :label="$t('form.ExpirationDate')"
            outlined
            dense
            clearable
            lazy-rules
            :disable="!currentRow.ICCID"
            :rules="expirationDateRules[currentRow.ICCID ? 1 : 0]"
            :readonly="!haveMaintainPerm"
            ref="exirationDate"
          >
            <template v-slot:append>
              <q-icon
                name="event"
                :class="[haveMaintainPerm ? 'cursor-pointer' : '' ]"
              >
                <q-popup-proxy
                  transition-show="scale"
                  transition-hide="scale"
                  v-if="haveMaintainPerm"
                >
                  <q-date
                    v-model="ExpirationDate"
                    mask="YYYY-MM-DD"
                    :options="ExpirationOptions"
                    :disable="!currentRow.ICCID"
                  />
                </q-popup-proxy>
              </q-icon>
            </template>
          </q-input>
        </div>
      </template>

      <template v-if="!is4GMarkerType">
        <div class="col col-xs-12 col-sm-6 col-md-4">
          <q-select
            v-model="currentRow.ControllerRID"
            :label="$t('form.parentController')"
            outlined
            dense
            clearable
            lazy-rules
            :rules="rules.ControllerRID"
            :options="ControllerRIDOptions"
            @filter="filterControllerRID"
            options-dense
            map-options
            emit-value
            :disable="!haveMaintainPerm"
            @update:model-value="controllerRIDInput"
          />
        </div>
        <div class="col col-xs-12 col-sm-6 col-md-4">
          <q-select
            v-model="currentRow.ControllerChannel"
            :label="$t('form.baseStationChannel')"
            outlined
            dense
            lazy-rules
            :rules="rules.ControllerChannel"
            :options="controllerChannelsOptions"
            :disable="isRepeater || !haveMaintainPerm"
            options-dense
            map-options
            emit-value
          />
        </div>
        <div class="col col-xs-12 col-sm-6 col-md-4">
          <q-select
            v-model="currentRow.MarkerChannel"
            :label="$t('form.markerChannel')"
            outlined
            dense
            lazy-rules
            :rules="rules.MarkerChannel"
            :options="channelsOptions"
            :disable="!haveMaintainPerm"
            options-dense
            map-options
            emit-value
          />
        </div>
        <div class="col col-xs-12 col-sm-6 col-md-4">
          <q-input
            v-model.number="currentRow.MarkerQueueNo"
            type="number"
            :label="$t('form.markerQueueNo')"
            outlined
            dense
            clearable
            :rules="rules.MarkerQueueNo"
            :disable="judgeMarkerQueueNo || !haveMaintainPerm"
            debounce="1000"
            @update:model-value="markerQueueNoChange"
            ref="queueNoInput"
          />
        </div>
        <div class="col col-xs-12 col-sm-6 col-md-4 pb-[12px]">
          <input-number
            v-model:model-value="currentRow.MarkerDayInterval"
            :label="$t('form.markerDayInterval')"
            outlined
            dense
            clearable
            :min="1"
            :max="24"
            :readonly="!haveMaintainPerm"
          >
            <template #default>
              <q-tooltip :delay="500" :offset="[0 ,1]">1 ~ 24</q-tooltip>
            </template>
          </input-number>
        </div>
        <div class="col col-xs-12 col-sm-6 col-md-4 pb-[12px]">
          <input-number
            v-model:model-value="currentRow.MarkerQueueInterval"
            :label="$t('form.markerQueueInterval')"
            outlined
            dense
            clearable
            :min="20"
            :max="255"
            :readonly="!haveMaintainPerm"
          >
            <template #default>
              <q-tooltip :delay="500" :offset="[0 ,1]">20 ~ 255</q-tooltip>
            </template>
          </input-number>
        </div>
        <div class="col col-xs-12 col-sm-6 col-md-4 pb-[12px]">
          <input-number
            v-model:model-value="currentRow.MarkerEmergentInterval"
            :label="$t('form.markerEmergentInterval')"
            outlined
            dense
            clearable
            :min="20"
            :max="255"
            :readonly="!haveMaintainPerm"
          >
            <template #default>
              <q-tooltip :delay="500" :offset="[0 ,1]">20 ~ 255</q-tooltip>
            </template>
          </input-number>
        </div>
        <div class="col col-xs-12 col-sm-6 col-md-4">
          <q-input
            v-model="MarkerWakeupBaseTime"
            :label="$t('form.markerWakeupBaseTime')"
            mask="fulltime"
            outlined
            dense
            clearable
            lazy-rules
            :rules="rules.MarkerWakeupBaseTime"
            :readonly="!haveMaintainPerm"
          >
            <template v-slot:append>
              <q-icon
                name="access_time"
                class="cursor-pointer"
              >
                <q-popup-proxy
                  transition-show="scale"
                  transition-hide="scale"
                >
                  <q-time
                    v-model="MarkerWakeupBaseTime"
                    with-seconds
                    format24h
                  />
                </q-popup-proxy>
              </q-icon>
            </template>
          </q-input>
        </div>
      </template>

      <div class="col col-xs-12 col-sm-6 col-md-4">
        <q-input
          v-model.number="Lon"
          :label="$t('form.lon')"
          outlined
          dense
          clearable
          lazy-rules
          :rules="rules.Lon"
          :maxlength="16"
        >
          <template v-slot:append>
            <q-btn
              round
              dense
              flat
              icon="place"
              @click="lonLatVisible = true"
            ></q-btn>
          </template>
        </q-input>
      </div>
      <div class="col col-xs-12 col-sm-6 col-md-4">
        <q-input
          v-model="Lat"
          :label="$t('form.lat')"
          outlined
          dense
          clearable
          lazy-rules
          :rules="rules.Lat"
          :maxlength="16"
        >
          <template v-slot:append>
            <q-btn
              round
              dense
              flat
              icon="place"
              @click="lonLatVisible = true"
            ></q-btn>
          </template>
        </q-input>
      </div>
      <div class="col col-xs-12 col-sm-6 col-md-4 pb-[12px]">
        <input-number
          v-model:model-value="currentRow.MapShowLevel"
          :label="$t('form.mapShowLevel')"
          outlined
          dense
          clearable
          :min="3"
          :max="22"
        >
          <template #default>
            <q-tooltip :delay="500" :offset="[0 ,1]">3 ~ 22</q-tooltip>
          </template>
        </input-number>
      </div>
      <!--      <div class="col col-xs-12 col-sm-6 col-md-4">-->
      <!--        <q-input-->
      <!--          v-model.number="currentRow.MarkerRedLineNo"-->
      <!--          type="number"-->
      <!--          :label="i18n.t('form.markerRedLineNo')"-->
      <!--          outlined dense clearable-->
      <!--          lazy-rules-->
      <!--          :rules="rules.MarkerRedLineNo"-->
      <!--        />-->
      <!--      </div>-->
      <div class="col col-xs-12 col-sm-6 col-md-4">
        <q-select
          v-model="redLineLinks"
          multiple
          :virtual-scroll-slice-size='100'
          :label="$t('form.redLineGraphRules')"
          outlined
          dense
          clearable
          lazy-rules
          :rules="redLineLinksRules"
          :options="markerRIDOptions"
          @filter="filterMarkerRedLine"
          options-dense
          map-options
          emit-value
          use-input
          ref='redLineRef'
        >
        </q-select>
      </div>
      <div class="col col-12 q-mb-sm">
        <q-input
          v-model="currentRow.MarkerDescription"
          type="textarea"
          :label="$t('form.description')"
          outlined
          dense
          clearable
          autogrow
          lazy-rules
          :rules="rules.MarkerDescription"
          :maxlength="4096"
        />
      </div>
      <div class="col col-12 q-mb-sm row">
        <!-- 第一列：已安装设备和已安装石柱 -->
        <div class="col col-xs-12 col-sm-6  row items-start q-mb-sm gap-2" :class="is4GMarkerPro ? 'col-md-4' : 'col-md-6'">
          <div class="col-12">
            <div class="row items-center">
              <div class="relative-position">
                <q-checkbox
                  v-if="isShowDisableMarker"
                  v-model="HasInstallDevice"
                  dense
                  :label="$t('form.isInstallDevice')"
                  @update:model-value="HasInstallDeviceChange"
                />
              </div>
              <q-item-label
                class="cmdTime"
                caption
                v-if="isShowDisableMarker && HasInstallDevice"
              >
                {{ toLocalTimeSelf(currentRowSetting.installDeviceTime) }}
              </q-item-label>
            </div>
          </div>
          <div class="col-12">
            <div class="row items-center">
              <div class="relative-position">
                <q-checkbox
                  v-if="isShowDisableMarker"
                  v-model="HasInstallStone"
                  dense
                  :label="$t('form.HasInstallStone')"
                  @update:model-value="HasInstallStoneChange"
                />
              </div>
              <q-item-label
                class="cmdTime"
                caption
                v-if="isShowDisableMarker && HasInstallStone"
              >
                {{ toLocalTimeSelf(currentRowSetting.installStoneTime) }}
              </q-item-label>
            </div>
          </div>
        </div>

        <!-- 第二列：界桩已遥毙和界桩已遥晕 -->
        <div class="col col-xs-12 col-sm-6 row items-start q-mb-sm gap-2" :class="is4GMarkerPro ? 'col-md-4' : 'col-md-6'">
          <div class="col-12">
            <div class="row items-center">
              <div class="relative-position">
                <q-checkbox
                  v-if="isShowDisableMarker"
                  v-model="killRemotely"
                  dense
                  :disable="killRemotelyDisabled"
                  :label="$t('form.markerAlreadyDiedRemotely')"
                  color="red"
                />
                <div
                  :class="['kill-stun-mask', killRemotelyDisabled ? 'cursor-not-allowed pointer-events-none': '']"
                  @click="killRemotelAndRemoteStunChange(!killRemotely, $t('form.killRemotely'), killRemotely ? 0 : 1, killRemotelyDisabled)"
                ></div>
              </div>
              <q-item-label
                class="cmdTime"
                caption
                v-if="isShowDisableMarker && killRemotely"
              >
                {{ toLocalTimeSelf(currentRowSetting.MarkerDisabledTime) }}
              </q-item-label>
            </div>
          </div>
          <div class="col-12">
            <div class="row items-center">
              <div class="relative-position">
                <q-checkbox
                  v-if="isShowDisableMarker"
                  v-model="remoteStun"
                  dense
                  :disable="remoteStunDisabled"
                  :label="$t('form.markerAlreadyFainted')"
                  color="orange"
                />
                <div
                  :class="['kill-stun-mask', remoteStunDisabled ? 'cursor-not-allowed pointer-events-none' : '']"
                  @click="killRemotelAndRemoteStunChange(!remoteStun, $t('form.remoteStun'), remoteStun ? 0 : 2, remoteStunDisabled)"
                ></div>
              </div>
              <q-item-label
                class="cmdTime"
                caption
                v-if="isShowDisableMarker && remoteStun"
              >
                {{ toLocalTimeSelf(currentRowSetting.MarkerDisabledTime) }}
              </q-item-label>
            </div>
          </div>
        </div>

        <!-- 第三列：摄像机已遥晕（仅在is4GMarkerPro为true时显示） -->
        <div class="col col-xs-12 col-sm-6  row items-start q-mb-sm gap-2" :class="is4GMarkerPro ? 'col-md-4' : 'col-md-6'" v-if="is4GMarkerPro">
          <div class="col-12">
            <div class="row items-center">
              <div class="relative-position">
                <q-checkbox
                  v-if="isShowDisableMarker"
                  v-model="cameraRemoteStun"
                  dense
                  :disable="cameraRemoteStunDisabled"
                  :label="$t('form.cameraAlreadyFainted')"
                  color="orange"
                />
                <div
                  v-if="isShowDisableMarker"
                  :class="['kill-stun-mask', cameraRemoteStunDisabled ? 'cursor-not-allowed pointer-events-none' : '']"
                  @click="handleCameraControlChange(!cameraRemoteStun, $t('form.cameraRemoteStun'), cameraRemoteStun ? 0 : 2, cameraRemoteStunDisabled)"
                ></div>
              </div>
              <q-item-label
                class="cmdTime"
                caption
                v-if="isShowDisableMarker && cameraRemoteStun"
              >
                {{ toLocalTimeSelf(currentRowSetting.CameraDisabledTime) }}
              </q-item-label>
            </div>
          </div>
        </div>
      </div>
    </div>
    <LonLat
      v-if="lonLatVisible"
      :modelValue="currentRow.GCJ02LngLat"
      :data="currentRow"
      :isMarker="true"
      v-model:visible="lonLatVisible"
      @update:model-value="getLngLatFromMap"
    ></LonLat>
  </div>
</template>

<script lang="ts">
  import { bysdb } from '@ygen/bysdb'
  import { isValidTime } from '@utils/validation'
  import { RedLineRuleOptVal, TLngLat } from '@utils/map'
  import {
    checkIs4GMarker,
    isHaveMaintainPerm,
    MarkerType,
    secondConfirmDialog
  } from '@utils/common'
  import { GET_DATA, NS } from '@store/data/methodTypes'
  import { DataName } from '@src/store'
  import {
    dayjs,
    getDateString,
    DateMask,
    TimeMask,
    toLocalTime,
    toUtcTime,
    utcTime,
    getDeffDay,
    sysUtcTime
  } from '@utils/dayjs'
  import dataEditMixin from '@utils/mixins/editor'
  // import bysMarkerEdit from '@utils/mixins/bysMarkerEdit'
  import { useBysMarkerData } from '@utils/vueUse/bysMarkerData'
  import { useBysMarkerEdit, getMarkerDefaultParams } from '@utils/vueUse/useBysMarkerEdit'
  import { BysMarkerAndUpdateInfo } from '@utils/bysdb.type'
  import { setGCJ02LngLat, setLngLat, toWGS84 } from '@utils/gcoord'
  import log from '@utils/log'
  import { cloneDeep } from 'lodash'
  import { useStore } from 'vuex'
  import { useI18n } from 'vue-i18n'
  import {
    ref,
    computed,
    ComputedRef,
    watch,
    onBeforeUnmount,
    defineAsyncComponent,
    onMounted,
  } from 'vue'
  import { QSelect, QInput, debounce } from 'quasar'
  import { ICCIDAndIMEIOptions, markerSetRemoteKillOrActive } from '@services/controller'
  import { crud } from '@src/ygen/crud'
  import { BysMarker } from '@services/dataStore'
  import { syncMarkerDataAndPartialUpdateSetting } from '@services/queryData';
  import { StrPubSub } from 'ypubsub'
  import { UpdateMarkerForm } from '@app/src/utils/pubSubSubject'

  let Cache4gMarkerParams: Record<string, any> | null = null
  let ControllerRID4gMarker = ''

  export default {
    name: 'BysMarkerEdit',
    mixins: [dataEditMixin],
    props: {
      modelValue: {
        type: Object,
        required: true,
      },
      updateRowData: {
        type: Boolean,
        default: false,
      },
    },
    components: {
      LonLat: defineAsyncComponent(() => import('@components/LonLat.vue')),
      InputNumber: defineAsyncComponent(() => import('@components/InputNumber.vue'))
    },
    emits: ['update:modelValue'],
    setup(props, { emit }) {

      // const ctx = getCurrentInstance()
      // const { proxy = {} as ComponentPublicInstance } = ctx ?? {}
      const store = useStore()
      const i18n = useI18n()
      let currentRow = ref<BysMarkerAndUpdateInfo>({ MarkerType: 0 })
      const { bysMarkerData } = useBysMarkerData()
      const bysMarkerEdit = useBysMarkerEdit(currentRow)
      const { controllerData, markerQueueNoChange } = bysMarkerEdit

      // const queueNoAvailable = ref(true)
      // const defaultMarkerQueueNo = ref('')
      const controllerFilter = ref('')
      const deviceFilter = ref('')
      let paramsWith4g = ref<Record<string, any>>(getMarkerDefaultParams())

      const redLineRef = ref<InstanceType<typeof QSelect>>()
      const exirationDate = ref<InstanceType<typeof QInput>>()
      const iccid = ref<InstanceType<typeof QSelect>>()
      const is4GMarkerPro = computed(() => {
        return currentRow.value.MarkerType === MarkerType.Net4GPro
      })

      let lonLatVisible = ref<boolean>(false)
      const currentRowSetting: Record<string, any> = computed(() => {
        return JSON.parse(currentRow.value.Setting as string)
      })
      const killRemotely = computed({
        get() {
          return currentRow.value.MarkerDisabled === 1
        },
        set(val) {
          currentRow.value['MarkerDisabled'] = val ? 1 : 0
        }
      })
      const remoteStun = computed({
        get() {
          return currentRow.value.MarkerDisabled === 2
        },
        set(val) {
          currentRow.value['MarkerDisabled'] = val ? 2 : 0
        }
      })
      const cameraKillRemotely = computed({
        get() {
          return currentRow.value.CameraDisabled === 1
        },
        set(val) {
          currentRow.value['CameraDisabled'] = val ? 1 : 0
        }
      })
      const cameraRemoteStun = computed({
        get() {
          return currentRow.value.CameraDisabled === 2
        },
        set(val) {
          currentRow.value['CameraDisabled'] = val ? 2 : 0
        }
      })

      const killRemotelyDisabled = computed(() => {
        return !HasInstallDevice.value
      })
      const remoteStunDisabled = computed(() => {
        return !HasInstallDevice.value || killRemotely.value
      })

      const cameraKillRemotelyDisabled = computed(() => {
        return !HasInstallDevice.value
      })

      const cameraRemoteStunDisabled = computed(() => {
        return !HasInstallDevice.value || cameraKillRemotely.value || remoteStun.value
      })

      async function handleCameraControlChange(desiredState: boolean, cmdStr: string, cmdVal: number, disabled: boolean) {
        if (disabled) {
          return
        }

        const msg = desiredState ? i18n.t('form.confirmKillOrStun', { cmd: cmdStr }) : i18n.t('form.confirmCancelKillOrStun', { cmd: cmdStr })
        const htmlMsg = '<i class="q-icon text-warning notranslate material-icons" aria-hidden="true" role="presentation" style="font-size: 2rem;padding-right: 0.5rem;">warning</i>'
        const isOk = await secondConfirmDialog(htmlMsg + msg, i18n.t('form.sendCmd'), i18n.t('common.cancel'), { html: true })

        if (!isOk) {
          return
        }

        currentRow.value.CameraDisabled = cmdVal
        const paramCrud: crud.IDMLParam = { KeyColumn: ['CameraDisabled'] }
        if (cmdVal) {
          syncCmdTime('CameraDisabledTime')
          paramCrud.KeyColumn?.push('Setting')
        }

        const sendOk = await markerSetRemoteKillOrActive(currentRow.value.MarkerDisabled ?? 0, cmdVal, currentRow.value)
        if (!sendOk) {
          return
        }
        let setting = {}
        if (cmdVal !== 0) {
          setting = {
            CameraDisabledTime: sysUtcTime()
          }
        }
        syncMarkerDataAndPartialUpdateSetting(currentRow.value.RID as string, setting)
      }

      const ExpirationDate = computed({
        get() {
          return currentRow.value.ExpirationDate?.split(' ')[0] ?? ''
        },
        set(val: string) {
          val = val ?? ''
          if (getDeffDay(dayjs(), val) < 0) {
            currentRow.value.ExpirationDate = toLocalTime(dayjs(), DateMask) + ' 23:59:59'
            return
          }
          currentRow.value.ExpirationDate = val + ' 23:59:59'
        }
      })
      const addrServeAndPort = computed(() => {
        const addrAndPort = paramsWith4g.value.addr.split(':')
        return [addrAndPort[0], addrAndPort[1] ?? '10000']
      })
      const addr = computed({
        get() {
          return addrServeAndPort.value[0]
        },
        set(val) {
          paramsWith4g.value.addr = paramsWith4g.value.addr.replace(/^(.*):/, () => (val ?? '') + ':')
        }
      })
      const port = computed({
        get() {
          return Number(addrServeAndPort.value[1])
        },
        set(val) {
          paramsWith4g.value.addr = paramsWith4g.value.addr.replace(/:(\d+)$/, () => ':' + val)
        }
      })
      const is4GMarkerType = computed(() => {
        return checkIs4GMarker(currentRow.value.MarkerType)
      })
      const redLineLinks = computed({
        get(): Array<number> {
          try {
            const settings = JSON.parse(currentRow.value.Setting ?? '{}')
            // 没有，为默认
            if (!settings.redLine) {
              return [RedLineRuleOptVal.Default]
            }
            return settings.redLine.length ? settings.redLine : [RedLineRuleOptVal.NotConnect]
          } catch (e) {
            log.error('JSON parse currentRow.Setting error')
            return []
          }
        },
        set(val: Array<number> | null) {
          try {
            const settings: { [key: string]: any } = JSON.parse(currentRow.value.Setting ?? '{}')
            // 该参数非空，空则为默认
            if (val === null || val[val.length - 1] === RedLineRuleOptVal.Default) {
              delete settings.redLine
              redLineRef.value?.hidePopup()
            } else if (val.length === 0 || val[val.length - 1] === RedLineRuleOptVal.NotConnect) {
              // 空数组，则为不连接
              settings.redLine = []
              redLineRef.value?.hidePopup()
            } else {
              settings.redLine = val.filter(v => ![RedLineRuleOptVal.Default, RedLineRuleOptVal.NotConnect].includes(v))
            }
            currentRow.value.Setting = JSON.stringify(settings)
          } catch (e) {
            log.error('marker redLine setter error')
          }
        },
      })
      const Lon = computed({
        get() {
          return currentRow.value.Lon ?? ''
        },
        set(val) {
          currentRow.value.Lon = val === '' ? null : val
        },
      })
      const Lat = computed({
        get() {
          return currentRow.value.Lat ?? ''
        },
        set(val) {
          currentRow.value.Lat = val === '' ? null : val
        },
      })
      const ControllerRIDOptions = computed(() => {
        return controllerData.value
          .map((data: bysdb.IDbController) => {
            return { label: data.ControllerNo, value: data.RID }
          })
          .filter(option => {
            const needle = controllerFilter.value.toLowerCase()
            return option.label.toLowerCase().includes(needle)
          })
      })
      const controllerChannelsOptions = computed(() => {
        const ChannelCount = parentController.value?.ChannelCount ?? 1
        const options: Array<{ [key: string]: any }> = []
        for (let i = 1; i <= ChannelCount; i++) {
          options.push({ label: i, value: i })
        }
        return options
      })
      const isRepeater = computed(() => {
        return parentController.value?.ControllerType === 1
      })
      const channelsOptions = computed(() => {
        const ChannelCount = 2
        const options: Array<{ [key: string]: any }> = []
        for (let i = 1; i <= ChannelCount; i++) {
          options.push({ label: i, value: i })
        }
        return options
      })
      const judgeMarkerQueueNo = computed(() => {
        return currentRow.value.ControllerRID === ''
      })
      // const checkWakeupBaseTimeForNotNull = computed(() => {
      //   return [
      //     'fulltime',
      //     val => required(val) || i18n.t('rules.required'),
      //     val => isValidTime(val) || i18n.t('rules.invalidTime'),
      //   ]
      // })
      const isShowDisableMarker = computed(() => {
        //需要有权限且在修改处才显示
        return (isHaveMaintainPerm() && props.updateRowData)
      })
      const parentController: ComputedRef<bysdb.IDbController | undefined> = computed(() => {
        return store.getters[`${NS}/${GET_DATA}`](DataName.Controller, currentRow.value.ControllerRID)
      })
      const HasInstallDevice = computed({
        get() {
          return !!currentRow.value.HasInstallDevice
        },
        set(val) {
          currentRow.value['HasInstallDevice'] = val
        },
      })
      const HasInstallStone = computed({
        get() {
          return !!currentRow.value.HasInstallStone
        },
        set(val) {
          currentRow.value['HasInstallStone'] = val
        },
      })
      const MarkerWakeupBaseTime = computed<string>({
        get() {
          let wakeUpTime = currentRow.value.MarkerWakeupBaseTime as string
          return isValidTime(wakeUpTime) ?
            toLocalTime((getDateString() + ' ' + wakeUpTime), TimeMask)
            : wakeUpTime
        },
        set(data) {
          currentRow.value.MarkerWakeupBaseTime = isValidTime(data) ?
            toUtcTime((getDateString() + ' ' + data), TimeMask) : data
        },
      })
      const markerRIDOptions = computed(() => {
        let options: Array<{ [key: string]: any }> =
          bysMarkerData.value.map((data: bysdb.IDbBysMarker) => {
            return {
              label: data.MarkerNo,
              value: data.MarkerHWID,
            }
          })

        options.sort((a, b) => a.value - b.value)
        // 添加默认选项
        const defaultOpt: Array<{ [key: string]: any }> = [
          {
            label: i18n.t('form.default'),
            value: RedLineRuleOptVal.Default,
          },
          {
            label: i18n.t('form.notConnectRedLine'),
            value: RedLineRuleOptVal.NotConnect,
          },
        ]
        options.unshift(...defaultOpt)

        // 根据输入内容过滤数据
        if (deviceFilter.value) {
          return options.filter(option => {
            const needle = deviceFilter.value.toLowerCase()
            return option.label.toLowerCase().includes(needle)
          })
        }

        return options
      })
      const haveMaintainPerm = computed(() => {
        return isHaveMaintainPerm()
      })
      const IMEIOptions = computed(() => {
        return Array.from(ICCIDAndIMEIOptions.value).map(item => {
          return {
            ...item,
            value: item.IMEI
          }
        })
      })
      const ICCIDOptions = computed(() => {
        return Array.from(ICCIDAndIMEIOptions.value).map(item => {
          return {
            ...item,
            value: item.ICCID
          }
        })
      })

      function toLocalTimeSelf(time) {
        return time ? toLocalTime(time) : ''
      }

      async function killRemotelAndRemoteStunChange(val: boolean, cmdStr: string, cmdVal: number, disabled: boolean) {
        if (disabled) {
          return
        }
        const msg = val ? i18n.t('form.confirmKillOrStun', { cmd: cmdStr }) : i18n.t('form.confirmCancelKillOrStun', { cmd: cmdStr })
        const htmlMsg = '<i class="q-icon text-warning notranslate material-icons" aria-hidden="true" role="presentation" style="font-size: 2rem;padding-right: 0.5rem;">warning</i>'
        const isOk = await secondConfirmDialog(htmlMsg + msg, i18n.t('form.sendCmd'), i18n.t('common.cancel'), { html: true })
        if (!isOk) {
          return
        }
        const param: crud.IDMLParam = {
          KeyColumn: ['MarkerDisabled'],
        }
        currentRow.value.MarkerDisabled = cmdVal
        currentRow.value.CameraDisabled = cmdVal
        if (cmdVal) {
          syncCmdTime('MarkerDisabledTime')
          param.KeyColumn?.push('Setting')
        }
        const sendOk = await markerSetRemoteKillOrActive(currentRow.value.MarkerDisabled ?? 0, currentRow.value.CameraDisabled ?? 0, currentRow.value)
        if (!sendOk) {
          return
        }
        let setting = {}
        //  遥晕或者遥毙才需要设置时间
        if (cmdVal !== 0) {
          setting = {
            MarkerDisabledTime: sysUtcTime(),
            CameraDisabledTime: sysUtcTime()
          }
        }
        syncMarkerDataAndPartialUpdateSetting(currentRow.value.RID as string, setting)
      }

      function syncCmdTime(key: string) {
        const Setting: Record<string, any> = JSON.parse(currentRow.value.Setting || '{}')
        Setting[key] = sysUtcTime()
        currentRow.value.Setting = JSON.stringify(Setting)
      }

      function ExpirationOptions(date) {
        // date picker组件的options年月日只支持格式为YYYY/MM/DD
        return date >= toLocalTime(dayjs(), 'YYYY/MM/DD')
      }

      function markerTypeChange(val) {
        if (val === MarkerType.Regular) {
          Cache4gMarkerParams = cloneDeep(paramsWith4g.value)
          currentRow.value.MarkerSettings = '{}'
          currentRow.value.ControllerRID = ControllerRID4gMarker
        } else {
          paramsWith4g.value = cloneDeep(Cache4gMarkerParams ?? getMarkerDefaultParams())
          ControllerRID4gMarker = currentRow.value.ControllerRID as string
          currentRow.value.ControllerRID = ''
          deviceIDChange(currentRow.value.MarkerHWID)
        }
      }

      function deviceIDChange(val) {
        paramsWith4g.value.deviceID = val
      }

      function HasInstallStoneChange(val: boolean) {
        // 如果石桩没有安装，则取消界桩已安装设备选项
        if (!val) {
          HasInstallDevice.value = false
          killRemotely.value = false
          remoteStun.value = false
        } else {
          syncCmdTime('installStoneTime')
        }
      }

      function HasInstallDeviceChange(val: boolean) {
        // 如果已经安装界桩设备，石桩默认已安装
        if (val) {
          syncCmdTime('installDeviceTime')
          if (!HasInstallStone.value) {
            syncCmdTime('installStoneTime')
            HasInstallStone.value = true
          }
        } else {
          killRemotely.value = false
          remoteStun.value = false
          cameraKillRemotely.value = false
          cameraRemoteStun.value = false
        }
      }

      function filterMarkerRedLine(val: string, update: Function) {
        deviceFilter.value = val
        update()
      }

      function filterControllerRID(val: string, update: Function) {
        controllerFilter.value = val
        update()
      }

      function controllerRIDInput() {
        if (isRepeater.value) {
          currentRow.value.ControllerChannel = 1
        }
        //主动申请排队号
        markerQueueNoChange(1, false)
      }

      function getLngLatFromMap(lngLat: TLngLat) {
        setLngLat(currentRow.value, toWGS84(lngLat))
        setGCJ02LngLat(currentRow.value, lngLat)
      }

      function setICCID(val) {
        currentRow.value.ICCID = val
        if (val && !currentRow.value.ExpirationDate) {
          currentRow.value.ExpirationDate = toLocalTime(dayjs().add(1, 'year'), DateMask) + ' 23:59:59'
        } else if (!val) {
          currentRow.value.ExpirationDate = ''
          currentRow.value.IEMI = ''
          exirationDate.value?.resetValidation()
          iccid.value?.resetValidation()
        }
      }

      function setIMEI(val) {
        currentRow.value.IMEI = val
      }

      function chooseIMEIAndICCID({ ICCID, IMEI }) {
        currentRow.value.ICCID = ICCID
        currentRow.value.IMEI = IMEI
      }

      let debounceWatchCurrentRow = (newVal) => {
        emit('update:modelValue', newVal)
      }

      debounceWatchCurrentRow = debounce(debounceWatchCurrentRow, 500)

      watch(() => props.modelValue, (newVal: BysMarkerAndUpdateInfo) => {
        currentRow.value = newVal
        if (checkIs4GMarker(currentRow.value.MarkerType)) {
          if (currentRow.value.MarkerSettings === '{}') return
          paramsWith4g.value = JSON.parse(currentRow.value.MarkerSettings || '{}')
        }
      }, { deep: true, immediate: true, })

      watch(() => currentRow.value, (newVal: BysMarkerAndUpdateInfo) => {
        if (!newVal.GCJ02LngLat) {
          setGCJ02LngLat(newVal)
        }
        debounceWatchCurrentRow(newVal)
      }, { deep: true, })

      // 向父组件传递界桩参数数据
      const getMarkerSettings = () => {
        paramsWith4g.value.dataVersion = utcTime()
        paramsWith4g.value.timerbase = currentRow.value.MarkerWakeupBaseTime as string
        paramsWith4g.value.timer = currentRow.value.MarkerDayInterval as number
        return cloneDeep(paramsWith4g.value)
      }

      onBeforeUnmount(() => {
        Cache4gMarkerParams = null
        ControllerRID4gMarker = ''
        StrPubSub.unsubscribe(UpdateMarkerForm, syncMarkerDataFromStore)
      })

      const syncMarkerDataFromStore = (rid: string, syncArr: string[]) => {
        const data = BysMarker.getData(rid) as bysdb.DbBysMarker
        for (let i = 0; i < syncArr.length; i++) {
          const key = syncArr[i]
          const keyValue = data[key]
          currentRow.value[key] = typeof keyValue === 'object' ? cloneDeep(keyValue) : keyValue
        }
      }

      onMounted(() => { 
        StrPubSub.subscribe(UpdateMarkerForm, syncMarkerDataFromStore)
      })

      return {
        ...(bysMarkerEdit),
        getMarkerSettings,
        currentRow,
        paramsWith4g,
        currentRowSetting,
        killRemotely,
        remoteStun,
        ExpirationDate,
        addr,
        port,
        is4GMarkerType,
        redLineLinks,
        Lon,
        Lat,
        ControllerRIDOptions,
        isShowDisableMarker,
        parentController,
        HasInstallDevice,
        HasInstallStone,
        MarkerWakeupBaseTime,
        markerRIDOptions,
        controllerChannelsOptions,
        isRepeater,
        channelsOptions,
        judgeMarkerQueueNo,
        lonLatVisible,
        ExpirationOptions,
        controllerRIDInput,
        filterControllerRID,
        deviceIDChange,
        markerTypeChange,
        getLngLatFromMap,
        filterMarkerRedLine,
        HasInstallDeviceChange,
        killRemotelAndRemoteStunChange,
        HasInstallStoneChange,
        toLocalTimeSelf,
        haveMaintainPerm,
        setICCID,
        setIMEI,
        IMEIOptions,
        ICCIDOptions,
        toLocalTime,
        TimeMask,
        chooseIMEIAndICCID,
        remoteStunDisabled,
        killRemotelyDisabled,
        cameraKillRemotely,
        cameraRemoteStun,
        cameraKillRemotelyDisabled,
        cameraRemoteStunDisabled,
        handleCameraControlChange,
        is4GMarkerPro,
      }
    }
  }
</script>

<style lang="scss">
  .marker-edit-content {
    width: 60vw;
  }

  .cmdTime {
    @media (min-width: 321px) {
      line-height: 21px !important;
    }

    @media (max-width: 375px) {
      margin-left: 0px;
    }

    @media (min-width: 376px) {
      margin-left: 5px;
    }
  }

  .opt-time {
    font-size: 12px;
    color: $grey;
  }

  .kill-stun-mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    cursor: pointer;
  }
</style>

<template>
  <q-dialog
    persistent
    ref="dialogRef"
    class="z-top"
    @hide="onDialogHide"
  >
    <q-card class="w-80 delete-warning-card">
      <q-card-section class="flex items-center gap-2 no-wrap">
        <q-avatar
          icon="warning"
          color="warning"
          size="lg"
        />
        <span>{{ content || $t('message.sureDeleteIt') }}</span>
      </q-card-section>

      <q-card-actions
        align="center"
        class="flex justify-center gap-2"
      >
        <q-btn
          :label="$t('common.cancel')"
          color="secondary"
          size="md"
          v-close-popup
          flat
          @click="onDialogCancel"
        />
        <q-btn
          :label="$t('common.delete')"
          color="negative"
          size="md"
          v-close-popup
          flat
          autofocus
          @click="onDialogOK"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script lang="ts" setup>
  import { useDialogPluginComponent } from 'quasar'

  interface DeleteWarningProps {
    content?: string
  }

  defineProps<DeleteWarningProps>()

  const { dialogRef, onDialogHide, onDialogOK, onDialogCancel } = useDialogPluginComponent()

  defineEmits([
    // REQUIRED; need to specify some events that your
    // component will emit through useDialogPluginComponent()
    ...useDialogPluginComponent.emits
  ])
</script>

<style lang="scss"></style>

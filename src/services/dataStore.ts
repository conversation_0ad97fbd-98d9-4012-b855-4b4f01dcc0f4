import { DataName, Store } from '@src/store'
import * as methods from '@src/store/data/methodTypes'
import { DefUuid } from '@src/config'
import { org } from '@ygen/org'
import { PubSub } from 'ypubsub'
import { user } from '@ygen/user'
import { user as userPermission } from '@ygen/userPermission'
import { user as userSession } from '@ygen/userSession'
import { user as userOrgPrivilege } from '@ygen/userOrgPrivilege'
import { bysdb } from '@ygen/bysdb'
import { config } from '@ygen/config'
import { image } from '@ygen/image'
import { LocalRole } from '@utils/bysdb.type'
import { shallowReactive } from 'vue'

function getKey(key: string): string {
  return `${methods.NS}/${key}`
}

export type TData = {
  [key: string]: any
  RID: string
}

function sortBy<T = TData>(dataList: T[], options?: ISortOptions) {
  const opts: ISortOptions = Object.assign({}, options)
  return dataList.sort((a: T, b: T) => {
    const sortBy = opts.sortBy as string
    if (opts.descending) {
      return b[sortBy] - a[sortBy]
    }
    return a[sortBy] - b[sortBy]
  })
}

export interface ISortOptions {
  sortBy?: string
  descending?: boolean
}

export interface IDataStoreOptions extends ISortOptions {
  [key: string]: any
}

export interface IDataStore<T = TData> {
  name: DataName
  options: IDataStoreOptions

  getData(key: string): undefined | T

  getDataList(): T[]

  sortBy(data: T[], options?: ISortOptions)

  setData(key: string, value: T): DataStore<T>

  setPartData(key: string, value: { [key: string]: any }): DataStore<T>

  deleteData(key: string): DataStore<T>

  getDataByIndex(index: string): undefined | T

  setDataIndex(index: string, key: string): DataStore<T>

  getDataIndex(index: string): string | undefined

  getParent(key: string): undefined | org.IDbOrg

  getParentName(key: string): string
}

export class DataStore<T = TData> implements IDataStore<T> {
  name: DataName
  options: IDataStoreOptions

  constructor(name: DataName, options?: IDataStoreOptions) {
    this.name = name
    this.options = Object.assign({
      sortBy: '',
      descending: false,
    }, options)
  }

  commit(type: string, key: string, value?: any): void {
    const payload = { type: this.name, key, value }
    Store.commit(getKey(type), payload)
  }

  getter(type: string, key?: string): any {
    return Store.getters[getKey(type)](this.name, key)
  }

  dispatch(type: string, key?: string): any {
    const payload = { type: this.name, key }
    return Store.dispatch(getKey(type), payload)
  }

  getData(key: string): undefined | T {
    return this.getter(methods.GET_DATA, key)
  }

  getDataList(): T[] {
    let dataList = this.getter(methods.GET_DATA)
    // key不存在则为数组，按配置进行默认的排序
    return sortBy(dataList, this.options)
  }

  sortBy(data: T[], options: ISortOptions = this.options) {
    return sortBy(data, options)
  }

  setData(key: string, value: T): DataStore<T> {
    this.commit(methods.SET_DATA, key, value)

    return this
  }

  setPartData(key: string, value: { [key: string]: any }): DataStore<T> {
    this.commit(methods.SET_PART_DATA, key, value)

    return this
  }

  deleteData(key: string): DataStore<T> {
    this.commit(methods.DEL_DATA, key)

    return this
  }

  getDataByIndex(index: string): undefined | T {
    return this.getter(methods.GET_DATA_BY_INDEX, index)
  }

  setDataIndex(key: string, value: string): DataStore<T> {
    this.commit(methods.SET_INDEX, key, value)

    return this
  }

  getDataIndex(index: string): string | undefined {
    return this.getter(methods.GET_INDEX, index)
  }

  getParent(key: string): undefined | org.IDbOrg {
    return Store.getters[getKey(methods.GET_PARENT)](key)
  }

  getParentName(key: string): string {
    return this.getParent(key)?.ShortName ?? ''
  }
}

export class UnitDataStore<T extends TData | org.IDbOrg = org.IDbOrg> extends DataStore<T> {
  constructor(options?: IDataStoreOptions) { super(DataName.Unit, options) }

  getDataList(): T[] {
    return super.getDataList().filter(item => item.RID !== DefUuid)
  }

  deleteData(key: string): DataStore<T> {
    this.dispatch('deleteUnitData', key)

    return this
  }
}

export const Config = new DataStore<config.IDbConfig>(DataName.Config)
export const Image = new DataStore<image.IDbImage>(DataName.Image)
export const Permission = new DataStore<userPermission.IDbPermission>(DataName.Permission)
export const Role = new DataStore<LocalRole>(DataName.Role, { sortBy: 'SortValue' })
export const RolePermission = new DataStore<userPermission.IDbRolePermission>(DataName.RolePermission)
export const UserRole = new DataStore<user.IDbUserRole>(DataName.UserRole)
export const UserSession = new DataStore<userSession.IDbUserSession>(DataName.UserSession)
export const UserOrgPrivilege = new DataStore<userOrgPrivilege.IDbUserOrgPrivilege>(DataName.UserOrgPrivilege)
export const Unit = new UnitDataStore<org.IDbOrg>({ sortBy: 'SortValue' })
export const User = new DataStore<user.IDbUser>(DataName.User, { sortBy: 'UserID' })
export const Controller = new DataStore<bysdb.IDbController>(DataName.Controller, { sortBy: 'ControllerHWID' })
export const MediaInfo = new DataStore<bysdb.IDbMediaInfo>(DataName.MediaInfo)
export const BysMarker = new DataStore<bysdb.IDbBysMarker>(DataName.BysMarker, { sortBy: 'MarkerHWID' })
export const NFCPatrolLine = new DataStore<bysdb.IDbNFCPatrolLine>(DataName.NFCPatrolLine)
export const NFCPatrolLineDetail = new DataStore<bysdb.IDbNFCPatrolLineDetail>(DataName.NFCPatrolLineDetail)
export const NFCPatrolLineAndRules = new DataStore<bysdb.IDbNFCPatrolLineAndRules>(DataName.NFCPatrolLineAndRules)
export const NFCPatrolLineRules = new DataStore<bysdb.IDbNFCPatrolLineRules>(DataName.NFCPatrolLineRules)

export interface DataStoreEvent {
  [key: string]: any

  SetData?: string
  DeleteData?: string
}

export interface ILocalDataStoreOption extends ISortOptions {
  [key: string]: any
}

export interface ILocalDataStore<T = TData> {
  name: string
  data: { [key: string]: T }
  index: { [key: string]: string }
  pubSub: any
  events: DataStoreEvent
  options: ILocalDataStoreOption

  getData(key: string): undefined | T

  getDataList(): T[]

  sortBy(data: T[], options?: ISortOptions)

  getDataByIndex(index: string): undefined | T

  setData(key: string, value: T): LocalDataStore<T>

  setPartData(key: string, value: { [key: string]: any }): LocalDataStore<T>

  deleteData(key: string): LocalDataStore<T>

  setDataIndex(index: string | number, key: string): LocalDataStore<T>

  getDataIndex(index: string | number): string | undefined

  deleteDataIndex(key: string): LocalDataStore<T>

  getParent(key: string): undefined | org.IDbOrg

  getParentName(key: string): string
}

export class LocalDataStore<T = TData> implements ILocalDataStore<T> {
  name: string
  data: { [key: string]: T }
  index: { [key: string]: string }
  pubSub: any
  events: DataStoreEvent
  options: ILocalDataStoreOption

  constructor(name: string, options?: ILocalDataStoreOption) {
    this.name = name
    this.data = {}
    this.index = {}
    this.pubSub = new PubSub<string>()
    this.events = {
      SetData: `${name}-SetData`,
      DeleteData: `${name}-DeleteData`,
    }
    this.options = Object.assign({
      sortBy: '',
      descending: false,
    }, options)
  }

  getData(key: string): undefined | T {
    return this.data[key]
  }

  getDataList(): T[] {
    let list = Object.keys(this.data)
      .map(key => {
        return this.data[key]
      })

    return sortBy(list, this.options)
  }

  sortBy(data: T[], options: ILocalDataStoreOption = this.options) {
    return sortBy(data, options)
  }

  getDataByIndex(index: string): undefined | T {
    const key = this.index[index]
    return this.data[key]
  }

  setData(key: string, value: T, isPublish = true): LocalDataStore<T> {
    this.data[key] = value
    if (isPublish) {
      this.pubSub.publish(this.events.SetData, this.data[key])
    }
    return this
  }

  setPartData(key: string, value: { [key: string]: any }): LocalDataStore<T> {
    Object.assign(this.data, { [key]: value })
    return this
  }

  deleteData(key: string, isPublish = true): LocalDataStore<T> {
    const data = this.data[key]
    if (key in this.data) {
      delete this.data[key]
      if (isPublish) {
        this.pubSub.publish(this.events.DeleteData, data)
      }
    }

    // 删除索引
    this.deleteDataIndex(key)

    return this
  }

  setDataIndex(index: string | number, key: string): LocalDataStore<T> {
    this.index[index] = key
    return this
  }

  getDataIndex(index: string | number): string | undefined {
    return this.index[index]
  }

  deleteDataIndex(key: string): LocalDataStore<T> {
    Object.keys(this.index).filter(i => this.index[i] === key)
      .forEach(i => (delete this.index[i]))

    return this
  }

  getParent(key: string): undefined | org.IDbOrg {
    return Store.getters[getKey(methods.GET_PARENT)](key)
  }

  getParentName(key: string): string {
    return this.getParent(key)?.ShortName ?? ''
  }
}

// export const BysMarker = new LocalDataStore<bysdb.IDbBysMarker>(DataName.BysMarker, { sortBy: 'MarkerHWID' })

// 异常界桩的RID集合
export const BysMarkerAbnormalRidSet = shallowReactive(new Set<string>())

// // 订阅处理界桩的上级单位被删除事件
// StrPubSub.subscribe('deleteBysMarkerByDelUnit', (key: string) => {
//   const dataList = BysMarker.getDataList()
//   const waitDeleteList: Array<bysdb.IDbBysMarker> = dataList.filter(item => item.OrgRID === key)

//   for (let i = 0; i < waitDeleteList.length; i++) {
//     const data = waitDeleteList[i]
//     BysMarker.deleteData(data.RID + '')
//     StrPubSub.publish(DeleteDbBysMarker, data)
//   }
// })

export default {
  Config,
  Image,
  Permission,
  Role,
  RolePermission,
  UserRole,
  UserSession,
  UserOrgPrivilege,
  Unit,
  User,
  Controller,
  BysMarker,
  MediaInfo,
  NFCPatrolLine,
}

export const unknownUnitData: Array<org.IDbOrg> = window.unknownUnitData = []
export const unknownUserData: Array<user.IDbUser> = []

export function getUnknownUnit(rid: string): org.IDbOrg | undefined {
  return unknownUnitData.find(data => data.RID === rid)
}

export function getLocalUnit(rid: string): org.IDbOrg | undefined {
  return Unit.getData(rid) || getUnknownUnit(rid)
}

export function getUnknownUser(rid: string): user.IDbUser | undefined {
  return unknownUserData.find(data => data.RID === rid)
}

export function getLocalUser(rid: string): user.IDbUser | undefined {
  return User.getData(rid) || getUnknownUser(rid)
}

export function roleUpdateIsMyRole(dest: LocalRole, source: LocalRole | undefined): LocalRole {
  if (source?.isMyRole) {
    dest.isMyRole = source.isMyRole
  }

  return dest
}

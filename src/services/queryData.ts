import { PrpcDbOrg } from '@ygen/org.rpc.yrpc'
import { org } from '@ygen/org'
import { crud } from '@ygen/crud'
import { PrpcDbUser, PrpcDbUserRole } from '@ygen/user.rpc.yrpc'
import { user } from '@ygen/user'
import { ICallOption, rpcCon, TRpcStream } from 'jsyrpc'
import { yrpcmsg } from 'yrpcmsg'
import log from '@utils/log'
import dataStore, {
  BysMarker, BysMarkerAbnormalRidSet,
  Controller,
  NFCPatrolLine,
  NFCPatrolLineAndRules,
  NFCPatrolLineDetail,
  NFCPatrolLineRules,
  Permission,
  Role,
  Unit,
  unknownUnitData,
  unknownUserData,
  User,
} from './dataStore'
import { DataName, Store } from '@src/store'
import { Notify } from 'quasar'
import { RpcDbConfig } from '@ygen/config.rpc.yrpc'
import { config } from '@ygen/config'
import { RpcDbImage } from '@ygen/image.rpc.yrpc'
import { image } from '@ygen/image'
import { PrpcDbUserOrgPrivilege } from '@ygen/userOrgPrivilege.rpc.yrpc'
import * as UserOrgPrivilegeY from '@ygen/userOrgPrivilege'
import { user as userOrgPrivilege } from '@ygen/userOrgPrivilege'
import { PrpcDbRole, PrpcDbRolePermission } from '@ygen/userPermission.rpc.yrpc'
import { user as userPermission } from '@ygen/userPermission'
import { RpcDbUserSession } from '@ygen/userSession.rpc.yrpc'
import { user as userSession } from '@ygen/userSession'
import { RpcUser } from '@ygen/user.api.yrpc'
import { user as userApi } from '@ygen/user.api'
import { rpc } from '@ygen/rpc'
import { globalLoading, NoRows } from '@src/config'
import { i18n } from '@boot/i18n'
import { StrPubSub } from 'ypubsub'
import { PrpcDbBysMarker, PrpcDbController, PrpcDbMediaInfo, PrpcDbNFCPatrolLine, PrpcDbNFCPatrolLineAndRules, PrpcDbNFCPatrolLineDetail, PrpcDbNFCPatrolLineRules } from '@ygen/bysdb.rpc.yrpc'
import { bysdb } from '@ygen/bysdb'
import { bysdb as bysdbList } from '@ygen/bysdb.list'
import {
  bs_base_timeout,
  bs_relay_timeout,
  controllerCmdProcess,
  GotControllerData,
  updateControllerStationDeviceNoRelation,
} from './controller'
import {
  BysMarkerDumpingAlarm,
  ControllerOnline,
  QueryBysMarkerFinish,
  QueryConfigDataFinish,
  QueryControllerFinish,
  QueryMediaInfoFinish,
  QueryRoleFinish,
  QueryRolePermissionFinish,
  QueryUnitFinish,
  QueryUnknownUnitDataOnceFinish,
  QueryUserFinish,
  QueryUserOrgPrivilegeFinish,
  QueryUserRoleFinish,
  ShowNotInstalledMarker,
  SortDeviceTree,
} from '@utils/pubSubSubject'
import { RpcBysApi } from '@ygen/bys.api.yrpc'
import { doc } from '@ygen/bys.api'
import { DefaultDataStr, getDiffMin, getSubtractTime, utcTime } from '@utils/dayjs'
import { setGCJ02LngLat } from '@utils/gcoord'
import {
  checkAllControllerError,
  checkBysMarkerIsAlarm,
  checkICCIDIsExpire,
  checkoutAbnormalBysMarker,
  ControllerDeviceType,
  ControllerTypes,
  Deferred,
  emptyFn, MarkerType,
  rawObjectProps,
  updateMarkerStatusAfterAction,
  checkIs4GMarker, partialUpdateMarkerSetting,
} from '@utils/common'
import { SubScribeUpdateEvt, syncLocalStore } from '@utils/syncLocalStore'
import { UInt8Array2String } from '@utils/crypto'
import {
  BysMarkerAndUpdateInfo,
  ControllerAndUpdateCmd,
  LocalRole,
} from '@utils/bysdb.type'
import { checkOperationPermission, DbName, OperationType } from '@utils/permission'
import { user as rolePermissionList } from '@ygen/userPermission.list'
import IGrpcMeta = yrpcmsg.IGrpcMeta
import IDbOrg = org.IDbOrg
import Ymsg = yrpcmsg.Ymsg
import { bysproto } from '@ygen/controller'
import { cloneDeep } from 'lodash'
import { DefaultOrg } from '@store/state'
import { syncDeviceLayerMarker } from '@utils/map'

// 各类数据请求、查询封装
type DbData = config.IDbConfig | image.IDbImage | org.IDbOrg | user.IDbUser |
  userOrgPrivilege.IDbUserOrgPrivilege | userPermission.IDbPermission |
  userPermission.IDbRole | userPermission.IDbRolePermission |
  userSession.IDbUserSession

const RpcDb: { [key: string]: any } = {
  [DataName.Unit]: PrpcDbOrg,
  [DataName.User]: PrpcDbUser,
  [DataName.Role]: PrpcDbRole,
  [DataName.RolePermission]: PrpcDbRolePermission,
  [DataName.UserRole]: PrpcDbUserRole,
  [DataName.Config]: RpcDbConfig,
  [DataName.Image]: RpcDbImage,
  [DataName.UserOrgPrivilege]: PrpcDbUserOrgPrivilege,
  [DataName.UserSession]: RpcDbUserSession,
  [DataName.Controller]: PrpcDbController,
  [DataName.BysMarker]: PrpcDbBysMarker,
  [DataName.MediaInfo]: PrpcDbMediaInfo,
  [DataName.NFCPatrolLine]: PrpcDbNFCPatrolLine,
  [DataName.NFCPatrolLineDetail]: PrpcDbNFCPatrolLineDetail,
  [DataName.NFCPatrolLineAndRules]: PrpcDbNFCPatrolLineAndRules,
  [DataName.NFCPatrolLineRules]: PrpcDbNFCPatrolLineRules,
}

export interface PRpcDbQueryApi {
  dbName: string

  Query(option?: ICallOption): void

  SelectOne(rid: string): Promise<DbData>

  SelectMany(param: crud.IDMLParam, option?: ICallOption): void
}

export class PRpcDbQuery implements PRpcDbQueryApi {
  dbName: string

  constructor(name: DataName) {
    this.dbName = name
  }

  Query(option?: ICallOption): void {
    const reqData: crud.IPrivilegeParam = {
      System: Store.state.System,
      SessionID: Store.state.SessionRID,
    }
    const req: crud.PrivilegeParam = new crud.PrivilegeParam(reqData)
    const opts: ICallOption = {
      OnResult: (data: DbData, rpcCmd: yrpcmsg.Ymsg, meta?: IGrpcMeta) => {
        log.info(`Query [${this.dbName}] result`, data, rpcCmd, meta)
      },
      OnServerErr: (errRpc: yrpcmsg.Ymsg) => {
        log.error(`Query [${this.dbName}] server error`, errRpc)
        // 判断是否没有数据
        if (errRpc.Optstr.includes(NoRows)) {
          return
        }
        Notify.create({
          type: 'negative',
          icon: 'error',
          message: errRpc.Optstr,
          position: 'top',
        })
      },
      OnLocalErr: (err: any) => {
        log.error(`Query [${this.dbName}] local error`, err)
        Notify.create({
          type: 'negative',
          icon: 'error',
          message: err.toString(),
          position: 'top',
        })
      },
      OnTimeout: (v?: any) => {
        log.warn(`Query [${this.dbName}] timeout`, v)
        Notify.create({
          type: 'warning',
          icon: 'warning',
          message: i18n.global.t('message.requestTimeout') as string,
          position: 'top',
        })
      },
      OnStreamFinished: (rpcCmd: yrpcmsg.Ymsg) => {
        log.info(`Query [${this.dbName}] finish`, rpcCmd)
      },
      ...option,
    }
    RpcDb[this.dbName]?.Query(req, opts)
  }

  QueryBatch(option?: ICallOption): void {
    const reqData: crud.IPrivilegeParam = {
      System: Store.state.System,
      SessionID: Store.state.SessionRID,
      QueryCondition: {
        Batch: 600,
      },
    }
    const req: crud.PrivilegeParam = new crud.PrivilegeParam(reqData)
    const opts: ICallOption = {
      OnResult: (data: DbData, rpcCmd: yrpcmsg.Ymsg, meta?: IGrpcMeta) => {
        log.info(`Query [${this.dbName}] result`, data, rpcCmd, meta)
      },
      OnServerErr: (errRpc: yrpcmsg.Ymsg) => {
        log.error(`Query [${this.dbName}] server error`, errRpc)
        // 判断是否没有数据
        if (errRpc.Optstr.includes(NoRows)) {
          return
        }
        Notify.create({
          type: 'negative',
          icon: 'error',
          message: errRpc.Optstr,
          position: 'top',
        })
      },
      OnLocalErr: (err: any) => {
        log.error(`Query [${this.dbName}] local error`, err)
        Notify.create({
          type: 'negative',
          icon: 'error',
          message: err.toString(),
          position: 'top',
        })
      },
      OnTimeout: (v?: any) => {
        log.warn(`Query [${this.dbName}] timeout`, v)
        Notify.create({
          type: 'warning',
          icon: 'warning',
          message: i18n.global.t('message.requestTimeout') as string,
          position: 'top',
        })
      },
      OnStreamFinished: (rpcCmd: yrpcmsg.Ymsg) => {
        log.info(`Query [${this.dbName}] finish`, rpcCmd)
      },
      ...option,
    }
    RpcDb[this.dbName]?.QueryBatch(req, opts)
  }

  SelectOne(rid: string): Promise<DbData> {
    return new Promise((resolve, reject) => {
      const query: crud.IDMLParam = {
        KeyColumn: ['RID'],
        KeyValue: [rid],
      }
      const options: ICallOption = {
        OnResult: (data: DbData/*, rpcCmd: yrpcmsg.Ymsg, meta?: IGrpcMeta*/) => {
          // log.info(`SelectOne [${this.dbName}] result`, data, rpcCmd, meta)
          resolve(data)
        },
        OnServerErr: (errRpc: yrpcmsg.Ymsg) => {
          log.error(`SelectOne [${this.dbName}] server error`, errRpc)
          reject('server')
        },
        OnLocalErr: (err: any) => {
          log.error(`SelectOne [${this.dbName}] local error`, err)
          reject('local')
        },
        OnTimeout: (v: any) => {
          log.warn(`SelectOne [${this.dbName}] timeout`, v)
          reject('timeout')
        },
      }
      RpcDb[this.dbName]?.SelectOne(query, options)
    })
  }

  SelectMany(param: crud.IDMLParam, option?: ICallOption): void {
    const query: crud.DMLParam = new crud.DMLParam(param)
    const opts: ICallOption = {
      OnResult: (data: DbData, rpcCmd: yrpcmsg.Ymsg, meta?: IGrpcMeta) => {
        log.info(`Query [${this.dbName}] result`, data, rpcCmd, meta)
      },
      OnServerErr: (errRpc: yrpcmsg.Ymsg) => {
        log.error(`Query [${this.dbName}] server error`, errRpc)
        // 判断是否没有数据
        if (errRpc.Optstr.includes(NoRows)) {
          return
        }
        Notify.create({
          type: 'negative',
          icon: 'error',
          message: errRpc.Optstr,
          position: 'top',
        })
      },
      OnLocalErr: (err: any) => {
        log.error(`Query [${this.dbName}] local error`, err)
        Notify.create({
          type: 'negative',
          icon: 'error',
          message: err.toString(),
          position: 'top',
        })
      },
      OnTimeout: (v?: any) => {
        log.warn(`Query [${this.dbName}] timeout`, v)
        Notify.create({
          type: 'warning',
          icon: 'warning',
          message: i18n.global.t('message.requestTimeout') as string,
          position: 'top',
        })
      },
      OnStreamFinished: (rpcCmd: yrpcmsg.Ymsg) => {
        log.info(`Query [${this.dbName}] finish`, rpcCmd)
      },
      ...option,
    }
    RpcDb[this.dbName]?.SelectMany(query, opts)
  }
}

// 数据请求类实例对象
export const UnitDbQuery = new PRpcDbQuery(DataName.Unit)
export const UserDbQuery = new PRpcDbQuery(DataName.User)
export const RoleDbQuery = new PRpcDbQuery(DataName.Role)
export const RolePermissionDbQuery = new PRpcDbQuery(DataName.RolePermission)
export const UserRoleDbQuery = new PRpcDbQuery(DataName.UserRole)
export const ControllerDbQuery = new PRpcDbQuery(DataName.Controller)
export const BysMarkerDbQuery = new PRpcDbQuery(DataName.BysMarker)
export const MediaInfoDbQuery = new PRpcDbQuery(DataName.MediaInfo)
// export const ImageQuery = new PRpcDbQuery(DataName.Image)
export const ConfigQuery = new PRpcDbQuery(DataName.Config)
export const UserOrgPrivilegeQuery = new PRpcDbQuery(DataName.UserOrgPrivilege)
// export const UserSessionQuery = new PRpcDbQuery(DataName.UserSession)
export const NFCPatrolLineDbQuery = new PRpcDbQuery(DataName.NFCPatrolLine)
export const NFCPatrolLineDetailDbQuery = new PRpcDbQuery(DataName.NFCPatrolLineDetail)
export const NFCPatrolLineAndRulesDbQuery = new PRpcDbQuery(DataName.NFCPatrolLineAndRules)
export const NFCPatrolLineRulesDbQuery = new PRpcDbQuery(DataName.NFCPatrolLineRules)

// 请求用户有权限的单位
export function queryOrgData(): Promise<boolean> {
  return new Promise((resolve) => {
    const options: ICallOption = {
      // param: data: org.IDbOrg, rpcCmd: yrpcmsg.Ymsg, meta?: IGrpcMeta
      OnResult: (data: org.IDbOrg) => {
        // log.info(`Query [${DataName.Unit}] result`, data, rpcCmd, meta)
        if (!data.RID) {
          return
        }
        dataStore.Unit.setData(data.RID, data)
        if (data.OrgID) {
          dataStore.Unit.setDataIndex(data.OrgID, data.RID)
        }
      },
      OnStreamFinished: (rpcCmd: yrpcmsg.Ymsg) => {
        log.info(`Query [${DataName.Unit}] finish`, rpcCmd)
        resolve(true)
      },
    }
    UnitDbQuery.Query(options)
  })
}

export function IsUserHasOrgPrivilege(req: userApi.IReqUserHasOrgPrivilege): Promise<rpc.IRpcCommon> {
  return new Promise((resolve, reject) => {
    const options: ICallOption = {
      OnResult: (data: rpc.IRpcCommon, rpcCmd: yrpcmsg.Ymsg, meta?: IGrpcMeta) => {
        log.info('IsUserHasOrgPrivilege result', data, rpcCmd, meta)
        resolve(data)
      },
      OnServerErr: (errRpc: yrpcmsg.Ymsg) => {
        log.error('IsUserHasOrgPrivilege server error', errRpc)
        reject('server')
      },
      OnLocalErr: (err: any) => {
        log.error('IsUserHasOrgPrivilege local error', err)
        reject('local')
      },
      OnTimeout: (v: any) => {
        log.warn('IsUserHasOrgPrivilege timeout', v)
        reject('timeout')
      },
    }
    RpcUser.IsUserHasOrgPrivilege(req, options)
  })
}

// 请求用户权限关联单位数据
export function queryUserOrgPrivilegeData(): Promise<boolean> {
  return new Promise((resolve) => {
    const options: ICallOption = {
      OnResult: (data: UserOrgPrivilegeY.user.IDbUserOrgPrivilege) => {
        if (!data.RID) {
          return
        }
        dataStore.UserOrgPrivilege.setData(data.RID, data)
      },
      OnStreamFinished: (rpcCmd: yrpcmsg.Ymsg) => {
        log.info(`Query [${DataName.UserOrgPrivilege}] finish`, rpcCmd)
        resolve(true)
      },
    }
    UserOrgPrivilegeQuery.Query(options)
  })
}

export function configSetData(data: config.IDbConfig) {
  if (!data.RID) {
    return
  }
  dataStore.Config.setData(data.RID, data)
  const indexKey = `${data.ConfKey}.${data.OrgRID}`
  dataStore.Config.setDataIndex(indexKey, data.RID)
}

export function queryDbConfig(): Promise<boolean> {
  return new Promise((resolve) => {
    const options: ICallOption = {
      // param: data: org.IDbOrg, rpcCmd: yrpcmsg.Ymsg, meta?: IGrpcMeta
      OnResult: (data: config.IDbConfig) => {
        // log.info(`Query [${DataName.Unit}] result`, data, rpcCmd, meta)
        configSetData(data)
      },
      OnStreamFinished: (rpcCmd: yrpcmsg.Ymsg) => {
        log.info(`Query [${DataName.Config}] finish`, rpcCmd)
        resolve(true)
      },
    }
    ConfigQuery.Query(options)
  })
}

// 查找权限内公共的父级单位
export async function findTopLevelOrgWithinPermission(): Promise<org.IDbOrg> {
  const uints = dataStore.Unit.getDataList()

  // 没有单位的权限，返回默认的root单位
  if (uints.length === 0) {
    return Promise.resolve(DefaultOrg)
  }

  // 筛选出权限内的顶级单位
  const topOrgs = uints.filter(item => !dataStore.Unit.getData(item.OrgRID as string))

  // 只有一个顶级单位，直接返回
  if (topOrgs.length === 1) {
    return Promise.resolve(topOrgs[0])
  }

  // 循环查找出公共的父级单位
  const dbUints: Record<string, org.IDbOrg> = {}
  while (topOrgs.length > 1) {
    let firstTopOrg = topOrgs[0]
    // 倒序遍历，避免删除节点后索引异常
    for (let i = topOrgs.length - 1; i > 0; i--) {
      const topOrg = topOrgs[i]
      // 多个顶级单位中，如果其中一个单位的上级为默认根单位，则循环结束，返回根单位
      if (firstTopOrg.OrgRID === DefaultOrg.RID || topOrg.OrgRID === DefaultOrg.RID) {
        return Promise.resolve(DefaultOrg)
      }

      // 判断是否为上下级单位
      if (firstTopOrg.RID === topOrg.OrgRID) {
        topOrgs.splice(i, 1)
        continue
      }

      // 同级兄弟单位，使用上级单位替换当前遍历的两个单位
      if (firstTopOrg?.OrgRID === topOrg.OrgRID) {
        topOrgs.splice(i, 1)
        topOrgs.splice(0, 1)

        const firstTopOrgRID = firstTopOrg!.OrgRID as string
        let firstTopOrgParent: org.IDbOrg | undefined = dbUints[firstTopOrgRID]
        if (!firstTopOrgParent) {
          firstTopOrgParent = await UnitDbQuery.SelectOne(firstTopOrgRID).catch(() => undefined)
        }
        if (firstTopOrgParent) {
          dbUints[firstTopOrgRID] = firstTopOrgParent
          topOrgs.unshift(firstTopOrgParent)
        } else {
          // 查找不到公共的上级，返回默认根单位
          return Promise.resolve(DefaultOrg)
        }

        break
      }

      // 使用上级单位来替换当前单位
      const OrgRID = topOrg.OrgRID as string
      let parent: org.IDbOrg | undefined = dbUints[OrgRID]
      if (!parent) {
        parent = await UnitDbQuery.SelectOne(OrgRID).catch(() => undefined)
      }

      // 查找到父级单位，替换当前位置，否则移除该单位，不再处理
      if (parent) {
        topOrgs[i] = parent
        dbUints[OrgRID] = parent
      } else {
        topOrgs.splice(i, 1)
      }
    }
  }

  // 循环结束后，有且仅有一个单位数据，为公共的父级单位
  return Promise.resolve(topOrgs[0])
}

// 请求用户数据
export function queryUserData(): Promise<boolean> {
  return new Promise((resolve) => {
    const options: ICallOption = {
      OnResult: (data: user.IDbUser) => {
        if (!data.RID) {
          return
        }
        dataStore.User.setData(data.RID, data)
        if (data.UserID) {
          dataStore.User.setDataIndex(data.UserID, data.RID)
        }
      },
      OnStreamFinished: (rpcCmd: yrpcmsg.Ymsg) => {
        log.info(`Query [${DataName.User}] finish`, rpcCmd)
        resolve(true)
      },
    }
    UserDbQuery.Query(options)
  })
}

// 请求权限数据
export function queryMyPermissionData(): Promise<boolean> {
  return new Promise((resolve) => {
    const data: rpc.IRpcCommon = {
      System: Store.state.System,
      SessionID: Store.state.SessionRID,
      Err: Store.state.UserRID,
    }
    const rpcData: rpc.RpcCommon = new rpc.RpcCommon(data)
    const options: ICallOption = {
      OnResult: (data: userPermission.IDbPermission) => {
        if (!data.RID) {
          return
        }
        Permission.setData(data.RID, data).setDataIndex(data.PermissionName as string, data.RID)
      },
      OnStreamFinished: (rpcCmd: yrpcmsg.Ymsg) => {
        log.info('Query [MyPermission] finish', rpcCmd)
        resolve(true)
      },
    }

    RpcUser.MyPermission(rpcData, options)
  })
}

// 请求有权限单位下的角色数据
export function queryRoleData(): Promise<boolean> {
  return new Promise((resolve) => {
    const options: ICallOption = {
      OnResult: (data: userPermission.IDbRole) => {
        if (!data.RID) {
          return
        }
        // MyRole api 可能已经查询过了，需要合并数据，否则本地添加的字段被重置
        const localData = Role.getData(data.RID)
        if (localData) {
          Object.assign(data, localData)
        }
        dataStore.Role.setData(data.RID, data)
      },
      OnStreamFinished: (rpcCmd: yrpcmsg.Ymsg) => {
        log.info(`Query [${DataName.Role}] finish`, rpcCmd)
        resolve(true)
      },
    }
    RoleDbQuery.Query(options)
  })
}

// 请求角色权限数据
export function queryRolePermissionData(): Promise<boolean> {
  return new Promise((resolve) => {
    const options: ICallOption = {
      OnResult: (data: userPermission.IDbRolePermission) => {
        if (!data.RID) {
          return
        }
        dataStore.RolePermission.setData(data.RID, data)
      },
      OnStreamFinished: (rpcCmd: yrpcmsg.Ymsg) => {
        log.info(`Query [${DataName.RolePermission}] finish`, rpcCmd)
        resolve(true)
      },
    }
    RolePermissionDbQuery.Query(options)
  })
}

// 请求单个角色的所有角色权限数据
export function queryOneRolePermissions(role: userPermission.IDbRole): Promise<rolePermissionList.IDbRolePermissionList> {
  return new Promise((resolve, reject) => {
    const options: ICallOption = {
      OnResult: (rolePermissionList: rolePermissionList.IDbRolePermissionList) => {
        log.info('[GetRolePermissions] OnResult:', rolePermissionList)
        rolePermissionList.Rows?.forEach(data => {
          dataStore.RolePermission.setData(data.RID + '', data)
        })
        resolve(rolePermissionList)
      },
      OnServerErr: (errRpc: yrpcmsg.Ymsg) => {
        log.error('[GetRolePermissions] OnServerErr:', role, errRpc)
        reject(errRpc.Optstr)
      },
      OnTimeout: (v: any) => {
        log.error('[GetRolePermissions] OnTimeout:', v)
        reject(v)
      },
    }
    RpcBysApi.GetRolePermissions(role, options)
  })
}

// 请求用户角色数据
export function queryUserRoleData(): Promise<boolean> {
  return new Promise((resolve) => {
    const options: ICallOption = {
      OnResult: (data: user.IDbUserRole) => {
        if (!data.RID) {
          return
        }
        dataStore.UserRole.setData(data.RID, data)
      },
      OnStreamFinished: (rpcCmd: yrpcmsg.Ymsg) => {
        log.info(`Query [${DataName.UserRole}] finish`, rpcCmd)
        resolve(true)
      },
    }
    UserRoleDbQuery.Query(options)
  })
}

export function queryControllerData(): Promise<boolean> {
  return new Promise((resolve) => {
    const options: ICallOption = {
      OnResult: (data: bysdb.IDbController) => {
        if (!data.RID) {
          return
        }
        setGCJ02LngLat(data)
        dataStore.Controller.setData(data.RID, data)
          .setDataIndex(data.ControllerHWID + '', data.RID)
      },
      OnStreamFinished: (rpcCmd: yrpcmsg.Ymsg) => {
        log.info(`Query [${DataName.Controller}] finish`, rpcCmd)
        resolve(true)
      },
    }
    ControllerDbQuery.Query(options)
  })
}

export function queryBysMarkerData(): Promise<boolean> {
  return new Promise((resolve) => {
    const options: ICallOption = {
      OnResult: (data: bysdbList.IDbBysMarkerList) => {
        for (let rowsData of data.Rows as Array<bysdbList.IDbBysMarker>) {
          if (!rowsData.RID) {
            continue
          }
          setGCJ02LngLat(rowsData)
          BysMarker.setData(rowsData.RID, rowsData)
            .setDataIndex(rowsData.MarkerHWID + '', rowsData.RID)
        }
      },
      OnStreamFinished: (rpcCmd: yrpcmsg.Ymsg) => {
        log.info(`Query [${DataName.BysMarker}] finish`, rpcCmd)
        resolve(true)
      },
    }
    BysMarkerDbQuery.QueryBatch(options)
  })

}

export function queryNFCPatrolLinesData(): Promise<boolean> {
  return new Promise((resolve) => {
    const options: ICallOption = {
      OnResult: (data: bysdbList.IDbNFCPatrolLineList) => {
        for (let rowsData of data.Rows as Array<bysdbList.IDbNFCPatrolLine>) {
          if (!rowsData.RID) {
            continue
          }
          NFCPatrolLine.setData(rowsData.RID, rowsData)
        }
      },
      OnStreamFinished: (rpcCmd: yrpcmsg.Ymsg) => {
        log.info(`Query [${DataName.NFCPatrolLine}] finish`, rpcCmd)
        resolve(true)
      },
    }
    NFCPatrolLineDbQuery.QueryBatch(options)
  })
}

export function queryNFCPatrolLineDetailData(): Promise<boolean> {
  return new Promise((resolve) => {
    const options: ICallOption = {
      OnResult: (data: bysdbList.IDbNFCPatrolLineDetailList) => {
        for (let rowsData of data.Rows as Array<bysdbList.IDbNFCPatrolLineDetail>) {
          if (!rowsData.RID) {
            continue
          }
          NFCPatrolLineDetail.setData(rowsData.RID, rowsData)
        }
      },
      OnStreamFinished: (rpcCmd: yrpcmsg.Ymsg) => {
        log.info(`Query [${DataName.NFCPatrolLineDetail}] finish`, rpcCmd)
        resolve(true)
      },
    }
    NFCPatrolLineDetailDbQuery.QueryBatch(options)
  })
}

export function queryNFCPatrolLineRulesData(): Promise<boolean> {
  return new Promise((resolve) => {
    const options: ICallOption = {
      OnResult: (data: bysdbList.IDbNFCPatrolLineRulesList) => {
        for (let rowsData of data.Rows as Array<bysdbList.IDbNFCPatrolLineRules>) {
          if (!rowsData.RID) {
            continue
          }
          NFCPatrolLineRules.setData(rowsData.RID, rowsData)
        }
      },
      OnStreamFinished: (rpcCmd: yrpcmsg.Ymsg) => {
        log.info(`Query [${DataName.NFCPatrolLineRules}] finish`, rpcCmd)
        resolve(true)
      },
    }
    NFCPatrolLineRulesDbQuery.QueryBatch(options)
  })
}

export function queryNFCPatrolLineAndRulesData(): Promise<boolean> {
  return new Promise((resolve) => {
    const options: ICallOption = {
      OnResult: (data: bysdbList.IDbNFCPatrolLineAndRulesList) => {
        for (let rowsData of data.Rows as Array<bysdbList.IDbNFCPatrolLineAndRules>) {
          if (!rowsData.RID) {
            continue
          }
          NFCPatrolLineAndRules.setData(rowsData.RID, rowsData)
        }
      },
      OnStreamFinished: (rpcCmd: yrpcmsg.Ymsg) => {
        log.info(`Query [${DataName.NFCPatrolLineDetail}] finish`, rpcCmd)
        resolve(true)
      },
    }
    NFCPatrolLineAndRulesDbQuery.QueryBatch(options)
  })
}

export function queryMediaInfoData(): Promise<boolean> {
  return new Promise((resolve) => {
    const options: ICallOption = {
      OnResult: (data: bysdb.IDbMediaInfo) => {
        if (!data.RID) {
          return
        }
        dataStore.MediaInfo.setData(data.RID, data)
      },
      OnStreamFinished: (rpcCmd: yrpcmsg.Ymsg) => {
        log.info(`Query [${DataName.MediaInfo}] finish`, rpcCmd)
        resolve(true)
      },
    }
    MediaInfoDbQuery.Query(options)
  })
}

//请求在线控制器数据
export function queryOnlineControllers(): Promise<boolean> {
  return new Promise((resolve, reject) => {
    const options: ICallOption = {
      OnResult: (data: doc.IOnlineControllerInfo) => {
        data = rawObjectProps(data)
        let controller: any
        // 区分4g界桩的处理
        if (data.DeviceType === ControllerDeviceType.Net4g) {
          const dbMarker = BysMarker.getDataByIndex(data.ControllerID + '')
          if (!dbMarker) return

          BysMarker.setPartData(dbMarker.RID + '', { onlineInfo: data })
          controller = dbMarker
        } else {
          // 原有的fsk控制器
          const dbController: ControllerAndUpdateCmd | undefined = Controller.getDataByIndex(data.ControllerID + '')
          if (!dbController) {
            return
          }

          const timeout = dbController.ControllerType === ControllerTypes.relayController ? bs_relay_timeout : bs_base_timeout
          if (getDiffMin(data.LastDataTime ?? DefaultDataStr) > timeout) {
            return
          }

          Controller.setPartData(dbController.RID as string, {
            controllerState: Object.assign({}, dbController.controllerState, data, { online: true }),
          })
          GotControllerData(dbController)
          controller = dbController
        }

        const loginReq: bysproto.IBloginReq = {
          LoginTimeStr: data.ConnectTime,
          NetworkType: data.NetworkType,
          Power: data.Power,
          TransferChannelNos: data.TransferChannelNos,
          DeviceType: data.DeviceType,
        }
        StrPubSub.publish(ControllerOnline, controller, loginReq)
      },
      OnStreamFinished: (rpcCmd: yrpcmsg.Ymsg) => {
        log.info('Query OnlineControllers finish', rpcCmd)
        resolve(true)
      },
      OnServerErr: (errRpc: yrpcmsg.Ymsg) => {
        log.error('Query OnlineControllers error', errRpc)
        // 处理服务器响应的错误
        let reason = i18n.global.t('message.queryFailed')
        reject(reason)
      },
      OnLocalErr: (err: any) => {
        log.error('Query OnlineControllers error', err)
        reject('local')
      },
      OnTimeout: (v: any) => {
        log.warn('Query OnlineControllers timeout', v)
        const options = {
          action: i18n.global.t('form.query'),
          name: i18n.global.t('message.onlineController'),
        }
        const reason = i18n.global.t('message.timeout', options)
        reject(reason)
      },
    }

    RpcBysApi.OnlineController({}, options)
  })
}

// 请求数据后，更新状态
function createQueryFinishStatus(): { [key: string]: boolean } {
  const Status: { [key: string]: boolean } = {}
  const AllDataName = Object.entries(DataName)
  for (let i = 0; i < AllDataName.length; i++) {
    // [key,value]
    const [, v] = AllDataName[i]
    Status[v] = false
  }

  return Status
}

export const QueryFinishStatus = createQueryFinishStatus()

// 重置QueryFinishStatus所有状态为false
export function resetQueryFinishStatus() {
  const AllDataName = Object.entries(DataName)
  for (let i = 0; i < AllDataName.length; i++) {
    const [, v] = AllDataName[i]
    QueryFinishStatus[v] = false
  }
}

export async function awaitDataQueryFinish(dataName: DataName, finishEvent: string): Promise<boolean> {
  if (QueryFinishStatus[dataName]) {
    return Promise.resolve(true)
  }

  return new Promise(resolve => {
    StrPubSub.subscribeOnce(finishEvent, () => {
      resolve(true)
    })
  })
}

export function queryBaseData() {
  // 请求dbConfig数据表
  queryDbConfig()
    .then(() => {
      QueryFinishStatus[DataName.Config] = true
      StrPubSub.publish(QueryConfigDataFinish)
    })

  // 请求登录用户数据
  UserDbQuery.SelectOne(Store.state.UserRID)
    .then((data: user.IDbUser) => {
      if (!data.RID) {
        return
      }

      dataStore.User.setData(data.RID as string, data)
        .setDataIndex(data.UserName as string, data.RID)
      // 更新系统顶部登录用户名称
      Store.commit('UserName', { value: data?.UserName })
      Store.commit('syncSettings', { value: JSON.parse(data?.Setting as string) })

      natsSubsribeUserSettings()
    })
    /*
        // 请求登录用户头像
        .then(data => {
          if (!data || !data.Image || data.Image === DefUuid) { return }

          // return ImageQuery.SelectOne(data.Image)
          //   .then((data: image.IDbImage) => {
          //     if (!data.RID) { return }
          //     dataStore.Image.setData(data.RID, data)
          //   })
        })
    */
    // 请求用户有权限的单位数据
    .then(async () => {
      // 请求用户有的权限数据
      await queryMyPermissionData()
      QueryFinishStatus[DataName.Permission] = true
      StrPubSub.publish(QueryRolePermissionFinish)

      if (checkOperationPermission(DbName.DbOrg, OperationType.Query)) {
        await queryOrgData()
        QueryFinishStatus[DataName.Unit] = true
        StrPubSub.publish(QueryUnitFinish)

        awaitDataQueryFinish(DataName.Config, QueryConfigDataFinish)
          // 查找权限内的顶级单位的公共父级单位
          .then(() => findTopLevelOrgWithinPermission())
          .then(topOrg => {
            Store.commit('updateTopParentOrg', { value: topOrg })

            // 设置系统logo和标题
            const appLogoKey = `appLogo.${topOrg.RID}`
            const dbAppLogo = dataStore.Config.getDataByIndex(appLogoKey)
            if (dbAppLogo) {
              Store.commit('updateAppLogo', { value: dbAppLogo })
            }
            const titleKey = `systemTitle.${topOrg.RID}`
            const dbSystemTitle = dataStore.Config.getDataByIndex(titleKey)
            if (dbSystemTitle) {
              Store.commit('updateSystemTitle', { value: dbSystemTitle })
            }
          })
      }

      if (checkOperationPermission(DbName.DbBysMarker, OperationType.Query)) {
        // 请求界桩数据
        await queryBysMarkerData()
        // 数据可能在地图加载后才完成请求，发出事件通知
        QueryFinishStatus[DataName.BysMarker] = true
        StrPubSub.publish(QueryBysMarkerFinish)

        // 查询当前界桩的状态数据
        const allBysMarker: BysMarkerAndUpdateInfo[] = BysMarker.getDataList().filter(item => item.HasInstallDevice)
        const options: ICallOption = {
          OnResult: (data: doc.IMarkerInfo) => {
            data = rawObjectProps(data)
            updateMarkerLastInfo(data)
          },
          OnStreamFinished: (rpcCmd: yrpcmsg.Ymsg) => {
            log.info('Query MarkerLatestInfo finish', rpcCmd)
            setBysMarkerAbnormalRidSet()
          },
          OnServerErr: (errRpc: yrpcmsg.Ymsg) => {
            log.error('Query MarkerLatestInfo error', errRpc)
          },
          OnLocalErr: (err: any) => {
            log.error('Query MarkerLatestInfo error', err)
          },
          OnTimeout: (v: any) => {
            log.warn('Query MarkerLatestInfo timeout', v)
          },
        }
        // 只查找3天内的最后状态
        const queryOptions: queryMarkerInfoOptions = {
          markerInfo: {
            MarkerCmdTime: getSubtractTime(utcTime(), 30, 'day')
          }
        }
        queryMarkerLastInfo(allBysMarker, options, queryOptions)
      }

      if (checkOperationPermission(DbName.DbController, OperationType.Query)) {
        // 控制器指令处理入口
        controllerCmdProcess()
        // 请求控制器数据
        await queryControllerData()
        // 数据可能在地图加载后才完成请求，发出事件通知
        QueryFinishStatus[DataName.Controller] = true
        StrPubSub.publish(QueryControllerFinish)
        //请求在线控制器数据
        queryOnlineControllers()
        // 处理中继与基站的关系索引
        updateControllerStationDeviceNoRelation()
      }

      // 请求完控制器、界桩数据，则完成数据加载，取消动画
      globalLoading.hide()
    })
    // 请求用户权限关联单位数据
    .then(() => {
      queryUserOrgPrivilegeData()
        .then(() => {
          QueryFinishStatus[DataName.UserOrgPrivilege] = true
          StrPubSub.publish(QueryUserOrgPrivilegeFinish)
        })
    })
    // 请求登录用户有权限的用户数据
    .then(() => {
      queryUserData()
        .then(() => {
          QueryFinishStatus[DataName.User] = true
          StrPubSub.publish(QueryUserFinish)
        })
    })
    // 请求角色相关数据（角色、角色权限、用户角色等）
    .then(() => {
      queryRoleRelateData()
    })
    // 请求媒体信息数据
    .then(() => {
      queryMediaInfoData()
        .then(() => {
          QueryFinishStatus[DataName.MediaInfo] = true
          StrPubSub.publish(QueryMediaInfoFinish)
        })
        .then(() => {
          setTimeout(() => {
            //请求图影像不存在的用户名
            const allMedia: bysdb.IDbMediaInfo[] = dataStore.MediaInfo.getDataList()
            const allUser: user.IDbUser[] = dataStore.User.getDataList()
            // 利用Set特性去重
            const ridSet: Set<string> = new Set()
            for (let media of allMedia) {
              // media.UploadUserRID可能为空字符串(对应的用户被删除)，需要过滤
              if (!allUser.find(item => item.RID === media.UploadUserRID) && media.UploadUserRID) {
                ridSet.add(media.UploadUserRID as string)
              }
            }
            QueryAllUnknownUser([...ridSet])
          }, 0)
        })
    })
    .then(() => {
      setTimeout(() => {
        //找到缺失的上级单位RID
        const allUnitData: Array<org.IDbOrg> = dataStore.Unit.getDataList().concat(unknownUnitData)
        const ridArray: Array<string> = []
        //缓存已经查询过的
        const findBuf: { [key: string]: boolean } = {}
        //单位上级单位
        for (let allUnitDatum of allUnitData) {
          if (findBuf[allUnitDatum.OrgRID + '']) {
            continue
          }
          if (!allUnitData.find(item => item.RID === allUnitDatum.OrgRID)) {
            findBuf[allUnitDatum.OrgRID as string] = true
            ridArray.push(allUnitDatum.OrgRID as string)
          }
        }
        if (ridArray.length) {
          QueryAllUnknownUnit(ridArray)
            .catch(err => {
              log.error('QueryAllUnknownUnit err:', err)
            })
        }
      }, 0)
    })
    // 请求巡查线路数据
    .then(async () => {
      const getNFCPatrol = [
        queryNFCPatrolLinesData(),
        queryNFCPatrolLineDetailData(),
        queryNFCPatrolLineAndRulesData(),
        queryNFCPatrolLineRulesData(),
      ]
      Promise.allSettled(getNFCPatrol)
        .then((results) => {

          const queryName = [
            DataName.NFCPatrolLine,
            DataName.NFCPatrolLineDetail,
            DataName.NFCPatrolLineAndRules,
            DataName.NFCPatrolLineRules
          ]
          results.forEach((res, i) => {
            QueryFinishStatus[queryName[i]] = res.status === 'fulfilled'
          })
        })
    })
}

export function queryMyRoles() {
  return new Promise((resolve) => {
    const data: rpc.IRpcCommon = {
      System: Store.state.System,
      SessionID: Store.state.SessionRID,
      Err: Store.state.UserRID,
    }
    const rpcData: rpc.RpcCommon = new rpc.RpcCommon(data)
    const options: ICallOption = {
      OnResult: (data: LocalRole) => {
        if (!data.RID) {
          return
        }
        data.isMyRole = true
        Role.setData(data.RID, data)
      },
      OnStreamFinished: (rpcCmd: yrpcmsg.Ymsg) => {
        log.info('Query [MyRole] finish', rpcCmd)
        resolve(true)
      },
    }
    RpcUser.MyRole(rpcData, options)
  })
}

export async function queryRoleRelateData() {
  // 请求用户角色关系数据
  await queryUserRoleData()
  QueryFinishStatus[DataName.UserRole] = true
  StrPubSub.publish(QueryUserRoleFinish)

  // 请求自己的角色
  queryMyRoles().catch(emptyFn)
  // 请求自己的角色权限数据
  await queryRolePermissionData()
  QueryFinishStatus[DataName.RolePermission] = true
  StrPubSub.publish(QueryRolePermissionFinish)

  // 请求有权限的单位单位下的所有角色
  await queryRoleData()
  QueryFinishStatus[DataName.Role] = true
  StrPubSub.publish(QueryRoleFinish)

  //请求不存在的单位数据
  const allUnit: org.IDbOrg[] = dataStore.Unit.getDataList().concat(unknownUnitData)
  const allRole: userPermission.IDbRole[] = dataStore.Role.getDataList()
  const ridArray: Array<string> = allRole.filter(item => !allUnit.some(v => v.RID === item.OrgRID))
    .map(item => (item.OrgRID + ''))
  const ridList: string[] = [...new Set(ridArray)]
  if (ridList.length) {
    await QueryAllUnknownUnit(ridList)
  }

  // 请求角色的创建者中没有用户权限的数据
  // 过滤掉内置角色，和role.Creator为null的数据
  const allRoleCreators: string[] = Role.getDataList()
    .filter(role => !(role.IsBuiltIn === 1 || !role.Creator || User.getData(role.Creator + '')))
    .map(role => role.Creator) as string[]
  const roleCreators = Array.from(new Set(allRoleCreators))
  if (roleCreators.length) QueryAllUnknownUser(roleCreators)
}

// 遍历所有的界桩，拿到已安装设备的异常界桩和未安装设备的iccid到期异常的界桩的rid集合
function setBysMarkerAbnormalRidSet() {
  BysMarker.getDataList().forEach((item: BysMarkerAndUpdateInfo) => {
    const RID = item.RID as string
    let isAbnormal = false
    if (item.HasInstallDevice) {
      isAbnormal = checkoutAbnormalBysMarker(item, utcTime())
    } else if (checkIs4GMarker(item.MarkerType as MarkerType) && !!item.ICCID) {
      isAbnormal = checkICCIDIsExpire(item)
    }

    if (isAbnormal) {
      BysMarkerAbnormalRidSet.add(RID)
      item.HasInstallDevice && updateMarkerStatusAfterAction(item)
    }
  })
}

function QueryAllUnknownUnit(ridList: Array<string>): Promise<void> {
  return new Promise((resolve, reject) => {
    const param = {
      KeyColumn: ['rid'],
      KeyValue: ridList.filter(r => r !== ''),
    }
    const callback: ICallOption = {
      OnResult: (data: org.IDbOrg) => {
        unknownUnitData.push(data)
      },
      OnStreamFinished: (rpcCmd: yrpcmsg.Ymsg) => {
        log.info('QueryAllUnknownUnit:', rpcCmd)
        StrPubSub.publish(QueryUnknownUnitDataOnceFinish)
        resolve()
      },
      OnServerErr: (errRpc: yrpcmsg.Ymsg) => {
        log.error('QueryAllUnknownUnit OnServerErr:', errRpc)
        reject()
      },
      OnTimeout: (v: any) => {
        log.warn('QueryAllUnknownUnit OnTimeout:', v)
        reject()
      },
    }
    UnitDbQuery.SelectMany(param, callback)
  })
}

function QueryAllUnknownUser(ridList: Array<string>) {
  const param = {
    KeyColumn: ['rid'],
    KeyValue: ridList,
  }
  const callback: ICallOption = {
    OnResult: (data: user.IDbUser) => {
      unknownUserData.push(data)
    },
  }
  UserDbQuery.SelectMany(param, callback)
}

export function QueryUnknownUnit(RID: string) {
  return UnitDbQuery.SelectOne(RID)
}

export interface queryMarkerInfoOptions {
  markerInfo?: doc.IMarkerInfo
  beforeSend?: (dbMarker: bysdb.IDbBysMarker) => void
}

// 查询指定界桩的MarkerInfo数据
export function queryMarkerLastInfo(dbMarkers: bysdb.IDbBysMarker[], callOption: ICallOption, queryOption?: queryMarkerInfoOptions) {
  BysMarkerAbnormalRidSet.clear()
  if (dbMarkers.length === 0) return

  const firstMarker = dbMarkers[0]
  queryOption?.beforeSend?.(firstMarker)
  const req: doc.IMarkerInfo = {
    MarkerCmdTime: getSubtractTime(utcTime(), 30),
    ...queryOption?.markerInfo,
    MarkerID: firstMarker.MarkerHWID as number,
    MarkerType: firstMarker.MarkerType,
  }
  const stream = RpcBysApi.MarkerLatestInfo(req, callOption)

  for (let i = 1; i < dbMarkers.length; i++) {
    const dbMarker = dbMarkers[i]
    const req: doc.IMarkerInfo = {
      MarkerID: dbMarker.MarkerHWID,
      MarkerType: dbMarker.MarkerType,
    }

    queryOption?.beforeSend?.(dbMarker)
    stream.sendNext(req)
  }
  stream.sendFinish()
}

function updateMarkerLastInfo(markerInfo: doc.IMarkerInfo) {
  const marker: BysMarkerAndUpdateInfo | undefined = BysMarker.getDataByIndex(markerInfo.MarkerID + '')
  if (!marker) {
    return
  }

  const RID = marker.RID as string
  const partData: Record<string, any> = { markerInfo }
  BysMarker.setPartData(RID, partData)

  // 判断是否为报警状态，发布报警事件
  if (checkIs4GMarker(markerInfo.MarkerType) && checkBysMarkerIsAlarm(marker)) {
    StrPubSub.publish(BysMarkerDumpingAlarm, marker, markerInfo.AlarmReporting)
    return
  }
  // 登录进入创建界桩图层时, 可能4g界桩markerInfo还没请求回来, 导致创建的为异常界桩涂层, 在请求到markerInfo的时候在同步一边图层
  syncDeviceLayerMarker(marker)
}

export function QueryAbnormalMarkers(onFinish?: () => void) {
  //根据界庄id请求界庄打卡记录，如果 当前日期 - 打卡日期>3,则判定该界庄异常
  // 过滤没有安装好的界桩
  const allBysMarker: BysMarkerAndUpdateInfo[] = BysMarker.getDataList()
    .filter(item => item.HasInstallDevice)
  const options: ICallOption = {
    OnResult: (data: doc.IMarkerInfo) => {
      data = rawObjectProps(data)
      updateMarkerLastInfo(data)
    },
    OnStreamFinished: (rpcCmd: yrpcmsg.Ymsg) => {
      log.info('Query MarkerLatestInfo finish', rpcCmd)
      onFinish?.()
      setBysMarkerAbnormalRidSet()
    },
    OnServerErr: (errRpc: yrpcmsg.Ymsg) => {
      log.error('Query MarkerLatestInfo error', errRpc)
      // 处理服务器响应的错误
      // let reason =i18n.global.t('message.queryFailed')
    },
    OnLocalErr: (err: any) => {
      log.error('Query MarkerLatestInfo error', err)
    },
    OnTimeout: (v: any) => {
      log.warn('Query MarkerLatestInfo timeout', v)
    },
  }
  const queryOptions: queryMarkerInfoOptions = {
    markerInfo: {
      MarkerCmdTime: getSubtractTime(utcTime(), 30, 'day')
    }
  }
  queryMarkerLastInfo(allBysMarker, options, queryOptions)
}

export function QueryAbnormalControllers() {
  checkAllControllerError()
}

//常用的查询成功回调
export function defaultQueryFinished() {
  Notify.create({
    message: i18n.global.t('message.querySuccess') as string,
    type: 'positive',
    position: 'top',
  })
}

/**
 * 查询历史记录
 * @param Where 复合查询条件
 * @param queryFunc  调用查询的函数
 * @param orderBy
 * @param TimeColumn 需要转换的时间列，以及时差
 * @param resultColumn
 * @param retSig 需要什么信号通知
 * @param finishCallBack  查询结束回调
 */
export function QueryHistoryData(Where: crud.IWhereItem[], queryFunc: Function,
  orderBy: string[] = [], TimeColumn: string[] = [], resultColumn: string[], retSig: string,
  finishCallBack: Function = defaultQueryFinished): { [key: string]: any } {
  const data: crud.IPrivilegeParam = {
    System: Store.state.System,
    SessionID: Store.state.SessionRID,
    QueryCondition: {
      Where,
      //查找上限
      Limit: 100000,
      OrderBy: orderBy,
      ResultColumn: resultColumn,
      TimeColumn,
    },
  }
  const req: crud.PrivilegeParam = new crud.PrivilegeParam(data)
  let temp: Array<any> = []
  const intervalHandle = setInterval(() => {
    StrPubSub.publish(retSig, temp)
    temp = []
  }, 500)
  const options: ICallOption = {
    OnResult: (data) => {
      // log.info('QueryHistoryData', data)
      temp.push(Object.freeze(data))
    },
    OnStreamFinished: (rpcCmd: yrpcmsg.Ymsg) => {
      log.info('QueryHistoryData finish', rpcCmd)
      //publish query finish
      // StrPubSub.publish(QueryOptionHistoryFinish)
      finishCallBack()
      StrPubSub.publish(retSig, temp)
      temp = []
      clearInterval(intervalHandle)
    },
    OnServerErr: (errRpc: yrpcmsg.Ymsg) => {
      log.error('QueryHistoryData error', errRpc)
    },
    OnLocalErr: (err: any) => {
      log.error('Query QueryOptionHistory error', err)
    },
    OnTimeout: (v: any) => {
      log.warn('Query QueryOptionHistory timeout', v)
    },
  }
  const prcStream: TRpcStream = queryFunc(req, options)
  return {
    prcStreamHandle: prcStream,
    intervalHandle,
  }
}

/**
 * 查询历史记录 增加批处理功能
 * @param Where 复合查询条件
 * @param queryFunc  调用查询的函数
 * @param orderBy
 * @param TimeColumn 需要转换的时间列，以及时差
 * @param resultColumn
 * @param retSig 需要什么信号通知
 * @param finishCallBack  查询结束回调
 */
export function QueryBatch(Where: crud.IWhereItem[], queryFunc: Function,
  orderBy: string[] = [], TimeColumn: string[] = [], resultColumn: string[], retSig: string,
  finishCallBack: Function = defaultQueryFinished): TRpcStream {
  const data: crud.IPrivilegeParam = {
    System: Store.state.System,
    SessionID: Store.state.SessionRID,
    QueryCondition: {
      Where,
      //查找上限
      Limit: 100000,
      OrderBy: orderBy,
      ResultColumn: resultColumn,
      TimeColumn,
      Batch: 1500,
    },
  }
  const req: crud.PrivilegeParam = new crud.PrivilegeParam(data)
  let first = true
  const options: ICallOption = {
    OnResult: (data) => {
      if (first) {
        first = false
        StrPubSub.publish(retSig, data.Rows)
      } else {
        setTimeout(() => {
          StrPubSub.publish(retSig, data.Rows)
        }, 60)
      }
    },
    OnStreamFinished: (rpcCmd: yrpcmsg.Ymsg) => {
      log.info('QueryHistoryData finish', rpcCmd)
      //publish query finish
      finishCallBack()
    },
    OnServerErr: (errRpc: yrpcmsg.Ymsg) => {
      log.error('QueryHistoryData error', errRpc)
    },
    OnLocalErr: (err: any) => {
      log.error('Query QueryOptionHistory error', err)
    },
    OnTimeout: (v: any) => {
      log.warn('Query QueryOptionHistory timeout', v)
    },
  }
  // const prcStream: TRpcStream = queryFunc(req, options)
  return queryFunc(req, options)
}

export function QueryBatchV2(
  execQuery: (req: crud.PrivilegeParam, callOpt?: ICallOption) => TRpcStream,
  queryCondition: crud.IQueryParam,
  promiseDeferred: Deferred<boolean>,
  execOptions?: ICallOption,
): () => void {
  const req: crud.PrivilegeParam = new crud.PrivilegeParam({
    System: Store.state.System,
    SessionID: Store.state.SessionRID,
    QueryCondition: {
      //查找上限
      Limit: 100000,
      Batch: 1500,
      ...queryCondition,
    },
  })
  const options: ICallOption = {
    OnResult: (data) => {
      log.info('QueryBatch2 OnResult', data)
    },
    OnStreamFinished: (rpcCmd: yrpcmsg.Ymsg) => {
      log.info('QueryBatch2 finish', rpcCmd)
      promiseDeferred.resolve(true)
    },
    OnServerErr: (errRpc: yrpcmsg.Ymsg) => {
      log.error('QueryBatch2 error', errRpc)
      promiseDeferred.reject(errRpc)
    },
    OnLocalErr: (err: any) => {
      log.error('QueryBatch2 error', err)
      promiseDeferred.reject(`OnLocalErr: ${err}`)
    },
    OnTimeout: (v: any) => {
      log.warn('QueryBatch2 timeout', v)
      promiseDeferred.reject(`OnTimeout: ${v}`)
    },

    ...execOptions,
  }
  const stream: TRpcStream = execQuery(req, options)

  return () => {
    stream.cancel()
    promiseDeferred.reject('promiseDeferred Cancel')
  }
}

function syncStoreData(nats_body, nats_subject, nats_reply, msg: yrpcmsg.IYmsg) {
  const myMsg = Ymsg.decode(msg.Body as Uint8Array)
  syncLocalStore(myMsg)
}

export function natsSubscribeCdcEvents() {
  const units: IDbOrg[] = Unit.getDataList()
  for (const unit of units) {
    rpcCon.NatsSubsribe(`cdc.${Store.state.System}.${unit.RID}`, syncStoreData)
  }
}

//当请求玩单位数据，就可以订阅数据修改事件
StrPubSub.subscribe(QueryUnitFinish, () => {
  natsSubscribeCdcEvents()
  SubScribeUpdateEvt()
})

function syncUserSettings(nats_body, nats_subject, nats_reply, msg: yrpcmsg.IYmsg) {
  const myMsg = Ymsg.decode(msg.Body as Uint8Array)
  if (UInt8Array2String(myMsg.Sid) === Store.state.SessionRID) {
    return
  }
  const msgBody = user.DbUser.decode(myMsg.Body)

  User.setData(msgBody.RID, msgBody)
    .setDataIndex(msgBody.UserName as string, msgBody.RID)

  const settings = JSON.parse(msgBody.Setting)
  const checkProps = ['controllerFirst', 'descending']
  const storeSettings = cloneDeep(Store.state.Settings)
  const needSortTree = checkProps.some(name => settings[name] !== storeSettings[name])

  // 更新数据
  Store.commit({
    type: 'syncSettings',
    value: settings,
  })
  setTimeout(() => {
    // 切换显示未安装设备的界桩
    if (settings.showNotInstalledMarker !== storeSettings.showNotInstalledMarker) {
      StrPubSub.publish(ShowNotInstalledMarker)
    }
    needSortTree && StrPubSub.publish(SortDeviceTree)
  })
  Notify.create({
    type: 'info',
    message: i18n.global.t('CmdTest.UserSetting') as string,
    position: 'top',
  })
}

export function natsSubsribeUserSettings() {
  rpcCon.NatsSubsribe('user.' + Store.state.UserRID, syncUserSettings)
}

export async function syncMarkerDataAndPartialUpdateSetting(rid: string, settingOption: Record<string, string>) {
  const data = await BysMarkerDbQuery.SelectOne(rid) as bysdb.IDbBysMarker
  const oldData = cloneDeep(BysMarker.getData(rid)) as BysMarkerAndUpdateInfo
  BysMarker.setData(rid, data)
  // 保留原有的附加属性
  const partdata = {
    isAlreadyRemoveAlarm: oldData?.isAlreadyRemoveAlarm,
    removeAlarm: oldData?.removeAlarm,
  }
  if (oldData?.updateInfo) {
    Object.assign(partdata, {
      updateInfo: rawObjectProps(oldData.updateInfo)
    })
  }
  // 只有4g界桩才需要拷贝旧的数据
  if (data.MarkerType === oldData?.MarkerType && checkIs4GMarker(oldData?.MarkerType)) {
    Object.assign(partdata, {
      onlineInfo: oldData?.onlineInfo,
      markerInfo: oldData?.markerInfo,
    })
  }
  BysMarker.setPartData(rid, partdata)
  const paramUpdate: crud.IDMLParam = { KeyColumn: ['Setting'] }
  partialUpdateMarkerSetting(data, paramUpdate, settingOption)
}

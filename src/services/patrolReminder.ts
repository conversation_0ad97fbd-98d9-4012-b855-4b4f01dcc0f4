import { BysMarker, Role, RolePermission, Permission, UserRole } from './dataStore'
import { StrPubSub } from 'ypubsub'
import { PlayAlarmAudio, PatrolReminderChanged } from '@utils/pubSubSubject'
import { scheduleLocalNotify } from '@utils/common'
import { point, distance } from 'turf'
import { isApp } from '@config/index'
import { i18n } from '@src/boot/i18n'
import { Store } from '@src/store'
import { ADMIN_ROLE_RID } from '@utils/constants'

// 巡查提醒监听器
let patrolReminderWatchId: number | null = null
let patrolReminderNotifiedSet: Set<string> | null = null

// 巡查提醒距离（米），后续可通过配置或参数自定义
let patrolReminderDistance = 15
// TODO: 可暴露 setPatrolReminderDistance 方法供外部设置

// 上一次定位点
let lastPosition: [number, number] | null = null
const minMoveDistance = 3 // 米

// 节流控制：两次检索最小间隔（毫秒）
const minCheckInterval = 3000
let lastCheckTime = 0

// 判断当前用户是否有巡查提醒权限（NFC/维护管理员或超级管理员）
function hasPatrolReminderPermission(): boolean {
  const userRID = Store.state.UserRID
  // 1. 获取当前用户所有角色ID
  const myRoleIds = UserRole.getDataList()
    .filter(ur => ur.UserRID === userRID)
    .map(ur => ur.RoleRID)

  // 2. 获取所有与这些角色相关的 RolePermission
  const myRolePermissions = RolePermission.getDataList().filter(rp => myRoleIds.includes(rp.RoleRID))

  // 3. 判断是否有目标权限（cmd类型，Sys.NFCPatrol 或 Sys.Maintain）
  const hasPatrolPerm = myRolePermissions.some(rp => {
    if (!rp.PermissionRID) return false
    const perm = Permission.getData(rp.PermissionRID)
    return perm && perm.PermissionType === 'cmd' &&
      (perm.PermissionValue === 'Sys.NFCPatrol' || perm.PermissionValue === 'Sys.Maintain')
  })
  if (hasPatrolPerm) return true

  // 4. 是否为超级管理员（角色名为admin或角色RID为全0）
  const isSuperAdmin = Role.getDataList().some(role =>
    role.isMyRole && (role.RoleName === 'admin' || role.RID === ADMIN_ROLE_RID)
  )
  return isSuperAdmin
}

function stopPatrolReminderMonitor() {
  if (patrolReminderWatchId !== null) {
    navigator.geolocation.clearWatch(patrolReminderWatchId)
    patrolReminderWatchId = null
  }
  patrolReminderNotifiedSet = null
}

function startPatrolReminderMonitor() {
  stopPatrolReminderMonitor()
  patrolReminderNotifiedSet = new Set<string>()
  const options: PositionOptions = { enableHighAccuracy: true, maximumAge: 10000, timeout: 10000 }
  const gotPosition = (pos: GeolocationPosition) => {
    console.log('geolocation.watchPosition 1:', JSON.stringify(pos))
    const now = Date.now()
    if (now - lastCheckTime < minCheckInterval) {
      return
    }

    lastCheckTime = now
    const { longitude, latitude } = pos.coords
    const userPoint = point([longitude, latitude])
    // 检查与上一次定位的距离
    if (lastPosition) {
      const lastPoint = point(lastPosition)
      const moveDist = distance(userPoint, lastPoint, 'meters')
      console.log('geolocation.watchPosition move dist:', moveDist, minMoveDistance)
      if (moveDist < minMoveDistance) {
        return
      }
    }
    lastPosition = [longitude, latitude]
    const markers = BysMarker.getDataList() || []
    // 收集所有在范围内且未提醒过的界桩
    const nearbyMarkers = markers.filter(marker => {
      if (marker.Lon && marker.Lat) {
        const markerPoint = point([marker.Lon, marker.Lat])
        const dist = distance(userPoint, markerPoint, 'meters')
        const rid = marker.RID ? String(marker.RID) : ''
        return dist <= patrolReminderDistance && rid && patrolReminderNotifiedSet && !patrolReminderNotifiedSet.has(rid)
      }
      return false
    })
    if (nearbyMarkers.length > 0) {
      // 记录已提醒
      nearbyMarkers.forEach(marker => {
        const rid = marker.RID ? String(marker.RID) : ''
        if (rid) patrolReminderNotifiedSet!.add(rid)
      })
      // 震动、铃声、合并通知
      if (navigator.vibrate) navigator.vibrate([500, 200, 500])
      StrPubSub.publish(PlayAlarmAudio)
      const markerNos = nearbyMarkers.map(m => m.MarkerNo || m.RID).join(', ')
      console.log('geolocation.watchPosition markerNos:', markerNos)
      scheduleLocalNotify({
        title: i18n.global.t('settingsPage.patrolReminder'),
        text: i18n.global.t('patrolReminder.nearbyMarker', { markerNo: markerNos }),
        vibrate: true
      })
    }
    // 离开所有界桩范围则清空已提醒集合
    const anyInRange = markers.some(marker => {
      if (marker.Lon && marker.Lat) {
        const markerPoint = point([marker.Lon, marker.Lat])
        const dist = distance(userPoint, markerPoint, 'meters')
        return dist <= patrolReminderDistance
      }
      return false
    })
    if (!anyInRange && patrolReminderNotifiedSet) patrolReminderNotifiedSet.clear()
  }
  const locationError = (err: GeolocationPositionError) => {
    console.warn('watchPosition error: code=', err.code, 'message=', err.message)
  }

  patrolReminderWatchId = navigator.geolocation.watchPosition(gotPosition, locationError, options)
}

export function patrolReminderMonitor(enable: boolean) {
  if (enable) {
    startPatrolReminderMonitor()
  } else {
    stopPatrolReminderMonitor()
  }
}

export function initPatrolReminderMonitor(enable: boolean) {
  const per = hasPatrolReminderPermission()
  console.log(`initPatrolReminderMonitor: enable=${enable}, permission=${per}`)
  // 权限和环境检查，作为总入口控制
  if (!isApp || !per) return

  // 订阅设置变更事件
  StrPubSub.subscribe(PatrolReminderChanged, (val: boolean) => {
    patrolReminderMonitor(val)
  })
  // 首次初始化
  patrolReminderMonitor(enable)
}

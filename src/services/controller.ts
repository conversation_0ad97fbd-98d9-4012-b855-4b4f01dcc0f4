import { StrPubSub } from 'ypubsub'
import { By<PERSON><PERSON><PERSON><PERSON>, Controller, Unit } from './dataStore'
import { bysproto } from '@ygen/controller'
import { bysdb } from '@ygen/bysdb'
import { Dialog, Notify, QDialogOptions } from 'quasar'
import { i18n } from '@boot/i18n'
import {
  checkIs4GMarker,
  checkOneControllerError,
  CmdCode,
  ControllerDeviceType,
  controllerNoMap,
  ControllerTypes,
  createKeyVal,
  decodeDeviceStatus,
  LatestUploadTime,
  NetworkTypeEnum,
  objUnderline2SmallHump,
  partialUpdateMarker,
  rawObjectProps,
  updateMarkerStatusAfterAction,
} from '@utils/common'
import log from '@utils/log'
import { ICallOption, rpcCon } from 'jsyrpc'
import { yrpcmsg } from 'yrpcmsg'
import {
  BysMarkerDumpingAlarm,
  ControllerAlarm,
  ControllerHeartbeat,
  ControllerOffline,
  ControllerOfflinePanic,
  ControllerOnline,
  DeviceAlarmTest,
  DeviceReportInfo,
  MarkerPhotographUpload,
  NotifyICCIDAlreadyExist,
  NotifyICCIDAndIMEIConflict,
  PlayAlarmAudio,
  RemoveBysMarkerAlarm,
  removeBysMarkerAlarmCmd,
  removeBysMarkerAlarmCmdAuto,
  StopAlarmAudio,
  UpdateDbBysMarker,
} from '@utils/pubSubSubject'
import { doc } from '@ygen/bys.api'
import { RpcBysApi, RpcBysApiUpdateControllerServerAddr } from '@ygen/bys.api.yrpc'
import { sysUtcTime, utcTime } from '@utils/dayjs'
import { setGCJ02LngLat } from '@utils/gcoord'
import { DataName, Store } from '@src/store'
import dayjs from 'dayjs'
import { crud } from '@ygen/crud'
import { PRpcDbQuery } from '@services/queryData'
import {
  BysMarkerAndUpdateInfo,
  ControllerAbnormalStatus,
  ControllerAndUpdateCmd,
  ControllerStatus,
  DeviceUpdateInfo,
  ICustData,
} from '@utils/bysdb.type'
import { cloneDeep } from 'lodash'
import { computed, shallowRef } from 'vue'
import UpdataMarkerParam = doc.UpdataMarkerParam;
import IDbController = bysdb.IDbController;
import IGrpcMeta = yrpcmsg.IGrpcMeta;

function decodePower(val: number): number {
  return parseFloat((val / 10).toFixed(1))
}

// 上线 bmsg.cmd=1, bmsg.body=BloginReq
// BloginReq.Name为DbController.ControllerNo
function online(msg: bysproto.Bmsg): void {
  // 查找对应的的控制器数据
  const loginReq: bysproto.IBloginReq = rawObjectProps(bysproto.BloginReq.decode(msg.Body))
  let controller: any
  let name = ''
  let showNotify = true
  // 判断4g界桩或fsk控制器
  if (loginReq.DeviceType === ControllerDeviceType.Net4g) {
    const dbMarker = BysMarker.getDataByIndex(msg.Res + '')
    if (!dbMarker) return

    const onlineInfo = {
      ConnectTime: loginReq.LoginTimeStr,
      ControllerID: msg.Res,
      Power: loginReq.Power,
      DeviceType: loginReq.DeviceType,
      LastDataTime: loginReq.LoginTimeStr,
    }
    BysMarker.setPartData(dbMarker.RID + '', { onlineInfo })
    controller = dbMarker
    name = dbMarker.MarkerNo as string
  } else {
    const dbController: ControllerAndUpdateCmd | undefined = Controller.getDataByIndex(msg.Res + '')
    if (!dbController) {
      return
    }

    const controllerState: ControllerStatus = {
      ...loginReq,
      online: true,
      LastDataTime: loginReq.LoginTimeStr,
      Power: decodePower(loginReq.Power ?? 0),
    }
    Controller.setPartData(dbController.RID as string, { controllerState })
    GotControllerData(dbController)
    controller = dbController
    name = dbController.ControllerNo as string

    // default network is fsk but current login network type is not fsk,do not notify
    if (dbController.DefaultNetworkType == NetworkTypeEnum.Fsk && loginReq.NetworkType != dbController.DefaultNetworkType) {
      showNotify = false
    }
  }

  log.info('controller online', controller, loginReq, msg)

  StrPubSub.publish(ControllerOnline, controller, loginReq)

  // 显示通知
  if (showNotify) {
    Notify.create({
      message: i18n.global.t('message.online', { name }) as string,
      position: 'top',
      type: 'positive',
    })
  }
}

function _offline(data: ControllerAndUpdateCmd, loginReq?: bysproto.IBloginReq) {
  const controllerState: ControllerStatus = Object.assign({}, data.controllerState, {
    online: false,
  })
  Controller.setPartData(data.RID as string, { controllerState })
  loginReq = loginReq ?? new bysproto.BloginReq({
    DeviceType: 0,
  })
  StrPubSub.publish(ControllerOffline, data, loginReq)

  ClearControllerTimeoutTimer(data)
}

function markerPhotographUpload(msg: bysproto.Bmsg): void {
  const camImageData = objUnderline2SmallHump(JSON.parse(msg.Optstr), bysdb.CamImageData)
  log.info('markerPhotographUpload msg', msg)
  log.info('markerPhotographUpload msg.Optstr', camImageData)
  let custData: ICustData = {
    hwid: 0,
    type: 0,
  }
  try {
    custData = JSON.parse(camImageData.custData ?? '{}')
  } catch (error) {
    log.info('parse markerPhotographUpload custData error', error)
  }
  const allMarker = BysMarker.getDataList()
  const dbMarker = allMarker.find(item => item.MarkerHWID === custData.hwid)
  if (!dbMarker) {
    return
  }

  StrPubSub.publish(MarkerPhotographUpload, dbMarker, msg.Body, camImageData, custData)
}

// 下线 bmsg.cmd=1000 bmsg.Res=controllerHWID bmsg.Optstr=controllerno
function offline(msg: bysproto.Bmsg): void {
  // 区分控制器类型
  const loginReq: bysproto.IBloginReq = rawObjectProps(bysproto.BloginReq.decode(msg.Body))
  let controller: any
  let showNotify = true
  let name = ''
  // 4g界桩
  if (loginReq.DeviceType === ControllerDeviceType.Net4g) {
    const dbMarker = BysMarker.getDataByIndex(msg.Res + '')
    if (!dbMarker) return

    if (!loginReq.Name) {
      loginReq.Name = dbMarker.ICCID
    }

    controller = dbMarker
    name = dbMarker.MarkerNo as string
    // todo 处理4g界桩下线逻辑
    BysMarker.setPartData(dbMarker.RID + '', { onlineInfo: undefined })
    StrPubSub.publish(ControllerOffline, dbMarker, loginReq)
  } else {
    // fsk控制器数据
    const data: ControllerAndUpdateCmd | undefined = Controller.getDataByIndex(msg.Res + '')
    if (!data) return

    controller = data
    name = data.ControllerNo as string
    _offline(data, loginReq)
    //default network is fsk but current login network type is not fsk,do not notify
    if (data.DefaultNetworkType == NetworkTypeEnum.Fsk && data?.controllerState?.NetworkType !=
      data.DefaultNetworkType) {
      showNotify = false
    }
  }
  log.info('controller offline', msg, loginReq, controller)

  // 显示通知
  if (showNotify) {
    Notify.create({
      message: i18n.global.t('message.offline', { name }) as string,
      position: 'top',
      type: 'warning',
    })
  }
}

const heartBeatTimerMap: Map<string, number> = new Map<string, number>()
const lastHeartBeatTimeMap: Map<string, number> = new Map<string, number>()
//基站 10 min超时
export const bs_base_timeout = 10 * 60 * 1000
//中继 70 min超时
export const bs_relay_timeout = 70 * 60 * 1000

function ClearControllerTimeoutTimer(data: ControllerAndUpdateCmd) {
  const controllerRid = data.RID as string

  const timer = heartBeatTimerMap.get(controllerRid)
  if (timer !== undefined) {
    //清除上一次的定时器
    clearInterval(timer)
  }
}

export function GotControllerData(data: ControllerAndUpdateCmd) {
  const controllerRid = data.RID as string
  lastHeartBeatTimeMap.set(controllerRid, Date.now())

  ClearControllerTimeoutTimer(data)

  // 如果**分钟内都没有收到心跳，则视该控制器异常下线
  const timeout = data.ControllerType === ControllerTypes.relayController ? bs_relay_timeout : bs_base_timeout
  const tm = window.setTimeout(() => {
    const lastDataTime = lastHeartBeatTimeMap.get(controllerRid) ?? 0

    if (Date.now() - lastDataTime >= timeout) {
      const controllerState: ControllerStatus = Object.assign({}, data.controllerState, {
        online: false,
      })
      Controller.setPartData(data.RID as string, { controllerState })

      log.info('controller time out:', timeout, data)
      StrPubSub.publish(ControllerOfflinePanic, data)
      lastHeartBeatTimeMap.delete(controllerRid)
    }
  }, timeout)

  heartBeatTimerMap.set(controllerRid, tm)
}

// bit0:时间异常 0:无异常 1:无法获取时间（可能gps异常)
export function decodeControllerStatus(status: number): ControllerAbnormalStatus {
  const result: ControllerAbnormalStatus = {
    timeError: false,
  }

  // 解析时间是否异常
  const bit0 = status & 0x01
  result.timeError = bit0 === 1

  return result
}

function commonControllerDecode(msg: bysproto.Bmsg): {
  controller?: ControllerAndUpdateCmd,
  statusValue?: ControllerStatus
} {
  const result: { controller?: ControllerAndUpdateCmd, statusValue?: ControllerStatus } = {
    controller: undefined,
    statusValue: undefined,
  }
  const controllerStatus: ControllerStatus = result.statusValue = bysproto.ControllerStatus.decode(msg.Body)
  controllerStatus.status = decodeControllerStatus(controllerStatus.Status ?? 0)

  let data: ControllerAndUpdateCmd | undefined
  // StationDeviceNo为0时，控制器有登录服务器(包括中继4G登录)
  const isDirectConnect = controllerStatus.StationDeviceNo === 0
  if (isDirectConnect) {
    // 查在对应的的控制器数据
    data = result.controller = Controller.getDataByIndex(controllerStatus.StationID + '')
  } else {
    // 中继从基站上传指令
    const key = createKeyVal(-1, controllerStatus.StationDeviceNo, controllerStatus.StationID as number)
    const rid = controllerNoMap[key]
    if (!rid) {
      return result
    }
    data = result.controller = Controller.getData(rid)
  }
  if (!data) {
    return result
  }

  const status: ControllerStatus = rawObjectProps(controllerStatus)
  Object.assign(status, data.controllerState, controllerStatus)

  // 3种网络类型，如果没有登录，则为0或undefined，默认视为fsk
  if (!isDirectConnect || !status.NetworkType) {
    status.NetworkType = NetworkTypeEnum.Fsk
  }

  status.online = true
  status.LastDataTime = sysUtcTime()
  status.Power = decodePower(status.Power ?? 0)
  Controller.setPartData(data.RID as string, {
    controllerState: status,
  })

  return result
}

// 心跳 bmsg.cmd=11 bmsg.Res=controllerHWID bmsg.Optstr=controllerno bmsg.body = ControllerStatus
function heartbeat(msg: bysproto.Bmsg): void {
  const { controller, statusValue } = commonControllerDecode(msg)
  if (!controller || !statusValue) {
    return
  }

  log.info('heartbeat', controller, statusValue)
  StrPubSub.publish(ControllerHeartbeat, controller)

  GotControllerData(controller)
}

// cmd=16 控制器报警
function controllerAlarm(msg: bysproto.Bmsg): void {
  const { controller } = commonControllerDecode(msg)
  if (!controller) {
    return
  }

  log.warn('controllerAlarm', controller)
  checkOneControllerError(controller)
  StrPubSub.publish(ControllerAlarm, controller)
}

function controllerOnlineByData(controller: ControllerAndUpdateCmd, state: ControllerStatus) {
  const nowTime = sysUtcTime()
  const defaultState: ControllerStatus = {
    TransferChannelNos: [],
    NetworkType: controller.DefaultNetworkType ?? NetworkTypeEnum.Fsk,
    LoginTimeStr: nowTime,
    Power: 7.2,
    StationDeviceNo: controller.ParentChannelNo,
    LastDataTime: nowTime,
  }
  const oldState: ControllerStatus = cloneDeep(controller.controllerState ?? {})
  const newState: ControllerStatus = { ...defaultState, ...oldState, ...state, DeviceType: 0 }
  Controller.setPartData(controller.RID + '', {
    controllerState: newState,
  })

  // 之前是下线状态，则重新发布上线
  if (!oldState?.online) {
    StrPubSub.publish(ControllerOnline, controller, newState)
  }
}

// 0xd1: 常规上传
function normalUpdate(data: BysMarkerAndUpdateInfo, updateInfo: DeviceUpdateInfo): void {
  log.info('normalUpdate:', data, updateInfo)
  StrPubSub.publish(DeviceReportInfo, data, updateInfo)

  // 解析指令状态位，可能没有解除报警的，主要体现在报警时更新参数，上传的指令中还是属于报警
  if (data.isAlreadyRemoveAlarm === false && !updateInfo.StatusInfo?.isAlarm) {
    Notify.create({
      message: i18n.global.t('message.removeAlarmSuccess') as string,
      type: 'positive',
      html: true,
      timeout: 7000,
    })
    BysMarker.setPartData(data.RID as string, { removeAlarm: false, isAlreadyRemoveAlarm: true })
    StrPubSub.publish(RemoveBysMarkerAlarm, data, Controller.getDataByIndex(updateInfo.StationID + ''))
  }
}

/***
 * 缓存当前报警的界庄:定时器句柄
 * markerRID:timerHandle
 */

// let AlarmMarkerCache = {}

/***
 * 超时，解除报警
 */
/*function createTimeoutEvt(data: bysdb.IDbBysMarker): NodeJS.Timeout {
  return setTimeout(() => {
    //30min 未再次上报警，则清楚之前的报警位
    StrPubSub.publish(RemoveBysMarkerAlarm, data)
  }, 30 * 60 * 1000)
}*/

// 0xd2: 报警上传
function alarmUpdate(data: BysMarkerAndUpdateInfo, updateInfo: DeviceUpdateInfo): void {
  log.info('alarmUpdate:', data, updateInfo)
  if (data.removeAlarm) {
    // 主动发布解除界桩报警命令
    StrPubSub.publish(removeBysMarkerAlarmCmdAuto, data, Controller.getData(data.ControllerRID as string) as bysdb.IDbController | undefined)
  }
  // 只将倾倒报警显示到图层及tree(非低电报警)
  // if (data.updateInfo.StatusInfo.isDumping || data.updateInfo.batteryPower !== 0) {
  //发送的是新的经纬度的marker
  StrPubSub.publish(BysMarkerDumpingAlarm, data, updateInfo)
  if (Store.state.Settings.closeAlarmAudio) {
    StrPubSub.publish(PlayAlarmAudio)
  }
  // }
  // 发布界桩报警,用于统计面板
  // StrPubSub.publish(BysMarkerAlarm, data, updateInfo)
  /*if (AlarmMarkerCache[data.RID as string]) {
    //已经存在该计时器,则重设定时器
    clearTimeout(AlarmMarkerCache[data.RID as string])
    AlarmMarkerCache[data.RID as string] = createTimeoutEvt(data)
  } else {
    AlarmMarkerCache[data.RID as string] = createTimeoutEvt(data)
  }*/

  // 本地数据，标记是否已经解除报警的状态
  BysMarker.setPartData(data.RID + '', {
    isAlreadyRemoveAlarm: false,
  })
}

/***
 * 判断界桩校准是否参数,界桩参数时间>=数据库时间  即为校准完毕
 */
function judgeMarkerParamIsUpdate(data: bysdb.IDbBysMarker, updateInfo: bysproto.IBdeviceUpdate | bysproto.IBInfoReporting) {
  // 4g界桩，判断的参数不一样
  const is4gMarker = checkIs4GMarker(data.MarkerType)
  let msg = `${data.MarkerNo}(${data.MarkerHWID})  `, myType = 'info'

  if (is4gMarker) {
    let markerDataVersion = ''
    try {
      const MarkerSettings = JSON.parse(data.MarkerSettings ?? '{}') as bysproto.IBDataUpdate
      markerDataVersion = MarkerSettings.dataVersion ?? markerDataVersion
      // eslint-disable-next-line no-empty
    } catch (error) {
    }
    updateInfo = updateInfo as bysproto.IBInfoReporting
    if (!markerDataVersion || dayjs(updateInfo.dataVersion as string).isBefore(markerDataVersion as string)) {
      msg += `${i18n.global.t('CmdTest.markerParamUnSync')}`
      myType = 'warning'
    } else {
      msg += `${i18n.global.t('CmdTest.markerParamIsSync')}`
    }
  } else {
    updateInfo = updateInfo as bysproto.IBdeviceUpdate
    if (dayjs(updateInfo.ParamTime as string).isBefore(data.MarkerParamTime as string)) {
      msg += `${i18n.global.t('CmdTest.markerParamUnSync')}`
      myType = 'warning'
    } else {
      msg += `${i18n.global.t('CmdTest.markerParamIsSync')}`
    }
    if (checkUpdateCmdTimeIsSync(updateInfo.CmdTime as string)) {
      msg += `<br/>${i18n.global.t('CmdTest.markerTimeIsSync')}`
    } else {
      msg += `<br/>${i18n.global.t('CmdTest.markerTimeUnSync')}`
      myType = 'warning'
    }
  }

  Notify.create({
    message: msg,
    type: myType,
    html: true,
    timeout: 7000,
  })
}

// 界桩开栈
function testUpdate(data: bysdb.IDbBysMarker, updateInfo: bysproto.IBdeviceUpdate): void {
  log.info('testUpdate:', data, updateInfo)
  // 件添加日志信息
  StrPubSub.publish(DeviceAlarmTest, data, updateInfo)
  BysMarker.setPartData(data.RID as string, { removeAlarm: false })
  const controller = Controller.getDataByIndex(updateInfo.StationID + '')
  StrPubSub.publish(RemoveBysMarkerAlarm, data, controller)
  judgeMarkerParamIsUpdate(data, updateInfo)
}

// 0xd1: 常规上传 0xd2: 报警上传  0xd3: 测试
const DeviceCmdMethods = {
  [CmdCode.D1]: normalUpdate,
  [CmdCode.D2]: alarmUpdate,
  [CmdCode.D3]: testUpdate,
}

function decodeStrength(val: number): number {
  return Array.from(new Int16Array([val])).shift() || 0
}

function setDeviceUpdateCommonInfo(data: BysMarkerAndUpdateInfo,
                                   updateInfo: DeviceUpdateInfo) {
  updateInfo.StatusInfo = decodeDeviceStatus(updateInfo.Status as number)
  if (updateInfo.StatusInfo.isValidPosition && updateInfo.GPS) {
    setGCJ02LngLat(updateInfo.GPS)
  }
  // 解析场强，转成int8类型 0xFF92 -> -110
  if (updateInfo.DeviceFieldStrength) {
    updateInfo.DeviceFieldStrength = decodeStrength(updateInfo.DeviceFieldStrength)
  }
  BysMarker.setPartData(data.RID as string, { updateInfo })
}

// 界桩上报指令 bmsg.cmd=2, bmsg.body=BdeviceUpdate
function deviceUpdate(msg: bysproto.Bmsg): void {
  // 查找上报的界桩数据
  const deviceUpdate: bysproto.IBdeviceUpdate = bysproto.BdeviceUpdate.decode(msg.Body)

  const data: bysdb.IDbBysMarker | undefined = BysMarker.getDataByIndex(
    deviceUpdate.DeviceID + '')
  if (!data) {
    return
  }
  log.info('deviceUpdate', deviceUpdate, data)

  // 处理公共逻辑，如更新状态码、指令上传时间等
  setDeviceUpdateCommonInfo(data, deviceUpdate)

  // 有数据，则说明基站或中继在线，需要更新 LastDataTime
  const controllerState: ControllerStatus = {
    // updateInfo.CmdTime 可能不准确，直接使用本机utc时间
    LastDataTime: sysUtcTime(),
    online: true,
  }
  const baseController: ControllerAndUpdateCmd | undefined = Controller.getDataByIndex(deviceUpdate.StationID + '')
  log.info('deviceUpdate baseController:', baseController)
  if (baseController) {
    controllerOnlineByData(baseController, controllerState)
    GotControllerData(baseController)
  }
  const relayController = getControllerByChannelNo(deviceUpdate.StationID ?? 0, deviceUpdate.StationDeviceNo ?? 0)
  log.info('deviceUpdate relayController:', relayController)
  if (relayController) {
    controllerOnlineByData(relayController, controllerState)
    GotControllerData(relayController)
  }

  // 根据上报的指令类型，执行相关方法
  DeviceCmdMethods[deviceUpdate.Cmd as number]?.(data, deviceUpdate)

  // 暂时不显示
  // 根据指令的状态，更新界桩列表树电量值
  // if (isHaveMaintainPerm()) {
  //   StrPubSub.publish(UpdateBysMarkerTreeNodeBattery, data)
  // }
}

// 修改控制器服务器地址应答
export function gotModifyControllerServerAddressRes(msg: yrpcmsg.Ymsg, StationID: number): void {
  const bMsg: bysproto.Bmsg = bysproto.Bmsg.decode(msg.Body)
  log.info('gotModifyControllerServerAddressRes', StationID, msg, bMsg)
}

export function deviceInfoReporting(msg: bysproto.Bmsg) {
  const infoReporting = rawObjectProps(bysproto.BInfoReporting.decode(msg.Body)) as bysproto.IBInfoReporting
  const dbMarker = BysMarker.getDataByIndex((infoReporting.deviceID ?? -1) + '') as BysMarkerAndUpdateInfo | undefined
  log.info('deviceInfoReporting', msg, infoReporting, dbMarker)
  if (!dbMarker) return

  // 需要更新的参数
  const markerInfo = Object.assign({}, dbMarker.markerInfo ?? {}, {
    MarkerCmdTime: utcTime(),
    MarkerUploadCmd: 21,
    InfoReporting: infoReporting,
    AlarmStatusWith4G: 0,
  })
  const onlineInfo = Object.assign({}, dbMarker.onlineInfo ?? {}, {
    LastDataTime: markerInfo.MarkerCmdTime,
  })

  const partData: Record<string, any> = { onlineInfo, markerInfo }

  // 上一次命令是否为报警命令
  const lastReportCmdIsAlarmCmd = dbMarker.markerInfo?.MarkerUploadCmd === CmdCode.AlarmReport
  const isReportingAfterRemoveAlarm = dbMarker.isAlreadyRemoveAlarm === false && lastReportCmdIsAlarmCmd
  // 按上报的类型来处理，兼容原界桩UI显示
  // 1：开机上报 2：调试上报 3：定时上报
  switch (infoReporting.type) {
    case 3:
      // 解析指令状态位，可能没有解除报警的，主要体现在报警时更新参数，上传的指令中还是属于报警
      BysMarker.setPartData(dbMarker.RID as string, partData)
      if (isReportingAfterRemoveAlarm) {
        Notify.create({
          message: i18n.global.t('message.removeAlarmSuccess') as string,
          type: 'positive',
          html: true,
          timeout: 7000,
        })
        BysMarker.setPartData(dbMarker.RID as string, { removeAlarm: false, isAlreadyRemoveAlarm: true })
        StrPubSub.publish(RemoveBysMarkerAlarm, dbMarker)
      }
      break
    default:
      partData.removeAlarm = false
      BysMarker.setPartData(dbMarker.RID as string, partData)
      if (lastReportCmdIsAlarmCmd) {
        StrPubSub.publish(RemoveBysMarkerAlarm, dbMarker)
      }
      judgeMarkerParamIsUpdate(dbMarker, infoReporting)
  }


  StrPubSub.publish(DeviceReportInfo, dbMarker, infoReporting)
  updateMarkerStatusAfterAction(dbMarker)
}

export function deviceAlarmReporting(msg: bysproto.Bmsg) {
  const alarmReporting = rawObjectProps(bysproto.BAlarmReporting.decode(msg.Body)) as bysproto.IBAlarmReporting
  const dbMarker = BysMarker.getDataByIndex((alarmReporting.deviceID ?? -1) + '') as BysMarkerAndUpdateInfo | undefined
  log.info('deviceAlarmReporting', msg, alarmReporting, dbMarker)
  if (!dbMarker) return

  if (dbMarker.removeAlarm) {
    // 主动发布解除界桩报警命令
    StrPubSub.publish(removeBysMarkerAlarmCmdAuto, dbMarker)
  }
  // 开机上报不播放语音
  if (Store.state.Settings.closeAlarmAudio && alarmReporting.type !== 1) {
    StrPubSub.publish(PlayAlarmAudio)
  }
  // 需要更新的参数
  const markerInfo = Object.assign({}, dbMarker.markerInfo ?? {}, {
    MarkerCmdTime: utcTime(),
    MarkerUploadCmd: 22,
    AlarmReporting: alarmReporting,
    AlarmStatusWith4G: 1,
  })
  const onlineInfo = Object.assign({}, dbMarker.onlineInfo ?? {}, {
    LastDataTime: markerInfo.MarkerCmdTime
  })
  const partData: Record<string, any> = {
    onlineInfo, markerInfo,
    isAlreadyRemoveAlarm: false, // 本地数据，标记是否已经解除报警的状态
  }
  // if (alarmReporting.locate) {
  //   const gcj02LngLat = toGCJ02([alarmReporting.locate.Lon ?? 0, alarmReporting.locate.Lat ?? 0])
  //   partData.GCJ02LngLat = gcj02LngLat
  // }
  BysMarker.setPartData(dbMarker.RID as string, partData)

  //发送的是新的经纬度的marker
  StrPubSub.publish(BysMarkerDumpingAlarm, dbMarker, alarmReporting)
  // 界桩报警时，图标会闪烁，不做异常标记处理
  // updateMarkerStatusAfterAction(dbMarker)
}

export function deviceRemoteKillReply(msg: bysproto.Bmsg) {
  log.info('deviceRemoteKillReply', msg)
}

export function answerRemoveAlarm(msg: bysproto.Bmsg) {
  const markerHWID = +msg.Optstr
  log.info('answerRemoveAlarm', msg, markerHWID)
  const marker = BysMarker.getDataByIndex(markerHWID + '') as BysMarkerAndUpdateInfo
  BysMarker.setPartData(marker?.RID as string, { markerInfo: { ...marker.markerInfo, AlarmStatusWith4G: 0 } })
  Notify.create({
    message: i18n.global.t('message.removeAlarm', { name: marker.MarkerNo }) as string,
    position: 'top',
    type: 'positive',
  })
  StrPubSub.publish(RemoveBysMarkerAlarm, marker)
}

// 控制器NATS命令类型对应的方法
const BmsgCmdMethods = {
  // 控制器指令
  1: online,
  1000: offline,
  1003: markerPhotographUpload,
  11: heartbeat,
  16: controllerAlarm,

  // 界桩指令
  2: deviceUpdate,

  // 以下为4g界桩新增命令
  // 21：信息上报
  21: deviceInfoReporting,
  // 22：报警
  22: deviceAlarmReporting,
  // 37: 遥毙应答
  37: deviceRemoteKillReply,
  // 28: 界桩应答解除报警
  28: answerRemoveAlarm,
}

function subscribeControllerCommand(nats_body, nats_subject, nats_reply, msg: yrpcmsg.Ymsg) {
  const data: bysproto.IBmsg = bysproto.Bmsg.decode(msg.Body)
  //  const controllerStatus: bysproto.IControllerStatus = bysproto.ControllerStatus.decode(msg.Body)
  //  console.log('subscribeControllerCommand', nats_body, nats_subject, nats_reply, msg)
  BmsgCmdMethods[data.Cmd as number]?.(data)
}

interface ICCIDAndIMEI {
  ICCID: string
  IMEI: string
  LoginTimeStr: string
}

const ICCIDAndIMEI = shallowRef<Record<string, ICCIDAndIMEI>>({})
export const ICCIDAndIMEIOptions = computed(() => {
  return Object.values(ICCIDAndIMEI.value)
})

export function deleteOneICCIDAndIMEI(IMEI) {
  delete ICCIDAndIMEI.value[IMEI]
}

// 同步ICCID和IMEI到该界桩的界桩表编辑表单的ICCIDOptions
function syncICCID2MarkerEdit(nats_body, nats_subject, nats_reply, msg: yrpcmsg.Ymsg) {
  const data: bysproto.IBmsg = bysproto.Bmsg.decode(msg.Body)
  const marker4gLoginInfo = bysproto.BloginReq.decode(data.Body as Uint8Array)
  ICCIDAndIMEI.value[marker4gLoginInfo.IMEI] = {
    ICCID: marker4gLoginInfo.ICCID,
    IMEI: marker4gLoginInfo.IMEI,
    LoginTimeStr: marker4gLoginInfo.LoginTimeStr
  }
}

const conflictICCIDAndIMEI = new Set()

// 提示ICCID和IMEI的对应存在冲突，需要用户手动确认
function notifyICCIDAndIMEIConflict(nats_body, nats_subject, nats_reply, msg: yrpcmsg.Ymsg) {
  const data: bysproto.IBmsg = bysproto.Bmsg.decode(msg.Body)
  const marker4gLoginInfo = bysproto.BloginReq.decode(data.Body as Uint8Array)
  const marker = BysMarker.getDataList().find(m => m.IMEI === data.Optstr) as bysdb.DbBysMarker
  StrPubSub.publish(NotifyICCIDAndIMEIConflict, marker, marker4gLoginInfo, data)
  if (conflictICCIDAndIMEI.has(marker?.MarkerNo)) {
    return
  }
  conflictICCIDAndIMEI.add(marker?.MarkerNo)
  const onOk = () => {
    conflictICCIDAndIMEI.delete(marker?.MarkerNo)
    // 更新系统内界桩的imei为marker4gLoginInfo.IMEI
    const dbMarker = cloneDeep(marker)
    dbMarker.IMEI = marker4gLoginInfo.IMEI
    const param: crud.IDMLParam = {
      KeyColumn: ['IMEI'],
    }
    const options: ICallOption = {
      OnResult() {
        BysMarker.setPartData(marker.RID + '', { IMEI: marker4gLoginInfo.IMEI })
        Notify.create({
          message: i18n.global.t('message.updateSuccess') as string,
          color: 'positive',
          icon: 'check_circle',
          position: 'top',
        })
      },
    }
    partialUpdateMarker(dbMarker, param, options)
  }
  const onCancel = () => {
    conflictICCIDAndIMEI.delete(marker?.MarkerNo)
  }
  const message = i18n.global.t('message.notifyICCIDAndIMEIConflict', {
    MarkerNo: marker?.MarkerNo,
    optStr: data.Optstr,
    IMEI: `<b>${marker4gLoginInfo.IMEI}</b>`
  })
  createDialogNotifyICCIDAndIMEI(message, onOk, onCancel, { html: true })
}

const conflictICCID = new Set()

function notifyICCIDAlreadyExist(nats_body, nats_subject, nats_reply, msg: yrpcmsg.Ymsg) {
  const data: bysproto.IBmsg = bysproto.Bmsg.decode(msg.Body)
  const marker4gLoginInfo = bysproto.BloginReq.decode(data.Body as Uint8Array)
  const duplicateMarker = bysdb.DbBysMarker.decode(data.Optbin as Uint8Array)
  const marker = BysMarker.getDataList().find(m => m.IMEI === data.Optstr) as bysdb.IDbBysMarker
  // 日志提示iccid重复
  StrPubSub.publish(NotifyICCIDAlreadyExist, duplicateMarker, marker, marker4gLoginInfo)
  if (conflictICCID.has(marker?.MarkerNo)) {
    return
  }
  conflictICCID.add(marker?.MarkerNo)

  const onOk = async () => {
    conflictICCID.delete(marker?.MarkerNo)
    // 清除系统内拥有该iccid的界桩的iccid
    duplicateMarker.ICCID = ''
    duplicateMarker.ExpirationDate = ''
    const param: crud.IDMLParam = {
      KeyColumn: ['ICCID', 'ExpirationDate'],
    }
    const isOk = await partialUpdateMarker(duplicateMarker, param)
    // 设置上报界桩的iccid
    if (isOk) {
      BysMarker.setPartData(duplicateMarker.RID + '', { ICCID: '', ExpirationDate: '' })
      const data = cloneDeep(marker)
      data.ICCID = marker4gLoginInfo.ICCID
      data.ExpirationDate = sysUtcTime()
      const param: crud.IDMLParam = {
        KeyColumn: ['ICCID', 'ExpirationDate'],
      }
      const options2: ICallOption = {
        OnResult() {
          BysMarker.setPartData(data.RID + '', { ICCID: data.ICCID, ExpirationDate: data.ExpirationDate })
          Notify.create({
            message: i18n.global.t('message.updateSuccess') as string,
            color: 'positive',
            icon: 'check_circle',
            position: 'top',
          })
        },
      }
      partialUpdateMarker(data, param, options2)
    }
  }
  const onCancel = () => {
    conflictICCID.delete(marker?.MarkerNo)
  }
  const message = i18n.global.t('message.notifyICCIDAlreadyExist', {
    duplicateMarkerNo: `<b>${duplicateMarker.MarkerNo}</b>`,
    markerNo: `<b>${marker?.MarkerNo}</b>`
  }) + `(ICCID: ${marker4gLoginInfo.ICCID})`
  createDialogNotifyICCIDAndIMEI(message, onOk, onCancel, { html: true })
}

function createDialogNotifyICCIDAndIMEI(message: string, okAction: Function, cancelAction: Function, options?: QDialogOptions) {
  Dialog.create({
    cancel: i18n.global.t('common.ignore'),
    ok: i18n.global.t('common.confirm'),
    message: message,
    focus: 'ok' as const,
    class: ['second-confirm', 'iccid-imei-repeat-dialog'],
    ...options,
  }).onOk(async () => {
    okAction()
  }).onCancel(() => {
    cancelAction()
  })
}

function syncRemoveAlarm2Client(nats_body, nats_subject, nats_reply, msg: yrpcmsg.Ymsg) {
  const bMsg: bysproto.IBmsg = bysproto.Bmsg.decode(msg.Body)
  const data: doc.IMarkerInfo = doc.MarkerInfo.decode(bMsg.Body as Uint8Array)
  const marker = BysMarker.getDataByIndex(data.MarkerID + '')
  BysMarker.setPartData(marker?.RID as string, { markerInfo: data })
  StrPubSub.publish(RemoveBysMarkerAlarm, marker)
}

// 订阅控制器上/下线指令
export function controllerCmdProcess(): void {
  // @ts-ignore
  rpcCon.NatsSubscribeAgain()
  const orgRIDList = Unit.getDataList().map(org => org.RID)
  orgRIDList.forEach(RID => {
    rpcCon.NatsSubsribe(`device.${RID}`, subscribeControllerCommand)
  })
  rpcCon.NatsSubsribe('device.1001', syncICCID2MarkerEdit)
  // device.1002指令 为系统内imei与上报界桩的imei存在冲突时，警告用户
  rpcCon.NatsSubsribe('device.1002', notifyICCIDAndIMEIConflict)
  // device.1100指令，为界桩报警超时后，用户解除报警后，同步解除报警到其他客户端
  rpcCon.NatsSubsribe('device.1100', syncRemoveAlarm2Client)
  // device.1101指令，为4g界桩上线,该iccid已经存在系统内,服务器无法同步, 在客户端给用户提示
  rpcCon.NatsSubsribe('device.1004', notifyICCIDAlreadyExist)
}

//缓存已经发送解除报警指令的界桩数据及发送时间
// let removeAlarmCmdCache: { [key: string]: number } = {}

function removeAlarm(bysMarker: BysMarkerAndUpdateInfo, controller: bysdb.IDbController, isAuto = false) {
  const checkParamsValid = () => {
    if (!bysMarker) {
      log.warn('Invalid bysMarker or controller')
      Notify.create({
        message: i18n.global.t('message.invalidRmAlarmCmd') as string,
        position: 'top',
        type: 'warning',
      })
      return false
    }

    // 4g界桩
    if (checkIs4GMarker(bysMarker.MarkerType)) {
      return true
    }

    // 常规界桩，需要控制器
    if (!controller) {
      log.warn('Invalid bysMarker or controller')
      Notify.create({
        message: i18n.global.t('message.invalidRmAlarmCmd') as string,
        position: 'top',
        type: 'warning',
      })
      return false
    }

    return true
  }

  const valid = checkParamsValid()
  if (!valid) return

  StrPubSub.publish(StopAlarmAudio)

  const createReqData: () => doc.IMarkerInfo = () => {
    // 4g界桩
    if (checkIs4GMarker(bysMarker.MarkerType)) {
      return {
        ControllerID: bysMarker.MarkerHWID,
        MarkerID: bysMarker.MarkerHWID,
        MarkerRID: bysMarker.RID,
        MarkerType: bysMarker.MarkerType,
      }
    }

    const updateInfo = bysMarker.updateInfo as DeviceUpdateInfo | undefined
    return {
      ControllerID: updateInfo?.StationID ?? controller.ControllerHWID,
      ControllerChannelNo: updateInfo?.StationDeviceNo ?? bysMarker.ControllerChannel,
      MarkerID: bysMarker.MarkerHWID,
      MarkerRID: bysMarker.RID,
      MarkerChannel: updateInfo?.DeviceChannelNo ?? bysMarker.MarkerChannel,
      MarkerUploadCmd: updateInfo?.Cmd,
      MarkerCmdTime: updateInfo?.CmdTime,
      MarkerSysPassOK: !updateInfo?.MarkerSysPassOK,
    }
  }

  let data = createReqData()
  log.info('removeAlarm', bysMarker.MarkerType, data)
  const opts: ICallOption = {
    OnResult: () => {
      // 设置解除报警指令状态位
      if (isAuto && bysMarker.removeAlarm) {
        return
      }

      BysMarker.setPartData(bysMarker.RID as string, { removeAlarm: true })
      // 显示通知
      Notify.create({
        message: i18n.global.t('message.sendSuccess') as string,
        position: 'top',
        type: 'positive',
      })
    },
    OnServerErr: (errRpc: yrpcmsg.Ymsg) => {
      log.error('removeAlarm err', errRpc)
      let message = ''
      if (errRpc.Optstr.includes('controller not online')) {
        message = message + i18n.global.t('message.4GMarkerOffline')
      } else {
        message = i18n.global.t('message.sendFailed') + errRpc.Optstr
      }
      Notify.create({
        message: message,
        position: 'top',
        type: 'warning',
      })

    },
    OnLocalErr: (err: any) => {
      log.error('removeAlarm error', err)
      Notify.create({
        message: i18n.global.t('message.sendFailed') + i18n.global.t('message.localErr') as string,
        position: 'top',
        type: 'warning',
      })
    },
    OnTimeout: (v?: any) => {
      log.warn('removeAlarm Timeout', v)
      Notify.create({
        message: i18n.global.t('message.sendFailed') + i18n.global.t('message.timeoutLabel') as string,
        position: 'top',
        type: 'warning',
      })
    },
  }
  RpcBysApi.CancelEmergency(data, opts)
}

/*function rmAlarmCmdShakeProof(MarkerHWID: string): boolean {

  if (removeAlarmCmdCache[MarkerHWID] &&
    (Date.now() - removeAlarmCmdCache[MarkerHWID]) < (15 * 1000)) {
    return false
  }
  removeAlarmCmdCache[MarkerHWID] = Date.now()
  return true
}*/

/*StrPubSub.subscribe(removeBysMarkerAlarmCmd,
  (bysMarker: BysMarkerAndUpdateInfo, controller: bysdb.IDbController) => {
    if (!rmAlarmCmdShakeProof(bysMarker.MarkerHWID as number + '')) {
      log.warn('rmAlarmCmdShakeProof warning')
      Notify.create({
        message:i18n.global.t('message.rmAlarmCmdShakeProof') as string,
        position: 'top',
        type: 'warning',
      })
      return
    }
    removeAlarm(bysMarker, controller)
  },
)
StrPubSub.subscribe(removeBysMarkerAlarmCmdAuto,
  (bysMarker: BysMarkerAndUpdateInfo, controller: bysdb.IDbController) => {
    if (!rmAlarmCmdShakeProof(bysMarker.MarkerHWID as number + '')) {
      return
    }
    removeAlarm(bysMarker, controller)
  },
)*/

StrPubSub.subscribe(removeBysMarkerAlarmCmd,
  (bysMarker: BysMarkerAndUpdateInfo, controller: bysdb.IDbController) => {
    removeAlarm(bysMarker, controller)
  },
)
StrPubSub.subscribe(removeBysMarkerAlarmCmdAuto,
  (bysMarker: BysMarkerAndUpdateInfo, controller: bysdb.IDbController) => {
    removeAlarm(bysMarker, controller, true)
  },
)

//基站/中继信道号   ChannelNo
// export const ChannelNo = [1, 2]

function getCmdSendSuccessOption() {
  //复用命令下发回调
  return {
    OnResult: () => {
      // 显示通知
      Notify.create({
        message: i18n.global.t('common.cmdSendSuc') as string,
        position: 'top',
        type: 'positive',
      })
    },
    OnServerErr: (errRpc: yrpcmsg.Ymsg) => {
      log.error('changeDefaultChannelNo server err', errRpc)
      // 显示通知
      Notify.create({
        message: i18n.global.t('common.cmdSendFailed') as string,
        type: 'warning',
        icon: 'warning',
        position: 'top',
      })
    },
    OnLocalErr: (err: any) => {
      log.error(i18n.global.t('common.cmdSendFailed') + ' local err', err)
      Notify.create({
        message: (i18n.global.t('common.cmdSendFailed') + i18n.global.t('message.localErr')) as string,
        position: 'top',
        type: 'positive',
      })
    },
    OnTimeout: (v: any) => {
      log.warn(i18n.global.t('common.cmdSendFailed') + ' timeout', v)
      Notify.create({
        message: (i18n.global.t('common.cmdSendFailed') + i18n.global.t('message.timeoutLabel')) as string,
        position: 'top',
        type: 'positive',
      })
    },
  }
}

/***
 * 修改目标控制器本地通道信道
 * @param controller 控制器
 * @param newChannelNo 新控制器信道号
 */
export function changeDefaultChannelNo(controller: bysdb.IDbController, newChannelNo: number) {
  let controllerCmd: doc.IControllerCmd = {
    ControllerID: controller.ControllerHWID,
    //如果是中继下面的界桩，传递中继在该基站下方的通道，如果是本基站下方的界桩，则传递0
    ControllerChannelNo: controller.ControllerType === ControllerTypes.relayController ? controller.ParentChannelNo : 0,
    NewChannelNo: newChannelNo,
    Cmd: 13,
  }
  RpcBysApi.SendControllerCmd(controllerCmd, getCmdSendSuccessOption())
}

/**
 * 修改目标控制器链路层信道号
 * @param controller
 * @param channelNo
 * @param newDevNo
 */
export function changeBetweenChannelNo(controller: bysdb.IDbController, channelNo: number, newDevNo: number) {
  let controllerCmd: doc.IControllerCmd = {
    ControllerID: controller.ControllerHWID,
    ControllerChannelNo: channelNo,//修改基站的哪个通道（指链路通道）
    NewChannelNo: newDevNo,
    Cmd: 14,
  }
  RpcBysApi.SendControllerCmd(controllerCmd, getCmdSendSuccessOption())
}

const controllerPingCount: Map<number, number> = new Map<number, number>()

export async function pingController(controller: bysdb.IDbController) {
  let ControllerID = controller.ControllerHWID
  if (controller.ControllerType !== ControllerTypes.baseController) {
    ControllerID = await getControllerIdByRID(controller.ParentRID)
  }
  const data: doc.IControllerCmd = {
    ControllerID,
    ControllerChannelNo: controller.ControllerType === ControllerTypes.baseController
      ? 0 : controller.ParentChannelNo,
    NewChannelNo: controller.ControllerType === ControllerTypes.baseController
      ? 0 : controller.ControllerHWID,
  }
  const opts: ICallOption = {
    OnResult: () => {
      // 显示通知
      Notify.create({
        message: i18n.global.t('message.sendSuccess') as string,
        position: 'top',
        type: 'positive',
      })
    },
    OnServerErr: (errRpc: yrpcmsg.Ymsg) => {
      log.error('pingController err', errRpc)
      let message: string = i18n.global.t('message.sendFailed') + ' '
      if (errRpc.Optstr.includes('cmd can not send')) {
        message += i18n.global.t('message.controllerOffline')
        setTimeout(() => {
          // 不在线，则标记处理为下线
          _offline(controller)
          controllerPingCount.delete(pingCountKey)
          StrPubSub.unsubscribe(ControllerHeartbeat, subFn)
        }, 0)
      } else {
        message += errRpc.Optstr
      }
      Notify.create({
        message: message,
        position: 'top',
        type: 'warning',
      })
    },
    OnLocalErr: (err: any) => {
      log.error('pingController error', err)
      Notify.create({
        message: i18n.global.t('message.sendFailed') + i18n.global.t('message.localErr') as string,
        position: 'top',
        type: 'warning',
      })
    },
    OnTimeout: (v?: any) => {
      log.warn('pingController Timeout', v)
      Notify.create({
        message: i18n.global.t('message.sendFailed') + i18n.global.t('message.timeoutLabel') as string,
        position: 'top',
        type: 'warning',
      })
    },
  }

  let timer: any = null
  const pingCountKey = controller.ControllerHWID as number
  let count = controllerPingCount.get(pingCountKey) as number ?? 0

  function subFn(data: ControllerAndUpdateCmd) {
    if (data.ControllerHWID !== controller.ControllerHWID) {
      return
    }

    timer && clearTimeout(timer)
    controllerPingCount.delete(pingCountKey)
  }

// 默认3s超时，没有收到对应的心跳，累计3次则判断为异常下线
  timer = setTimeout(() => {
    if (++count >= 3) {
      // 异常下线
      _offline(controller)
      controllerPingCount.delete(pingCountKey)
      StrPubSub.unsubscribe(ControllerHeartbeat, subFn)
      Notify.create({
        message: `${controller.ControllerNo} ${i18n.global.t('message.controllerOffline')}`,
        position: 'top',
        type: 'warning',
      })
      return
    }

    controllerPingCount.set(pingCountKey, count)
  }, 3 * 1000)

  StrPubSub.subscribe(ControllerHeartbeat, subFn)

  RpcBysApi.PingController(data, opts)
}

async function getControllerIdByRID(RID): Promise<number> {
  let controller = Controller.getData(RID) as IDbController
  if (!controller) {
    const controllerQuery: PRpcDbQuery = new PRpcDbQuery(DataName.Controller)
    controller = await controllerQuery.SelectOne(RID)
  }
  return controller?.ControllerHWID ?? -1
}

async function updateMarkerParamter(params: UpdataMarkerParam) {
  const options: ICallOption = {
    OnResult: (res: crud.IDMLResult, rpcCmd: yrpcmsg.Ymsg, meta?: yrpcmsg.IGrpcMeta) => {
      log.info('updateMarkerParamter Send result', res, rpcCmd, meta)
    },
    OnServerErr: (errRpc: yrpcmsg.Ymsg) => {
      log.error('updateMarkerParamter Send server error', errRpc)
    },
    OnLocalErr: (err: any) => {
      log.error('updateMarkerParamter Send local error', err)
    },
    OnTimeout: (v: any) => {
      log.warn('updateMarkerParamter Send timeout', v)
    },
  }

  RpcBysApi.UpdateMarkerParamter(params, options)
}

async function checkIfSendD0(newMarker: bysdb.IDbBysMarker, oldData: bysdb.IDbBysMarker) {
  const checkedAttrs = [
    'MarkerHWID', 'ControllerRID', 'ControllerChannel', 'MarkerQueueNo',
    'MarkerDayInterval', 'MarkerQueueInterval', 'MarkerEmergentInterval',
    'MarkerChannel', 'MarkerWakeupBaseTime', 'MarkerDisabled',
  ]
  const isNeedSendD0 = checkedAttrs.some((item) => {
    return newMarker[item] !== oldData[item]
  })
  if (!isNeedSendD0) return

  const controllerID = await getControllerIdByRID(oldData.ControllerRID)
  const sendData: UpdataMarkerParam = {
    controllerID,
    markerData: newMarker,
  }
  updateMarkerParamter(sendData)
}

// 检查参数是否有变更，需要判断对象类型，以便正确递归处理
// 返回值为true表示有变更
function checkParamsNotSomeWithObject(source: Record<string, any>, target: Record<string, any>): boolean {
  if (typeof source !== 'object' || typeof target !== 'object') {
    return source !== target
  }
  const sourceKeys = Object.keys(source)
  const targetKeys = Object.keys(target)
  if (sourceKeys.length !== targetKeys.length) {
    return true
  }
  for (const key of sourceKeys) {
    if (typeof source[key] === 'object' && typeof target[key] === 'object') {
      if (checkParamsNotSomeWithObject(source[key], target[key])) {
        return true
      }
    } else if (source[key] !== target[key]) {
      return true
    }
  }
  return false
}

export function check4GMarkerIsNeedUpdateParamTime(newMarker: bysdb.IDbBysMarker, oldData: bysdb.IDbBysMarker) {
  const checkedAttrs = [
    'addr', 'timerbase', 'timer', 'attitude', 'dirft', 'vibration', 'infrared',
    't1', 't2', 't3', 'n1',
  ]
  const newMarkerSettings = JSON.parse(newMarker.MarkerSettings ?? '{}')
  const oldMarkerSettings = JSON.parse(oldData.MarkerSettings ?? '{}')

  return checkedAttrs.some((item) => {
    return checkParamsNotSomeWithObject(newMarkerSettings[item], oldMarkerSettings[item])
  })
}

async function checkUpdateMarkerParamterWith4gMarker(newMarker: bysdb.IDbBysMarker, oldData: bysdb.IDbBysMarker) {
  const isNeedSend = check4GMarkerIsNeedUpdateParamTime(newMarker, oldData)
  if (!isNeedSend) return

  const sendData: UpdataMarkerParam = {
    controllerID: newMarker.MarkerHWID as number,
    markerData: newMarker,
  }
  updateMarkerParamter(sendData)
}

async function checkMarkerQueueIntervalWakeupBaseTime(newMarker: bysdb.IDbBysMarker) {
  const checkedAttrs = [
    'MarkerQueueInterval',
    'MarkerWakeupBaseTime',
  ]
  const propsValue = checkedAttrs.map((prop) => {
    return { [prop]: newMarker[prop] }
  }).reduce((p, c) => ({ ...p, ...c }), {})

  // 找到当前控制器下所有界桩
  const markerList = BysMarker.getDataList().filter(item => item.ControllerRID === newMarker.ControllerRID)

  // 更新界桩的本地数据
  markerList.forEach((item) => {
    BysMarker.setPartData(item.RID + '', propsValue)
  })
}

function checkUpdateParamsCommand(newMarker: bysdb.IDbBysMarker, oldData: bysdb.IDbBysMarker) {
  // 需要根据界桩类型来处理，如果类型不一样，则忽略
  if (newMarker.MarkerType !== oldData.MarkerType) return

  if (checkIs4GMarker(newMarker.MarkerType)) {
    checkUpdateMarkerParamterWith4gMarker(newMarker, oldData)
  } else {
    checkIfSendD0(newMarker, oldData)
    // 检查界桩的排队发射间隔、唤醒基准时间是否需要同步，同一个基站下的界桩，这两个参数必须相同
    checkMarkerQueueIntervalWakeupBaseTime(newMarker)
  }
}

export function markerRemoteKillOrActive(params: bysproto.BShutDown, options?: ICallOption) {
  log.info('markerRemoteKillOrActive:', params)
  const opts: ICallOption = {
    OnResult: (res: bysproto.IBmsg, rpcCmd: yrpcmsg.Ymsg, meta?: yrpcmsg.IGrpcMeta) => {
      log.info('MarkerRemoteKillOrActive Send result', res, rpcCmd, meta)
    },
    OnServerErr: (errRpc: yrpcmsg.Ymsg) => {
      log.error('MarkerRemoteKillOrActive Send server error', errRpc)
    },
    OnLocalErr: (err: any) => {
      log.error('MarkerRemoteKillOrActive Send local error', err)
    },
    OnTimeout: (v: any) => {
      log.warn('MarkerRemoteKillOrActive Send timeout', v)
    },
    ...options,
  }

  RpcBysApi.MarkerRemoteKillOrActive(params, opts)
}

// 当前只有4g界桩支持遥毙命令
// function checkMarkerRemoteKillOrActive(newMarker: bysdb.IDbBysMarker, oldData: bysdb.IDbBysMarker) {
//   if (newMarker.MarkerType !== MarkerType.Net4G && newMarker.MarkerType !== MarkerType.Net4GPro) return
//   // 参数没有变更，或者从遥毙变更为其他状态，都不下发命令
//   if ((newMarker.MarkerDisabled === oldData.MarkerDisabled || oldData.MarkerDisabled === 1) &&
//    (newMarker.CameraDisabled === oldData.CameraDisabled)) return

//   // 下发命令
//   const params: bysproto.BShutDown = {
//     deviceID: newMarker.MarkerHWID as number,
//     type: newMarker.MarkerDisabled as number,
//     camType: newMarker.CameraDisabled as number,
//   }
//   markerRemoteKillOrActive(params)
// }

StrPubSub.subscribe(UpdateDbBysMarker, (newMarker: bysdb.IDbBysMarker, oldData: bysdb.IDbBysMarker, from?: string) => {
  // 如果是多客户端同步，忽略
  if (from === 'cdc') return

  // 检查界桩参数是否需要更新
  checkUpdateParamsCommand(newMarker, oldData)
  // 检查是否要下发遥毙等命令
  // 以前遥晕遥毙都是通过表单更新后，检测是否有遥晕遥毙，然后下发指令，现在都是通过二次确认后直接下发
  // checkMarkerRemoteKillOrActive(newMarker, oldData)
})

export function modifyControllerServerAddr(req: doc.IControllerNewServerAddr): Promise<boolean> {
  log.info('modifyControllerServerAddr', req)
  return new Promise((resolve, reject) => {
    const ResCache: Map<number, boolean> = new Map<number, boolean>()
    const targets = req.Target ?? []
    const cancelSubsribe = () => {
      targets.forEach(t => {
        if (ResCache.has(t.StationID as number)) {
          return
        }
        const controller = Controller.getDataByIndex(t.StationID + '')
        rpcCon.NatsUnsubsribe(`device.${controller?.OrgRID}.${t.StationID}`)
      })
    }
    // 发送数据前，先订阅控制器的应答事件
    targets.forEach(t => {
      const controller = Controller.getDataByIndex(t.StationID + '')
      rpcCon.NatsSubsribeOnce(`device.${controller?.OrgRID}.${t.StationID}`,
        (nats_body, nats_subject, nats_reply, msg: yrpcmsg.Ymsg) => {
          // 设置已收到应答标记
          ResCache.set(t.StationID as number, true)
          gotModifyControllerServerAddressRes(msg, t.StationID as number)
        },
      )
    })

    const options: ICallOption = {
      OnResult: (res: any, rpcCmd: yrpcmsg.Ymsg, meta?: IGrpcMeta) => {
        log.info('modifyControllerServerAddr OnResult:', res, rpcCmd, meta)
        resolve(true)

        // 3分钟都没有响应，则取消订阅
        setTimeout(() => {
          cancelSubsribe()
        }, 3 * 60 * 1000)
      },
      OnServerErr: (errRpc: yrpcmsg.Ymsg) => {
        log.error('modifyControllerServerAddr OnServerErr:', errRpc)
        reject(errRpc.Optstr)
        cancelSubsribe()
      },
      OnTimeout: (v: any) => {
        log.error('modifyControllerServerAddr OnTimeout:', v)
        reject('OnTimeout:' + v)
        cancelSubsribe()
      },
    }
    RpcBysApiUpdateControllerServerAddr(req, options)
  })
}

export function isLonginController(NetworkType: number) {
  /** 登录渠道， 0:未知 1:有线网络， 2:4g网络 3:无线FSK */
  const NetworkTypes = [1, 2]
  return NetworkTypes.includes(NetworkType)
}

export const ControllerChannelNoRelation: Map<string, number> = new Map<string, number>()

window.ControllerChannelNoRelation = ControllerChannelNoRelation

// 更新中继与基站的索引关系
export function updateControllerStationDeviceNoRelation() {
  // 中继
  const controllers = Controller.getDataList().filter(d => d.ControllerType === ControllerTypes.relayController)
  for (let controller of controllers) {
    // 用基站的hwid和中继通道号生成key，缓存中继的hwid
    const parent = Controller.getData(controller.ParentRID + '')
    if (!parent) {
      continue
    }

    const key = `${parent.ControllerHWID}.${controller.ParentChannelNo}`
    ControllerChannelNoRelation.set(key, controller.ControllerHWID as number)
  }
}

export function getControllerByChannelNo(parentID: number, channel: number): ControllerAndUpdateCmd | undefined {
  const index = ControllerChannelNoRelation.get(`${parentID}.${channel}`)
  return Controller.getDataByIndex(index + '')
}

// 检测界桩时间是否同步，如果已同步则返回true
export function checkUpdateCmdTimeIsSync(CmdTime: dayjs.ConfigType): boolean {
  const val = Math.abs(dayjs.utc(sysUtcTime()).diff(dayjs.utc(CmdTime), 'second'))
  return val <= LatestUploadTime
}

// 遥晕,遥毙,解除遥晕
export async function markerSetRemoteKillOrActive(shutDownType: number, camType: number, data: bysdb.IDbBysMarker): Promise<boolean> {
  return new Promise((resolve, reject) => {
    const oldData = Object.assign({}, data)
    const params: bysproto.BShutDown = {
      deviceID: data.MarkerHWID as number,
      type: shutDownType,
      camType,
    }
    const options: ICallOption = {
      OnResult: (res: bysproto.IBmsg, rpcCmd: yrpcmsg.Ymsg, meta?: yrpcmsg.IGrpcMeta) => {
        log.info('MarkerRemoteKillOrActive Send result', res, rpcCmd, meta)

        const partData: Record<string, any> = {
          MarkerDisabled: shutDownType,
          CameraDisabled: camType,
        }
        BysMarker.setPartData(data.RID as string, partData)
        StrPubSub.publish(UpdateDbBysMarker, data, oldData)

        let message = i18n.global.t('common.cmdSendSuc') as string
        if (res.Res === 2) {
          message = i18n.global.t('common.cmdSendSucAndWaitMarkerRes') as string
        }
        Notify.create({
          message,
          color: 'positive',
          icon: 'check_circle',
          position: 'top',
        })
        return resolve(true)
      },
      OnServerErr: (errRpc: yrpcmsg.Ymsg) => {
        log.error('MarkerRemoteKillOrActive Send server error', errRpc)
        return reject(false)
      },
      OnLocalErr: (err: any) => {
        log.error('MarkerRemoteKillOrActive Send local error', err)
        return reject(false)
      },
      OnTimeout: (v: any) => {
        log.warn('MarkerRemoteKillOrActive Send timeout', v)
        return reject(false)
      },
    }
    markerRemoteKillOrActive(params, options)
  })
}

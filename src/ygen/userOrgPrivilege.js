/*eslint-disable block-scoped-var, id-length, no-control-regex, no-magic-numbers, no-prototype-builtins, no-redeclare, no-shadow, no-var, sort-vars*/
import * as $protobuf from 'protobufjs/minimal'

// Common aliases
const $Reader = $protobuf.Reader,
  $Writer = $protobuf.Writer,
  $util = $protobuf.util

// Exported root namespace
const $root = $protobuf.roots['default'] || ($protobuf.roots['default'] = {})

export const user = ($root.user = (() => {
  /**
   * Namespace user.
   * @exports user
   * @namespace
   */
  const user = $root.user || {}

  user.DbUserOrgPrivilege = (function () {
    /**
     * Properties of a DbUserOrgPrivilege.
     * @memberof user
     * @interface IDbUserOrgPrivilege
     * @property {string|null} [RID] @db uuid primary key
     * 行ID
     * @property {string|null} [UserRID] @db uuid not null REFERENCES DbUser(RID) ON DELETE CASCADE
     * 用户rid
     * @property {string|null} [OrgRID] @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 有权限的群组
     * @property {number|null} [IncludeChildren] @db int default 0
     * 是否包含下级群组
     * @property {string|null} [Setting] @db jsonb not null default  '{}'::jsonb
     * 其它设置，可以在里面扩展需要用到的其它信息
     * @property {string|null} [UpdatedAt] @db timestamp not null default now_utc()
     * 数据最后修改时间
     * @property {string|null} [UpdatedDC] @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */

    /**
     * Constructs a new DbUserOrgPrivilege.
     * @memberof user
     * @classdesc 用户群组权限表
     * @rpc crud pcrud
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbUserOrgPrivilegeUserRID on DbUserOrgPrivilege USING hash(UserRID);
     * @dbpost INSERT INTO dbuserorgprivilege(rid, userrid, orgrid, includechildren, setting, updatedat, updateddc)VALUES('00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', 1, '{}'::jsonb, now_utc(), '') ON CONFLICT  DO NOTHING;
     * @implements IDbUserOrgPrivilege
     * @constructor
     * @param {user.IDbUserOrgPrivilege=} [properties] Properties to set
     */
    function DbUserOrgPrivilege(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * @db uuid primary key
     * 行ID
     * @member {string} RID
     * @memberof user.DbUserOrgPrivilege
     * @instance
     */
    DbUserOrgPrivilege.prototype.RID = ''

    /**
     * @db uuid not null REFERENCES DbUser(RID) ON DELETE CASCADE
     * 用户rid
     * @member {string} UserRID
     * @memberof user.DbUserOrgPrivilege
     * @instance
     */
    DbUserOrgPrivilege.prototype.UserRID = ''

    /**
     * @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 有权限的群组
     * @member {string} OrgRID
     * @memberof user.DbUserOrgPrivilege
     * @instance
     */
    DbUserOrgPrivilege.prototype.OrgRID = ''

    /**
     * @db int default 0
     * 是否包含下级群组
     * @member {number} IncludeChildren
     * @memberof user.DbUserOrgPrivilege
     * @instance
     */
    DbUserOrgPrivilege.prototype.IncludeChildren = 0

    /**
     * @db jsonb not null default  '{}'::jsonb
     * 其它设置，可以在里面扩展需要用到的其它信息
     * @member {string} Setting
     * @memberof user.DbUserOrgPrivilege
     * @instance
     */
    DbUserOrgPrivilege.prototype.Setting = ''

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     * @member {string} UpdatedAt
     * @memberof user.DbUserOrgPrivilege
     * @instance
     */
    DbUserOrgPrivilege.prototype.UpdatedAt = ''

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     * @member {string} UpdatedDC
     * @memberof user.DbUserOrgPrivilege
     * @instance
     */
    DbUserOrgPrivilege.prototype.UpdatedDC = ''

    /**
     * Encodes the specified DbUserOrgPrivilege message. Does not implicitly {@link user.DbUserOrgPrivilege.verify|verify} messages.
     * @function encode
     * @memberof user.DbUserOrgPrivilege
     * @static
     * @param {user.IDbUserOrgPrivilege} message DbUserOrgPrivilege message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    DbUserOrgPrivilege.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (message.RID != null && Object.hasOwnProperty.call(message, 'RID'))
        writer.uint32(/* id 1, wireType 2 =*/ 10).string(message.RID)
      if (
        message.UserRID != null &&
        Object.hasOwnProperty.call(message, 'UserRID')
      )
        writer.uint32(/* id 3, wireType 2 =*/ 26).string(message.UserRID)
      if (
        message.OrgRID != null &&
        Object.hasOwnProperty.call(message, 'OrgRID')
      )
        writer.uint32(/* id 4, wireType 2 =*/ 34).string(message.OrgRID)
      if (
        message.IncludeChildren != null &&
        Object.hasOwnProperty.call(message, 'IncludeChildren')
      )
        writer.uint32(/* id 5, wireType 0 =*/ 40).int32(message.IncludeChildren)
      if (
        message.Setting != null &&
        Object.hasOwnProperty.call(message, 'Setting')
      )
        writer.uint32(/* id 8, wireType 2 =*/ 66).string(message.Setting)
      if (
        message.UpdatedAt != null &&
        Object.hasOwnProperty.call(message, 'UpdatedAt')
      )
        writer.uint32(/* id 14, wireType 2 =*/ 114).string(message.UpdatedAt)
      if (
        message.UpdatedDC != null &&
        Object.hasOwnProperty.call(message, 'UpdatedDC')
      )
        writer.uint32(/* id 15, wireType 2 =*/ 122).string(message.UpdatedDC)
      return writer
    }

    /**
     * Decodes a DbUserOrgPrivilege message from the specified reader or buffer.
     * @function decode
     * @memberof user.DbUserOrgPrivilege
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {user.DbUserOrgPrivilege} DbUserOrgPrivilege
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    DbUserOrgPrivilege.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.user.DbUserOrgPrivilege()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.RID = reader.string()
            break
          case 3:
            message.UserRID = reader.string()
            break
          case 4:
            message.OrgRID = reader.string()
            break
          case 5:
            message.IncludeChildren = reader.int32()
            break
          case 8:
            message.Setting = reader.string()
            break
          case 14:
            message.UpdatedAt = reader.string()
            break
          case 15:
            message.UpdatedDC = reader.string()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return DbUserOrgPrivilege
  })()

  return user
})())

export { $root as default }

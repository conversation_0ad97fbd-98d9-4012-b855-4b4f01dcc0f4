/*eslint-disable block-scoped-var, id-length, no-control-regex, no-magic-numbers, no-prototype-builtins, no-redeclare, no-shadow, no-var, sort-vars*/
import * as $protobuf from 'protobufjs/minimal'

// Common aliases
const $Reader = $protobuf.Reader,
  $Writer = $protobuf.Writer,
  $util = $protobuf.util

// Exported root namespace
const $root = $protobuf.roots['default'] || ($protobuf.roots['default'] = {})

export const rpc = ($root.rpc = (() => {
  /**
   * Namespace rpc.
   * @exports rpc
   * @namespace
   */
  const rpc = $root.rpc || {}

  rpc.RpcEmpty = (function () {
    /**
     * Properties of a RpcEmpty.
     * @memberof rpc
     * @interface IRpcEmpty
     */

    /**
     * Constructs a new RpcEmpty.
     * @memberof rpc
     * @classdesc 空消息，grpc要求必须有req,res message
     * @implements IRpcEmpty
     * @constructor
     * @param {rpc.IRpcEmpty=} [properties] Properties to set
     */
    function RpcEmpty(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * Encodes the specified RpcEmpty message. Does not implicitly {@link rpc.RpcEmpty.verify|verify} messages.
     * @function encode
     * @memberof rpc.RpcEmpty
     * @static
     * @param {rpc.IRpcEmpty} message RpcEmpty message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    RpcEmpty.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      return writer
    }

    /**
     * Decodes a RpcEmpty message from the specified reader or buffer.
     * @function decode
     * @memberof rpc.RpcEmpty
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {rpc.RpcEmpty} RpcEmpty
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    RpcEmpty.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.rpc.RpcEmpty()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return RpcEmpty
  })()

  rpc.RpcCommon = (function () {
    /**
     * Properties of a RpcCommon.
     * @memberof rpc
     * @interface IRpcCommon
     * @property {number|null} [Code] 代码，要求各个rpc的不同，会有不同的值
     * @property {string|null} [System] optional system
     * @property {string|null} [SessionID] optional session id
     * @property {string|null} [Err] optional Error
     * @property {Uint8Array|null} [Body] optional body
     */

    /**
     * Constructs a new RpcCommon.
     * @memberof rpc
     * @classdesc 一般消息
     * @implements IRpcCommon
     * @constructor
     * @param {rpc.IRpcCommon=} [properties] Properties to set
     */
    function RpcCommon(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 代码，要求各个rpc的不同，会有不同的值
     * @member {number} Code
     * @memberof rpc.RpcCommon
     * @instance
     */
    RpcCommon.prototype.Code = 0

    /**
     * optional system
     * @member {string} System
     * @memberof rpc.RpcCommon
     * @instance
     */
    RpcCommon.prototype.System = ''

    /**
     * optional session id
     * @member {string} SessionID
     * @memberof rpc.RpcCommon
     * @instance
     */
    RpcCommon.prototype.SessionID = ''

    /**
     * optional Error
     * @member {string} Err
     * @memberof rpc.RpcCommon
     * @instance
     */
    RpcCommon.prototype.Err = ''

    /**
     * optional body
     * @member {Uint8Array} Body
     * @memberof rpc.RpcCommon
     * @instance
     */
    RpcCommon.prototype.Body = $util.newBuffer([])

    /**
     * Encodes the specified RpcCommon message. Does not implicitly {@link rpc.RpcCommon.verify|verify} messages.
     * @function encode
     * @memberof rpc.RpcCommon
     * @static
     * @param {rpc.IRpcCommon} message RpcCommon message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    RpcCommon.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (message.Code != null && Object.hasOwnProperty.call(message, 'Code'))
        writer.uint32(/* id 1, wireType 0 =*/ 8).int32(message.Code)
      if (
        message.System != null &&
        Object.hasOwnProperty.call(message, 'System')
      )
        writer.uint32(/* id 2, wireType 2 =*/ 18).string(message.System)
      if (
        message.SessionID != null &&
        Object.hasOwnProperty.call(message, 'SessionID')
      )
        writer.uint32(/* id 3, wireType 2 =*/ 26).string(message.SessionID)
      if (message.Err != null && Object.hasOwnProperty.call(message, 'Err'))
        writer.uint32(/* id 4, wireType 2 =*/ 34).string(message.Err)
      if (message.Body != null && Object.hasOwnProperty.call(message, 'Body'))
        writer.uint32(/* id 5, wireType 2 =*/ 42).bytes(message.Body)
      return writer
    }

    /**
     * Decodes a RpcCommon message from the specified reader or buffer.
     * @function decode
     * @memberof rpc.RpcCommon
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {rpc.RpcCommon} RpcCommon
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    RpcCommon.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.rpc.RpcCommon()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.Code = reader.int32()
            break
          case 2:
            message.System = reader.string()
            break
          case 3:
            message.SessionID = reader.string()
            break
          case 4:
            message.Err = reader.string()
            break
          case 5:
            message.Body = reader.bytes()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return RpcCommon
  })()

  return rpc
})())

export { $root as default }

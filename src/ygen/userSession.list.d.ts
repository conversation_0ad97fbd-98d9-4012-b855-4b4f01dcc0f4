import * as $protobuf from 'protobufjs'
/** Namespace user. */
export namespace user {
  /** Properties of a DbUserSessionList. */
  interface IDbUserSessionList {
    /** 批次号，从0开始 */
    BatchNo?: number | null

    /** rows offset,从0开始 */
    RowsOffset?: number | null

    /** 本批次数据 */
    Rows?: user.IDbUserSession[] | null
  }

  /** DbUserSession list */
  class DbUserSessionList implements IDbUserSessionList {
    /**
     * Constructs a new DbUserSessionList.
     * @param [properties] Properties to set
     */
    constructor(properties?: user.IDbUserSessionList)

    /** 批次号，从0开始 */
    public BatchNo: number

    /** rows offset,从0开始 */
    public RowsOffset: number

    /** 本批次数据 */
    public Rows: user.IDbUserSession[]

    /**
     * Encodes the specified DbUserSessionList message. Does not implicitly {@link user.DbUserSessionList.verify|verify} messages.
     * @param message DbUserSessionList message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: user.IDbUserSessionList,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a DbUserSessionList message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns DbUserSessionList
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): user.DbUserSessionList
  }

  /** Properties of a DbUserSession. */
  interface IDbUserSession {
    /**
     * @db uuid primary key
     * 行ID,session rid
     */
    RID?: string | null

    /**
     * @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 用户所属的群组
     */
    OrgRID?: string | null

    /**
     * @db uuid not null REFERENCES DbUser(RID) ON DELETE CASCADE
     * 用户rid
     */
    UserRID?: string | null

    /**
     * @db int
     * session类型 0:web client
     */
    SessionType?: number | null

    /**
     * @db timestamp not null default now_utc()
     * 用户登录的时间
     */
    LoginTime?: string | null

    /**
     * @db jsonb not null default  '{}'::jsonb
     * 用户登录信息
     */
    LoginInfo?: string | null

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     */
    UpdatedAt?: string | null

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */
    UpdatedDC?: string | null
  }

  /** 用户session表 */
  class DbUserSession implements IDbUserSession {
    /**
     * Constructs a new DbUserSession.
     * @rpc crud
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbUserSessionOrgRID on DbUserSession USING hash(OrgRID)
     * @param [properties] Properties to set
     */
    constructor(properties?: user.IDbUserSession)

    /**
     * @db uuid primary key
     * 行ID,session rid
     */
    public RID: string

    /**
     * @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 用户所属的群组
     */
    public OrgRID: string

    /**
     * @db uuid not null REFERENCES DbUser(RID) ON DELETE CASCADE
     * 用户rid
     */
    public UserRID: string

    /**
     * @db int
     * session类型 0:web client
     */
    public SessionType: number

    /**
     * @db timestamp not null default now_utc()
     * 用户登录的时间
     */
    public LoginTime: string

    /**
     * @db jsonb not null default  '{}'::jsonb
     * 用户登录信息
     */
    public LoginInfo: string

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     */
    public UpdatedAt: string

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */
    public UpdatedDC: string

    /**
     * Encodes the specified DbUserSession message. Does not implicitly {@link user.DbUserSession.verify|verify} messages.
     * @param message DbUserSession message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: user.IDbUserSession,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a DbUserSession message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns DbUserSession
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): user.DbUserSession
  }
}

import * as $protobuf from 'protobufjs'
/** Namespace org. */
export namespace org {
  /** Properties of a DbOrgList. */
  interface IDbOrgList {
    /** 批次号，从0开始 */
    BatchNo?: number | null

    /** rows offset,从0开始 */
    RowsOffset?: number | null

    /** 本批次数据 */
    Rows?: org.IDbOrg[] | null
  }

  /** DbOrg list */
  class DbOrgList implements IDbOrgList {
    /**
     * Constructs a new DbOrgList.
     * @param [properties] Properties to set
     */
    constructor(properties?: org.IDbOrgList)

    /** 批次号，从0开始 */
    public BatchNo: number

    /** rows offset,从0开始 */
    public RowsOffset: number

    /** 本批次数据 */
    public Rows: org.IDbOrg[]

    /**
     * Encodes the specified DbOrgList message. Does not implicitly {@link org.DbOrgList.verify|verify} messages.
     * @param message DbOrgList message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: org.<PERSON>b<PERSON><PERSON><PERSON><PERSON>,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a DbOrgList message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns DbOrgList
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): org.DbOrgList
  }

  /** Properties of a DbOrg. */
  interface IDbOrg {
    /**
     * @db uuid primary key
     * 行ID
     */
    RID?: string | null

    /**
     * @db varchar(64) unique not null
     * 组织机构自编号
     */
    OrgID?: string | null

    /**
     * @db int default 0
     */
    OrgType?: number | null

    /**
     * @db int default 100
     * 排序值,由用户指定,不用管拼音或者其它语种的排序
     */
    SortValue?: number | null

    /**
     * @db varchar(32) not null
     * 机构名称,缩写
     */
    ShortName?: string | null

    /**
     * @db varchar(256)
     * 机构名称,全称
     */
    FullName?: string | null

    /**
     * @db text
     * 机构描述/备注信息
     */
    Note?: string | null

    /**
     * @db jsonb default  '{}'::jsonb
     * 其它设置，可以在里面扩展需要用到的其它信息
     */
    Setting?: string | null

    /**
     * @db uuid
     * 创建者的rid
     */
    CreatorRID?: string | null

    /**
     * @db uuid
     * 此组织的上级机构
     */
    OrgRID?: string | null

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     */
    UpdatedAt?: string | null

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */
    UpdatedDC?: string | null
  }

  /** 群组/组织结构表 */
  class DbOrg implements IDbOrg {
    /**
     * Constructs a new DbOrg.
     * @rpc crud pcrud
     * @dbpost DO $$ BEGIN BEGIN
     * @dbpost ALTER TABLE DbOrg ADD CONSTRAINT fkDbOrgParentRID  FOREIGN KEY (OrgRID) REFERENCES DbOrg(RID);
     * @dbpost EXCEPTION WHEN duplicate_object THEN --do nothing
     * @dbpost END; END $$;
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbOrgCreatorRID on DbOrg USING hash(CreatorRID);
     * @dbpost INSERT INTO dborg(rid, orgid, orgtype, sortvalue, shortname, fullname, note, setting, OrgRID, updatedat, updateddc)VALUES('00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', 0, 100, 'root', 'root', '', '{}'::jsonb, NULL, now_utc(), '')  ON CONFLICT  DO NOTHING;
     * @param [properties] Properties to set
     */
    constructor(properties?: org.IDbOrg)

    /**
     * @db uuid primary key
     * 行ID
     */
    public RID: string

    /**
     * @db varchar(64) unique not null
     * 组织机构自编号
     */
    public OrgID: string

    /**
     * @db int default 0
     */
    public OrgType: number

    /**
     * @db int default 100
     * 排序值,由用户指定,不用管拼音或者其它语种的排序
     */
    public SortValue: number

    /**
     * @db varchar(32) not null
     * 机构名称,缩写
     */
    public ShortName: string

    /**
     * @db varchar(256)
     * 机构名称,全称
     */
    public FullName: string

    /**
     * @db text
     * 机构描述/备注信息
     */
    public Note: string

    /**
     * @db jsonb default  '{}'::jsonb
     * 其它设置，可以在里面扩展需要用到的其它信息
     */
    public Setting: string

    /**
     * @db uuid
     * 创建者的rid
     */
    public CreatorRID: string

    /**
     * @db uuid
     * 此组织的上级机构
     */
    public OrgRID: string

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     */
    public UpdatedAt: string

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */
    public UpdatedDC: string

    /**
     * Encodes the specified DbOrg message. Does not implicitly {@link org.DbOrg.verify|verify} messages.
     * @param message DbOrg message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: org.IDbOrg,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a DbOrg message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns DbOrg
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): org.DbOrg
  }
}

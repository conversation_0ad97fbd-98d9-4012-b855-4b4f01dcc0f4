import * as $protobuf from 'protobufjs'
/** Namespace bysproto. */
export namespace bysproto {
  /** Properties of a Bmsg. */
  interface IBmsg {
    /**
     * 命令字
     * 1:登录
     * 2:界桩打卡上传
     * 12:回应界桩上传
     * 13：修改控制器手台监听信道号
     * 15：更新控制器新的注册地址
     * 11:ping
     * ----4G界桩新增----
     * 21：信息上报
     * 31：上报应答
     * 22：报警
     * 23：拓展报警(外接传感器)
     * 32：报警应答
     * 33：报警解除
     * 28: 报警解除应答
     * 24：参数更新
     * 34：参数更新应答
     * 25：软件更新(预留)
     * 35：软件更新应答(预留)
     * 26：主动抓拍(预留)
     * 36：主动抓拍应答(预留)
     * 27：遥闭
     * 37：遥闭应答
     */
    Cmd?: number | null

    /** 从0开始增1使用,用于区分相同命令重复的包 */
    No?: number | null

    /** response code */
    Res?: number | null

    /** msg body */
    Body?: Uint8Array | null

    /** optional str parameter,额外的信息,一般不会有,有些特殊命令里面可能用到 */
    Optstr?: string | null

    /** optional binary parameter,额外的信息,一般不会有,有些特殊命令里面可能用到 */
    Optbin?: Uint8Array | null
  }

  /** 系统与控制器所有的消息交互底层都以此为包装 */
  class Bmsg implements IBmsg {
    /**
     * Constructs a new Bmsg.
     * @param [properties] Properties to set
     */
    constructor(properties?: bysproto.IBmsg)

    /**
     * 命令字
     * 1:登录
     * 2:界桩打卡上传
     * 12:回应界桩上传
     * 13：修改控制器手台监听信道号
     * 15：更新控制器新的注册地址
     * 11:ping
     * ----4G界桩新增----
     * 21：信息上报
     * 31：上报应答
     * 22：报警
     * 23：拓展报警(外接传感器)
     * 32：报警应答
     * 33：报警解除
     * 28: 报警解除应答
     * 24：参数更新
     * 34：参数更新应答
     * 25：软件更新(预留)
     * 35：软件更新应答(预留)
     * 26：主动抓拍(预留)
     * 36：主动抓拍应答(预留)
     * 27：遥闭
     * 37：遥闭应答
     */
    public Cmd: number

    /** 从0开始增1使用,用于区分相同命令重复的包 */
    public No: number

    /** response code */
    public Res: number

    /** msg body */
    public Body: Uint8Array

    /** optional str parameter,额外的信息,一般不会有,有些特殊命令里面可能用到 */
    public Optstr: string

    /** optional binary parameter,额外的信息,一般不会有,有些特殊命令里面可能用到 */
    public Optbin: Uint8Array

    /**
     * Encodes the specified Bmsg message. Does not implicitly {@link bysproto.Bmsg.verify|verify} messages.
     * @param message Bmsg message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: bysproto.IBmsg,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a Bmsg message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns Bmsg
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): bysproto.Bmsg
  }

  /** Properties of a BloginReq. */
  interface IBloginReq {
    /** 控制器名称，不是控制器硬件ID */
    Name?: string | null

    /**
     * base64(sha256(base64(sha256(controllerName+controllerPass))+LoginTimeStr))
     * 4g界桩: base64(sha256(base64(sha256(IMEI+ICCID))+LoginTimeStr))
     */
    PassHash?: string | null

    /** yyyy-mm-dd HH:MM:SS, 必须是3分钟内的时间(utc) */
    LoginTimeStr?: string | null

    /** 系统名称 */
    Sys?: string | null

    /** 电量,单位V,=0无效，7.2v = 72 */
    Power?: number | null

    /** 控制器本地手台当前信道号(对界桩通讯的手台)，无效时为0 */
    ChannelNo?: number | null

    /**
     * 本控制器数据中转的通道号和对应的信道号
     * [通道号1,通道1手台的信道号，通道号2,通道2手台的信道号，...]
     * 如[1,1,2,1]表示通道1 在1信道，通道2也在1信道
     */
    TransferChannelNos?: number[] | null

    /** 登录渠道， 0:未知 1:有线网络， 2:4g网络 3:无线FSK */
    NetworkType?: number | null

    /** 登录设备类型 0:fsk控制器 1:4g界桩 */
    DeviceType?: number | null

    /** 4g界桩IMEI */
    IMEI?: string | null

    /** 4g界桩ICCID */
    ICCID?: string | null

    /**
     * 界桩参数版本时间
     * yyyy-mm-dd HH:MM:SS
     */
    dataVersion?: string | null
  }

  /**
   * 登录信息
   * bmsg.cmd=1
   */
  class BloginReq implements IBloginReq {
    /**
     * Constructs a new BloginReq.
     * @param [properties] Properties to set
     */
    constructor(properties?: bysproto.IBloginReq)

    /** 控制器名称，不是控制器硬件ID */
    public Name: string

    /**
     * base64(sha256(base64(sha256(controllerName+controllerPass))+LoginTimeStr))
     * 4g界桩: base64(sha256(base64(sha256(IMEI+ICCID))+LoginTimeStr))
     */
    public PassHash: string

    /** yyyy-mm-dd HH:MM:SS, 必须是3分钟内的时间(utc) */
    public LoginTimeStr: string

    /** 系统名称 */
    public Sys: string

    /** 电量,单位V,=0无效，7.2v = 72 */
    public Power: number

    /** 控制器本地手台当前信道号(对界桩通讯的手台)，无效时为0 */
    public ChannelNo: number

    /**
     * 本控制器数据中转的通道号和对应的信道号
     * [通道号1,通道1手台的信道号，通道号2,通道2手台的信道号，...]
     * 如[1,1,2,1]表示通道1 在1信道，通道2也在1信道
     */
    public TransferChannelNos: number[]

    /** 登录渠道， 0:未知 1:有线网络， 2:4g网络 3:无线FSK */
    public NetworkType: number

    /** 登录设备类型 0:fsk控制器 1:4g界桩 */
    public DeviceType: number

    /** 4g界桩IMEI */
    public IMEI: string

    /** 4g界桩ICCID */
    public ICCID: string

    /**
     * 界桩参数版本时间
     * yyyy-mm-dd HH:MM:SS
     */
    public dataVersion: string

    /**
     * Encodes the specified BloginReq message. Does not implicitly {@link bysproto.BloginReq.verify|verify} messages.
     * @param message BloginReq message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: bysproto.IBloginReq,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a BloginReq message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns BloginReq
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): bysproto.BloginReq
  }

  /** Properties of a BloginRes. */
  interface IBloginRes {
    /** 100: 登录成功 -1：无此控制器信息 -2:时间参数不对 -3: 未知错误 -4:pass 不对 */
    Code?: number | null

    /** 控制器硬件ID，登录成功时返回 */
    ControllerID?: number | null

    /** 服务器时间yyyy-mm-dd HH:MM:SS.zzz(utc) */
    ServerTime?: string | null

    /** 校验界桩crc2的系统密码 */
    SystemPassword?: string | null

    /** 错误描述，未知错误时可能有 */
    Err?: string | null
  }

  /**
   * 登录回应
   * bmsy.cmd=1
   * bmsg.Res=1
   * bmsg.body=BloginRes
   */
  class BloginRes implements IBloginRes {
    /**
     * Constructs a new BloginRes.
     * @param [properties] Properties to set
     */
    constructor(properties?: bysproto.IBloginRes)

    /** 100: 登录成功 -1：无此控制器信息 -2:时间参数不对 -3: 未知错误 -4:pass 不对 */
    public Code: number

    /** 控制器硬件ID，登录成功时返回 */
    public ControllerID: number

    /** 服务器时间yyyy-mm-dd HH:MM:SS.zzz(utc) */
    public ServerTime: string

    /** 校验界桩crc2的系统密码 */
    public SystemPassword: string

    /** 错误描述，未知错误时可能有 */
    public Err: string

    /**
     * Encodes the specified BloginRes message. Does not implicitly {@link bysproto.BloginRes.verify|verify} messages.
     * @param message BloginRes message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: bysproto.IBloginRes,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a BloginRes message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns BloginRes
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): bysproto.BloginRes
  }

  /** Properties of a BGPS. */
  interface IBGPS {
    /** 东经为+，西经为-,单位为度 */
    Lon?: number | null

    /** 北纬为+，南纬为-,单位为度 */
    Lat?: number | null

    /** BGPS Height */
    Height?: number | null
  }

  /** 界桩上传的gps信息,无效gps时为全0 */
  class BGPS implements IBGPS {
    /**
     * Constructs a new BGPS.
     * @param [properties] Properties to set
     */
    constructor(properties?: bysproto.IBGPS)

    /** 东经为+，西经为-,单位为度 */
    public Lon: number

    /** 北纬为+，南纬为-,单位为度 */
    public Lat: number

    /** BGPS Height. */
    public Height: number

    /**
     * Encodes the specified BGPS message. Does not implicitly {@link bysproto.BGPS.verify|verify} messages.
     * @param message BGPS message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: bysproto.IBGPS,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a BGPS message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns BGPS
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): bysproto.BGPS
  }

  /** Properties of a BdeviceUpdate. */
  interface IBdeviceUpdate {
    /** 界桩ID */
    DeviceID?: number | null

    /**
     * 上传的命令
     * 0xd1: 常规上传
     * 0xd2: 报警上传
     */
    Cmd?: number | null

    /** 界桩状态，目前只用低位一个字节 */
    Status?: number | null

    /** 界桩上传的系统密码检验是否正确 0:正确 1：不正确 */
    SystemPassCheckOK?: number | null

    /** gps信息，没有时不需要填写 */
    GPS?: bysproto.IBGPS | null

    /** 界桩参数版本 */
    ParamVersion?: number | null

    /** 界桩参数更新时间 */
    ParamTime?: string | null

    /** 指令时间 */
    CmdTime?: string | null

    /** 接收的基站/中继控制器ID */
    StationID?: number | null

    /** 基站控制器的接收通道号，编号从1开始,不是基站上传的，此值为0 */
    StationDeviceNo?: number | null

    /** 接收设备的场强值 */
    DeviceFieldStrength?: number | null

    /** 接收设备的信道号 */
    DeviceChannelNo?: number | null
  }

  /**
   * 界桩数据上传
   * bmsg.cmd=2
   */
  class BdeviceUpdate implements IBdeviceUpdate {
    /**
     * Constructs a new BdeviceUpdate.
     * @param [properties] Properties to set
     */
    constructor(properties?: bysproto.IBdeviceUpdate)

    /** 界桩ID */
    public DeviceID: number

    /**
     * 上传的命令
     * 0xd1: 常规上传
     * 0xd2: 报警上传
     */
    public Cmd: number

    /** 界桩状态，目前只用低位一个字节 */
    public Status: number

    /** 界桩上传的系统密码检验是否正确 0:正确 1：不正确 */
    public SystemPassCheckOK: number

    /** gps信息，没有时不需要填写 */
    public GPS?: bysproto.IBGPS | null

    /** 界桩参数版本 */
    public ParamVersion: number

    /** 界桩参数更新时间 */
    public ParamTime: string

    /** 指令时间 */
    public CmdTime: string

    /** 接收的基站/中继控制器ID */
    public StationID: number

    /** 基站控制器的接收通道号，编号从1开始,不是基站上传的，此值为0 */
    public StationDeviceNo: number

    /** 接收设备的场强值 */
    public DeviceFieldStrength: number

    /** 接收设备的信道号 */
    public DeviceChannelNo: number

    /**
     * Encodes the specified BdeviceUpdate message. Does not implicitly {@link bysproto.BdeviceUpdate.verify|verify} messages.
     * @param message BdeviceUpdate message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: bysproto.IBdeviceUpdate,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a BdeviceUpdate message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns BdeviceUpdate
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): bysproto.BdeviceUpdate
  }

  /** Properties of a ControllerStatus. */
  interface IControllerStatus {
    /** 接收的基站/中继控制器ID */
    StationID?: number | null

    /** 基站控制器的接收手台号，编号从1开始,本控制器的状态时，此值为0 */
    StationDeviceNo?: number | null

    /** 电量,单位V,=0无效，7.2v = 72 */
    Power?: number | null

    /** 控制器手台当前信道号(与界桩通讯的手台) */
    ChannelNo?: number | null

    /**
     * 本控制器数据中转的通道号和对应的信道号
     * [通道号1,通道1手台的信道号，通道号2,通道2手台的信道号，...]
     * 如[1,1,2,1]表示通道1 在1信道，通道2也在1信道
     */
    TransferChannelNos?: number[] | null

    /**
     * 控制器状态
     * bit0:时间异常 0:无异常 1:无法获取时间（可能gps异常)
     */
    Status?: number | null
  }

  /** 中继/基站状态信息 */
  class ControllerStatus implements IControllerStatus {
    /**
     * Constructs a new ControllerStatus.
     * @param [properties] Properties to set
     */
    constructor(properties?: bysproto.IControllerStatus)

    /** 接收的基站/中继控制器ID */
    public StationID: number

    /** 基站控制器的接收手台号，编号从1开始,本控制器的状态时，此值为0 */
    public StationDeviceNo: number

    /** 电量,单位V,=0无效，7.2v = 72 */
    public Power: number

    /** 控制器手台当前信道号(与界桩通讯的手台) */
    public ChannelNo: number

    /**
     * 本控制器数据中转的通道号和对应的信道号
     * [通道号1,通道1手台的信道号，通道号2,通道2手台的信道号，...]
     * 如[1,1,2,1]表示通道1 在1信道，通道2也在1信道
     */
    public TransferChannelNos: number[]

    /**
     * 控制器状态
     * bit0:时间异常 0:无异常 1:无法获取时间（可能gps异常)
     */
    public Status: number

    /**
     * Encodes the specified ControllerStatus message. Does not implicitly {@link bysproto.ControllerStatus.verify|verify} messages.
     * @param message ControllerStatus message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: bysproto.IControllerStatus,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a ControllerStatus message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns ControllerStatus
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): bysproto.ControllerStatus
  }

  /** Properties of a BdeviceUpdateResponse. */
  interface IBdeviceUpdateResponse {
    /** 界桩ID */
    DeviceID?: number | null

    /** 接收的基站/中继控制器ID */
    StationID?: number | null

    /** 基站控制器的通道号，编号从1开始,不是基站上传的，此值为0 */
    StationDeviceNo?: number | null

    /** 回应命令数据 */
    Cmd0xD0?: Uint8Array | null
  }

  /**
   * 回应界桩上传（如果确实有参数需要修改的话）
   * bmsg.cmd=12
   */
  class BdeviceUpdateResponse implements IBdeviceUpdateResponse {
    /**
     * Constructs a new BdeviceUpdateResponse.
     * @param [properties] Properties to set
     */
    constructor(properties?: bysproto.IBdeviceUpdateResponse)

    /** 界桩ID */
    public DeviceID: number

    /** 接收的基站/中继控制器ID */
    public StationID: number

    /** 基站控制器的通道号，编号从1开始,不是基站上传的，此值为0 */
    public StationDeviceNo: number

    /** 回应命令数据 */
    public Cmd0xD0: Uint8Array

    /**
     * Encodes the specified BdeviceUpdateResponse message. Does not implicitly {@link bysproto.BdeviceUpdateResponse.verify|verify} messages.
     * @param message BdeviceUpdateResponse message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: bysproto.IBdeviceUpdateResponse,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a BdeviceUpdateResponse message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns BdeviceUpdateResponse
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): bysproto.BdeviceUpdateResponse
  }

  /** Properties of a BControllerUpdateChannel. */
  interface IBControllerUpdateChannel {
    /** 控制器ID */
    StationID?: number | null

    /** 控制器通道号 */
    StationDeviceNo?: number | null

    /** 新默认监听信道号 */
    NewChannelNo?: number | null
  }

  /**
   * 修改控制器手台默认信道号（针对与界桩通讯的手台）
   * bmsg.cmd=13
   */
  class BControllerUpdateChannel implements IBControllerUpdateChannel {
    /**
     * Constructs a new BControllerUpdateChannel.
     * @param [properties] Properties to set
     */
    constructor(properties?: bysproto.IBControllerUpdateChannel)

    /** 控制器ID */
    public StationID: number

    /** 控制器通道号 */
    public StationDeviceNo: number

    /** 新默认监听信道号 */
    public NewChannelNo: number

    /**
     * Encodes the specified BControllerUpdateChannel message. Does not implicitly {@link bysproto.BControllerUpdateChannel.verify|verify} messages.
     * @param message BControllerUpdateChannel message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: bysproto.IBControllerUpdateChannel,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a BControllerUpdateChannel message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns BControllerUpdateChannel
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): bysproto.BControllerUpdateChannel
  }

  /** Properties of a BControllerNewServerAddr. */
  interface IBControllerNewServerAddr {
    /** 基站控制器ID */
    StationID?: number | null

    /** 在上级控制器的通道号，中继时需要填写，基站本地通道时为0 */
    ControllerChannelNo?: number | null

    /** 新服务器IP地址，支持域名和IP */
    Ip?: string | null

    /** 新服务器端口 */
    Port?: number | null
  }

  /**
   * bmsg.cmd=15, 更新控制器新的注册地址
   * bmsg.body=BControllerNewServerAddr
   * bmsg.Res=1,控制器应答
   */
  class BControllerNewServerAddr implements IBControllerNewServerAddr {
    /**
     * Constructs a new BControllerNewServerAddr.
     * @param [properties] Properties to set
     */
    constructor(properties?: bysproto.IBControllerNewServerAddr)

    /** 基站控制器ID */
    public StationID: number

    /** 在上级控制器的通道号，中继时需要填写，基站本地通道时为0 */
    public ControllerChannelNo: number

    /** 新服务器IP地址，支持域名和IP */
    public Ip: string

    /** 新服务器端口 */
    public Port: number

    /**
     * Encodes the specified BControllerNewServerAddr message. Does not implicitly {@link bysproto.BControllerNewServerAddr.verify|verify} messages.
     * @param message BControllerNewServerAddr message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: bysproto.IBControllerNewServerAddr,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a BControllerNewServerAddr message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns BControllerNewServerAddr
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): bysproto.BControllerNewServerAddr
  }

  /** Properties of a BInfoReporting. */
  interface IBInfoReporting {
    /** 本机设备编号 */
    deviceID?: number | null

    /** 1：开机上报 2：调试上报 3：定时上报 */
    type?: number | null

    /** sim卡iccid 20位 */
    iccid?: string | null

    /** 格式：Vx.x.x_yyMMdd V1.0.1_231031 */
    softwareVersion?: string | null

    /** yyyy-mm-dd HH:MM:SS */
    dataVersion?: string | null

    /** 设备时间yyyy-mm-dd HH:MM:SS */
    time?: string | null

    /** 设备电池电压单位mv */
    battery?: number | null

    /** 设备4G模块场强信号幅度 单位dbm */
    signal?: number | null

    /**
     * Bit0：报警锁定(0:正常，1:报警锁定)
     * Bit1：RTC时钟状态(0:正常，1:故障)
     * Bit2：GPS模块状态(0:正常，1:故障)
     * Bit3：三轴传感器状态(0:正常，1:故障)
     * Bit4：电池状态(0:正常，1:故障)
     * Bit5：摄像头状态(0:正常，1:故障)(预留)
     * Bit6：红外探头状态(0:正常，1:故障)(预留)
     * Bit7-Bit31：预留
     */
    state?: number | null

    /**
     * Bit0：位移报警(0:正常，1:报警)
     * Bit1：震动报警(0:正常，1:报警)
     * Bit2：倾斜报警(0:正常，1:报警)
     * Bit3：红外报警(0:正常，1:报警)(预留)
     * Bit4-Bit31：预留
     */
    alarmstate?: number | null

    /** 设备温度 */
    temp?: number | null

    /** 湿度 单位：%RH */
    rh?: number | null
  }

  /**
   * 信息上报
   * bmsg.cmd=21
   */
  class BInfoReporting implements IBInfoReporting {
    /**
     * Constructs a new BInfoReporting.
     * @param [properties] Properties to set
     */
    constructor(properties?: bysproto.IBInfoReporting)

    /** 本机设备编号 */
    public deviceID: number

    /** 1：开机上报 2：调试上报 3：定时上报 */
    public type: number

    /** sim卡iccid 20位 */
    public iccid: string

    /** 格式：Vx.x.x_yyMMdd V1.0.1_231031 */
    public softwareVersion: string

    /** yyyy-mm-dd HH:MM:SS */
    public dataVersion: string

    /** 设备时间yyyy-mm-dd HH:MM:SS */
    public time: string

    /** 设备电池电压单位mv */
    public battery: number

    /** 设备4G模块场强信号幅度 单位dbm */
    public signal: number

    /**
     * Bit0：报警锁定(0:正常，1:报警锁定)
     * Bit1：RTC时钟状态(0:正常，1:故障)
     * Bit2：GPS模块状态(0:正常，1:故障)
     * Bit3：三轴传感器状态(0:正常，1:故障)
     * Bit4：电池状态(0:正常，1:故障)
     * Bit5：摄像头状态(0:正常，1:故障)(预留)
     * Bit6：红外探头状态(0:正常，1:故障)(预留)
     * Bit7-Bit31：预留
     */
    public state: number

    /**
     * Bit0：位移报警(0:正常，1:报警)
     * Bit1：震动报警(0:正常，1:报警)
     * Bit2：倾斜报警(0:正常，1:报警)
     * Bit3：红外报警(0:正常，1:报警)(预留)
     * Bit4-Bit31：预留
     */
    public alarmstate: number

    /** 设备温度 */
    public temp: number

    /** 湿度 单位：%RH */
    public rh: number

    /**
     * Encodes the specified BInfoReporting message. Does not implicitly {@link bysproto.BInfoReporting.verify|verify} messages.
     * @param message BInfoReporting message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: bysproto.IBInfoReporting,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a BInfoReporting message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns BInfoReporting
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): bysproto.BInfoReporting
  }

  /** Properties of a BVamp. */
  interface IBVamp {
    /** 震动幅度 */
    amplitude?: number | null

    /** 震动持续时间 s */
    duration?: number | null
  }

  /** 震动报警参数 */
  class BVamp implements IBVamp {
    /**
     * Constructs a new BVamp.
     * @param [properties] Properties to set
     */
    constructor(properties?: bysproto.IBVamp)

    /** 震动幅度 */
    public amplitude: number

    /** 震动持续时间 s */
    public duration: number

    /**
     * Encodes the specified BVamp message. Does not implicitly {@link bysproto.BVamp.verify|verify} messages.
     * @param message BVamp message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: bysproto.IBVamp,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a BVamp message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns BVamp
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): bysproto.BVamp
  }

  /** Properties of a BAlarmReporting. */
  interface IBAlarmReporting {
    /** 本机设备编号 */
    deviceID?: number | null

    /** 1：开机上报 2：调试上报 3：报警上报 */
    type?: number | null

    /**
     * Bit0：位移报警(0:正常，1:报警)
     * Bit1：震动报警(0:正常，1:报警)
     * Bit2：倾斜报警(0:正常，1:报警)
     * Bit3：红外报警(0:正常，1:报警)(预留)
     * Bit4-Bit31：预留
     */
    alarmstate?: number | null

    /** 定位坐标 */
    locate?: bysproto.IBGPS | null

    /** 倾斜角度，单位：度 */
    attitude?: number | null

    /** 震动报警 */
    vibration?: bysproto.IBVamp | null

    /** 红外预留 */
    infrared?: number | null

    /** 摄像头预留 */
    camera?: number | null

    /** 其他（预留） */
    reserve?: Uint8Array | null
  }

  /**
   * 报警上报
   * bmsg.cmd=22
   */
  class BAlarmReporting implements IBAlarmReporting {
    /**
     * Constructs a new BAlarmReporting.
     * @param [properties] Properties to set
     */
    constructor(properties?: bysproto.IBAlarmReporting)

    /** 本机设备编号 */
    public deviceID: number

    /** 1：开机上报 2：调试上报 3：报警上报 */
    public type: number

    /**
     * Bit0：位移报警(0:正常，1:报警)
     * Bit1：震动报警(0:正常，1:报警)
     * Bit2：倾斜报警(0:正常，1:报警)
     * Bit3：红外报警(0:正常，1:报警)(预留)
     * Bit4-Bit31：预留
     */
    public alarmstate: number

    /** 定位坐标 */
    public locate?: bysproto.IBGPS | null

    /** 倾斜角度，单位：度 */
    public attitude: number

    /** 震动报警 */
    public vibration?: bysproto.IBVamp | null

    /** 红外预留 */
    public infrared: number

    /** 摄像头预留 */
    public camera: number

    /** 其他（预留） */
    public reserve: Uint8Array

    /**
     * Encodes the specified BAlarmReporting message. Does not implicitly {@link bysproto.BAlarmReporting.verify|verify} messages.
     * @param message BAlarmReporting message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: bysproto.IBAlarmReporting,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a BAlarmReporting message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns BAlarmReporting
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): bysproto.BAlarmReporting
  }

  /** Properties of a BAlarmClear. */
  interface IBAlarmClear {
    /** 目标设备编号 */
    deviceID?: number | null

    /** 1：报警解除 2：报警锁定 */
    response?: number | null
  }

  /**
   * 报警解除
   * bmsg.cmd=33
   */
  class BAlarmClear implements IBAlarmClear {
    /**
     * Constructs a new BAlarmClear.
     * @param [properties] Properties to set
     */
    constructor(properties?: bysproto.IBAlarmClear)

    /** 目标设备编号 */
    public deviceID: number

    /** 1：报警解除 2：报警锁定 */
    public response: number

    /**
     * Encodes the specified BAlarmClear message. Does not implicitly {@link bysproto.BAlarmClear.verify|verify} messages.
     * @param message BAlarmClear message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: bysproto.IBAlarmClear,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a BAlarmClear message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns BAlarmClear
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): bysproto.BAlarmClear
  }

  /** Properties of a BDataUpdate. */
  interface IBDataUpdate {
    /** 目标设备编号 */
    deviceID?: number | null

    /** Bit0：解除报警锁定 Bit1：更新参数 Bit2：校准时间 */
    type?: number | null

    /** 版本yyyy-mm-dd HH:MM:SS */
    dataVersion?: string | null

    /** 服务器地址  xxx.xxx.xxx.xxx:xxxx   xxxxxx.com:xxxx */
    addr?: string | null

    /** 定时上报基准时间HH:MM:SS */
    timerbase?: string | null

    /** timer 6h、8h、12h、24h */
    timer?: number | null

    /** 姿态报警阈值 */
    attitude?: number | null

    /** 位移报警阈值 */
    dirft?: number | null

    /** 震动报警阈值 */
    vibration?: bysproto.IBVamp | null

    /** 红外报警阈值 */
    infrared?: number | null

    /** 延迟休眠时间 10s */
    t1?: number | null

    /** 调试模式时间 120s */
    t2?: number | null

    /** 报警间隔 10s */
    t3?: number | null

    /** 报警次数 10 */
    n1?: number | null
  }

  /**
   * 参数更新
   * bmsg.cmd=24
   */
  class BDataUpdate implements IBDataUpdate {
    /**
     * Constructs a new BDataUpdate.
     * @param [properties] Properties to set
     */
    constructor(properties?: bysproto.IBDataUpdate)

    /** 目标设备编号 */
    public deviceID: number

    /** Bit0：解除报警锁定 Bit1：更新参数 Bit2：校准时间 */
    public type: number

    /** 版本yyyy-mm-dd HH:MM:SS */
    public dataVersion: string

    /** 服务器地址  xxx.xxx.xxx.xxx:xxxx   xxxxxx.com:xxxx */
    public addr: string

    /** 定时上报基准时间HH:MM:SS */
    public timerbase: string

    /** timer 6h、8h、12h、24h */
    public timer: number

    /** 姿态报警阈值 */
    public attitude: number

    /** 位移报警阈值 */
    public dirft: number

    /** 震动报警阈值 */
    public vibration?: bysproto.IBVamp | null

    /** 红外报警阈值 */
    public infrared: number

    /** 延迟休眠时间 10s */
    public t1: number

    /** 调试模式时间 120s */
    public t2: number

    /** 报警间隔 10s */
    public t3: number

    /** 报警次数 10 */
    public n1: number

    /**
     * Encodes the specified BDataUpdate message. Does not implicitly {@link bysproto.BDataUpdate.verify|verify} messages.
     * @param message BDataUpdate message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: bysproto.IBDataUpdate,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a BDataUpdate message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns BDataUpdate
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): bysproto.BDataUpdate
  }

  /** Properties of a BShutDown. */
  interface IBShutDown {
    /** 目标设备编号 */
    deviceID?: number | null

    /** 0:遥活 1:遥闭  2:遥晕 */
    type?: number | null

    /** 0:遥活 1:遥闭  2:遥晕 */
    camType?: number | null
  }

  /**
   * 遥闭
   * bmsg.cmd=27
   */
  class BShutDown implements IBShutDown {
    /**
     * Constructs a new BShutDown.
     * @param [properties] Properties to set
     */
    constructor(properties?: bysproto.IBShutDown)

    /** 目标设备编号 */
    public deviceID: number

    /** 0:遥活 1:遥闭  2:遥晕 */
    public type: number

    /** 0:遥活 1:遥闭  2:遥晕 */
    public camType: number

    /**
     * Encodes the specified BShutDown message. Does not implicitly {@link bysproto.BShutDown.verify|verify} messages.
     * @param message BShutDown message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: bysproto.IBShutDown,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a BShutDown message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns BShutDown
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): bysproto.BShutDown
  }
}

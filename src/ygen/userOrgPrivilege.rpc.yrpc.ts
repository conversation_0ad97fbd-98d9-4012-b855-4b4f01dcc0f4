import { Writer } from 'protobufjs'
import { rpcCon } from 'jsyrpc'
import { ICallOption } from 'jsyrpc'

import { TRpcStream } from 'jsyrpc'
import * as UserOrgPrivilegeY from '@ygen/userOrgPrivilege'
import * as CrudY from '@ygen/crud'
import * as UserOrgPrivilegeListY from '@ygen/userOrgPrivilege.list'

export function RpcDbUserOrgPrivilegeInsert(
  req: UserOrgPrivilegeY.user.IDbUserOrgPrivilege,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = UserOrgPrivilegeY.user.DbUserOrgPrivilege.encode(req)
  let reqData = w.finish()

  const api = '/user.RpcDbUserOrgPrivilege/Insert'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function RpcDbUserOrgPrivilegeUpdate(
  req: UserOrgPrivilegeY.user.IDbUserOrgPrivilege,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = UserOrgPrivilegeY.user.DbUserOrgPrivilege.encode(req)
  let reqData = w.finish()

  const api = '/user.RpcDbUserOrgPrivilege/Update'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function RpcDbUserOrgPrivilegePartialUpdate(
  req: UserOrgPrivilegeY.user.IDbUserOrgPrivilege,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = UserOrgPrivilegeY.user.DbUserOrgPrivilege.encode(req)
  let reqData = w.finish()

  const api = '/user.RpcDbUserOrgPrivilege/PartialUpdate'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function RpcDbUserOrgPrivilegeDelete(
  req: UserOrgPrivilegeY.user.IDbUserOrgPrivilege,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = UserOrgPrivilegeY.user.DbUserOrgPrivilege.encode(req)
  let reqData = w.finish()

  const api = '/user.RpcDbUserOrgPrivilege/Delete'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function RpcDbUserOrgPrivilegeSelectOne(
  req: CrudY.crud.IDMLParam,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = CrudY.crud.DMLParam.encode(req)
  let reqData = w.finish()

  const api = '/user.RpcDbUserOrgPrivilege/SelectOne'
  const v = 0

  return rpcCon.UnaryCall(
    reqData,
    api,
    v,
    UserOrgPrivilegeY.user.DbUserOrgPrivilege,
    callOpt,
  )
}

export function RpcDbUserOrgPrivilegeSelectMany(
  req: CrudY.crud.DMLParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/user.RpcDbUserOrgPrivilege/SelectMany'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.DMLParam,
    UserOrgPrivilegeY.user.DbUserOrgPrivilege,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function RpcDbUserOrgPrivilegeQuery(
  req: CrudY.crud.QueryParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/user.RpcDbUserOrgPrivilege/Query'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.QueryParam,
    UserOrgPrivilegeY.user.DbUserOrgPrivilege,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function RpcDbUserOrgPrivilegeQueryBatch(
  req: CrudY.crud.QueryParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/user.RpcDbUserOrgPrivilege/QueryBatch'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.QueryParam,
    UserOrgPrivilegeListY.user.DbUserOrgPrivilegeList,
    callOpt,
  )
  r.sendFirst(req)
  return r
}

export class RpcDbUserOrgPrivilege {
  public static Insert(
    req: UserOrgPrivilegeY.user.IDbUserOrgPrivilege,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbUserOrgPrivilegeInsert(req, callOpt)
  }
  public static Update(
    req: UserOrgPrivilegeY.user.IDbUserOrgPrivilege,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbUserOrgPrivilegeUpdate(req, callOpt)
  }
  public static PartialUpdate(
    req: UserOrgPrivilegeY.user.IDbUserOrgPrivilege,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbUserOrgPrivilegePartialUpdate(req, callOpt)
  }
  public static Delete(
    req: UserOrgPrivilegeY.user.IDbUserOrgPrivilege,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbUserOrgPrivilegeDelete(req, callOpt)
  }
  public static SelectOne(
    req: CrudY.crud.IDMLParam,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbUserOrgPrivilegeSelectOne(req, callOpt)
  }
  public static SelectMany(
    req: CrudY.crud.DMLParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return RpcDbUserOrgPrivilegeSelectMany(req, callOpt)
  }
  public static Query(
    req: CrudY.crud.QueryParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return RpcDbUserOrgPrivilegeQuery(req, callOpt)
  }
  public static QueryBatch(
    req: CrudY.crud.QueryParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return RpcDbUserOrgPrivilegeQueryBatch(req, callOpt)
  }
}

export function PrpcDbUserOrgPrivilegeInsert(
  req: UserOrgPrivilegeY.user.IDbUserOrgPrivilege,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = UserOrgPrivilegeY.user.DbUserOrgPrivilege.encode(req)
  let reqData = w.finish()

  const api = '/user.PrpcDbUserOrgPrivilege/Insert'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function PrpcDbUserOrgPrivilegeUpdate(
  req: UserOrgPrivilegeY.user.IDbUserOrgPrivilege,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = UserOrgPrivilegeY.user.DbUserOrgPrivilege.encode(req)
  let reqData = w.finish()

  const api = '/user.PrpcDbUserOrgPrivilege/Update'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function PrpcDbUserOrgPrivilegePartialUpdate(
  req: UserOrgPrivilegeY.user.IDbUserOrgPrivilege,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = UserOrgPrivilegeY.user.DbUserOrgPrivilege.encode(req)
  let reqData = w.finish()

  const api = '/user.PrpcDbUserOrgPrivilege/PartialUpdate'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function PrpcDbUserOrgPrivilegeDelete(
  req: UserOrgPrivilegeY.user.IDbUserOrgPrivilege,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = UserOrgPrivilegeY.user.DbUserOrgPrivilege.encode(req)
  let reqData = w.finish()

  const api = '/user.PrpcDbUserOrgPrivilege/Delete'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function PrpcDbUserOrgPrivilegeSelectOne(
  req: CrudY.crud.IDMLParam,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = CrudY.crud.DMLParam.encode(req)
  let reqData = w.finish()

  const api = '/user.PrpcDbUserOrgPrivilege/SelectOne'
  const v = 0

  return rpcCon.UnaryCall(
    reqData,
    api,
    v,
    UserOrgPrivilegeY.user.DbUserOrgPrivilege,
    callOpt,
  )
}

export function PrpcDbUserOrgPrivilegeSelectMany(
  req: CrudY.crud.DMLParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/user.PrpcDbUserOrgPrivilege/SelectMany'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.DMLParam,
    UserOrgPrivilegeY.user.DbUserOrgPrivilege,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function PrpcDbUserOrgPrivilegeQuery(
  req: CrudY.crud.PrivilegeParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/user.PrpcDbUserOrgPrivilege/Query'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.PrivilegeParam,
    UserOrgPrivilegeY.user.DbUserOrgPrivilege,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function PrpcDbUserOrgPrivilegeQueryBatch(
  req: CrudY.crud.PrivilegeParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/user.PrpcDbUserOrgPrivilege/QueryBatch'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.PrivilegeParam,
    UserOrgPrivilegeListY.user.DbUserOrgPrivilegeList,
    callOpt,
  )
  r.sendFirst(req)
  return r
}

export class PrpcDbUserOrgPrivilege {
  public static Insert(
    req: UserOrgPrivilegeY.user.IDbUserOrgPrivilege,
    callOpt?: ICallOption,
  ): boolean {
    return PrpcDbUserOrgPrivilegeInsert(req, callOpt)
  }
  public static Update(
    req: UserOrgPrivilegeY.user.IDbUserOrgPrivilege,
    callOpt?: ICallOption,
  ): boolean {
    return PrpcDbUserOrgPrivilegeUpdate(req, callOpt)
  }
  public static PartialUpdate(
    req: UserOrgPrivilegeY.user.IDbUserOrgPrivilege,
    callOpt?: ICallOption,
  ): boolean {
    return PrpcDbUserOrgPrivilegePartialUpdate(req, callOpt)
  }
  public static Delete(
    req: UserOrgPrivilegeY.user.IDbUserOrgPrivilege,
    callOpt?: ICallOption,
  ): boolean {
    return PrpcDbUserOrgPrivilegeDelete(req, callOpt)
  }
  public static SelectOne(
    req: CrudY.crud.IDMLParam,
    callOpt?: ICallOption,
  ): boolean {
    return PrpcDbUserOrgPrivilegeSelectOne(req, callOpt)
  }
  public static SelectMany(
    req: CrudY.crud.DMLParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return PrpcDbUserOrgPrivilegeSelectMany(req, callOpt)
  }
  public static Query(
    req: CrudY.crud.PrivilegeParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return PrpcDbUserOrgPrivilegeQuery(req, callOpt)
  }
  public static QueryBatch(
    req: CrudY.crud.PrivilegeParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return PrpcDbUserOrgPrivilegeQueryBatch(req, callOpt)
  }
}
export default {
  RpcDbUserOrgPrivilege,
  RpcDbUserOrgPrivilegeInsert,
  RpcDbUserOrgPrivilegeUpdate,
  RpcDbUserOrgPrivilegePartialUpdate,
  RpcDbUserOrgPrivilegeDelete,
  RpcDbUserOrgPrivilegeSelectOne,
  RpcDbUserOrgPrivilegeSelectMany,
  RpcDbUserOrgPrivilegeQuery,
  RpcDbUserOrgPrivilegeQueryBatch,
  PrpcDbUserOrgPrivilege,
  PrpcDbUserOrgPrivilegeInsert,
  PrpcDbUserOrgPrivilegeUpdate,
  PrpcDbUserOrgPrivilegePartialUpdate,
  PrpcDbUserOrgPrivilegeDelete,
  PrpcDbUserOrgPrivilegeSelectOne,
  PrpcDbUserOrgPrivilegeSelectMany,
  PrpcDbUserOrgPrivilegeQuery,
  PrpcDbUserOrgPrivilegeQueryBatch,
}

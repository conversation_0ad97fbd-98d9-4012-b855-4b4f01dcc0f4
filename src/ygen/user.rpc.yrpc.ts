import { Writer } from 'protobufjs'
import { rpcCon } from 'jsyrpc'
import { ICallOption } from 'jsyrpc'

import { TRpcStream } from 'jsyrpc'
import * as UserY from '@ygen/user'
import * as CrudY from '@ygen/crud'
import * as UserListY from '@ygen/user.list'

export function RpcDbUserInsert(
  req: UserY.user.IDbUser,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = UserY.user.DbUser.encode(req)
  let reqData = w.finish()

  const api = '/user.RpcDbUser/Insert'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function RpcDbUserUpdate(
  req: UserY.user.IDbUser,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = UserY.user.DbUser.encode(req)
  let reqData = w.finish()

  const api = '/user.RpcDbUser/Update'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function RpcDbUserPartialUpdate(
  req: UserY.user.IDbUser,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = UserY.user.DbUser.encode(req)
  let reqData = w.finish()

  const api = '/user.RpcDbUser/PartialUpdate'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function RpcDbUserDelete(
  req: UserY.user.IDbUser,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = UserY.user.DbUser.encode(req)
  let reqData = w.finish()

  const api = '/user.RpcDbUser/Delete'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function RpcDbUserSelectOne(
  req: CrudY.crud.IDMLParam,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = CrudY.crud.DMLParam.encode(req)
  let reqData = w.finish()

  const api = '/user.RpcDbUser/SelectOne'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, UserY.user.DbUser, callOpt)
}

export function RpcDbUserSelectMany(
  req: CrudY.crud.DMLParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/user.RpcDbUser/SelectMany'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.DMLParam,
    UserY.user.DbUser,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function RpcDbUserQuery(
  req: CrudY.crud.QueryParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/user.RpcDbUser/Query'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.QueryParam,
    UserY.user.DbUser,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function RpcDbUserQueryBatch(
  req: CrudY.crud.QueryParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/user.RpcDbUser/QueryBatch'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.QueryParam,
    UserListY.user.DbUserList,
    callOpt,
  )
  r.sendFirst(req)
  return r
}

export class RpcDbUser {
  public static Insert(
    req: UserY.user.IDbUser,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbUserInsert(req, callOpt)
  }
  public static Update(
    req: UserY.user.IDbUser,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbUserUpdate(req, callOpt)
  }
  public static PartialUpdate(
    req: UserY.user.IDbUser,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbUserPartialUpdate(req, callOpt)
  }
  public static Delete(
    req: UserY.user.IDbUser,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbUserDelete(req, callOpt)
  }
  public static SelectOne(
    req: CrudY.crud.IDMLParam,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbUserSelectOne(req, callOpt)
  }
  public static SelectMany(
    req: CrudY.crud.DMLParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return RpcDbUserSelectMany(req, callOpt)
  }
  public static Query(
    req: CrudY.crud.QueryParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return RpcDbUserQuery(req, callOpt)
  }
  public static QueryBatch(
    req: CrudY.crud.QueryParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return RpcDbUserQueryBatch(req, callOpt)
  }
}

export function PrpcDbUserInsert(
  req: UserY.user.IDbUser,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = UserY.user.DbUser.encode(req)
  let reqData = w.finish()

  const api = '/user.PrpcDbUser/Insert'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function PrpcDbUserUpdate(
  req: UserY.user.IDbUser,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = UserY.user.DbUser.encode(req)
  let reqData = w.finish()

  const api = '/user.PrpcDbUser/Update'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function PrpcDbUserPartialUpdate(
  req: UserY.user.IDbUser,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = UserY.user.DbUser.encode(req)
  let reqData = w.finish()

  const api = '/user.PrpcDbUser/PartialUpdate'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function PrpcDbUserDelete(
  req: UserY.user.IDbUser,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = UserY.user.DbUser.encode(req)
  let reqData = w.finish()

  const api = '/user.PrpcDbUser/Delete'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function PrpcDbUserSelectOne(
  req: CrudY.crud.IDMLParam,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = CrudY.crud.DMLParam.encode(req)
  let reqData = w.finish()

  const api = '/user.PrpcDbUser/SelectOne'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, UserY.user.DbUser, callOpt)
}

export function PrpcDbUserSelectMany(
  req: CrudY.crud.DMLParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/user.PrpcDbUser/SelectMany'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.DMLParam,
    UserY.user.DbUser,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function PrpcDbUserQuery(
  req: CrudY.crud.PrivilegeParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/user.PrpcDbUser/Query'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.PrivilegeParam,
    UserY.user.DbUser,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function PrpcDbUserQueryBatch(
  req: CrudY.crud.PrivilegeParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/user.PrpcDbUser/QueryBatch'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.PrivilegeParam,
    UserListY.user.DbUserList,
    callOpt,
  )
  r.sendFirst(req)
  return r
}

export class PrpcDbUser {
  public static Insert(
    req: UserY.user.IDbUser,
    callOpt?: ICallOption,
  ): boolean {
    return PrpcDbUserInsert(req, callOpt)
  }
  public static Update(
    req: UserY.user.IDbUser,
    callOpt?: ICallOption,
  ): boolean {
    return PrpcDbUserUpdate(req, callOpt)
  }
  public static PartialUpdate(
    req: UserY.user.IDbUser,
    callOpt?: ICallOption,
  ): boolean {
    return PrpcDbUserPartialUpdate(req, callOpt)
  }
  public static Delete(
    req: UserY.user.IDbUser,
    callOpt?: ICallOption,
  ): boolean {
    return PrpcDbUserDelete(req, callOpt)
  }
  public static SelectOne(
    req: CrudY.crud.IDMLParam,
    callOpt?: ICallOption,
  ): boolean {
    return PrpcDbUserSelectOne(req, callOpt)
  }
  public static SelectMany(
    req: CrudY.crud.DMLParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return PrpcDbUserSelectMany(req, callOpt)
  }
  public static Query(
    req: CrudY.crud.PrivilegeParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return PrpcDbUserQuery(req, callOpt)
  }
  public static QueryBatch(
    req: CrudY.crud.PrivilegeParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return PrpcDbUserQueryBatch(req, callOpt)
  }
}

export function RpcDbUserRoleInsert(
  req: UserY.user.IDbUserRole,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = UserY.user.DbUserRole.encode(req)
  let reqData = w.finish()

  const api = '/user.RpcDbUserRole/Insert'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function RpcDbUserRoleUpdate(
  req: UserY.user.IDbUserRole,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = UserY.user.DbUserRole.encode(req)
  let reqData = w.finish()

  const api = '/user.RpcDbUserRole/Update'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function RpcDbUserRolePartialUpdate(
  req: UserY.user.IDbUserRole,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = UserY.user.DbUserRole.encode(req)
  let reqData = w.finish()

  const api = '/user.RpcDbUserRole/PartialUpdate'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function RpcDbUserRoleDelete(
  req: UserY.user.IDbUserRole,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = UserY.user.DbUserRole.encode(req)
  let reqData = w.finish()

  const api = '/user.RpcDbUserRole/Delete'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function RpcDbUserRoleSelectOne(
  req: CrudY.crud.IDMLParam,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = CrudY.crud.DMLParam.encode(req)
  let reqData = w.finish()

  const api = '/user.RpcDbUserRole/SelectOne'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, UserY.user.DbUserRole, callOpt)
}

export function RpcDbUserRoleSelectMany(
  req: CrudY.crud.DMLParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/user.RpcDbUserRole/SelectMany'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.DMLParam,
    UserY.user.DbUserRole,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function RpcDbUserRoleQuery(
  req: CrudY.crud.QueryParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/user.RpcDbUserRole/Query'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.QueryParam,
    UserY.user.DbUserRole,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function RpcDbUserRoleQueryBatch(
  req: CrudY.crud.QueryParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/user.RpcDbUserRole/QueryBatch'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.QueryParam,
    UserListY.user.DbUserRoleList,
    callOpt,
  )
  r.sendFirst(req)
  return r
}

export class RpcDbUserRole {
  public static Insert(
    req: UserY.user.IDbUserRole,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbUserRoleInsert(req, callOpt)
  }
  public static Update(
    req: UserY.user.IDbUserRole,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbUserRoleUpdate(req, callOpt)
  }
  public static PartialUpdate(
    req: UserY.user.IDbUserRole,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbUserRolePartialUpdate(req, callOpt)
  }
  public static Delete(
    req: UserY.user.IDbUserRole,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbUserRoleDelete(req, callOpt)
  }
  public static SelectOne(
    req: CrudY.crud.IDMLParam,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbUserRoleSelectOne(req, callOpt)
  }
  public static SelectMany(
    req: CrudY.crud.DMLParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return RpcDbUserRoleSelectMany(req, callOpt)
  }
  public static Query(
    req: CrudY.crud.QueryParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return RpcDbUserRoleQuery(req, callOpt)
  }
  public static QueryBatch(
    req: CrudY.crud.QueryParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return RpcDbUserRoleQueryBatch(req, callOpt)
  }
}

export function PrpcDbUserRoleInsert(
  req: UserY.user.IDbUserRole,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = UserY.user.DbUserRole.encode(req)
  let reqData = w.finish()

  const api = '/user.PrpcDbUserRole/Insert'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function PrpcDbUserRoleUpdate(
  req: UserY.user.IDbUserRole,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = UserY.user.DbUserRole.encode(req)
  let reqData = w.finish()

  const api = '/user.PrpcDbUserRole/Update'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function PrpcDbUserRolePartialUpdate(
  req: UserY.user.IDbUserRole,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = UserY.user.DbUserRole.encode(req)
  let reqData = w.finish()

  const api = '/user.PrpcDbUserRole/PartialUpdate'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function PrpcDbUserRoleDelete(
  req: UserY.user.IDbUserRole,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = UserY.user.DbUserRole.encode(req)
  let reqData = w.finish()

  const api = '/user.PrpcDbUserRole/Delete'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function PrpcDbUserRoleSelectOne(
  req: CrudY.crud.IDMLParam,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = CrudY.crud.DMLParam.encode(req)
  let reqData = w.finish()

  const api = '/user.PrpcDbUserRole/SelectOne'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, UserY.user.DbUserRole, callOpt)
}

export function PrpcDbUserRoleSelectMany(
  req: CrudY.crud.DMLParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/user.PrpcDbUserRole/SelectMany'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.DMLParam,
    UserY.user.DbUserRole,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function PrpcDbUserRoleQuery(
  req: CrudY.crud.PrivilegeParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/user.PrpcDbUserRole/Query'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.PrivilegeParam,
    UserY.user.DbUserRole,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function PrpcDbUserRoleQueryBatch(
  req: CrudY.crud.PrivilegeParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/user.PrpcDbUserRole/QueryBatch'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.PrivilegeParam,
    UserListY.user.DbUserRoleList,
    callOpt,
  )
  r.sendFirst(req)
  return r
}

export class PrpcDbUserRole {
  public static Insert(
    req: UserY.user.IDbUserRole,
    callOpt?: ICallOption,
  ): boolean {
    return PrpcDbUserRoleInsert(req, callOpt)
  }
  public static Update(
    req: UserY.user.IDbUserRole,
    callOpt?: ICallOption,
  ): boolean {
    return PrpcDbUserRoleUpdate(req, callOpt)
  }
  public static PartialUpdate(
    req: UserY.user.IDbUserRole,
    callOpt?: ICallOption,
  ): boolean {
    return PrpcDbUserRolePartialUpdate(req, callOpt)
  }
  public static Delete(
    req: UserY.user.IDbUserRole,
    callOpt?: ICallOption,
  ): boolean {
    return PrpcDbUserRoleDelete(req, callOpt)
  }
  public static SelectOne(
    req: CrudY.crud.IDMLParam,
    callOpt?: ICallOption,
  ): boolean {
    return PrpcDbUserRoleSelectOne(req, callOpt)
  }
  public static SelectMany(
    req: CrudY.crud.DMLParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return PrpcDbUserRoleSelectMany(req, callOpt)
  }
  public static Query(
    req: CrudY.crud.PrivilegeParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return PrpcDbUserRoleQuery(req, callOpt)
  }
  public static QueryBatch(
    req: CrudY.crud.PrivilegeParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return PrpcDbUserRoleQueryBatch(req, callOpt)
  }
}
export default {
  RpcDbUser,
  RpcDbUserInsert,
  RpcDbUserUpdate,
  RpcDbUserPartialUpdate,
  RpcDbUserDelete,
  RpcDbUserSelectOne,
  RpcDbUserSelectMany,
  RpcDbUserQuery,
  RpcDbUserQueryBatch,
  PrpcDbUser,
  PrpcDbUserInsert,
  PrpcDbUserUpdate,
  PrpcDbUserPartialUpdate,
  PrpcDbUserDelete,
  PrpcDbUserSelectOne,
  PrpcDbUserSelectMany,
  PrpcDbUserQuery,
  PrpcDbUserQueryBatch,
  RpcDbUserRole,
  RpcDbUserRoleInsert,
  RpcDbUserRoleUpdate,
  RpcDbUserRolePartialUpdate,
  RpcDbUserRoleDelete,
  RpcDbUserRoleSelectOne,
  RpcDbUserRoleSelectMany,
  RpcDbUserRoleQuery,
  RpcDbUserRoleQueryBatch,
  PrpcDbUserRole,
  PrpcDbUserRoleInsert,
  PrpcDbUserRoleUpdate,
  PrpcDbUserRolePartialUpdate,
  PrpcDbUserRoleDelete,
  PrpcDbUserRoleSelectOne,
  PrpcDbUserRoleSelectMany,
  PrpcDbUserRoleQuery,
  PrpcDbUserRoleQueryBatch,
}

import * as $protobuf from 'protobufjs'
/** Namespace config. */
export namespace config {
  /** Properties of a DbConfigList. */
  interface IDbConfigList {
    /** 批次号，从0开始 */
    BatchNo?: number | null

    /** rows offset,从0开始 */
    RowsOffset?: number | null

    /** 本批次数据 */
    Rows?: config.IDbConfig[] | null
  }

  /** DbConfig list */
  class DbConfigList implements IDbConfigList {
    /**
     * Constructs a new DbConfigList.
     * @param [properties] Properties to set
     */
    constructor(properties?: config.IDbConfigList)

    /** 批次号，从0开始 */
    public BatchNo: number

    /** rows offset,从0开始 */
    public RowsOffset: number

    /** 本批次数据 */
    public Rows: config.IDbConfig[]

    /**
     * Encodes the specified DbConfigList message. Does not implicitly {@link config.DbConfigList.verify|verify} messages.
     * @param message DbConfigList message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: config.IDbConfigList,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a DbConfigList message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns DbConfigList
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): config.DbConfigList
  }

  /** Properties of a DbConfig. */
  interface IDbConfig {
    /**
     * @db uuid primary key
     * 行ID
     */
    RID?: string | null

    /**
     * @db uuid  REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 所属的群组，如果是全系统通用的配置，此项为NULL
     */
    OrgRID?: string | null

    /**
     * @db text not null
     * 配置名
     */
    ConfKey?: string | null

    /**
     * @db text
     * 配置的值
     */
    ConfValue?: string | null

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     */
    UpdatedAt?: string | null

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */
    UpdatedDC?: string | null
  }

  /** 系统配置信息表 */
  class DbConfig implements IDbConfig {
    /**
     * Constructs a new DbConfig.
     * @rpc crud
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbConfigOrgRID on DbConfig USING hash(OrgRID)
     * @param [properties] Properties to set
     */
    constructor(properties?: config.IDbConfig)

    /**
     * @db uuid primary key
     * 行ID
     */
    public RID: string

    /**
     * @db uuid  REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 所属的群组，如果是全系统通用的配置，此项为NULL
     */
    public OrgRID: string

    /**
     * @db text not null
     * 配置名
     */
    public ConfKey: string

    /**
     * @db text
     * 配置的值
     */
    public ConfValue: string

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     */
    public UpdatedAt: string

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */
    public UpdatedDC: string

    /**
     * Encodes the specified DbConfig message. Does not implicitly {@link config.DbConfig.verify|verify} messages.
     * @param message DbConfig message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: config.IDbConfig,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a DbConfig message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns DbConfig
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): config.DbConfig
  }
}

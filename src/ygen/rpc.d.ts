import * as $protobuf from 'protobufjs'
/** Namespace rpc. */
export namespace rpc {
  /** Properties of a RpcEmpty. */
  interface IRpcEmpty {}

  /** 空消息，grpc要求必须有req,res message */
  class RpcEmpty implements IRpcEmpty {
    /**
     * Constructs a new RpcEmpty.
     * @param [properties] Properties to set
     */
    constructor(properties?: rpc.IRpcEmpty)

    /**
     * Encodes the specified RpcEmpty message. Does not implicitly {@link rpc.RpcEmpty.verify|verify} messages.
     * @param message RpcEmpty message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: rpc.IRpcEmpty,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a RpcEmpty message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns RpcEmpty
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): rpc.RpcEmpty
  }

  /** Properties of a RpcCommon. */
  interface IRpcCommon {
    /** 代码，要求各个rpc的不同，会有不同的值 */
    Code?: number | null

    /** optional system */
    System?: string | null

    /** optional session id */
    SessionID?: string | null

    /** optional Error */
    Err?: string | null

    /** optional body */
    Body?: Uint8Array | null
  }

  /** 一般消息 */
  class RpcCommon implements IRpcCommon {
    /**
     * Constructs a new RpcCommon.
     * @param [properties] Properties to set
     */
    constructor(properties?: rpc.IRpcCommon)

    /** 代码，要求各个rpc的不同，会有不同的值 */
    public Code: number

    /** optional system */
    public System: string

    /** optional session id */
    public SessionID: string

    /** optional Error */
    public Err: string

    /** optional body */
    public Body: Uint8Array

    /**
     * Encodes the specified RpcCommon message. Does not implicitly {@link rpc.RpcCommon.verify|verify} messages.
     * @param message RpcCommon message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: rpc.IRpcCommon,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a RpcCommon message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns RpcCommon
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): rpc.RpcCommon
  }
}

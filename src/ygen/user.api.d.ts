import * as $protobuf from 'protobufjs'
/** Namespace user. */
export namespace user {
  /** Properties of a ReqUserLogin. */
  interface IReqUserLogin {
    /**
     * 登录方式
     * 0：username+userpass
     * 1: session rid
     */
    LoginType?: number | null

    /** login name or session rid */
    LoginName?: string | null

    /** login pass base64(sha256(base64(sha256(username+userpass))+LoginTimeStr)) */
    LoginPass?: string | null

    /** 时间字符串，必须是3分钟内的时间(utc)，格式 yyyy-mm-dd HH:MM:SS */
    LoginTimeStr?: string | null

    /** 系统号 */
    System?: string | null
  }

  /** Represents a ReqUserLogin. */
  class ReqUserLogin implements IReqUserLogin {
    /**
     * Constructs a new ReqUserLogin.
     * @param [properties] Properties to set
     */
    constructor(properties?: user.IReqUserLogin)

    /**
     * 登录方式
     * 0：username+userpass
     * 1: session rid
     */
    public LoginType: number

    /** login name or session rid */
    public LoginName: string

    /** login pass base64(sha256(base64(sha256(username+userpass))+LoginTimeStr)) */
    public LoginPass: string

    /** 时间字符串，必须是3分钟内的时间(utc)，格式 yyyy-mm-dd HH:MM:SS */
    public LoginTimeStr: string

    /** 系统号 */
    public System: string

    /**
     * Encodes the specified ReqUserLogin message. Does not implicitly {@link user.ReqUserLogin.verify|verify} messages.
     * @param message ReqUserLogin message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: user.IReqUserLogin,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a ReqUserLogin message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns ReqUserLogin
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): user.ReqUserLogin
  }

  /** Properties of a ResUserLogin. */
  interface IResUserLogin {
    /**
     * 回应码
     * 100:ok,其它为错误
     * 1:密码错误 2: 无此sid 3:此sessionn用户已经删除 4:无此用户
     */
    Code?: number | null

    /** 错误信息,如果有的话 */
    Err?: string | null

    /** user rid */
    UserRID?: string | null

    /** user org rid */
    UserOrgRID?: string | null

    /** session rid */
    SessionRID?: string | null

    /** sys utc time yyyy-mm-dd HH:MM:SS */
    SysUTCTime?: string | null
  }

  /** Represents a ResUserLogin. */
  class ResUserLogin implements IResUserLogin {
    /**
     * Constructs a new ResUserLogin.
     * @param [properties] Properties to set
     */
    constructor(properties?: user.IResUserLogin)

    /**
     * 回应码
     * 100:ok,其它为错误
     * 1:密码错误 2: 无此sid 3:此sessionn用户已经删除 4:无此用户
     */
    public Code: number

    /** 错误信息,如果有的话 */
    public Err: string

    /** user rid */
    public UserRID: string

    /** user org rid */
    public UserOrgRID: string

    /** session rid */
    public SessionRID: string

    /** sys utc time yyyy-mm-dd HH:MM:SS */
    public SysUTCTime: string

    /**
     * Encodes the specified ResUserLogin message. Does not implicitly {@link user.ResUserLogin.verify|verify} messages.
     * @param message ResUserLogin message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: user.IResUserLogin,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a ResUserLogin message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns ResUserLogin
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): user.ResUserLogin
  }

  /** Properties of a ReqUserHasOrgPrivilege. */
  interface IReqUserHasOrgPrivilege {
    /** 系统号 */
    System?: string | null

    /** 要查询的用户RID */
    UserRID?: string | null

    /** 是不是对此群组拥有权限 */
    OrgRID?: string | null

    /** 发起者的session id */
    SessionID?: string | null
  }

  /** Represents a ReqUserHasOrgPrivilege. */
  class ReqUserHasOrgPrivilege implements IReqUserHasOrgPrivilege {
    /**
     * Constructs a new ReqUserHasOrgPrivilege.
     * @param [properties] Properties to set
     */
    constructor(properties?: user.IReqUserHasOrgPrivilege)

    /** 系统号 */
    public System: string

    /** 要查询的用户RID */
    public UserRID: string

    /** 是不是对此群组拥有权限 */
    public OrgRID: string

    /** 发起者的session id */
    public SessionID: string

    /**
     * Encodes the specified ReqUserHasOrgPrivilege message. Does not implicitly {@link user.ReqUserHasOrgPrivilege.verify|verify} messages.
     * @param message ReqUserHasOrgPrivilege message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: user.IReqUserHasOrgPrivilege,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a ReqUserHasOrgPrivilege message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns ReqUserHasOrgPrivilege
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): user.ReqUserHasOrgPrivilege
  }

  /** Properties of a ReqUserHasPermission. */
  interface IReqUserHasPermission {
    /** 系统号 */
    System?: string | null

    /** 要查询的用户RID */
    UserRID?: string | null

    /** ReqUserHasPermission PermissionType */
    PermissionType?: string | null

    /** ReqUserHasPermission PermissionValue */
    PermissionValue?: string | null

    /** 发起者的session id */
    SessionID?: string | null
  }

  /** Represents a ReqUserHasPermission. */
  class ReqUserHasPermission implements IReqUserHasPermission {
    /**
     * Constructs a new ReqUserHasPermission.
     * @param [properties] Properties to set
     */
    constructor(properties?: user.IReqUserHasPermission)

    /** 系统号 */
    public System: string

    /** 要查询的用户RID */
    public UserRID: string

    /** ReqUserHasPermission PermissionType. */
    public PermissionType: string

    /** ReqUserHasPermission PermissionValue. */
    public PermissionValue: string

    /** 发起者的session id */
    public SessionID: string

    /**
     * Encodes the specified ReqUserHasPermission message. Does not implicitly {@link user.ReqUserHasPermission.verify|verify} messages.
     * @param message ReqUserHasPermission message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: user.IReqUserHasPermission,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a ReqUserHasPermission message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns ReqUserHasPermission
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): user.ReqUserHasPermission
  }

  /** Represents a RpcUser */
  class RpcUser extends $protobuf.rpc.Service {
    /**
     * Constructs a new RpcUser service.
     * @param rpcImpl RPC implementation
     * @param [requestDelimited=false] Whether requests are length-delimited
     * @param [responseDelimited=false] Whether responses are length-delimited
     */
    constructor(
      rpcImpl: $protobuf.RPCImpl,
      requestDelimited?: boolean,
      responseDelimited?: boolean,
    )

    /**
     * 登录系统
     * @param request ReqUserLogin message or plain object
     * @param callback Node-style callback called with the error, if any, and ResUserLogin
     */
    public login(
      request: user.IReqUserLogin,
      callback: user.RpcUser.LoginCallback,
    ): void

    /**
     * 登录系统
     * @param request ReqUserLogin message or plain object
     * @returns Promise
     */
    public login(request: user.IReqUserLogin): Promise<user.ResUserLogin>

    /**
     * 查询用户对相应的群组是否有权限
     * 返回值RpcCommon.Code=100为拥有权限，其它为不拥有权限
     * @param request ReqUserHasOrgPrivilege message or plain object
     * @param callback Node-style callback called with the error, if any, and RpcCommon
     */
    public isUserHasOrgPrivilege(
      request: user.IReqUserHasOrgPrivilege,
      callback: user.RpcUser.IsUserHasOrgPrivilegeCallback,
    ): void

    /**
     * 查询用户对相应的群组是否有权限
     * 返回值RpcCommon.Code=100为拥有权限，其它为不拥有权限
     * @param request ReqUserHasOrgPrivilege message or plain object
     * @returns Promise
     */
    public isUserHasOrgPrivilege(
      request: user.IReqUserHasOrgPrivilege,
    ): Promise<rpc.RpcCommon>

    /**
     * 取得有权限的org rid列表
     * RpcCommon.Err = userrid
     * @param request RpcCommon message or plain object
     * @param callback Node-style callback called with the error, if any, and DbOrg
     */
    public myOrgRID(
      request: rpc.IRpcCommon,
      callback: user.RpcUser.MyOrgRIDCallback,
    ): void

    /**
     * 取得有权限的org rid列表
     * RpcCommon.Err = userrid
     * @param request RpcCommon message or plain object
     * @returns Promise
     */
    public myOrgRID(request: rpc.IRpcCommon): Promise<org.DbOrg>

    /**
     * 取得用户的权限列表
     * RpcCommon.Err = userrid
     * @param request RpcCommon message or plain object
     * @param callback Node-style callback called with the error, if any, and DbPermission
     */
    public myPermission(
      request: rpc.IRpcCommon,
      callback: user.RpcUser.MyPermissionCallback,
    ): void

    /**
     * 取得用户的权限列表
     * RpcCommon.Err = userrid
     * @param request RpcCommon message or plain object
     * @returns Promise
     */
    public myPermission(request: rpc.IRpcCommon): Promise<user.DbPermission>

    /**
     * 取得用户的角色列表
     * RpcCommon.Err = userrid
     * @param request RpcCommon message or plain object
     * @param callback Node-style callback called with the error, if any, and DbRole
     */
    public myRole(
      request: rpc.IRpcCommon,
      callback: user.RpcUser.MyRoleCallback,
    ): void

    /**
     * 取得用户的角色列表
     * RpcCommon.Err = userrid
     * @param request RpcCommon message or plain object
     * @returns Promise
     */
    public myRole(request: rpc.IRpcCommon): Promise<user.DbRole>

    /**
     * 查询用户是否有特定的权限
     * 返回值RpcCommon.Code=100为拥有权限，其它为不拥有权限
     * @param request ReqUserHasPermission message or plain object
     * @param callback Node-style callback called with the error, if any, and RpcCommon
     */
    public isUserHasPermission(
      request: user.IReqUserHasPermission,
      callback: user.RpcUser.IsUserHasPermissionCallback,
    ): void

    /**
     * 查询用户是否有特定的权限
     * 返回值RpcCommon.Code=100为拥有权限，其它为不拥有权限
     * @param request ReqUserHasPermission message or plain object
     * @returns Promise
     */
    public isUserHasPermission(
      request: user.IReqUserHasPermission,
    ): Promise<rpc.RpcCommon>

    /**
     * 修改用户自己的配置信息
     * 以metadata.DMLParam.keyColumn为要修改的目标列
     * @param request DbUser message or plain object
     * @param callback Node-style callback called with the error, if any, and DMLResult
     */
    public updateMySetting(
      request: user.IDbUser,
      callback: user.RpcUser.UpdateMySettingCallback,
    ): void

    /**
     * 修改用户自己的配置信息
     * 以metadata.DMLParam.keyColumn为要修改的目标列
     * @param request DbUser message or plain object
     * @returns Promise
     */
    public updateMySetting(request: user.IDbUser): Promise<crud.DMLResult>

    /**
     * 获取系统当前utc时间
     * 系统时间在RpcCommon.System=yyyy-mm-dd HH:MM:SS
     * @param request RpcCommon message or plain object
     * @param callback Node-style callback called with the error, if any, and RpcCommon
     */
    public sysUTCTime(
      request: rpc.IRpcCommon,
      callback: user.RpcUser.SysUTCTimeCallback,
    ): void

    /**
     * 获取系统当前utc时间
     * 系统时间在RpcCommon.System=yyyy-mm-dd HH:MM:SS
     * @param request RpcCommon message or plain object
     * @returns Promise
     */
    public sysUTCTime(request: rpc.IRpcCommon): Promise<rpc.RpcCommon>
  }

  namespace RpcUser {
    /**
     * Callback as used by {@link user.RpcUser#login}.
     * @param error Error, if any
     * @param [response] ResUserLogin
     */
    type LoginCallback = (
      error: Error | null,
      response?: user.ResUserLogin,
    ) => void

    /**
     * Callback as used by {@link user.RpcUser#isUserHasOrgPrivilege}.
     * @param error Error, if any
     * @param [response] RpcCommon
     */
    type IsUserHasOrgPrivilegeCallback = (
      error: Error | null,
      response?: rpc.RpcCommon,
    ) => void

    /**
     * Callback as used by {@link user.RpcUser#myOrgRID}.
     * @param error Error, if any
     * @param [response] DbOrg
     */
    type MyOrgRIDCallback = (error: Error | null, response?: org.DbOrg) => void

    /**
     * Callback as used by {@link user.RpcUser#myPermission}.
     * @param error Error, if any
     * @param [response] DbPermission
     */
    type MyPermissionCallback = (
      error: Error | null,
      response?: user.DbPermission,
    ) => void

    /**
     * Callback as used by {@link user.RpcUser#myRole}.
     * @param error Error, if any
     * @param [response] DbRole
     */
    type MyRoleCallback = (error: Error | null, response?: user.DbRole) => void

    /**
     * Callback as used by {@link user.RpcUser#isUserHasPermission}.
     * @param error Error, if any
     * @param [response] RpcCommon
     */
    type IsUserHasPermissionCallback = (
      error: Error | null,
      response?: rpc.RpcCommon,
    ) => void

    /**
     * Callback as used by {@link user.RpcUser#updateMySetting}.
     * @param error Error, if any
     * @param [response] DMLResult
     */
    type UpdateMySettingCallback = (
      error: Error | null,
      response?: crud.DMLResult,
    ) => void

    /**
     * Callback as used by {@link user.RpcUser#sysUTCTime}.
     * @param error Error, if any
     * @param [response] RpcCommon
     */
    type SysUTCTimeCallback = (
      error: Error | null,
      response?: rpc.RpcCommon,
    ) => void
  }

  /** Properties of a DbPermission. */
  interface IDbPermission {
    /**
     * @db uuid primary key
     * 行ID,permission rid
     */
    RID?: string | null

    /**
     * @db text not null
     * 权限类别
     */
    PermissionType?: string | null

    /**
     * @db text not null
     * permission 名称
     */
    PermissionName?: string | null

    /**
     * @db text not null
     * permission value
     */
    PermissionValue?: string | null

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     */
    UpdatedAt?: string | null

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */
    UpdatedDC?: string | null
  }

  /** 所有的权限信息表,此表信息为预置，一般不给删除 */
  class DbPermission implements IDbPermission {
    /**
     * Constructs a new DbPermission.
     * @rpc crud
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbPermissionPermissionValue on DbPermission USING hash(PermissionValue);
     * @param [properties] Properties to set
     */
    constructor(properties?: user.IDbPermission)

    /**
     * @db uuid primary key
     * 行ID,permission rid
     */
    public RID: string

    /**
     * @db text not null
     * 权限类别
     */
    public PermissionType: string

    /**
     * @db text not null
     * permission 名称
     */
    public PermissionName: string

    /**
     * @db text not null
     * permission value
     */
    public PermissionValue: string

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     */
    public UpdatedAt: string

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */
    public UpdatedDC: string

    /**
     * Encodes the specified DbPermission message. Does not implicitly {@link user.DbPermission.verify|verify} messages.
     * @param message DbPermission message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: user.IDbPermission,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a DbPermission message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns DbPermission
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): user.DbPermission
  }

  /** Properties of a DbRole. */
  interface IDbRole {
    /**
     * @db uuid primary key
     * 行ID,role rid
     */
    RID?: string | null

    /**
     * @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 用户所属的群组
     */
    OrgRID?: string | null

    /**
     * @db text not null
     * role 名称
     */
    RoleName?: string | null

    /**
     * @db uuid --REFERENCES DbUser(RID) ON DELETE set null
     * role 创建者 rid
     */
    Creator?: string | null

    /**
     * @db int
     * 是否是内置的角色，内置角色不能删除
     */
    IsBuiltIn?: number | null

    /**
     * @db jsonb not null default  '{}'::jsonb
     * 其它设置，可以在里面扩展需要用到的其它信息
     */
    Setting?: string | null

    /**
     * @db int default 100
     * sort value
     */
    SortValue?: number | null

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     */
    UpdatedAt?: string | null

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */
    UpdatedDC?: string | null
  }

  /** 所有的角色信息表 */
  class DbRole implements IDbRole {
    /**
     * Constructs a new DbRole.
     * @rpc crud pcrud
     * @dbpost insert into DbRole (rid, orgrid, rolename, creator, isbuiltin, setting, sortvalue, updatedat, updateddc) VALUES('55555555-5555-5555-5555-555555555555', '00000000-0000-0000-0000-000000000000', 'base', null, 1, '{}'::jsonb, 0, now_utc(), '') ON CONFLICT  DO NOTHING;
     * @dbpost insert into DbRole (rid, orgrid, rolename, creator, isbuiltin, setting, sortvalue, updatedat, updateddc) VALUES('00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', 'admin', null, 1, '{}'::jsonb, 0, now_utc(), '') ON CONFLICT  DO NOTHING;
     * @param [properties] Properties to set
     */
    constructor(properties?: user.IDbRole)

    /**
     * @db uuid primary key
     * 行ID,role rid
     */
    public RID: string

    /**
     * @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 用户所属的群组
     */
    public OrgRID: string

    /**
     * @db text not null
     * role 名称
     */
    public RoleName: string

    /**
     * @db uuid --REFERENCES DbUser(RID) ON DELETE set null
     * role 创建者 rid
     */
    public Creator: string

    /**
     * @db int
     * 是否是内置的角色，内置角色不能删除
     */
    public IsBuiltIn: number

    /**
     * @db jsonb not null default  '{}'::jsonb
     * 其它设置，可以在里面扩展需要用到的其它信息
     */
    public Setting: string

    /**
     * @db int default 100
     * sort value
     */
    public SortValue: number

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     */
    public UpdatedAt: string

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */
    public UpdatedDC: string

    /**
     * Encodes the specified DbRole message. Does not implicitly {@link user.DbRole.verify|verify} messages.
     * @param message DbRole message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: user.IDbRole,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a DbRole message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns DbRole
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): user.DbRole
  }

  /** Properties of a DbRolePermission. */
  interface IDbRolePermission {
    /**
     * @db uuid primary key
     * 行ID
     */
    RID?: string | null

    /**
     * @db uuid not null REFERENCES DbRole(RID) ON DELETE CASCADE
     * role rid
     */
    RoleRID?: string | null

    /**
     * @db uuid not null REFERENCES DbPermission(RID) ON DELETE CASCADE
     * permission rid
     */
    PermissionRID?: string | null

    /**
     * @db uuid --REFERENCES DbUser(RID) ON DELETE set null
     * role 创建者 rid
     */
    Creator?: string | null

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     */
    UpdatedAt?: string | null

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */
    UpdatedDC?: string | null
  }

  /** 角色权限信息表 */
  class DbRolePermission implements IDbRolePermission {
    /**
     * Constructs a new DbRolePermission.
     * @rpc crud pcrud
     * @param [properties] Properties to set
     */
    constructor(properties?: user.IDbRolePermission)

    /**
     * @db uuid primary key
     * 行ID
     */
    public RID: string

    /**
     * @db uuid not null REFERENCES DbRole(RID) ON DELETE CASCADE
     * role rid
     */
    public RoleRID: string

    /**
     * @db uuid not null REFERENCES DbPermission(RID) ON DELETE CASCADE
     * permission rid
     */
    public PermissionRID: string

    /**
     * @db uuid --REFERENCES DbUser(RID) ON DELETE set null
     * role 创建者 rid
     */
    public Creator: string

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     */
    public UpdatedAt: string

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */
    public UpdatedDC: string

    /**
     * Encodes the specified DbRolePermission message. Does not implicitly {@link user.DbRolePermission.verify|verify} messages.
     * @param message DbRolePermission message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: user.IDbRolePermission,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a DbRolePermission message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns DbRolePermission
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): user.DbRolePermission
  }

  /** Properties of a DbUser. */
  interface IDbUser {
    /**
     * @db uuid primary key
     * 行ID
     */
    RID?: string | null

    /**
     * @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 用户所属的群组
     */
    OrgRID?: string | null

    /**
     * @db varchar(16) not null unique
     * 用户编号
     */
    UserID?: string | null

    /**
     * @db int
     * 用户类型
     */
    UserType?: number | null

    /**
     * @db varchar(32) not null
     * 用户名
     */
    UserName?: string | null

    /**
     * @db text
     * 备注
     */
    Note?: string | null

    /**
     * @db text
     * 用户电话
     */
    Phone?: string | null

    /**
     * @db uuid REFERENCES DbImage(RID) ON DELETE SET NULL
     * 用户图片
     */
    Image?: string | null

    /**
     * @db jsonb not null default  '{}'::jsonb
     * 其它设置，可以在里面扩展需要用到的其它信息
     */
    Setting?: string | null

    /**
     * @db varchar(32) unique
     * 用户登录名,NULL(空)表示不能登录系统
     */
    LoginName?: string | null

    /**
     * @db text default ''
     * 用户登录密码,为base64(sha256(user_name+user_password)),系统不保存真实的密码
     */
    LoginPass?: string | null

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     */
    UpdatedAt?: string | null

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */
    UpdatedDC?: string | null
  }

  /** 用户表 */
  class DbUser implements IDbUser {
    /**
     * Constructs a new DbUser.
     * @rpc crud pcrud
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbUserOrgRID on DbUser USING hash(OrgRID);
     * @dbpost INSERT INTO dbuser(rid, orgrid, userid, usertype, username, phone, image, setting, loginname, loginpass, updatedat, updateddc)VALUES('00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', 'ttt', 0, 'ttt', '', NULL, '{}'::jsonb, 'ttt', 'UwhpDj0xMGNzZw8sXYZOLGBHmH/q9Wxf7aB6/EbT43Q=', now_utc(), '') ON CONFLICT  DO NOTHING;
     * @param [properties] Properties to set
     */
    constructor(properties?: user.IDbUser)

    /**
     * @db uuid primary key
     * 行ID
     */
    public RID: string

    /**
     * @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 用户所属的群组
     */
    public OrgRID: string

    /**
     * @db varchar(16) not null unique
     * 用户编号
     */
    public UserID: string

    /**
     * @db int
     * 用户类型
     */
    public UserType: number

    /**
     * @db varchar(32) not null
     * 用户名
     */
    public UserName: string

    /**
     * @db text
     * 备注
     */
    public Note: string

    /**
     * @db text
     * 用户电话
     */
    public Phone: string

    /**
     * @db uuid REFERENCES DbImage(RID) ON DELETE SET NULL
     * 用户图片
     */
    public Image: string

    /**
     * @db jsonb not null default  '{}'::jsonb
     * 其它设置，可以在里面扩展需要用到的其它信息
     */
    public Setting: string

    /**
     * @db varchar(32) unique
     * 用户登录名,NULL(空)表示不能登录系统
     */
    public LoginName: string

    /**
     * @db text default ''
     * 用户登录密码,为base64(sha256(user_name+user_password)),系统不保存真实的密码
     */
    public LoginPass: string

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     */
    public UpdatedAt: string

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */
    public UpdatedDC: string

    /**
     * Encodes the specified DbUser message. Does not implicitly {@link user.DbUser.verify|verify} messages.
     * @param message DbUser message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: user.IDbUser,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a DbUser message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns DbUser
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): user.DbUser
  }

  /** Properties of a DbUserRole. */
  interface IDbUserRole {
    /**
     * @db uuid primary key
     * 行ID
     */
    RID?: string | null

    /**
     * @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 用户所属的群组
     */
    OrgRID?: string | null

    /**
     * @db uuid not null REFERENCES DbUser(RID) ON DELETE CASCADE
     * 用户rid
     */
    UserRID?: string | null

    /**
     * @db uuid not null REFERENCES DbRole(RID) ON DELETE CASCADE
     * role rid
     */
    RoleRID?: string | null

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     */
    UpdatedAt?: string | null

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */
    UpdatedDC?: string | null
  }

  /**
   * 用户角色数据表
   * 一个用户可以有多个角色
   */
  class DbUserRole implements IDbUserRole {
    /**
     * Constructs a new DbUserRole.
     * @rpc crud pcrud
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbUserRoleUserRID on DbUserRole USING hash(UserRID);
     * @param [properties] Properties to set
     */
    constructor(properties?: user.IDbUserRole)

    /**
     * @db uuid primary key
     * 行ID
     */
    public RID: string

    /**
     * @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 用户所属的群组
     */
    public OrgRID: string

    /**
     * @db uuid not null REFERENCES DbUser(RID) ON DELETE CASCADE
     * 用户rid
     */
    public UserRID: string

    /**
     * @db uuid not null REFERENCES DbRole(RID) ON DELETE CASCADE
     * role rid
     */
    public RoleRID: string

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     */
    public UpdatedAt: string

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */
    public UpdatedDC: string

    /**
     * Encodes the specified DbUserRole message. Does not implicitly {@link user.DbUserRole.verify|verify} messages.
     * @param message DbUserRole message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: user.IDbUserRole,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a DbUserRole message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns DbUserRole
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): user.DbUserRole
  }
}

/** Namespace org. */
export namespace org {
  /** Properties of a DbOrg. */
  interface IDbOrg {
    /**
     * @db uuid primary key
     * 行ID
     */
    RID?: string | null

    /**
     * @db varchar(64) unique not null
     * 组织机构自编号
     */
    OrgID?: string | null

    /**
     * @db int default 0
     */
    OrgType?: number | null

    /**
     * @db int default 100
     * 排序值,由用户指定,不用管拼音或者其它语种的排序
     */
    SortValue?: number | null

    /**
     * @db varchar(32) not null
     * 机构名称,缩写
     */
    ShortName?: string | null

    /**
     * @db varchar(256)
     * 机构名称,全称
     */
    FullName?: string | null

    /**
     * @db text
     * 机构描述/备注信息
     */
    Note?: string | null

    /**
     * @db jsonb default  '{}'::jsonb
     * 其它设置，可以在里面扩展需要用到的其它信息
     */
    Setting?: string | null

    /**
     * @db uuid
     * 创建者的rid
     */
    CreatorRID?: string | null

    /**
     * @db uuid
     * 此组织的上级机构
     */
    OrgRID?: string | null

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     */
    UpdatedAt?: string | null

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */
    UpdatedDC?: string | null
  }

  /** 群组/组织结构表 */
  class DbOrg implements IDbOrg {
    /**
     * Constructs a new DbOrg.
     * @rpc crud pcrud
     * @dbpost DO $$ BEGIN BEGIN
     * @dbpost ALTER TABLE DbOrg ADD CONSTRAINT fkDbOrgParentRID  FOREIGN KEY (OrgRID) REFERENCES DbOrg(RID);
     * @dbpost EXCEPTION WHEN duplicate_object THEN --do nothing
     * @dbpost END; END $$;
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbOrgCreatorRID on DbOrg USING hash(CreatorRID);
     * @dbpost INSERT INTO dborg(rid, orgid, orgtype, sortvalue, shortname, fullname, note, setting, OrgRID, updatedat, updateddc)VALUES('00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', 0, 100, 'root', 'root', '', '{}'::jsonb, NULL, now_utc(), '')  ON CONFLICT  DO NOTHING;
     * @param [properties] Properties to set
     */
    constructor(properties?: org.IDbOrg)

    /**
     * @db uuid primary key
     * 行ID
     */
    public RID: string

    /**
     * @db varchar(64) unique not null
     * 组织机构自编号
     */
    public OrgID: string

    /**
     * @db int default 0
     */
    public OrgType: number

    /**
     * @db int default 100
     * 排序值,由用户指定,不用管拼音或者其它语种的排序
     */
    public SortValue: number

    /**
     * @db varchar(32) not null
     * 机构名称,缩写
     */
    public ShortName: string

    /**
     * @db varchar(256)
     * 机构名称,全称
     */
    public FullName: string

    /**
     * @db text
     * 机构描述/备注信息
     */
    public Note: string

    /**
     * @db jsonb default  '{}'::jsonb
     * 其它设置，可以在里面扩展需要用到的其它信息
     */
    public Setting: string

    /**
     * @db uuid
     * 创建者的rid
     */
    public CreatorRID: string

    /**
     * @db uuid
     * 此组织的上级机构
     */
    public OrgRID: string

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     */
    public UpdatedAt: string

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */
    public UpdatedDC: string

    /**
     * Encodes the specified DbOrg message. Does not implicitly {@link org.DbOrg.verify|verify} messages.
     * @param message DbOrg message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: org.IDbOrg,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a DbOrg message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns DbOrg
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): org.DbOrg
  }
}

/** Namespace rpc. */
export namespace rpc {
  /** Properties of a RpcEmpty. */
  interface IRpcEmpty {}

  /** 空消息，grpc要求必须有req,res message */
  class RpcEmpty implements IRpcEmpty {
    /**
     * Constructs a new RpcEmpty.
     * @param [properties] Properties to set
     */
    constructor(properties?: rpc.IRpcEmpty)

    /**
     * Encodes the specified RpcEmpty message. Does not implicitly {@link rpc.RpcEmpty.verify|verify} messages.
     * @param message RpcEmpty message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: rpc.IRpcEmpty,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a RpcEmpty message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns RpcEmpty
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): rpc.RpcEmpty
  }

  /** Properties of a RpcCommon. */
  interface IRpcCommon {
    /** 代码，要求各个rpc的不同，会有不同的值 */
    Code?: number | null

    /** optional system */
    System?: string | null

    /** optional session id */
    SessionID?: string | null

    /** optional Error */
    Err?: string | null

    /** optional body */
    Body?: Uint8Array | null
  }

  /** 一般消息 */
  class RpcCommon implements IRpcCommon {
    /**
     * Constructs a new RpcCommon.
     * @param [properties] Properties to set
     */
    constructor(properties?: rpc.IRpcCommon)

    /** 代码，要求各个rpc的不同，会有不同的值 */
    public Code: number

    /** optional system */
    public System: string

    /** optional session id */
    public SessionID: string

    /** optional Error */
    public Err: string

    /** optional body */
    public Body: Uint8Array

    /**
     * Encodes the specified RpcCommon message. Does not implicitly {@link rpc.RpcCommon.verify|verify} messages.
     * @param message RpcCommon message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: rpc.IRpcCommon,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a RpcCommon message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns RpcCommon
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): rpc.RpcCommon
  }
}

/** Namespace crud. */
export namespace crud {
  /** Properties of a DMLResult. */
  interface IDMLResult {
    /** 影响的行数，如果成功>0,否则=0 */
    AffectedRow?: number | null

    /** 错误信息，如果有的话 */
    errInfo?: string | null

    /** 附加信息,json格式 */
    Note?: string | null
  }

  /** CRUD DML结果 */
  class DMLResult implements IDMLResult {
    /**
     * Constructs a new DMLResult.
     * @param [properties] Properties to set
     */
    constructor(properties?: crud.IDMLResult)

    /** 影响的行数，如果成功>0,否则=0 */
    public AffectedRow: number

    /** 错误信息，如果有的话 */
    public errInfo: string

    /** 附加信息,json格式 */
    public Note: string

    /**
     * Encodes the specified DMLResult message. Does not implicitly {@link crud.DMLResult.verify|verify} messages.
     * @param message DMLResult message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: crud.IDMLResult,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a DMLResult message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns DMLResult
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): crud.DMLResult
  }

  /** Properties of a DMLParam. */
  interface IDMLParam {
    /** 条件字段名 */
    KeyColumn?: string[] | null

    /** 结果字段名 */
    ResultColumn?: string[] | null

    /** 条件值 */
    KeyValue?: string[] | null
  }

  /** 简单CRUD里面用到的条件和结果字段列表 */
  class DMLParam implements IDMLParam {
    /**
     * Constructs a new DMLParam.
     * @param [properties] Properties to set
     */
    constructor(properties?: crud.IDMLParam)

    /** 条件字段名 */
    public KeyColumn: string[]

    /** 结果字段名 */
    public ResultColumn: string[]

    /** 条件值 */
    public KeyValue: string[]

    /**
     * Encodes the specified DMLParam message. Does not implicitly {@link crud.DMLParam.verify|verify} messages.
     * @param message DMLParam message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: crud.IDMLParam,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a DMLParam message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns DMLParam
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): crud.DMLParam
  }

  /** Properties of a WhereItem. */
  interface IWhereItem {
    /** fieldname */
    Field?: string | null

    /** field compare operator */
    FieldCompareOperator?: string | null

    /** Field value */
    FieldValue?: string | null
  }

  /** sql where额外条件项 */
  class WhereItem implements IWhereItem {
    /**
     * Constructs a new WhereItem.
     * @param [properties] Properties to set
     */
    constructor(properties?: crud.IWhereItem)

    /** fieldname */
    public Field: string

    /** field compare operator */
    public FieldCompareOperator: string

    /** Field value */
    public FieldValue: string

    /**
     * Encodes the specified WhereItem message. Does not implicitly {@link crud.WhereItem.verify|verify} messages.
     * @param message WhereItem message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: crud.IWhereItem,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a WhereItem message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns WhereItem
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): crud.WhereItem
  }

  /** Properties of a QueryParam. */
  interface IQueryParam {
    /** 想要的结果字段名，不填写为全要 */
    ResultColumn?: string[] | null

    /**
     * Query时需要处理时区的字段,单数为字段名，双数为需要加减的的分钟数
     * 需要此功能必须把所有的结果列名填写在ResultColumn，否则无法处理
     * TimeColumn = ['ActionTime','480'，‘RecvTime','-60']
     */
    TimeColumn?: string[] | null

    /** where额外条件项,只支持 and */
    Where?: crud.IWhereItem[] | null

    /** 分页，需要的数据，如果Limit=0 and Offset=0,则视为不需要分页 */
    Limit?: number | null

    /**
     * 分页，从什么位置开始，如果Limit=0 and
     * Offset!=0,为从Offset的位置开始的所有数据
     */
    Offset?: number | null

    /**
     * 排序条件，每项为sql的order by 条件
     * select * from a order by col1 asc,col2 desc
     * OrderBy=["col1 asc","col2 desc"]
     */
    OrderBy?: string[] | null

    /** QueryBatch调用时,指定一次返回的数据量，不指定时后端按每次一个返回 */
    Batch?: number | null
  }

  /** 查询条件 */
  class QueryParam implements IQueryParam {
    /**
     * Constructs a new QueryParam.
     * @param [properties] Properties to set
     */
    constructor(properties?: crud.IQueryParam)

    /** 想要的结果字段名，不填写为全要 */
    public ResultColumn: string[]

    /**
     * Query时需要处理时区的字段,单数为字段名，双数为需要加减的的分钟数
     * 需要此功能必须把所有的结果列名填写在ResultColumn，否则无法处理
     * TimeColumn = ['ActionTime','480'，‘RecvTime','-60']
     */
    public TimeColumn: string[]

    /** where额外条件项,只支持 and */
    public Where: crud.IWhereItem[]

    /** 分页，需要的数据，如果Limit=0 and Offset=0,则视为不需要分页 */
    public Limit: number

    /**
     * 分页，从什么位置开始，如果Limit=0 and
     * Offset!=0,为从Offset的位置开始的所有数据
     */
    public Offset: number

    /**
     * 排序条件，每项为sql的order by 条件
     * select * from a order by col1 asc,col2 desc
     * OrderBy=["col1 asc","col2 desc"]
     */
    public OrderBy: string[]

    /** QueryBatch调用时,指定一次返回的数据量，不指定时后端按每次一个返回 */
    public Batch: number

    /**
     * Encodes the specified QueryParam message. Does not implicitly {@link crud.QueryParam.verify|verify} messages.
     * @param message QueryParam message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: crud.IQueryParam,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a QueryParam message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns QueryParam
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): crud.QueryParam
  }

  /** Properties of a PrivilegeParam. */
  interface IPrivilegeParam {
    /** 系统名称 */
    System?: string | null

    /** SessionID */
    SessionID?: string | null

    /** 查询条件 */
    QueryCondition?: crud.IQueryParam | null
  }

  /** 取得用户有权限的数据 */
  class PrivilegeParam implements IPrivilegeParam {
    /**
     * Constructs a new PrivilegeParam.
     * @param [properties] Properties to set
     */
    constructor(properties?: crud.IPrivilegeParam)

    /** 系统名称 */
    public System: string

    /** SessionID */
    public SessionID: string

    /** 查询条件 */
    public QueryCondition?: crud.IQueryParam | null

    /**
     * Encodes the specified PrivilegeParam message. Does not implicitly {@link crud.PrivilegeParam.verify|verify} messages.
     * @param message PrivilegeParam message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: crud.IPrivilegeParam,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a PrivilegeParam message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns PrivilegeParam
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): crud.PrivilegeParam
  }
}

import * as $protobuf from 'protobufjs'
/** Namespace bysdb. */
export namespace bysdb {
  /** Properties of a DbController. */
  interface IDbController {
    /**
     * @db uuid primary key
     * 行ID
     */
    RID?: string | null

    /**
     * @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 控制器所属的群组
     */
    OrgRID?: string | null

    /**
     * @db varchar(16) not null unique
     * controller编号
     */
    ControllerNo?: string | null

    /**
     * @db text
     * controller描述信息
     */
    ControllerDescription?: string | null

    /**
     * @db int
     * 控制器类型 1:中继控制器  2:基站控制器
     */
    ControllerType?: number | null

    /**
     * @db uuid
     * 上级控制器RID, 可以没有，中继控制器一般都有
     */
    ParentRID?: string | null

    /**
     * @db double precision
     * 经度
     */
    Lon?: number | null

    /**
     * @db double precision
     * 纬度
     */
    Lat?: number | null

    /**
     * @db jsonb not null default  '{}'::jsonb
     * 其它设置，可以在里面扩展需要用到的其它信息
     */
    Setting?: string | null

    /**
     * @db int not null unique
     * 控制器硬件ID，int32 > 0
     */
    ControllerHWID?: number | null

    /**
     * @db int not null default 1
     * 控制器可用信道数，中继控制器为1,基站按实际情况
     */
    ChannelCount?: number | null

    /**
     * @db int default 12
     * 地图开始显示级别
     */
    MapShowLevel?: number | null

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     */
    UpdatedAt?: string | null

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */
    UpdatedDC?: string | null

    /**
     * @db int
     * 默认通讯方式， 1:有线网络， 2:4g网络 3:无线FSK
     */
    DefaultNetworkType?: number | null

    /**
     * @db int
     * 中继对应的上级控制器的通道
     */
    ParentChannelNo?: number | null
  }

  /** 控制器信息表 */
  class DbController implements IDbController {
    /**
     * Constructs a new DbController.
     * @rpc crud pcrud
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbControllerOrgRID on DbController USING hash(OrgRID);
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbControllerParentRID on DbController USING hash(ParentRID);
     * @dbpost CREATE UNIQUE INDEX IF NOT EXISTS uidxDbControllerParent ON DbController (ParentRID, ParentChannelNo) WHERE ControllerType=1;
     * @param [properties] Properties to set
     */
    constructor(properties?: bysdb.IDbController)

    /**
     * @db uuid primary key
     * 行ID
     */
    public RID: string

    /**
     * @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 控制器所属的群组
     */
    public OrgRID: string

    /**
     * @db varchar(16) not null unique
     * controller编号
     */
    public ControllerNo: string

    /**
     * @db text
     * controller描述信息
     */
    public ControllerDescription: string

    /**
     * @db int
     * 控制器类型 1:中继控制器  2:基站控制器
     */
    public ControllerType: number

    /**
     * @db uuid
     * 上级控制器RID, 可以没有，中继控制器一般都有
     */
    public ParentRID: string

    /**
     * @db double precision
     * 经度
     */
    public Lon: number

    /**
     * @db double precision
     * 纬度
     */
    public Lat: number

    /**
     * @db jsonb not null default  '{}'::jsonb
     * 其它设置，可以在里面扩展需要用到的其它信息
     */
    public Setting: string

    /**
     * @db int not null unique
     * 控制器硬件ID，int32 > 0
     */
    public ControllerHWID: number

    /**
     * @db int not null default 1
     * 控制器可用信道数，中继控制器为1,基站按实际情况
     */
    public ChannelCount: number

    /**
     * @db int default 12
     * 地图开始显示级别
     */
    public MapShowLevel: number

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     */
    public UpdatedAt: string

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */
    public UpdatedDC: string

    /**
     * @db int
     * 默认通讯方式， 1:有线网络， 2:4g网络 3:无线FSK
     */
    public DefaultNetworkType: number

    /**
     * @db int
     * 中继对应的上级控制器的通道
     */
    public ParentChannelNo: number

    /**
     * Encodes the specified DbController message. Does not implicitly {@link bysdb.DbController.verify|verify} messages.
     * @param message DbController message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: bysdb.IDbController,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a DbController message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns DbController
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): bysdb.DbController
  }

  /** Properties of a DbBysMarker. */
  interface IDbBysMarker {
    /**
     * @db uuid primary key
     * 行ID
     */
    RID?: string | null

    /**
     * @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 界桩所属的群组
     */
    OrgRID?: string | null

    /**
     * @db varchar(16) not null unique
     * 界桩编号
     */
    MarkerNo?: string | null

    /**
     * @db text
     * 界桩描述信息
     */
    MarkerDescription?: string | null

    /**
     * @db uuid REFERENCES DbController(RID) ON DELETE RESTRICT
     * 所属控制器RID
     */
    ControllerRID?: string | null

    /**
     * @db int not null
     * 所属的基站通道，如果所属的控制器为中继控制器，此值固定为1
     */
    ControllerChannel?: number | null

    /**
     * @db double precision
     * 经度
     */
    Lon?: number | null

    /**
     * @db double precision
     * 纬度
     */
    Lat?: number | null

    /**
     * @db int not null unique
     * 界桩硬件ID,范围
     */
    MarkerHWID?: number | null

    /**
     * @db jsonb not null default  '{}'::jsonb
     * 其它设置，可以在里面扩展需要用到的其它信息
     * 新的红线图规则属性为：redLine，不存在该字段为默认连接，空数组为不连接，
     * 数组元素为界桩硬件ID，每个都连接
     */
    Setting?: string | null

    /**
     * @db int
     * 界桩型号 1:长型 X*Y*Z cm, 2:短型 x*y*z cm
     */
    MarkerModel?: number | null

    /**
     * @db int default 12
     * 地图开始显示级别
     */
    MapShowLevel?: number | null

    /**
     * @db int not null
     * 在所属于基站下的排队号
     */
    MarkerQueueNo?: number | null

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     */
    UpdatedAt?: string | null

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */
    UpdatedDC?: string | null

    /**
     * @db timestamp not null default now_utc()
     * 参数更新时间
     */
    MarkerParamTime?: string | null

    /**
     * @db int not null default 24
     * 打卡时间间隔(6-24小时)
     */
    MarkerDayInterval?: number | null

    /**
     * @db int not null default 6
     * 排队发射间隔时间（秒）
     */
    MarkerQueueInterval?: number | null

    /**
     * @db int not null default 60
     * 报警后发射间隔(30-240秒)
     */
    MarkerEmergentInterval?: number | null

    /**
     * @db int not null default 1
     * 界桩通信信道
     */
    MarkerChannel?: number | null

    /**
     * @db time not null
     * 界桩苏醒基准时间
     */
    MarkerWakeupBaseTime?: string | null

    /**
     * @db int not null default 0
     * 界桩是否已经被遥毙, 0:遥活 1:遥闭  2:遥晕
     */
    MarkerDisabled?: number | null

    /**
     * @db boolean default true
     * 界桩是否有安装电子设备
     */
    HasInstallDevice?: boolean | null

    /**
     * @db boolean default false
     * 石头界桩是否已安装
     */
    HasInstallStone?: boolean | null

    /**
     * @db int not null default 0
     * 常规界桩：0, 4g界桩(BF-DTD002): 1, 4g界桩带摄像机(BF-DTD002(Pro)): 2, 专网界桩dmr版(BF-DTD003): 3
     */
    MarkerType?: number | null

    /**
     * @db jsonb not null default  '{}'::jsonb
     * MarkerType=1时，填写bysproto.BDataUpdate
     */
    MarkerSettings?: string | null

    /**
     * @db varchar(20)
     * 4G界桩的iccid卡号，20位数字字符串
     */
    ICCID?: string | null

    /**
     * @db timestamp
     * iccid卡号到期日期
     */
    ExpirationDate?: string | null

    /**
     * @db text
     * 4g界桩的设备唯一标识
     */
    IMEI?: string | null

    /**
     * @db int not null default 0
     * 摄像机是否已经被遥毙, 0:遥活 1:遥闭  2:遥晕
     */
    CameraDisabled?: number | null
  }

  /** 界桩信息表 */
  class DbBysMarker implements IDbBysMarker {
    /**
     * Constructs a new DbBysMarker.
     * @rpc crud pcrud
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbBysMarkerOrgRID on DbBysMarker USING hash(OrgRID);
     * @dbpost ALTER TABLE DbBysMarker ADD COLUMN IF NOT EXISTS MarkerType integer not null default 0;
     * @dbpost ALTER TABLE DbBysMarker ADD COLUMN IF NOT EXISTS MarkerSettings jsonb not null default  '{}'::jsonb;
     * @dbpost ALTER TABLE dbbysmarker DROP CONSTRAINT if exists dbbysmarker_controllerrid_controllerchannel_markerqueueno_key;
     * @dbpost drop index if exists dbbysmarker_controllerrid_controllerchannel_markerqueueno_key;
     * @dbpost CREATE UNIQUE INDEX IF NOT EXISTS idxdbbysmarker_controllerrid_controllerchannel_markerqueueno_key on DbBysMarker (ControllerRID, ControllerChannel, MarkerQueueNo) where MarkerType=0; --同一个信道的排队号必须唯一,只针对旧的界桩
     * @dbpost ALTER TABLE DbBysMarker ALTER COLUMN ControllerRID DROP NOT NULL;
     * @dbpost ALTER TABLE DbBysMarker ADD COLUMN IF NOT EXISTS ICCID varchar(20);
     * @dbpost ALTER TABLE DbBysMarker ADD COLUMN IF NOT EXISTS ExpirationDate timestamp;
     * @dbpost CREATE UNIQUE INDEX IF NOT EXISTS idxDbBysMarkerICCID on DbBysMarker (ICCID) where MarkerType=1;
     * @dbpost ALTER TABLE DbBysMarker ADD COLUMN IF NOT EXISTS IMEI text;
     * @dbpost ALTER TABLE DbBysMarker ADD COLUMN IF NOT EXISTS CameraDisabled integer not null default 0;
     * @param [properties] Properties to set
     */
    constructor(properties?: bysdb.IDbBysMarker)

    /**
     * @db uuid primary key
     * 行ID
     */
    public RID: string

    /**
     * @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 界桩所属的群组
     */
    public OrgRID: string

    /**
     * @db varchar(16) not null unique
     * 界桩编号
     */
    public MarkerNo: string

    /**
     * @db text
     * 界桩描述信息
     */
    public MarkerDescription: string

    /**
     * @db uuid REFERENCES DbController(RID) ON DELETE RESTRICT
     * 所属控制器RID
     */
    public ControllerRID: string

    /**
     * @db int not null
     * 所属的基站通道，如果所属的控制器为中继控制器，此值固定为1
     */
    public ControllerChannel: number

    /**
     * @db double precision
     * 经度
     */
    public Lon: number

    /**
     * @db double precision
     * 纬度
     */
    public Lat: number

    /**
     * @db int not null unique
     * 界桩硬件ID,范围
     */
    public MarkerHWID: number

    /**
     * @db jsonb not null default  '{}'::jsonb
     * 其它设置，可以在里面扩展需要用到的其它信息
     * 新的红线图规则属性为：redLine，不存在该字段为默认连接，空数组为不连接，
     * 数组元素为界桩硬件ID，每个都连接
     */
    public Setting: string

    /**
     * @db int
     * 界桩型号 1:长型 X*Y*Z cm, 2:短型 x*y*z cm
     */
    public MarkerModel: number

    /**
     * @db int default 12
     * 地图开始显示级别
     */
    public MapShowLevel: number

    /**
     * @db int not null
     * 在所属于基站下的排队号
     */
    public MarkerQueueNo: number

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     */
    public UpdatedAt: string

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */
    public UpdatedDC: string

    /**
     * @db timestamp not null default now_utc()
     * 参数更新时间
     */
    public MarkerParamTime: string

    /**
     * @db int not null default 24
     * 打卡时间间隔(6-24小时)
     */
    public MarkerDayInterval: number

    /**
     * @db int not null default 6
     * 排队发射间隔时间（秒）
     */
    public MarkerQueueInterval: number

    /**
     * @db int not null default 60
     * 报警后发射间隔(30-240秒)
     */
    public MarkerEmergentInterval: number

    /**
     * @db int not null default 1
     * 界桩通信信道
     */
    public MarkerChannel: number

    /**
     * @db time not null
     * 界桩苏醒基准时间
     */
    public MarkerWakeupBaseTime: string

    /**
     * @db int not null default 0
     * 界桩是否已经被遥毙, 0:遥活 1:遥闭  2:遥晕
     */
    public MarkerDisabled: number

    /**
     * @db boolean default true
     * 界桩是否有安装电子设备
     */
    public HasInstallDevice: boolean

    /**
     * @db boolean default false
     * 石头界桩是否已安装
     */
    public HasInstallStone: boolean

    /**
     * @db int not null default 0
     * 常规界桩：0, 4g界桩(BF-DTD002): 1, 4g界桩带摄像机(BF-DTD002(Pro)): 2, 专网界桩dmr版(BF-DTD003): 3
     */
    public MarkerType: number

    /**
     * @db jsonb not null default  '{}'::jsonb
     * MarkerType=1时，填写bysproto.BDataUpdate
     */
    public MarkerSettings: string

    /**
     * @db varchar(20)
     * 4G界桩的iccid卡号，20位数字字符串
     */
    public ICCID: string

    /**
     * @db timestamp
     * iccid卡号到期日期
     */
    public ExpirationDate: string

    /**
     * @db text
     * 4g界桩的设备唯一标识
     */
    public IMEI: string

    /**
     * @db int not null default 0
     * 摄像机是否已经被遥毙, 0:遥活 1:遥闭  2:遥晕
     */
    public CameraDisabled: number

    /**
     * Encodes the specified DbBysMarker message. Does not implicitly {@link bysdb.DbBysMarker.verify|verify} messages.
     * @param message DbBysMarker message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: bysdb.IDbBysMarker,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a DbBysMarker message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns DbBysMarker
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): bysdb.DbBysMarker
  }

  /** Properties of a DbControllerOnlineHistory. */
  interface IDbControllerOnlineHistory {
    /**
     * @db uuid not null
     * 所属的群组
     */
    OrgRID?: string | null

    /**
     * @db timestamp not null
     * 动作时间,utc
     */
    ActionTime?: string | null

    /**
     * @db int
     * 控制器硬件ID
     */
    ControllerHWID?: number | null

    /**
     * @db int
     * action 1:上线 2:下线 11:ping信息
     */
    ActionCode?: number | null

    /**
     * @db text
     * ip/状态信息
     */
    IpInfo?: string | null

    /**
     * @db int
     * 登录网络类型,只对上线有效
     */
    NetworkType?: number | null

    /**
     * @db real
     * 电量V
     */
    Power?: number | null

    /**
     * @db int
     * 状态
     */
    Status?: number | null

    /** 登录设备类型 0:fsk控制器 1:4g界桩 */
    DeviceType?: number | null
  }

  /** 控制器上下线历史表，以月为单位分表 */
  class DbControllerOnlineHistory implements IDbControllerOnlineHistory {
    /**
     * Constructs a new DbControllerOnlineHistory.
     * @rpc pcrud
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbControllerOnlineHistoryControllerHWID on DbControllerOnlineHistory USING hash(ControllerHWID);
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbControllerOnlineHistoryOrgRID on DbControllerOnlineHistory USING hash(OrgRID);
     * @dbpost ALTER TABLE dbcontrolleronlinehistory ADD COLUMN IF NOT EXISTS Status integer;
     * @dbpost ALTER TABLE dbcontrolleronlinehistory ADD COLUMN IF NOT EXISTS DeviceType integer;
     * @param [properties] Properties to set
     */
    constructor(properties?: bysdb.IDbControllerOnlineHistory)

    /**
     * @db uuid not null
     * 所属的群组
     */
    public OrgRID: string

    /**
     * @db timestamp not null
     * 动作时间,utc
     */
    public ActionTime: string

    /**
     * @db int
     * 控制器硬件ID
     */
    public ControllerHWID: number

    /**
     * @db int
     * action 1:上线 2:下线 11:ping信息
     */
    public ActionCode: number

    /**
     * @db text
     * ip/状态信息
     */
    public IpInfo: string

    /**
     * @db int
     * 登录网络类型,只对上线有效
     */
    public NetworkType: number

    /**
     * @db real
     * 电量V
     */
    public Power: number

    /**
     * @db int
     * 状态
     */
    public Status: number

    /** 登录设备类型 0:fsk控制器 1:4g界桩 */
    public DeviceType: number

    /**
     * Encodes the specified DbControllerOnlineHistory message. Does not implicitly {@link bysdb.DbControllerOnlineHistory.verify|verify} messages.
     * @param message DbControllerOnlineHistory message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: bysdb.IDbControllerOnlineHistory,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a DbControllerOnlineHistory message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns DbControllerOnlineHistory
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): bysdb.DbControllerOnlineHistory
  }

  /** Properties of a DbMediaInfo. */
  interface IDbMediaInfo {
    /**
     * @db uuid primary key
     * 行ID
     */
    RID?: string | null

    /**
     * @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 物体所属的群组
     */
    OrgRID?: string | null

    /**
     * @db uuid null REFERENCES DbBysMarker(RID) ON DELETE set null
     * 界桩的RID
     */
    MarkerRID?: string | null

    /**
     * @db uuid null REFERENCES DbController(RID) ON DELETE set null
     * 控制器的RID
     */
    ControllerRID?: string | null

    /**
     * @db int
     * 媒体类型 1:normal pic 2:3d pic 3:video
     */
    MediaType?: number | null

    /**
     * @db text
     * 媒体原始文件名
     */
    MediaOrigFileName?: string | null

    /**
     * @db uuid REFERENCES DbUser(RID) ON DELETE set null
     * 上传用户rid
     */
    UploadUserRID?: string | null

    /**
     * @db text
     * 描述信息
     */
    MediaDescription?: string | null

    /**
     * @db timestamp not null
     * 上传时间,utc
     */
    UploadTime?: string | null

    /**
     * @db jsonb not null default  '{}'::jsonb
     * 其它设置，可以在里面扩展需要用到的其它信息
     */
    Setting?: string | null

    /**
     * @db uuid REFERENCES DbUser(RID) ON DELETE set null
     * 最新编辑用户rid
     */
    LastUpdateUserRID?: string | null

    /**
     * @db timestamp not null default now_utc()
     * 最新编辑时间,utc
     */
    LastUpdateTime?: string | null
  }

  /** 界桩/控制器媒体信息表 */
  class DbMediaInfo implements IDbMediaInfo {
    /**
     * Constructs a new DbMediaInfo.
     * @rpc pcrud
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbMediaInfoOrgRID on DbMediaInfo USING hash(OrgRID);
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbMediaInfoMarkerRID on DbMediaInfo USING hash(MarkerRID);
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbMediaInfoControllerRID on DbMediaInfo USING hash(ControllerRID);
     * @param [properties] Properties to set
     */
    constructor(properties?: bysdb.IDbMediaInfo)

    /**
     * @db uuid primary key
     * 行ID
     */
    public RID: string

    /**
     * @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 物体所属的群组
     */
    public OrgRID: string

    /**
     * @db uuid null REFERENCES DbBysMarker(RID) ON DELETE set null
     * 界桩的RID
     */
    public MarkerRID: string

    /**
     * @db uuid null REFERENCES DbController(RID) ON DELETE set null
     * 控制器的RID
     */
    public ControllerRID: string

    /**
     * @db int
     * 媒体类型 1:normal pic 2:3d pic 3:video
     */
    public MediaType: number

    /**
     * @db text
     * 媒体原始文件名
     */
    public MediaOrigFileName: string

    /**
     * @db uuid REFERENCES DbUser(RID) ON DELETE set null
     * 上传用户rid
     */
    public UploadUserRID: string

    /**
     * @db text
     * 描述信息
     */
    public MediaDescription: string

    /**
     * @db timestamp not null
     * 上传时间,utc
     */
    public UploadTime: string

    /**
     * @db jsonb not null default  '{}'::jsonb
     * 其它设置，可以在里面扩展需要用到的其它信息
     */
    public Setting: string

    /**
     * @db uuid REFERENCES DbUser(RID) ON DELETE set null
     * 最新编辑用户rid
     */
    public LastUpdateUserRID: string

    /**
     * @db timestamp not null default now_utc()
     * 最新编辑时间,utc
     */
    public LastUpdateTime: string

    /**
     * Encodes the specified DbMediaInfo message. Does not implicitly {@link bysdb.DbMediaInfo.verify|verify} messages.
     * @param message DbMediaInfo message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: bysdb.IDbMediaInfo,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a DbMediaInfo message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns DbMediaInfo
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): bysdb.DbMediaInfo
  }

  /** Properties of a DbMarkerHistory. */
  interface IDbMarkerHistory {
    /**
     * @db int
     * 接收的控制器ID
     */
    ControllerID?: number | null

    /**
     * @db int
     * 接收的控制器通道
     */
    ControllerChannel?: number | null

    /**
     * @db uuid not null
     * 所属的群组
     */
    OrgRID?: string | null

    /**
     * @db timestamp not null
     * 动作时间,utc
     */
    ActionTime?: string | null

    /**
     * @db int
     * 界桩硬件ID
     */
    MarkerHWID?: number | null

    /**
     * @db int
     * action 0xd1, 0xd2
     * 4g界桩信息上报21, ReportInfo=bysproto.BInfoReporting
     * 4g界桩报警22, ReportInfo=bysproto.BAlarmReporting
     */
    ActionCode?: number | null

    /**
     * @db int
     */
    Status?: number | null

    /**
     * @db text
     * 配置参数信息
     */
    ParamInfo?: string | null

    /**
     * @db double precision
     * gps lon
     */
    Lon?: number | null

    /**
     * @db double precision
     * gps lat
     */
    Lat?: number | null

    /**
     * @db timestamp
     * cmd time
     */
    CmdTime?: string | null

    /**
     * @db int
     * 实际接收的控制器ID（中继/基站）
     */
    RecvControllerID?: number | null

    /**
     * @db jsonb not null default  '{}'::jsonb
     */
    ReportInfo?: string | null
  }

  /** 界桩上传数据历史表，以月为单位分表 */
  class DbMarkerHistory implements IDbMarkerHistory {
    /**
     * Constructs a new DbMarkerHistory.
     * @rpc pcrud
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbMarkerHistoryMarkerHWID on DbMarkerHistory USING hash(MarkerHWID);
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbMarkerHistoryOrgRID on DbMarkerHistory USING hash(OrgRID);
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbMarkerHistoryEmergency on DbMarkerHistory (Status) where (Status & 128) >0;
     * @dbpost alter table DbMarkerHistory ADD COLUMN IF NOT EXISTS RecvControllerID int;
     * @dbpost alter table DbMarkerHistory ADD COLUMN IF NOT EXISTS ReportInfo jsonb not null default  '{}'::jsonb;
     * @param [properties] Properties to set
     */
    constructor(properties?: bysdb.IDbMarkerHistory)

    /**
     * @db int
     * 接收的控制器ID
     */
    public ControllerID: number

    /**
     * @db int
     * 接收的控制器通道
     */
    public ControllerChannel: number

    /**
     * @db uuid not null
     * 所属的群组
     */
    public OrgRID: string

    /**
     * @db timestamp not null
     * 动作时间,utc
     */
    public ActionTime: string

    /**
     * @db int
     * 界桩硬件ID
     */
    public MarkerHWID: number

    /**
     * @db int
     * action 0xd1, 0xd2
     * 4g界桩信息上报21, ReportInfo=bysproto.BInfoReporting
     * 4g界桩报警22, ReportInfo=bysproto.BAlarmReporting
     */
    public ActionCode: number

    /**
     * @db int
     */
    public Status: number

    /**
     * @db text
     * 配置参数信息
     */
    public ParamInfo: string

    /**
     * @db double precision
     * gps lon
     */
    public Lon: number

    /**
     * @db double precision
     * gps lat
     */
    public Lat: number

    /**
     * @db timestamp
     * cmd time
     */
    public CmdTime: string

    /**
     * @db int
     * 实际接收的控制器ID（中继/基站）
     */
    public RecvControllerID: number

    /**
     * @db jsonb not null default  '{}'::jsonb
     */
    public ReportInfo: string

    /**
     * Encodes the specified DbMarkerHistory message. Does not implicitly {@link bysdb.DbMarkerHistory.verify|verify} messages.
     * @param message DbMarkerHistory message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: bysdb.IDbMarkerHistory,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a DbMarkerHistory message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns DbMarkerHistory
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): bysdb.DbMarkerHistory
  }

  /** Properties of a DbMarkerPatrolHistory. */
  interface IDbMarkerPatrolHistory {
    /**
     * @db text
     * NFC卡片ID，hex string
     */
    NFCID?: string | null

    /**
     * @db timestamp not null
     * NFC打卡时间,utc
     */
    ActionTime?: string | null

    /**
     * @db int
     * 界桩硬件ID
     */
    MarkerHWID?: number | null

    /**
     * @db uuid not null
     * 打卡用户
     */
    UserID?: string | null

    /**
     * @db uuid not null
     * 所属的群组
     */
    OrgRID?: string | null
  }

  /** 4g界桩NFC巡查打卡历史表，以月为单位分表 */
  class DbMarkerPatrolHistory implements IDbMarkerPatrolHistory {
    /**
     * Constructs a new DbMarkerPatrolHistory.
     * @rpc pcrud
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbMarkerPatrolHistoryHWID on DbMarkerPatrolHistory USING hash(MarkerHWID);
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbMarkerPatrolHistoryOrgRID on DbMarkerPatrolHistory USING hash(OrgRID);
     * @param [properties] Properties to set
     */
    constructor(properties?: bysdb.IDbMarkerPatrolHistory)

    /**
     * @db text
     * NFC卡片ID，hex string
     */
    public NFCID: string

    /**
     * @db timestamp not null
     * NFC打卡时间,utc
     */
    public ActionTime: string

    /**
     * @db int
     * 界桩硬件ID
     */
    public MarkerHWID: number

    /**
     * @db uuid not null
     * 打卡用户
     */
    public UserID: string

    /**
     * @db uuid not null
     * 所属的群组
     */
    public OrgRID: string

    /**
     * Encodes the specified DbMarkerPatrolHistory message. Does not implicitly {@link bysdb.DbMarkerPatrolHistory.verify|verify} messages.
     * @param message DbMarkerPatrolHistory message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: bysdb.IDbMarkerPatrolHistory,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a DbMarkerPatrolHistory message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns DbMarkerPatrolHistory
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): bysdb.DbMarkerPatrolHistory
  }

  /** Properties of a DbNFCPatrolLine. */
  interface IDbNFCPatrolLine {
    /**
     * @db uuid primary key
     * 行ID
     */
    RID?: string | null

    /**
     * @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 线路的归属组
     */
    OrgRID?: string | null

    /**
     * @db varchar(16) not null unique
     * 线路名称
     */
    Name?: string | null

    /**
     * @db text
     */
    Note?: string | null

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     */
    UpdatedAt?: string | null

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */
    UpdatedDC?: string | null
  }

  /** 巡查线路表 */
  class DbNFCPatrolLine implements IDbNFCPatrolLine {
    /**
     * Constructs a new DbNFCPatrolLine.
     * @rpc pcrud
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbNFCPatrolLineOrgRID on DbNFCPatrolLine USING hash(OrgRID);
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbNFCPatrolLineName on DbNFCPatrolLine USING hash(Name);
     * @param [properties] Properties to set
     */
    constructor(properties?: bysdb.IDbNFCPatrolLine)

    /**
     * @db uuid primary key
     * 行ID
     */
    public RID: string

    /**
     * @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 线路的归属组
     */
    public OrgRID: string

    /**
     * @db varchar(16) not null unique
     * 线路名称
     */
    public Name: string

    /**
     * @db text
     */
    public Note: string

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     */
    public UpdatedAt: string

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */
    public UpdatedDC: string

    /**
     * Encodes the specified DbNFCPatrolLine message. Does not implicitly {@link bysdb.DbNFCPatrolLine.verify|verify} messages.
     * @param message DbNFCPatrolLine message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: bysdb.IDbNFCPatrolLine,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a DbNFCPatrolLine message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns DbNFCPatrolLine
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): bysdb.DbNFCPatrolLine
  }

  /** Properties of a DbNFCPatrolLineDetail. */
  interface IDbNFCPatrolLineDetail {
    /**
     * @db uuid primary key
     * 行ID
     */
    RID?: string | null

    /**
     * @db uuid not null REFERENCES DbNFCPatrolLine(RID) ON DELETE CASCADE
     * 归属线路
     */
    LineRID?: string | null

    /**
     * @db uuid not null REFERENCES DbBysMarker(RID) ON DELETE CASCADE
     * 线路下的巡查点
     */
    MarkerRID?: string | null

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     */
    UpdatedAt?: string | null

    /**
     * @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 归属组，与路线的上线一致
     */
    OrgRID?: string | null
  }

  /** 巡查线路详细表，分开主要是方便使用数据库外键约束 */
  class DbNFCPatrolLineDetail implements IDbNFCPatrolLineDetail {
    /**
     * Constructs a new DbNFCPatrolLineDetail.
     * @rpc pcrud
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbNFCPatrolLineDetailOrgRID on DbNFCPatrolLineDetail USING hash(OrgRID);
     * @dbpost CREATE INDEX IF NOT EXISTS idxDDbNFCPatrolLineDetailLineRID on DbNFCPatrolLineDetail USING hash(LineRID);
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbNFCPatrolLineDetailMarkerRID on DbNFCPatrolLineDetail USING hash(MarkerRID);
     * @param [properties] Properties to set
     */
    constructor(properties?: bysdb.IDbNFCPatrolLineDetail)

    /**
     * @db uuid primary key
     * 行ID
     */
    public RID: string

    /**
     * @db uuid not null REFERENCES DbNFCPatrolLine(RID) ON DELETE CASCADE
     * 归属线路
     */
    public LineRID: string

    /**
     * @db uuid not null REFERENCES DbBysMarker(RID) ON DELETE CASCADE
     * 线路下的巡查点
     */
    public MarkerRID: string

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     */
    public UpdatedAt: string

    /**
     * @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 归属组，与路线的上线一致
     */
    public OrgRID: string

    /**
     * Encodes the specified DbNFCPatrolLineDetail message. Does not implicitly {@link bysdb.DbNFCPatrolLineDetail.verify|verify} messages.
     * @param message DbNFCPatrolLineDetail message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: bysdb.IDbNFCPatrolLineDetail,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a DbNFCPatrolLineDetail message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns DbNFCPatrolLineDetail
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): bysdb.DbNFCPatrolLineDetail
  }

  /** Properties of a DbNFCPatrolLineRules. */
  interface IDbNFCPatrolLineRules {
    /**
     * @db uuid primary key
     * 行ID
     */
    RID?: string | null

    /**
     * @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 规则的归属组
     */
    OrgRID?: string | null

    /**
     * @db varchar(16) not null unique
     * 规则名称
     */
    Name?: string | null

    /**
     * @db boolean default false
     * 星期一
     */
    Day1?: boolean | null

    /**
     * @db boolean default false
     * 星期二
     */
    Day2?: boolean | null

    /**
     * @db boolean default false
     * 星期三
     */
    Day3?: boolean | null

    /**
     * @db boolean default false
     * 星期四
     */
    Day4?: boolean | null

    /**
     * @db boolean default false
     * 星期五
     */
    Day5?: boolean | null

    /**
     * @db boolean default false
     * 星期六
     */
    Day6?: boolean | null

    /**
     * @db boolean default false
     * 星期日
     */
    Day7?: boolean | null

    /**
     * @db time
     * 巡查开始的时间
     */
    CheckStartTime?: string | null

    /**
     * @db time
     * 巡查结束的时间
     */
    CheckEndTime?: string | null

    /**
     * @db int not null default 1
     * 巡查次数
     */
    CheckCount?: number | null

    /**
     * @db int default 0
     * 线路巡查规则生效时间的类型 0:总是生效 1:按有效时间段
     */
    EffectiveType?: number | null

    /**
     * @db timestamp
     * 规则开始生效时间
     */
    EffectiveStart?: string | null

    /**
     * @db timestamp
     * 规则生效结束时间
     */
    EffectiveEnd?: string | null

    /**
     * @db text
     */
    Note?: string | null

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     */
    UpdatedAt?: string | null

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */
    UpdatedDC?: string | null
  }

  /** 界桩NFC巡查规则 */
  class DbNFCPatrolLineRules implements IDbNFCPatrolLineRules {
    /**
     * Constructs a new DbNFCPatrolLineRules.
     * @rpc pcrud
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbNFCPatrolLineRulesOrgRID on DbNFCPatrolLineRules USING hash(OrgRID);
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbNFCPatrolLineRulesName on DbNFCPatrolLineRules USING hash(Name);
     * @param [properties] Properties to set
     */
    constructor(properties?: bysdb.IDbNFCPatrolLineRules)

    /**
     * @db uuid primary key
     * 行ID
     */
    public RID: string

    /**
     * @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 规则的归属组
     */
    public OrgRID: string

    /**
     * @db varchar(16) not null unique
     * 规则名称
     */
    public Name: string

    /**
     * @db boolean default false
     * 星期一
     */
    public Day1: boolean

    /**
     * @db boolean default false
     * 星期二
     */
    public Day2: boolean

    /**
     * @db boolean default false
     * 星期三
     */
    public Day3: boolean

    /**
     * @db boolean default false
     * 星期四
     */
    public Day4: boolean

    /**
     * @db boolean default false
     * 星期五
     */
    public Day5: boolean

    /**
     * @db boolean default false
     * 星期六
     */
    public Day6: boolean

    /**
     * @db boolean default false
     * 星期日
     */
    public Day7: boolean

    /**
     * @db time
     * 巡查开始的时间
     */
    public CheckStartTime: string

    /**
     * @db time
     * 巡查结束的时间
     */
    public CheckEndTime: string

    /**
     * @db int not null default 1
     * 巡查次数
     */
    public CheckCount: number

    /**
     * @db int default 0
     * 线路巡查规则生效时间的类型 0:总是生效 1:按有效时间段
     */
    public EffectiveType: number

    /**
     * @db timestamp
     * 规则开始生效时间
     */
    public EffectiveStart: string

    /**
     * @db timestamp
     * 规则生效结束时间
     */
    public EffectiveEnd: string

    /**
     * @db text
     */
    public Note: string

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     */
    public UpdatedAt: string

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */
    public UpdatedDC: string

    /**
     * Encodes the specified DbNFCPatrolLineRules message. Does not implicitly {@link bysdb.DbNFCPatrolLineRules.verify|verify} messages.
     * @param message DbNFCPatrolLineRules message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: bysdb.IDbNFCPatrolLineRules,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a DbNFCPatrolLineRules message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns DbNFCPatrolLineRules
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): bysdb.DbNFCPatrolLineRules
  }

  /** Properties of a DbNFCPatrolLineAndRules. */
  interface IDbNFCPatrolLineAndRules {
    /**
     * @db uuid primary key
     * 行ID
     */
    RID?: string | null

    /**
     * @db uuid not null REFERENCES DbNFCPatrolLine(RID) ON DELETE CASCADE
     * 归属线路
     */
    LineRID?: string | null

    /**
     * @db uuid not null REFERENCES DbNFCPatrolLineRules(RID) ON DELETE CASCADE
     * 线路规则
     */
    RuleRID?: string | null

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     */
    UpdatedAt?: string | null

    /**
     * @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 归属组，与路线的上线一致
     */
    OrgRID?: string | null
  }

  /** 巡查线路和规则的关系表，分开主要是方便使用数据库外键约束 */
  class DbNFCPatrolLineAndRules implements IDbNFCPatrolLineAndRules {
    /**
     * Constructs a new DbNFCPatrolLineAndRules.
     * @rpc pcrud
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbNFCPatrolLineAndRulesOrgRID on DbNFCPatrolLineAndRules USING hash(OrgRID);
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbNFCPatrolLineAndRulesLineRID on DbNFCPatrolLineAndRules USING hash(LineRID);
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbNFCPatrolLineAndRulesRuleRID on DbNFCPatrolLineAndRules USING hash(RuleRID);
     * @param [properties] Properties to set
     */
    constructor(properties?: bysdb.IDbNFCPatrolLineAndRules)

    /**
     * @db uuid primary key
     * 行ID
     */
    public RID: string

    /**
     * @db uuid not null REFERENCES DbNFCPatrolLine(RID) ON DELETE CASCADE
     * 归属线路
     */
    public LineRID: string

    /**
     * @db uuid not null REFERENCES DbNFCPatrolLineRules(RID) ON DELETE CASCADE
     * 线路规则
     */
    public RuleRID: string

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     */
    public UpdatedAt: string

    /**
     * @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 归属组，与路线的上线一致
     */
    public OrgRID: string

    /**
     * Encodes the specified DbNFCPatrolLineAndRules message. Does not implicitly {@link bysdb.DbNFCPatrolLineAndRules.verify|verify} messages.
     * @param message DbNFCPatrolLineAndRules message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: bysdb.IDbNFCPatrolLineAndRules,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a DbNFCPatrolLineAndRules message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns DbNFCPatrolLineAndRules
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): bysdb.DbNFCPatrolLineAndRules
  }

  /** Properties of a CamImageData. */
  interface ICamImageData {
    /** 设备IMEI */
    devId?: string | null

    /** 4G模块自动添加 */
    ccid?: string | null

    /** 固件版本 */
    firmwareVersion?: string | null

    /** 抓拍时间戳，Unix秒时间戳 */
    timestamp?: Long | null

    /** 电池电压单位mv */
    battery?: number | null

    /** 4G信号强度 */
    signal?: string | null

    /** 环境温度 */
    tempEnv?: number | null

    /** CPU温度 */
    tempCpu?: number | null

    /**
     * 工作类型
     * 0 – 手动
     * 1 – 唤醒拍照
     * 2 – 定时拍照
     * 3 – 唤醒录像
     * 4 – 定时录像
     */
    type?: number | null

    /**
     * 以下为可选字段
     * 放大系数
     */
    zoomRate?: number | null

    /** 充电电流，合方圆太阳能充电模块支持 */
    icharge?: number | null

    /** 负载电流，合方圆太阳能充电模块支持 */
    iload?: number | null

    /** 充电电压，合方圆太阳能充电模块支持 */
    vcharge?: number | null

    /**
     * 上传图片时，界桩自定义的json数据
     * "{\"hwid\": 14, \"type\": 2}"
     * hwid: 界桩硬件ID
     * type: 1、红外感应抓拍，2、报警抓拍，3、定时抓拍，4、手动抓拍
     */
    custData?: string | null
  }

  /** 合方圆摄像机上传图片时，附带的json结构体 */
  class CamImageData implements ICamImageData {
    /**
     * Constructs a new CamImageData.
     * @param [properties] Properties to set
     */
    constructor(properties?: bysdb.ICamImageData)

    /** 设备IMEI */
    public devId: string

    /** 4G模块自动添加 */
    public ccid: string

    /** 固件版本 */
    public firmwareVersion: string

    /** 抓拍时间戳，Unix秒时间戳 */
    public timestamp: Long

    /** 电池电压单位mv */
    public battery: number

    /** 4G信号强度 */
    public signal: string

    /** 环境温度 */
    public tempEnv: number

    /** CPU温度 */
    public tempCpu: number

    /**
     * 工作类型
     * 0 – 手动
     * 1 – 唤醒拍照
     * 2 – 定时拍照
     * 3 – 唤醒录像
     * 4 – 定时录像
     */
    public type: number

    /**
     * 以下为可选字段
     * 放大系数
     */
    public zoomRate: number

    /** 充电电流，合方圆太阳能充电模块支持 */
    public icharge: number

    /** 负载电流，合方圆太阳能充电模块支持 */
    public iload: number

    /** 充电电压，合方圆太阳能充电模块支持 */
    public vcharge: number

    /**
     * 上传图片时，界桩自定义的json数据
     * "{\"hwid\": 14, \"type\": 2}"
     * hwid: 界桩硬件ID
     * type: 1、红外感应抓拍，2、报警抓拍，3、定时抓拍，4、手动抓拍
     */
    public custData: string

    /**
     * Encodes the specified CamImageData message. Does not implicitly {@link bysdb.CamImageData.verify|verify} messages.
     * @param message CamImageData message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: bysdb.ICamImageData,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a CamImageData message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns CamImageData
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): bysdb.CamImageData
  }

  /** Properties of a DbMarkerUploadImageHistory. */
  interface IDbMarkerUploadImageHistory {
    /**
     * @db uuid not null
     * 所属的群组
     */
    OrgRID?: string | null

    /**
     * @db int
     * 界桩硬件ID
     */
    MarkerHWID?: number | null

    /**
     * @db timestamp not null
     * 抓拍时间,utc
     */
    CaptureTime?: string | null

    /**
     * @db int
     * 抓拍的动作类型，与CamImageData.type一致
     * 0 – 手动
     * 1 – 唤醒拍照
     * 2 – 定时拍照
     * 3 – 唤醒录像
     * 4 – 定时录像
     */
    CaptureType?: number | null

    /**
     * @db timestamp not null
     * 服务器接收上传时间,utc
     */
    UploadTime?: string | null

    /**
     * @db text
     * 上传的图片保存路径，f512673f-0f6a-4b54-95bf-d377b78fba1a.jpg
     */
    FileName?: string | null

    /**
     * @db jsonb not null default  '{}'::jsonb
     * 界桩上传图片时附带的json数据，CamImageData json字符串
     */
    FormData?: string | null
  }

  /** 4g界桩上传的图片历史表，以月为单位分表 */
  class DbMarkerUploadImageHistory implements IDbMarkerUploadImageHistory {
    /**
     * Constructs a new DbMarkerUploadImageHistory.
     * @rpc pcrud
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbMarkerUploadImageHistoryHWID on DbMarkerUploadImageHistory USING hash(MarkerHWID);
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbMarkerUploadImageHistoryOrgRID on DbMarkerUploadImageHistory USING hash(OrgRID);
     * @param [properties] Properties to set
     */
    constructor(properties?: bysdb.IDbMarkerUploadImageHistory)

    /**
     * @db uuid not null
     * 所属的群组
     */
    public OrgRID: string

    /**
     * @db int
     * 界桩硬件ID
     */
    public MarkerHWID: number

    /**
     * @db timestamp not null
     * 抓拍时间,utc
     */
    public CaptureTime: string

    /**
     * @db int
     * 抓拍的动作类型，与CamImageData.type一致
     * 0 – 手动
     * 1 – 唤醒拍照
     * 2 – 定时拍照
     * 3 – 唤醒录像
     * 4 – 定时录像
     */
    public CaptureType: number

    /**
     * @db timestamp not null
     * 服务器接收上传时间,utc
     */
    public UploadTime: string

    /**
     * @db text
     * 上传的图片保存路径，f512673f-0f6a-4b54-95bf-d377b78fba1a.jpg
     */
    public FileName: string

    /**
     * @db jsonb not null default  '{}'::jsonb
     * 界桩上传图片时附带的json数据，CamImageData json字符串
     */
    public FormData: string

    /**
     * Encodes the specified DbMarkerUploadImageHistory message. Does not implicitly {@link bysdb.DbMarkerUploadImageHistory.verify|verify} messages.
     * @param message DbMarkerUploadImageHistory message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: bysdb.IDbMarkerUploadImageHistory,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a DbMarkerUploadImageHistory message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns DbMarkerUploadImageHistory
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): bysdb.DbMarkerUploadImageHistory
  }
}

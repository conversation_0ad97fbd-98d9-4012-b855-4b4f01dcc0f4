/*eslint-disable block-scoped-var, id-length, no-control-regex, no-magic-numbers, no-prototype-builtins, no-redeclare, no-shadow, no-var, sort-vars*/
import * as $protobuf from 'protobufjs/minimal'

// Common aliases
const $Reader = $protobuf.Reader,
  $Writer = $protobuf.Writer,
  $util = $protobuf.util

// Exported root namespace
const $root = $protobuf.roots['default'] || ($protobuf.roots['default'] = {})

export const doc = ($root.doc = (() => {
  /**
   * Namespace doc.
   * @exports doc
   * @namespace
   */
  const doc = $root.doc || {}

  doc.OnlineControllerInfo = (function () {
    /**
     * Properties of an OnlineControllerInfo.
     * @memberof doc
     * @interface IOnlineControllerInfo
     * @property {string|null} [ConnectTime] 连接时间
     * @property {number|null} [ControllerID] 控制器ID
     * @property {number|null} [NetworkType] 登录网络方式
     * @property {string|null} [LastDataTime] 最后数据时间
     * @property {number|null} [Power] 最后电量
     * @property {number|null} [ChannelNo] 通道手台信道号
     * @property {Array.<number>|null} [TransferChannelNos] 本控制器数据中转的通道号和对应的信道号
     * [通道号1,通道1手台的信道号，通道号2,通道2手台的信道号，...]
     * 如[1,1,2,1]表示通道1 在1信道，通道2也在1信道
     * @property {number|null} [DeviceType] 登录设备类型 0:fsk控制器 1:4g界桩
     */

    /**
     * Constructs a new OnlineControllerInfo.
     * @memberof doc
     * @classdesc Represents an OnlineControllerInfo.
     * @implements IOnlineControllerInfo
     * @constructor
     * @param {doc.IOnlineControllerInfo=} [properties] Properties to set
     */
    function OnlineControllerInfo(properties) {
      this.TransferChannelNos = []
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 连接时间
     * @member {string} ConnectTime
     * @memberof doc.OnlineControllerInfo
     * @instance
     */
    OnlineControllerInfo.prototype.ConnectTime = ''

    /**
     * 控制器ID
     * @member {number} ControllerID
     * @memberof doc.OnlineControllerInfo
     * @instance
     */
    OnlineControllerInfo.prototype.ControllerID = 0

    /**
     * 登录网络方式
     * @member {number} NetworkType
     * @memberof doc.OnlineControllerInfo
     * @instance
     */
    OnlineControllerInfo.prototype.NetworkType = 0

    /**
     * 最后数据时间
     * @member {string} LastDataTime
     * @memberof doc.OnlineControllerInfo
     * @instance
     */
    OnlineControllerInfo.prototype.LastDataTime = ''

    /**
     * 最后电量
     * @member {number} Power
     * @memberof doc.OnlineControllerInfo
     * @instance
     */
    OnlineControllerInfo.prototype.Power = 0

    /**
     * 通道手台信道号
     * @member {number} ChannelNo
     * @memberof doc.OnlineControllerInfo
     * @instance
     */
    OnlineControllerInfo.prototype.ChannelNo = 0

    /**
     * 本控制器数据中转的通道号和对应的信道号
     * [通道号1,通道1手台的信道号，通道号2,通道2手台的信道号，...]
     * 如[1,1,2,1]表示通道1 在1信道，通道2也在1信道
     * @member {Array.<number>} TransferChannelNos
     * @memberof doc.OnlineControllerInfo
     * @instance
     */
    OnlineControllerInfo.prototype.TransferChannelNos = $util.emptyArray

    /**
     * 登录设备类型 0:fsk控制器 1:4g界桩
     * @member {number} DeviceType
     * @memberof doc.OnlineControllerInfo
     * @instance
     */
    OnlineControllerInfo.prototype.DeviceType = 0

    /**
     * Encodes the specified OnlineControllerInfo message. Does not implicitly {@link doc.OnlineControllerInfo.verify|verify} messages.
     * @function encode
     * @memberof doc.OnlineControllerInfo
     * @static
     * @param {doc.IOnlineControllerInfo} message OnlineControllerInfo message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    OnlineControllerInfo.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (
        message.ConnectTime != null &&
        Object.hasOwnProperty.call(message, 'ConnectTime')
      )
        writer.uint32(/* id 1, wireType 2 =*/ 10).string(message.ConnectTime)
      if (
        message.ControllerID != null &&
        Object.hasOwnProperty.call(message, 'ControllerID')
      )
        writer.uint32(/* id 2, wireType 0 =*/ 16).sint32(message.ControllerID)
      if (
        message.NetworkType != null &&
        Object.hasOwnProperty.call(message, 'NetworkType')
      )
        writer.uint32(/* id 3, wireType 0 =*/ 24).sint32(message.NetworkType)
      if (
        message.LastDataTime != null &&
        Object.hasOwnProperty.call(message, 'LastDataTime')
      )
        writer.uint32(/* id 4, wireType 2 =*/ 34).string(message.LastDataTime)
      if (message.Power != null && Object.hasOwnProperty.call(message, 'Power'))
        writer.uint32(/* id 5, wireType 5 =*/ 45).float(message.Power)
      if (
        message.ChannelNo != null &&
        Object.hasOwnProperty.call(message, 'ChannelNo')
      )
        writer.uint32(/* id 6, wireType 0 =*/ 48).sint32(message.ChannelNo)
      if (
        message.TransferChannelNos != null &&
        message.TransferChannelNos.length
      ) {
        writer.uint32(/* id 7, wireType 2 =*/ 58).fork()
        for (let i = 0; i < message.TransferChannelNos.length; ++i)
          writer.sint32(message.TransferChannelNos[i])
        writer.ldelim()
      }
      if (
        message.DeviceType != null &&
        Object.hasOwnProperty.call(message, 'DeviceType')
      )
        writer.uint32(/* id 8, wireType 0 =*/ 64).int32(message.DeviceType)
      return writer
    }

    /**
     * Decodes an OnlineControllerInfo message from the specified reader or buffer.
     * @function decode
     * @memberof doc.OnlineControllerInfo
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {doc.OnlineControllerInfo} OnlineControllerInfo
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    OnlineControllerInfo.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.doc.OnlineControllerInfo()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.ConnectTime = reader.string()
            break
          case 2:
            message.ControllerID = reader.sint32()
            break
          case 3:
            message.NetworkType = reader.sint32()
            break
          case 4:
            message.LastDataTime = reader.string()
            break
          case 5:
            message.Power = reader.float()
            break
          case 6:
            message.ChannelNo = reader.sint32()
            break
          case 7:
            if (
              !(message.TransferChannelNos && message.TransferChannelNos.length)
            )
              message.TransferChannelNos = []
            if ((tag & 7) === 2) {
              let end2 = reader.uint32() + reader.pos
              while (reader.pos < end2)
                message.TransferChannelNos.push(reader.sint32())
            } else message.TransferChannelNos.push(reader.sint32())
            break
          case 8:
            message.DeviceType = reader.int32()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return OnlineControllerInfo
  })()

  doc.MarkerQueueNoInfo = (function () {
    /**
     * Properties of a MarkerQueueNoInfo.
     * @memberof doc
     * @interface IMarkerQueueNoInfo
     * @property {string|null} [ControllerRID] 控制器RID
     * @property {number|null} [ControllerChannelNo] 控制器通道号
     * @property {number|null} [QueueNoAvailable] 0:查询可用的排队号
     * >0:此排队号是否可用
     * @property {number|null} [Result] 结果
     * 可用的排队号,或者=QueueNoAvailable，或者是新的可用排队号
     */

    /**
     * Constructs a new MarkerQueueNoInfo.
     * @memberof doc
     * @classdesc Represents a MarkerQueueNoInfo.
     * @implements IMarkerQueueNoInfo
     * @constructor
     * @param {doc.IMarkerQueueNoInfo=} [properties] Properties to set
     */
    function MarkerQueueNoInfo(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 控制器RID
     * @member {string} ControllerRID
     * @memberof doc.MarkerQueueNoInfo
     * @instance
     */
    MarkerQueueNoInfo.prototype.ControllerRID = ''

    /**
     * 控制器通道号
     * @member {number} ControllerChannelNo
     * @memberof doc.MarkerQueueNoInfo
     * @instance
     */
    MarkerQueueNoInfo.prototype.ControllerChannelNo = 0

    /**
     * 0:查询可用的排队号
     * >0:此排队号是否可用
     * @member {number} QueueNoAvailable
     * @memberof doc.MarkerQueueNoInfo
     * @instance
     */
    MarkerQueueNoInfo.prototype.QueueNoAvailable = 0

    /**
     * 结果
     * 可用的排队号,或者=QueueNoAvailable，或者是新的可用排队号
     * @member {number} Result
     * @memberof doc.MarkerQueueNoInfo
     * @instance
     */
    MarkerQueueNoInfo.prototype.Result = 0

    /**
     * Encodes the specified MarkerQueueNoInfo message. Does not implicitly {@link doc.MarkerQueueNoInfo.verify|verify} messages.
     * @function encode
     * @memberof doc.MarkerQueueNoInfo
     * @static
     * @param {doc.IMarkerQueueNoInfo} message MarkerQueueNoInfo message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    MarkerQueueNoInfo.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (
        message.ControllerRID != null &&
        Object.hasOwnProperty.call(message, 'ControllerRID')
      )
        writer.uint32(/* id 1, wireType 2 =*/ 10).string(message.ControllerRID)
      if (
        message.ControllerChannelNo != null &&
        Object.hasOwnProperty.call(message, 'ControllerChannelNo')
      )
        writer
          .uint32(/* id 2, wireType 0 =*/ 16)
          .sint32(message.ControllerChannelNo)
      if (
        message.QueueNoAvailable != null &&
        Object.hasOwnProperty.call(message, 'QueueNoAvailable')
      )
        writer
          .uint32(/* id 3, wireType 0 =*/ 24)
          .sint32(message.QueueNoAvailable)
      if (
        message.Result != null &&
        Object.hasOwnProperty.call(message, 'Result')
      )
        writer.uint32(/* id 4, wireType 0 =*/ 32).sint32(message.Result)
      return writer
    }

    /**
     * Decodes a MarkerQueueNoInfo message from the specified reader or buffer.
     * @function decode
     * @memberof doc.MarkerQueueNoInfo
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {doc.MarkerQueueNoInfo} MarkerQueueNoInfo
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    MarkerQueueNoInfo.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.doc.MarkerQueueNoInfo()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.ControllerRID = reader.string()
            break
          case 2:
            message.ControllerChannelNo = reader.sint32()
            break
          case 3:
            message.QueueNoAvailable = reader.sint32()
            break
          case 4:
            message.Result = reader.sint32()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return MarkerQueueNoInfo
  })()

  doc.MarkerInfo = (function () {
    /**
     * Properties of a MarkerInfo.
     * @memberof doc
     * @interface IMarkerInfo
     * @property {number|null} [ControllerID] 控制器ID
     * @property {number|null} [ControllerChannelNo] 控制器通道
     * @property {number|null} [MarkerID] 界桩ID
     * @property {string|null} [MarkerRID] 界桩RID
     * @property {number|null} [MarkerUploadCmd] 上来的指令，0xd1,0xd2,0xd3
     * @property {number|null} [MarkerChannel] 界桩所在信道,未知设置为0
     * @property {string|null} [MarkerCmdTime] 上传的命令时间 yyyy-mm-dd HH:MM:SS(utc)
     * @property {boolean|null} [MarkerSysPassOK] 上传的命令系统密码是否正确
     * @property {string|null} [MarkerParamTime] 界桩的参数配置时间
     * @property {number|null} [Status] 界桩状态，目前只用低位一个字节
     * @property {bysproto.IBInfoReporting|null} [InfoReporting] 界桩上报
     * @property {bysproto.IBAlarmReporting|null} [AlarmReporting] MarkerInfo AlarmReporting
     * @property {number|null} [MarkerType] 常规界桩：0, 4g界桩: 1
     * @property {number|null} [AlarmStatusWith4G] 4g界桩报警状态，0:无报警，1:报警
     */

    /**
     * Constructs a new MarkerInfo.
     * @memberof doc
     * @classdesc 界桩信息
     * @implements IMarkerInfo
     * @constructor
     * @param {doc.IMarkerInfo=} [properties] Properties to set
     */
    function MarkerInfo(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 控制器ID
     * @member {number} ControllerID
     * @memberof doc.MarkerInfo
     * @instance
     */
    MarkerInfo.prototype.ControllerID = 0

    /**
     * 控制器通道
     * @member {number} ControllerChannelNo
     * @memberof doc.MarkerInfo
     * @instance
     */
    MarkerInfo.prototype.ControllerChannelNo = 0

    /**
     * 界桩ID
     * @member {number} MarkerID
     * @memberof doc.MarkerInfo
     * @instance
     */
    MarkerInfo.prototype.MarkerID = 0

    /**
     * 界桩RID
     * @member {string} MarkerRID
     * @memberof doc.MarkerInfo
     * @instance
     */
    MarkerInfo.prototype.MarkerRID = ''

    /**
     * 上来的指令，0xd1,0xd2,0xd3
     * @member {number} MarkerUploadCmd
     * @memberof doc.MarkerInfo
     * @instance
     */
    MarkerInfo.prototype.MarkerUploadCmd = 0

    /**
     * 界桩所在信道,未知设置为0
     * @member {number} MarkerChannel
     * @memberof doc.MarkerInfo
     * @instance
     */
    MarkerInfo.prototype.MarkerChannel = 0

    /**
     * 上传的命令时间 yyyy-mm-dd HH:MM:SS(utc)
     * @member {string} MarkerCmdTime
     * @memberof doc.MarkerInfo
     * @instance
     */
    MarkerInfo.prototype.MarkerCmdTime = ''

    /**
     * 上传的命令系统密码是否正确
     * @member {boolean} MarkerSysPassOK
     * @memberof doc.MarkerInfo
     * @instance
     */
    MarkerInfo.prototype.MarkerSysPassOK = false

    /**
     * 界桩的参数配置时间
     * @member {string} MarkerParamTime
     * @memberof doc.MarkerInfo
     * @instance
     */
    MarkerInfo.prototype.MarkerParamTime = ''

    /**
     * 界桩状态，目前只用低位一个字节
     * @member {number} Status
     * @memberof doc.MarkerInfo
     * @instance
     */
    MarkerInfo.prototype.Status = 0

    /**
     * 界桩上报
     * @member {bysproto.IBInfoReporting|null|undefined} InfoReporting
     * @memberof doc.MarkerInfo
     * @instance
     */
    MarkerInfo.prototype.InfoReporting = null

    /**
     * MarkerInfo AlarmReporting.
     * @member {bysproto.IBAlarmReporting|null|undefined} AlarmReporting
     * @memberof doc.MarkerInfo
     * @instance
     */
    MarkerInfo.prototype.AlarmReporting = null

    /**
     * 常规界桩：0, 4g界桩: 1
     * @member {number} MarkerType
     * @memberof doc.MarkerInfo
     * @instance
     */
    MarkerInfo.prototype.MarkerType = 0

    /**
     * 4g界桩报警状态，0:无报警，1:报警
     * @member {number} AlarmStatusWith4G
     * @memberof doc.MarkerInfo
     * @instance
     */
    MarkerInfo.prototype.AlarmStatusWith4G = 0

    /**
     * Encodes the specified MarkerInfo message. Does not implicitly {@link doc.MarkerInfo.verify|verify} messages.
     * @function encode
     * @memberof doc.MarkerInfo
     * @static
     * @param {doc.IMarkerInfo} message MarkerInfo message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    MarkerInfo.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (
        message.ControllerID != null &&
        Object.hasOwnProperty.call(message, 'ControllerID')
      )
        writer.uint32(/* id 1, wireType 0 =*/ 8).sint32(message.ControllerID)
      if (
        message.ControllerChannelNo != null &&
        Object.hasOwnProperty.call(message, 'ControllerChannelNo')
      )
        writer
          .uint32(/* id 2, wireType 0 =*/ 16)
          .sint32(message.ControllerChannelNo)
      if (
        message.MarkerID != null &&
        Object.hasOwnProperty.call(message, 'MarkerID')
      )
        writer.uint32(/* id 3, wireType 0 =*/ 24).sint32(message.MarkerID)
      if (
        message.MarkerRID != null &&
        Object.hasOwnProperty.call(message, 'MarkerRID')
      )
        writer.uint32(/* id 4, wireType 2 =*/ 34).string(message.MarkerRID)
      if (
        message.MarkerUploadCmd != null &&
        Object.hasOwnProperty.call(message, 'MarkerUploadCmd')
      )
        writer
          .uint32(/* id 5, wireType 0 =*/ 40)
          .sint32(message.MarkerUploadCmd)
      if (
        message.MarkerChannel != null &&
        Object.hasOwnProperty.call(message, 'MarkerChannel')
      )
        writer.uint32(/* id 6, wireType 0 =*/ 48).sint32(message.MarkerChannel)
      if (
        message.MarkerCmdTime != null &&
        Object.hasOwnProperty.call(message, 'MarkerCmdTime')
      )
        writer.uint32(/* id 7, wireType 2 =*/ 58).string(message.MarkerCmdTime)
      if (
        message.MarkerSysPassOK != null &&
        Object.hasOwnProperty.call(message, 'MarkerSysPassOK')
      )
        writer.uint32(/* id 8, wireType 0 =*/ 64).bool(message.MarkerSysPassOK)
      if (
        message.MarkerParamTime != null &&
        Object.hasOwnProperty.call(message, 'MarkerParamTime')
      )
        writer
          .uint32(/* id 9, wireType 2 =*/ 74)
          .string(message.MarkerParamTime)
      if (
        message.Status != null &&
        Object.hasOwnProperty.call(message, 'Status')
      )
        writer.uint32(/* id 10, wireType 0 =*/ 80).sint32(message.Status)
      if (
        message.InfoReporting != null &&
        Object.hasOwnProperty.call(message, 'InfoReporting')
      )
        $root.bysproto.BInfoReporting.encode(
          message.InfoReporting,
          writer.uint32(/* id 11, wireType 2 =*/ 90).fork(),
        ).ldelim()
      if (
        message.AlarmReporting != null &&
        Object.hasOwnProperty.call(message, 'AlarmReporting')
      )
        $root.bysproto.BAlarmReporting.encode(
          message.AlarmReporting,
          writer.uint32(/* id 12, wireType 2 =*/ 98).fork(),
        ).ldelim()
      if (
        message.MarkerType != null &&
        Object.hasOwnProperty.call(message, 'MarkerType')
      )
        writer.uint32(/* id 13, wireType 0 =*/ 104).sint32(message.MarkerType)
      if (
        message.AlarmStatusWith4G != null &&
        Object.hasOwnProperty.call(message, 'AlarmStatusWith4G')
      )
        writer
          .uint32(/* id 14, wireType 0 =*/ 112)
          .sint32(message.AlarmStatusWith4G)
      return writer
    }

    /**
     * Decodes a MarkerInfo message from the specified reader or buffer.
     * @function decode
     * @memberof doc.MarkerInfo
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {doc.MarkerInfo} MarkerInfo
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    MarkerInfo.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.doc.MarkerInfo()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.ControllerID = reader.sint32()
            break
          case 2:
            message.ControllerChannelNo = reader.sint32()
            break
          case 3:
            message.MarkerID = reader.sint32()
            break
          case 4:
            message.MarkerRID = reader.string()
            break
          case 5:
            message.MarkerUploadCmd = reader.sint32()
            break
          case 6:
            message.MarkerChannel = reader.sint32()
            break
          case 7:
            message.MarkerCmdTime = reader.string()
            break
          case 8:
            message.MarkerSysPassOK = reader.bool()
            break
          case 9:
            message.MarkerParamTime = reader.string()
            break
          case 10:
            message.Status = reader.sint32()
            break
          case 11:
            message.InfoReporting = $root.bysproto.BInfoReporting.decode(
              reader,
              reader.uint32(),
            )
            break
          case 12:
            message.AlarmReporting = $root.bysproto.BAlarmReporting.decode(
              reader,
              reader.uint32(),
            )
            break
          case 13:
            message.MarkerType = reader.sint32()
            break
          case 14:
            message.AlarmStatusWith4G = reader.sint32()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return MarkerInfo
  })()

  doc.ControllerCmd = (function () {
    /**
     * Properties of a ControllerCmd.
     * @memberof doc
     * @interface IControllerCmd
     * @property {number|null} [ControllerID] 目标控制器ID
     * @property {number|null} [ControllerChannelNo] 在上级控制器的通道号，中继时需要填写，基站本地通道时为0
     * @property {number|null} [NewChannelNo] 新监听信道号
     * @property {number|null} [Cmd] 命令
     * 13,14
     */

    /**
     * Constructs a new ControllerCmd.
     * @memberof doc
     * @classdesc Represents a ControllerCmd.
     * @implements IControllerCmd
     * @constructor
     * @param {doc.IControllerCmd=} [properties] Properties to set
     */
    function ControllerCmd(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 目标控制器ID
     * @member {number} ControllerID
     * @memberof doc.ControllerCmd
     * @instance
     */
    ControllerCmd.prototype.ControllerID = 0

    /**
     * 在上级控制器的通道号，中继时需要填写，基站本地通道时为0
     * @member {number} ControllerChannelNo
     * @memberof doc.ControllerCmd
     * @instance
     */
    ControllerCmd.prototype.ControllerChannelNo = 0

    /**
     * 新监听信道号
     * @member {number} NewChannelNo
     * @memberof doc.ControllerCmd
     * @instance
     */
    ControllerCmd.prototype.NewChannelNo = 0

    /**
     * 命令
     * 13,14
     * @member {number} Cmd
     * @memberof doc.ControllerCmd
     * @instance
     */
    ControllerCmd.prototype.Cmd = 0

    /**
     * Encodes the specified ControllerCmd message. Does not implicitly {@link doc.ControllerCmd.verify|verify} messages.
     * @function encode
     * @memberof doc.ControllerCmd
     * @static
     * @param {doc.IControllerCmd} message ControllerCmd message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    ControllerCmd.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (
        message.ControllerID != null &&
        Object.hasOwnProperty.call(message, 'ControllerID')
      )
        writer.uint32(/* id 1, wireType 0 =*/ 8).sint32(message.ControllerID)
      if (
        message.ControllerChannelNo != null &&
        Object.hasOwnProperty.call(message, 'ControllerChannelNo')
      )
        writer
          .uint32(/* id 2, wireType 0 =*/ 16)
          .sint32(message.ControllerChannelNo)
      if (
        message.NewChannelNo != null &&
        Object.hasOwnProperty.call(message, 'NewChannelNo')
      )
        writer.uint32(/* id 4, wireType 0 =*/ 32).sint32(message.NewChannelNo)
      if (message.Cmd != null && Object.hasOwnProperty.call(message, 'Cmd'))
        writer.uint32(/* id 5, wireType 0 =*/ 40).sint32(message.Cmd)
      return writer
    }

    /**
     * Decodes a ControllerCmd message from the specified reader or buffer.
     * @function decode
     * @memberof doc.ControllerCmd
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {doc.ControllerCmd} ControllerCmd
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    ControllerCmd.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.doc.ControllerCmd()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.ControllerID = reader.sint32()
            break
          case 2:
            message.ControllerChannelNo = reader.sint32()
            break
          case 4:
            message.NewChannelNo = reader.sint32()
            break
          case 5:
            message.Cmd = reader.sint32()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return ControllerCmd
  })()

  doc.UpdataMarkerParam = (function () {
    /**
     * Properties of an UpdataMarkerParam.
     * @memberof doc
     * @interface IUpdataMarkerParam
     * @property {number|null} [controllerID] 系统设置上级控制器HWID,用于后台缓存找不到markerinfo时使用
     * @property {bysdb.IDbBysMarker|null} [markerData] 界桩更新后的参数
     */

    /**
     * Constructs a new UpdataMarkerParam.
     * @memberof doc
     * @classdesc Represents an UpdataMarkerParam.
     * @implements IUpdataMarkerParam
     * @constructor
     * @param {doc.IUpdataMarkerParam=} [properties] Properties to set
     */
    function UpdataMarkerParam(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 系统设置上级控制器HWID,用于后台缓存找不到markerinfo时使用
     * @member {number} controllerID
     * @memberof doc.UpdataMarkerParam
     * @instance
     */
    UpdataMarkerParam.prototype.controllerID = 0

    /**
     * 界桩更新后的参数
     * @member {bysdb.IDbBysMarker|null|undefined} markerData
     * @memberof doc.UpdataMarkerParam
     * @instance
     */
    UpdataMarkerParam.prototype.markerData = null

    /**
     * Encodes the specified UpdataMarkerParam message. Does not implicitly {@link doc.UpdataMarkerParam.verify|verify} messages.
     * @function encode
     * @memberof doc.UpdataMarkerParam
     * @static
     * @param {doc.IUpdataMarkerParam} message UpdataMarkerParam message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    UpdataMarkerParam.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (
        message.controllerID != null &&
        Object.hasOwnProperty.call(message, 'controllerID')
      )
        writer.uint32(/* id 1, wireType 0 =*/ 8).int32(message.controllerID)
      if (
        message.markerData != null &&
        Object.hasOwnProperty.call(message, 'markerData')
      )
        $root.bysdb.DbBysMarker.encode(
          message.markerData,
          writer.uint32(/* id 2, wireType 2 =*/ 18).fork(),
        ).ldelim()
      return writer
    }

    /**
     * Decodes an UpdataMarkerParam message from the specified reader or buffer.
     * @function decode
     * @memberof doc.UpdataMarkerParam
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {doc.UpdataMarkerParam} UpdataMarkerParam
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    UpdataMarkerParam.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.doc.UpdataMarkerParam()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.controllerID = reader.int32()
            break
          case 2:
            message.markerData = $root.bysdb.DbBysMarker.decode(
              reader,
              reader.uint32(),
            )
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return UpdataMarkerParam
  })()

  doc.ControllerTarget = (function () {
    /**
     * Properties of a ControllerTarget.
     * @memberof doc
     * @interface IControllerTarget
     * @property {number|null} [StationID] 基站控制器ID
     * @property {number|null} [ControllerChannelNo] 在上级控制器的通道号，中继时需要填写，基站本地通道时为0
     */

    /**
     * Constructs a new ControllerTarget.
     * @memberof doc
     * @classdesc 更新控制器新服务器的地址。如果更改的地址错误，需要维护人员到现场修改控制器配置
     * @implements IControllerTarget
     * @constructor
     * @param {doc.IControllerTarget=} [properties] Properties to set
     */
    function ControllerTarget(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 基站控制器ID
     * @member {number} StationID
     * @memberof doc.ControllerTarget
     * @instance
     */
    ControllerTarget.prototype.StationID = 0

    /**
     * 在上级控制器的通道号，中继时需要填写，基站本地通道时为0
     * @member {number} ControllerChannelNo
     * @memberof doc.ControllerTarget
     * @instance
     */
    ControllerTarget.prototype.ControllerChannelNo = 0

    /**
     * Encodes the specified ControllerTarget message. Does not implicitly {@link doc.ControllerTarget.verify|verify} messages.
     * @function encode
     * @memberof doc.ControllerTarget
     * @static
     * @param {doc.IControllerTarget} message ControllerTarget message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    ControllerTarget.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (
        message.StationID != null &&
        Object.hasOwnProperty.call(message, 'StationID')
      )
        writer.uint32(/* id 1, wireType 0 =*/ 8).sint32(message.StationID)
      if (
        message.ControllerChannelNo != null &&
        Object.hasOwnProperty.call(message, 'ControllerChannelNo')
      )
        writer
          .uint32(/* id 2, wireType 0 =*/ 16)
          .sint32(message.ControllerChannelNo)
      return writer
    }

    /**
     * Decodes a ControllerTarget message from the specified reader or buffer.
     * @function decode
     * @memberof doc.ControllerTarget
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {doc.ControllerTarget} ControllerTarget
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    ControllerTarget.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.doc.ControllerTarget()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.StationID = reader.sint32()
            break
          case 2:
            message.ControllerChannelNo = reader.sint32()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return ControllerTarget
  })()

  doc.ControllerNewServerAddr = (function () {
    /**
     * Properties of a ControllerNewServerAddr.
     * @memberof doc
     * @interface IControllerNewServerAddr
     * @property {Array.<doc.IControllerTarget>|null} [Target] 基站控制器ID
     * @property {string|null} [Ip] 新服务器IP地址
     * @property {number|null} [Port] 新服务器端口
     */

    /**
     * Constructs a new ControllerNewServerAddr.
     * @memberof doc
     * @classdesc 控制器回应对应 controller.proto ： BControllerNewServerAddr
     * bmsg.cmd=15 bmsg.Res=1
     * Nats事件主题：device.OrgRID.ControllerID.
     * @implements IControllerNewServerAddr
     * @constructor
     * @param {doc.IControllerNewServerAddr=} [properties] Properties to set
     */
    function ControllerNewServerAddr(properties) {
      this.Target = []
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 基站控制器ID
     * @member {Array.<doc.IControllerTarget>} Target
     * @memberof doc.ControllerNewServerAddr
     * @instance
     */
    ControllerNewServerAddr.prototype.Target = $util.emptyArray

    /**
     * 新服务器IP地址
     * @member {string} Ip
     * @memberof doc.ControllerNewServerAddr
     * @instance
     */
    ControllerNewServerAddr.prototype.Ip = ''

    /**
     * 新服务器端口
     * @member {number} Port
     * @memberof doc.ControllerNewServerAddr
     * @instance
     */
    ControllerNewServerAddr.prototype.Port = 0

    /**
     * Encodes the specified ControllerNewServerAddr message. Does not implicitly {@link doc.ControllerNewServerAddr.verify|verify} messages.
     * @function encode
     * @memberof doc.ControllerNewServerAddr
     * @static
     * @param {doc.IControllerNewServerAddr} message ControllerNewServerAddr message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    ControllerNewServerAddr.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (message.Target != null && message.Target.length)
        for (let i = 0; i < message.Target.length; ++i)
          $root.doc.ControllerTarget.encode(
            message.Target[i],
            writer.uint32(/* id 1, wireType 2 =*/ 10).fork(),
          ).ldelim()
      if (message.Ip != null && Object.hasOwnProperty.call(message, 'Ip'))
        writer.uint32(/* id 2, wireType 2 =*/ 18).string(message.Ip)
      if (message.Port != null && Object.hasOwnProperty.call(message, 'Port'))
        writer.uint32(/* id 3, wireType 0 =*/ 24).sint32(message.Port)
      return writer
    }

    /**
     * Decodes a ControllerNewServerAddr message from the specified reader or buffer.
     * @function decode
     * @memberof doc.ControllerNewServerAddr
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {doc.ControllerNewServerAddr} ControllerNewServerAddr
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    ControllerNewServerAddr.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.doc.ControllerNewServerAddr()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            if (!(message.Target && message.Target.length)) message.Target = []
            message.Target.push(
              $root.doc.ControllerTarget.decode(reader, reader.uint32()),
            )
            break
          case 2:
            message.Ip = reader.string()
            break
          case 3:
            message.Port = reader.sint32()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return ControllerNewServerAddr
  })()

  doc.MarkerOrControllerId = (function () {
    /**
     * Properties of a MarkerOrControllerId.
     * @memberof doc
     * @interface IMarkerOrControllerId
     * @property {number|null} [id] req: 界桩/控制器id  resp: 上级控制器id
     * @property {number|null} [controllerChannelNo] resp: 上级控制器通道
     * @property {boolean|null} [isMarker] 界桩=1,控制器=2
     */

    /**
     * Constructs a new MarkerOrControllerId.
     * @memberof doc
     * @classdesc 查询界桩/控制器链路通道
     * @implements IMarkerOrControllerId
     * @constructor
     * @param {doc.IMarkerOrControllerId=} [properties] Properties to set
     */
    function MarkerOrControllerId(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * req: 界桩/控制器id  resp: 上级控制器id
     * @member {number} id
     * @memberof doc.MarkerOrControllerId
     * @instance
     */
    MarkerOrControllerId.prototype.id = 0

    /**
     * resp: 上级控制器通道
     * @member {number} controllerChannelNo
     * @memberof doc.MarkerOrControllerId
     * @instance
     */
    MarkerOrControllerId.prototype.controllerChannelNo = 0

    /**
     * 界桩=1,控制器=2
     * @member {boolean} isMarker
     * @memberof doc.MarkerOrControllerId
     * @instance
     */
    MarkerOrControllerId.prototype.isMarker = false

    /**
     * Encodes the specified MarkerOrControllerId message. Does not implicitly {@link doc.MarkerOrControllerId.verify|verify} messages.
     * @function encode
     * @memberof doc.MarkerOrControllerId
     * @static
     * @param {doc.IMarkerOrControllerId} message MarkerOrControllerId message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    MarkerOrControllerId.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (message.id != null && Object.hasOwnProperty.call(message, 'id'))
        writer.uint32(/* id 1, wireType 0 =*/ 8).int32(message.id)
      if (
        message.controllerChannelNo != null &&
        Object.hasOwnProperty.call(message, 'controllerChannelNo')
      )
        writer
          .uint32(/* id 2, wireType 0 =*/ 16)
          .int32(message.controllerChannelNo)
      if (
        message.isMarker != null &&
        Object.hasOwnProperty.call(message, 'isMarker')
      )
        writer.uint32(/* id 3, wireType 0 =*/ 24).bool(message.isMarker)
      return writer
    }

    /**
     * Decodes a MarkerOrControllerId message from the specified reader or buffer.
     * @function decode
     * @memberof doc.MarkerOrControllerId
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {doc.MarkerOrControllerId} MarkerOrControllerId
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    MarkerOrControllerId.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.doc.MarkerOrControllerId()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.id = reader.int32()
            break
          case 2:
            message.controllerChannelNo = reader.int32()
            break
          case 3:
            message.isMarker = reader.bool()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return MarkerOrControllerId
  })()

  doc.RpcBysApi = (function () {
    /**
     * Constructs a new RpcBysApi service.
     * @memberof doc
     * @classdesc bys 常规 api
     * @extends $protobuf.rpc.Service
     * @constructor
     * @param {$protobuf.RPCImpl} rpcImpl RPC implementation
     * @param {boolean} [requestDelimited=false] Whether requests are length-delimited
     * @param {boolean} [responseDelimited=false] Whether responses are length-delimited
     */
    function RpcBysApi(rpcImpl, requestDelimited, responseDelimited) {
      $protobuf.rpc.Service.call(
        this,
        rpcImpl,
        requestDelimited,
        responseDelimited,
      )
    }

    ;(RpcBysApi.prototype = Object.create(
      $protobuf.rpc.Service.prototype,
    )).constructor = RpcBysApi

    /**
     * Callback as used by {@link doc.RpcBysApi#onlineController}.
     * @memberof doc.RpcBysApi
     * @typedef OnlineControllerCallback
     * @type {function}
     * @param {Error|null} error Error, if any
     * @param {doc.OnlineControllerInfo} [response] OnlineControllerInfo
     */

    /**
     * 获取有权限的在线的控制器列表信息
     * @function onlineController
     * @memberof doc.RpcBysApi
     * @instance
     * @param {yrpcmsg.IYempty} request Yempty message or plain object
     * @param {doc.RpcBysApi.OnlineControllerCallback} callback Node-style callback called with the error, if any, and OnlineControllerInfo
     * @returns {undefined}
     * @variation 1
     */
    Object.defineProperty(
      (RpcBysApi.prototype.onlineController = function onlineController(
        request,
        callback,
      ) {
        return this.rpcCall(
          onlineController,
          $root.yrpcmsg.Yempty,
          $root.doc.OnlineControllerInfo,
          request,
          callback,
        )
      }),
      'name',
      { value: 'OnlineController' },
    )

    /**
     * 获取有权限的在线的控制器列表信息
     * @function onlineController
     * @memberof doc.RpcBysApi
     * @instance
     * @param {yrpcmsg.IYempty} request Yempty message or plain object
     * @returns {Promise<doc.OnlineControllerInfo>} Promise
     * @variation 2
     */

    /**
     * Callback as used by {@link doc.RpcBysApi#queryMarkerQueueNo}.
     * @memberof doc.RpcBysApi
     * @typedef QueryMarkerQueueNoCallback
     * @type {function}
     * @param {Error|null} error Error, if any
     * @param {doc.MarkerQueueNoInfo} [response] MarkerQueueNoInfo
     */

    /**
     * 获取或查询控制器下界桩排队号
     * @function queryMarkerQueueNo
     * @memberof doc.RpcBysApi
     * @instance
     * @param {doc.IMarkerQueueNoInfo} request MarkerQueueNoInfo message or plain object
     * @param {doc.RpcBysApi.QueryMarkerQueueNoCallback} callback Node-style callback called with the error, if any, and MarkerQueueNoInfo
     * @returns {undefined}
     * @variation 1
     */
    Object.defineProperty(
      (RpcBysApi.prototype.queryMarkerQueueNo = function queryMarkerQueueNo(
        request,
        callback,
      ) {
        return this.rpcCall(
          queryMarkerQueueNo,
          $root.doc.MarkerQueueNoInfo,
          $root.doc.MarkerQueueNoInfo,
          request,
          callback,
        )
      }),
      'name',
      { value: 'QueryMarkerQueueNo' },
    )

    /**
     * 获取或查询控制器下界桩排队号
     * @function queryMarkerQueueNo
     * @memberof doc.RpcBysApi
     * @instance
     * @param {doc.IMarkerQueueNoInfo} request MarkerQueueNoInfo message or plain object
     * @returns {Promise<doc.MarkerQueueNoInfo>} Promise
     * @variation 2
     */

    /**
     * Callback as used by {@link doc.RpcBysApi#cancelEmergency}.
     * @memberof doc.RpcBysApi
     * @typedef CancelEmergencyCallback
     * @type {function}
     * @param {Error|null} error Error, if any
     * @param {yrpcmsg.Yempty} [response] Yempty
     */

    /**
     * 界桩取消报警
     * @function cancelEmergency
     * @memberof doc.RpcBysApi
     * @instance
     * @param {doc.IMarkerInfo} request MarkerInfo message or plain object
     * @param {doc.RpcBysApi.CancelEmergencyCallback} callback Node-style callback called with the error, if any, and Yempty
     * @returns {undefined}
     * @variation 1
     */
    Object.defineProperty(
      (RpcBysApi.prototype.cancelEmergency = function cancelEmergency(
        request,
        callback,
      ) {
        return this.rpcCall(
          cancelEmergency,
          $root.doc.MarkerInfo,
          $root.yrpcmsg.Yempty,
          request,
          callback,
        )
      }),
      'name',
      { value: 'CancelEmergency' },
    )

    /**
     * 界桩取消报警
     * @function cancelEmergency
     * @memberof doc.RpcBysApi
     * @instance
     * @param {doc.IMarkerInfo} request MarkerInfo message or plain object
     * @returns {Promise<yrpcmsg.Yempty>} Promise
     * @variation 2
     */

    /**
     * Callback as used by {@link doc.RpcBysApi#send0xD0}.
     * @memberof doc.RpcBysApi
     * @typedef Send0xD0Callback
     * @type {function}
     * @param {Error|null} error Error, if any
     * @param {yrpcmsg.Yempty} [response] Yempty
     */

    /**
     * 界桩下发参数
     * @function send0xD0
     * @memberof doc.RpcBysApi
     * @instance
     * @param {doc.IMarkerInfo} request MarkerInfo message or plain object
     * @param {doc.RpcBysApi.Send0xD0Callback} callback Node-style callback called with the error, if any, and Yempty
     * @returns {undefined}
     * @variation 1
     */
    Object.defineProperty(
      (RpcBysApi.prototype.send0xD0 = function send0xD0(request, callback) {
        return this.rpcCall(
          send0xD0,
          $root.doc.MarkerInfo,
          $root.yrpcmsg.Yempty,
          request,
          callback,
        )
      }),
      'name',
      { value: 'Send0xD0' },
    )

    /**
     * 界桩下发参数
     * @function send0xD0
     * @memberof doc.RpcBysApi
     * @instance
     * @param {doc.IMarkerInfo} request MarkerInfo message or plain object
     * @returns {Promise<yrpcmsg.Yempty>} Promise
     * @variation 2
     */

    /**
     * Callback as used by {@link doc.RpcBysApi#markerLatestInfo}.
     * @memberof doc.RpcBysApi
     * @typedef MarkerLatestInfoCallback
     * @type {function}
     * @param {Error|null} error Error, if any
     * @param {doc.MarkerInfo} [response] MarkerInfo
     */

    /**
     * 获取界桩最后数据，
     * 参数MarkerInfo需要填写MarkerID,MarkerCmdTime
     * MarkerCmdTime作为查询数据库时最早的时间(缓存失效的情况)，第一个必须填写，以后的不需要
     * @function markerLatestInfo
     * @memberof doc.RpcBysApi
     * @instance
     * @param {doc.IMarkerInfo} request MarkerInfo message or plain object
     * @param {doc.RpcBysApi.MarkerLatestInfoCallback} callback Node-style callback called with the error, if any, and MarkerInfo
     * @returns {undefined}
     * @variation 1
     */
    Object.defineProperty(
      (RpcBysApi.prototype.markerLatestInfo = function markerLatestInfo(
        request,
        callback,
      ) {
        return this.rpcCall(
          markerLatestInfo,
          $root.doc.MarkerInfo,
          $root.doc.MarkerInfo,
          request,
          callback,
        )
      }),
      'name',
      { value: 'MarkerLatestInfo' },
    )

    /**
     * 获取界桩最后数据，
     * 参数MarkerInfo需要填写MarkerID,MarkerCmdTime
     * MarkerCmdTime作为查询数据库时最早的时间(缓存失效的情况)，第一个必须填写，以后的不需要
     * @function markerLatestInfo
     * @memberof doc.RpcBysApi
     * @instance
     * @param {doc.IMarkerInfo} request MarkerInfo message or plain object
     * @returns {Promise<doc.MarkerInfo>} Promise
     * @variation 2
     */

    /**
     * Callback as used by {@link doc.RpcBysApi#sendControllerCmd}.
     * @memberof doc.RpcBysApi
     * @typedef SendControllerCmdCallback
     * @type {function}
     * @param {Error|null} error Error, if any
     * @param {yrpcmsg.Yempty} [response] Yempty
     */

    /**
     * 修改基站/中继默认信道号
     * @function sendControllerCmd
     * @memberof doc.RpcBysApi
     * @instance
     * @param {doc.IControllerCmd} request ControllerCmd message or plain object
     * @param {doc.RpcBysApi.SendControllerCmdCallback} callback Node-style callback called with the error, if any, and Yempty
     * @returns {undefined}
     * @variation 1
     */
    Object.defineProperty(
      (RpcBysApi.prototype.sendControllerCmd = function sendControllerCmd(
        request,
        callback,
      ) {
        return this.rpcCall(
          sendControllerCmd,
          $root.doc.ControllerCmd,
          $root.yrpcmsg.Yempty,
          request,
          callback,
        )
      }),
      'name',
      { value: 'SendControllerCmd' },
    )

    /**
     * 修改基站/中继默认信道号
     * @function sendControllerCmd
     * @memberof doc.RpcBysApi
     * @instance
     * @param {doc.IControllerCmd} request ControllerCmd message or plain object
     * @returns {Promise<yrpcmsg.Yempty>} Promise
     * @variation 2
     */

    /**
     * Callback as used by {@link doc.RpcBysApi#updateMarkerParamter}.
     * @memberof doc.RpcBysApi
     * @typedef UpdateMarkerParamterCallback
     * @type {function}
     * @param {Error|null} error Error, if any
     * @param {yrpcmsg.Yempty} [response] Yempty
     */

    /**
     * 下发指令 更新界桩参数
     * @function updateMarkerParamter
     * @memberof doc.RpcBysApi
     * @instance
     * @param {doc.IUpdataMarkerParam} request UpdataMarkerParam message or plain object
     * @param {doc.RpcBysApi.UpdateMarkerParamterCallback} callback Node-style callback called with the error, if any, and Yempty
     * @returns {undefined}
     * @variation 1
     */
    Object.defineProperty(
      (RpcBysApi.prototype.updateMarkerParamter = function updateMarkerParamter(
        request,
        callback,
      ) {
        return this.rpcCall(
          updateMarkerParamter,
          $root.doc.UpdataMarkerParam,
          $root.yrpcmsg.Yempty,
          request,
          callback,
        )
      }),
      'name',
      { value: 'UpdateMarkerParamter' },
    )

    /**
     * 下发指令 更新界桩参数
     * @function updateMarkerParamter
     * @memberof doc.RpcBysApi
     * @instance
     * @param {doc.IUpdataMarkerParam} request UpdataMarkerParam message or plain object
     * @returns {Promise<yrpcmsg.Yempty>} Promise
     * @variation 2
     */

    /**
     * Callback as used by {@link doc.RpcBysApi#pingController}.
     * @memberof doc.RpcBysApi
     * @typedef PingControllerCallback
     * @type {function}
     * @param {Error|null} error Error, if any
     * @param {yrpcmsg.Yempty} [response] Yempty
     */

    /**
     * 实时ping控制器
     * ControllerCmd.ControllerID为基站ID, Ping中继时为中继控制器的上级控制器ID
     * ControllerCmd.ControllerChannelNo 通道，ping基站时为0,ping中继时为中继对应基站下的通道号
     * ControllerCmd.NewChannelNo,ping基站时为0,ping中继时为中继的ID
     * @function pingController
     * @memberof doc.RpcBysApi
     * @instance
     * @param {doc.IControllerCmd} request ControllerCmd message or plain object
     * @param {doc.RpcBysApi.PingControllerCallback} callback Node-style callback called with the error, if any, and Yempty
     * @returns {undefined}
     * @variation 1
     */
    Object.defineProperty(
      (RpcBysApi.prototype.pingController = function pingController(
        request,
        callback,
      ) {
        return this.rpcCall(
          pingController,
          $root.doc.ControllerCmd,
          $root.yrpcmsg.Yempty,
          request,
          callback,
        )
      }),
      'name',
      { value: 'PingController' },
    )

    /**
     * 实时ping控制器
     * ControllerCmd.ControllerID为基站ID, Ping中继时为中继控制器的上级控制器ID
     * ControllerCmd.ControllerChannelNo 通道，ping基站时为0,ping中继时为中继对应基站下的通道号
     * ControllerCmd.NewChannelNo,ping基站时为0,ping中继时为中继的ID
     * @function pingController
     * @memberof doc.RpcBysApi
     * @instance
     * @param {doc.IControllerCmd} request ControllerCmd message or plain object
     * @returns {Promise<yrpcmsg.Yempty>} Promise
     * @variation 2
     */

    /**
     * Callback as used by {@link doc.RpcBysApi#getRolePermissions}.
     * @memberof doc.RpcBysApi
     * @typedef GetRolePermissionsCallback
     * @type {function}
     * @param {Error|null} error Error, if any
     * @param {user.DbRolePermissionList} [response] DbRolePermissionList
     */

    /**
     * 请求角色拥有的权限
     * @function getRolePermissions
     * @memberof doc.RpcBysApi
     * @instance
     * @param {user.IDbRole} request DbRole message or plain object
     * @param {doc.RpcBysApi.GetRolePermissionsCallback} callback Node-style callback called with the error, if any, and DbRolePermissionList
     * @returns {undefined}
     * @variation 1
     */
    Object.defineProperty(
      (RpcBysApi.prototype.getRolePermissions = function getRolePermissions(
        request,
        callback,
      ) {
        return this.rpcCall(
          getRolePermissions,
          $root.user.DbRole,
          $root.user.DbRolePermissionList,
          request,
          callback,
        )
      }),
      'name',
      { value: 'GetRolePermissions' },
    )

    /**
     * 请求角色拥有的权限
     * @function getRolePermissions
     * @memberof doc.RpcBysApi
     * @instance
     * @param {user.IDbRole} request DbRole message or plain object
     * @returns {Promise<user.DbRolePermissionList>} Promise
     * @variation 2
     */

    /**
     * Callback as used by {@link doc.RpcBysApi#updateControllerServerAddr}.
     * @memberof doc.RpcBysApi
     * @typedef UpdateControllerServerAddrCallback
     * @type {function}
     * @param {Error|null} error Error, if any
     * @param {yrpcmsg.Yempty} [response] Yempty
     */

    /**
     * 更新指定控制器连接的服务器地址
     * @function updateControllerServerAddr
     * @memberof doc.RpcBysApi
     * @instance
     * @param {doc.IControllerNewServerAddr} request ControllerNewServerAddr message or plain object
     * @param {doc.RpcBysApi.UpdateControllerServerAddrCallback} callback Node-style callback called with the error, if any, and Yempty
     * @returns {undefined}
     * @variation 1
     */
    Object.defineProperty(
      (RpcBysApi.prototype.updateControllerServerAddr =
        function updateControllerServerAddr(request, callback) {
          return this.rpcCall(
            updateControllerServerAddr,
            $root.doc.ControllerNewServerAddr,
            $root.yrpcmsg.Yempty,
            request,
            callback,
          )
        }),
      'name',
      { value: 'UpdateControllerServerAddr' },
    )

    /**
     * 更新指定控制器连接的服务器地址
     * @function updateControllerServerAddr
     * @memberof doc.RpcBysApi
     * @instance
     * @param {doc.IControllerNewServerAddr} request ControllerNewServerAddr message or plain object
     * @returns {Promise<yrpcmsg.Yempty>} Promise
     * @variation 2
     */

    /**
     * Callback as used by {@link doc.RpcBysApi#queryMarkerOrControllerLinkList}.
     * @memberof doc.RpcBysApi
     * @typedef QueryMarkerOrControllerLinkListCallback
     * @type {function}
     * @param {Error|null} error Error, if any
     * @param {doc.MarkerOrControllerId} [response] MarkerOrControllerId
     */

    /**
     * 查询界桩/控制器链路信息
     * @function queryMarkerOrControllerLinkList
     * @memberof doc.RpcBysApi
     * @instance
     * @param {doc.IMarkerOrControllerId} request MarkerOrControllerId message or plain object
     * @param {doc.RpcBysApi.QueryMarkerOrControllerLinkListCallback} callback Node-style callback called with the error, if any, and MarkerOrControllerId
     * @returns {undefined}
     * @variation 1
     */
    Object.defineProperty(
      (RpcBysApi.prototype.queryMarkerOrControllerLinkList =
        function queryMarkerOrControllerLinkList(request, callback) {
          return this.rpcCall(
            queryMarkerOrControllerLinkList,
            $root.doc.MarkerOrControllerId,
            $root.doc.MarkerOrControllerId,
            request,
            callback,
          )
        }),
      'name',
      { value: 'QueryMarkerOrControllerLinkList' },
    )

    /**
     * 查询界桩/控制器链路信息
     * @function queryMarkerOrControllerLinkList
     * @memberof doc.RpcBysApi
     * @instance
     * @param {doc.IMarkerOrControllerId} request MarkerOrControllerId message or plain object
     * @returns {Promise<doc.MarkerOrControllerId>} Promise
     * @variation 2
     */

    /**
     * Callback as used by {@link doc.RpcBysApi#markerRemoteKillOrActive}.
     * @memberof doc.RpcBysApi
     * @typedef MarkerRemoteKillOrActiveCallback
     * @type {function}
     * @param {Error|null} error Error, if any
     * @param {bysproto.Bmsg} [response] Bmsg
     */

    /**
     * 4g界桩遥毙/遥晕/遥活
     * 应答的Bmsg中，Res=1 表示下发成功，Res=2 表示已缓存到数据库中，等待界桩唤醒再下发
     * @function markerRemoteKillOrActive
     * @memberof doc.RpcBysApi
     * @instance
     * @param {bysproto.IBShutDown} request BShutDown message or plain object
     * @param {doc.RpcBysApi.MarkerRemoteKillOrActiveCallback} callback Node-style callback called with the error, if any, and Bmsg
     * @returns {undefined}
     * @variation 1
     */
    Object.defineProperty(
      (RpcBysApi.prototype.markerRemoteKillOrActive =
        function markerRemoteKillOrActive(request, callback) {
          return this.rpcCall(
            markerRemoteKillOrActive,
            $root.bysproto.BShutDown,
            $root.bysproto.Bmsg,
            request,
            callback,
          )
        }),
      'name',
      { value: 'MarkerRemoteKillOrActive' },
    )

    /**
     * 4g界桩遥毙/遥晕/遥活
     * 应答的Bmsg中，Res=1 表示下发成功，Res=2 表示已缓存到数据库中，等待界桩唤醒再下发
     * @function markerRemoteKillOrActive
     * @memberof doc.RpcBysApi
     * @instance
     * @param {bysproto.IBShutDown} request BShutDown message or plain object
     * @returns {Promise<bysproto.Bmsg>} Promise
     * @variation 2
     */

    /**
     * Callback as used by {@link doc.RpcBysApi#markerUploadNFCRecord}.
     * @memberof doc.RpcBysApi
     * @typedef MarkerUploadNFCRecordCallback
     * @type {function}
     * @param {Error|null} error Error, if any
     * @param {yrpcmsg.Yempty} [response] Yempty
     */

    /**
     * 4g界桩NFC巡查打卡上传
     * @function markerUploadNFCRecord
     * @memberof doc.RpcBysApi
     * @instance
     * @param {bysdb.IDbMarkerPatrolHistory} request DbMarkerPatrolHistory message or plain object
     * @param {doc.RpcBysApi.MarkerUploadNFCRecordCallback} callback Node-style callback called with the error, if any, and Yempty
     * @returns {undefined}
     * @variation 1
     */
    Object.defineProperty(
      (RpcBysApi.prototype.markerUploadNFCRecord =
        function markerUploadNFCRecord(request, callback) {
          return this.rpcCall(
            markerUploadNFCRecord,
            $root.bysdb.DbMarkerPatrolHistory,
            $root.yrpcmsg.Yempty,
            request,
            callback,
          )
        }),
      'name',
      { value: 'MarkerUploadNFCRecord' },
    )

    /**
     * 4g界桩NFC巡查打卡上传
     * @function markerUploadNFCRecord
     * @memberof doc.RpcBysApi
     * @instance
     * @param {bysdb.IDbMarkerPatrolHistory} request DbMarkerPatrolHistory message or plain object
     * @returns {Promise<yrpcmsg.Yempty>} Promise
     * @variation 2
     */

    /**
     * Callback as used by {@link doc.RpcBysApi#nFCPatrolStatistics}.
     * @memberof doc.RpcBysApi
     * @typedef NFCPatrolStatisticsCallback
     * @type {function}
     * @param {Error|null} error Error, if any
     * @param {doc.PatrolStatisticsQueryResult} [response] PatrolStatisticsQueryResult
     */

    /**
     * nfc巡查统计查询
     * @function nFCPatrolStatistics
     * @memberof doc.RpcBysApi
     * @instance
     * @param {doc.IPatrolStatisticsQuery} request PatrolStatisticsQuery message or plain object
     * @param {doc.RpcBysApi.NFCPatrolStatisticsCallback} callback Node-style callback called with the error, if any, and PatrolStatisticsQueryResult
     * @returns {undefined}
     * @variation 1
     */
    Object.defineProperty(
      (RpcBysApi.prototype.nFCPatrolStatistics = function nFCPatrolStatistics(
        request,
        callback,
      ) {
        return this.rpcCall(
          nFCPatrolStatistics,
          $root.doc.PatrolStatisticsQuery,
          $root.doc.PatrolStatisticsQueryResult,
          request,
          callback,
        )
      }),
      'name',
      { value: 'NFCPatrolStatistics' },
    )

    /**
     * nfc巡查统计查询
     * @function nFCPatrolStatistics
     * @memberof doc.RpcBysApi
     * @instance
     * @param {doc.IPatrolStatisticsQuery} request PatrolStatisticsQuery message or plain object
     * @returns {Promise<doc.PatrolStatisticsQueryResult>} Promise
     * @variation 2
     */

    return RpcBysApi
  })()

  doc.PatrolStatisticsQuery = (function () {
    /**
     * Properties of a PatrolStatisticsQuery.
     * @memberof doc
     * @interface IPatrolStatisticsQuery
     * @property {string|null} [StartTime] 开始时间(utc)， 格式：yyyy-mm-dd HH:MM:SS
     * @property {string|null} [EndTime] 结束时间(utc)
     * @property {string|null} [LineRid] 巡查路线RID
     */

    /**
     * Constructs a new PatrolStatisticsQuery.
     * @memberof doc
     * @classdesc 巡查统计条件
     * @implements IPatrolStatisticsQuery
     * @constructor
     * @param {doc.IPatrolStatisticsQuery=} [properties] Properties to set
     */
    function PatrolStatisticsQuery(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 开始时间(utc)， 格式：yyyy-mm-dd HH:MM:SS
     * @member {string} StartTime
     * @memberof doc.PatrolStatisticsQuery
     * @instance
     */
    PatrolStatisticsQuery.prototype.StartTime = ''

    /**
     * 结束时间(utc)
     * @member {string} EndTime
     * @memberof doc.PatrolStatisticsQuery
     * @instance
     */
    PatrolStatisticsQuery.prototype.EndTime = ''

    /**
     * 巡查路线RID
     * @member {string} LineRid
     * @memberof doc.PatrolStatisticsQuery
     * @instance
     */
    PatrolStatisticsQuery.prototype.LineRid = ''

    /**
     * Encodes the specified PatrolStatisticsQuery message. Does not implicitly {@link doc.PatrolStatisticsQuery.verify|verify} messages.
     * @function encode
     * @memberof doc.PatrolStatisticsQuery
     * @static
     * @param {doc.IPatrolStatisticsQuery} message PatrolStatisticsQuery message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    PatrolStatisticsQuery.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (
        message.StartTime != null &&
        Object.hasOwnProperty.call(message, 'StartTime')
      )
        writer.uint32(/* id 1, wireType 2 =*/ 10).string(message.StartTime)
      if (
        message.EndTime != null &&
        Object.hasOwnProperty.call(message, 'EndTime')
      )
        writer.uint32(/* id 2, wireType 2 =*/ 18).string(message.EndTime)
      if (
        message.LineRid != null &&
        Object.hasOwnProperty.call(message, 'LineRid')
      )
        writer.uint32(/* id 3, wireType 2 =*/ 26).string(message.LineRid)
      return writer
    }

    /**
     * Decodes a PatrolStatisticsQuery message from the specified reader or buffer.
     * @function decode
     * @memberof doc.PatrolStatisticsQuery
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {doc.PatrolStatisticsQuery} PatrolStatisticsQuery
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    PatrolStatisticsQuery.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.doc.PatrolStatisticsQuery()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.StartTime = reader.string()
            break
          case 2:
            message.EndTime = reader.string()
            break
          case 3:
            message.LineRid = reader.string()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return PatrolStatisticsQuery
  })()

  doc.PatrolCheckItem = (function () {
    /**
     * Properties of a PatrolCheckItem.
     * @memberof doc
     * @interface IPatrolCheckItem
     * @property {number|null} [MarkerHWID] 巡查的界桩的HWID
     * @property {string|null} [CheckTime] 巡查时间 格式：yyyy-mm-dd HH:MM:SS
     * @property {string|null} [CheckUserRID] 巡查用户
     * @property {number|null} [CheckResult] 巡查结果, 0:正常, 1: 未打卡
     */

    /**
     * Constructs a new PatrolCheckItem.
     * @memberof doc
     * @classdesc Represents a PatrolCheckItem.
     * @implements IPatrolCheckItem
     * @constructor
     * @param {doc.IPatrolCheckItem=} [properties] Properties to set
     */
    function PatrolCheckItem(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 巡查的界桩的HWID
     * @member {number} MarkerHWID
     * @memberof doc.PatrolCheckItem
     * @instance
     */
    PatrolCheckItem.prototype.MarkerHWID = 0

    /**
     * 巡查时间 格式：yyyy-mm-dd HH:MM:SS
     * @member {string} CheckTime
     * @memberof doc.PatrolCheckItem
     * @instance
     */
    PatrolCheckItem.prototype.CheckTime = ''

    /**
     * 巡查用户
     * @member {string} CheckUserRID
     * @memberof doc.PatrolCheckItem
     * @instance
     */
    PatrolCheckItem.prototype.CheckUserRID = ''

    /**
     * 巡查结果, 0:正常, 1: 未打卡
     * @member {number} CheckResult
     * @memberof doc.PatrolCheckItem
     * @instance
     */
    PatrolCheckItem.prototype.CheckResult = 0

    /**
     * Encodes the specified PatrolCheckItem message. Does not implicitly {@link doc.PatrolCheckItem.verify|verify} messages.
     * @function encode
     * @memberof doc.PatrolCheckItem
     * @static
     * @param {doc.IPatrolCheckItem} message PatrolCheckItem message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    PatrolCheckItem.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (
        message.MarkerHWID != null &&
        Object.hasOwnProperty.call(message, 'MarkerHWID')
      )
        writer.uint32(/* id 1, wireType 0 =*/ 8).int32(message.MarkerHWID)
      if (
        message.CheckTime != null &&
        Object.hasOwnProperty.call(message, 'CheckTime')
      )
        writer.uint32(/* id 2, wireType 2 =*/ 18).string(message.CheckTime)
      if (
        message.CheckUserRID != null &&
        Object.hasOwnProperty.call(message, 'CheckUserRID')
      )
        writer.uint32(/* id 3, wireType 2 =*/ 26).string(message.CheckUserRID)
      if (
        message.CheckResult != null &&
        Object.hasOwnProperty.call(message, 'CheckResult')
      )
        writer.uint32(/* id 4, wireType 0 =*/ 32).int32(message.CheckResult)
      return writer
    }

    /**
     * Decodes a PatrolCheckItem message from the specified reader or buffer.
     * @function decode
     * @memberof doc.PatrolCheckItem
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {doc.PatrolCheckItem} PatrolCheckItem
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    PatrolCheckItem.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.doc.PatrolCheckItem()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.MarkerHWID = reader.int32()
            break
          case 2:
            message.CheckTime = reader.string()
            break
          case 3:
            message.CheckUserRID = reader.string()
            break
          case 4:
            message.CheckResult = reader.int32()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return PatrolCheckItem
  })()

  doc.PatrolStatisticsQueryResult = (function () {
    /**
     * Properties of a PatrolStatisticsQueryResult.
     * @memberof doc
     * @interface IPatrolStatisticsQueryResult
     * @property {string|null} [LineRid] 巡查路线RID
     * @property {string|null} [RuleRID] 路线规则RID
     * @property {number|null} [ShouldCheckCount] 应查数目
     * @property {string|null} [CheckDate] 巡查日期 yyyy-mm-dd
     * @property {Array.<doc.IPatrolCheckItem>|null} [CheckResult] 巡查结果
     */

    /**
     * Constructs a new PatrolStatisticsQueryResult.
     * @memberof doc
     * @classdesc 巡查统计结果
     * @implements IPatrolStatisticsQueryResult
     * @constructor
     * @param {doc.IPatrolStatisticsQueryResult=} [properties] Properties to set
     */
    function PatrolStatisticsQueryResult(properties) {
      this.CheckResult = []
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 巡查路线RID
     * @member {string} LineRid
     * @memberof doc.PatrolStatisticsQueryResult
     * @instance
     */
    PatrolStatisticsQueryResult.prototype.LineRid = ''

    /**
     * 路线规则RID
     * @member {string} RuleRID
     * @memberof doc.PatrolStatisticsQueryResult
     * @instance
     */
    PatrolStatisticsQueryResult.prototype.RuleRID = ''

    /**
     * 应查数目
     * @member {number} ShouldCheckCount
     * @memberof doc.PatrolStatisticsQueryResult
     * @instance
     */
    PatrolStatisticsQueryResult.prototype.ShouldCheckCount = 0

    /**
     * 巡查日期 yyyy-mm-dd
     * @member {string} CheckDate
     * @memberof doc.PatrolStatisticsQueryResult
     * @instance
     */
    PatrolStatisticsQueryResult.prototype.CheckDate = ''

    /**
     * 巡查结果
     * @member {Array.<doc.IPatrolCheckItem>} CheckResult
     * @memberof doc.PatrolStatisticsQueryResult
     * @instance
     */
    PatrolStatisticsQueryResult.prototype.CheckResult = $util.emptyArray

    /**
     * Encodes the specified PatrolStatisticsQueryResult message. Does not implicitly {@link doc.PatrolStatisticsQueryResult.verify|verify} messages.
     * @function encode
     * @memberof doc.PatrolStatisticsQueryResult
     * @static
     * @param {doc.IPatrolStatisticsQueryResult} message PatrolStatisticsQueryResult message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    PatrolStatisticsQueryResult.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (
        message.LineRid != null &&
        Object.hasOwnProperty.call(message, 'LineRid')
      )
        writer.uint32(/* id 1, wireType 2 =*/ 10).string(message.LineRid)
      if (
        message.RuleRID != null &&
        Object.hasOwnProperty.call(message, 'RuleRID')
      )
        writer.uint32(/* id 2, wireType 2 =*/ 18).string(message.RuleRID)
      if (
        message.ShouldCheckCount != null &&
        Object.hasOwnProperty.call(message, 'ShouldCheckCount')
      )
        writer
          .uint32(/* id 3, wireType 0 =*/ 24)
          .sint32(message.ShouldCheckCount)
      if (
        message.CheckDate != null &&
        Object.hasOwnProperty.call(message, 'CheckDate')
      )
        writer.uint32(/* id 4, wireType 2 =*/ 34).string(message.CheckDate)
      if (message.CheckResult != null && message.CheckResult.length)
        for (let i = 0; i < message.CheckResult.length; ++i)
          $root.doc.PatrolCheckItem.encode(
            message.CheckResult[i],
            writer.uint32(/* id 5, wireType 2 =*/ 42).fork(),
          ).ldelim()
      return writer
    }

    /**
     * Decodes a PatrolStatisticsQueryResult message from the specified reader or buffer.
     * @function decode
     * @memberof doc.PatrolStatisticsQueryResult
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {doc.PatrolStatisticsQueryResult} PatrolStatisticsQueryResult
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    PatrolStatisticsQueryResult.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.doc.PatrolStatisticsQueryResult()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.LineRid = reader.string()
            break
          case 2:
            message.RuleRID = reader.string()
            break
          case 3:
            message.ShouldCheckCount = reader.sint32()
            break
          case 4:
            message.CheckDate = reader.string()
            break
          case 5:
            if (!(message.CheckResult && message.CheckResult.length))
              message.CheckResult = []
            message.CheckResult.push(
              $root.doc.PatrolCheckItem.decode(reader, reader.uint32()),
            )
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return PatrolStatisticsQueryResult
  })()

  return doc
})())

export const yrpcmsg = ($root.yrpcmsg = (() => {
  /**
   * Namespace yrpcmsg.
   * @exports yrpcmsg
   * @namespace
   */
  const yrpcmsg = $root.yrpcmsg || {}

  yrpcmsg.Ymsg = (function () {
    /**
     * Properties of a Ymsg.
     * @memberof yrpcmsg
     * @interface IYmsg
     * @property {number|null} [Len] 整个rpc msg的长度，不包含此字段
     * 虽然这个长度可以很长，但是为了避免大包阻塞其它操作，通常都要限制长度,采用分包多发机制
     * 当使用基于包的传输通道时(udp/kcp/websocket)，此值可能为0(此时长度为收到的整个包的长度)
     * @property {number|null} [Cmd] rpc command,rpc的命令和option
     * b15-b0(uint16):低16为rpc命令
     * b19-b16:body压缩方式 0:无压缩 1:lz4 2:zlib inflate/deflate
     * b23-b20:optbin压缩方式 0:无压缩 1:lz4 2:zlib inflate/deflate
     * b31-b24: not used
     * @property {Uint8Array|null} [Sid] session id，登录后一定会有,用于后台区分不同的用户请求
     * @property {number|null} [Cid] rpc call id,给分辨不同的rpc调用使用,调用方增1循环使用
     * @property {number|null} [No] rpc no,从0开始增1使用,用于区分收到重复的包,特别是udp的情况下
     * @property {number|null} [Res] response code
     * @property {Uint8Array|null} [Body] msg body
     * @property {string|null} [Optstr] optional str parameter,额外的信息,一般不会有,有些特殊命令里面可能用到
     * @property {Uint8Array|null} [Optbin] optional binary parameter,额外的信息,一般不会有,有些特殊命令里面可能用到
     * @property {yrpcmsg.IMeta|null} [MetaInfo] optional grpc meta
     */

    /**
     * Constructs a new Ymsg.
     * @memberof yrpcmsg
     * @classdesc 系统中所有的消息交互底层都以此为包装
     * ymsg multiline comment
     * @implements IYmsg
     * @constructor
     * @param {yrpcmsg.IYmsg=} [properties] Properties to set
     */
    function Ymsg(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 整个rpc msg的长度，不包含此字段
     * 虽然这个长度可以很长，但是为了避免大包阻塞其它操作，通常都要限制长度,采用分包多发机制
     * 当使用基于包的传输通道时(udp/kcp/websocket)，此值可能为0(此时长度为收到的整个包的长度)
     * @member {number} Len
     * @memberof yrpcmsg.Ymsg
     * @instance
     */
    Ymsg.prototype.Len = 0

    /**
     * rpc command,rpc的命令和option
     * b15-b0(uint16):低16为rpc命令
     * b19-b16:body压缩方式 0:无压缩 1:lz4 2:zlib inflate/deflate
     * b23-b20:optbin压缩方式 0:无压缩 1:lz4 2:zlib inflate/deflate
     * b31-b24: not used
     * @member {number} Cmd
     * @memberof yrpcmsg.Ymsg
     * @instance
     */
    Ymsg.prototype.Cmd = 0

    /**
     * session id，登录后一定会有,用于后台区分不同的用户请求
     * @member {Uint8Array} Sid
     * @memberof yrpcmsg.Ymsg
     * @instance
     */
    Ymsg.prototype.Sid = $util.newBuffer([])

    /**
     * rpc call id,给分辨不同的rpc调用使用,调用方增1循环使用
     * @member {number} Cid
     * @memberof yrpcmsg.Ymsg
     * @instance
     */
    Ymsg.prototype.Cid = 0

    /**
     * rpc no,从0开始增1使用,用于区分收到重复的包,特别是udp的情况下
     * @member {number} No
     * @memberof yrpcmsg.Ymsg
     * @instance
     */
    Ymsg.prototype.No = 0

    /**
     * response code
     * @member {number} Res
     * @memberof yrpcmsg.Ymsg
     * @instance
     */
    Ymsg.prototype.Res = 0

    /**
     * msg body
     * @member {Uint8Array} Body
     * @memberof yrpcmsg.Ymsg
     * @instance
     */
    Ymsg.prototype.Body = $util.newBuffer([])

    /**
     * optional str parameter,额外的信息,一般不会有,有些特殊命令里面可能用到
     * @member {string} Optstr
     * @memberof yrpcmsg.Ymsg
     * @instance
     */
    Ymsg.prototype.Optstr = ''

    /**
     * optional binary parameter,额外的信息,一般不会有,有些特殊命令里面可能用到
     * @member {Uint8Array} Optbin
     * @memberof yrpcmsg.Ymsg
     * @instance
     */
    Ymsg.prototype.Optbin = $util.newBuffer([])

    /**
     * optional grpc meta
     * @member {yrpcmsg.IMeta|null|undefined} MetaInfo
     * @memberof yrpcmsg.Ymsg
     * @instance
     */
    Ymsg.prototype.MetaInfo = null

    /**
     * Encodes the specified Ymsg message. Does not implicitly {@link yrpcmsg.Ymsg.verify|verify} messages.
     * @function encode
     * @memberof yrpcmsg.Ymsg
     * @static
     * @param {yrpcmsg.IYmsg} message Ymsg message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    Ymsg.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (message.Len != null && Object.hasOwnProperty.call(message, 'Len'))
        writer.uint32(/* id 1, wireType 5 =*/ 13).fixed32(message.Len)
      if (message.Cmd != null && Object.hasOwnProperty.call(message, 'Cmd'))
        writer.uint32(/* id 2, wireType 5 =*/ 21).fixed32(message.Cmd)
      if (message.Sid != null && Object.hasOwnProperty.call(message, 'Sid'))
        writer.uint32(/* id 3, wireType 2 =*/ 26).bytes(message.Sid)
      if (message.Cid != null && Object.hasOwnProperty.call(message, 'Cid'))
        writer.uint32(/* id 4, wireType 0 =*/ 32).uint32(message.Cid)
      if (message.No != null && Object.hasOwnProperty.call(message, 'No'))
        writer.uint32(/* id 5, wireType 0 =*/ 40).uint32(message.No)
      if (message.Res != null && Object.hasOwnProperty.call(message, 'Res'))
        writer.uint32(/* id 9, wireType 0 =*/ 72).sint32(message.Res)
      if (message.Body != null && Object.hasOwnProperty.call(message, 'Body'))
        writer.uint32(/* id 10, wireType 2 =*/ 82).bytes(message.Body)
      if (
        message.Optstr != null &&
        Object.hasOwnProperty.call(message, 'Optstr')
      )
        writer.uint32(/* id 11, wireType 2 =*/ 90).string(message.Optstr)
      if (
        message.Optbin != null &&
        Object.hasOwnProperty.call(message, 'Optbin')
      )
        writer.uint32(/* id 12, wireType 2 =*/ 98).bytes(message.Optbin)
      if (
        message.MetaInfo != null &&
        Object.hasOwnProperty.call(message, 'MetaInfo')
      )
        $root.yrpcmsg.Meta.encode(
          message.MetaInfo,
          writer.uint32(/* id 13, wireType 2 =*/ 106).fork(),
        ).ldelim()
      return writer
    }

    /**
     * Decodes a Ymsg message from the specified reader or buffer.
     * @function decode
     * @memberof yrpcmsg.Ymsg
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {yrpcmsg.Ymsg} Ymsg
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    Ymsg.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.yrpcmsg.Ymsg()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.Len = reader.fixed32()
            break
          case 2:
            message.Cmd = reader.fixed32()
            break
          case 3:
            message.Sid = reader.bytes()
            break
          case 4:
            message.Cid = reader.uint32()
            break
          case 5:
            message.No = reader.uint32()
            break
          case 9:
            message.Res = reader.sint32()
            break
          case 10:
            message.Body = reader.bytes()
            break
          case 11:
            message.Optstr = reader.string()
            break
          case 12:
            message.Optbin = reader.bytes()
            break
          case 13:
            message.MetaInfo = $root.yrpcmsg.Meta.decode(
              reader,
              reader.uint32(),
            )
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return Ymsg
  })()

  yrpcmsg.MetaItem = (function () {
    /**
     * Properties of a MetaItem.
     * @memberof yrpcmsg
     * @interface IMetaItem
     * @property {string|null} [key] MetaItem key
     * @property {Array.<string>|null} [vals] MetaItem vals
     */

    /**
     * Constructs a new MetaItem.
     * @memberof yrpcmsg
     * @classdesc grpc meta data item
     * @implements IMetaItem
     * @constructor
     * @param {yrpcmsg.IMetaItem=} [properties] Properties to set
     */
    function MetaItem(properties) {
      this.vals = []
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * MetaItem key.
     * @member {string} key
     * @memberof yrpcmsg.MetaItem
     * @instance
     */
    MetaItem.prototype.key = ''

    /**
     * MetaItem vals.
     * @member {Array.<string>} vals
     * @memberof yrpcmsg.MetaItem
     * @instance
     */
    MetaItem.prototype.vals = $util.emptyArray

    /**
     * Encodes the specified MetaItem message. Does not implicitly {@link yrpcmsg.MetaItem.verify|verify} messages.
     * @function encode
     * @memberof yrpcmsg.MetaItem
     * @static
     * @param {yrpcmsg.IMetaItem} message MetaItem message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    MetaItem.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (message.key != null && Object.hasOwnProperty.call(message, 'key'))
        writer.uint32(/* id 1, wireType 2 =*/ 10).string(message.key)
      if (message.vals != null && message.vals.length)
        for (let i = 0; i < message.vals.length; ++i)
          writer.uint32(/* id 2, wireType 2 =*/ 18).string(message.vals[i])
      return writer
    }

    /**
     * Decodes a MetaItem message from the specified reader or buffer.
     * @function decode
     * @memberof yrpcmsg.MetaItem
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {yrpcmsg.MetaItem} MetaItem
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    MetaItem.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.yrpcmsg.MetaItem()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.key = reader.string()
            break
          case 2:
            if (!(message.vals && message.vals.length)) message.vals = []
            message.vals.push(reader.string())
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return MetaItem
  })()

  yrpcmsg.Meta = (function () {
    /**
     * Properties of a Meta.
     * @memberof yrpcmsg
     * @interface IMeta
     * @property {Array.<yrpcmsg.IMetaItem>|null} [val] grpc meta
     */

    /**
     * Constructs a new Meta.
     * @memberof yrpcmsg
     * @classdesc grpc meta
     * @implements IMeta
     * @constructor
     * @param {yrpcmsg.IMeta=} [properties] Properties to set
     */
    function Meta(properties) {
      this.val = []
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * grpc meta
     * @member {Array.<yrpcmsg.IMetaItem>} val
     * @memberof yrpcmsg.Meta
     * @instance
     */
    Meta.prototype.val = $util.emptyArray

    /**
     * Encodes the specified Meta message. Does not implicitly {@link yrpcmsg.Meta.verify|verify} messages.
     * @function encode
     * @memberof yrpcmsg.Meta
     * @static
     * @param {yrpcmsg.IMeta} message Meta message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    Meta.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (message.val != null && message.val.length)
        for (let i = 0; i < message.val.length; ++i)
          $root.yrpcmsg.MetaItem.encode(
            message.val[i],
            writer.uint32(/* id 1, wireType 2 =*/ 10).fork(),
          ).ldelim()
      return writer
    }

    /**
     * Decodes a Meta message from the specified reader or buffer.
     * @function decode
     * @memberof yrpcmsg.Meta
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {yrpcmsg.Meta} Meta
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    Meta.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.yrpcmsg.Meta()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            if (!(message.val && message.val.length)) message.val = []
            message.val.push(
              $root.yrpcmsg.MetaItem.decode(reader, reader.uint32()),
            )
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return Meta
  })()

  yrpcmsg.GrpcMeta = (function () {
    /**
     * Properties of a GrpcMeta.
     * @memberof yrpcmsg
     * @interface IGrpcMeta
     * @property {yrpcmsg.IMeta|null} [Header] GrpcMeta Header
     * @property {yrpcmsg.IMeta|null} [Trailer] GrpcMeta Trailer
     */

    /**
     * Constructs a new GrpcMeta.
     * @memberof yrpcmsg
     * @classdesc grpc Header Trailer meta
     * @implements IGrpcMeta
     * @constructor
     * @param {yrpcmsg.IGrpcMeta=} [properties] Properties to set
     */
    function GrpcMeta(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * GrpcMeta Header.
     * @member {yrpcmsg.IMeta|null|undefined} Header
     * @memberof yrpcmsg.GrpcMeta
     * @instance
     */
    GrpcMeta.prototype.Header = null

    /**
     * GrpcMeta Trailer.
     * @member {yrpcmsg.IMeta|null|undefined} Trailer
     * @memberof yrpcmsg.GrpcMeta
     * @instance
     */
    GrpcMeta.prototype.Trailer = null

    /**
     * Encodes the specified GrpcMeta message. Does not implicitly {@link yrpcmsg.GrpcMeta.verify|verify} messages.
     * @function encode
     * @memberof yrpcmsg.GrpcMeta
     * @static
     * @param {yrpcmsg.IGrpcMeta} message GrpcMeta message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    GrpcMeta.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (
        message.Header != null &&
        Object.hasOwnProperty.call(message, 'Header')
      )
        $root.yrpcmsg.Meta.encode(
          message.Header,
          writer.uint32(/* id 1, wireType 2 =*/ 10).fork(),
        ).ldelim()
      if (
        message.Trailer != null &&
        Object.hasOwnProperty.call(message, 'Trailer')
      )
        $root.yrpcmsg.Meta.encode(
          message.Trailer,
          writer.uint32(/* id 2, wireType 2 =*/ 18).fork(),
        ).ldelim()
      return writer
    }

    /**
     * Decodes a GrpcMeta message from the specified reader or buffer.
     * @function decode
     * @memberof yrpcmsg.GrpcMeta
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {yrpcmsg.GrpcMeta} GrpcMeta
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    GrpcMeta.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.yrpcmsg.GrpcMeta()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.Header = $root.yrpcmsg.Meta.decode(reader, reader.uint32())
            break
          case 2:
            message.Trailer = $root.yrpcmsg.Meta.decode(reader, reader.uint32())
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return GrpcMeta
  })()

  yrpcmsg.Yempty = (function () {
    /**
     * Properties of a Yempty.
     * @memberof yrpcmsg
     * @interface IYempty
     */

    /**
     * Constructs a new Yempty.
     * @memberof yrpcmsg
     * @classdesc Represents a Yempty.
     * @implements IYempty
     * @constructor
     * @param {yrpcmsg.IYempty=} [properties] Properties to set
     */
    function Yempty(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * Encodes the specified Yempty message. Does not implicitly {@link yrpcmsg.Yempty.verify|verify} messages.
     * @function encode
     * @memberof yrpcmsg.Yempty
     * @static
     * @param {yrpcmsg.IYempty} message Yempty message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    Yempty.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      return writer
    }

    /**
     * Decodes a Yempty message from the specified reader or buffer.
     * @function decode
     * @memberof yrpcmsg.Yempty
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {yrpcmsg.Yempty} Yempty
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    Yempty.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.yrpcmsg.Yempty()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return Yempty
  })()

  yrpcmsg.Ynocare = (function () {
    /**
     * Properties of a Ynocare.
     * @memberof yrpcmsg
     * @interface IYnocare
     */

    /**
     * Constructs a new Ynocare.
     * @memberof yrpcmsg
     * @classdesc A generic nocare message that you can use to info the call is not important
     * and no care the result. A typical example is to use it in report log/trace.
     * For instance:
     *
     * service Log {
     * rpc Log(infos) returns (yrpc.Ynocare);
     * }
     * @implements IYnocare
     * @constructor
     * @param {yrpcmsg.IYnocare=} [properties] Properties to set
     */
    function Ynocare(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * Encodes the specified Ynocare message. Does not implicitly {@link yrpcmsg.Ynocare.verify|verify} messages.
     * @function encode
     * @memberof yrpcmsg.Ynocare
     * @static
     * @param {yrpcmsg.IYnocare} message Ynocare message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    Ynocare.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      return writer
    }

    /**
     * Decodes a Ynocare message from the specified reader or buffer.
     * @function decode
     * @memberof yrpcmsg.Ynocare
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {yrpcmsg.Ynocare} Ynocare
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    Ynocare.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.yrpcmsg.Ynocare()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return Ynocare
  })()

  yrpcmsg.UnixTime = (function () {
    /**
     * Properties of an UnixTime.
     * @memberof yrpcmsg
     * @interface IUnixTime
     * @property {Long|null} [TimeUnix] Unix time, the number of miliseconds elapsed since January 1, 1970 UTC
     * @property {string|null} [TimeStr] utc time yyyy-MM-dd hh:mm:ss.zzz
     */

    /**
     * Constructs a new UnixTime.
     * @memberof yrpcmsg
     * @classdesc Represents an UnixTime.
     * @implements IUnixTime
     * @constructor
     * @param {yrpcmsg.IUnixTime=} [properties] Properties to set
     */
    function UnixTime(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * Unix time, the number of miliseconds elapsed since January 1, 1970 UTC
     * @member {Long} TimeUnix
     * @memberof yrpcmsg.UnixTime
     * @instance
     */
    UnixTime.prototype.TimeUnix = $util.Long
      ? $util.Long.fromBits(0, 0, false)
      : 0

    /**
     * utc time yyyy-MM-dd hh:mm:ss.zzz
     * @member {string} TimeStr
     * @memberof yrpcmsg.UnixTime
     * @instance
     */
    UnixTime.prototype.TimeStr = ''

    /**
     * Encodes the specified UnixTime message. Does not implicitly {@link yrpcmsg.UnixTime.verify|verify} messages.
     * @function encode
     * @memberof yrpcmsg.UnixTime
     * @static
     * @param {yrpcmsg.IUnixTime} message UnixTime message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    UnixTime.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (
        message.TimeUnix != null &&
        Object.hasOwnProperty.call(message, 'TimeUnix')
      )
        writer.uint32(/* id 1, wireType 0 =*/ 8).sint64(message.TimeUnix)
      if (
        message.TimeStr != null &&
        Object.hasOwnProperty.call(message, 'TimeStr')
      )
        writer.uint32(/* id 2, wireType 2 =*/ 18).string(message.TimeStr)
      return writer
    }

    /**
     * Decodes an UnixTime message from the specified reader or buffer.
     * @function decode
     * @memberof yrpcmsg.UnixTime
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {yrpcmsg.UnixTime} UnixTime
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    UnixTime.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.yrpcmsg.UnixTime()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.TimeUnix = reader.sint64()
            break
          case 2:
            message.TimeStr = reader.string()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return UnixTime
  })()

  return yrpcmsg
})())

export const bysdb = ($root.bysdb = (() => {
  /**
   * Namespace bysdb.
   * @exports bysdb
   * @namespace
   */
  const bysdb = $root.bysdb || {}

  bysdb.DbController = (function () {
    /**
     * Properties of a DbController.
     * @memberof bysdb
     * @interface IDbController
     * @property {string|null} [RID] @db uuid primary key
     * 行ID
     * @property {string|null} [OrgRID] @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 控制器所属的群组
     * @property {string|null} [ControllerNo] @db varchar(16) not null unique
     * controller编号
     * @property {string|null} [ControllerDescription] @db text
     * controller描述信息
     * @property {number|null} [ControllerType] @db int
     * 控制器类型 1:中继控制器  2:基站控制器
     * @property {string|null} [ParentRID] @db uuid
     * 上级控制器RID, 可以没有，中继控制器一般都有
     * @property {number|null} [Lon] @db double precision
     * 经度
     * @property {number|null} [Lat] @db double precision
     * 纬度
     * @property {string|null} [Setting] @db jsonb not null default  '{}'::jsonb
     * 其它设置，可以在里面扩展需要用到的其它信息
     * @property {number|null} [ControllerHWID] @db int not null unique
     * 控制器硬件ID，int32 > 0
     * @property {number|null} [ChannelCount] @db int not null default 1
     * 控制器可用信道数，中继控制器为1,基站按实际情况
     * @property {number|null} [MapShowLevel] @db int default 12
     * 地图开始显示级别
     * @property {string|null} [UpdatedAt] @db timestamp not null default now_utc()
     * 数据最后修改时间
     * @property {string|null} [UpdatedDC] @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     * @property {number|null} [DefaultNetworkType] @db int
     * 默认通讯方式， 1:有线网络， 2:4g网络 3:无线FSK
     * @property {number|null} [ParentChannelNo] @db int
     * 中继对应的上级控制器的通道
     */

    /**
     * Constructs a new DbController.
     * @memberof bysdb
     * @classdesc 控制器信息表
     * @rpc crud pcrud
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbControllerOrgRID on DbController USING hash(OrgRID);
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbControllerParentRID on DbController USING hash(ParentRID);
     * @dbpost CREATE UNIQUE INDEX IF NOT EXISTS uidxDbControllerParent ON DbController (ParentRID, ParentChannelNo) WHERE ControllerType=1;
     * @implements IDbController
     * @constructor
     * @param {bysdb.IDbController=} [properties] Properties to set
     */
    function DbController(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * @db uuid primary key
     * 行ID
     * @member {string} RID
     * @memberof bysdb.DbController
     * @instance
     */
    DbController.prototype.RID = ''

    /**
     * @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 控制器所属的群组
     * @member {string} OrgRID
     * @memberof bysdb.DbController
     * @instance
     */
    DbController.prototype.OrgRID = ''

    /**
     * @db varchar(16) not null unique
     * controller编号
     * @member {string} ControllerNo
     * @memberof bysdb.DbController
     * @instance
     */
    DbController.prototype.ControllerNo = ''

    /**
     * @db text
     * controller描述信息
     * @member {string} ControllerDescription
     * @memberof bysdb.DbController
     * @instance
     */
    DbController.prototype.ControllerDescription = ''

    /**
     * @db int
     * 控制器类型 1:中继控制器  2:基站控制器
     * @member {number} ControllerType
     * @memberof bysdb.DbController
     * @instance
     */
    DbController.prototype.ControllerType = 0

    /**
     * @db uuid
     * 上级控制器RID, 可以没有，中继控制器一般都有
     * @member {string} ParentRID
     * @memberof bysdb.DbController
     * @instance
     */
    DbController.prototype.ParentRID = ''

    /**
     * @db double precision
     * 经度
     * @member {number} Lon
     * @memberof bysdb.DbController
     * @instance
     */
    DbController.prototype.Lon = 0

    /**
     * @db double precision
     * 纬度
     * @member {number} Lat
     * @memberof bysdb.DbController
     * @instance
     */
    DbController.prototype.Lat = 0

    /**
     * @db jsonb not null default  '{}'::jsonb
     * 其它设置，可以在里面扩展需要用到的其它信息
     * @member {string} Setting
     * @memberof bysdb.DbController
     * @instance
     */
    DbController.prototype.Setting = ''

    /**
     * @db int not null unique
     * 控制器硬件ID，int32 > 0
     * @member {number} ControllerHWID
     * @memberof bysdb.DbController
     * @instance
     */
    DbController.prototype.ControllerHWID = 0

    /**
     * @db int not null default 1
     * 控制器可用信道数，中继控制器为1,基站按实际情况
     * @member {number} ChannelCount
     * @memberof bysdb.DbController
     * @instance
     */
    DbController.prototype.ChannelCount = 0

    /**
     * @db int default 12
     * 地图开始显示级别
     * @member {number} MapShowLevel
     * @memberof bysdb.DbController
     * @instance
     */
    DbController.prototype.MapShowLevel = 0

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     * @member {string} UpdatedAt
     * @memberof bysdb.DbController
     * @instance
     */
    DbController.prototype.UpdatedAt = ''

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     * @member {string} UpdatedDC
     * @memberof bysdb.DbController
     * @instance
     */
    DbController.prototype.UpdatedDC = ''

    /**
     * @db int
     * 默认通讯方式， 1:有线网络， 2:4g网络 3:无线FSK
     * @member {number} DefaultNetworkType
     * @memberof bysdb.DbController
     * @instance
     */
    DbController.prototype.DefaultNetworkType = 0

    /**
     * @db int
     * 中继对应的上级控制器的通道
     * @member {number} ParentChannelNo
     * @memberof bysdb.DbController
     * @instance
     */
    DbController.prototype.ParentChannelNo = 0

    /**
     * Encodes the specified DbController message. Does not implicitly {@link bysdb.DbController.verify|verify} messages.
     * @function encode
     * @memberof bysdb.DbController
     * @static
     * @param {bysdb.IDbController} message DbController message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    DbController.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (message.RID != null && Object.hasOwnProperty.call(message, 'RID'))
        writer.uint32(/* id 1, wireType 2 =*/ 10).string(message.RID)
      if (
        message.OrgRID != null &&
        Object.hasOwnProperty.call(message, 'OrgRID')
      )
        writer.uint32(/* id 2, wireType 2 =*/ 18).string(message.OrgRID)
      if (
        message.ControllerNo != null &&
        Object.hasOwnProperty.call(message, 'ControllerNo')
      )
        writer.uint32(/* id 3, wireType 2 =*/ 26).string(message.ControllerNo)
      if (
        message.ControllerDescription != null &&
        Object.hasOwnProperty.call(message, 'ControllerDescription')
      )
        writer
          .uint32(/* id 4, wireType 2 =*/ 34)
          .string(message.ControllerDescription)
      if (
        message.ControllerType != null &&
        Object.hasOwnProperty.call(message, 'ControllerType')
      )
        writer.uint32(/* id 5, wireType 0 =*/ 40).int32(message.ControllerType)
      if (
        message.ParentRID != null &&
        Object.hasOwnProperty.call(message, 'ParentRID')
      )
        writer.uint32(/* id 6, wireType 2 =*/ 50).string(message.ParentRID)
      if (message.Lon != null && Object.hasOwnProperty.call(message, 'Lon'))
        writer.uint32(/* id 7, wireType 1 =*/ 57).double(message.Lon)
      if (message.Lat != null && Object.hasOwnProperty.call(message, 'Lat'))
        writer.uint32(/* id 8, wireType 1 =*/ 65).double(message.Lat)
      if (
        message.Setting != null &&
        Object.hasOwnProperty.call(message, 'Setting')
      )
        writer.uint32(/* id 9, wireType 2 =*/ 74).string(message.Setting)
      if (
        message.ControllerHWID != null &&
        Object.hasOwnProperty.call(message, 'ControllerHWID')
      )
        writer
          .uint32(/* id 10, wireType 0 =*/ 80)
          .sint32(message.ControllerHWID)
      if (
        message.ChannelCount != null &&
        Object.hasOwnProperty.call(message, 'ChannelCount')
      )
        writer.uint32(/* id 11, wireType 0 =*/ 88).sint32(message.ChannelCount)
      if (
        message.MapShowLevel != null &&
        Object.hasOwnProperty.call(message, 'MapShowLevel')
      )
        writer.uint32(/* id 12, wireType 0 =*/ 96).sint32(message.MapShowLevel)
      if (
        message.UpdatedAt != null &&
        Object.hasOwnProperty.call(message, 'UpdatedAt')
      )
        writer.uint32(/* id 14, wireType 2 =*/ 114).string(message.UpdatedAt)
      if (
        message.UpdatedDC != null &&
        Object.hasOwnProperty.call(message, 'UpdatedDC')
      )
        writer.uint32(/* id 15, wireType 2 =*/ 122).string(message.UpdatedDC)
      if (
        message.DefaultNetworkType != null &&
        Object.hasOwnProperty.call(message, 'DefaultNetworkType')
      )
        writer
          .uint32(/* id 16, wireType 0 =*/ 128)
          .sint32(message.DefaultNetworkType)
      if (
        message.ParentChannelNo != null &&
        Object.hasOwnProperty.call(message, 'ParentChannelNo')
      )
        writer
          .uint32(/* id 17, wireType 0 =*/ 136)
          .sint32(message.ParentChannelNo)
      return writer
    }

    /**
     * Decodes a DbController message from the specified reader or buffer.
     * @function decode
     * @memberof bysdb.DbController
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {bysdb.DbController} DbController
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    DbController.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.bysdb.DbController()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.RID = reader.string()
            break
          case 2:
            message.OrgRID = reader.string()
            break
          case 3:
            message.ControllerNo = reader.string()
            break
          case 4:
            message.ControllerDescription = reader.string()
            break
          case 5:
            message.ControllerType = reader.int32()
            break
          case 6:
            message.ParentRID = reader.string()
            break
          case 7:
            message.Lon = reader.double()
            break
          case 8:
            message.Lat = reader.double()
            break
          case 9:
            message.Setting = reader.string()
            break
          case 10:
            message.ControllerHWID = reader.sint32()
            break
          case 11:
            message.ChannelCount = reader.sint32()
            break
          case 12:
            message.MapShowLevel = reader.sint32()
            break
          case 14:
            message.UpdatedAt = reader.string()
            break
          case 15:
            message.UpdatedDC = reader.string()
            break
          case 16:
            message.DefaultNetworkType = reader.sint32()
            break
          case 17:
            message.ParentChannelNo = reader.sint32()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return DbController
  })()

  bysdb.DbBysMarker = (function () {
    /**
     * Properties of a DbBysMarker.
     * @memberof bysdb
     * @interface IDbBysMarker
     * @property {string|null} [RID] @db uuid primary key
     * 行ID
     * @property {string|null} [OrgRID] @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 界桩所属的群组
     * @property {string|null} [MarkerNo] @db varchar(16) not null unique
     * 界桩编号
     * @property {string|null} [MarkerDescription] @db text
     * 界桩描述信息
     * @property {string|null} [ControllerRID] @db uuid REFERENCES DbController(RID) ON DELETE RESTRICT
     * 所属控制器RID
     * @property {number|null} [ControllerChannel] @db int not null
     * 所属的基站通道，如果所属的控制器为中继控制器，此值固定为1
     * @property {number|null} [Lon] @db double precision
     * 经度
     * @property {number|null} [Lat] @db double precision
     * 纬度
     * @property {number|null} [MarkerHWID] @db int not null unique
     * 界桩硬件ID,范围
     * @property {string|null} [Setting] @db jsonb not null default  '{}'::jsonb
     * 其它设置，可以在里面扩展需要用到的其它信息
     * 新的红线图规则属性为：redLine，不存在该字段为默认连接，空数组为不连接，
     * 数组元素为界桩硬件ID，每个都连接
     * @property {number|null} [MarkerModel] @db int
     * 界桩型号 1:长型 X*Y*Z cm, 2:短型 x*y*z cm
     * @property {number|null} [MapShowLevel] @db int default 12
     * 地图开始显示级别
     * @property {number|null} [MarkerQueueNo] @db int not null
     * 在所属于基站下的排队号
     * @property {string|null} [UpdatedAt] @db timestamp not null default now_utc()
     * 数据最后修改时间
     * @property {string|null} [UpdatedDC] @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     * @property {string|null} [MarkerParamTime] @db timestamp not null default now_utc()
     * 参数更新时间
     * @property {number|null} [MarkerDayInterval] @db int not null default 24
     * 打卡时间间隔(6-24小时)
     * @property {number|null} [MarkerQueueInterval] @db int not null default 6
     * 排队发射间隔时间（秒）
     * @property {number|null} [MarkerEmergentInterval] @db int not null default 60
     * 报警后发射间隔(30-240秒)
     * @property {number|null} [MarkerChannel] @db int not null default 1
     * 界桩通信信道
     * @property {string|null} [MarkerWakeupBaseTime] @db time not null
     * 界桩苏醒基准时间
     * @property {number|null} [MarkerDisabled] @db int not null default 0
     * 界桩是否已经被遥毙, 0:遥活 1:遥闭  2:遥晕
     * @property {boolean|null} [HasInstallDevice] @db boolean default true
     * 界桩是否有安装电子设备
     * @property {boolean|null} [HasInstallStone] @db boolean default false
     * 石头界桩是否已安装
     * @property {number|null} [MarkerType] @db int not null default 0
     * 常规界桩：0, 4g界桩(BF-DTD002): 1, 4g界桩带摄像机(BF-DTD002(Pro)): 2, 专网界桩dmr版(BF-DTD003): 3
     * @property {string|null} [MarkerSettings] @db jsonb not null default  '{}'::jsonb
     * MarkerType=1时，填写bysproto.BDataUpdate
     * @property {string|null} [ICCID] @db varchar(20)
     * 4G界桩的iccid卡号，20位数字字符串
     * @property {string|null} [ExpirationDate] @db timestamp
     * iccid卡号到期日期
     * @property {string|null} [IMEI] @db text
     * 4g界桩的设备唯一标识
     * @property {number|null} [CameraDisabled] @db int not null default 0
     * 摄像机是否已经被遥毙, 0:遥活 1:遥闭  2:遥晕
     */

    /**
     * Constructs a new DbBysMarker.
     * @memberof bysdb
     * @classdesc 界桩信息表
     * @rpc crud pcrud
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbBysMarkerOrgRID on DbBysMarker USING hash(OrgRID);
     * @dbpost ALTER TABLE DbBysMarker ADD COLUMN IF NOT EXISTS MarkerType integer not null default 0;
     * @dbpost ALTER TABLE DbBysMarker ADD COLUMN IF NOT EXISTS MarkerSettings jsonb not null default  '{}'::jsonb;
     * @dbpost ALTER TABLE dbbysmarker DROP CONSTRAINT if exists dbbysmarker_controllerrid_controllerchannel_markerqueueno_key;
     * @dbpost drop index if exists dbbysmarker_controllerrid_controllerchannel_markerqueueno_key;
     * @dbpost CREATE UNIQUE INDEX IF NOT EXISTS idxdbbysmarker_controllerrid_controllerchannel_markerqueueno_key on DbBysMarker (ControllerRID, ControllerChannel, MarkerQueueNo) where MarkerType=0; --同一个信道的排队号必须唯一,只针对旧的界桩
     * @dbpost ALTER TABLE DbBysMarker ALTER COLUMN ControllerRID DROP NOT NULL;
     * @dbpost ALTER TABLE DbBysMarker ADD COLUMN IF NOT EXISTS ICCID varchar(20);
     * @dbpost ALTER TABLE DbBysMarker ADD COLUMN IF NOT EXISTS ExpirationDate timestamp;
     * @dbpost CREATE UNIQUE INDEX IF NOT EXISTS idxDbBysMarkerICCID on DbBysMarker (ICCID) where MarkerType=1;
     * @dbpost ALTER TABLE DbBysMarker ADD COLUMN IF NOT EXISTS IMEI text;
     * @dbpost ALTER TABLE DbBysMarker ADD COLUMN IF NOT EXISTS CameraDisabled integer not null default 0;
     * @implements IDbBysMarker
     * @constructor
     * @param {bysdb.IDbBysMarker=} [properties] Properties to set
     */
    function DbBysMarker(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * @db uuid primary key
     * 行ID
     * @member {string} RID
     * @memberof bysdb.DbBysMarker
     * @instance
     */
    DbBysMarker.prototype.RID = ''

    /**
     * @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 界桩所属的群组
     * @member {string} OrgRID
     * @memberof bysdb.DbBysMarker
     * @instance
     */
    DbBysMarker.prototype.OrgRID = ''

    /**
     * @db varchar(16) not null unique
     * 界桩编号
     * @member {string} MarkerNo
     * @memberof bysdb.DbBysMarker
     * @instance
     */
    DbBysMarker.prototype.MarkerNo = ''

    /**
     * @db text
     * 界桩描述信息
     * @member {string} MarkerDescription
     * @memberof bysdb.DbBysMarker
     * @instance
     */
    DbBysMarker.prototype.MarkerDescription = ''

    /**
     * @db uuid REFERENCES DbController(RID) ON DELETE RESTRICT
     * 所属控制器RID
     * @member {string} ControllerRID
     * @memberof bysdb.DbBysMarker
     * @instance
     */
    DbBysMarker.prototype.ControllerRID = ''

    /**
     * @db int not null
     * 所属的基站通道，如果所属的控制器为中继控制器，此值固定为1
     * @member {number} ControllerChannel
     * @memberof bysdb.DbBysMarker
     * @instance
     */
    DbBysMarker.prototype.ControllerChannel = 0

    /**
     * @db double precision
     * 经度
     * @member {number} Lon
     * @memberof bysdb.DbBysMarker
     * @instance
     */
    DbBysMarker.prototype.Lon = 0

    /**
     * @db double precision
     * 纬度
     * @member {number} Lat
     * @memberof bysdb.DbBysMarker
     * @instance
     */
    DbBysMarker.prototype.Lat = 0

    /**
     * @db int not null unique
     * 界桩硬件ID,范围
     * @member {number} MarkerHWID
     * @memberof bysdb.DbBysMarker
     * @instance
     */
    DbBysMarker.prototype.MarkerHWID = 0

    /**
     * @db jsonb not null default  '{}'::jsonb
     * 其它设置，可以在里面扩展需要用到的其它信息
     * 新的红线图规则属性为：redLine，不存在该字段为默认连接，空数组为不连接，
     * 数组元素为界桩硬件ID，每个都连接
     * @member {string} Setting
     * @memberof bysdb.DbBysMarker
     * @instance
     */
    DbBysMarker.prototype.Setting = ''

    /**
     * @db int
     * 界桩型号 1:长型 X*Y*Z cm, 2:短型 x*y*z cm
     * @member {number} MarkerModel
     * @memberof bysdb.DbBysMarker
     * @instance
     */
    DbBysMarker.prototype.MarkerModel = 0

    /**
     * @db int default 12
     * 地图开始显示级别
     * @member {number} MapShowLevel
     * @memberof bysdb.DbBysMarker
     * @instance
     */
    DbBysMarker.prototype.MapShowLevel = 0

    /**
     * @db int not null
     * 在所属于基站下的排队号
     * @member {number} MarkerQueueNo
     * @memberof bysdb.DbBysMarker
     * @instance
     */
    DbBysMarker.prototype.MarkerQueueNo = 0

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     * @member {string} UpdatedAt
     * @memberof bysdb.DbBysMarker
     * @instance
     */
    DbBysMarker.prototype.UpdatedAt = ''

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     * @member {string} UpdatedDC
     * @memberof bysdb.DbBysMarker
     * @instance
     */
    DbBysMarker.prototype.UpdatedDC = ''

    /**
     * @db timestamp not null default now_utc()
     * 参数更新时间
     * @member {string} MarkerParamTime
     * @memberof bysdb.DbBysMarker
     * @instance
     */
    DbBysMarker.prototype.MarkerParamTime = ''

    /**
     * @db int not null default 24
     * 打卡时间间隔(6-24小时)
     * @member {number} MarkerDayInterval
     * @memberof bysdb.DbBysMarker
     * @instance
     */
    DbBysMarker.prototype.MarkerDayInterval = 0

    /**
     * @db int not null default 6
     * 排队发射间隔时间（秒）
     * @member {number} MarkerQueueInterval
     * @memberof bysdb.DbBysMarker
     * @instance
     */
    DbBysMarker.prototype.MarkerQueueInterval = 0

    /**
     * @db int not null default 60
     * 报警后发射间隔(30-240秒)
     * @member {number} MarkerEmergentInterval
     * @memberof bysdb.DbBysMarker
     * @instance
     */
    DbBysMarker.prototype.MarkerEmergentInterval = 0

    /**
     * @db int not null default 1
     * 界桩通信信道
     * @member {number} MarkerChannel
     * @memberof bysdb.DbBysMarker
     * @instance
     */
    DbBysMarker.prototype.MarkerChannel = 0

    /**
     * @db time not null
     * 界桩苏醒基准时间
     * @member {string} MarkerWakeupBaseTime
     * @memberof bysdb.DbBysMarker
     * @instance
     */
    DbBysMarker.prototype.MarkerWakeupBaseTime = ''

    /**
     * @db int not null default 0
     * 界桩是否已经被遥毙, 0:遥活 1:遥闭  2:遥晕
     * @member {number} MarkerDisabled
     * @memberof bysdb.DbBysMarker
     * @instance
     */
    DbBysMarker.prototype.MarkerDisabled = 0

    /**
     * @db boolean default true
     * 界桩是否有安装电子设备
     * @member {boolean} HasInstallDevice
     * @memberof bysdb.DbBysMarker
     * @instance
     */
    DbBysMarker.prototype.HasInstallDevice = false

    /**
     * @db boolean default false
     * 石头界桩是否已安装
     * @member {boolean} HasInstallStone
     * @memberof bysdb.DbBysMarker
     * @instance
     */
    DbBysMarker.prototype.HasInstallStone = false

    /**
     * @db int not null default 0
     * 常规界桩：0, 4g界桩(BF-DTD002): 1, 4g界桩带摄像机(BF-DTD002(Pro)): 2, 专网界桩dmr版(BF-DTD003): 3
     * @member {number} MarkerType
     * @memberof bysdb.DbBysMarker
     * @instance
     */
    DbBysMarker.prototype.MarkerType = 0

    /**
     * @db jsonb not null default  '{}'::jsonb
     * MarkerType=1时，填写bysproto.BDataUpdate
     * @member {string} MarkerSettings
     * @memberof bysdb.DbBysMarker
     * @instance
     */
    DbBysMarker.prototype.MarkerSettings = ''

    /**
     * @db varchar(20)
     * 4G界桩的iccid卡号，20位数字字符串
     * @member {string} ICCID
     * @memberof bysdb.DbBysMarker
     * @instance
     */
    DbBysMarker.prototype.ICCID = ''

    /**
     * @db timestamp
     * iccid卡号到期日期
     * @member {string} ExpirationDate
     * @memberof bysdb.DbBysMarker
     * @instance
     */
    DbBysMarker.prototype.ExpirationDate = ''

    /**
     * @db text
     * 4g界桩的设备唯一标识
     * @member {string} IMEI
     * @memberof bysdb.DbBysMarker
     * @instance
     */
    DbBysMarker.prototype.IMEI = ''

    /**
     * @db int not null default 0
     * 摄像机是否已经被遥毙, 0:遥活 1:遥闭  2:遥晕
     * @member {number} CameraDisabled
     * @memberof bysdb.DbBysMarker
     * @instance
     */
    DbBysMarker.prototype.CameraDisabled = 0

    /**
     * Encodes the specified DbBysMarker message. Does not implicitly {@link bysdb.DbBysMarker.verify|verify} messages.
     * @function encode
     * @memberof bysdb.DbBysMarker
     * @static
     * @param {bysdb.IDbBysMarker} message DbBysMarker message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    DbBysMarker.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (message.RID != null && Object.hasOwnProperty.call(message, 'RID'))
        writer.uint32(/* id 1, wireType 2 =*/ 10).string(message.RID)
      if (
        message.OrgRID != null &&
        Object.hasOwnProperty.call(message, 'OrgRID')
      )
        writer.uint32(/* id 2, wireType 2 =*/ 18).string(message.OrgRID)
      if (
        message.MarkerNo != null &&
        Object.hasOwnProperty.call(message, 'MarkerNo')
      )
        writer.uint32(/* id 3, wireType 2 =*/ 26).string(message.MarkerNo)
      if (
        message.MarkerDescription != null &&
        Object.hasOwnProperty.call(message, 'MarkerDescription')
      )
        writer
          .uint32(/* id 4, wireType 2 =*/ 34)
          .string(message.MarkerDescription)
      if (
        message.ControllerRID != null &&
        Object.hasOwnProperty.call(message, 'ControllerRID')
      )
        writer.uint32(/* id 5, wireType 2 =*/ 42).string(message.ControllerRID)
      if (
        message.ControllerChannel != null &&
        Object.hasOwnProperty.call(message, 'ControllerChannel')
      )
        writer
          .uint32(/* id 6, wireType 0 =*/ 48)
          .sint32(message.ControllerChannel)
      if (message.Lon != null && Object.hasOwnProperty.call(message, 'Lon'))
        writer.uint32(/* id 7, wireType 1 =*/ 57).double(message.Lon)
      if (message.Lat != null && Object.hasOwnProperty.call(message, 'Lat'))
        writer.uint32(/* id 8, wireType 1 =*/ 65).double(message.Lat)
      if (
        message.MarkerHWID != null &&
        Object.hasOwnProperty.call(message, 'MarkerHWID')
      )
        writer.uint32(/* id 9, wireType 0 =*/ 72).sint32(message.MarkerHWID)
      if (
        message.Setting != null &&
        Object.hasOwnProperty.call(message, 'Setting')
      )
        writer.uint32(/* id 10, wireType 2 =*/ 82).string(message.Setting)
      if (
        message.MarkerModel != null &&
        Object.hasOwnProperty.call(message, 'MarkerModel')
      )
        writer.uint32(/* id 11, wireType 0 =*/ 88).sint32(message.MarkerModel)
      if (
        message.MapShowLevel != null &&
        Object.hasOwnProperty.call(message, 'MapShowLevel')
      )
        writer.uint32(/* id 12, wireType 0 =*/ 96).sint32(message.MapShowLevel)
      if (
        message.MarkerQueueNo != null &&
        Object.hasOwnProperty.call(message, 'MarkerQueueNo')
      )
        writer
          .uint32(/* id 13, wireType 0 =*/ 104)
          .sint32(message.MarkerQueueNo)
      if (
        message.UpdatedAt != null &&
        Object.hasOwnProperty.call(message, 'UpdatedAt')
      )
        writer.uint32(/* id 14, wireType 2 =*/ 114).string(message.UpdatedAt)
      if (
        message.UpdatedDC != null &&
        Object.hasOwnProperty.call(message, 'UpdatedDC')
      )
        writer.uint32(/* id 15, wireType 2 =*/ 122).string(message.UpdatedDC)
      if (
        message.MarkerParamTime != null &&
        Object.hasOwnProperty.call(message, 'MarkerParamTime')
      )
        writer
          .uint32(/* id 16, wireType 2 =*/ 130)
          .string(message.MarkerParamTime)
      if (
        message.MarkerDayInterval != null &&
        Object.hasOwnProperty.call(message, 'MarkerDayInterval')
      )
        writer
          .uint32(/* id 17, wireType 0 =*/ 136)
          .sint32(message.MarkerDayInterval)
      if (
        message.MarkerQueueInterval != null &&
        Object.hasOwnProperty.call(message, 'MarkerQueueInterval')
      )
        writer
          .uint32(/* id 18, wireType 0 =*/ 144)
          .sint32(message.MarkerQueueInterval)
      if (
        message.MarkerEmergentInterval != null &&
        Object.hasOwnProperty.call(message, 'MarkerEmergentInterval')
      )
        writer
          .uint32(/* id 19, wireType 0 =*/ 152)
          .sint32(message.MarkerEmergentInterval)
      if (
        message.MarkerChannel != null &&
        Object.hasOwnProperty.call(message, 'MarkerChannel')
      )
        writer
          .uint32(/* id 20, wireType 0 =*/ 160)
          .sint32(message.MarkerChannel)
      if (
        message.MarkerWakeupBaseTime != null &&
        Object.hasOwnProperty.call(message, 'MarkerWakeupBaseTime')
      )
        writer
          .uint32(/* id 21, wireType 2 =*/ 170)
          .string(message.MarkerWakeupBaseTime)
      if (
        message.MarkerDisabled != null &&
        Object.hasOwnProperty.call(message, 'MarkerDisabled')
      )
        writer
          .uint32(/* id 22, wireType 0 =*/ 176)
          .sint32(message.MarkerDisabled)
      if (
        message.HasInstallDevice != null &&
        Object.hasOwnProperty.call(message, 'HasInstallDevice')
      )
        writer
          .uint32(/* id 23, wireType 0 =*/ 184)
          .bool(message.HasInstallDevice)
      if (
        message.HasInstallStone != null &&
        Object.hasOwnProperty.call(message, 'HasInstallStone')
      )
        writer
          .uint32(/* id 25, wireType 0 =*/ 200)
          .bool(message.HasInstallStone)
      if (
        message.MarkerType != null &&
        Object.hasOwnProperty.call(message, 'MarkerType')
      )
        writer.uint32(/* id 26, wireType 0 =*/ 208).sint32(message.MarkerType)
      if (
        message.MarkerSettings != null &&
        Object.hasOwnProperty.call(message, 'MarkerSettings')
      )
        writer
          .uint32(/* id 27, wireType 2 =*/ 218)
          .string(message.MarkerSettings)
      if (message.ICCID != null && Object.hasOwnProperty.call(message, 'ICCID'))
        writer.uint32(/* id 28, wireType 2 =*/ 226).string(message.ICCID)
      if (
        message.ExpirationDate != null &&
        Object.hasOwnProperty.call(message, 'ExpirationDate')
      )
        writer
          .uint32(/* id 29, wireType 2 =*/ 234)
          .string(message.ExpirationDate)
      if (message.IMEI != null && Object.hasOwnProperty.call(message, 'IMEI'))
        writer.uint32(/* id 30, wireType 2 =*/ 242).string(message.IMEI)
      if (
        message.CameraDisabled != null &&
        Object.hasOwnProperty.call(message, 'CameraDisabled')
      )
        writer
          .uint32(/* id 31, wireType 0 =*/ 248)
          .sint32(message.CameraDisabled)
      return writer
    }

    /**
     * Decodes a DbBysMarker message from the specified reader or buffer.
     * @function decode
     * @memberof bysdb.DbBysMarker
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {bysdb.DbBysMarker} DbBysMarker
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    DbBysMarker.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.bysdb.DbBysMarker()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.RID = reader.string()
            break
          case 2:
            message.OrgRID = reader.string()
            break
          case 3:
            message.MarkerNo = reader.string()
            break
          case 4:
            message.MarkerDescription = reader.string()
            break
          case 5:
            message.ControllerRID = reader.string()
            break
          case 6:
            message.ControllerChannel = reader.sint32()
            break
          case 7:
            message.Lon = reader.double()
            break
          case 8:
            message.Lat = reader.double()
            break
          case 9:
            message.MarkerHWID = reader.sint32()
            break
          case 10:
            message.Setting = reader.string()
            break
          case 11:
            message.MarkerModel = reader.sint32()
            break
          case 12:
            message.MapShowLevel = reader.sint32()
            break
          case 13:
            message.MarkerQueueNo = reader.sint32()
            break
          case 14:
            message.UpdatedAt = reader.string()
            break
          case 15:
            message.UpdatedDC = reader.string()
            break
          case 16:
            message.MarkerParamTime = reader.string()
            break
          case 17:
            message.MarkerDayInterval = reader.sint32()
            break
          case 18:
            message.MarkerQueueInterval = reader.sint32()
            break
          case 19:
            message.MarkerEmergentInterval = reader.sint32()
            break
          case 20:
            message.MarkerChannel = reader.sint32()
            break
          case 21:
            message.MarkerWakeupBaseTime = reader.string()
            break
          case 22:
            message.MarkerDisabled = reader.sint32()
            break
          case 23:
            message.HasInstallDevice = reader.bool()
            break
          case 25:
            message.HasInstallStone = reader.bool()
            break
          case 26:
            message.MarkerType = reader.sint32()
            break
          case 27:
            message.MarkerSettings = reader.string()
            break
          case 28:
            message.ICCID = reader.string()
            break
          case 29:
            message.ExpirationDate = reader.string()
            break
          case 30:
            message.IMEI = reader.string()
            break
          case 31:
            message.CameraDisabled = reader.sint32()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return DbBysMarker
  })()

  bysdb.DbControllerOnlineHistory = (function () {
    /**
     * Properties of a DbControllerOnlineHistory.
     * @memberof bysdb
     * @interface IDbControllerOnlineHistory
     * @property {string|null} [OrgRID] @db uuid not null
     * 所属的群组
     * @property {string|null} [ActionTime] @db timestamp not null
     * 动作时间,utc
     * @property {number|null} [ControllerHWID] @db int
     * 控制器硬件ID
     * @property {number|null} [ActionCode] @db int
     * action 1:上线 2:下线 11:ping信息
     * @property {string|null} [IpInfo] @db text
     * ip/状态信息
     * @property {number|null} [NetworkType] @db int
     * 登录网络类型,只对上线有效
     * @property {number|null} [Power] @db real
     * 电量V
     * @property {number|null} [Status] @db int
     * 状态
     * @property {number|null} [DeviceType] 登录设备类型 0:fsk控制器 1:4g界桩
     */

    /**
     * Constructs a new DbControllerOnlineHistory.
     * @memberof bysdb
     * @classdesc 控制器上下线历史表，以月为单位分表
     * @rpc pcrud
     * @dbend PARTITION BY RANGE (ActionTime)
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbControllerOnlineHistoryControllerHWID on DbControllerOnlineHistory USING hash(ControllerHWID);
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbControllerOnlineHistoryOrgRID on DbControllerOnlineHistory USING hash(OrgRID);
     * @dbpost ALTER TABLE dbcontrolleronlinehistory ADD COLUMN IF NOT EXISTS Status integer;
     * @dbpost ALTER TABLE dbcontrolleronlinehistory ADD COLUMN IF NOT EXISTS DeviceType integer;
     * @implements IDbControllerOnlineHistory
     * @constructor
     * @param {bysdb.IDbControllerOnlineHistory=} [properties] Properties to set
     */
    function DbControllerOnlineHistory(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * @db uuid not null
     * 所属的群组
     * @member {string} OrgRID
     * @memberof bysdb.DbControllerOnlineHistory
     * @instance
     */
    DbControllerOnlineHistory.prototype.OrgRID = ''

    /**
     * @db timestamp not null
     * 动作时间,utc
     * @member {string} ActionTime
     * @memberof bysdb.DbControllerOnlineHistory
     * @instance
     */
    DbControllerOnlineHistory.prototype.ActionTime = ''

    /**
     * @db int
     * 控制器硬件ID
     * @member {number} ControllerHWID
     * @memberof bysdb.DbControllerOnlineHistory
     * @instance
     */
    DbControllerOnlineHistory.prototype.ControllerHWID = 0

    /**
     * @db int
     * action 1:上线 2:下线 11:ping信息
     * @member {number} ActionCode
     * @memberof bysdb.DbControllerOnlineHistory
     * @instance
     */
    DbControllerOnlineHistory.prototype.ActionCode = 0

    /**
     * @db text
     * ip/状态信息
     * @member {string} IpInfo
     * @memberof bysdb.DbControllerOnlineHistory
     * @instance
     */
    DbControllerOnlineHistory.prototype.IpInfo = ''

    /**
     * @db int
     * 登录网络类型,只对上线有效
     * @member {number} NetworkType
     * @memberof bysdb.DbControllerOnlineHistory
     * @instance
     */
    DbControllerOnlineHistory.prototype.NetworkType = 0

    /**
     * @db real
     * 电量V
     * @member {number} Power
     * @memberof bysdb.DbControllerOnlineHistory
     * @instance
     */
    DbControllerOnlineHistory.prototype.Power = 0

    /**
     * @db int
     * 状态
     * @member {number} Status
     * @memberof bysdb.DbControllerOnlineHistory
     * @instance
     */
    DbControllerOnlineHistory.prototype.Status = 0

    /**
     * 登录设备类型 0:fsk控制器 1:4g界桩
     * @member {number} DeviceType
     * @memberof bysdb.DbControllerOnlineHistory
     * @instance
     */
    DbControllerOnlineHistory.prototype.DeviceType = 0

    /**
     * Encodes the specified DbControllerOnlineHistory message. Does not implicitly {@link bysdb.DbControllerOnlineHistory.verify|verify} messages.
     * @function encode
     * @memberof bysdb.DbControllerOnlineHistory
     * @static
     * @param {bysdb.IDbControllerOnlineHistory} message DbControllerOnlineHistory message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    DbControllerOnlineHistory.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (
        message.OrgRID != null &&
        Object.hasOwnProperty.call(message, 'OrgRID')
      )
        writer.uint32(/* id 2, wireType 2 =*/ 18).string(message.OrgRID)
      if (
        message.ActionTime != null &&
        Object.hasOwnProperty.call(message, 'ActionTime')
      )
        writer.uint32(/* id 3, wireType 2 =*/ 26).string(message.ActionTime)
      if (
        message.ControllerHWID != null &&
        Object.hasOwnProperty.call(message, 'ControllerHWID')
      )
        writer.uint32(/* id 4, wireType 0 =*/ 32).sint32(message.ControllerHWID)
      if (
        message.ActionCode != null &&
        Object.hasOwnProperty.call(message, 'ActionCode')
      )
        writer.uint32(/* id 5, wireType 0 =*/ 40).sint32(message.ActionCode)
      if (
        message.IpInfo != null &&
        Object.hasOwnProperty.call(message, 'IpInfo')
      )
        writer.uint32(/* id 6, wireType 2 =*/ 50).string(message.IpInfo)
      if (
        message.NetworkType != null &&
        Object.hasOwnProperty.call(message, 'NetworkType')
      )
        writer.uint32(/* id 7, wireType 0 =*/ 56).sint32(message.NetworkType)
      if (message.Power != null && Object.hasOwnProperty.call(message, 'Power'))
        writer.uint32(/* id 8, wireType 5 =*/ 69).float(message.Power)
      if (
        message.Status != null &&
        Object.hasOwnProperty.call(message, 'Status')
      )
        writer.uint32(/* id 9, wireType 0 =*/ 72).uint32(message.Status)
      if (
        message.DeviceType != null &&
        Object.hasOwnProperty.call(message, 'DeviceType')
      )
        writer.uint32(/* id 10, wireType 0 =*/ 80).int32(message.DeviceType)
      return writer
    }

    /**
     * Decodes a DbControllerOnlineHistory message from the specified reader or buffer.
     * @function decode
     * @memberof bysdb.DbControllerOnlineHistory
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {bysdb.DbControllerOnlineHistory} DbControllerOnlineHistory
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    DbControllerOnlineHistory.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.bysdb.DbControllerOnlineHistory()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 2:
            message.OrgRID = reader.string()
            break
          case 3:
            message.ActionTime = reader.string()
            break
          case 4:
            message.ControllerHWID = reader.sint32()
            break
          case 5:
            message.ActionCode = reader.sint32()
            break
          case 6:
            message.IpInfo = reader.string()
            break
          case 7:
            message.NetworkType = reader.sint32()
            break
          case 8:
            message.Power = reader.float()
            break
          case 9:
            message.Status = reader.uint32()
            break
          case 10:
            message.DeviceType = reader.int32()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return DbControllerOnlineHistory
  })()

  bysdb.DbMediaInfo = (function () {
    /**
     * Properties of a DbMediaInfo.
     * @memberof bysdb
     * @interface IDbMediaInfo
     * @property {string|null} [RID] @db uuid primary key
     * 行ID
     * @property {string|null} [OrgRID] @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 物体所属的群组
     * @property {string|null} [MarkerRID] @db uuid null REFERENCES DbBysMarker(RID) ON DELETE set null
     * 界桩的RID
     * @property {string|null} [ControllerRID] @db uuid null REFERENCES DbController(RID) ON DELETE set null
     * 控制器的RID
     * @property {number|null} [MediaType] @db int
     * 媒体类型 1:normal pic 2:3d pic 3:video
     * @property {string|null} [MediaOrigFileName] @db text
     * 媒体原始文件名
     * @property {string|null} [UploadUserRID] @db uuid REFERENCES DbUser(RID) ON DELETE set null
     * 上传用户rid
     * @property {string|null} [MediaDescription] @db text
     * 描述信息
     * @property {string|null} [UploadTime] @db timestamp not null
     * 上传时间,utc
     * @property {string|null} [Setting] @db jsonb not null default  '{}'::jsonb
     * 其它设置，可以在里面扩展需要用到的其它信息
     * @property {string|null} [LastUpdateUserRID] @db uuid REFERENCES DbUser(RID) ON DELETE set null
     * 最新编辑用户rid
     * @property {string|null} [LastUpdateTime] @db timestamp not null default now_utc()
     * 最新编辑时间,utc
     */

    /**
     * Constructs a new DbMediaInfo.
     * @memberof bysdb
     * @classdesc 界桩/控制器媒体信息表
     * @rpc pcrud
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbMediaInfoOrgRID on DbMediaInfo USING hash(OrgRID);
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbMediaInfoMarkerRID on DbMediaInfo USING hash(MarkerRID);
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbMediaInfoControllerRID on DbMediaInfo USING hash(ControllerRID);
     * @implements IDbMediaInfo
     * @constructor
     * @param {bysdb.IDbMediaInfo=} [properties] Properties to set
     */
    function DbMediaInfo(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * @db uuid primary key
     * 行ID
     * @member {string} RID
     * @memberof bysdb.DbMediaInfo
     * @instance
     */
    DbMediaInfo.prototype.RID = ''

    /**
     * @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 物体所属的群组
     * @member {string} OrgRID
     * @memberof bysdb.DbMediaInfo
     * @instance
     */
    DbMediaInfo.prototype.OrgRID = ''

    /**
     * @db uuid null REFERENCES DbBysMarker(RID) ON DELETE set null
     * 界桩的RID
     * @member {string} MarkerRID
     * @memberof bysdb.DbMediaInfo
     * @instance
     */
    DbMediaInfo.prototype.MarkerRID = ''

    /**
     * @db uuid null REFERENCES DbController(RID) ON DELETE set null
     * 控制器的RID
     * @member {string} ControllerRID
     * @memberof bysdb.DbMediaInfo
     * @instance
     */
    DbMediaInfo.prototype.ControllerRID = ''

    /**
     * @db int
     * 媒体类型 1:normal pic 2:3d pic 3:video
     * @member {number} MediaType
     * @memberof bysdb.DbMediaInfo
     * @instance
     */
    DbMediaInfo.prototype.MediaType = 0

    /**
     * @db text
     * 媒体原始文件名
     * @member {string} MediaOrigFileName
     * @memberof bysdb.DbMediaInfo
     * @instance
     */
    DbMediaInfo.prototype.MediaOrigFileName = ''

    /**
     * @db uuid REFERENCES DbUser(RID) ON DELETE set null
     * 上传用户rid
     * @member {string} UploadUserRID
     * @memberof bysdb.DbMediaInfo
     * @instance
     */
    DbMediaInfo.prototype.UploadUserRID = ''

    /**
     * @db text
     * 描述信息
     * @member {string} MediaDescription
     * @memberof bysdb.DbMediaInfo
     * @instance
     */
    DbMediaInfo.prototype.MediaDescription = ''

    /**
     * @db timestamp not null
     * 上传时间,utc
     * @member {string} UploadTime
     * @memberof bysdb.DbMediaInfo
     * @instance
     */
    DbMediaInfo.prototype.UploadTime = ''

    /**
     * @db jsonb not null default  '{}'::jsonb
     * 其它设置，可以在里面扩展需要用到的其它信息
     * @member {string} Setting
     * @memberof bysdb.DbMediaInfo
     * @instance
     */
    DbMediaInfo.prototype.Setting = ''

    /**
     * @db uuid REFERENCES DbUser(RID) ON DELETE set null
     * 最新编辑用户rid
     * @member {string} LastUpdateUserRID
     * @memberof bysdb.DbMediaInfo
     * @instance
     */
    DbMediaInfo.prototype.LastUpdateUserRID = ''

    /**
     * @db timestamp not null default now_utc()
     * 最新编辑时间,utc
     * @member {string} LastUpdateTime
     * @memberof bysdb.DbMediaInfo
     * @instance
     */
    DbMediaInfo.prototype.LastUpdateTime = ''

    /**
     * Encodes the specified DbMediaInfo message. Does not implicitly {@link bysdb.DbMediaInfo.verify|verify} messages.
     * @function encode
     * @memberof bysdb.DbMediaInfo
     * @static
     * @param {bysdb.IDbMediaInfo} message DbMediaInfo message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    DbMediaInfo.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (message.RID != null && Object.hasOwnProperty.call(message, 'RID'))
        writer.uint32(/* id 1, wireType 2 =*/ 10).string(message.RID)
      if (
        message.OrgRID != null &&
        Object.hasOwnProperty.call(message, 'OrgRID')
      )
        writer.uint32(/* id 2, wireType 2 =*/ 18).string(message.OrgRID)
      if (
        message.MarkerRID != null &&
        Object.hasOwnProperty.call(message, 'MarkerRID')
      )
        writer.uint32(/* id 3, wireType 2 =*/ 26).string(message.MarkerRID)
      if (
        message.ControllerRID != null &&
        Object.hasOwnProperty.call(message, 'ControllerRID')
      )
        writer.uint32(/* id 4, wireType 2 =*/ 34).string(message.ControllerRID)
      if (
        message.MediaType != null &&
        Object.hasOwnProperty.call(message, 'MediaType')
      )
        writer.uint32(/* id 5, wireType 0 =*/ 40).sint32(message.MediaType)
      if (
        message.MediaOrigFileName != null &&
        Object.hasOwnProperty.call(message, 'MediaOrigFileName')
      )
        writer
          .uint32(/* id 6, wireType 2 =*/ 50)
          .string(message.MediaOrigFileName)
      if (
        message.UploadUserRID != null &&
        Object.hasOwnProperty.call(message, 'UploadUserRID')
      )
        writer.uint32(/* id 7, wireType 2 =*/ 58).string(message.UploadUserRID)
      if (
        message.MediaDescription != null &&
        Object.hasOwnProperty.call(message, 'MediaDescription')
      )
        writer
          .uint32(/* id 8, wireType 2 =*/ 66)
          .string(message.MediaDescription)
      if (
        message.UploadTime != null &&
        Object.hasOwnProperty.call(message, 'UploadTime')
      )
        writer.uint32(/* id 9, wireType 2 =*/ 74).string(message.UploadTime)
      if (
        message.Setting != null &&
        Object.hasOwnProperty.call(message, 'Setting')
      )
        writer.uint32(/* id 10, wireType 2 =*/ 82).string(message.Setting)
      if (
        message.LastUpdateUserRID != null &&
        Object.hasOwnProperty.call(message, 'LastUpdateUserRID')
      )
        writer
          .uint32(/* id 11, wireType 2 =*/ 90)
          .string(message.LastUpdateUserRID)
      if (
        message.LastUpdateTime != null &&
        Object.hasOwnProperty.call(message, 'LastUpdateTime')
      )
        writer
          .uint32(/* id 12, wireType 2 =*/ 98)
          .string(message.LastUpdateTime)
      return writer
    }

    /**
     * Decodes a DbMediaInfo message from the specified reader or buffer.
     * @function decode
     * @memberof bysdb.DbMediaInfo
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {bysdb.DbMediaInfo} DbMediaInfo
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    DbMediaInfo.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.bysdb.DbMediaInfo()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.RID = reader.string()
            break
          case 2:
            message.OrgRID = reader.string()
            break
          case 3:
            message.MarkerRID = reader.string()
            break
          case 4:
            message.ControllerRID = reader.string()
            break
          case 5:
            message.MediaType = reader.sint32()
            break
          case 6:
            message.MediaOrigFileName = reader.string()
            break
          case 7:
            message.UploadUserRID = reader.string()
            break
          case 8:
            message.MediaDescription = reader.string()
            break
          case 9:
            message.UploadTime = reader.string()
            break
          case 10:
            message.Setting = reader.string()
            break
          case 11:
            message.LastUpdateUserRID = reader.string()
            break
          case 12:
            message.LastUpdateTime = reader.string()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return DbMediaInfo
  })()

  bysdb.DbMarkerHistory = (function () {
    /**
     * Properties of a DbMarkerHistory.
     * @memberof bysdb
     * @interface IDbMarkerHistory
     * @property {number|null} [ControllerID] @db int
     * 接收的控制器ID
     * @property {number|null} [ControllerChannel] @db int
     * 接收的控制器通道
     * @property {string|null} [OrgRID] @db uuid not null
     * 所属的群组
     * @property {string|null} [ActionTime] @db timestamp not null
     * 动作时间,utc
     * @property {number|null} [MarkerHWID] @db int
     * 界桩硬件ID
     * @property {number|null} [ActionCode] @db int
     * action 0xd1, 0xd2
     * 4g界桩信息上报21, ReportInfo=bysproto.BInfoReporting
     * 4g界桩报警22, ReportInfo=bysproto.BAlarmReporting
     * @property {number|null} [Status] @db int
     * @property {string|null} [ParamInfo] @db text
     * 配置参数信息
     * @property {number|null} [Lon] @db double precision
     * gps lon
     * @property {number|null} [Lat] @db double precision
     * gps lat
     * @property {string|null} [CmdTime] @db timestamp
     * cmd time
     * @property {number|null} [RecvControllerID] @db int
     * 实际接收的控制器ID（中继/基站）
     * @property {string|null} [ReportInfo] @db jsonb not null default  '{}'::jsonb
     */

    /**
     * Constructs a new DbMarkerHistory.
     * @memberof bysdb
     * @classdesc 界桩上传数据历史表，以月为单位分表
     * @rpc pcrud
     * @dbend PARTITION BY RANGE (ActionTime)
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbMarkerHistoryMarkerHWID on DbMarkerHistory USING hash(MarkerHWID);
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbMarkerHistoryOrgRID on DbMarkerHistory USING hash(OrgRID);
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbMarkerHistoryEmergency on DbMarkerHistory (Status) where (Status & 128) >0;
     * @dbpost alter table DbMarkerHistory ADD COLUMN IF NOT EXISTS RecvControllerID int;
     * @dbpost alter table DbMarkerHistory ADD COLUMN IF NOT EXISTS ReportInfo jsonb not null default  '{}'::jsonb;
     * @implements IDbMarkerHistory
     * @constructor
     * @param {bysdb.IDbMarkerHistory=} [properties] Properties to set
     */
    function DbMarkerHistory(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * @db int
     * 接收的控制器ID
     * @member {number} ControllerID
     * @memberof bysdb.DbMarkerHistory
     * @instance
     */
    DbMarkerHistory.prototype.ControllerID = 0

    /**
     * @db int
     * 接收的控制器通道
     * @member {number} ControllerChannel
     * @memberof bysdb.DbMarkerHistory
     * @instance
     */
    DbMarkerHistory.prototype.ControllerChannel = 0

    /**
     * @db uuid not null
     * 所属的群组
     * @member {string} OrgRID
     * @memberof bysdb.DbMarkerHistory
     * @instance
     */
    DbMarkerHistory.prototype.OrgRID = ''

    /**
     * @db timestamp not null
     * 动作时间,utc
     * @member {string} ActionTime
     * @memberof bysdb.DbMarkerHistory
     * @instance
     */
    DbMarkerHistory.prototype.ActionTime = ''

    /**
     * @db int
     * 界桩硬件ID
     * @member {number} MarkerHWID
     * @memberof bysdb.DbMarkerHistory
     * @instance
     */
    DbMarkerHistory.prototype.MarkerHWID = 0

    /**
     * @db int
     * action 0xd1, 0xd2
     * 4g界桩信息上报21, ReportInfo=bysproto.BInfoReporting
     * 4g界桩报警22, ReportInfo=bysproto.BAlarmReporting
     * @member {number} ActionCode
     * @memberof bysdb.DbMarkerHistory
     * @instance
     */
    DbMarkerHistory.prototype.ActionCode = 0

    /**
     * @db int
     * @member {number} Status
     * @memberof bysdb.DbMarkerHistory
     * @instance
     */
    DbMarkerHistory.prototype.Status = 0

    /**
     * @db text
     * 配置参数信息
     * @member {string} ParamInfo
     * @memberof bysdb.DbMarkerHistory
     * @instance
     */
    DbMarkerHistory.prototype.ParamInfo = ''

    /**
     * @db double precision
     * gps lon
     * @member {number} Lon
     * @memberof bysdb.DbMarkerHistory
     * @instance
     */
    DbMarkerHistory.prototype.Lon = 0

    /**
     * @db double precision
     * gps lat
     * @member {number} Lat
     * @memberof bysdb.DbMarkerHistory
     * @instance
     */
    DbMarkerHistory.prototype.Lat = 0

    /**
     * @db timestamp
     * cmd time
     * @member {string} CmdTime
     * @memberof bysdb.DbMarkerHistory
     * @instance
     */
    DbMarkerHistory.prototype.CmdTime = ''

    /**
     * @db int
     * 实际接收的控制器ID（中继/基站）
     * @member {number} RecvControllerID
     * @memberof bysdb.DbMarkerHistory
     * @instance
     */
    DbMarkerHistory.prototype.RecvControllerID = 0

    /**
     * @db jsonb not null default  '{}'::jsonb
     * @member {string} ReportInfo
     * @memberof bysdb.DbMarkerHistory
     * @instance
     */
    DbMarkerHistory.prototype.ReportInfo = ''

    /**
     * Encodes the specified DbMarkerHistory message. Does not implicitly {@link bysdb.DbMarkerHistory.verify|verify} messages.
     * @function encode
     * @memberof bysdb.DbMarkerHistory
     * @static
     * @param {bysdb.IDbMarkerHistory} message DbMarkerHistory message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    DbMarkerHistory.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (
        message.ControllerID != null &&
        Object.hasOwnProperty.call(message, 'ControllerID')
      )
        writer.uint32(/* id 1, wireType 0 =*/ 8).sint32(message.ControllerID)
      if (
        message.ControllerChannel != null &&
        Object.hasOwnProperty.call(message, 'ControllerChannel')
      )
        writer
          .uint32(/* id 2, wireType 0 =*/ 16)
          .sint32(message.ControllerChannel)
      if (
        message.OrgRID != null &&
        Object.hasOwnProperty.call(message, 'OrgRID')
      )
        writer.uint32(/* id 3, wireType 2 =*/ 26).string(message.OrgRID)
      if (
        message.ActionTime != null &&
        Object.hasOwnProperty.call(message, 'ActionTime')
      )
        writer.uint32(/* id 4, wireType 2 =*/ 34).string(message.ActionTime)
      if (
        message.MarkerHWID != null &&
        Object.hasOwnProperty.call(message, 'MarkerHWID')
      )
        writer.uint32(/* id 5, wireType 0 =*/ 40).sint32(message.MarkerHWID)
      if (
        message.ActionCode != null &&
        Object.hasOwnProperty.call(message, 'ActionCode')
      )
        writer.uint32(/* id 6, wireType 0 =*/ 48).sint32(message.ActionCode)
      if (
        message.Status != null &&
        Object.hasOwnProperty.call(message, 'Status')
      )
        writer.uint32(/* id 7, wireType 0 =*/ 56).sint32(message.Status)
      if (
        message.ParamInfo != null &&
        Object.hasOwnProperty.call(message, 'ParamInfo')
      )
        writer.uint32(/* id 8, wireType 2 =*/ 66).string(message.ParamInfo)
      if (message.Lon != null && Object.hasOwnProperty.call(message, 'Lon'))
        writer.uint32(/* id 9, wireType 1 =*/ 73).double(message.Lon)
      if (message.Lat != null && Object.hasOwnProperty.call(message, 'Lat'))
        writer.uint32(/* id 10, wireType 1 =*/ 81).double(message.Lat)
      if (
        message.CmdTime != null &&
        Object.hasOwnProperty.call(message, 'CmdTime')
      )
        writer.uint32(/* id 11, wireType 2 =*/ 90).string(message.CmdTime)
      if (
        message.RecvControllerID != null &&
        Object.hasOwnProperty.call(message, 'RecvControllerID')
      )
        writer
          .uint32(/* id 12, wireType 0 =*/ 96)
          .sint32(message.RecvControllerID)
      if (
        message.ReportInfo != null &&
        Object.hasOwnProperty.call(message, 'ReportInfo')
      )
        writer.uint32(/* id 13, wireType 2 =*/ 106).string(message.ReportInfo)
      return writer
    }

    /**
     * Decodes a DbMarkerHistory message from the specified reader or buffer.
     * @function decode
     * @memberof bysdb.DbMarkerHistory
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {bysdb.DbMarkerHistory} DbMarkerHistory
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    DbMarkerHistory.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.bysdb.DbMarkerHistory()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.ControllerID = reader.sint32()
            break
          case 2:
            message.ControllerChannel = reader.sint32()
            break
          case 3:
            message.OrgRID = reader.string()
            break
          case 4:
            message.ActionTime = reader.string()
            break
          case 5:
            message.MarkerHWID = reader.sint32()
            break
          case 6:
            message.ActionCode = reader.sint32()
            break
          case 7:
            message.Status = reader.sint32()
            break
          case 8:
            message.ParamInfo = reader.string()
            break
          case 9:
            message.Lon = reader.double()
            break
          case 10:
            message.Lat = reader.double()
            break
          case 11:
            message.CmdTime = reader.string()
            break
          case 12:
            message.RecvControllerID = reader.sint32()
            break
          case 13:
            message.ReportInfo = reader.string()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return DbMarkerHistory
  })()

  bysdb.DbMarkerPatrolHistory = (function () {
    /**
     * Properties of a DbMarkerPatrolHistory.
     * @memberof bysdb
     * @interface IDbMarkerPatrolHistory
     * @property {string|null} [NFCID] @db text
     * NFC卡片ID，hex string
     * @property {string|null} [ActionTime] @db timestamp not null
     * NFC打卡时间,utc
     * @property {number|null} [MarkerHWID] @db int
     * 界桩硬件ID
     * @property {string|null} [UserID] @db uuid not null
     * 打卡用户
     * @property {string|null} [OrgRID] @db uuid not null
     * 所属的群组
     */

    /**
     * Constructs a new DbMarkerPatrolHistory.
     * @memberof bysdb
     * @classdesc 4g界桩NFC巡查打卡历史表，以月为单位分表
     * @rpc pcrud
     * @dbend PARTITION BY RANGE (ActionTime)
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbMarkerPatrolHistoryHWID on DbMarkerPatrolHistory USING hash(MarkerHWID);
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbMarkerPatrolHistoryOrgRID on DbMarkerPatrolHistory USING hash(OrgRID);
     * @implements IDbMarkerPatrolHistory
     * @constructor
     * @param {bysdb.IDbMarkerPatrolHistory=} [properties] Properties to set
     */
    function DbMarkerPatrolHistory(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * @db text
     * NFC卡片ID，hex string
     * @member {string} NFCID
     * @memberof bysdb.DbMarkerPatrolHistory
     * @instance
     */
    DbMarkerPatrolHistory.prototype.NFCID = ''

    /**
     * @db timestamp not null
     * NFC打卡时间,utc
     * @member {string} ActionTime
     * @memberof bysdb.DbMarkerPatrolHistory
     * @instance
     */
    DbMarkerPatrolHistory.prototype.ActionTime = ''

    /**
     * @db int
     * 界桩硬件ID
     * @member {number} MarkerHWID
     * @memberof bysdb.DbMarkerPatrolHistory
     * @instance
     */
    DbMarkerPatrolHistory.prototype.MarkerHWID = 0

    /**
     * @db uuid not null
     * 打卡用户
     * @member {string} UserID
     * @memberof bysdb.DbMarkerPatrolHistory
     * @instance
     */
    DbMarkerPatrolHistory.prototype.UserID = ''

    /**
     * @db uuid not null
     * 所属的群组
     * @member {string} OrgRID
     * @memberof bysdb.DbMarkerPatrolHistory
     * @instance
     */
    DbMarkerPatrolHistory.prototype.OrgRID = ''

    /**
     * Encodes the specified DbMarkerPatrolHistory message. Does not implicitly {@link bysdb.DbMarkerPatrolHistory.verify|verify} messages.
     * @function encode
     * @memberof bysdb.DbMarkerPatrolHistory
     * @static
     * @param {bysdb.IDbMarkerPatrolHistory} message DbMarkerPatrolHistory message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    DbMarkerPatrolHistory.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (message.NFCID != null && Object.hasOwnProperty.call(message, 'NFCID'))
        writer.uint32(/* id 1, wireType 2 =*/ 10).string(message.NFCID)
      if (
        message.ActionTime != null &&
        Object.hasOwnProperty.call(message, 'ActionTime')
      )
        writer.uint32(/* id 2, wireType 2 =*/ 18).string(message.ActionTime)
      if (
        message.MarkerHWID != null &&
        Object.hasOwnProperty.call(message, 'MarkerHWID')
      )
        writer.uint32(/* id 3, wireType 0 =*/ 24).sint32(message.MarkerHWID)
      if (
        message.UserID != null &&
        Object.hasOwnProperty.call(message, 'UserID')
      )
        writer.uint32(/* id 4, wireType 2 =*/ 34).string(message.UserID)
      if (
        message.OrgRID != null &&
        Object.hasOwnProperty.call(message, 'OrgRID')
      )
        writer.uint32(/* id 5, wireType 2 =*/ 42).string(message.OrgRID)
      return writer
    }

    /**
     * Decodes a DbMarkerPatrolHistory message from the specified reader or buffer.
     * @function decode
     * @memberof bysdb.DbMarkerPatrolHistory
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {bysdb.DbMarkerPatrolHistory} DbMarkerPatrolHistory
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    DbMarkerPatrolHistory.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.bysdb.DbMarkerPatrolHistory()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.NFCID = reader.string()
            break
          case 2:
            message.ActionTime = reader.string()
            break
          case 3:
            message.MarkerHWID = reader.sint32()
            break
          case 4:
            message.UserID = reader.string()
            break
          case 5:
            message.OrgRID = reader.string()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return DbMarkerPatrolHistory
  })()

  bysdb.DbNFCPatrolLine = (function () {
    /**
     * Properties of a DbNFCPatrolLine.
     * @memberof bysdb
     * @interface IDbNFCPatrolLine
     * @property {string|null} [RID] @db uuid primary key
     * 行ID
     * @property {string|null} [OrgRID] @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 线路的归属组
     * @property {string|null} [Name] @db varchar(16) not null unique
     * 线路名称
     * @property {string|null} [Note] @db text
     * @property {string|null} [UpdatedAt] @db timestamp not null default now_utc()
     * 数据最后修改时间
     * @property {string|null} [UpdatedDC] @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */

    /**
     * Constructs a new DbNFCPatrolLine.
     * @memberof bysdb
     * @classdesc 巡查线路表
     * @rpc pcrud
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbNFCPatrolLineOrgRID on DbNFCPatrolLine USING hash(OrgRID);
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbNFCPatrolLineName on DbNFCPatrolLine USING hash(Name);
     * @implements IDbNFCPatrolLine
     * @constructor
     * @param {bysdb.IDbNFCPatrolLine=} [properties] Properties to set
     */
    function DbNFCPatrolLine(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * @db uuid primary key
     * 行ID
     * @member {string} RID
     * @memberof bysdb.DbNFCPatrolLine
     * @instance
     */
    DbNFCPatrolLine.prototype.RID = ''

    /**
     * @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 线路的归属组
     * @member {string} OrgRID
     * @memberof bysdb.DbNFCPatrolLine
     * @instance
     */
    DbNFCPatrolLine.prototype.OrgRID = ''

    /**
     * @db varchar(16) not null unique
     * 线路名称
     * @member {string} Name
     * @memberof bysdb.DbNFCPatrolLine
     * @instance
     */
    DbNFCPatrolLine.prototype.Name = ''

    /**
     * @db text
     * @member {string} Note
     * @memberof bysdb.DbNFCPatrolLine
     * @instance
     */
    DbNFCPatrolLine.prototype.Note = ''

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     * @member {string} UpdatedAt
     * @memberof bysdb.DbNFCPatrolLine
     * @instance
     */
    DbNFCPatrolLine.prototype.UpdatedAt = ''

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     * @member {string} UpdatedDC
     * @memberof bysdb.DbNFCPatrolLine
     * @instance
     */
    DbNFCPatrolLine.prototype.UpdatedDC = ''

    /**
     * Encodes the specified DbNFCPatrolLine message. Does not implicitly {@link bysdb.DbNFCPatrolLine.verify|verify} messages.
     * @function encode
     * @memberof bysdb.DbNFCPatrolLine
     * @static
     * @param {bysdb.IDbNFCPatrolLine} message DbNFCPatrolLine message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    DbNFCPatrolLine.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (message.RID != null && Object.hasOwnProperty.call(message, 'RID'))
        writer.uint32(/* id 1, wireType 2 =*/ 10).string(message.RID)
      if (
        message.OrgRID != null &&
        Object.hasOwnProperty.call(message, 'OrgRID')
      )
        writer.uint32(/* id 2, wireType 2 =*/ 18).string(message.OrgRID)
      if (message.Name != null && Object.hasOwnProperty.call(message, 'Name'))
        writer.uint32(/* id 3, wireType 2 =*/ 26).string(message.Name)
      if (message.Note != null && Object.hasOwnProperty.call(message, 'Note'))
        writer.uint32(/* id 4, wireType 2 =*/ 34).string(message.Note)
      if (
        message.UpdatedAt != null &&
        Object.hasOwnProperty.call(message, 'UpdatedAt')
      )
        writer.uint32(/* id 5, wireType 2 =*/ 42).string(message.UpdatedAt)
      if (
        message.UpdatedDC != null &&
        Object.hasOwnProperty.call(message, 'UpdatedDC')
      )
        writer.uint32(/* id 6, wireType 2 =*/ 50).string(message.UpdatedDC)
      return writer
    }

    /**
     * Decodes a DbNFCPatrolLine message from the specified reader or buffer.
     * @function decode
     * @memberof bysdb.DbNFCPatrolLine
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {bysdb.DbNFCPatrolLine} DbNFCPatrolLine
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    DbNFCPatrolLine.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.bysdb.DbNFCPatrolLine()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.RID = reader.string()
            break
          case 2:
            message.OrgRID = reader.string()
            break
          case 3:
            message.Name = reader.string()
            break
          case 4:
            message.Note = reader.string()
            break
          case 5:
            message.UpdatedAt = reader.string()
            break
          case 6:
            message.UpdatedDC = reader.string()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return DbNFCPatrolLine
  })()

  bysdb.DbNFCPatrolLineDetail = (function () {
    /**
     * Properties of a DbNFCPatrolLineDetail.
     * @memberof bysdb
     * @interface IDbNFCPatrolLineDetail
     * @property {string|null} [RID] @db uuid primary key
     * 行ID
     * @property {string|null} [LineRID] @db uuid not null REFERENCES DbNFCPatrolLine(RID) ON DELETE CASCADE
     * 归属线路
     * @property {string|null} [MarkerRID] @db uuid not null REFERENCES DbBysMarker(RID) ON DELETE CASCADE
     * 线路下的巡查点
     * @property {string|null} [UpdatedAt] @db timestamp not null default now_utc()
     * 数据最后修改时间
     * @property {string|null} [OrgRID] @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 归属组，与路线的上线一致
     */

    /**
     * Constructs a new DbNFCPatrolLineDetail.
     * @memberof bysdb
     * @classdesc 巡查线路详细表，分开主要是方便使用数据库外键约束
     * @rpc pcrud
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbNFCPatrolLineDetailOrgRID on DbNFCPatrolLineDetail USING hash(OrgRID);
     * @dbpost CREATE INDEX IF NOT EXISTS idxDDbNFCPatrolLineDetailLineRID on DbNFCPatrolLineDetail USING hash(LineRID);
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbNFCPatrolLineDetailMarkerRID on DbNFCPatrolLineDetail USING hash(MarkerRID);
     * @implements IDbNFCPatrolLineDetail
     * @constructor
     * @param {bysdb.IDbNFCPatrolLineDetail=} [properties] Properties to set
     */
    function DbNFCPatrolLineDetail(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * @db uuid primary key
     * 行ID
     * @member {string} RID
     * @memberof bysdb.DbNFCPatrolLineDetail
     * @instance
     */
    DbNFCPatrolLineDetail.prototype.RID = ''

    /**
     * @db uuid not null REFERENCES DbNFCPatrolLine(RID) ON DELETE CASCADE
     * 归属线路
     * @member {string} LineRID
     * @memberof bysdb.DbNFCPatrolLineDetail
     * @instance
     */
    DbNFCPatrolLineDetail.prototype.LineRID = ''

    /**
     * @db uuid not null REFERENCES DbBysMarker(RID) ON DELETE CASCADE
     * 线路下的巡查点
     * @member {string} MarkerRID
     * @memberof bysdb.DbNFCPatrolLineDetail
     * @instance
     */
    DbNFCPatrolLineDetail.prototype.MarkerRID = ''

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     * @member {string} UpdatedAt
     * @memberof bysdb.DbNFCPatrolLineDetail
     * @instance
     */
    DbNFCPatrolLineDetail.prototype.UpdatedAt = ''

    /**
     * @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 归属组，与路线的上线一致
     * @member {string} OrgRID
     * @memberof bysdb.DbNFCPatrolLineDetail
     * @instance
     */
    DbNFCPatrolLineDetail.prototype.OrgRID = ''

    /**
     * Encodes the specified DbNFCPatrolLineDetail message. Does not implicitly {@link bysdb.DbNFCPatrolLineDetail.verify|verify} messages.
     * @function encode
     * @memberof bysdb.DbNFCPatrolLineDetail
     * @static
     * @param {bysdb.IDbNFCPatrolLineDetail} message DbNFCPatrolLineDetail message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    DbNFCPatrolLineDetail.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (message.RID != null && Object.hasOwnProperty.call(message, 'RID'))
        writer.uint32(/* id 1, wireType 2 =*/ 10).string(message.RID)
      if (
        message.LineRID != null &&
        Object.hasOwnProperty.call(message, 'LineRID')
      )
        writer.uint32(/* id 2, wireType 2 =*/ 18).string(message.LineRID)
      if (
        message.MarkerRID != null &&
        Object.hasOwnProperty.call(message, 'MarkerRID')
      )
        writer.uint32(/* id 3, wireType 2 =*/ 26).string(message.MarkerRID)
      if (
        message.UpdatedAt != null &&
        Object.hasOwnProperty.call(message, 'UpdatedAt')
      )
        writer.uint32(/* id 4, wireType 2 =*/ 34).string(message.UpdatedAt)
      if (
        message.OrgRID != null &&
        Object.hasOwnProperty.call(message, 'OrgRID')
      )
        writer.uint32(/* id 5, wireType 2 =*/ 42).string(message.OrgRID)
      return writer
    }

    /**
     * Decodes a DbNFCPatrolLineDetail message from the specified reader or buffer.
     * @function decode
     * @memberof bysdb.DbNFCPatrolLineDetail
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {bysdb.DbNFCPatrolLineDetail} DbNFCPatrolLineDetail
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    DbNFCPatrolLineDetail.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.bysdb.DbNFCPatrolLineDetail()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.RID = reader.string()
            break
          case 2:
            message.LineRID = reader.string()
            break
          case 3:
            message.MarkerRID = reader.string()
            break
          case 4:
            message.UpdatedAt = reader.string()
            break
          case 5:
            message.OrgRID = reader.string()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return DbNFCPatrolLineDetail
  })()

  bysdb.DbNFCPatrolLineRules = (function () {
    /**
     * Properties of a DbNFCPatrolLineRules.
     * @memberof bysdb
     * @interface IDbNFCPatrolLineRules
     * @property {string|null} [RID] @db uuid primary key
     * 行ID
     * @property {string|null} [OrgRID] @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 规则的归属组
     * @property {string|null} [Name] @db varchar(16) not null unique
     * 规则名称
     * @property {boolean|null} [Day1] @db boolean default false
     * 星期一
     * @property {boolean|null} [Day2] @db boolean default false
     * 星期二
     * @property {boolean|null} [Day3] @db boolean default false
     * 星期三
     * @property {boolean|null} [Day4] @db boolean default false
     * 星期四
     * @property {boolean|null} [Day5] @db boolean default false
     * 星期五
     * @property {boolean|null} [Day6] @db boolean default false
     * 星期六
     * @property {boolean|null} [Day7] @db boolean default false
     * 星期日
     * @property {string|null} [CheckStartTime] @db time
     * 巡查开始的时间
     * @property {string|null} [CheckEndTime] @db time
     * 巡查结束的时间
     * @property {number|null} [CheckCount] @db int not null default 1
     * 巡查次数
     * @property {number|null} [EffectiveType] @db int default 0
     * 线路巡查规则生效时间的类型 0:总是生效 1:按有效时间段
     * @property {string|null} [EffectiveStart] @db timestamp
     * 规则开始生效时间
     * @property {string|null} [EffectiveEnd] @db timestamp
     * 规则生效结束时间
     * @property {string|null} [Note] @db text
     * @property {string|null} [UpdatedAt] @db timestamp not null default now_utc()
     * 数据最后修改时间
     * @property {string|null} [UpdatedDC] @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */

    /**
     * Constructs a new DbNFCPatrolLineRules.
     * @memberof bysdb
     * @classdesc 界桩NFC巡查规则
     * @rpc pcrud
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbNFCPatrolLineRulesOrgRID on DbNFCPatrolLineRules USING hash(OrgRID);
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbNFCPatrolLineRulesName on DbNFCPatrolLineRules USING hash(Name);
     * @implements IDbNFCPatrolLineRules
     * @constructor
     * @param {bysdb.IDbNFCPatrolLineRules=} [properties] Properties to set
     */
    function DbNFCPatrolLineRules(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * @db uuid primary key
     * 行ID
     * @member {string} RID
     * @memberof bysdb.DbNFCPatrolLineRules
     * @instance
     */
    DbNFCPatrolLineRules.prototype.RID = ''

    /**
     * @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 规则的归属组
     * @member {string} OrgRID
     * @memberof bysdb.DbNFCPatrolLineRules
     * @instance
     */
    DbNFCPatrolLineRules.prototype.OrgRID = ''

    /**
     * @db varchar(16) not null unique
     * 规则名称
     * @member {string} Name
     * @memberof bysdb.DbNFCPatrolLineRules
     * @instance
     */
    DbNFCPatrolLineRules.prototype.Name = ''

    /**
     * @db boolean default false
     * 星期一
     * @member {boolean} Day1
     * @memberof bysdb.DbNFCPatrolLineRules
     * @instance
     */
    DbNFCPatrolLineRules.prototype.Day1 = false

    /**
     * @db boolean default false
     * 星期二
     * @member {boolean} Day2
     * @memberof bysdb.DbNFCPatrolLineRules
     * @instance
     */
    DbNFCPatrolLineRules.prototype.Day2 = false

    /**
     * @db boolean default false
     * 星期三
     * @member {boolean} Day3
     * @memberof bysdb.DbNFCPatrolLineRules
     * @instance
     */
    DbNFCPatrolLineRules.prototype.Day3 = false

    /**
     * @db boolean default false
     * 星期四
     * @member {boolean} Day4
     * @memberof bysdb.DbNFCPatrolLineRules
     * @instance
     */
    DbNFCPatrolLineRules.prototype.Day4 = false

    /**
     * @db boolean default false
     * 星期五
     * @member {boolean} Day5
     * @memberof bysdb.DbNFCPatrolLineRules
     * @instance
     */
    DbNFCPatrolLineRules.prototype.Day5 = false

    /**
     * @db boolean default false
     * 星期六
     * @member {boolean} Day6
     * @memberof bysdb.DbNFCPatrolLineRules
     * @instance
     */
    DbNFCPatrolLineRules.prototype.Day6 = false

    /**
     * @db boolean default false
     * 星期日
     * @member {boolean} Day7
     * @memberof bysdb.DbNFCPatrolLineRules
     * @instance
     */
    DbNFCPatrolLineRules.prototype.Day7 = false

    /**
     * @db time
     * 巡查开始的时间
     * @member {string} CheckStartTime
     * @memberof bysdb.DbNFCPatrolLineRules
     * @instance
     */
    DbNFCPatrolLineRules.prototype.CheckStartTime = ''

    /**
     * @db time
     * 巡查结束的时间
     * @member {string} CheckEndTime
     * @memberof bysdb.DbNFCPatrolLineRules
     * @instance
     */
    DbNFCPatrolLineRules.prototype.CheckEndTime = ''

    /**
     * @db int not null default 1
     * 巡查次数
     * @member {number} CheckCount
     * @memberof bysdb.DbNFCPatrolLineRules
     * @instance
     */
    DbNFCPatrolLineRules.prototype.CheckCount = 0

    /**
     * @db int default 0
     * 线路巡查规则生效时间的类型 0:总是生效 1:按有效时间段
     * @member {number} EffectiveType
     * @memberof bysdb.DbNFCPatrolLineRules
     * @instance
     */
    DbNFCPatrolLineRules.prototype.EffectiveType = 0

    /**
     * @db timestamp
     * 规则开始生效时间
     * @member {string} EffectiveStart
     * @memberof bysdb.DbNFCPatrolLineRules
     * @instance
     */
    DbNFCPatrolLineRules.prototype.EffectiveStart = ''

    /**
     * @db timestamp
     * 规则生效结束时间
     * @member {string} EffectiveEnd
     * @memberof bysdb.DbNFCPatrolLineRules
     * @instance
     */
    DbNFCPatrolLineRules.prototype.EffectiveEnd = ''

    /**
     * @db text
     * @member {string} Note
     * @memberof bysdb.DbNFCPatrolLineRules
     * @instance
     */
    DbNFCPatrolLineRules.prototype.Note = ''

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     * @member {string} UpdatedAt
     * @memberof bysdb.DbNFCPatrolLineRules
     * @instance
     */
    DbNFCPatrolLineRules.prototype.UpdatedAt = ''

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     * @member {string} UpdatedDC
     * @memberof bysdb.DbNFCPatrolLineRules
     * @instance
     */
    DbNFCPatrolLineRules.prototype.UpdatedDC = ''

    /**
     * Encodes the specified DbNFCPatrolLineRules message. Does not implicitly {@link bysdb.DbNFCPatrolLineRules.verify|verify} messages.
     * @function encode
     * @memberof bysdb.DbNFCPatrolLineRules
     * @static
     * @param {bysdb.IDbNFCPatrolLineRules} message DbNFCPatrolLineRules message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    DbNFCPatrolLineRules.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (message.RID != null && Object.hasOwnProperty.call(message, 'RID'))
        writer.uint32(/* id 1, wireType 2 =*/ 10).string(message.RID)
      if (
        message.OrgRID != null &&
        Object.hasOwnProperty.call(message, 'OrgRID')
      )
        writer.uint32(/* id 2, wireType 2 =*/ 18).string(message.OrgRID)
      if (message.Name != null && Object.hasOwnProperty.call(message, 'Name'))
        writer.uint32(/* id 3, wireType 2 =*/ 26).string(message.Name)
      if (message.Day1 != null && Object.hasOwnProperty.call(message, 'Day1'))
        writer.uint32(/* id 4, wireType 0 =*/ 32).bool(message.Day1)
      if (message.Day2 != null && Object.hasOwnProperty.call(message, 'Day2'))
        writer.uint32(/* id 5, wireType 0 =*/ 40).bool(message.Day2)
      if (message.Day3 != null && Object.hasOwnProperty.call(message, 'Day3'))
        writer.uint32(/* id 6, wireType 0 =*/ 48).bool(message.Day3)
      if (message.Day4 != null && Object.hasOwnProperty.call(message, 'Day4'))
        writer.uint32(/* id 7, wireType 0 =*/ 56).bool(message.Day4)
      if (message.Day5 != null && Object.hasOwnProperty.call(message, 'Day5'))
        writer.uint32(/* id 8, wireType 0 =*/ 64).bool(message.Day5)
      if (message.Day6 != null && Object.hasOwnProperty.call(message, 'Day6'))
        writer.uint32(/* id 9, wireType 0 =*/ 72).bool(message.Day6)
      if (message.Day7 != null && Object.hasOwnProperty.call(message, 'Day7'))
        writer.uint32(/* id 10, wireType 0 =*/ 80).bool(message.Day7)
      if (
        message.CheckStartTime != null &&
        Object.hasOwnProperty.call(message, 'CheckStartTime')
      )
        writer
          .uint32(/* id 11, wireType 2 =*/ 90)
          .string(message.CheckStartTime)
      if (
        message.CheckEndTime != null &&
        Object.hasOwnProperty.call(message, 'CheckEndTime')
      )
        writer.uint32(/* id 12, wireType 2 =*/ 98).string(message.CheckEndTime)
      if (
        message.CheckCount != null &&
        Object.hasOwnProperty.call(message, 'CheckCount')
      )
        writer.uint32(/* id 13, wireType 0 =*/ 104).int32(message.CheckCount)
      if (
        message.EffectiveType != null &&
        Object.hasOwnProperty.call(message, 'EffectiveType')
      )
        writer.uint32(/* id 14, wireType 0 =*/ 112).int32(message.EffectiveType)
      if (
        message.EffectiveStart != null &&
        Object.hasOwnProperty.call(message, 'EffectiveStart')
      )
        writer
          .uint32(/* id 15, wireType 2 =*/ 122)
          .string(message.EffectiveStart)
      if (
        message.EffectiveEnd != null &&
        Object.hasOwnProperty.call(message, 'EffectiveEnd')
      )
        writer.uint32(/* id 16, wireType 2 =*/ 130).string(message.EffectiveEnd)
      if (message.Note != null && Object.hasOwnProperty.call(message, 'Note'))
        writer.uint32(/* id 17, wireType 2 =*/ 138).string(message.Note)
      if (
        message.UpdatedAt != null &&
        Object.hasOwnProperty.call(message, 'UpdatedAt')
      )
        writer.uint32(/* id 18, wireType 2 =*/ 146).string(message.UpdatedAt)
      if (
        message.UpdatedDC != null &&
        Object.hasOwnProperty.call(message, 'UpdatedDC')
      )
        writer.uint32(/* id 19, wireType 2 =*/ 154).string(message.UpdatedDC)
      return writer
    }

    /**
     * Decodes a DbNFCPatrolLineRules message from the specified reader or buffer.
     * @function decode
     * @memberof bysdb.DbNFCPatrolLineRules
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {bysdb.DbNFCPatrolLineRules} DbNFCPatrolLineRules
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    DbNFCPatrolLineRules.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.bysdb.DbNFCPatrolLineRules()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.RID = reader.string()
            break
          case 2:
            message.OrgRID = reader.string()
            break
          case 3:
            message.Name = reader.string()
            break
          case 4:
            message.Day1 = reader.bool()
            break
          case 5:
            message.Day2 = reader.bool()
            break
          case 6:
            message.Day3 = reader.bool()
            break
          case 7:
            message.Day4 = reader.bool()
            break
          case 8:
            message.Day5 = reader.bool()
            break
          case 9:
            message.Day6 = reader.bool()
            break
          case 10:
            message.Day7 = reader.bool()
            break
          case 11:
            message.CheckStartTime = reader.string()
            break
          case 12:
            message.CheckEndTime = reader.string()
            break
          case 13:
            message.CheckCount = reader.int32()
            break
          case 14:
            message.EffectiveType = reader.int32()
            break
          case 15:
            message.EffectiveStart = reader.string()
            break
          case 16:
            message.EffectiveEnd = reader.string()
            break
          case 17:
            message.Note = reader.string()
            break
          case 18:
            message.UpdatedAt = reader.string()
            break
          case 19:
            message.UpdatedDC = reader.string()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return DbNFCPatrolLineRules
  })()

  bysdb.DbNFCPatrolLineAndRules = (function () {
    /**
     * Properties of a DbNFCPatrolLineAndRules.
     * @memberof bysdb
     * @interface IDbNFCPatrolLineAndRules
     * @property {string|null} [RID] @db uuid primary key
     * 行ID
     * @property {string|null} [LineRID] @db uuid not null REFERENCES DbNFCPatrolLine(RID) ON DELETE CASCADE
     * 归属线路
     * @property {string|null} [RuleRID] @db uuid not null REFERENCES DbNFCPatrolLineRules(RID) ON DELETE CASCADE
     * 线路规则
     * @property {string|null} [UpdatedAt] @db timestamp not null default now_utc()
     * 数据最后修改时间
     * @property {string|null} [OrgRID] @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 归属组，与路线的上线一致
     */

    /**
     * Constructs a new DbNFCPatrolLineAndRules.
     * @memberof bysdb
     * @classdesc 巡查线路和规则的关系表，分开主要是方便使用数据库外键约束
     * @rpc pcrud
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbNFCPatrolLineAndRulesOrgRID on DbNFCPatrolLineAndRules USING hash(OrgRID);
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbNFCPatrolLineAndRulesLineRID on DbNFCPatrolLineAndRules USING hash(LineRID);
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbNFCPatrolLineAndRulesRuleRID on DbNFCPatrolLineAndRules USING hash(RuleRID);
     * @implements IDbNFCPatrolLineAndRules
     * @constructor
     * @param {bysdb.IDbNFCPatrolLineAndRules=} [properties] Properties to set
     */
    function DbNFCPatrolLineAndRules(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * @db uuid primary key
     * 行ID
     * @member {string} RID
     * @memberof bysdb.DbNFCPatrolLineAndRules
     * @instance
     */
    DbNFCPatrolLineAndRules.prototype.RID = ''

    /**
     * @db uuid not null REFERENCES DbNFCPatrolLine(RID) ON DELETE CASCADE
     * 归属线路
     * @member {string} LineRID
     * @memberof bysdb.DbNFCPatrolLineAndRules
     * @instance
     */
    DbNFCPatrolLineAndRules.prototype.LineRID = ''

    /**
     * @db uuid not null REFERENCES DbNFCPatrolLineRules(RID) ON DELETE CASCADE
     * 线路规则
     * @member {string} RuleRID
     * @memberof bysdb.DbNFCPatrolLineAndRules
     * @instance
     */
    DbNFCPatrolLineAndRules.prototype.RuleRID = ''

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     * @member {string} UpdatedAt
     * @memberof bysdb.DbNFCPatrolLineAndRules
     * @instance
     */
    DbNFCPatrolLineAndRules.prototype.UpdatedAt = ''

    /**
     * @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 归属组，与路线的上线一致
     * @member {string} OrgRID
     * @memberof bysdb.DbNFCPatrolLineAndRules
     * @instance
     */
    DbNFCPatrolLineAndRules.prototype.OrgRID = ''

    /**
     * Encodes the specified DbNFCPatrolLineAndRules message. Does not implicitly {@link bysdb.DbNFCPatrolLineAndRules.verify|verify} messages.
     * @function encode
     * @memberof bysdb.DbNFCPatrolLineAndRules
     * @static
     * @param {bysdb.IDbNFCPatrolLineAndRules} message DbNFCPatrolLineAndRules message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    DbNFCPatrolLineAndRules.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (message.RID != null && Object.hasOwnProperty.call(message, 'RID'))
        writer.uint32(/* id 1, wireType 2 =*/ 10).string(message.RID)
      if (
        message.LineRID != null &&
        Object.hasOwnProperty.call(message, 'LineRID')
      )
        writer.uint32(/* id 2, wireType 2 =*/ 18).string(message.LineRID)
      if (
        message.RuleRID != null &&
        Object.hasOwnProperty.call(message, 'RuleRID')
      )
        writer.uint32(/* id 3, wireType 2 =*/ 26).string(message.RuleRID)
      if (
        message.UpdatedAt != null &&
        Object.hasOwnProperty.call(message, 'UpdatedAt')
      )
        writer.uint32(/* id 4, wireType 2 =*/ 34).string(message.UpdatedAt)
      if (
        message.OrgRID != null &&
        Object.hasOwnProperty.call(message, 'OrgRID')
      )
        writer.uint32(/* id 5, wireType 2 =*/ 42).string(message.OrgRID)
      return writer
    }

    /**
     * Decodes a DbNFCPatrolLineAndRules message from the specified reader or buffer.
     * @function decode
     * @memberof bysdb.DbNFCPatrolLineAndRules
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {bysdb.DbNFCPatrolLineAndRules} DbNFCPatrolLineAndRules
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    DbNFCPatrolLineAndRules.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.bysdb.DbNFCPatrolLineAndRules()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.RID = reader.string()
            break
          case 2:
            message.LineRID = reader.string()
            break
          case 3:
            message.RuleRID = reader.string()
            break
          case 4:
            message.UpdatedAt = reader.string()
            break
          case 5:
            message.OrgRID = reader.string()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return DbNFCPatrolLineAndRules
  })()

  bysdb.CamImageData = (function () {
    /**
     * Properties of a CamImageData.
     * @memberof bysdb
     * @interface ICamImageData
     * @property {string|null} [devId] 设备IMEI
     * @property {string|null} [ccid] 4G模块自动添加
     * @property {string|null} [firmwareVersion] 固件版本
     * @property {Long|null} [timestamp] 抓拍时间戳，Unix秒时间戳
     * @property {number|null} [battery] 电池电压单位mv
     * @property {string|null} [signal] 4G信号强度
     * @property {number|null} [tempEnv] 环境温度
     * @property {number|null} [tempCpu] CPU温度
     * @property {number|null} [type] 工作类型
     * 0 – 手动
     * 1 – 唤醒拍照
     * 2 – 定时拍照
     * 3 – 唤醒录像
     * 4 – 定时录像
     * @property {number|null} [zoomRate] 以下为可选字段
     * 放大系数
     * @property {number|null} [icharge] 充电电流，合方圆太阳能充电模块支持
     * @property {number|null} [iload] 负载电流，合方圆太阳能充电模块支持
     * @property {number|null} [vcharge] 充电电压，合方圆太阳能充电模块支持
     * @property {string|null} [custData] 上传图片时，界桩自定义的json数据
     * "{\"hwid\": 14, \"type\": 2}"
     * hwid: 界桩硬件ID
     * type: 1、红外感应抓拍，2、报警抓拍，3、定时抓拍，4、手动抓拍
     */

    /**
     * Constructs a new CamImageData.
     * @memberof bysdb
     * @classdesc 合方圆摄像机上传图片时，附带的json结构体
     * @implements ICamImageData
     * @constructor
     * @param {bysdb.ICamImageData=} [properties] Properties to set
     */
    function CamImageData(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 设备IMEI
     * @member {string} devId
     * @memberof bysdb.CamImageData
     * @instance
     */
    CamImageData.prototype.devId = ''

    /**
     * 4G模块自动添加
     * @member {string} ccid
     * @memberof bysdb.CamImageData
     * @instance
     */
    CamImageData.prototype.ccid = ''

    /**
     * 固件版本
     * @member {string} firmwareVersion
     * @memberof bysdb.CamImageData
     * @instance
     */
    CamImageData.prototype.firmwareVersion = ''

    /**
     * 抓拍时间戳，Unix秒时间戳
     * @member {Long} timestamp
     * @memberof bysdb.CamImageData
     * @instance
     */
    CamImageData.prototype.timestamp = $util.Long
      ? $util.Long.fromBits(0, 0, false)
      : 0

    /**
     * 电池电压单位mv
     * @member {number} battery
     * @memberof bysdb.CamImageData
     * @instance
     */
    CamImageData.prototype.battery = 0

    /**
     * 4G信号强度
     * @member {string} signal
     * @memberof bysdb.CamImageData
     * @instance
     */
    CamImageData.prototype.signal = ''

    /**
     * 环境温度
     * @member {number} tempEnv
     * @memberof bysdb.CamImageData
     * @instance
     */
    CamImageData.prototype.tempEnv = 0

    /**
     * CPU温度
     * @member {number} tempCpu
     * @memberof bysdb.CamImageData
     * @instance
     */
    CamImageData.prototype.tempCpu = 0

    /**
     * 工作类型
     * 0 – 手动
     * 1 – 唤醒拍照
     * 2 – 定时拍照
     * 3 – 唤醒录像
     * 4 – 定时录像
     * @member {number} type
     * @memberof bysdb.CamImageData
     * @instance
     */
    CamImageData.prototype.type = 0

    /**
     * 以下为可选字段
     * 放大系数
     * @member {number} zoomRate
     * @memberof bysdb.CamImageData
     * @instance
     */
    CamImageData.prototype.zoomRate = 0

    /**
     * 充电电流，合方圆太阳能充电模块支持
     * @member {number} icharge
     * @memberof bysdb.CamImageData
     * @instance
     */
    CamImageData.prototype.icharge = 0

    /**
     * 负载电流，合方圆太阳能充电模块支持
     * @member {number} iload
     * @memberof bysdb.CamImageData
     * @instance
     */
    CamImageData.prototype.iload = 0

    /**
     * 充电电压，合方圆太阳能充电模块支持
     * @member {number} vcharge
     * @memberof bysdb.CamImageData
     * @instance
     */
    CamImageData.prototype.vcharge = 0

    /**
     * 上传图片时，界桩自定义的json数据
     * "{\"hwid\": 14, \"type\": 2}"
     * hwid: 界桩硬件ID
     * type: 1、红外感应抓拍，2、报警抓拍，3、定时抓拍，4、手动抓拍
     * @member {string} custData
     * @memberof bysdb.CamImageData
     * @instance
     */
    CamImageData.prototype.custData = ''

    /**
     * Encodes the specified CamImageData message. Does not implicitly {@link bysdb.CamImageData.verify|verify} messages.
     * @function encode
     * @memberof bysdb.CamImageData
     * @static
     * @param {bysdb.ICamImageData} message CamImageData message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    CamImageData.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (message.devId != null && Object.hasOwnProperty.call(message, 'devId'))
        writer.uint32(/* id 1, wireType 2 =*/ 10).string(message.devId)
      if (message.ccid != null && Object.hasOwnProperty.call(message, 'ccid'))
        writer.uint32(/* id 2, wireType 2 =*/ 18).string(message.ccid)
      if (
        message.firmwareVersion != null &&
        Object.hasOwnProperty.call(message, 'firmwareVersion')
      )
        writer
          .uint32(/* id 3, wireType 2 =*/ 26)
          .string(message.firmwareVersion)
      if (
        message.timestamp != null &&
        Object.hasOwnProperty.call(message, 'timestamp')
      )
        writer.uint32(/* id 4, wireType 1 =*/ 33).sfixed64(message.timestamp)
      if (
        message.battery != null &&
        Object.hasOwnProperty.call(message, 'battery')
      )
        writer.uint32(/* id 5, wireType 5 =*/ 45).float(message.battery)
      if (
        message.signal != null &&
        Object.hasOwnProperty.call(message, 'signal')
      )
        writer.uint32(/* id 6, wireType 2 =*/ 50).string(message.signal)
      if (
        message.tempEnv != null &&
        Object.hasOwnProperty.call(message, 'tempEnv')
      )
        writer.uint32(/* id 7, wireType 0 =*/ 56).sint32(message.tempEnv)
      if (
        message.tempCpu != null &&
        Object.hasOwnProperty.call(message, 'tempCpu')
      )
        writer.uint32(/* id 8, wireType 0 =*/ 64).sint32(message.tempCpu)
      if (message.type != null && Object.hasOwnProperty.call(message, 'type'))
        writer.uint32(/* id 9, wireType 0 =*/ 72).sint32(message.type)
      if (
        message.zoomRate != null &&
        Object.hasOwnProperty.call(message, 'zoomRate')
      )
        writer.uint32(/* id 10, wireType 5 =*/ 85).float(message.zoomRate)
      if (
        message.icharge != null &&
        Object.hasOwnProperty.call(message, 'icharge')
      )
        writer.uint32(/* id 11, wireType 5 =*/ 93).float(message.icharge)
      if (message.iload != null && Object.hasOwnProperty.call(message, 'iload'))
        writer.uint32(/* id 12, wireType 5 =*/ 101).float(message.iload)
      if (
        message.vcharge != null &&
        Object.hasOwnProperty.call(message, 'vcharge')
      )
        writer.uint32(/* id 13, wireType 5 =*/ 109).float(message.vcharge)
      if (
        message.custData != null &&
        Object.hasOwnProperty.call(message, 'custData')
      )
        writer.uint32(/* id 14, wireType 2 =*/ 114).string(message.custData)
      return writer
    }

    /**
     * Decodes a CamImageData message from the specified reader or buffer.
     * @function decode
     * @memberof bysdb.CamImageData
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {bysdb.CamImageData} CamImageData
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    CamImageData.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.bysdb.CamImageData()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.devId = reader.string()
            break
          case 2:
            message.ccid = reader.string()
            break
          case 3:
            message.firmwareVersion = reader.string()
            break
          case 4:
            message.timestamp = reader.sfixed64()
            break
          case 5:
            message.battery = reader.float()
            break
          case 6:
            message.signal = reader.string()
            break
          case 7:
            message.tempEnv = reader.sint32()
            break
          case 8:
            message.tempCpu = reader.sint32()
            break
          case 9:
            message.type = reader.sint32()
            break
          case 10:
            message.zoomRate = reader.float()
            break
          case 11:
            message.icharge = reader.float()
            break
          case 12:
            message.iload = reader.float()
            break
          case 13:
            message.vcharge = reader.float()
            break
          case 14:
            message.custData = reader.string()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return CamImageData
  })()

  bysdb.DbMarkerUploadImageHistory = (function () {
    /**
     * Properties of a DbMarkerUploadImageHistory.
     * @memberof bysdb
     * @interface IDbMarkerUploadImageHistory
     * @property {string|null} [OrgRID] @db uuid not null
     * 所属的群组
     * @property {number|null} [MarkerHWID] @db int
     * 界桩硬件ID
     * @property {string|null} [CaptureTime] @db timestamp not null
     * 抓拍时间,utc
     * @property {number|null} [CaptureType] @db int
     * 抓拍的动作类型，与CamImageData.type一致
     * 0 – 手动
     * 1 – 唤醒拍照
     * 2 – 定时拍照
     * 3 – 唤醒录像
     * 4 – 定时录像
     * @property {string|null} [UploadTime] @db timestamp not null
     * 服务器接收上传时间,utc
     * @property {string|null} [FileName] @db text
     * 上传的图片保存路径，f512673f-0f6a-4b54-95bf-d377b78fba1a.jpg
     * @property {string|null} [FormData] @db jsonb not null default  '{}'::jsonb
     * 界桩上传图片时附带的json数据，CamImageData json字符串
     */

    /**
     * Constructs a new DbMarkerUploadImageHistory.
     * @memberof bysdb
     * @classdesc 4g界桩上传的图片历史表，以月为单位分表
     * @rpc pcrud
     * @dbend PARTITION BY RANGE (UploadTime)
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbMarkerUploadImageHistoryHWID on DbMarkerUploadImageHistory USING hash(MarkerHWID);
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbMarkerUploadImageHistoryOrgRID on DbMarkerUploadImageHistory USING hash(OrgRID);
     * @implements IDbMarkerUploadImageHistory
     * @constructor
     * @param {bysdb.IDbMarkerUploadImageHistory=} [properties] Properties to set
     */
    function DbMarkerUploadImageHistory(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * @db uuid not null
     * 所属的群组
     * @member {string} OrgRID
     * @memberof bysdb.DbMarkerUploadImageHistory
     * @instance
     */
    DbMarkerUploadImageHistory.prototype.OrgRID = ''

    /**
     * @db int
     * 界桩硬件ID
     * @member {number} MarkerHWID
     * @memberof bysdb.DbMarkerUploadImageHistory
     * @instance
     */
    DbMarkerUploadImageHistory.prototype.MarkerHWID = 0

    /**
     * @db timestamp not null
     * 抓拍时间,utc
     * @member {string} CaptureTime
     * @memberof bysdb.DbMarkerUploadImageHistory
     * @instance
     */
    DbMarkerUploadImageHistory.prototype.CaptureTime = ''

    /**
     * @db int
     * 抓拍的动作类型，与CamImageData.type一致
     * 0 – 手动
     * 1 – 唤醒拍照
     * 2 – 定时拍照
     * 3 – 唤醒录像
     * 4 – 定时录像
     * @member {number} CaptureType
     * @memberof bysdb.DbMarkerUploadImageHistory
     * @instance
     */
    DbMarkerUploadImageHistory.prototype.CaptureType = 0

    /**
     * @db timestamp not null
     * 服务器接收上传时间,utc
     * @member {string} UploadTime
     * @memberof bysdb.DbMarkerUploadImageHistory
     * @instance
     */
    DbMarkerUploadImageHistory.prototype.UploadTime = ''

    /**
     * @db text
     * 上传的图片保存路径，f512673f-0f6a-4b54-95bf-d377b78fba1a.jpg
     * @member {string} FileName
     * @memberof bysdb.DbMarkerUploadImageHistory
     * @instance
     */
    DbMarkerUploadImageHistory.prototype.FileName = ''

    /**
     * @db jsonb not null default  '{}'::jsonb
     * 界桩上传图片时附带的json数据，CamImageData json字符串
     * @member {string} FormData
     * @memberof bysdb.DbMarkerUploadImageHistory
     * @instance
     */
    DbMarkerUploadImageHistory.prototype.FormData = ''

    /**
     * Encodes the specified DbMarkerUploadImageHistory message. Does not implicitly {@link bysdb.DbMarkerUploadImageHistory.verify|verify} messages.
     * @function encode
     * @memberof bysdb.DbMarkerUploadImageHistory
     * @static
     * @param {bysdb.IDbMarkerUploadImageHistory} message DbMarkerUploadImageHistory message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    DbMarkerUploadImageHistory.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (
        message.OrgRID != null &&
        Object.hasOwnProperty.call(message, 'OrgRID')
      )
        writer.uint32(/* id 1, wireType 2 =*/ 10).string(message.OrgRID)
      if (
        message.MarkerHWID != null &&
        Object.hasOwnProperty.call(message, 'MarkerHWID')
      )
        writer.uint32(/* id 2, wireType 0 =*/ 16).sint32(message.MarkerHWID)
      if (
        message.CaptureTime != null &&
        Object.hasOwnProperty.call(message, 'CaptureTime')
      )
        writer.uint32(/* id 3, wireType 2 =*/ 26).string(message.CaptureTime)
      if (
        message.CaptureType != null &&
        Object.hasOwnProperty.call(message, 'CaptureType')
      )
        writer.uint32(/* id 4, wireType 0 =*/ 32).sint32(message.CaptureType)
      if (
        message.UploadTime != null &&
        Object.hasOwnProperty.call(message, 'UploadTime')
      )
        writer.uint32(/* id 5, wireType 2 =*/ 42).string(message.UploadTime)
      if (
        message.FileName != null &&
        Object.hasOwnProperty.call(message, 'FileName')
      )
        writer.uint32(/* id 6, wireType 2 =*/ 50).string(message.FileName)
      if (
        message.FormData != null &&
        Object.hasOwnProperty.call(message, 'FormData')
      )
        writer.uint32(/* id 7, wireType 2 =*/ 58).string(message.FormData)
      return writer
    }

    /**
     * Decodes a DbMarkerUploadImageHistory message from the specified reader or buffer.
     * @function decode
     * @memberof bysdb.DbMarkerUploadImageHistory
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {bysdb.DbMarkerUploadImageHistory} DbMarkerUploadImageHistory
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    DbMarkerUploadImageHistory.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.bysdb.DbMarkerUploadImageHistory()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.OrgRID = reader.string()
            break
          case 2:
            message.MarkerHWID = reader.sint32()
            break
          case 3:
            message.CaptureTime = reader.string()
            break
          case 4:
            message.CaptureType = reader.sint32()
            break
          case 5:
            message.UploadTime = reader.string()
            break
          case 6:
            message.FileName = reader.string()
            break
          case 7:
            message.FormData = reader.string()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return DbMarkerUploadImageHistory
  })()

  return bysdb
})())

export const user = ($root.user = (() => {
  /**
   * Namespace user.
   * @exports user
   * @namespace
   */
  const user = $root.user || {}

  user.DbPermission = (function () {
    /**
     * Properties of a DbPermission.
     * @memberof user
     * @interface IDbPermission
     * @property {string|null} [RID] @db uuid primary key
     * 行ID,permission rid
     * @property {string|null} [PermissionType] @db text not null
     * 权限类别
     * @property {string|null} [PermissionName] @db text not null
     * permission 名称
     * @property {string|null} [PermissionValue] @db text not null
     * permission value
     * @property {string|null} [UpdatedAt] @db timestamp not null default now_utc()
     * 数据最后修改时间
     * @property {string|null} [UpdatedDC] @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */

    /**
     * Constructs a new DbPermission.
     * @memberof user
     * @classdesc 所有的权限信息表,此表信息为预置，一般不给删除
     * @rpc crud
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbPermissionPermissionValue on DbPermission USING hash(PermissionValue);
     * @implements IDbPermission
     * @constructor
     * @param {user.IDbPermission=} [properties] Properties to set
     */
    function DbPermission(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * @db uuid primary key
     * 行ID,permission rid
     * @member {string} RID
     * @memberof user.DbPermission
     * @instance
     */
    DbPermission.prototype.RID = ''

    /**
     * @db text not null
     * 权限类别
     * @member {string} PermissionType
     * @memberof user.DbPermission
     * @instance
     */
    DbPermission.prototype.PermissionType = ''

    /**
     * @db text not null
     * permission 名称
     * @member {string} PermissionName
     * @memberof user.DbPermission
     * @instance
     */
    DbPermission.prototype.PermissionName = ''

    /**
     * @db text not null
     * permission value
     * @member {string} PermissionValue
     * @memberof user.DbPermission
     * @instance
     */
    DbPermission.prototype.PermissionValue = ''

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     * @member {string} UpdatedAt
     * @memberof user.DbPermission
     * @instance
     */
    DbPermission.prototype.UpdatedAt = ''

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     * @member {string} UpdatedDC
     * @memberof user.DbPermission
     * @instance
     */
    DbPermission.prototype.UpdatedDC = ''

    /**
     * Encodes the specified DbPermission message. Does not implicitly {@link user.DbPermission.verify|verify} messages.
     * @function encode
     * @memberof user.DbPermission
     * @static
     * @param {user.IDbPermission} message DbPermission message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    DbPermission.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (message.RID != null && Object.hasOwnProperty.call(message, 'RID'))
        writer.uint32(/* id 1, wireType 2 =*/ 10).string(message.RID)
      if (
        message.PermissionType != null &&
        Object.hasOwnProperty.call(message, 'PermissionType')
      )
        writer.uint32(/* id 3, wireType 2 =*/ 26).string(message.PermissionType)
      if (
        message.PermissionName != null &&
        Object.hasOwnProperty.call(message, 'PermissionName')
      )
        writer.uint32(/* id 4, wireType 2 =*/ 34).string(message.PermissionName)
      if (
        message.PermissionValue != null &&
        Object.hasOwnProperty.call(message, 'PermissionValue')
      )
        writer
          .uint32(/* id 10, wireType 2 =*/ 82)
          .string(message.PermissionValue)
      if (
        message.UpdatedAt != null &&
        Object.hasOwnProperty.call(message, 'UpdatedAt')
      )
        writer.uint32(/* id 14, wireType 2 =*/ 114).string(message.UpdatedAt)
      if (
        message.UpdatedDC != null &&
        Object.hasOwnProperty.call(message, 'UpdatedDC')
      )
        writer.uint32(/* id 15, wireType 2 =*/ 122).string(message.UpdatedDC)
      return writer
    }

    /**
     * Decodes a DbPermission message from the specified reader or buffer.
     * @function decode
     * @memberof user.DbPermission
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {user.DbPermission} DbPermission
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    DbPermission.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.user.DbPermission()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.RID = reader.string()
            break
          case 3:
            message.PermissionType = reader.string()
            break
          case 4:
            message.PermissionName = reader.string()
            break
          case 10:
            message.PermissionValue = reader.string()
            break
          case 14:
            message.UpdatedAt = reader.string()
            break
          case 15:
            message.UpdatedDC = reader.string()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return DbPermission
  })()

  user.DbRole = (function () {
    /**
     * Properties of a DbRole.
     * @memberof user
     * @interface IDbRole
     * @property {string|null} [RID] @db uuid primary key
     * 行ID,role rid
     * @property {string|null} [OrgRID] @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 用户所属的群组
     * @property {string|null} [RoleName] @db text not null
     * role 名称
     * @property {string|null} [Creator] @db uuid --REFERENCES DbUser(RID) ON DELETE set null
     * role 创建者 rid
     * @property {number|null} [IsBuiltIn] @db int
     * 是否是内置的角色，内置角色不能删除
     * @property {string|null} [Setting] @db jsonb not null default  '{}'::jsonb
     * 其它设置，可以在里面扩展需要用到的其它信息
     * @property {number|null} [SortValue] @db int default 100
     * sort value
     * @property {string|null} [UpdatedAt] @db timestamp not null default now_utc()
     * 数据最后修改时间
     * @property {string|null} [UpdatedDC] @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */

    /**
     * Constructs a new DbRole.
     * @memberof user
     * @classdesc 所有的角色信息表
     * @rpc crud pcrud
     * @dbpost insert into DbRole (rid, orgrid, rolename, creator, isbuiltin, setting, sortvalue, updatedat, updateddc) VALUES('*************-5555-5555-************', '00000000-0000-0000-0000-000000000000', 'base', null, 1, '{}'::jsonb, 0, now_utc(), '') ON CONFLICT  DO NOTHING;
     * @dbpost insert into DbRole (rid, orgrid, rolename, creator, isbuiltin, setting, sortvalue, updatedat, updateddc) VALUES('00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', 'admin', null, 1, '{}'::jsonb, 0, now_utc(), '') ON CONFLICT  DO NOTHING;
     * @implements IDbRole
     * @constructor
     * @param {user.IDbRole=} [properties] Properties to set
     */
    function DbRole(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * @db uuid primary key
     * 行ID,role rid
     * @member {string} RID
     * @memberof user.DbRole
     * @instance
     */
    DbRole.prototype.RID = ''

    /**
     * @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 用户所属的群组
     * @member {string} OrgRID
     * @memberof user.DbRole
     * @instance
     */
    DbRole.prototype.OrgRID = ''

    /**
     * @db text not null
     * role 名称
     * @member {string} RoleName
     * @memberof user.DbRole
     * @instance
     */
    DbRole.prototype.RoleName = ''

    /**
     * @db uuid --REFERENCES DbUser(RID) ON DELETE set null
     * role 创建者 rid
     * @member {string} Creator
     * @memberof user.DbRole
     * @instance
     */
    DbRole.prototype.Creator = ''

    /**
     * @db int
     * 是否是内置的角色，内置角色不能删除
     * @member {number} IsBuiltIn
     * @memberof user.DbRole
     * @instance
     */
    DbRole.prototype.IsBuiltIn = 0

    /**
     * @db jsonb not null default  '{}'::jsonb
     * 其它设置，可以在里面扩展需要用到的其它信息
     * @member {string} Setting
     * @memberof user.DbRole
     * @instance
     */
    DbRole.prototype.Setting = ''

    /**
     * @db int default 100
     * sort value
     * @member {number} SortValue
     * @memberof user.DbRole
     * @instance
     */
    DbRole.prototype.SortValue = 0

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     * @member {string} UpdatedAt
     * @memberof user.DbRole
     * @instance
     */
    DbRole.prototype.UpdatedAt = ''

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     * @member {string} UpdatedDC
     * @memberof user.DbRole
     * @instance
     */
    DbRole.prototype.UpdatedDC = ''

    /**
     * Encodes the specified DbRole message. Does not implicitly {@link user.DbRole.verify|verify} messages.
     * @function encode
     * @memberof user.DbRole
     * @static
     * @param {user.IDbRole} message DbRole message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    DbRole.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (message.RID != null && Object.hasOwnProperty.call(message, 'RID'))
        writer.uint32(/* id 1, wireType 2 =*/ 10).string(message.RID)
      if (
        message.OrgRID != null &&
        Object.hasOwnProperty.call(message, 'OrgRID')
      )
        writer.uint32(/* id 2, wireType 2 =*/ 18).string(message.OrgRID)
      if (
        message.RoleName != null &&
        Object.hasOwnProperty.call(message, 'RoleName')
      )
        writer.uint32(/* id 4, wireType 2 =*/ 34).string(message.RoleName)
      if (
        message.Creator != null &&
        Object.hasOwnProperty.call(message, 'Creator')
      )
        writer.uint32(/* id 5, wireType 2 =*/ 42).string(message.Creator)
      if (
        message.IsBuiltIn != null &&
        Object.hasOwnProperty.call(message, 'IsBuiltIn')
      )
        writer.uint32(/* id 6, wireType 0 =*/ 48).int32(message.IsBuiltIn)
      if (
        message.Setting != null &&
        Object.hasOwnProperty.call(message, 'Setting')
      )
        writer.uint32(/* id 9, wireType 2 =*/ 74).string(message.Setting)
      if (
        message.SortValue != null &&
        Object.hasOwnProperty.call(message, 'SortValue')
      )
        writer.uint32(/* id 10, wireType 0 =*/ 80).int32(message.SortValue)
      if (
        message.UpdatedAt != null &&
        Object.hasOwnProperty.call(message, 'UpdatedAt')
      )
        writer.uint32(/* id 14, wireType 2 =*/ 114).string(message.UpdatedAt)
      if (
        message.UpdatedDC != null &&
        Object.hasOwnProperty.call(message, 'UpdatedDC')
      )
        writer.uint32(/* id 15, wireType 2 =*/ 122).string(message.UpdatedDC)
      return writer
    }

    /**
     * Decodes a DbRole message from the specified reader or buffer.
     * @function decode
     * @memberof user.DbRole
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {user.DbRole} DbRole
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    DbRole.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.user.DbRole()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.RID = reader.string()
            break
          case 2:
            message.OrgRID = reader.string()
            break
          case 4:
            message.RoleName = reader.string()
            break
          case 5:
            message.Creator = reader.string()
            break
          case 6:
            message.IsBuiltIn = reader.int32()
            break
          case 9:
            message.Setting = reader.string()
            break
          case 10:
            message.SortValue = reader.int32()
            break
          case 14:
            message.UpdatedAt = reader.string()
            break
          case 15:
            message.UpdatedDC = reader.string()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return DbRole
  })()

  user.DbRolePermission = (function () {
    /**
     * Properties of a DbRolePermission.
     * @memberof user
     * @interface IDbRolePermission
     * @property {string|null} [RID] @db uuid primary key
     * 行ID
     * @property {string|null} [RoleRID] @db uuid not null REFERENCES DbRole(RID) ON DELETE CASCADE
     * role rid
     * @property {string|null} [PermissionRID] @db uuid not null REFERENCES DbPermission(RID) ON DELETE CASCADE
     * permission rid
     * @property {string|null} [Creator] @db uuid --REFERENCES DbUser(RID) ON DELETE set null
     * role 创建者 rid
     * @property {string|null} [UpdatedAt] @db timestamp not null default now_utc()
     * 数据最后修改时间
     * @property {string|null} [UpdatedDC] @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */

    /**
     * Constructs a new DbRolePermission.
     * @memberof user
     * @classdesc 角色权限信息表
     * @rpc crud pcrud
     * @implements IDbRolePermission
     * @constructor
     * @param {user.IDbRolePermission=} [properties] Properties to set
     */
    function DbRolePermission(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * @db uuid primary key
     * 行ID
     * @member {string} RID
     * @memberof user.DbRolePermission
     * @instance
     */
    DbRolePermission.prototype.RID = ''

    /**
     * @db uuid not null REFERENCES DbRole(RID) ON DELETE CASCADE
     * role rid
     * @member {string} RoleRID
     * @memberof user.DbRolePermission
     * @instance
     */
    DbRolePermission.prototype.RoleRID = ''

    /**
     * @db uuid not null REFERENCES DbPermission(RID) ON DELETE CASCADE
     * permission rid
     * @member {string} PermissionRID
     * @memberof user.DbRolePermission
     * @instance
     */
    DbRolePermission.prototype.PermissionRID = ''

    /**
     * @db uuid --REFERENCES DbUser(RID) ON DELETE set null
     * role 创建者 rid
     * @member {string} Creator
     * @memberof user.DbRolePermission
     * @instance
     */
    DbRolePermission.prototype.Creator = ''

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     * @member {string} UpdatedAt
     * @memberof user.DbRolePermission
     * @instance
     */
    DbRolePermission.prototype.UpdatedAt = ''

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     * @member {string} UpdatedDC
     * @memberof user.DbRolePermission
     * @instance
     */
    DbRolePermission.prototype.UpdatedDC = ''

    /**
     * Encodes the specified DbRolePermission message. Does not implicitly {@link user.DbRolePermission.verify|verify} messages.
     * @function encode
     * @memberof user.DbRolePermission
     * @static
     * @param {user.IDbRolePermission} message DbRolePermission message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    DbRolePermission.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (message.RID != null && Object.hasOwnProperty.call(message, 'RID'))
        writer.uint32(/* id 1, wireType 2 =*/ 10).string(message.RID)
      if (
        message.RoleRID != null &&
        Object.hasOwnProperty.call(message, 'RoleRID')
      )
        writer.uint32(/* id 2, wireType 2 =*/ 18).string(message.RoleRID)
      if (
        message.PermissionRID != null &&
        Object.hasOwnProperty.call(message, 'PermissionRID')
      )
        writer.uint32(/* id 4, wireType 2 =*/ 34).string(message.PermissionRID)
      if (
        message.Creator != null &&
        Object.hasOwnProperty.call(message, 'Creator')
      )
        writer.uint32(/* id 5, wireType 2 =*/ 42).string(message.Creator)
      if (
        message.UpdatedAt != null &&
        Object.hasOwnProperty.call(message, 'UpdatedAt')
      )
        writer.uint32(/* id 14, wireType 2 =*/ 114).string(message.UpdatedAt)
      if (
        message.UpdatedDC != null &&
        Object.hasOwnProperty.call(message, 'UpdatedDC')
      )
        writer.uint32(/* id 15, wireType 2 =*/ 122).string(message.UpdatedDC)
      return writer
    }

    /**
     * Decodes a DbRolePermission message from the specified reader or buffer.
     * @function decode
     * @memberof user.DbRolePermission
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {user.DbRolePermission} DbRolePermission
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    DbRolePermission.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.user.DbRolePermission()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.RID = reader.string()
            break
          case 2:
            message.RoleRID = reader.string()
            break
          case 4:
            message.PermissionRID = reader.string()
            break
          case 5:
            message.Creator = reader.string()
            break
          case 14:
            message.UpdatedAt = reader.string()
            break
          case 15:
            message.UpdatedDC = reader.string()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return DbRolePermission
  })()

  user.DbPermissionList = (function () {
    /**
     * Properties of a DbPermissionList.
     * @memberof user
     * @interface IDbPermissionList
     * @property {number|null} [BatchNo] 批次号，从0开始
     * @property {number|null} [RowsOffset] rows offset,从0开始
     * @property {Array.<user.IDbPermission>|null} [Rows] 本批次数据
     */

    /**
     * Constructs a new DbPermissionList.
     * @memberof user
     * @classdesc DbPermission list
     * @implements IDbPermissionList
     * @constructor
     * @param {user.IDbPermissionList=} [properties] Properties to set
     */
    function DbPermissionList(properties) {
      this.Rows = []
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 批次号，从0开始
     * @member {number} BatchNo
     * @memberof user.DbPermissionList
     * @instance
     */
    DbPermissionList.prototype.BatchNo = 0

    /**
     * rows offset,从0开始
     * @member {number} RowsOffset
     * @memberof user.DbPermissionList
     * @instance
     */
    DbPermissionList.prototype.RowsOffset = 0

    /**
     * 本批次数据
     * @member {Array.<user.IDbPermission>} Rows
     * @memberof user.DbPermissionList
     * @instance
     */
    DbPermissionList.prototype.Rows = $util.emptyArray

    /**
     * Encodes the specified DbPermissionList message. Does not implicitly {@link user.DbPermissionList.verify|verify} messages.
     * @function encode
     * @memberof user.DbPermissionList
     * @static
     * @param {user.IDbPermissionList} message DbPermissionList message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    DbPermissionList.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (
        message.BatchNo != null &&
        Object.hasOwnProperty.call(message, 'BatchNo')
      )
        writer.uint32(/* id 1, wireType 0 =*/ 8).uint32(message.BatchNo)
      if (
        message.RowsOffset != null &&
        Object.hasOwnProperty.call(message, 'RowsOffset')
      )
        writer.uint32(/* id 2, wireType 0 =*/ 16).uint32(message.RowsOffset)
      if (message.Rows != null && message.Rows.length)
        for (let i = 0; i < message.Rows.length; ++i)
          $root.user.DbPermission.encode(
            message.Rows[i],
            writer.uint32(/* id 3, wireType 2 =*/ 26).fork(),
          ).ldelim()
      return writer
    }

    /**
     * Decodes a DbPermissionList message from the specified reader or buffer.
     * @function decode
     * @memberof user.DbPermissionList
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {user.DbPermissionList} DbPermissionList
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    DbPermissionList.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.user.DbPermissionList()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.BatchNo = reader.uint32()
            break
          case 2:
            message.RowsOffset = reader.uint32()
            break
          case 3:
            if (!(message.Rows && message.Rows.length)) message.Rows = []
            message.Rows.push(
              $root.user.DbPermission.decode(reader, reader.uint32()),
            )
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return DbPermissionList
  })()

  user.DbRoleList = (function () {
    /**
     * Properties of a DbRoleList.
     * @memberof user
     * @interface IDbRoleList
     * @property {number|null} [BatchNo] 批次号，从0开始
     * @property {number|null} [RowsOffset] rows offset,从0开始
     * @property {Array.<user.IDbRole>|null} [Rows] 本批次数据
     */

    /**
     * Constructs a new DbRoleList.
     * @memberof user
     * @classdesc DbRole list
     * @implements IDbRoleList
     * @constructor
     * @param {user.IDbRoleList=} [properties] Properties to set
     */
    function DbRoleList(properties) {
      this.Rows = []
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 批次号，从0开始
     * @member {number} BatchNo
     * @memberof user.DbRoleList
     * @instance
     */
    DbRoleList.prototype.BatchNo = 0

    /**
     * rows offset,从0开始
     * @member {number} RowsOffset
     * @memberof user.DbRoleList
     * @instance
     */
    DbRoleList.prototype.RowsOffset = 0

    /**
     * 本批次数据
     * @member {Array.<user.IDbRole>} Rows
     * @memberof user.DbRoleList
     * @instance
     */
    DbRoleList.prototype.Rows = $util.emptyArray

    /**
     * Encodes the specified DbRoleList message. Does not implicitly {@link user.DbRoleList.verify|verify} messages.
     * @function encode
     * @memberof user.DbRoleList
     * @static
     * @param {user.IDbRoleList} message DbRoleList message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    DbRoleList.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (
        message.BatchNo != null &&
        Object.hasOwnProperty.call(message, 'BatchNo')
      )
        writer.uint32(/* id 1, wireType 0 =*/ 8).uint32(message.BatchNo)
      if (
        message.RowsOffset != null &&
        Object.hasOwnProperty.call(message, 'RowsOffset')
      )
        writer.uint32(/* id 2, wireType 0 =*/ 16).uint32(message.RowsOffset)
      if (message.Rows != null && message.Rows.length)
        for (let i = 0; i < message.Rows.length; ++i)
          $root.user.DbRole.encode(
            message.Rows[i],
            writer.uint32(/* id 3, wireType 2 =*/ 26).fork(),
          ).ldelim()
      return writer
    }

    /**
     * Decodes a DbRoleList message from the specified reader or buffer.
     * @function decode
     * @memberof user.DbRoleList
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {user.DbRoleList} DbRoleList
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    DbRoleList.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.user.DbRoleList()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.BatchNo = reader.uint32()
            break
          case 2:
            message.RowsOffset = reader.uint32()
            break
          case 3:
            if (!(message.Rows && message.Rows.length)) message.Rows = []
            message.Rows.push($root.user.DbRole.decode(reader, reader.uint32()))
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return DbRoleList
  })()

  user.DbRolePermissionList = (function () {
    /**
     * Properties of a DbRolePermissionList.
     * @memberof user
     * @interface IDbRolePermissionList
     * @property {number|null} [BatchNo] 批次号，从0开始
     * @property {number|null} [RowsOffset] rows offset,从0开始
     * @property {Array.<user.IDbRolePermission>|null} [Rows] 本批次数据
     */

    /**
     * Constructs a new DbRolePermissionList.
     * @memberof user
     * @classdesc DbRolePermission list
     * @implements IDbRolePermissionList
     * @constructor
     * @param {user.IDbRolePermissionList=} [properties] Properties to set
     */
    function DbRolePermissionList(properties) {
      this.Rows = []
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 批次号，从0开始
     * @member {number} BatchNo
     * @memberof user.DbRolePermissionList
     * @instance
     */
    DbRolePermissionList.prototype.BatchNo = 0

    /**
     * rows offset,从0开始
     * @member {number} RowsOffset
     * @memberof user.DbRolePermissionList
     * @instance
     */
    DbRolePermissionList.prototype.RowsOffset = 0

    /**
     * 本批次数据
     * @member {Array.<user.IDbRolePermission>} Rows
     * @memberof user.DbRolePermissionList
     * @instance
     */
    DbRolePermissionList.prototype.Rows = $util.emptyArray

    /**
     * Encodes the specified DbRolePermissionList message. Does not implicitly {@link user.DbRolePermissionList.verify|verify} messages.
     * @function encode
     * @memberof user.DbRolePermissionList
     * @static
     * @param {user.IDbRolePermissionList} message DbRolePermissionList message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    DbRolePermissionList.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (
        message.BatchNo != null &&
        Object.hasOwnProperty.call(message, 'BatchNo')
      )
        writer.uint32(/* id 1, wireType 0 =*/ 8).uint32(message.BatchNo)
      if (
        message.RowsOffset != null &&
        Object.hasOwnProperty.call(message, 'RowsOffset')
      )
        writer.uint32(/* id 2, wireType 0 =*/ 16).uint32(message.RowsOffset)
      if (message.Rows != null && message.Rows.length)
        for (let i = 0; i < message.Rows.length; ++i)
          $root.user.DbRolePermission.encode(
            message.Rows[i],
            writer.uint32(/* id 3, wireType 2 =*/ 26).fork(),
          ).ldelim()
      return writer
    }

    /**
     * Decodes a DbRolePermissionList message from the specified reader or buffer.
     * @function decode
     * @memberof user.DbRolePermissionList
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {user.DbRolePermissionList} DbRolePermissionList
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    DbRolePermissionList.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.user.DbRolePermissionList()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.BatchNo = reader.uint32()
            break
          case 2:
            message.RowsOffset = reader.uint32()
            break
          case 3:
            if (!(message.Rows && message.Rows.length)) message.Rows = []
            message.Rows.push(
              $root.user.DbRolePermission.decode(reader, reader.uint32()),
            )
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return DbRolePermissionList
  })()

  return user
})())

export const bysproto = ($root.bysproto = (() => {
  /**
   * Namespace bysproto.
   * @exports bysproto
   * @namespace
   */
  const bysproto = $root.bysproto || {}

  bysproto.Bmsg = (function () {
    /**
     * Properties of a Bmsg.
     * @memberof bysproto
     * @interface IBmsg
     * @property {number|null} [Cmd] 命令字
     * 1:登录
     * 2:界桩打卡上传
     * 12:回应界桩上传
     * 13：修改控制器手台监听信道号
     * 15：更新控制器新的注册地址
     * 11:ping
     * ----4G界桩新增----
     * 21：信息上报
     * 31：上报应答
     * 22：报警
     * 23：拓展报警(外接传感器)
     * 32：报警应答
     * 33：报警解除
     * 28: 报警解除应答
     * 24：参数更新
     * 34：参数更新应答
     * 25：软件更新(预留)
     * 35：软件更新应答(预留)
     * 26：主动抓拍(预留)
     * 36：主动抓拍应答(预留)
     * 27：遥闭
     * 37：遥闭应答
     * @property {number|null} [No] 从0开始增1使用,用于区分相同命令重复的包
     * @property {number|null} [Res] response code
     * @property {Uint8Array|null} [Body] msg body
     * @property {string|null} [Optstr] optional str parameter,额外的信息,一般不会有,有些特殊命令里面可能用到
     * @property {Uint8Array|null} [Optbin] optional binary parameter,额外的信息,一般不会有,有些特殊命令里面可能用到
     */

    /**
     * Constructs a new Bmsg.
     * @memberof bysproto
     * @classdesc 系统与控制器所有的消息交互底层都以此为包装
     * @implements IBmsg
     * @constructor
     * @param {bysproto.IBmsg=} [properties] Properties to set
     */
    function Bmsg(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 命令字
     * 1:登录
     * 2:界桩打卡上传
     * 12:回应界桩上传
     * 13：修改控制器手台监听信道号
     * 15：更新控制器新的注册地址
     * 11:ping
     * ----4G界桩新增----
     * 21：信息上报
     * 31：上报应答
     * 22：报警
     * 23：拓展报警(外接传感器)
     * 32：报警应答
     * 33：报警解除
     * 28: 报警解除应答
     * 24：参数更新
     * 34：参数更新应答
     * 25：软件更新(预留)
     * 35：软件更新应答(预留)
     * 26：主动抓拍(预留)
     * 36：主动抓拍应答(预留)
     * 27：遥闭
     * 37：遥闭应答
     * @member {number} Cmd
     * @memberof bysproto.Bmsg
     * @instance
     */
    Bmsg.prototype.Cmd = 0

    /**
     * 从0开始增1使用,用于区分相同命令重复的包
     * @member {number} No
     * @memberof bysproto.Bmsg
     * @instance
     */
    Bmsg.prototype.No = 0

    /**
     * response code
     * @member {number} Res
     * @memberof bysproto.Bmsg
     * @instance
     */
    Bmsg.prototype.Res = 0

    /**
     * msg body
     * @member {Uint8Array} Body
     * @memberof bysproto.Bmsg
     * @instance
     */
    Bmsg.prototype.Body = $util.newBuffer([])

    /**
     * optional str parameter,额外的信息,一般不会有,有些特殊命令里面可能用到
     * @member {string} Optstr
     * @memberof bysproto.Bmsg
     * @instance
     */
    Bmsg.prototype.Optstr = ''

    /**
     * optional binary parameter,额外的信息,一般不会有,有些特殊命令里面可能用到
     * @member {Uint8Array} Optbin
     * @memberof bysproto.Bmsg
     * @instance
     */
    Bmsg.prototype.Optbin = $util.newBuffer([])

    /**
     * Encodes the specified Bmsg message. Does not implicitly {@link bysproto.Bmsg.verify|verify} messages.
     * @function encode
     * @memberof bysproto.Bmsg
     * @static
     * @param {bysproto.IBmsg} message Bmsg message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    Bmsg.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (message.Cmd != null && Object.hasOwnProperty.call(message, 'Cmd'))
        writer.uint32(/* id 2, wireType 5 =*/ 21).fixed32(message.Cmd)
      if (message.No != null && Object.hasOwnProperty.call(message, 'No'))
        writer.uint32(/* id 5, wireType 0 =*/ 40).uint32(message.No)
      if (message.Res != null && Object.hasOwnProperty.call(message, 'Res'))
        writer.uint32(/* id 9, wireType 0 =*/ 72).sint32(message.Res)
      if (message.Body != null && Object.hasOwnProperty.call(message, 'Body'))
        writer.uint32(/* id 10, wireType 2 =*/ 82).bytes(message.Body)
      if (
        message.Optstr != null &&
        Object.hasOwnProperty.call(message, 'Optstr')
      )
        writer.uint32(/* id 11, wireType 2 =*/ 90).string(message.Optstr)
      if (
        message.Optbin != null &&
        Object.hasOwnProperty.call(message, 'Optbin')
      )
        writer.uint32(/* id 12, wireType 2 =*/ 98).bytes(message.Optbin)
      return writer
    }

    /**
     * Decodes a Bmsg message from the specified reader or buffer.
     * @function decode
     * @memberof bysproto.Bmsg
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {bysproto.Bmsg} Bmsg
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    Bmsg.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.bysproto.Bmsg()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 2:
            message.Cmd = reader.fixed32()
            break
          case 5:
            message.No = reader.uint32()
            break
          case 9:
            message.Res = reader.sint32()
            break
          case 10:
            message.Body = reader.bytes()
            break
          case 11:
            message.Optstr = reader.string()
            break
          case 12:
            message.Optbin = reader.bytes()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return Bmsg
  })()

  bysproto.BloginReq = (function () {
    /**
     * Properties of a BloginReq.
     * @memberof bysproto
     * @interface IBloginReq
     * @property {string|null} [Name] 控制器名称，不是控制器硬件ID
     * @property {string|null} [PassHash] base64(sha256(base64(sha256(controllerName+controllerPass))+LoginTimeStr))
     * 4g界桩: base64(sha256(base64(sha256(IMEI+ICCID))+LoginTimeStr))
     * @property {string|null} [LoginTimeStr] yyyy-mm-dd HH:MM:SS, 必须是3分钟内的时间(utc)
     * @property {string|null} [Sys] 系统名称
     * @property {number|null} [Power] 电量,单位V,=0无效，7.2v = 72
     * @property {number|null} [ChannelNo] 控制器本地手台当前信道号(对界桩通讯的手台)，无效时为0
     * @property {Array.<number>|null} [TransferChannelNos] 本控制器数据中转的通道号和对应的信道号
     * [通道号1,通道1手台的信道号，通道号2,通道2手台的信道号，...]
     * 如[1,1,2,1]表示通道1 在1信道，通道2也在1信道
     * @property {number|null} [NetworkType] 登录渠道， 0:未知 1:有线网络， 2:4g网络 3:无线FSK
     * @property {number|null} [DeviceType] 登录设备类型 0:fsk控制器 1:4g界桩
     * @property {string|null} [IMEI] 4g界桩IMEI
     * @property {string|null} [ICCID] 4g界桩ICCID
     * @property {string|null} [dataVersion] 界桩参数版本时间
     * yyyy-mm-dd HH:MM:SS
     */

    /**
     * Constructs a new BloginReq.
     * @memberof bysproto
     * @classdesc 登录信息
     * bmsg.cmd=1
     * @implements IBloginReq
     * @constructor
     * @param {bysproto.IBloginReq=} [properties] Properties to set
     */
    function BloginReq(properties) {
      this.TransferChannelNos = []
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 控制器名称，不是控制器硬件ID
     * @member {string} Name
     * @memberof bysproto.BloginReq
     * @instance
     */
    BloginReq.prototype.Name = ''

    /**
     * base64(sha256(base64(sha256(controllerName+controllerPass))+LoginTimeStr))
     * 4g界桩: base64(sha256(base64(sha256(IMEI+ICCID))+LoginTimeStr))
     * @member {string} PassHash
     * @memberof bysproto.BloginReq
     * @instance
     */
    BloginReq.prototype.PassHash = ''

    /**
     * yyyy-mm-dd HH:MM:SS, 必须是3分钟内的时间(utc)
     * @member {string} LoginTimeStr
     * @memberof bysproto.BloginReq
     * @instance
     */
    BloginReq.prototype.LoginTimeStr = ''

    /**
     * 系统名称
     * @member {string} Sys
     * @memberof bysproto.BloginReq
     * @instance
     */
    BloginReq.prototype.Sys = ''

    /**
     * 电量,单位V,=0无效，7.2v = 72
     * @member {number} Power
     * @memberof bysproto.BloginReq
     * @instance
     */
    BloginReq.prototype.Power = 0

    /**
     * 控制器本地手台当前信道号(对界桩通讯的手台)，无效时为0
     * @member {number} ChannelNo
     * @memberof bysproto.BloginReq
     * @instance
     */
    BloginReq.prototype.ChannelNo = 0

    /**
     * 本控制器数据中转的通道号和对应的信道号
     * [通道号1,通道1手台的信道号，通道号2,通道2手台的信道号，...]
     * 如[1,1,2,1]表示通道1 在1信道，通道2也在1信道
     * @member {Array.<number>} TransferChannelNos
     * @memberof bysproto.BloginReq
     * @instance
     */
    BloginReq.prototype.TransferChannelNos = $util.emptyArray

    /**
     * 登录渠道， 0:未知 1:有线网络， 2:4g网络 3:无线FSK
     * @member {number} NetworkType
     * @memberof bysproto.BloginReq
     * @instance
     */
    BloginReq.prototype.NetworkType = 0

    /**
     * 登录设备类型 0:fsk控制器 1:4g界桩
     * @member {number} DeviceType
     * @memberof bysproto.BloginReq
     * @instance
     */
    BloginReq.prototype.DeviceType = 0

    /**
     * 4g界桩IMEI
     * @member {string} IMEI
     * @memberof bysproto.BloginReq
     * @instance
     */
    BloginReq.prototype.IMEI = ''

    /**
     * 4g界桩ICCID
     * @member {string} ICCID
     * @memberof bysproto.BloginReq
     * @instance
     */
    BloginReq.prototype.ICCID = ''

    /**
     * 界桩参数版本时间
     * yyyy-mm-dd HH:MM:SS
     * @member {string} dataVersion
     * @memberof bysproto.BloginReq
     * @instance
     */
    BloginReq.prototype.dataVersion = ''

    /**
     * Encodes the specified BloginReq message. Does not implicitly {@link bysproto.BloginReq.verify|verify} messages.
     * @function encode
     * @memberof bysproto.BloginReq
     * @static
     * @param {bysproto.IBloginReq} message BloginReq message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    BloginReq.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (message.Name != null && Object.hasOwnProperty.call(message, 'Name'))
        writer.uint32(/* id 1, wireType 2 =*/ 10).string(message.Name)
      if (
        message.PassHash != null &&
        Object.hasOwnProperty.call(message, 'PassHash')
      )
        writer.uint32(/* id 2, wireType 2 =*/ 18).string(message.PassHash)
      if (
        message.LoginTimeStr != null &&
        Object.hasOwnProperty.call(message, 'LoginTimeStr')
      )
        writer.uint32(/* id 3, wireType 2 =*/ 26).string(message.LoginTimeStr)
      if (message.Sys != null && Object.hasOwnProperty.call(message, 'Sys'))
        writer.uint32(/* id 4, wireType 2 =*/ 34).string(message.Sys)
      if (message.Power != null && Object.hasOwnProperty.call(message, 'Power'))
        writer.uint32(/* id 5, wireType 0 =*/ 40).sint32(message.Power)
      if (
        message.ChannelNo != null &&
        Object.hasOwnProperty.call(message, 'ChannelNo')
      )
        writer.uint32(/* id 6, wireType 0 =*/ 48).sint32(message.ChannelNo)
      if (
        message.TransferChannelNos != null &&
        message.TransferChannelNos.length
      ) {
        writer.uint32(/* id 7, wireType 2 =*/ 58).fork()
        for (let i = 0; i < message.TransferChannelNos.length; ++i)
          writer.sint32(message.TransferChannelNos[i])
        writer.ldelim()
      }
      if (
        message.NetworkType != null &&
        Object.hasOwnProperty.call(message, 'NetworkType')
      )
        writer.uint32(/* id 8, wireType 0 =*/ 64).int32(message.NetworkType)
      if (
        message.DeviceType != null &&
        Object.hasOwnProperty.call(message, 'DeviceType')
      )
        writer.uint32(/* id 9, wireType 0 =*/ 72).int32(message.DeviceType)
      if (message.IMEI != null && Object.hasOwnProperty.call(message, 'IMEI'))
        writer.uint32(/* id 10, wireType 2 =*/ 82).string(message.IMEI)
      if (message.ICCID != null && Object.hasOwnProperty.call(message, 'ICCID'))
        writer.uint32(/* id 11, wireType 2 =*/ 90).string(message.ICCID)
      if (
        message.dataVersion != null &&
        Object.hasOwnProperty.call(message, 'dataVersion')
      )
        writer.uint32(/* id 12, wireType 2 =*/ 98).string(message.dataVersion)
      return writer
    }

    /**
     * Decodes a BloginReq message from the specified reader or buffer.
     * @function decode
     * @memberof bysproto.BloginReq
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {bysproto.BloginReq} BloginReq
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    BloginReq.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.bysproto.BloginReq()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.Name = reader.string()
            break
          case 2:
            message.PassHash = reader.string()
            break
          case 3:
            message.LoginTimeStr = reader.string()
            break
          case 4:
            message.Sys = reader.string()
            break
          case 5:
            message.Power = reader.sint32()
            break
          case 6:
            message.ChannelNo = reader.sint32()
            break
          case 7:
            if (
              !(message.TransferChannelNos && message.TransferChannelNos.length)
            )
              message.TransferChannelNos = []
            if ((tag & 7) === 2) {
              let end2 = reader.uint32() + reader.pos
              while (reader.pos < end2)
                message.TransferChannelNos.push(reader.sint32())
            } else message.TransferChannelNos.push(reader.sint32())
            break
          case 8:
            message.NetworkType = reader.int32()
            break
          case 9:
            message.DeviceType = reader.int32()
            break
          case 10:
            message.IMEI = reader.string()
            break
          case 11:
            message.ICCID = reader.string()
            break
          case 12:
            message.dataVersion = reader.string()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return BloginReq
  })()

  bysproto.BloginRes = (function () {
    /**
     * Properties of a BloginRes.
     * @memberof bysproto
     * @interface IBloginRes
     * @property {number|null} [Code] 100: 登录成功 -1：无此控制器信息 -2:时间参数不对 -3: 未知错误 -4:pass 不对
     * @property {number|null} [ControllerID] 控制器硬件ID，登录成功时返回
     * @property {string|null} [ServerTime] 服务器时间yyyy-mm-dd HH:MM:SS.zzz(utc)
     * @property {string|null} [SystemPassword] 校验界桩crc2的系统密码
     * @property {string|null} [Err] 错误描述，未知错误时可能有
     */

    /**
     * Constructs a new BloginRes.
     * @memberof bysproto
     * @classdesc 登录回应
     * bmsy.cmd=1
     * bmsg.Res=1
     * bmsg.body=BloginRes
     * @implements IBloginRes
     * @constructor
     * @param {bysproto.IBloginRes=} [properties] Properties to set
     */
    function BloginRes(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 100: 登录成功 -1：无此控制器信息 -2:时间参数不对 -3: 未知错误 -4:pass 不对
     * @member {number} Code
     * @memberof bysproto.BloginRes
     * @instance
     */
    BloginRes.prototype.Code = 0

    /**
     * 控制器硬件ID，登录成功时返回
     * @member {number} ControllerID
     * @memberof bysproto.BloginRes
     * @instance
     */
    BloginRes.prototype.ControllerID = 0

    /**
     * 服务器时间yyyy-mm-dd HH:MM:SS.zzz(utc)
     * @member {string} ServerTime
     * @memberof bysproto.BloginRes
     * @instance
     */
    BloginRes.prototype.ServerTime = ''

    /**
     * 校验界桩crc2的系统密码
     * @member {string} SystemPassword
     * @memberof bysproto.BloginRes
     * @instance
     */
    BloginRes.prototype.SystemPassword = ''

    /**
     * 错误描述，未知错误时可能有
     * @member {string} Err
     * @memberof bysproto.BloginRes
     * @instance
     */
    BloginRes.prototype.Err = ''

    /**
     * Encodes the specified BloginRes message. Does not implicitly {@link bysproto.BloginRes.verify|verify} messages.
     * @function encode
     * @memberof bysproto.BloginRes
     * @static
     * @param {bysproto.IBloginRes} message BloginRes message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    BloginRes.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (message.Code != null && Object.hasOwnProperty.call(message, 'Code'))
        writer.uint32(/* id 1, wireType 0 =*/ 8).int32(message.Code)
      if (
        message.ControllerID != null &&
        Object.hasOwnProperty.call(message, 'ControllerID')
      )
        writer.uint32(/* id 2, wireType 5 =*/ 21).fixed32(message.ControllerID)
      if (
        message.ServerTime != null &&
        Object.hasOwnProperty.call(message, 'ServerTime')
      )
        writer.uint32(/* id 5, wireType 2 =*/ 42).string(message.ServerTime)
      if (
        message.SystemPassword != null &&
        Object.hasOwnProperty.call(message, 'SystemPassword')
      )
        writer.uint32(/* id 6, wireType 2 =*/ 50).string(message.SystemPassword)
      if (message.Err != null && Object.hasOwnProperty.call(message, 'Err'))
        writer.uint32(/* id 7, wireType 2 =*/ 58).string(message.Err)
      return writer
    }

    /**
     * Decodes a BloginRes message from the specified reader or buffer.
     * @function decode
     * @memberof bysproto.BloginRes
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {bysproto.BloginRes} BloginRes
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    BloginRes.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.bysproto.BloginRes()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.Code = reader.int32()
            break
          case 2:
            message.ControllerID = reader.fixed32()
            break
          case 5:
            message.ServerTime = reader.string()
            break
          case 6:
            message.SystemPassword = reader.string()
            break
          case 7:
            message.Err = reader.string()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return BloginRes
  })()

  bysproto.BGPS = (function () {
    /**
     * Properties of a BGPS.
     * @memberof bysproto
     * @interface IBGPS
     * @property {number|null} [Lon] 东经为+，西经为-,单位为度
     * @property {number|null} [Lat] 北纬为+，南纬为-,单位为度
     * @property {number|null} [Height] BGPS Height
     */

    /**
     * Constructs a new BGPS.
     * @memberof bysproto
     * @classdesc 界桩上传的gps信息,无效gps时为全0
     * @implements IBGPS
     * @constructor
     * @param {bysproto.IBGPS=} [properties] Properties to set
     */
    function BGPS(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 东经为+，西经为-,单位为度
     * @member {number} Lon
     * @memberof bysproto.BGPS
     * @instance
     */
    BGPS.prototype.Lon = 0

    /**
     * 北纬为+，南纬为-,单位为度
     * @member {number} Lat
     * @memberof bysproto.BGPS
     * @instance
     */
    BGPS.prototype.Lat = 0

    /**
     * BGPS Height.
     * @member {number} Height
     * @memberof bysproto.BGPS
     * @instance
     */
    BGPS.prototype.Height = 0

    /**
     * Encodes the specified BGPS message. Does not implicitly {@link bysproto.BGPS.verify|verify} messages.
     * @function encode
     * @memberof bysproto.BGPS
     * @static
     * @param {bysproto.IBGPS} message BGPS message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    BGPS.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (message.Lon != null && Object.hasOwnProperty.call(message, 'Lon'))
        writer.uint32(/* id 1, wireType 1 =*/ 9).double(message.Lon)
      if (message.Lat != null && Object.hasOwnProperty.call(message, 'Lat'))
        writer.uint32(/* id 2, wireType 1 =*/ 17).double(message.Lat)
      if (
        message.Height != null &&
        Object.hasOwnProperty.call(message, 'Height')
      )
        writer.uint32(/* id 3, wireType 0 =*/ 24).sint32(message.Height)
      return writer
    }

    /**
     * Decodes a BGPS message from the specified reader or buffer.
     * @function decode
     * @memberof bysproto.BGPS
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {bysproto.BGPS} BGPS
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    BGPS.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.bysproto.BGPS()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.Lon = reader.double()
            break
          case 2:
            message.Lat = reader.double()
            break
          case 3:
            message.Height = reader.sint32()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return BGPS
  })()

  bysproto.BdeviceUpdate = (function () {
    /**
     * Properties of a BdeviceUpdate.
     * @memberof bysproto
     * @interface IBdeviceUpdate
     * @property {number|null} [DeviceID] 界桩ID
     * @property {number|null} [Cmd] 上传的命令
     * 0xd1: 常规上传
     * 0xd2: 报警上传
     * @property {number|null} [Status] 界桩状态，目前只用低位一个字节
     * @property {number|null} [SystemPassCheckOK] 界桩上传的系统密码检验是否正确 0:正确 1：不正确
     * @property {bysproto.IBGPS|null} [GPS] gps信息，没有时不需要填写
     * @property {number|null} [ParamVersion] 界桩参数版本
     * @property {string|null} [ParamTime] 界桩参数更新时间
     * @property {string|null} [CmdTime] 指令时间
     * @property {number|null} [StationID] 接收的基站/中继控制器ID
     * @property {number|null} [StationDeviceNo] 基站控制器的接收通道号，编号从1开始,不是基站上传的，此值为0
     * @property {number|null} [DeviceFieldStrength] 接收设备的场强值
     * @property {number|null} [DeviceChannelNo] 接收设备的信道号
     */

    /**
     * Constructs a new BdeviceUpdate.
     * @memberof bysproto
     * @classdesc 界桩数据上传
     * bmsg.cmd=2
     * @implements IBdeviceUpdate
     * @constructor
     * @param {bysproto.IBdeviceUpdate=} [properties] Properties to set
     */
    function BdeviceUpdate(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 界桩ID
     * @member {number} DeviceID
     * @memberof bysproto.BdeviceUpdate
     * @instance
     */
    BdeviceUpdate.prototype.DeviceID = 0

    /**
     * 上传的命令
     * 0xd1: 常规上传
     * 0xd2: 报警上传
     * @member {number} Cmd
     * @memberof bysproto.BdeviceUpdate
     * @instance
     */
    BdeviceUpdate.prototype.Cmd = 0

    /**
     * 界桩状态，目前只用低位一个字节
     * @member {number} Status
     * @memberof bysproto.BdeviceUpdate
     * @instance
     */
    BdeviceUpdate.prototype.Status = 0

    /**
     * 界桩上传的系统密码检验是否正确 0:正确 1：不正确
     * @member {number} SystemPassCheckOK
     * @memberof bysproto.BdeviceUpdate
     * @instance
     */
    BdeviceUpdate.prototype.SystemPassCheckOK = 0

    /**
     * gps信息，没有时不需要填写
     * @member {bysproto.IBGPS|null|undefined} GPS
     * @memberof bysproto.BdeviceUpdate
     * @instance
     */
    BdeviceUpdate.prototype.GPS = null

    /**
     * 界桩参数版本
     * @member {number} ParamVersion
     * @memberof bysproto.BdeviceUpdate
     * @instance
     */
    BdeviceUpdate.prototype.ParamVersion = 0

    /**
     * 界桩参数更新时间
     * @member {string} ParamTime
     * @memberof bysproto.BdeviceUpdate
     * @instance
     */
    BdeviceUpdate.prototype.ParamTime = ''

    /**
     * 指令时间
     * @member {string} CmdTime
     * @memberof bysproto.BdeviceUpdate
     * @instance
     */
    BdeviceUpdate.prototype.CmdTime = ''

    /**
     * 接收的基站/中继控制器ID
     * @member {number} StationID
     * @memberof bysproto.BdeviceUpdate
     * @instance
     */
    BdeviceUpdate.prototype.StationID = 0

    /**
     * 基站控制器的接收通道号，编号从1开始,不是基站上传的，此值为0
     * @member {number} StationDeviceNo
     * @memberof bysproto.BdeviceUpdate
     * @instance
     */
    BdeviceUpdate.prototype.StationDeviceNo = 0

    /**
     * 接收设备的场强值
     * @member {number} DeviceFieldStrength
     * @memberof bysproto.BdeviceUpdate
     * @instance
     */
    BdeviceUpdate.prototype.DeviceFieldStrength = 0

    /**
     * 接收设备的信道号
     * @member {number} DeviceChannelNo
     * @memberof bysproto.BdeviceUpdate
     * @instance
     */
    BdeviceUpdate.prototype.DeviceChannelNo = 0

    /**
     * Encodes the specified BdeviceUpdate message. Does not implicitly {@link bysproto.BdeviceUpdate.verify|verify} messages.
     * @function encode
     * @memberof bysproto.BdeviceUpdate
     * @static
     * @param {bysproto.IBdeviceUpdate} message BdeviceUpdate message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    BdeviceUpdate.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (
        message.DeviceID != null &&
        Object.hasOwnProperty.call(message, 'DeviceID')
      )
        writer.uint32(/* id 1, wireType 0 =*/ 8).sint32(message.DeviceID)
      if (message.Cmd != null && Object.hasOwnProperty.call(message, 'Cmd'))
        writer.uint32(/* id 2, wireType 0 =*/ 16).sint32(message.Cmd)
      if (
        message.Status != null &&
        Object.hasOwnProperty.call(message, 'Status')
      )
        writer.uint32(/* id 3, wireType 0 =*/ 24).sint32(message.Status)
      if (
        message.SystemPassCheckOK != null &&
        Object.hasOwnProperty.call(message, 'SystemPassCheckOK')
      )
        writer
          .uint32(/* id 4, wireType 0 =*/ 32)
          .int32(message.SystemPassCheckOK)
      if (message.GPS != null && Object.hasOwnProperty.call(message, 'GPS'))
        $root.bysproto.BGPS.encode(
          message.GPS,
          writer.uint32(/* id 5, wireType 2 =*/ 42).fork(),
        ).ldelim()
      if (
        message.ParamVersion != null &&
        Object.hasOwnProperty.call(message, 'ParamVersion')
      )
        writer.uint32(/* id 6, wireType 0 =*/ 48).int32(message.ParamVersion)
      if (
        message.ParamTime != null &&
        Object.hasOwnProperty.call(message, 'ParamTime')
      )
        writer.uint32(/* id 7, wireType 2 =*/ 58).string(message.ParamTime)
      if (
        message.CmdTime != null &&
        Object.hasOwnProperty.call(message, 'CmdTime')
      )
        writer.uint32(/* id 8, wireType 2 =*/ 66).string(message.CmdTime)
      if (
        message.StationID != null &&
        Object.hasOwnProperty.call(message, 'StationID')
      )
        writer.uint32(/* id 9, wireType 0 =*/ 72).sint32(message.StationID)
      if (
        message.StationDeviceNo != null &&
        Object.hasOwnProperty.call(message, 'StationDeviceNo')
      )
        writer
          .uint32(/* id 10, wireType 0 =*/ 80)
          .sint32(message.StationDeviceNo)
      if (
        message.DeviceFieldStrength != null &&
        Object.hasOwnProperty.call(message, 'DeviceFieldStrength')
      )
        writer
          .uint32(/* id 12, wireType 0 =*/ 96)
          .sint32(message.DeviceFieldStrength)
      if (
        message.DeviceChannelNo != null &&
        Object.hasOwnProperty.call(message, 'DeviceChannelNo')
      )
        writer
          .uint32(/* id 13, wireType 0 =*/ 104)
          .sint32(message.DeviceChannelNo)
      return writer
    }

    /**
     * Decodes a BdeviceUpdate message from the specified reader or buffer.
     * @function decode
     * @memberof bysproto.BdeviceUpdate
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {bysproto.BdeviceUpdate} BdeviceUpdate
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    BdeviceUpdate.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.bysproto.BdeviceUpdate()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.DeviceID = reader.sint32()
            break
          case 2:
            message.Cmd = reader.sint32()
            break
          case 3:
            message.Status = reader.sint32()
            break
          case 4:
            message.SystemPassCheckOK = reader.int32()
            break
          case 5:
            message.GPS = $root.bysproto.BGPS.decode(reader, reader.uint32())
            break
          case 6:
            message.ParamVersion = reader.int32()
            break
          case 7:
            message.ParamTime = reader.string()
            break
          case 8:
            message.CmdTime = reader.string()
            break
          case 9:
            message.StationID = reader.sint32()
            break
          case 10:
            message.StationDeviceNo = reader.sint32()
            break
          case 12:
            message.DeviceFieldStrength = reader.sint32()
            break
          case 13:
            message.DeviceChannelNo = reader.sint32()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return BdeviceUpdate
  })()

  bysproto.ControllerStatus = (function () {
    /**
     * Properties of a ControllerStatus.
     * @memberof bysproto
     * @interface IControllerStatus
     * @property {number|null} [StationID] 接收的基站/中继控制器ID
     * @property {number|null} [StationDeviceNo] 基站控制器的接收手台号，编号从1开始,本控制器的状态时，此值为0
     * @property {number|null} [Power] 电量,单位V,=0无效，7.2v = 72
     * @property {number|null} [ChannelNo] 控制器手台当前信道号(与界桩通讯的手台)
     * @property {Array.<number>|null} [TransferChannelNos] 本控制器数据中转的通道号和对应的信道号
     * [通道号1,通道1手台的信道号，通道号2,通道2手台的信道号，...]
     * 如[1,1,2,1]表示通道1 在1信道，通道2也在1信道
     * @property {number|null} [Status] 控制器状态
     * bit0:时间异常 0:无异常 1:无法获取时间（可能gps异常)
     */

    /**
     * Constructs a new ControllerStatus.
     * @memberof bysproto
     * @classdesc 中继/基站状态信息
     * @implements IControllerStatus
     * @constructor
     * @param {bysproto.IControllerStatus=} [properties] Properties to set
     */
    function ControllerStatus(properties) {
      this.TransferChannelNos = []
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 接收的基站/中继控制器ID
     * @member {number} StationID
     * @memberof bysproto.ControllerStatus
     * @instance
     */
    ControllerStatus.prototype.StationID = 0

    /**
     * 基站控制器的接收手台号，编号从1开始,本控制器的状态时，此值为0
     * @member {number} StationDeviceNo
     * @memberof bysproto.ControllerStatus
     * @instance
     */
    ControllerStatus.prototype.StationDeviceNo = 0

    /**
     * 电量,单位V,=0无效，7.2v = 72
     * @member {number} Power
     * @memberof bysproto.ControllerStatus
     * @instance
     */
    ControllerStatus.prototype.Power = 0

    /**
     * 控制器手台当前信道号(与界桩通讯的手台)
     * @member {number} ChannelNo
     * @memberof bysproto.ControllerStatus
     * @instance
     */
    ControllerStatus.prototype.ChannelNo = 0

    /**
     * 本控制器数据中转的通道号和对应的信道号
     * [通道号1,通道1手台的信道号，通道号2,通道2手台的信道号，...]
     * 如[1,1,2,1]表示通道1 在1信道，通道2也在1信道
     * @member {Array.<number>} TransferChannelNos
     * @memberof bysproto.ControllerStatus
     * @instance
     */
    ControllerStatus.prototype.TransferChannelNos = $util.emptyArray

    /**
     * 控制器状态
     * bit0:时间异常 0:无异常 1:无法获取时间（可能gps异常)
     * @member {number} Status
     * @memberof bysproto.ControllerStatus
     * @instance
     */
    ControllerStatus.prototype.Status = 0

    /**
     * Encodes the specified ControllerStatus message. Does not implicitly {@link bysproto.ControllerStatus.verify|verify} messages.
     * @function encode
     * @memberof bysproto.ControllerStatus
     * @static
     * @param {bysproto.IControllerStatus} message ControllerStatus message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    ControllerStatus.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (
        message.StationID != null &&
        Object.hasOwnProperty.call(message, 'StationID')
      )
        writer.uint32(/* id 1, wireType 0 =*/ 8).sint32(message.StationID)
      if (
        message.StationDeviceNo != null &&
        Object.hasOwnProperty.call(message, 'StationDeviceNo')
      )
        writer
          .uint32(/* id 2, wireType 0 =*/ 16)
          .sint32(message.StationDeviceNo)
      if (message.Power != null && Object.hasOwnProperty.call(message, 'Power'))
        writer.uint32(/* id 3, wireType 0 =*/ 24).sint32(message.Power)
      if (
        message.ChannelNo != null &&
        Object.hasOwnProperty.call(message, 'ChannelNo')
      )
        writer.uint32(/* id 4, wireType 0 =*/ 32).sint32(message.ChannelNo)
      if (
        message.TransferChannelNos != null &&
        message.TransferChannelNos.length
      ) {
        writer.uint32(/* id 7, wireType 2 =*/ 58).fork()
        for (let i = 0; i < message.TransferChannelNos.length; ++i)
          writer.sint32(message.TransferChannelNos[i])
        writer.ldelim()
      }
      if (
        message.Status != null &&
        Object.hasOwnProperty.call(message, 'Status')
      )
        writer.uint32(/* id 8, wireType 0 =*/ 64).uint32(message.Status)
      return writer
    }

    /**
     * Decodes a ControllerStatus message from the specified reader or buffer.
     * @function decode
     * @memberof bysproto.ControllerStatus
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {bysproto.ControllerStatus} ControllerStatus
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    ControllerStatus.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.bysproto.ControllerStatus()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.StationID = reader.sint32()
            break
          case 2:
            message.StationDeviceNo = reader.sint32()
            break
          case 3:
            message.Power = reader.sint32()
            break
          case 4:
            message.ChannelNo = reader.sint32()
            break
          case 7:
            if (
              !(message.TransferChannelNos && message.TransferChannelNos.length)
            )
              message.TransferChannelNos = []
            if ((tag & 7) === 2) {
              let end2 = reader.uint32() + reader.pos
              while (reader.pos < end2)
                message.TransferChannelNos.push(reader.sint32())
            } else message.TransferChannelNos.push(reader.sint32())
            break
          case 8:
            message.Status = reader.uint32()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return ControllerStatus
  })()

  bysproto.BdeviceUpdateResponse = (function () {
    /**
     * Properties of a BdeviceUpdateResponse.
     * @memberof bysproto
     * @interface IBdeviceUpdateResponse
     * @property {number|null} [DeviceID] 界桩ID
     * @property {number|null} [StationID] 接收的基站/中继控制器ID
     * @property {number|null} [StationDeviceNo] 基站控制器的通道号，编号从1开始,不是基站上传的，此值为0
     * @property {Uint8Array|null} [Cmd0xD0] 回应命令数据
     */

    /**
     * Constructs a new BdeviceUpdateResponse.
     * @memberof bysproto
     * @classdesc 回应界桩上传（如果确实有参数需要修改的话）
     * bmsg.cmd=12
     * @implements IBdeviceUpdateResponse
     * @constructor
     * @param {bysproto.IBdeviceUpdateResponse=} [properties] Properties to set
     */
    function BdeviceUpdateResponse(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 界桩ID
     * @member {number} DeviceID
     * @memberof bysproto.BdeviceUpdateResponse
     * @instance
     */
    BdeviceUpdateResponse.prototype.DeviceID = 0

    /**
     * 接收的基站/中继控制器ID
     * @member {number} StationID
     * @memberof bysproto.BdeviceUpdateResponse
     * @instance
     */
    BdeviceUpdateResponse.prototype.StationID = 0

    /**
     * 基站控制器的通道号，编号从1开始,不是基站上传的，此值为0
     * @member {number} StationDeviceNo
     * @memberof bysproto.BdeviceUpdateResponse
     * @instance
     */
    BdeviceUpdateResponse.prototype.StationDeviceNo = 0

    /**
     * 回应命令数据
     * @member {Uint8Array} Cmd0xD0
     * @memberof bysproto.BdeviceUpdateResponse
     * @instance
     */
    BdeviceUpdateResponse.prototype.Cmd0xD0 = $util.newBuffer([])

    /**
     * Encodes the specified BdeviceUpdateResponse message. Does not implicitly {@link bysproto.BdeviceUpdateResponse.verify|verify} messages.
     * @function encode
     * @memberof bysproto.BdeviceUpdateResponse
     * @static
     * @param {bysproto.IBdeviceUpdateResponse} message BdeviceUpdateResponse message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    BdeviceUpdateResponse.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (
        message.DeviceID != null &&
        Object.hasOwnProperty.call(message, 'DeviceID')
      )
        writer.uint32(/* id 1, wireType 0 =*/ 8).sint32(message.DeviceID)
      if (
        message.StationID != null &&
        Object.hasOwnProperty.call(message, 'StationID')
      )
        writer.uint32(/* id 9, wireType 0 =*/ 72).sint32(message.StationID)
      if (
        message.StationDeviceNo != null &&
        Object.hasOwnProperty.call(message, 'StationDeviceNo')
      )
        writer
          .uint32(/* id 10, wireType 0 =*/ 80)
          .sint32(message.StationDeviceNo)
      if (
        message.Cmd0xD0 != null &&
        Object.hasOwnProperty.call(message, 'Cmd0xD0')
      )
        writer.uint32(/* id 11, wireType 2 =*/ 90).bytes(message.Cmd0xD0)
      return writer
    }

    /**
     * Decodes a BdeviceUpdateResponse message from the specified reader or buffer.
     * @function decode
     * @memberof bysproto.BdeviceUpdateResponse
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {bysproto.BdeviceUpdateResponse} BdeviceUpdateResponse
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    BdeviceUpdateResponse.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.bysproto.BdeviceUpdateResponse()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.DeviceID = reader.sint32()
            break
          case 9:
            message.StationID = reader.sint32()
            break
          case 10:
            message.StationDeviceNo = reader.sint32()
            break
          case 11:
            message.Cmd0xD0 = reader.bytes()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return BdeviceUpdateResponse
  })()

  bysproto.BControllerUpdateChannel = (function () {
    /**
     * Properties of a BControllerUpdateChannel.
     * @memberof bysproto
     * @interface IBControllerUpdateChannel
     * @property {number|null} [StationID] 控制器ID
     * @property {number|null} [StationDeviceNo] 控制器通道号
     * @property {number|null} [NewChannelNo] 新默认监听信道号
     */

    /**
     * Constructs a new BControllerUpdateChannel.
     * @memberof bysproto
     * @classdesc 修改控制器手台默认信道号（针对与界桩通讯的手台）
     * bmsg.cmd=13
     * @implements IBControllerUpdateChannel
     * @constructor
     * @param {bysproto.IBControllerUpdateChannel=} [properties] Properties to set
     */
    function BControllerUpdateChannel(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 控制器ID
     * @member {number} StationID
     * @memberof bysproto.BControllerUpdateChannel
     * @instance
     */
    BControllerUpdateChannel.prototype.StationID = 0

    /**
     * 控制器通道号
     * @member {number} StationDeviceNo
     * @memberof bysproto.BControllerUpdateChannel
     * @instance
     */
    BControllerUpdateChannel.prototype.StationDeviceNo = 0

    /**
     * 新默认监听信道号
     * @member {number} NewChannelNo
     * @memberof bysproto.BControllerUpdateChannel
     * @instance
     */
    BControllerUpdateChannel.prototype.NewChannelNo = 0

    /**
     * Encodes the specified BControllerUpdateChannel message. Does not implicitly {@link bysproto.BControllerUpdateChannel.verify|verify} messages.
     * @function encode
     * @memberof bysproto.BControllerUpdateChannel
     * @static
     * @param {bysproto.IBControllerUpdateChannel} message BControllerUpdateChannel message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    BControllerUpdateChannel.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (
        message.NewChannelNo != null &&
        Object.hasOwnProperty.call(message, 'NewChannelNo')
      )
        writer.uint32(/* id 3, wireType 0 =*/ 24).sint32(message.NewChannelNo)
      if (
        message.StationID != null &&
        Object.hasOwnProperty.call(message, 'StationID')
      )
        writer.uint32(/* id 9, wireType 0 =*/ 72).sint32(message.StationID)
      if (
        message.StationDeviceNo != null &&
        Object.hasOwnProperty.call(message, 'StationDeviceNo')
      )
        writer
          .uint32(/* id 10, wireType 0 =*/ 80)
          .sint32(message.StationDeviceNo)
      return writer
    }

    /**
     * Decodes a BControllerUpdateChannel message from the specified reader or buffer.
     * @function decode
     * @memberof bysproto.BControllerUpdateChannel
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {bysproto.BControllerUpdateChannel} BControllerUpdateChannel
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    BControllerUpdateChannel.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.bysproto.BControllerUpdateChannel()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 9:
            message.StationID = reader.sint32()
            break
          case 10:
            message.StationDeviceNo = reader.sint32()
            break
          case 3:
            message.NewChannelNo = reader.sint32()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return BControllerUpdateChannel
  })()

  bysproto.BControllerNewServerAddr = (function () {
    /**
     * Properties of a BControllerNewServerAddr.
     * @memberof bysproto
     * @interface IBControllerNewServerAddr
     * @property {number|null} [StationID] 基站控制器ID
     * @property {number|null} [ControllerChannelNo] 在上级控制器的通道号，中继时需要填写，基站本地通道时为0
     * @property {string|null} [Ip] 新服务器IP地址，支持域名和IP
     * @property {number|null} [Port] 新服务器端口
     */

    /**
     * Constructs a new BControllerNewServerAddr.
     * @memberof bysproto
     * @classdesc bmsg.cmd=15, 更新控制器新的注册地址
     * bmsg.body=BControllerNewServerAddr
     * bmsg.Res=1,控制器应答
     * @implements IBControllerNewServerAddr
     * @constructor
     * @param {bysproto.IBControllerNewServerAddr=} [properties] Properties to set
     */
    function BControllerNewServerAddr(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 基站控制器ID
     * @member {number} StationID
     * @memberof bysproto.BControllerNewServerAddr
     * @instance
     */
    BControllerNewServerAddr.prototype.StationID = 0

    /**
     * 在上级控制器的通道号，中继时需要填写，基站本地通道时为0
     * @member {number} ControllerChannelNo
     * @memberof bysproto.BControllerNewServerAddr
     * @instance
     */
    BControllerNewServerAddr.prototype.ControllerChannelNo = 0

    /**
     * 新服务器IP地址，支持域名和IP
     * @member {string} Ip
     * @memberof bysproto.BControllerNewServerAddr
     * @instance
     */
    BControllerNewServerAddr.prototype.Ip = ''

    /**
     * 新服务器端口
     * @member {number} Port
     * @memberof bysproto.BControllerNewServerAddr
     * @instance
     */
    BControllerNewServerAddr.prototype.Port = 0

    /**
     * Encodes the specified BControllerNewServerAddr message. Does not implicitly {@link bysproto.BControllerNewServerAddr.verify|verify} messages.
     * @function encode
     * @memberof bysproto.BControllerNewServerAddr
     * @static
     * @param {bysproto.IBControllerNewServerAddr} message BControllerNewServerAddr message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    BControllerNewServerAddr.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (
        message.StationID != null &&
        Object.hasOwnProperty.call(message, 'StationID')
      )
        writer.uint32(/* id 1, wireType 0 =*/ 8).sint32(message.StationID)
      if (
        message.ControllerChannelNo != null &&
        Object.hasOwnProperty.call(message, 'ControllerChannelNo')
      )
        writer
          .uint32(/* id 2, wireType 0 =*/ 16)
          .sint32(message.ControllerChannelNo)
      if (message.Ip != null && Object.hasOwnProperty.call(message, 'Ip'))
        writer.uint32(/* id 3, wireType 2 =*/ 26).string(message.Ip)
      if (message.Port != null && Object.hasOwnProperty.call(message, 'Port'))
        writer.uint32(/* id 4, wireType 0 =*/ 32).sint32(message.Port)
      return writer
    }

    /**
     * Decodes a BControllerNewServerAddr message from the specified reader or buffer.
     * @function decode
     * @memberof bysproto.BControllerNewServerAddr
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {bysproto.BControllerNewServerAddr} BControllerNewServerAddr
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    BControllerNewServerAddr.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.bysproto.BControllerNewServerAddr()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.StationID = reader.sint32()
            break
          case 2:
            message.ControllerChannelNo = reader.sint32()
            break
          case 3:
            message.Ip = reader.string()
            break
          case 4:
            message.Port = reader.sint32()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return BControllerNewServerAddr
  })()

  bysproto.BInfoReporting = (function () {
    /**
     * Properties of a BInfoReporting.
     * @memberof bysproto
     * @interface IBInfoReporting
     * @property {number|null} [deviceID] 本机设备编号
     * @property {number|null} [type] 1：开机上报 2：调试上报 3：定时上报
     * @property {string|null} [iccid] sim卡iccid 20位
     * @property {string|null} [softwareVersion] 格式：Vx.x.x_yyMMdd V1.0.1_231031
     * @property {string|null} [dataVersion] yyyy-mm-dd HH:MM:SS
     * @property {string|null} [time] 设备时间yyyy-mm-dd HH:MM:SS
     * @property {number|null} [battery] 设备电池电压单位mv
     * @property {number|null} [signal] 设备4G模块场强信号幅度 单位dbm
     * @property {number|null} [state] Bit0：报警锁定(0:正常，1:报警锁定)
     * Bit1：RTC时钟状态(0:正常，1:故障)
     * Bit2：GPS模块状态(0:正常，1:故障)
     * Bit3：三轴传感器状态(0:正常，1:故障)
     * Bit4：电池状态(0:正常，1:故障)
     * Bit5：摄像头状态(0:正常，1:故障)(预留)
     * Bit6：红外探头状态(0:正常，1:故障)(预留)
     * Bit7-Bit31：预留
     * @property {number|null} [alarmstate] Bit0：位移报警(0:正常，1:报警)
     * Bit1：震动报警(0:正常，1:报警)
     * Bit2：倾斜报警(0:正常，1:报警)
     * Bit3：红外报警(0:正常，1:报警)(预留)
     * Bit4-Bit31：预留
     * @property {number|null} [temp] 设备温度
     * @property {number|null} [rh] 湿度 单位：%RH
     */

    /**
     * Constructs a new BInfoReporting.
     * @memberof bysproto
     * @classdesc 信息上报
     * bmsg.cmd=21
     * @implements IBInfoReporting
     * @constructor
     * @param {bysproto.IBInfoReporting=} [properties] Properties to set
     */
    function BInfoReporting(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 本机设备编号
     * @member {number} deviceID
     * @memberof bysproto.BInfoReporting
     * @instance
     */
    BInfoReporting.prototype.deviceID = 0

    /**
     * 1：开机上报 2：调试上报 3：定时上报
     * @member {number} type
     * @memberof bysproto.BInfoReporting
     * @instance
     */
    BInfoReporting.prototype.type = 0

    /**
     * sim卡iccid 20位
     * @member {string} iccid
     * @memberof bysproto.BInfoReporting
     * @instance
     */
    BInfoReporting.prototype.iccid = ''

    /**
     * 格式：Vx.x.x_yyMMdd V1.0.1_231031
     * @member {string} softwareVersion
     * @memberof bysproto.BInfoReporting
     * @instance
     */
    BInfoReporting.prototype.softwareVersion = ''

    /**
     * yyyy-mm-dd HH:MM:SS
     * @member {string} dataVersion
     * @memberof bysproto.BInfoReporting
     * @instance
     */
    BInfoReporting.prototype.dataVersion = ''

    /**
     * 设备时间yyyy-mm-dd HH:MM:SS
     * @member {string} time
     * @memberof bysproto.BInfoReporting
     * @instance
     */
    BInfoReporting.prototype.time = ''

    /**
     * 设备电池电压单位mv
     * @member {number} battery
     * @memberof bysproto.BInfoReporting
     * @instance
     */
    BInfoReporting.prototype.battery = 0

    /**
     * 设备4G模块场强信号幅度 单位dbm
     * @member {number} signal
     * @memberof bysproto.BInfoReporting
     * @instance
     */
    BInfoReporting.prototype.signal = 0

    /**
     * Bit0：报警锁定(0:正常，1:报警锁定)
     * Bit1：RTC时钟状态(0:正常，1:故障)
     * Bit2：GPS模块状态(0:正常，1:故障)
     * Bit3：三轴传感器状态(0:正常，1:故障)
     * Bit4：电池状态(0:正常，1:故障)
     * Bit5：摄像头状态(0:正常，1:故障)(预留)
     * Bit6：红外探头状态(0:正常，1:故障)(预留)
     * Bit7-Bit31：预留
     * @member {number} state
     * @memberof bysproto.BInfoReporting
     * @instance
     */
    BInfoReporting.prototype.state = 0

    /**
     * Bit0：位移报警(0:正常，1:报警)
     * Bit1：震动报警(0:正常，1:报警)
     * Bit2：倾斜报警(0:正常，1:报警)
     * Bit3：红外报警(0:正常，1:报警)(预留)
     * Bit4-Bit31：预留
     * @member {number} alarmstate
     * @memberof bysproto.BInfoReporting
     * @instance
     */
    BInfoReporting.prototype.alarmstate = 0

    /**
     * 设备温度
     * @member {number} temp
     * @memberof bysproto.BInfoReporting
     * @instance
     */
    BInfoReporting.prototype.temp = 0

    /**
     * 湿度 单位：%RH
     * @member {number} rh
     * @memberof bysproto.BInfoReporting
     * @instance
     */
    BInfoReporting.prototype.rh = 0

    /**
     * Encodes the specified BInfoReporting message. Does not implicitly {@link bysproto.BInfoReporting.verify|verify} messages.
     * @function encode
     * @memberof bysproto.BInfoReporting
     * @static
     * @param {bysproto.IBInfoReporting} message BInfoReporting message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    BInfoReporting.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (
        message.deviceID != null &&
        Object.hasOwnProperty.call(message, 'deviceID')
      )
        writer.uint32(/* id 1, wireType 0 =*/ 8).uint32(message.deviceID)
      if (message.type != null && Object.hasOwnProperty.call(message, 'type'))
        writer.uint32(/* id 2, wireType 0 =*/ 16).uint32(message.type)
      if (message.iccid != null && Object.hasOwnProperty.call(message, 'iccid'))
        writer.uint32(/* id 3, wireType 2 =*/ 26).string(message.iccid)
      if (
        message.softwareVersion != null &&
        Object.hasOwnProperty.call(message, 'softwareVersion')
      )
        writer
          .uint32(/* id 4, wireType 2 =*/ 34)
          .string(message.softwareVersion)
      if (
        message.dataVersion != null &&
        Object.hasOwnProperty.call(message, 'dataVersion')
      )
        writer.uint32(/* id 5, wireType 2 =*/ 42).string(message.dataVersion)
      if (message.time != null && Object.hasOwnProperty.call(message, 'time'))
        writer.uint32(/* id 6, wireType 2 =*/ 50).string(message.time)
      if (
        message.battery != null &&
        Object.hasOwnProperty.call(message, 'battery')
      )
        writer.uint32(/* id 7, wireType 0 =*/ 56).uint32(message.battery)
      if (
        message.signal != null &&
        Object.hasOwnProperty.call(message, 'signal')
      )
        writer.uint32(/* id 8, wireType 0 =*/ 64).sint32(message.signal)
      if (message.state != null && Object.hasOwnProperty.call(message, 'state'))
        writer.uint32(/* id 9, wireType 0 =*/ 72).uint32(message.state)
      if (
        message.alarmstate != null &&
        Object.hasOwnProperty.call(message, 'alarmstate')
      )
        writer.uint32(/* id 10, wireType 0 =*/ 80).uint32(message.alarmstate)
      if (message.temp != null && Object.hasOwnProperty.call(message, 'temp'))
        writer.uint32(/* id 11, wireType 5 =*/ 93).float(message.temp)
      if (message.rh != null && Object.hasOwnProperty.call(message, 'rh'))
        writer.uint32(/* id 12, wireType 5 =*/ 101).float(message.rh)
      return writer
    }

    /**
     * Decodes a BInfoReporting message from the specified reader or buffer.
     * @function decode
     * @memberof bysproto.BInfoReporting
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {bysproto.BInfoReporting} BInfoReporting
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    BInfoReporting.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.bysproto.BInfoReporting()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.deviceID = reader.uint32()
            break
          case 2:
            message.type = reader.uint32()
            break
          case 3:
            message.iccid = reader.string()
            break
          case 4:
            message.softwareVersion = reader.string()
            break
          case 5:
            message.dataVersion = reader.string()
            break
          case 6:
            message.time = reader.string()
            break
          case 7:
            message.battery = reader.uint32()
            break
          case 8:
            message.signal = reader.sint32()
            break
          case 9:
            message.state = reader.uint32()
            break
          case 10:
            message.alarmstate = reader.uint32()
            break
          case 11:
            message.temp = reader.float()
            break
          case 12:
            message.rh = reader.float()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return BInfoReporting
  })()

  bysproto.BVamp = (function () {
    /**
     * Properties of a BVamp.
     * @memberof bysproto
     * @interface IBVamp
     * @property {number|null} [amplitude] 震动幅度
     * @property {number|null} [duration] 震动持续时间 s
     */

    /**
     * Constructs a new BVamp.
     * @memberof bysproto
     * @classdesc 震动报警参数
     * @implements IBVamp
     * @constructor
     * @param {bysproto.IBVamp=} [properties] Properties to set
     */
    function BVamp(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 震动幅度
     * @member {number} amplitude
     * @memberof bysproto.BVamp
     * @instance
     */
    BVamp.prototype.amplitude = 0

    /**
     * 震动持续时间 s
     * @member {number} duration
     * @memberof bysproto.BVamp
     * @instance
     */
    BVamp.prototype.duration = 0

    /**
     * Encodes the specified BVamp message. Does not implicitly {@link bysproto.BVamp.verify|verify} messages.
     * @function encode
     * @memberof bysproto.BVamp
     * @static
     * @param {bysproto.IBVamp} message BVamp message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    BVamp.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (
        message.amplitude != null &&
        Object.hasOwnProperty.call(message, 'amplitude')
      )
        writer.uint32(/* id 1, wireType 0 =*/ 8).uint32(message.amplitude)
      if (
        message.duration != null &&
        Object.hasOwnProperty.call(message, 'duration')
      )
        writer.uint32(/* id 2, wireType 0 =*/ 16).uint32(message.duration)
      return writer
    }

    /**
     * Decodes a BVamp message from the specified reader or buffer.
     * @function decode
     * @memberof bysproto.BVamp
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {bysproto.BVamp} BVamp
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    BVamp.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.bysproto.BVamp()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.amplitude = reader.uint32()
            break
          case 2:
            message.duration = reader.uint32()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return BVamp
  })()

  bysproto.BAlarmReporting = (function () {
    /**
     * Properties of a BAlarmReporting.
     * @memberof bysproto
     * @interface IBAlarmReporting
     * @property {number|null} [deviceID] 本机设备编号
     * @property {number|null} [type] 1：开机上报 2：调试上报 3：报警上报
     * @property {number|null} [alarmstate] Bit0：位移报警(0:正常，1:报警)
     * Bit1：震动报警(0:正常，1:报警)
     * Bit2：倾斜报警(0:正常，1:报警)
     * Bit3：红外报警(0:正常，1:报警)(预留)
     * Bit4-Bit31：预留
     * @property {bysproto.IBGPS|null} [locate] 定位坐标
     * @property {number|null} [attitude] 倾斜角度，单位：度
     * @property {bysproto.IBVamp|null} [vibration] 震动报警
     * @property {number|null} [infrared] 红外预留
     * @property {number|null} [camera] 摄像头预留
     * @property {Uint8Array|null} [reserve] 其他（预留）
     */

    /**
     * Constructs a new BAlarmReporting.
     * @memberof bysproto
     * @classdesc 报警上报
     * bmsg.cmd=22
     * @implements IBAlarmReporting
     * @constructor
     * @param {bysproto.IBAlarmReporting=} [properties] Properties to set
     */
    function BAlarmReporting(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 本机设备编号
     * @member {number} deviceID
     * @memberof bysproto.BAlarmReporting
     * @instance
     */
    BAlarmReporting.prototype.deviceID = 0

    /**
     * 1：开机上报 2：调试上报 3：报警上报
     * @member {number} type
     * @memberof bysproto.BAlarmReporting
     * @instance
     */
    BAlarmReporting.prototype.type = 0

    /**
     * Bit0：位移报警(0:正常，1:报警)
     * Bit1：震动报警(0:正常，1:报警)
     * Bit2：倾斜报警(0:正常，1:报警)
     * Bit3：红外报警(0:正常，1:报警)(预留)
     * Bit4-Bit31：预留
     * @member {number} alarmstate
     * @memberof bysproto.BAlarmReporting
     * @instance
     */
    BAlarmReporting.prototype.alarmstate = 0

    /**
     * 定位坐标
     * @member {bysproto.IBGPS|null|undefined} locate
     * @memberof bysproto.BAlarmReporting
     * @instance
     */
    BAlarmReporting.prototype.locate = null

    /**
     * 倾斜角度，单位：度
     * @member {number} attitude
     * @memberof bysproto.BAlarmReporting
     * @instance
     */
    BAlarmReporting.prototype.attitude = 0

    /**
     * 震动报警
     * @member {bysproto.IBVamp|null|undefined} vibration
     * @memberof bysproto.BAlarmReporting
     * @instance
     */
    BAlarmReporting.prototype.vibration = null

    /**
     * 红外预留
     * @member {number} infrared
     * @memberof bysproto.BAlarmReporting
     * @instance
     */
    BAlarmReporting.prototype.infrared = 0

    /**
     * 摄像头预留
     * @member {number} camera
     * @memberof bysproto.BAlarmReporting
     * @instance
     */
    BAlarmReporting.prototype.camera = 0

    /**
     * 其他（预留）
     * @member {Uint8Array} reserve
     * @memberof bysproto.BAlarmReporting
     * @instance
     */
    BAlarmReporting.prototype.reserve = $util.newBuffer([])

    /**
     * Encodes the specified BAlarmReporting message. Does not implicitly {@link bysproto.BAlarmReporting.verify|verify} messages.
     * @function encode
     * @memberof bysproto.BAlarmReporting
     * @static
     * @param {bysproto.IBAlarmReporting} message BAlarmReporting message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    BAlarmReporting.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (
        message.deviceID != null &&
        Object.hasOwnProperty.call(message, 'deviceID')
      )
        writer.uint32(/* id 1, wireType 0 =*/ 8).uint32(message.deviceID)
      if (message.type != null && Object.hasOwnProperty.call(message, 'type'))
        writer.uint32(/* id 2, wireType 0 =*/ 16).uint32(message.type)
      if (
        message.alarmstate != null &&
        Object.hasOwnProperty.call(message, 'alarmstate')
      )
        writer.uint32(/* id 3, wireType 0 =*/ 24).uint32(message.alarmstate)
      if (
        message.locate != null &&
        Object.hasOwnProperty.call(message, 'locate')
      )
        $root.bysproto.BGPS.encode(
          message.locate,
          writer.uint32(/* id 4, wireType 2 =*/ 34).fork(),
        ).ldelim()
      if (
        message.attitude != null &&
        Object.hasOwnProperty.call(message, 'attitude')
      )
        writer.uint32(/* id 5, wireType 0 =*/ 40).uint32(message.attitude)
      if (
        message.vibration != null &&
        Object.hasOwnProperty.call(message, 'vibration')
      )
        $root.bysproto.BVamp.encode(
          message.vibration,
          writer.uint32(/* id 6, wireType 2 =*/ 50).fork(),
        ).ldelim()
      if (
        message.infrared != null &&
        Object.hasOwnProperty.call(message, 'infrared')
      )
        writer.uint32(/* id 7, wireType 0 =*/ 56).uint32(message.infrared)
      if (
        message.camera != null &&
        Object.hasOwnProperty.call(message, 'camera')
      )
        writer.uint32(/* id 8, wireType 0 =*/ 64).uint32(message.camera)
      if (
        message.reserve != null &&
        Object.hasOwnProperty.call(message, 'reserve')
      )
        writer.uint32(/* id 9, wireType 2 =*/ 74).bytes(message.reserve)
      return writer
    }

    /**
     * Decodes a BAlarmReporting message from the specified reader or buffer.
     * @function decode
     * @memberof bysproto.BAlarmReporting
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {bysproto.BAlarmReporting} BAlarmReporting
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    BAlarmReporting.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.bysproto.BAlarmReporting()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.deviceID = reader.uint32()
            break
          case 2:
            message.type = reader.uint32()
            break
          case 3:
            message.alarmstate = reader.uint32()
            break
          case 4:
            message.locate = $root.bysproto.BGPS.decode(reader, reader.uint32())
            break
          case 5:
            message.attitude = reader.uint32()
            break
          case 6:
            message.vibration = $root.bysproto.BVamp.decode(
              reader,
              reader.uint32(),
            )
            break
          case 7:
            message.infrared = reader.uint32()
            break
          case 8:
            message.camera = reader.uint32()
            break
          case 9:
            message.reserve = reader.bytes()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return BAlarmReporting
  })()

  bysproto.BAlarmClear = (function () {
    /**
     * Properties of a BAlarmClear.
     * @memberof bysproto
     * @interface IBAlarmClear
     * @property {number|null} [deviceID] 目标设备编号
     * @property {number|null} [response] 1：报警解除 2：报警锁定
     */

    /**
     * Constructs a new BAlarmClear.
     * @memberof bysproto
     * @classdesc 报警解除
     * bmsg.cmd=33
     * @implements IBAlarmClear
     * @constructor
     * @param {bysproto.IBAlarmClear=} [properties] Properties to set
     */
    function BAlarmClear(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 目标设备编号
     * @member {number} deviceID
     * @memberof bysproto.BAlarmClear
     * @instance
     */
    BAlarmClear.prototype.deviceID = 0

    /**
     * 1：报警解除 2：报警锁定
     * @member {number} response
     * @memberof bysproto.BAlarmClear
     * @instance
     */
    BAlarmClear.prototype.response = 0

    /**
     * Encodes the specified BAlarmClear message. Does not implicitly {@link bysproto.BAlarmClear.verify|verify} messages.
     * @function encode
     * @memberof bysproto.BAlarmClear
     * @static
     * @param {bysproto.IBAlarmClear} message BAlarmClear message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    BAlarmClear.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (
        message.deviceID != null &&
        Object.hasOwnProperty.call(message, 'deviceID')
      )
        writer.uint32(/* id 1, wireType 0 =*/ 8).uint32(message.deviceID)
      if (
        message.response != null &&
        Object.hasOwnProperty.call(message, 'response')
      )
        writer.uint32(/* id 2, wireType 0 =*/ 16).uint32(message.response)
      return writer
    }

    /**
     * Decodes a BAlarmClear message from the specified reader or buffer.
     * @function decode
     * @memberof bysproto.BAlarmClear
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {bysproto.BAlarmClear} BAlarmClear
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    BAlarmClear.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.bysproto.BAlarmClear()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.deviceID = reader.uint32()
            break
          case 2:
            message.response = reader.uint32()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return BAlarmClear
  })()

  bysproto.BDataUpdate = (function () {
    /**
     * Properties of a BDataUpdate.
     * @memberof bysproto
     * @interface IBDataUpdate
     * @property {number|null} [deviceID] 目标设备编号
     * @property {number|null} [type] Bit0：解除报警锁定 Bit1：更新参数 Bit2：校准时间
     * @property {string|null} [dataVersion] 版本yyyy-mm-dd HH:MM:SS
     * @property {string|null} [addr] 服务器地址  xxx.xxx.xxx.xxx:xxxx   xxxxxx.com:xxxx
     * @property {string|null} [timerbase] 定时上报基准时间HH:MM:SS
     * @property {number|null} [timer] timer 6h、8h、12h、24h
     * @property {number|null} [attitude] 姿态报警阈值
     * @property {number|null} [dirft] 位移报警阈值
     * @property {bysproto.IBVamp|null} [vibration] 震动报警阈值
     * @property {number|null} [infrared] 红外报警阈值
     * @property {number|null} [t1] 延迟休眠时间 10s
     * @property {number|null} [t2] 调试模式时间 120s
     * @property {number|null} [t3] 报警间隔 10s
     * @property {number|null} [n1] 报警次数 10
     */

    /**
     * Constructs a new BDataUpdate.
     * @memberof bysproto
     * @classdesc 参数更新
     * bmsg.cmd=24
     * @implements IBDataUpdate
     * @constructor
     * @param {bysproto.IBDataUpdate=} [properties] Properties to set
     */
    function BDataUpdate(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 目标设备编号
     * @member {number} deviceID
     * @memberof bysproto.BDataUpdate
     * @instance
     */
    BDataUpdate.prototype.deviceID = 0

    /**
     * Bit0：解除报警锁定 Bit1：更新参数 Bit2：校准时间
     * @member {number} type
     * @memberof bysproto.BDataUpdate
     * @instance
     */
    BDataUpdate.prototype.type = 0

    /**
     * 版本yyyy-mm-dd HH:MM:SS
     * @member {string} dataVersion
     * @memberof bysproto.BDataUpdate
     * @instance
     */
    BDataUpdate.prototype.dataVersion = ''

    /**
     * 服务器地址  xxx.xxx.xxx.xxx:xxxx   xxxxxx.com:xxxx
     * @member {string} addr
     * @memberof bysproto.BDataUpdate
     * @instance
     */
    BDataUpdate.prototype.addr = ''

    /**
     * 定时上报基准时间HH:MM:SS
     * @member {string} timerbase
     * @memberof bysproto.BDataUpdate
     * @instance
     */
    BDataUpdate.prototype.timerbase = ''

    /**
     * timer 6h、8h、12h、24h
     * @member {number} timer
     * @memberof bysproto.BDataUpdate
     * @instance
     */
    BDataUpdate.prototype.timer = 0

    /**
     * 姿态报警阈值
     * @member {number} attitude
     * @memberof bysproto.BDataUpdate
     * @instance
     */
    BDataUpdate.prototype.attitude = 0

    /**
     * 位移报警阈值
     * @member {number} dirft
     * @memberof bysproto.BDataUpdate
     * @instance
     */
    BDataUpdate.prototype.dirft = 0

    /**
     * 震动报警阈值
     * @member {bysproto.IBVamp|null|undefined} vibration
     * @memberof bysproto.BDataUpdate
     * @instance
     */
    BDataUpdate.prototype.vibration = null

    /**
     * 红外报警阈值
     * @member {number} infrared
     * @memberof bysproto.BDataUpdate
     * @instance
     */
    BDataUpdate.prototype.infrared = 0

    /**
     * 延迟休眠时间 10s
     * @member {number} t1
     * @memberof bysproto.BDataUpdate
     * @instance
     */
    BDataUpdate.prototype.t1 = 0

    /**
     * 调试模式时间 120s
     * @member {number} t2
     * @memberof bysproto.BDataUpdate
     * @instance
     */
    BDataUpdate.prototype.t2 = 0

    /**
     * 报警间隔 10s
     * @member {number} t3
     * @memberof bysproto.BDataUpdate
     * @instance
     */
    BDataUpdate.prototype.t3 = 0

    /**
     * 报警次数 10
     * @member {number} n1
     * @memberof bysproto.BDataUpdate
     * @instance
     */
    BDataUpdate.prototype.n1 = 0

    /**
     * Encodes the specified BDataUpdate message. Does not implicitly {@link bysproto.BDataUpdate.verify|verify} messages.
     * @function encode
     * @memberof bysproto.BDataUpdate
     * @static
     * @param {bysproto.IBDataUpdate} message BDataUpdate message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    BDataUpdate.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (
        message.deviceID != null &&
        Object.hasOwnProperty.call(message, 'deviceID')
      )
        writer.uint32(/* id 1, wireType 0 =*/ 8).uint32(message.deviceID)
      if (message.type != null && Object.hasOwnProperty.call(message, 'type'))
        writer.uint32(/* id 2, wireType 0 =*/ 16).uint32(message.type)
      if (
        message.dataVersion != null &&
        Object.hasOwnProperty.call(message, 'dataVersion')
      )
        writer.uint32(/* id 3, wireType 2 =*/ 26).string(message.dataVersion)
      if (message.addr != null && Object.hasOwnProperty.call(message, 'addr'))
        writer.uint32(/* id 4, wireType 2 =*/ 34).string(message.addr)
      if (
        message.timerbase != null &&
        Object.hasOwnProperty.call(message, 'timerbase')
      )
        writer.uint32(/* id 5, wireType 2 =*/ 42).string(message.timerbase)
      if (message.timer != null && Object.hasOwnProperty.call(message, 'timer'))
        writer.uint32(/* id 6, wireType 0 =*/ 48).uint32(message.timer)
      if (
        message.attitude != null &&
        Object.hasOwnProperty.call(message, 'attitude')
      )
        writer.uint32(/* id 7, wireType 0 =*/ 56).uint32(message.attitude)
      if (message.dirft != null && Object.hasOwnProperty.call(message, 'dirft'))
        writer.uint32(/* id 8, wireType 0 =*/ 64).uint32(message.dirft)
      if (
        message.vibration != null &&
        Object.hasOwnProperty.call(message, 'vibration')
      )
        $root.bysproto.BVamp.encode(
          message.vibration,
          writer.uint32(/* id 9, wireType 2 =*/ 74).fork(),
        ).ldelim()
      if (
        message.infrared != null &&
        Object.hasOwnProperty.call(message, 'infrared')
      )
        writer.uint32(/* id 10, wireType 0 =*/ 80).uint32(message.infrared)
      if (message.t1 != null && Object.hasOwnProperty.call(message, 't1'))
        writer.uint32(/* id 11, wireType 0 =*/ 88).uint32(message.t1)
      if (message.t2 != null && Object.hasOwnProperty.call(message, 't2'))
        writer.uint32(/* id 12, wireType 0 =*/ 96).uint32(message.t2)
      if (message.t3 != null && Object.hasOwnProperty.call(message, 't3'))
        writer.uint32(/* id 13, wireType 0 =*/ 104).uint32(message.t3)
      if (message.n1 != null && Object.hasOwnProperty.call(message, 'n1'))
        writer.uint32(/* id 14, wireType 0 =*/ 112).uint32(message.n1)
      return writer
    }

    /**
     * Decodes a BDataUpdate message from the specified reader or buffer.
     * @function decode
     * @memberof bysproto.BDataUpdate
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {bysproto.BDataUpdate} BDataUpdate
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    BDataUpdate.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.bysproto.BDataUpdate()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.deviceID = reader.uint32()
            break
          case 2:
            message.type = reader.uint32()
            break
          case 3:
            message.dataVersion = reader.string()
            break
          case 4:
            message.addr = reader.string()
            break
          case 5:
            message.timerbase = reader.string()
            break
          case 6:
            message.timer = reader.uint32()
            break
          case 7:
            message.attitude = reader.uint32()
            break
          case 8:
            message.dirft = reader.uint32()
            break
          case 9:
            message.vibration = $root.bysproto.BVamp.decode(
              reader,
              reader.uint32(),
            )
            break
          case 10:
            message.infrared = reader.uint32()
            break
          case 11:
            message.t1 = reader.uint32()
            break
          case 12:
            message.t2 = reader.uint32()
            break
          case 13:
            message.t3 = reader.uint32()
            break
          case 14:
            message.n1 = reader.uint32()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return BDataUpdate
  })()

  bysproto.BShutDown = (function () {
    /**
     * Properties of a BShutDown.
     * @memberof bysproto
     * @interface IBShutDown
     * @property {number|null} [deviceID] 目标设备编号
     * @property {number|null} [type] 0:遥活 1:遥闭  2:遥晕
     * @property {number|null} [camType] 0:遥活 1:遥闭  2:遥晕
     */

    /**
     * Constructs a new BShutDown.
     * @memberof bysproto
     * @classdesc 遥闭
     * bmsg.cmd=27
     * @implements IBShutDown
     * @constructor
     * @param {bysproto.IBShutDown=} [properties] Properties to set
     */
    function BShutDown(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 目标设备编号
     * @member {number} deviceID
     * @memberof bysproto.BShutDown
     * @instance
     */
    BShutDown.prototype.deviceID = 0

    /**
     * 0:遥活 1:遥闭  2:遥晕
     * @member {number} type
     * @memberof bysproto.BShutDown
     * @instance
     */
    BShutDown.prototype.type = 0

    /**
     * 0:遥活 1:遥闭  2:遥晕
     * @member {number} camType
     * @memberof bysproto.BShutDown
     * @instance
     */
    BShutDown.prototype.camType = 0

    /**
     * Encodes the specified BShutDown message. Does not implicitly {@link bysproto.BShutDown.verify|verify} messages.
     * @function encode
     * @memberof bysproto.BShutDown
     * @static
     * @param {bysproto.IBShutDown} message BShutDown message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    BShutDown.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (
        message.deviceID != null &&
        Object.hasOwnProperty.call(message, 'deviceID')
      )
        writer.uint32(/* id 1, wireType 0 =*/ 8).uint32(message.deviceID)
      if (message.type != null && Object.hasOwnProperty.call(message, 'type'))
        writer.uint32(/* id 2, wireType 0 =*/ 16).uint32(message.type)
      if (
        message.camType != null &&
        Object.hasOwnProperty.call(message, 'camType')
      )
        writer.uint32(/* id 3, wireType 0 =*/ 24).uint32(message.camType)
      return writer
    }

    /**
     * Decodes a BShutDown message from the specified reader or buffer.
     * @function decode
     * @memberof bysproto.BShutDown
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {bysproto.BShutDown} BShutDown
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    BShutDown.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.bysproto.BShutDown()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.deviceID = reader.uint32()
            break
          case 2:
            message.type = reader.uint32()
            break
          case 3:
            message.camType = reader.uint32()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return BShutDown
  })()

  return bysproto
})())

export { $root as default }

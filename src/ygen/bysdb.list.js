/*eslint-disable block-scoped-var, id-length, no-control-regex, no-magic-numbers, no-prototype-builtins, no-redeclare, no-shadow, no-var, sort-vars*/
import * as $protobuf from 'protobufjs/minimal'

// Common aliases
const $Reader = $protobuf.Reader,
  $Writer = $protobuf.Writer,
  $util = $protobuf.util

// Exported root namespace
const $root = $protobuf.roots['default'] || ($protobuf.roots['default'] = {})

export const bysdb = ($root.bysdb = (() => {
  /**
   * Namespace bysdb.
   * @exports bysdb
   * @namespace
   */
  const bysdb = $root.bysdb || {}

  bysdb.DbControllerList = (function () {
    /**
     * Properties of a DbControllerList.
     * @memberof bysdb
     * @interface IDbControllerList
     * @property {number|null} [BatchNo] 批次号，从0开始
     * @property {number|null} [RowsOffset] rows offset,从0开始
     * @property {Array.<bysdb.IDbController>|null} [Rows] 本批次数据
     */

    /**
     * Constructs a new DbControllerList.
     * @memberof bysdb
     * @classdesc DbController list
     * @implements IDbControllerList
     * @constructor
     * @param {bysdb.IDbControllerList=} [properties] Properties to set
     */
    function DbControllerList(properties) {
      this.Rows = []
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 批次号，从0开始
     * @member {number} BatchNo
     * @memberof bysdb.DbControllerList
     * @instance
     */
    DbControllerList.prototype.BatchNo = 0

    /**
     * rows offset,从0开始
     * @member {number} RowsOffset
     * @memberof bysdb.DbControllerList
     * @instance
     */
    DbControllerList.prototype.RowsOffset = 0

    /**
     * 本批次数据
     * @member {Array.<bysdb.IDbController>} Rows
     * @memberof bysdb.DbControllerList
     * @instance
     */
    DbControllerList.prototype.Rows = $util.emptyArray

    /**
     * Encodes the specified DbControllerList message. Does not implicitly {@link bysdb.DbControllerList.verify|verify} messages.
     * @function encode
     * @memberof bysdb.DbControllerList
     * @static
     * @param {bysdb.IDbControllerList} message DbControllerList message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    DbControllerList.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (
        message.BatchNo != null &&
        Object.hasOwnProperty.call(message, 'BatchNo')
      )
        writer.uint32(/* id 1, wireType 0 =*/ 8).uint32(message.BatchNo)
      if (
        message.RowsOffset != null &&
        Object.hasOwnProperty.call(message, 'RowsOffset')
      )
        writer.uint32(/* id 2, wireType 0 =*/ 16).uint32(message.RowsOffset)
      if (message.Rows != null && message.Rows.length)
        for (let i = 0; i < message.Rows.length; ++i)
          $root.bysdb.DbController.encode(
            message.Rows[i],
            writer.uint32(/* id 3, wireType 2 =*/ 26).fork(),
          ).ldelim()
      return writer
    }

    /**
     * Decodes a DbControllerList message from the specified reader or buffer.
     * @function decode
     * @memberof bysdb.DbControllerList
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {bysdb.DbControllerList} DbControllerList
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    DbControllerList.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.bysdb.DbControllerList()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.BatchNo = reader.uint32()
            break
          case 2:
            message.RowsOffset = reader.uint32()
            break
          case 3:
            if (!(message.Rows && message.Rows.length)) message.Rows = []
            message.Rows.push(
              $root.bysdb.DbController.decode(reader, reader.uint32()),
            )
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return DbControllerList
  })()

  bysdb.DbBysMarkerList = (function () {
    /**
     * Properties of a DbBysMarkerList.
     * @memberof bysdb
     * @interface IDbBysMarkerList
     * @property {number|null} [BatchNo] 批次号，从0开始
     * @property {number|null} [RowsOffset] rows offset,从0开始
     * @property {Array.<bysdb.IDbBysMarker>|null} [Rows] 本批次数据
     */

    /**
     * Constructs a new DbBysMarkerList.
     * @memberof bysdb
     * @classdesc DbBysMarker list
     * @implements IDbBysMarkerList
     * @constructor
     * @param {bysdb.IDbBysMarkerList=} [properties] Properties to set
     */
    function DbBysMarkerList(properties) {
      this.Rows = []
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 批次号，从0开始
     * @member {number} BatchNo
     * @memberof bysdb.DbBysMarkerList
     * @instance
     */
    DbBysMarkerList.prototype.BatchNo = 0

    /**
     * rows offset,从0开始
     * @member {number} RowsOffset
     * @memberof bysdb.DbBysMarkerList
     * @instance
     */
    DbBysMarkerList.prototype.RowsOffset = 0

    /**
     * 本批次数据
     * @member {Array.<bysdb.IDbBysMarker>} Rows
     * @memberof bysdb.DbBysMarkerList
     * @instance
     */
    DbBysMarkerList.prototype.Rows = $util.emptyArray

    /**
     * Encodes the specified DbBysMarkerList message. Does not implicitly {@link bysdb.DbBysMarkerList.verify|verify} messages.
     * @function encode
     * @memberof bysdb.DbBysMarkerList
     * @static
     * @param {bysdb.IDbBysMarkerList} message DbBysMarkerList message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    DbBysMarkerList.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (
        message.BatchNo != null &&
        Object.hasOwnProperty.call(message, 'BatchNo')
      )
        writer.uint32(/* id 1, wireType 0 =*/ 8).uint32(message.BatchNo)
      if (
        message.RowsOffset != null &&
        Object.hasOwnProperty.call(message, 'RowsOffset')
      )
        writer.uint32(/* id 2, wireType 0 =*/ 16).uint32(message.RowsOffset)
      if (message.Rows != null && message.Rows.length)
        for (let i = 0; i < message.Rows.length; ++i)
          $root.bysdb.DbBysMarker.encode(
            message.Rows[i],
            writer.uint32(/* id 3, wireType 2 =*/ 26).fork(),
          ).ldelim()
      return writer
    }

    /**
     * Decodes a DbBysMarkerList message from the specified reader or buffer.
     * @function decode
     * @memberof bysdb.DbBysMarkerList
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {bysdb.DbBysMarkerList} DbBysMarkerList
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    DbBysMarkerList.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.bysdb.DbBysMarkerList()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.BatchNo = reader.uint32()
            break
          case 2:
            message.RowsOffset = reader.uint32()
            break
          case 3:
            if (!(message.Rows && message.Rows.length)) message.Rows = []
            message.Rows.push(
              $root.bysdb.DbBysMarker.decode(reader, reader.uint32()),
            )
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return DbBysMarkerList
  })()

  bysdb.DbControllerOnlineHistoryList = (function () {
    /**
     * Properties of a DbControllerOnlineHistoryList.
     * @memberof bysdb
     * @interface IDbControllerOnlineHistoryList
     * @property {number|null} [BatchNo] 批次号，从0开始
     * @property {number|null} [RowsOffset] rows offset,从0开始
     * @property {Array.<bysdb.IDbControllerOnlineHistory>|null} [Rows] 本批次数据
     */

    /**
     * Constructs a new DbControllerOnlineHistoryList.
     * @memberof bysdb
     * @classdesc DbControllerOnlineHistory list
     * @implements IDbControllerOnlineHistoryList
     * @constructor
     * @param {bysdb.IDbControllerOnlineHistoryList=} [properties] Properties to set
     */
    function DbControllerOnlineHistoryList(properties) {
      this.Rows = []
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 批次号，从0开始
     * @member {number} BatchNo
     * @memberof bysdb.DbControllerOnlineHistoryList
     * @instance
     */
    DbControllerOnlineHistoryList.prototype.BatchNo = 0

    /**
     * rows offset,从0开始
     * @member {number} RowsOffset
     * @memberof bysdb.DbControllerOnlineHistoryList
     * @instance
     */
    DbControllerOnlineHistoryList.prototype.RowsOffset = 0

    /**
     * 本批次数据
     * @member {Array.<bysdb.IDbControllerOnlineHistory>} Rows
     * @memberof bysdb.DbControllerOnlineHistoryList
     * @instance
     */
    DbControllerOnlineHistoryList.prototype.Rows = $util.emptyArray

    /**
     * Encodes the specified DbControllerOnlineHistoryList message. Does not implicitly {@link bysdb.DbControllerOnlineHistoryList.verify|verify} messages.
     * @function encode
     * @memberof bysdb.DbControllerOnlineHistoryList
     * @static
     * @param {bysdb.IDbControllerOnlineHistoryList} message DbControllerOnlineHistoryList message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    DbControllerOnlineHistoryList.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (
        message.BatchNo != null &&
        Object.hasOwnProperty.call(message, 'BatchNo')
      )
        writer.uint32(/* id 1, wireType 0 =*/ 8).uint32(message.BatchNo)
      if (
        message.RowsOffset != null &&
        Object.hasOwnProperty.call(message, 'RowsOffset')
      )
        writer.uint32(/* id 2, wireType 0 =*/ 16).uint32(message.RowsOffset)
      if (message.Rows != null && message.Rows.length)
        for (let i = 0; i < message.Rows.length; ++i)
          $root.bysdb.DbControllerOnlineHistory.encode(
            message.Rows[i],
            writer.uint32(/* id 3, wireType 2 =*/ 26).fork(),
          ).ldelim()
      return writer
    }

    /**
     * Decodes a DbControllerOnlineHistoryList message from the specified reader or buffer.
     * @function decode
     * @memberof bysdb.DbControllerOnlineHistoryList
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {bysdb.DbControllerOnlineHistoryList} DbControllerOnlineHistoryList
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    DbControllerOnlineHistoryList.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.bysdb.DbControllerOnlineHistoryList()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.BatchNo = reader.uint32()
            break
          case 2:
            message.RowsOffset = reader.uint32()
            break
          case 3:
            if (!(message.Rows && message.Rows.length)) message.Rows = []
            message.Rows.push(
              $root.bysdb.DbControllerOnlineHistory.decode(
                reader,
                reader.uint32(),
              ),
            )
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return DbControllerOnlineHistoryList
  })()

  bysdb.DbMediaInfoList = (function () {
    /**
     * Properties of a DbMediaInfoList.
     * @memberof bysdb
     * @interface IDbMediaInfoList
     * @property {number|null} [BatchNo] 批次号，从0开始
     * @property {number|null} [RowsOffset] rows offset,从0开始
     * @property {Array.<bysdb.IDbMediaInfo>|null} [Rows] 本批次数据
     */

    /**
     * Constructs a new DbMediaInfoList.
     * @memberof bysdb
     * @classdesc DbMediaInfo list
     * @implements IDbMediaInfoList
     * @constructor
     * @param {bysdb.IDbMediaInfoList=} [properties] Properties to set
     */
    function DbMediaInfoList(properties) {
      this.Rows = []
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 批次号，从0开始
     * @member {number} BatchNo
     * @memberof bysdb.DbMediaInfoList
     * @instance
     */
    DbMediaInfoList.prototype.BatchNo = 0

    /**
     * rows offset,从0开始
     * @member {number} RowsOffset
     * @memberof bysdb.DbMediaInfoList
     * @instance
     */
    DbMediaInfoList.prototype.RowsOffset = 0

    /**
     * 本批次数据
     * @member {Array.<bysdb.IDbMediaInfo>} Rows
     * @memberof bysdb.DbMediaInfoList
     * @instance
     */
    DbMediaInfoList.prototype.Rows = $util.emptyArray

    /**
     * Encodes the specified DbMediaInfoList message. Does not implicitly {@link bysdb.DbMediaInfoList.verify|verify} messages.
     * @function encode
     * @memberof bysdb.DbMediaInfoList
     * @static
     * @param {bysdb.IDbMediaInfoList} message DbMediaInfoList message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    DbMediaInfoList.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (
        message.BatchNo != null &&
        Object.hasOwnProperty.call(message, 'BatchNo')
      )
        writer.uint32(/* id 1, wireType 0 =*/ 8).uint32(message.BatchNo)
      if (
        message.RowsOffset != null &&
        Object.hasOwnProperty.call(message, 'RowsOffset')
      )
        writer.uint32(/* id 2, wireType 0 =*/ 16).uint32(message.RowsOffset)
      if (message.Rows != null && message.Rows.length)
        for (let i = 0; i < message.Rows.length; ++i)
          $root.bysdb.DbMediaInfo.encode(
            message.Rows[i],
            writer.uint32(/* id 3, wireType 2 =*/ 26).fork(),
          ).ldelim()
      return writer
    }

    /**
     * Decodes a DbMediaInfoList message from the specified reader or buffer.
     * @function decode
     * @memberof bysdb.DbMediaInfoList
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {bysdb.DbMediaInfoList} DbMediaInfoList
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    DbMediaInfoList.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.bysdb.DbMediaInfoList()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.BatchNo = reader.uint32()
            break
          case 2:
            message.RowsOffset = reader.uint32()
            break
          case 3:
            if (!(message.Rows && message.Rows.length)) message.Rows = []
            message.Rows.push(
              $root.bysdb.DbMediaInfo.decode(reader, reader.uint32()),
            )
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return DbMediaInfoList
  })()

  bysdb.DbMarkerHistoryList = (function () {
    /**
     * Properties of a DbMarkerHistoryList.
     * @memberof bysdb
     * @interface IDbMarkerHistoryList
     * @property {number|null} [BatchNo] 批次号，从0开始
     * @property {number|null} [RowsOffset] rows offset,从0开始
     * @property {Array.<bysdb.IDbMarkerHistory>|null} [Rows] 本批次数据
     */

    /**
     * Constructs a new DbMarkerHistoryList.
     * @memberof bysdb
     * @classdesc DbMarkerHistory list
     * @implements IDbMarkerHistoryList
     * @constructor
     * @param {bysdb.IDbMarkerHistoryList=} [properties] Properties to set
     */
    function DbMarkerHistoryList(properties) {
      this.Rows = []
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 批次号，从0开始
     * @member {number} BatchNo
     * @memberof bysdb.DbMarkerHistoryList
     * @instance
     */
    DbMarkerHistoryList.prototype.BatchNo = 0

    /**
     * rows offset,从0开始
     * @member {number} RowsOffset
     * @memberof bysdb.DbMarkerHistoryList
     * @instance
     */
    DbMarkerHistoryList.prototype.RowsOffset = 0

    /**
     * 本批次数据
     * @member {Array.<bysdb.IDbMarkerHistory>} Rows
     * @memberof bysdb.DbMarkerHistoryList
     * @instance
     */
    DbMarkerHistoryList.prototype.Rows = $util.emptyArray

    /**
     * Encodes the specified DbMarkerHistoryList message. Does not implicitly {@link bysdb.DbMarkerHistoryList.verify|verify} messages.
     * @function encode
     * @memberof bysdb.DbMarkerHistoryList
     * @static
     * @param {bysdb.IDbMarkerHistoryList} message DbMarkerHistoryList message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    DbMarkerHistoryList.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (
        message.BatchNo != null &&
        Object.hasOwnProperty.call(message, 'BatchNo')
      )
        writer.uint32(/* id 1, wireType 0 =*/ 8).uint32(message.BatchNo)
      if (
        message.RowsOffset != null &&
        Object.hasOwnProperty.call(message, 'RowsOffset')
      )
        writer.uint32(/* id 2, wireType 0 =*/ 16).uint32(message.RowsOffset)
      if (message.Rows != null && message.Rows.length)
        for (let i = 0; i < message.Rows.length; ++i)
          $root.bysdb.DbMarkerHistory.encode(
            message.Rows[i],
            writer.uint32(/* id 3, wireType 2 =*/ 26).fork(),
          ).ldelim()
      return writer
    }

    /**
     * Decodes a DbMarkerHistoryList message from the specified reader or buffer.
     * @function decode
     * @memberof bysdb.DbMarkerHistoryList
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {bysdb.DbMarkerHistoryList} DbMarkerHistoryList
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    DbMarkerHistoryList.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.bysdb.DbMarkerHistoryList()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.BatchNo = reader.uint32()
            break
          case 2:
            message.RowsOffset = reader.uint32()
            break
          case 3:
            if (!(message.Rows && message.Rows.length)) message.Rows = []
            message.Rows.push(
              $root.bysdb.DbMarkerHistory.decode(reader, reader.uint32()),
            )
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return DbMarkerHistoryList
  })()

  bysdb.DbMarkerPatrolHistoryList = (function () {
    /**
     * Properties of a DbMarkerPatrolHistoryList.
     * @memberof bysdb
     * @interface IDbMarkerPatrolHistoryList
     * @property {number|null} [BatchNo] 批次号，从0开始
     * @property {number|null} [RowsOffset] rows offset,从0开始
     * @property {Array.<bysdb.IDbMarkerPatrolHistory>|null} [Rows] 本批次数据
     */

    /**
     * Constructs a new DbMarkerPatrolHistoryList.
     * @memberof bysdb
     * @classdesc DbMarkerPatrolHistory list
     * @implements IDbMarkerPatrolHistoryList
     * @constructor
     * @param {bysdb.IDbMarkerPatrolHistoryList=} [properties] Properties to set
     */
    function DbMarkerPatrolHistoryList(properties) {
      this.Rows = []
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 批次号，从0开始
     * @member {number} BatchNo
     * @memberof bysdb.DbMarkerPatrolHistoryList
     * @instance
     */
    DbMarkerPatrolHistoryList.prototype.BatchNo = 0

    /**
     * rows offset,从0开始
     * @member {number} RowsOffset
     * @memberof bysdb.DbMarkerPatrolHistoryList
     * @instance
     */
    DbMarkerPatrolHistoryList.prototype.RowsOffset = 0

    /**
     * 本批次数据
     * @member {Array.<bysdb.IDbMarkerPatrolHistory>} Rows
     * @memberof bysdb.DbMarkerPatrolHistoryList
     * @instance
     */
    DbMarkerPatrolHistoryList.prototype.Rows = $util.emptyArray

    /**
     * Encodes the specified DbMarkerPatrolHistoryList message. Does not implicitly {@link bysdb.DbMarkerPatrolHistoryList.verify|verify} messages.
     * @function encode
     * @memberof bysdb.DbMarkerPatrolHistoryList
     * @static
     * @param {bysdb.IDbMarkerPatrolHistoryList} message DbMarkerPatrolHistoryList message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    DbMarkerPatrolHistoryList.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (
        message.BatchNo != null &&
        Object.hasOwnProperty.call(message, 'BatchNo')
      )
        writer.uint32(/* id 1, wireType 0 =*/ 8).uint32(message.BatchNo)
      if (
        message.RowsOffset != null &&
        Object.hasOwnProperty.call(message, 'RowsOffset')
      )
        writer.uint32(/* id 2, wireType 0 =*/ 16).uint32(message.RowsOffset)
      if (message.Rows != null && message.Rows.length)
        for (let i = 0; i < message.Rows.length; ++i)
          $root.bysdb.DbMarkerPatrolHistory.encode(
            message.Rows[i],
            writer.uint32(/* id 3, wireType 2 =*/ 26).fork(),
          ).ldelim()
      return writer
    }

    /**
     * Decodes a DbMarkerPatrolHistoryList message from the specified reader or buffer.
     * @function decode
     * @memberof bysdb.DbMarkerPatrolHistoryList
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {bysdb.DbMarkerPatrolHistoryList} DbMarkerPatrolHistoryList
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    DbMarkerPatrolHistoryList.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.bysdb.DbMarkerPatrolHistoryList()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.BatchNo = reader.uint32()
            break
          case 2:
            message.RowsOffset = reader.uint32()
            break
          case 3:
            if (!(message.Rows && message.Rows.length)) message.Rows = []
            message.Rows.push(
              $root.bysdb.DbMarkerPatrolHistory.decode(reader, reader.uint32()),
            )
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return DbMarkerPatrolHistoryList
  })()

  bysdb.DbNFCPatrolLineList = (function () {
    /**
     * Properties of a DbNFCPatrolLineList.
     * @memberof bysdb
     * @interface IDbNFCPatrolLineList
     * @property {number|null} [BatchNo] 批次号，从0开始
     * @property {number|null} [RowsOffset] rows offset,从0开始
     * @property {Array.<bysdb.IDbNFCPatrolLine>|null} [Rows] 本批次数据
     */

    /**
     * Constructs a new DbNFCPatrolLineList.
     * @memberof bysdb
     * @classdesc DbNFCPatrolLine list
     * @implements IDbNFCPatrolLineList
     * @constructor
     * @param {bysdb.IDbNFCPatrolLineList=} [properties] Properties to set
     */
    function DbNFCPatrolLineList(properties) {
      this.Rows = []
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 批次号，从0开始
     * @member {number} BatchNo
     * @memberof bysdb.DbNFCPatrolLineList
     * @instance
     */
    DbNFCPatrolLineList.prototype.BatchNo = 0

    /**
     * rows offset,从0开始
     * @member {number} RowsOffset
     * @memberof bysdb.DbNFCPatrolLineList
     * @instance
     */
    DbNFCPatrolLineList.prototype.RowsOffset = 0

    /**
     * 本批次数据
     * @member {Array.<bysdb.IDbNFCPatrolLine>} Rows
     * @memberof bysdb.DbNFCPatrolLineList
     * @instance
     */
    DbNFCPatrolLineList.prototype.Rows = $util.emptyArray

    /**
     * Encodes the specified DbNFCPatrolLineList message. Does not implicitly {@link bysdb.DbNFCPatrolLineList.verify|verify} messages.
     * @function encode
     * @memberof bysdb.DbNFCPatrolLineList
     * @static
     * @param {bysdb.IDbNFCPatrolLineList} message DbNFCPatrolLineList message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    DbNFCPatrolLineList.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (
        message.BatchNo != null &&
        Object.hasOwnProperty.call(message, 'BatchNo')
      )
        writer.uint32(/* id 1, wireType 0 =*/ 8).uint32(message.BatchNo)
      if (
        message.RowsOffset != null &&
        Object.hasOwnProperty.call(message, 'RowsOffset')
      )
        writer.uint32(/* id 2, wireType 0 =*/ 16).uint32(message.RowsOffset)
      if (message.Rows != null && message.Rows.length)
        for (let i = 0; i < message.Rows.length; ++i)
          $root.bysdb.DbNFCPatrolLine.encode(
            message.Rows[i],
            writer.uint32(/* id 3, wireType 2 =*/ 26).fork(),
          ).ldelim()
      return writer
    }

    /**
     * Decodes a DbNFCPatrolLineList message from the specified reader or buffer.
     * @function decode
     * @memberof bysdb.DbNFCPatrolLineList
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {bysdb.DbNFCPatrolLineList} DbNFCPatrolLineList
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    DbNFCPatrolLineList.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.bysdb.DbNFCPatrolLineList()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.BatchNo = reader.uint32()
            break
          case 2:
            message.RowsOffset = reader.uint32()
            break
          case 3:
            if (!(message.Rows && message.Rows.length)) message.Rows = []
            message.Rows.push(
              $root.bysdb.DbNFCPatrolLine.decode(reader, reader.uint32()),
            )
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return DbNFCPatrolLineList
  })()

  bysdb.DbNFCPatrolLineDetailList = (function () {
    /**
     * Properties of a DbNFCPatrolLineDetailList.
     * @memberof bysdb
     * @interface IDbNFCPatrolLineDetailList
     * @property {number|null} [BatchNo] 批次号，从0开始
     * @property {number|null} [RowsOffset] rows offset,从0开始
     * @property {Array.<bysdb.IDbNFCPatrolLineDetail>|null} [Rows] 本批次数据
     */

    /**
     * Constructs a new DbNFCPatrolLineDetailList.
     * @memberof bysdb
     * @classdesc DbNFCPatrolLineDetail list
     * @implements IDbNFCPatrolLineDetailList
     * @constructor
     * @param {bysdb.IDbNFCPatrolLineDetailList=} [properties] Properties to set
     */
    function DbNFCPatrolLineDetailList(properties) {
      this.Rows = []
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 批次号，从0开始
     * @member {number} BatchNo
     * @memberof bysdb.DbNFCPatrolLineDetailList
     * @instance
     */
    DbNFCPatrolLineDetailList.prototype.BatchNo = 0

    /**
     * rows offset,从0开始
     * @member {number} RowsOffset
     * @memberof bysdb.DbNFCPatrolLineDetailList
     * @instance
     */
    DbNFCPatrolLineDetailList.prototype.RowsOffset = 0

    /**
     * 本批次数据
     * @member {Array.<bysdb.IDbNFCPatrolLineDetail>} Rows
     * @memberof bysdb.DbNFCPatrolLineDetailList
     * @instance
     */
    DbNFCPatrolLineDetailList.prototype.Rows = $util.emptyArray

    /**
     * Encodes the specified DbNFCPatrolLineDetailList message. Does not implicitly {@link bysdb.DbNFCPatrolLineDetailList.verify|verify} messages.
     * @function encode
     * @memberof bysdb.DbNFCPatrolLineDetailList
     * @static
     * @param {bysdb.IDbNFCPatrolLineDetailList} message DbNFCPatrolLineDetailList message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    DbNFCPatrolLineDetailList.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (
        message.BatchNo != null &&
        Object.hasOwnProperty.call(message, 'BatchNo')
      )
        writer.uint32(/* id 1, wireType 0 =*/ 8).uint32(message.BatchNo)
      if (
        message.RowsOffset != null &&
        Object.hasOwnProperty.call(message, 'RowsOffset')
      )
        writer.uint32(/* id 2, wireType 0 =*/ 16).uint32(message.RowsOffset)
      if (message.Rows != null && message.Rows.length)
        for (let i = 0; i < message.Rows.length; ++i)
          $root.bysdb.DbNFCPatrolLineDetail.encode(
            message.Rows[i],
            writer.uint32(/* id 3, wireType 2 =*/ 26).fork(),
          ).ldelim()
      return writer
    }

    /**
     * Decodes a DbNFCPatrolLineDetailList message from the specified reader or buffer.
     * @function decode
     * @memberof bysdb.DbNFCPatrolLineDetailList
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {bysdb.DbNFCPatrolLineDetailList} DbNFCPatrolLineDetailList
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    DbNFCPatrolLineDetailList.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.bysdb.DbNFCPatrolLineDetailList()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.BatchNo = reader.uint32()
            break
          case 2:
            message.RowsOffset = reader.uint32()
            break
          case 3:
            if (!(message.Rows && message.Rows.length)) message.Rows = []
            message.Rows.push(
              $root.bysdb.DbNFCPatrolLineDetail.decode(reader, reader.uint32()),
            )
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return DbNFCPatrolLineDetailList
  })()

  bysdb.DbNFCPatrolLineRulesList = (function () {
    /**
     * Properties of a DbNFCPatrolLineRulesList.
     * @memberof bysdb
     * @interface IDbNFCPatrolLineRulesList
     * @property {number|null} [BatchNo] 批次号，从0开始
     * @property {number|null} [RowsOffset] rows offset,从0开始
     * @property {Array.<bysdb.IDbNFCPatrolLineRules>|null} [Rows] 本批次数据
     */

    /**
     * Constructs a new DbNFCPatrolLineRulesList.
     * @memberof bysdb
     * @classdesc DbNFCPatrolLineRules list
     * @implements IDbNFCPatrolLineRulesList
     * @constructor
     * @param {bysdb.IDbNFCPatrolLineRulesList=} [properties] Properties to set
     */
    function DbNFCPatrolLineRulesList(properties) {
      this.Rows = []
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 批次号，从0开始
     * @member {number} BatchNo
     * @memberof bysdb.DbNFCPatrolLineRulesList
     * @instance
     */
    DbNFCPatrolLineRulesList.prototype.BatchNo = 0

    /**
     * rows offset,从0开始
     * @member {number} RowsOffset
     * @memberof bysdb.DbNFCPatrolLineRulesList
     * @instance
     */
    DbNFCPatrolLineRulesList.prototype.RowsOffset = 0

    /**
     * 本批次数据
     * @member {Array.<bysdb.IDbNFCPatrolLineRules>} Rows
     * @memberof bysdb.DbNFCPatrolLineRulesList
     * @instance
     */
    DbNFCPatrolLineRulesList.prototype.Rows = $util.emptyArray

    /**
     * Encodes the specified DbNFCPatrolLineRulesList message. Does not implicitly {@link bysdb.DbNFCPatrolLineRulesList.verify|verify} messages.
     * @function encode
     * @memberof bysdb.DbNFCPatrolLineRulesList
     * @static
     * @param {bysdb.IDbNFCPatrolLineRulesList} message DbNFCPatrolLineRulesList message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    DbNFCPatrolLineRulesList.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (
        message.BatchNo != null &&
        Object.hasOwnProperty.call(message, 'BatchNo')
      )
        writer.uint32(/* id 1, wireType 0 =*/ 8).uint32(message.BatchNo)
      if (
        message.RowsOffset != null &&
        Object.hasOwnProperty.call(message, 'RowsOffset')
      )
        writer.uint32(/* id 2, wireType 0 =*/ 16).uint32(message.RowsOffset)
      if (message.Rows != null && message.Rows.length)
        for (let i = 0; i < message.Rows.length; ++i)
          $root.bysdb.DbNFCPatrolLineRules.encode(
            message.Rows[i],
            writer.uint32(/* id 3, wireType 2 =*/ 26).fork(),
          ).ldelim()
      return writer
    }

    /**
     * Decodes a DbNFCPatrolLineRulesList message from the specified reader or buffer.
     * @function decode
     * @memberof bysdb.DbNFCPatrolLineRulesList
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {bysdb.DbNFCPatrolLineRulesList} DbNFCPatrolLineRulesList
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    DbNFCPatrolLineRulesList.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.bysdb.DbNFCPatrolLineRulesList()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.BatchNo = reader.uint32()
            break
          case 2:
            message.RowsOffset = reader.uint32()
            break
          case 3:
            if (!(message.Rows && message.Rows.length)) message.Rows = []
            message.Rows.push(
              $root.bysdb.DbNFCPatrolLineRules.decode(reader, reader.uint32()),
            )
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return DbNFCPatrolLineRulesList
  })()

  bysdb.DbNFCPatrolLineAndRulesList = (function () {
    /**
     * Properties of a DbNFCPatrolLineAndRulesList.
     * @memberof bysdb
     * @interface IDbNFCPatrolLineAndRulesList
     * @property {number|null} [BatchNo] 批次号，从0开始
     * @property {number|null} [RowsOffset] rows offset,从0开始
     * @property {Array.<bysdb.IDbNFCPatrolLineAndRules>|null} [Rows] 本批次数据
     */

    /**
     * Constructs a new DbNFCPatrolLineAndRulesList.
     * @memberof bysdb
     * @classdesc DbNFCPatrolLineAndRules list
     * @implements IDbNFCPatrolLineAndRulesList
     * @constructor
     * @param {bysdb.IDbNFCPatrolLineAndRulesList=} [properties] Properties to set
     */
    function DbNFCPatrolLineAndRulesList(properties) {
      this.Rows = []
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 批次号，从0开始
     * @member {number} BatchNo
     * @memberof bysdb.DbNFCPatrolLineAndRulesList
     * @instance
     */
    DbNFCPatrolLineAndRulesList.prototype.BatchNo = 0

    /**
     * rows offset,从0开始
     * @member {number} RowsOffset
     * @memberof bysdb.DbNFCPatrolLineAndRulesList
     * @instance
     */
    DbNFCPatrolLineAndRulesList.prototype.RowsOffset = 0

    /**
     * 本批次数据
     * @member {Array.<bysdb.IDbNFCPatrolLineAndRules>} Rows
     * @memberof bysdb.DbNFCPatrolLineAndRulesList
     * @instance
     */
    DbNFCPatrolLineAndRulesList.prototype.Rows = $util.emptyArray

    /**
     * Encodes the specified DbNFCPatrolLineAndRulesList message. Does not implicitly {@link bysdb.DbNFCPatrolLineAndRulesList.verify|verify} messages.
     * @function encode
     * @memberof bysdb.DbNFCPatrolLineAndRulesList
     * @static
     * @param {bysdb.IDbNFCPatrolLineAndRulesList} message DbNFCPatrolLineAndRulesList message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    DbNFCPatrolLineAndRulesList.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (
        message.BatchNo != null &&
        Object.hasOwnProperty.call(message, 'BatchNo')
      )
        writer.uint32(/* id 1, wireType 0 =*/ 8).uint32(message.BatchNo)
      if (
        message.RowsOffset != null &&
        Object.hasOwnProperty.call(message, 'RowsOffset')
      )
        writer.uint32(/* id 2, wireType 0 =*/ 16).uint32(message.RowsOffset)
      if (message.Rows != null && message.Rows.length)
        for (let i = 0; i < message.Rows.length; ++i)
          $root.bysdb.DbNFCPatrolLineAndRules.encode(
            message.Rows[i],
            writer.uint32(/* id 3, wireType 2 =*/ 26).fork(),
          ).ldelim()
      return writer
    }

    /**
     * Decodes a DbNFCPatrolLineAndRulesList message from the specified reader or buffer.
     * @function decode
     * @memberof bysdb.DbNFCPatrolLineAndRulesList
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {bysdb.DbNFCPatrolLineAndRulesList} DbNFCPatrolLineAndRulesList
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    DbNFCPatrolLineAndRulesList.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.bysdb.DbNFCPatrolLineAndRulesList()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.BatchNo = reader.uint32()
            break
          case 2:
            message.RowsOffset = reader.uint32()
            break
          case 3:
            if (!(message.Rows && message.Rows.length)) message.Rows = []
            message.Rows.push(
              $root.bysdb.DbNFCPatrolLineAndRules.decode(
                reader,
                reader.uint32(),
              ),
            )
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return DbNFCPatrolLineAndRulesList
  })()

  bysdb.DbMarkerUploadImageHistoryList = (function () {
    /**
     * Properties of a DbMarkerUploadImageHistoryList.
     * @memberof bysdb
     * @interface IDbMarkerUploadImageHistoryList
     * @property {number|null} [BatchNo] 批次号，从0开始
     * @property {number|null} [RowsOffset] rows offset,从0开始
     * @property {Array.<bysdb.IDbMarkerUploadImageHistory>|null} [Rows] 本批次数据
     */

    /**
     * Constructs a new DbMarkerUploadImageHistoryList.
     * @memberof bysdb
     * @classdesc DbMarkerUploadImageHistory list
     * @implements IDbMarkerUploadImageHistoryList
     * @constructor
     * @param {bysdb.IDbMarkerUploadImageHistoryList=} [properties] Properties to set
     */
    function DbMarkerUploadImageHistoryList(properties) {
      this.Rows = []
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 批次号，从0开始
     * @member {number} BatchNo
     * @memberof bysdb.DbMarkerUploadImageHistoryList
     * @instance
     */
    DbMarkerUploadImageHistoryList.prototype.BatchNo = 0

    /**
     * rows offset,从0开始
     * @member {number} RowsOffset
     * @memberof bysdb.DbMarkerUploadImageHistoryList
     * @instance
     */
    DbMarkerUploadImageHistoryList.prototype.RowsOffset = 0

    /**
     * 本批次数据
     * @member {Array.<bysdb.IDbMarkerUploadImageHistory>} Rows
     * @memberof bysdb.DbMarkerUploadImageHistoryList
     * @instance
     */
    DbMarkerUploadImageHistoryList.prototype.Rows = $util.emptyArray

    /**
     * Encodes the specified DbMarkerUploadImageHistoryList message. Does not implicitly {@link bysdb.DbMarkerUploadImageHistoryList.verify|verify} messages.
     * @function encode
     * @memberof bysdb.DbMarkerUploadImageHistoryList
     * @static
     * @param {bysdb.IDbMarkerUploadImageHistoryList} message DbMarkerUploadImageHistoryList message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    DbMarkerUploadImageHistoryList.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (
        message.BatchNo != null &&
        Object.hasOwnProperty.call(message, 'BatchNo')
      )
        writer.uint32(/* id 1, wireType 0 =*/ 8).uint32(message.BatchNo)
      if (
        message.RowsOffset != null &&
        Object.hasOwnProperty.call(message, 'RowsOffset')
      )
        writer.uint32(/* id 2, wireType 0 =*/ 16).uint32(message.RowsOffset)
      if (message.Rows != null && message.Rows.length)
        for (let i = 0; i < message.Rows.length; ++i)
          $root.bysdb.DbMarkerUploadImageHistory.encode(
            message.Rows[i],
            writer.uint32(/* id 3, wireType 2 =*/ 26).fork(),
          ).ldelim()
      return writer
    }

    /**
     * Decodes a DbMarkerUploadImageHistoryList message from the specified reader or buffer.
     * @function decode
     * @memberof bysdb.DbMarkerUploadImageHistoryList
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {bysdb.DbMarkerUploadImageHistoryList} DbMarkerUploadImageHistoryList
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    DbMarkerUploadImageHistoryList.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.bysdb.DbMarkerUploadImageHistoryList()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.BatchNo = reader.uint32()
            break
          case 2:
            message.RowsOffset = reader.uint32()
            break
          case 3:
            if (!(message.Rows && message.Rows.length)) message.Rows = []
            message.Rows.push(
              $root.bysdb.DbMarkerUploadImageHistory.decode(
                reader,
                reader.uint32(),
              ),
            )
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return DbMarkerUploadImageHistoryList
  })()

  bysdb.DbController = (function () {
    /**
     * Properties of a DbController.
     * @memberof bysdb
     * @interface IDbController
     * @property {string|null} [RID] @db uuid primary key
     * 行ID
     * @property {string|null} [OrgRID] @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 控制器所属的群组
     * @property {string|null} [ControllerNo] @db varchar(16) not null unique
     * controller编号
     * @property {string|null} [ControllerDescription] @db text
     * controller描述信息
     * @property {number|null} [ControllerType] @db int
     * 控制器类型 1:中继控制器  2:基站控制器
     * @property {string|null} [ParentRID] @db uuid
     * 上级控制器RID, 可以没有，中继控制器一般都有
     * @property {number|null} [Lon] @db double precision
     * 经度
     * @property {number|null} [Lat] @db double precision
     * 纬度
     * @property {string|null} [Setting] @db jsonb not null default  '{}'::jsonb
     * 其它设置，可以在里面扩展需要用到的其它信息
     * @property {number|null} [ControllerHWID] @db int not null unique
     * 控制器硬件ID，int32 > 0
     * @property {number|null} [ChannelCount] @db int not null default 1
     * 控制器可用信道数，中继控制器为1,基站按实际情况
     * @property {number|null} [MapShowLevel] @db int default 12
     * 地图开始显示级别
     * @property {string|null} [UpdatedAt] @db timestamp not null default now_utc()
     * 数据最后修改时间
     * @property {string|null} [UpdatedDC] @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     * @property {number|null} [DefaultNetworkType] @db int
     * 默认通讯方式， 1:有线网络， 2:4g网络 3:无线FSK
     * @property {number|null} [ParentChannelNo] @db int
     * 中继对应的上级控制器的通道
     */

    /**
     * Constructs a new DbController.
     * @memberof bysdb
     * @classdesc 控制器信息表
     * @rpc crud pcrud
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbControllerOrgRID on DbController USING hash(OrgRID);
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbControllerParentRID on DbController USING hash(ParentRID);
     * @dbpost CREATE UNIQUE INDEX IF NOT EXISTS uidxDbControllerParent ON DbController (ParentRID, ParentChannelNo) WHERE ControllerType=1;
     * @implements IDbController
     * @constructor
     * @param {bysdb.IDbController=} [properties] Properties to set
     */
    function DbController(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * @db uuid primary key
     * 行ID
     * @member {string} RID
     * @memberof bysdb.DbController
     * @instance
     */
    DbController.prototype.RID = ''

    /**
     * @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 控制器所属的群组
     * @member {string} OrgRID
     * @memberof bysdb.DbController
     * @instance
     */
    DbController.prototype.OrgRID = ''

    /**
     * @db varchar(16) not null unique
     * controller编号
     * @member {string} ControllerNo
     * @memberof bysdb.DbController
     * @instance
     */
    DbController.prototype.ControllerNo = ''

    /**
     * @db text
     * controller描述信息
     * @member {string} ControllerDescription
     * @memberof bysdb.DbController
     * @instance
     */
    DbController.prototype.ControllerDescription = ''

    /**
     * @db int
     * 控制器类型 1:中继控制器  2:基站控制器
     * @member {number} ControllerType
     * @memberof bysdb.DbController
     * @instance
     */
    DbController.prototype.ControllerType = 0

    /**
     * @db uuid
     * 上级控制器RID, 可以没有，中继控制器一般都有
     * @member {string} ParentRID
     * @memberof bysdb.DbController
     * @instance
     */
    DbController.prototype.ParentRID = ''

    /**
     * @db double precision
     * 经度
     * @member {number} Lon
     * @memberof bysdb.DbController
     * @instance
     */
    DbController.prototype.Lon = 0

    /**
     * @db double precision
     * 纬度
     * @member {number} Lat
     * @memberof bysdb.DbController
     * @instance
     */
    DbController.prototype.Lat = 0

    /**
     * @db jsonb not null default  '{}'::jsonb
     * 其它设置，可以在里面扩展需要用到的其它信息
     * @member {string} Setting
     * @memberof bysdb.DbController
     * @instance
     */
    DbController.prototype.Setting = ''

    /**
     * @db int not null unique
     * 控制器硬件ID，int32 > 0
     * @member {number} ControllerHWID
     * @memberof bysdb.DbController
     * @instance
     */
    DbController.prototype.ControllerHWID = 0

    /**
     * @db int not null default 1
     * 控制器可用信道数，中继控制器为1,基站按实际情况
     * @member {number} ChannelCount
     * @memberof bysdb.DbController
     * @instance
     */
    DbController.prototype.ChannelCount = 0

    /**
     * @db int default 12
     * 地图开始显示级别
     * @member {number} MapShowLevel
     * @memberof bysdb.DbController
     * @instance
     */
    DbController.prototype.MapShowLevel = 0

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     * @member {string} UpdatedAt
     * @memberof bysdb.DbController
     * @instance
     */
    DbController.prototype.UpdatedAt = ''

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     * @member {string} UpdatedDC
     * @memberof bysdb.DbController
     * @instance
     */
    DbController.prototype.UpdatedDC = ''

    /**
     * @db int
     * 默认通讯方式， 1:有线网络， 2:4g网络 3:无线FSK
     * @member {number} DefaultNetworkType
     * @memberof bysdb.DbController
     * @instance
     */
    DbController.prototype.DefaultNetworkType = 0

    /**
     * @db int
     * 中继对应的上级控制器的通道
     * @member {number} ParentChannelNo
     * @memberof bysdb.DbController
     * @instance
     */
    DbController.prototype.ParentChannelNo = 0

    /**
     * Encodes the specified DbController message. Does not implicitly {@link bysdb.DbController.verify|verify} messages.
     * @function encode
     * @memberof bysdb.DbController
     * @static
     * @param {bysdb.IDbController} message DbController message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    DbController.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (message.RID != null && Object.hasOwnProperty.call(message, 'RID'))
        writer.uint32(/* id 1, wireType 2 =*/ 10).string(message.RID)
      if (
        message.OrgRID != null &&
        Object.hasOwnProperty.call(message, 'OrgRID')
      )
        writer.uint32(/* id 2, wireType 2 =*/ 18).string(message.OrgRID)
      if (
        message.ControllerNo != null &&
        Object.hasOwnProperty.call(message, 'ControllerNo')
      )
        writer.uint32(/* id 3, wireType 2 =*/ 26).string(message.ControllerNo)
      if (
        message.ControllerDescription != null &&
        Object.hasOwnProperty.call(message, 'ControllerDescription')
      )
        writer
          .uint32(/* id 4, wireType 2 =*/ 34)
          .string(message.ControllerDescription)
      if (
        message.ControllerType != null &&
        Object.hasOwnProperty.call(message, 'ControllerType')
      )
        writer.uint32(/* id 5, wireType 0 =*/ 40).int32(message.ControllerType)
      if (
        message.ParentRID != null &&
        Object.hasOwnProperty.call(message, 'ParentRID')
      )
        writer.uint32(/* id 6, wireType 2 =*/ 50).string(message.ParentRID)
      if (message.Lon != null && Object.hasOwnProperty.call(message, 'Lon'))
        writer.uint32(/* id 7, wireType 1 =*/ 57).double(message.Lon)
      if (message.Lat != null && Object.hasOwnProperty.call(message, 'Lat'))
        writer.uint32(/* id 8, wireType 1 =*/ 65).double(message.Lat)
      if (
        message.Setting != null &&
        Object.hasOwnProperty.call(message, 'Setting')
      )
        writer.uint32(/* id 9, wireType 2 =*/ 74).string(message.Setting)
      if (
        message.ControllerHWID != null &&
        Object.hasOwnProperty.call(message, 'ControllerHWID')
      )
        writer
          .uint32(/* id 10, wireType 0 =*/ 80)
          .sint32(message.ControllerHWID)
      if (
        message.ChannelCount != null &&
        Object.hasOwnProperty.call(message, 'ChannelCount')
      )
        writer.uint32(/* id 11, wireType 0 =*/ 88).sint32(message.ChannelCount)
      if (
        message.MapShowLevel != null &&
        Object.hasOwnProperty.call(message, 'MapShowLevel')
      )
        writer.uint32(/* id 12, wireType 0 =*/ 96).sint32(message.MapShowLevel)
      if (
        message.UpdatedAt != null &&
        Object.hasOwnProperty.call(message, 'UpdatedAt')
      )
        writer.uint32(/* id 14, wireType 2 =*/ 114).string(message.UpdatedAt)
      if (
        message.UpdatedDC != null &&
        Object.hasOwnProperty.call(message, 'UpdatedDC')
      )
        writer.uint32(/* id 15, wireType 2 =*/ 122).string(message.UpdatedDC)
      if (
        message.DefaultNetworkType != null &&
        Object.hasOwnProperty.call(message, 'DefaultNetworkType')
      )
        writer
          .uint32(/* id 16, wireType 0 =*/ 128)
          .sint32(message.DefaultNetworkType)
      if (
        message.ParentChannelNo != null &&
        Object.hasOwnProperty.call(message, 'ParentChannelNo')
      )
        writer
          .uint32(/* id 17, wireType 0 =*/ 136)
          .sint32(message.ParentChannelNo)
      return writer
    }

    /**
     * Decodes a DbController message from the specified reader or buffer.
     * @function decode
     * @memberof bysdb.DbController
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {bysdb.DbController} DbController
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    DbController.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.bysdb.DbController()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.RID = reader.string()
            break
          case 2:
            message.OrgRID = reader.string()
            break
          case 3:
            message.ControllerNo = reader.string()
            break
          case 4:
            message.ControllerDescription = reader.string()
            break
          case 5:
            message.ControllerType = reader.int32()
            break
          case 6:
            message.ParentRID = reader.string()
            break
          case 7:
            message.Lon = reader.double()
            break
          case 8:
            message.Lat = reader.double()
            break
          case 9:
            message.Setting = reader.string()
            break
          case 10:
            message.ControllerHWID = reader.sint32()
            break
          case 11:
            message.ChannelCount = reader.sint32()
            break
          case 12:
            message.MapShowLevel = reader.sint32()
            break
          case 14:
            message.UpdatedAt = reader.string()
            break
          case 15:
            message.UpdatedDC = reader.string()
            break
          case 16:
            message.DefaultNetworkType = reader.sint32()
            break
          case 17:
            message.ParentChannelNo = reader.sint32()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return DbController
  })()

  bysdb.DbBysMarker = (function () {
    /**
     * Properties of a DbBysMarker.
     * @memberof bysdb
     * @interface IDbBysMarker
     * @property {string|null} [RID] @db uuid primary key
     * 行ID
     * @property {string|null} [OrgRID] @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 界桩所属的群组
     * @property {string|null} [MarkerNo] @db varchar(16) not null unique
     * 界桩编号
     * @property {string|null} [MarkerDescription] @db text
     * 界桩描述信息
     * @property {string|null} [ControllerRID] @db uuid REFERENCES DbController(RID) ON DELETE RESTRICT
     * 所属控制器RID
     * @property {number|null} [ControllerChannel] @db int not null
     * 所属的基站通道，如果所属的控制器为中继控制器，此值固定为1
     * @property {number|null} [Lon] @db double precision
     * 经度
     * @property {number|null} [Lat] @db double precision
     * 纬度
     * @property {number|null} [MarkerHWID] @db int not null unique
     * 界桩硬件ID,范围
     * @property {string|null} [Setting] @db jsonb not null default  '{}'::jsonb
     * 其它设置，可以在里面扩展需要用到的其它信息
     * 新的红线图规则属性为：redLine，不存在该字段为默认连接，空数组为不连接，
     * 数组元素为界桩硬件ID，每个都连接
     * @property {number|null} [MarkerModel] @db int
     * 界桩型号 1:长型 X*Y*Z cm, 2:短型 x*y*z cm
     * @property {number|null} [MapShowLevel] @db int default 12
     * 地图开始显示级别
     * @property {number|null} [MarkerQueueNo] @db int not null
     * 在所属于基站下的排队号
     * @property {string|null} [UpdatedAt] @db timestamp not null default now_utc()
     * 数据最后修改时间
     * @property {string|null} [UpdatedDC] @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     * @property {string|null} [MarkerParamTime] @db timestamp not null default now_utc()
     * 参数更新时间
     * @property {number|null} [MarkerDayInterval] @db int not null default 24
     * 打卡时间间隔(6-24小时)
     * @property {number|null} [MarkerQueueInterval] @db int not null default 6
     * 排队发射间隔时间（秒）
     * @property {number|null} [MarkerEmergentInterval] @db int not null default 60
     * 报警后发射间隔(30-240秒)
     * @property {number|null} [MarkerChannel] @db int not null default 1
     * 界桩通信信道
     * @property {string|null} [MarkerWakeupBaseTime] @db time not null
     * 界桩苏醒基准时间
     * @property {number|null} [MarkerDisabled] @db int not null default 0
     * 界桩是否已经被遥毙, 0:遥活 1:遥闭  2:遥晕
     * @property {boolean|null} [HasInstallDevice] @db boolean default true
     * 界桩是否有安装电子设备
     * @property {boolean|null} [HasInstallStone] @db boolean default false
     * 石头界桩是否已安装
     * @property {number|null} [MarkerType] @db int not null default 0
     * 常规界桩：0, 4g界桩(BF-DTD002): 1, 4g界桩带摄像机(BF-DTD002(Pro)): 2, 专网界桩dmr版(BF-DTD003): 3
     * @property {string|null} [MarkerSettings] @db jsonb not null default  '{}'::jsonb
     * MarkerType=1时，填写bysproto.BDataUpdate
     * @property {string|null} [ICCID] @db varchar(20)
     * 4G界桩的iccid卡号，20位数字字符串
     * @property {string|null} [ExpirationDate] @db timestamp
     * iccid卡号到期日期
     * @property {string|null} [IMEI] @db text
     * 4g界桩的设备唯一标识
     * @property {number|null} [CameraDisabled] @db int not null default 0
     * 摄像机是否已经被遥毙, 0:遥活 1:遥闭  2:遥晕
     */

    /**
     * Constructs a new DbBysMarker.
     * @memberof bysdb
     * @classdesc 界桩信息表
     * @rpc crud pcrud
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbBysMarkerOrgRID on DbBysMarker USING hash(OrgRID);
     * @dbpost ALTER TABLE DbBysMarker ADD COLUMN IF NOT EXISTS MarkerType integer not null default 0;
     * @dbpost ALTER TABLE DbBysMarker ADD COLUMN IF NOT EXISTS MarkerSettings jsonb not null default  '{}'::jsonb;
     * @dbpost ALTER TABLE dbbysmarker DROP CONSTRAINT if exists dbbysmarker_controllerrid_controllerchannel_markerqueueno_key;
     * @dbpost drop index if exists dbbysmarker_controllerrid_controllerchannel_markerqueueno_key;
     * @dbpost CREATE UNIQUE INDEX IF NOT EXISTS idxdbbysmarker_controllerrid_controllerchannel_markerqueueno_key on DbBysMarker (ControllerRID, ControllerChannel, MarkerQueueNo) where MarkerType=0; --同一个信道的排队号必须唯一,只针对旧的界桩
     * @dbpost ALTER TABLE DbBysMarker ALTER COLUMN ControllerRID DROP NOT NULL;
     * @dbpost ALTER TABLE DbBysMarker ADD COLUMN IF NOT EXISTS ICCID varchar(20);
     * @dbpost ALTER TABLE DbBysMarker ADD COLUMN IF NOT EXISTS ExpirationDate timestamp;
     * @dbpost CREATE UNIQUE INDEX IF NOT EXISTS idxDbBysMarkerICCID on DbBysMarker (ICCID) where MarkerType=1;
     * @dbpost ALTER TABLE DbBysMarker ADD COLUMN IF NOT EXISTS IMEI text;
     * @dbpost ALTER TABLE DbBysMarker ADD COLUMN IF NOT EXISTS CameraDisabled integer not null default 0;
     * @implements IDbBysMarker
     * @constructor
     * @param {bysdb.IDbBysMarker=} [properties] Properties to set
     */
    function DbBysMarker(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * @db uuid primary key
     * 行ID
     * @member {string} RID
     * @memberof bysdb.DbBysMarker
     * @instance
     */
    DbBysMarker.prototype.RID = ''

    /**
     * @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 界桩所属的群组
     * @member {string} OrgRID
     * @memberof bysdb.DbBysMarker
     * @instance
     */
    DbBysMarker.prototype.OrgRID = ''

    /**
     * @db varchar(16) not null unique
     * 界桩编号
     * @member {string} MarkerNo
     * @memberof bysdb.DbBysMarker
     * @instance
     */
    DbBysMarker.prototype.MarkerNo = ''

    /**
     * @db text
     * 界桩描述信息
     * @member {string} MarkerDescription
     * @memberof bysdb.DbBysMarker
     * @instance
     */
    DbBysMarker.prototype.MarkerDescription = ''

    /**
     * @db uuid REFERENCES DbController(RID) ON DELETE RESTRICT
     * 所属控制器RID
     * @member {string} ControllerRID
     * @memberof bysdb.DbBysMarker
     * @instance
     */
    DbBysMarker.prototype.ControllerRID = ''

    /**
     * @db int not null
     * 所属的基站通道，如果所属的控制器为中继控制器，此值固定为1
     * @member {number} ControllerChannel
     * @memberof bysdb.DbBysMarker
     * @instance
     */
    DbBysMarker.prototype.ControllerChannel = 0

    /**
     * @db double precision
     * 经度
     * @member {number} Lon
     * @memberof bysdb.DbBysMarker
     * @instance
     */
    DbBysMarker.prototype.Lon = 0

    /**
     * @db double precision
     * 纬度
     * @member {number} Lat
     * @memberof bysdb.DbBysMarker
     * @instance
     */
    DbBysMarker.prototype.Lat = 0

    /**
     * @db int not null unique
     * 界桩硬件ID,范围
     * @member {number} MarkerHWID
     * @memberof bysdb.DbBysMarker
     * @instance
     */
    DbBysMarker.prototype.MarkerHWID = 0

    /**
     * @db jsonb not null default  '{}'::jsonb
     * 其它设置，可以在里面扩展需要用到的其它信息
     * 新的红线图规则属性为：redLine，不存在该字段为默认连接，空数组为不连接，
     * 数组元素为界桩硬件ID，每个都连接
     * @member {string} Setting
     * @memberof bysdb.DbBysMarker
     * @instance
     */
    DbBysMarker.prototype.Setting = ''

    /**
     * @db int
     * 界桩型号 1:长型 X*Y*Z cm, 2:短型 x*y*z cm
     * @member {number} MarkerModel
     * @memberof bysdb.DbBysMarker
     * @instance
     */
    DbBysMarker.prototype.MarkerModel = 0

    /**
     * @db int default 12
     * 地图开始显示级别
     * @member {number} MapShowLevel
     * @memberof bysdb.DbBysMarker
     * @instance
     */
    DbBysMarker.prototype.MapShowLevel = 0

    /**
     * @db int not null
     * 在所属于基站下的排队号
     * @member {number} MarkerQueueNo
     * @memberof bysdb.DbBysMarker
     * @instance
     */
    DbBysMarker.prototype.MarkerQueueNo = 0

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     * @member {string} UpdatedAt
     * @memberof bysdb.DbBysMarker
     * @instance
     */
    DbBysMarker.prototype.UpdatedAt = ''

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     * @member {string} UpdatedDC
     * @memberof bysdb.DbBysMarker
     * @instance
     */
    DbBysMarker.prototype.UpdatedDC = ''

    /**
     * @db timestamp not null default now_utc()
     * 参数更新时间
     * @member {string} MarkerParamTime
     * @memberof bysdb.DbBysMarker
     * @instance
     */
    DbBysMarker.prototype.MarkerParamTime = ''

    /**
     * @db int not null default 24
     * 打卡时间间隔(6-24小时)
     * @member {number} MarkerDayInterval
     * @memberof bysdb.DbBysMarker
     * @instance
     */
    DbBysMarker.prototype.MarkerDayInterval = 0

    /**
     * @db int not null default 6
     * 排队发射间隔时间（秒）
     * @member {number} MarkerQueueInterval
     * @memberof bysdb.DbBysMarker
     * @instance
     */
    DbBysMarker.prototype.MarkerQueueInterval = 0

    /**
     * @db int not null default 60
     * 报警后发射间隔(30-240秒)
     * @member {number} MarkerEmergentInterval
     * @memberof bysdb.DbBysMarker
     * @instance
     */
    DbBysMarker.prototype.MarkerEmergentInterval = 0

    /**
     * @db int not null default 1
     * 界桩通信信道
     * @member {number} MarkerChannel
     * @memberof bysdb.DbBysMarker
     * @instance
     */
    DbBysMarker.prototype.MarkerChannel = 0

    /**
     * @db time not null
     * 界桩苏醒基准时间
     * @member {string} MarkerWakeupBaseTime
     * @memberof bysdb.DbBysMarker
     * @instance
     */
    DbBysMarker.prototype.MarkerWakeupBaseTime = ''

    /**
     * @db int not null default 0
     * 界桩是否已经被遥毙, 0:遥活 1:遥闭  2:遥晕
     * @member {number} MarkerDisabled
     * @memberof bysdb.DbBysMarker
     * @instance
     */
    DbBysMarker.prototype.MarkerDisabled = 0

    /**
     * @db boolean default true
     * 界桩是否有安装电子设备
     * @member {boolean} HasInstallDevice
     * @memberof bysdb.DbBysMarker
     * @instance
     */
    DbBysMarker.prototype.HasInstallDevice = false

    /**
     * @db boolean default false
     * 石头界桩是否已安装
     * @member {boolean} HasInstallStone
     * @memberof bysdb.DbBysMarker
     * @instance
     */
    DbBysMarker.prototype.HasInstallStone = false

    /**
     * @db int not null default 0
     * 常规界桩：0, 4g界桩(BF-DTD002): 1, 4g界桩带摄像机(BF-DTD002(Pro)): 2, 专网界桩dmr版(BF-DTD003): 3
     * @member {number} MarkerType
     * @memberof bysdb.DbBysMarker
     * @instance
     */
    DbBysMarker.prototype.MarkerType = 0

    /**
     * @db jsonb not null default  '{}'::jsonb
     * MarkerType=1时，填写bysproto.BDataUpdate
     * @member {string} MarkerSettings
     * @memberof bysdb.DbBysMarker
     * @instance
     */
    DbBysMarker.prototype.MarkerSettings = ''

    /**
     * @db varchar(20)
     * 4G界桩的iccid卡号，20位数字字符串
     * @member {string} ICCID
     * @memberof bysdb.DbBysMarker
     * @instance
     */
    DbBysMarker.prototype.ICCID = ''

    /**
     * @db timestamp
     * iccid卡号到期日期
     * @member {string} ExpirationDate
     * @memberof bysdb.DbBysMarker
     * @instance
     */
    DbBysMarker.prototype.ExpirationDate = ''

    /**
     * @db text
     * 4g界桩的设备唯一标识
     * @member {string} IMEI
     * @memberof bysdb.DbBysMarker
     * @instance
     */
    DbBysMarker.prototype.IMEI = ''

    /**
     * @db int not null default 0
     * 摄像机是否已经被遥毙, 0:遥活 1:遥闭  2:遥晕
     * @member {number} CameraDisabled
     * @memberof bysdb.DbBysMarker
     * @instance
     */
    DbBysMarker.prototype.CameraDisabled = 0

    /**
     * Encodes the specified DbBysMarker message. Does not implicitly {@link bysdb.DbBysMarker.verify|verify} messages.
     * @function encode
     * @memberof bysdb.DbBysMarker
     * @static
     * @param {bysdb.IDbBysMarker} message DbBysMarker message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    DbBysMarker.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (message.RID != null && Object.hasOwnProperty.call(message, 'RID'))
        writer.uint32(/* id 1, wireType 2 =*/ 10).string(message.RID)
      if (
        message.OrgRID != null &&
        Object.hasOwnProperty.call(message, 'OrgRID')
      )
        writer.uint32(/* id 2, wireType 2 =*/ 18).string(message.OrgRID)
      if (
        message.MarkerNo != null &&
        Object.hasOwnProperty.call(message, 'MarkerNo')
      )
        writer.uint32(/* id 3, wireType 2 =*/ 26).string(message.MarkerNo)
      if (
        message.MarkerDescription != null &&
        Object.hasOwnProperty.call(message, 'MarkerDescription')
      )
        writer
          .uint32(/* id 4, wireType 2 =*/ 34)
          .string(message.MarkerDescription)
      if (
        message.ControllerRID != null &&
        Object.hasOwnProperty.call(message, 'ControllerRID')
      )
        writer.uint32(/* id 5, wireType 2 =*/ 42).string(message.ControllerRID)
      if (
        message.ControllerChannel != null &&
        Object.hasOwnProperty.call(message, 'ControllerChannel')
      )
        writer
          .uint32(/* id 6, wireType 0 =*/ 48)
          .sint32(message.ControllerChannel)
      if (message.Lon != null && Object.hasOwnProperty.call(message, 'Lon'))
        writer.uint32(/* id 7, wireType 1 =*/ 57).double(message.Lon)
      if (message.Lat != null && Object.hasOwnProperty.call(message, 'Lat'))
        writer.uint32(/* id 8, wireType 1 =*/ 65).double(message.Lat)
      if (
        message.MarkerHWID != null &&
        Object.hasOwnProperty.call(message, 'MarkerHWID')
      )
        writer.uint32(/* id 9, wireType 0 =*/ 72).sint32(message.MarkerHWID)
      if (
        message.Setting != null &&
        Object.hasOwnProperty.call(message, 'Setting')
      )
        writer.uint32(/* id 10, wireType 2 =*/ 82).string(message.Setting)
      if (
        message.MarkerModel != null &&
        Object.hasOwnProperty.call(message, 'MarkerModel')
      )
        writer.uint32(/* id 11, wireType 0 =*/ 88).sint32(message.MarkerModel)
      if (
        message.MapShowLevel != null &&
        Object.hasOwnProperty.call(message, 'MapShowLevel')
      )
        writer.uint32(/* id 12, wireType 0 =*/ 96).sint32(message.MapShowLevel)
      if (
        message.MarkerQueueNo != null &&
        Object.hasOwnProperty.call(message, 'MarkerQueueNo')
      )
        writer
          .uint32(/* id 13, wireType 0 =*/ 104)
          .sint32(message.MarkerQueueNo)
      if (
        message.UpdatedAt != null &&
        Object.hasOwnProperty.call(message, 'UpdatedAt')
      )
        writer.uint32(/* id 14, wireType 2 =*/ 114).string(message.UpdatedAt)
      if (
        message.UpdatedDC != null &&
        Object.hasOwnProperty.call(message, 'UpdatedDC')
      )
        writer.uint32(/* id 15, wireType 2 =*/ 122).string(message.UpdatedDC)
      if (
        message.MarkerParamTime != null &&
        Object.hasOwnProperty.call(message, 'MarkerParamTime')
      )
        writer
          .uint32(/* id 16, wireType 2 =*/ 130)
          .string(message.MarkerParamTime)
      if (
        message.MarkerDayInterval != null &&
        Object.hasOwnProperty.call(message, 'MarkerDayInterval')
      )
        writer
          .uint32(/* id 17, wireType 0 =*/ 136)
          .sint32(message.MarkerDayInterval)
      if (
        message.MarkerQueueInterval != null &&
        Object.hasOwnProperty.call(message, 'MarkerQueueInterval')
      )
        writer
          .uint32(/* id 18, wireType 0 =*/ 144)
          .sint32(message.MarkerQueueInterval)
      if (
        message.MarkerEmergentInterval != null &&
        Object.hasOwnProperty.call(message, 'MarkerEmergentInterval')
      )
        writer
          .uint32(/* id 19, wireType 0 =*/ 152)
          .sint32(message.MarkerEmergentInterval)
      if (
        message.MarkerChannel != null &&
        Object.hasOwnProperty.call(message, 'MarkerChannel')
      )
        writer
          .uint32(/* id 20, wireType 0 =*/ 160)
          .sint32(message.MarkerChannel)
      if (
        message.MarkerWakeupBaseTime != null &&
        Object.hasOwnProperty.call(message, 'MarkerWakeupBaseTime')
      )
        writer
          .uint32(/* id 21, wireType 2 =*/ 170)
          .string(message.MarkerWakeupBaseTime)
      if (
        message.MarkerDisabled != null &&
        Object.hasOwnProperty.call(message, 'MarkerDisabled')
      )
        writer
          .uint32(/* id 22, wireType 0 =*/ 176)
          .sint32(message.MarkerDisabled)
      if (
        message.HasInstallDevice != null &&
        Object.hasOwnProperty.call(message, 'HasInstallDevice')
      )
        writer
          .uint32(/* id 23, wireType 0 =*/ 184)
          .bool(message.HasInstallDevice)
      if (
        message.HasInstallStone != null &&
        Object.hasOwnProperty.call(message, 'HasInstallStone')
      )
        writer
          .uint32(/* id 25, wireType 0 =*/ 200)
          .bool(message.HasInstallStone)
      if (
        message.MarkerType != null &&
        Object.hasOwnProperty.call(message, 'MarkerType')
      )
        writer.uint32(/* id 26, wireType 0 =*/ 208).sint32(message.MarkerType)
      if (
        message.MarkerSettings != null &&
        Object.hasOwnProperty.call(message, 'MarkerSettings')
      )
        writer
          .uint32(/* id 27, wireType 2 =*/ 218)
          .string(message.MarkerSettings)
      if (message.ICCID != null && Object.hasOwnProperty.call(message, 'ICCID'))
        writer.uint32(/* id 28, wireType 2 =*/ 226).string(message.ICCID)
      if (
        message.ExpirationDate != null &&
        Object.hasOwnProperty.call(message, 'ExpirationDate')
      )
        writer
          .uint32(/* id 29, wireType 2 =*/ 234)
          .string(message.ExpirationDate)
      if (message.IMEI != null && Object.hasOwnProperty.call(message, 'IMEI'))
        writer.uint32(/* id 30, wireType 2 =*/ 242).string(message.IMEI)
      if (
        message.CameraDisabled != null &&
        Object.hasOwnProperty.call(message, 'CameraDisabled')
      )
        writer
          .uint32(/* id 31, wireType 0 =*/ 248)
          .sint32(message.CameraDisabled)
      return writer
    }

    /**
     * Decodes a DbBysMarker message from the specified reader or buffer.
     * @function decode
     * @memberof bysdb.DbBysMarker
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {bysdb.DbBysMarker} DbBysMarker
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    DbBysMarker.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.bysdb.DbBysMarker()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.RID = reader.string()
            break
          case 2:
            message.OrgRID = reader.string()
            break
          case 3:
            message.MarkerNo = reader.string()
            break
          case 4:
            message.MarkerDescription = reader.string()
            break
          case 5:
            message.ControllerRID = reader.string()
            break
          case 6:
            message.ControllerChannel = reader.sint32()
            break
          case 7:
            message.Lon = reader.double()
            break
          case 8:
            message.Lat = reader.double()
            break
          case 9:
            message.MarkerHWID = reader.sint32()
            break
          case 10:
            message.Setting = reader.string()
            break
          case 11:
            message.MarkerModel = reader.sint32()
            break
          case 12:
            message.MapShowLevel = reader.sint32()
            break
          case 13:
            message.MarkerQueueNo = reader.sint32()
            break
          case 14:
            message.UpdatedAt = reader.string()
            break
          case 15:
            message.UpdatedDC = reader.string()
            break
          case 16:
            message.MarkerParamTime = reader.string()
            break
          case 17:
            message.MarkerDayInterval = reader.sint32()
            break
          case 18:
            message.MarkerQueueInterval = reader.sint32()
            break
          case 19:
            message.MarkerEmergentInterval = reader.sint32()
            break
          case 20:
            message.MarkerChannel = reader.sint32()
            break
          case 21:
            message.MarkerWakeupBaseTime = reader.string()
            break
          case 22:
            message.MarkerDisabled = reader.sint32()
            break
          case 23:
            message.HasInstallDevice = reader.bool()
            break
          case 25:
            message.HasInstallStone = reader.bool()
            break
          case 26:
            message.MarkerType = reader.sint32()
            break
          case 27:
            message.MarkerSettings = reader.string()
            break
          case 28:
            message.ICCID = reader.string()
            break
          case 29:
            message.ExpirationDate = reader.string()
            break
          case 30:
            message.IMEI = reader.string()
            break
          case 31:
            message.CameraDisabled = reader.sint32()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return DbBysMarker
  })()

  bysdb.DbControllerOnlineHistory = (function () {
    /**
     * Properties of a DbControllerOnlineHistory.
     * @memberof bysdb
     * @interface IDbControllerOnlineHistory
     * @property {string|null} [OrgRID] @db uuid not null
     * 所属的群组
     * @property {string|null} [ActionTime] @db timestamp not null
     * 动作时间,utc
     * @property {number|null} [ControllerHWID] @db int
     * 控制器硬件ID
     * @property {number|null} [ActionCode] @db int
     * action 1:上线 2:下线 11:ping信息
     * @property {string|null} [IpInfo] @db text
     * ip/状态信息
     * @property {number|null} [NetworkType] @db int
     * 登录网络类型,只对上线有效
     * @property {number|null} [Power] @db real
     * 电量V
     * @property {number|null} [Status] @db int
     * 状态
     * @property {number|null} [DeviceType] 登录设备类型 0:fsk控制器 1:4g界桩
     */

    /**
     * Constructs a new DbControllerOnlineHistory.
     * @memberof bysdb
     * @classdesc 控制器上下线历史表，以月为单位分表
     * @rpc pcrud
     * @dbend PARTITION BY RANGE (ActionTime)
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbControllerOnlineHistoryControllerHWID on DbControllerOnlineHistory USING hash(ControllerHWID);
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbControllerOnlineHistoryOrgRID on DbControllerOnlineHistory USING hash(OrgRID);
     * @dbpost ALTER TABLE dbcontrolleronlinehistory ADD COLUMN IF NOT EXISTS Status integer;
     * @dbpost ALTER TABLE dbcontrolleronlinehistory ADD COLUMN IF NOT EXISTS DeviceType integer;
     * @implements IDbControllerOnlineHistory
     * @constructor
     * @param {bysdb.IDbControllerOnlineHistory=} [properties] Properties to set
     */
    function DbControllerOnlineHistory(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * @db uuid not null
     * 所属的群组
     * @member {string} OrgRID
     * @memberof bysdb.DbControllerOnlineHistory
     * @instance
     */
    DbControllerOnlineHistory.prototype.OrgRID = ''

    /**
     * @db timestamp not null
     * 动作时间,utc
     * @member {string} ActionTime
     * @memberof bysdb.DbControllerOnlineHistory
     * @instance
     */
    DbControllerOnlineHistory.prototype.ActionTime = ''

    /**
     * @db int
     * 控制器硬件ID
     * @member {number} ControllerHWID
     * @memberof bysdb.DbControllerOnlineHistory
     * @instance
     */
    DbControllerOnlineHistory.prototype.ControllerHWID = 0

    /**
     * @db int
     * action 1:上线 2:下线 11:ping信息
     * @member {number} ActionCode
     * @memberof bysdb.DbControllerOnlineHistory
     * @instance
     */
    DbControllerOnlineHistory.prototype.ActionCode = 0

    /**
     * @db text
     * ip/状态信息
     * @member {string} IpInfo
     * @memberof bysdb.DbControllerOnlineHistory
     * @instance
     */
    DbControllerOnlineHistory.prototype.IpInfo = ''

    /**
     * @db int
     * 登录网络类型,只对上线有效
     * @member {number} NetworkType
     * @memberof bysdb.DbControllerOnlineHistory
     * @instance
     */
    DbControllerOnlineHistory.prototype.NetworkType = 0

    /**
     * @db real
     * 电量V
     * @member {number} Power
     * @memberof bysdb.DbControllerOnlineHistory
     * @instance
     */
    DbControllerOnlineHistory.prototype.Power = 0

    /**
     * @db int
     * 状态
     * @member {number} Status
     * @memberof bysdb.DbControllerOnlineHistory
     * @instance
     */
    DbControllerOnlineHistory.prototype.Status = 0

    /**
     * 登录设备类型 0:fsk控制器 1:4g界桩
     * @member {number} DeviceType
     * @memberof bysdb.DbControllerOnlineHistory
     * @instance
     */
    DbControllerOnlineHistory.prototype.DeviceType = 0

    /**
     * Encodes the specified DbControllerOnlineHistory message. Does not implicitly {@link bysdb.DbControllerOnlineHistory.verify|verify} messages.
     * @function encode
     * @memberof bysdb.DbControllerOnlineHistory
     * @static
     * @param {bysdb.IDbControllerOnlineHistory} message DbControllerOnlineHistory message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    DbControllerOnlineHistory.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (
        message.OrgRID != null &&
        Object.hasOwnProperty.call(message, 'OrgRID')
      )
        writer.uint32(/* id 2, wireType 2 =*/ 18).string(message.OrgRID)
      if (
        message.ActionTime != null &&
        Object.hasOwnProperty.call(message, 'ActionTime')
      )
        writer.uint32(/* id 3, wireType 2 =*/ 26).string(message.ActionTime)
      if (
        message.ControllerHWID != null &&
        Object.hasOwnProperty.call(message, 'ControllerHWID')
      )
        writer.uint32(/* id 4, wireType 0 =*/ 32).sint32(message.ControllerHWID)
      if (
        message.ActionCode != null &&
        Object.hasOwnProperty.call(message, 'ActionCode')
      )
        writer.uint32(/* id 5, wireType 0 =*/ 40).sint32(message.ActionCode)
      if (
        message.IpInfo != null &&
        Object.hasOwnProperty.call(message, 'IpInfo')
      )
        writer.uint32(/* id 6, wireType 2 =*/ 50).string(message.IpInfo)
      if (
        message.NetworkType != null &&
        Object.hasOwnProperty.call(message, 'NetworkType')
      )
        writer.uint32(/* id 7, wireType 0 =*/ 56).sint32(message.NetworkType)
      if (message.Power != null && Object.hasOwnProperty.call(message, 'Power'))
        writer.uint32(/* id 8, wireType 5 =*/ 69).float(message.Power)
      if (
        message.Status != null &&
        Object.hasOwnProperty.call(message, 'Status')
      )
        writer.uint32(/* id 9, wireType 0 =*/ 72).uint32(message.Status)
      if (
        message.DeviceType != null &&
        Object.hasOwnProperty.call(message, 'DeviceType')
      )
        writer.uint32(/* id 10, wireType 0 =*/ 80).int32(message.DeviceType)
      return writer
    }

    /**
     * Decodes a DbControllerOnlineHistory message from the specified reader or buffer.
     * @function decode
     * @memberof bysdb.DbControllerOnlineHistory
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {bysdb.DbControllerOnlineHistory} DbControllerOnlineHistory
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    DbControllerOnlineHistory.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.bysdb.DbControllerOnlineHistory()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 2:
            message.OrgRID = reader.string()
            break
          case 3:
            message.ActionTime = reader.string()
            break
          case 4:
            message.ControllerHWID = reader.sint32()
            break
          case 5:
            message.ActionCode = reader.sint32()
            break
          case 6:
            message.IpInfo = reader.string()
            break
          case 7:
            message.NetworkType = reader.sint32()
            break
          case 8:
            message.Power = reader.float()
            break
          case 9:
            message.Status = reader.uint32()
            break
          case 10:
            message.DeviceType = reader.int32()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return DbControllerOnlineHistory
  })()

  bysdb.DbMediaInfo = (function () {
    /**
     * Properties of a DbMediaInfo.
     * @memberof bysdb
     * @interface IDbMediaInfo
     * @property {string|null} [RID] @db uuid primary key
     * 行ID
     * @property {string|null} [OrgRID] @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 物体所属的群组
     * @property {string|null} [MarkerRID] @db uuid null REFERENCES DbBysMarker(RID) ON DELETE set null
     * 界桩的RID
     * @property {string|null} [ControllerRID] @db uuid null REFERENCES DbController(RID) ON DELETE set null
     * 控制器的RID
     * @property {number|null} [MediaType] @db int
     * 媒体类型 1:normal pic 2:3d pic 3:video
     * @property {string|null} [MediaOrigFileName] @db text
     * 媒体原始文件名
     * @property {string|null} [UploadUserRID] @db uuid REFERENCES DbUser(RID) ON DELETE set null
     * 上传用户rid
     * @property {string|null} [MediaDescription] @db text
     * 描述信息
     * @property {string|null} [UploadTime] @db timestamp not null
     * 上传时间,utc
     * @property {string|null} [Setting] @db jsonb not null default  '{}'::jsonb
     * 其它设置，可以在里面扩展需要用到的其它信息
     * @property {string|null} [LastUpdateUserRID] @db uuid REFERENCES DbUser(RID) ON DELETE set null
     * 最新编辑用户rid
     * @property {string|null} [LastUpdateTime] @db timestamp not null default now_utc()
     * 最新编辑时间,utc
     */

    /**
     * Constructs a new DbMediaInfo.
     * @memberof bysdb
     * @classdesc 界桩/控制器媒体信息表
     * @rpc pcrud
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbMediaInfoOrgRID on DbMediaInfo USING hash(OrgRID);
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbMediaInfoMarkerRID on DbMediaInfo USING hash(MarkerRID);
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbMediaInfoControllerRID on DbMediaInfo USING hash(ControllerRID);
     * @implements IDbMediaInfo
     * @constructor
     * @param {bysdb.IDbMediaInfo=} [properties] Properties to set
     */
    function DbMediaInfo(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * @db uuid primary key
     * 行ID
     * @member {string} RID
     * @memberof bysdb.DbMediaInfo
     * @instance
     */
    DbMediaInfo.prototype.RID = ''

    /**
     * @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 物体所属的群组
     * @member {string} OrgRID
     * @memberof bysdb.DbMediaInfo
     * @instance
     */
    DbMediaInfo.prototype.OrgRID = ''

    /**
     * @db uuid null REFERENCES DbBysMarker(RID) ON DELETE set null
     * 界桩的RID
     * @member {string} MarkerRID
     * @memberof bysdb.DbMediaInfo
     * @instance
     */
    DbMediaInfo.prototype.MarkerRID = ''

    /**
     * @db uuid null REFERENCES DbController(RID) ON DELETE set null
     * 控制器的RID
     * @member {string} ControllerRID
     * @memberof bysdb.DbMediaInfo
     * @instance
     */
    DbMediaInfo.prototype.ControllerRID = ''

    /**
     * @db int
     * 媒体类型 1:normal pic 2:3d pic 3:video
     * @member {number} MediaType
     * @memberof bysdb.DbMediaInfo
     * @instance
     */
    DbMediaInfo.prototype.MediaType = 0

    /**
     * @db text
     * 媒体原始文件名
     * @member {string} MediaOrigFileName
     * @memberof bysdb.DbMediaInfo
     * @instance
     */
    DbMediaInfo.prototype.MediaOrigFileName = ''

    /**
     * @db uuid REFERENCES DbUser(RID) ON DELETE set null
     * 上传用户rid
     * @member {string} UploadUserRID
     * @memberof bysdb.DbMediaInfo
     * @instance
     */
    DbMediaInfo.prototype.UploadUserRID = ''

    /**
     * @db text
     * 描述信息
     * @member {string} MediaDescription
     * @memberof bysdb.DbMediaInfo
     * @instance
     */
    DbMediaInfo.prototype.MediaDescription = ''

    /**
     * @db timestamp not null
     * 上传时间,utc
     * @member {string} UploadTime
     * @memberof bysdb.DbMediaInfo
     * @instance
     */
    DbMediaInfo.prototype.UploadTime = ''

    /**
     * @db jsonb not null default  '{}'::jsonb
     * 其它设置，可以在里面扩展需要用到的其它信息
     * @member {string} Setting
     * @memberof bysdb.DbMediaInfo
     * @instance
     */
    DbMediaInfo.prototype.Setting = ''

    /**
     * @db uuid REFERENCES DbUser(RID) ON DELETE set null
     * 最新编辑用户rid
     * @member {string} LastUpdateUserRID
     * @memberof bysdb.DbMediaInfo
     * @instance
     */
    DbMediaInfo.prototype.LastUpdateUserRID = ''

    /**
     * @db timestamp not null default now_utc()
     * 最新编辑时间,utc
     * @member {string} LastUpdateTime
     * @memberof bysdb.DbMediaInfo
     * @instance
     */
    DbMediaInfo.prototype.LastUpdateTime = ''

    /**
     * Encodes the specified DbMediaInfo message. Does not implicitly {@link bysdb.DbMediaInfo.verify|verify} messages.
     * @function encode
     * @memberof bysdb.DbMediaInfo
     * @static
     * @param {bysdb.IDbMediaInfo} message DbMediaInfo message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    DbMediaInfo.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (message.RID != null && Object.hasOwnProperty.call(message, 'RID'))
        writer.uint32(/* id 1, wireType 2 =*/ 10).string(message.RID)
      if (
        message.OrgRID != null &&
        Object.hasOwnProperty.call(message, 'OrgRID')
      )
        writer.uint32(/* id 2, wireType 2 =*/ 18).string(message.OrgRID)
      if (
        message.MarkerRID != null &&
        Object.hasOwnProperty.call(message, 'MarkerRID')
      )
        writer.uint32(/* id 3, wireType 2 =*/ 26).string(message.MarkerRID)
      if (
        message.ControllerRID != null &&
        Object.hasOwnProperty.call(message, 'ControllerRID')
      )
        writer.uint32(/* id 4, wireType 2 =*/ 34).string(message.ControllerRID)
      if (
        message.MediaType != null &&
        Object.hasOwnProperty.call(message, 'MediaType')
      )
        writer.uint32(/* id 5, wireType 0 =*/ 40).sint32(message.MediaType)
      if (
        message.MediaOrigFileName != null &&
        Object.hasOwnProperty.call(message, 'MediaOrigFileName')
      )
        writer
          .uint32(/* id 6, wireType 2 =*/ 50)
          .string(message.MediaOrigFileName)
      if (
        message.UploadUserRID != null &&
        Object.hasOwnProperty.call(message, 'UploadUserRID')
      )
        writer.uint32(/* id 7, wireType 2 =*/ 58).string(message.UploadUserRID)
      if (
        message.MediaDescription != null &&
        Object.hasOwnProperty.call(message, 'MediaDescription')
      )
        writer
          .uint32(/* id 8, wireType 2 =*/ 66)
          .string(message.MediaDescription)
      if (
        message.UploadTime != null &&
        Object.hasOwnProperty.call(message, 'UploadTime')
      )
        writer.uint32(/* id 9, wireType 2 =*/ 74).string(message.UploadTime)
      if (
        message.Setting != null &&
        Object.hasOwnProperty.call(message, 'Setting')
      )
        writer.uint32(/* id 10, wireType 2 =*/ 82).string(message.Setting)
      if (
        message.LastUpdateUserRID != null &&
        Object.hasOwnProperty.call(message, 'LastUpdateUserRID')
      )
        writer
          .uint32(/* id 11, wireType 2 =*/ 90)
          .string(message.LastUpdateUserRID)
      if (
        message.LastUpdateTime != null &&
        Object.hasOwnProperty.call(message, 'LastUpdateTime')
      )
        writer
          .uint32(/* id 12, wireType 2 =*/ 98)
          .string(message.LastUpdateTime)
      return writer
    }

    /**
     * Decodes a DbMediaInfo message from the specified reader or buffer.
     * @function decode
     * @memberof bysdb.DbMediaInfo
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {bysdb.DbMediaInfo} DbMediaInfo
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    DbMediaInfo.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.bysdb.DbMediaInfo()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.RID = reader.string()
            break
          case 2:
            message.OrgRID = reader.string()
            break
          case 3:
            message.MarkerRID = reader.string()
            break
          case 4:
            message.ControllerRID = reader.string()
            break
          case 5:
            message.MediaType = reader.sint32()
            break
          case 6:
            message.MediaOrigFileName = reader.string()
            break
          case 7:
            message.UploadUserRID = reader.string()
            break
          case 8:
            message.MediaDescription = reader.string()
            break
          case 9:
            message.UploadTime = reader.string()
            break
          case 10:
            message.Setting = reader.string()
            break
          case 11:
            message.LastUpdateUserRID = reader.string()
            break
          case 12:
            message.LastUpdateTime = reader.string()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return DbMediaInfo
  })()

  bysdb.DbMarkerHistory = (function () {
    /**
     * Properties of a DbMarkerHistory.
     * @memberof bysdb
     * @interface IDbMarkerHistory
     * @property {number|null} [ControllerID] @db int
     * 接收的控制器ID
     * @property {number|null} [ControllerChannel] @db int
     * 接收的控制器通道
     * @property {string|null} [OrgRID] @db uuid not null
     * 所属的群组
     * @property {string|null} [ActionTime] @db timestamp not null
     * 动作时间,utc
     * @property {number|null} [MarkerHWID] @db int
     * 界桩硬件ID
     * @property {number|null} [ActionCode] @db int
     * action 0xd1, 0xd2
     * 4g界桩信息上报21, ReportInfo=bysproto.BInfoReporting
     * 4g界桩报警22, ReportInfo=bysproto.BAlarmReporting
     * @property {number|null} [Status] @db int
     * @property {string|null} [ParamInfo] @db text
     * 配置参数信息
     * @property {number|null} [Lon] @db double precision
     * gps lon
     * @property {number|null} [Lat] @db double precision
     * gps lat
     * @property {string|null} [CmdTime] @db timestamp
     * cmd time
     * @property {number|null} [RecvControllerID] @db int
     * 实际接收的控制器ID（中继/基站）
     * @property {string|null} [ReportInfo] @db jsonb not null default  '{}'::jsonb
     */

    /**
     * Constructs a new DbMarkerHistory.
     * @memberof bysdb
     * @classdesc 界桩上传数据历史表，以月为单位分表
     * @rpc pcrud
     * @dbend PARTITION BY RANGE (ActionTime)
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbMarkerHistoryMarkerHWID on DbMarkerHistory USING hash(MarkerHWID);
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbMarkerHistoryOrgRID on DbMarkerHistory USING hash(OrgRID);
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbMarkerHistoryEmergency on DbMarkerHistory (Status) where (Status & 128) >0;
     * @dbpost alter table DbMarkerHistory ADD COLUMN IF NOT EXISTS RecvControllerID int;
     * @dbpost alter table DbMarkerHistory ADD COLUMN IF NOT EXISTS ReportInfo jsonb not null default  '{}'::jsonb;
     * @implements IDbMarkerHistory
     * @constructor
     * @param {bysdb.IDbMarkerHistory=} [properties] Properties to set
     */
    function DbMarkerHistory(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * @db int
     * 接收的控制器ID
     * @member {number} ControllerID
     * @memberof bysdb.DbMarkerHistory
     * @instance
     */
    DbMarkerHistory.prototype.ControllerID = 0

    /**
     * @db int
     * 接收的控制器通道
     * @member {number} ControllerChannel
     * @memberof bysdb.DbMarkerHistory
     * @instance
     */
    DbMarkerHistory.prototype.ControllerChannel = 0

    /**
     * @db uuid not null
     * 所属的群组
     * @member {string} OrgRID
     * @memberof bysdb.DbMarkerHistory
     * @instance
     */
    DbMarkerHistory.prototype.OrgRID = ''

    /**
     * @db timestamp not null
     * 动作时间,utc
     * @member {string} ActionTime
     * @memberof bysdb.DbMarkerHistory
     * @instance
     */
    DbMarkerHistory.prototype.ActionTime = ''

    /**
     * @db int
     * 界桩硬件ID
     * @member {number} MarkerHWID
     * @memberof bysdb.DbMarkerHistory
     * @instance
     */
    DbMarkerHistory.prototype.MarkerHWID = 0

    /**
     * @db int
     * action 0xd1, 0xd2
     * 4g界桩信息上报21, ReportInfo=bysproto.BInfoReporting
     * 4g界桩报警22, ReportInfo=bysproto.BAlarmReporting
     * @member {number} ActionCode
     * @memberof bysdb.DbMarkerHistory
     * @instance
     */
    DbMarkerHistory.prototype.ActionCode = 0

    /**
     * @db int
     * @member {number} Status
     * @memberof bysdb.DbMarkerHistory
     * @instance
     */
    DbMarkerHistory.prototype.Status = 0

    /**
     * @db text
     * 配置参数信息
     * @member {string} ParamInfo
     * @memberof bysdb.DbMarkerHistory
     * @instance
     */
    DbMarkerHistory.prototype.ParamInfo = ''

    /**
     * @db double precision
     * gps lon
     * @member {number} Lon
     * @memberof bysdb.DbMarkerHistory
     * @instance
     */
    DbMarkerHistory.prototype.Lon = 0

    /**
     * @db double precision
     * gps lat
     * @member {number} Lat
     * @memberof bysdb.DbMarkerHistory
     * @instance
     */
    DbMarkerHistory.prototype.Lat = 0

    /**
     * @db timestamp
     * cmd time
     * @member {string} CmdTime
     * @memberof bysdb.DbMarkerHistory
     * @instance
     */
    DbMarkerHistory.prototype.CmdTime = ''

    /**
     * @db int
     * 实际接收的控制器ID（中继/基站）
     * @member {number} RecvControllerID
     * @memberof bysdb.DbMarkerHistory
     * @instance
     */
    DbMarkerHistory.prototype.RecvControllerID = 0

    /**
     * @db jsonb not null default  '{}'::jsonb
     * @member {string} ReportInfo
     * @memberof bysdb.DbMarkerHistory
     * @instance
     */
    DbMarkerHistory.prototype.ReportInfo = ''

    /**
     * Encodes the specified DbMarkerHistory message. Does not implicitly {@link bysdb.DbMarkerHistory.verify|verify} messages.
     * @function encode
     * @memberof bysdb.DbMarkerHistory
     * @static
     * @param {bysdb.IDbMarkerHistory} message DbMarkerHistory message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    DbMarkerHistory.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (
        message.ControllerID != null &&
        Object.hasOwnProperty.call(message, 'ControllerID')
      )
        writer.uint32(/* id 1, wireType 0 =*/ 8).sint32(message.ControllerID)
      if (
        message.ControllerChannel != null &&
        Object.hasOwnProperty.call(message, 'ControllerChannel')
      )
        writer
          .uint32(/* id 2, wireType 0 =*/ 16)
          .sint32(message.ControllerChannel)
      if (
        message.OrgRID != null &&
        Object.hasOwnProperty.call(message, 'OrgRID')
      )
        writer.uint32(/* id 3, wireType 2 =*/ 26).string(message.OrgRID)
      if (
        message.ActionTime != null &&
        Object.hasOwnProperty.call(message, 'ActionTime')
      )
        writer.uint32(/* id 4, wireType 2 =*/ 34).string(message.ActionTime)
      if (
        message.MarkerHWID != null &&
        Object.hasOwnProperty.call(message, 'MarkerHWID')
      )
        writer.uint32(/* id 5, wireType 0 =*/ 40).sint32(message.MarkerHWID)
      if (
        message.ActionCode != null &&
        Object.hasOwnProperty.call(message, 'ActionCode')
      )
        writer.uint32(/* id 6, wireType 0 =*/ 48).sint32(message.ActionCode)
      if (
        message.Status != null &&
        Object.hasOwnProperty.call(message, 'Status')
      )
        writer.uint32(/* id 7, wireType 0 =*/ 56).sint32(message.Status)
      if (
        message.ParamInfo != null &&
        Object.hasOwnProperty.call(message, 'ParamInfo')
      )
        writer.uint32(/* id 8, wireType 2 =*/ 66).string(message.ParamInfo)
      if (message.Lon != null && Object.hasOwnProperty.call(message, 'Lon'))
        writer.uint32(/* id 9, wireType 1 =*/ 73).double(message.Lon)
      if (message.Lat != null && Object.hasOwnProperty.call(message, 'Lat'))
        writer.uint32(/* id 10, wireType 1 =*/ 81).double(message.Lat)
      if (
        message.CmdTime != null &&
        Object.hasOwnProperty.call(message, 'CmdTime')
      )
        writer.uint32(/* id 11, wireType 2 =*/ 90).string(message.CmdTime)
      if (
        message.RecvControllerID != null &&
        Object.hasOwnProperty.call(message, 'RecvControllerID')
      )
        writer
          .uint32(/* id 12, wireType 0 =*/ 96)
          .sint32(message.RecvControllerID)
      if (
        message.ReportInfo != null &&
        Object.hasOwnProperty.call(message, 'ReportInfo')
      )
        writer.uint32(/* id 13, wireType 2 =*/ 106).string(message.ReportInfo)
      return writer
    }

    /**
     * Decodes a DbMarkerHistory message from the specified reader or buffer.
     * @function decode
     * @memberof bysdb.DbMarkerHistory
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {bysdb.DbMarkerHistory} DbMarkerHistory
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    DbMarkerHistory.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.bysdb.DbMarkerHistory()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.ControllerID = reader.sint32()
            break
          case 2:
            message.ControllerChannel = reader.sint32()
            break
          case 3:
            message.OrgRID = reader.string()
            break
          case 4:
            message.ActionTime = reader.string()
            break
          case 5:
            message.MarkerHWID = reader.sint32()
            break
          case 6:
            message.ActionCode = reader.sint32()
            break
          case 7:
            message.Status = reader.sint32()
            break
          case 8:
            message.ParamInfo = reader.string()
            break
          case 9:
            message.Lon = reader.double()
            break
          case 10:
            message.Lat = reader.double()
            break
          case 11:
            message.CmdTime = reader.string()
            break
          case 12:
            message.RecvControllerID = reader.sint32()
            break
          case 13:
            message.ReportInfo = reader.string()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return DbMarkerHistory
  })()

  bysdb.DbMarkerPatrolHistory = (function () {
    /**
     * Properties of a DbMarkerPatrolHistory.
     * @memberof bysdb
     * @interface IDbMarkerPatrolHistory
     * @property {string|null} [NFCID] @db text
     * NFC卡片ID，hex string
     * @property {string|null} [ActionTime] @db timestamp not null
     * NFC打卡时间,utc
     * @property {number|null} [MarkerHWID] @db int
     * 界桩硬件ID
     * @property {string|null} [UserID] @db uuid not null
     * 打卡用户
     * @property {string|null} [OrgRID] @db uuid not null
     * 所属的群组
     */

    /**
     * Constructs a new DbMarkerPatrolHistory.
     * @memberof bysdb
     * @classdesc 4g界桩NFC巡查打卡历史表，以月为单位分表
     * @rpc pcrud
     * @dbend PARTITION BY RANGE (ActionTime)
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbMarkerPatrolHistoryHWID on DbMarkerPatrolHistory USING hash(MarkerHWID);
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbMarkerPatrolHistoryOrgRID on DbMarkerPatrolHistory USING hash(OrgRID);
     * @implements IDbMarkerPatrolHistory
     * @constructor
     * @param {bysdb.IDbMarkerPatrolHistory=} [properties] Properties to set
     */
    function DbMarkerPatrolHistory(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * @db text
     * NFC卡片ID，hex string
     * @member {string} NFCID
     * @memberof bysdb.DbMarkerPatrolHistory
     * @instance
     */
    DbMarkerPatrolHistory.prototype.NFCID = ''

    /**
     * @db timestamp not null
     * NFC打卡时间,utc
     * @member {string} ActionTime
     * @memberof bysdb.DbMarkerPatrolHistory
     * @instance
     */
    DbMarkerPatrolHistory.prototype.ActionTime = ''

    /**
     * @db int
     * 界桩硬件ID
     * @member {number} MarkerHWID
     * @memberof bysdb.DbMarkerPatrolHistory
     * @instance
     */
    DbMarkerPatrolHistory.prototype.MarkerHWID = 0

    /**
     * @db uuid not null
     * 打卡用户
     * @member {string} UserID
     * @memberof bysdb.DbMarkerPatrolHistory
     * @instance
     */
    DbMarkerPatrolHistory.prototype.UserID = ''

    /**
     * @db uuid not null
     * 所属的群组
     * @member {string} OrgRID
     * @memberof bysdb.DbMarkerPatrolHistory
     * @instance
     */
    DbMarkerPatrolHistory.prototype.OrgRID = ''

    /**
     * Encodes the specified DbMarkerPatrolHistory message. Does not implicitly {@link bysdb.DbMarkerPatrolHistory.verify|verify} messages.
     * @function encode
     * @memberof bysdb.DbMarkerPatrolHistory
     * @static
     * @param {bysdb.IDbMarkerPatrolHistory} message DbMarkerPatrolHistory message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    DbMarkerPatrolHistory.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (message.NFCID != null && Object.hasOwnProperty.call(message, 'NFCID'))
        writer.uint32(/* id 1, wireType 2 =*/ 10).string(message.NFCID)
      if (
        message.ActionTime != null &&
        Object.hasOwnProperty.call(message, 'ActionTime')
      )
        writer.uint32(/* id 2, wireType 2 =*/ 18).string(message.ActionTime)
      if (
        message.MarkerHWID != null &&
        Object.hasOwnProperty.call(message, 'MarkerHWID')
      )
        writer.uint32(/* id 3, wireType 0 =*/ 24).sint32(message.MarkerHWID)
      if (
        message.UserID != null &&
        Object.hasOwnProperty.call(message, 'UserID')
      )
        writer.uint32(/* id 4, wireType 2 =*/ 34).string(message.UserID)
      if (
        message.OrgRID != null &&
        Object.hasOwnProperty.call(message, 'OrgRID')
      )
        writer.uint32(/* id 5, wireType 2 =*/ 42).string(message.OrgRID)
      return writer
    }

    /**
     * Decodes a DbMarkerPatrolHistory message from the specified reader or buffer.
     * @function decode
     * @memberof bysdb.DbMarkerPatrolHistory
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {bysdb.DbMarkerPatrolHistory} DbMarkerPatrolHistory
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    DbMarkerPatrolHistory.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.bysdb.DbMarkerPatrolHistory()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.NFCID = reader.string()
            break
          case 2:
            message.ActionTime = reader.string()
            break
          case 3:
            message.MarkerHWID = reader.sint32()
            break
          case 4:
            message.UserID = reader.string()
            break
          case 5:
            message.OrgRID = reader.string()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return DbMarkerPatrolHistory
  })()

  bysdb.DbNFCPatrolLine = (function () {
    /**
     * Properties of a DbNFCPatrolLine.
     * @memberof bysdb
     * @interface IDbNFCPatrolLine
     * @property {string|null} [RID] @db uuid primary key
     * 行ID
     * @property {string|null} [OrgRID] @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 线路的归属组
     * @property {string|null} [Name] @db varchar(16) not null unique
     * 线路名称
     * @property {string|null} [Note] @db text
     * @property {string|null} [UpdatedAt] @db timestamp not null default now_utc()
     * 数据最后修改时间
     * @property {string|null} [UpdatedDC] @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */

    /**
     * Constructs a new DbNFCPatrolLine.
     * @memberof bysdb
     * @classdesc 巡查线路表
     * @rpc pcrud
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbNFCPatrolLineOrgRID on DbNFCPatrolLine USING hash(OrgRID);
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbNFCPatrolLineName on DbNFCPatrolLine USING hash(Name);
     * @implements IDbNFCPatrolLine
     * @constructor
     * @param {bysdb.IDbNFCPatrolLine=} [properties] Properties to set
     */
    function DbNFCPatrolLine(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * @db uuid primary key
     * 行ID
     * @member {string} RID
     * @memberof bysdb.DbNFCPatrolLine
     * @instance
     */
    DbNFCPatrolLine.prototype.RID = ''

    /**
     * @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 线路的归属组
     * @member {string} OrgRID
     * @memberof bysdb.DbNFCPatrolLine
     * @instance
     */
    DbNFCPatrolLine.prototype.OrgRID = ''

    /**
     * @db varchar(16) not null unique
     * 线路名称
     * @member {string} Name
     * @memberof bysdb.DbNFCPatrolLine
     * @instance
     */
    DbNFCPatrolLine.prototype.Name = ''

    /**
     * @db text
     * @member {string} Note
     * @memberof bysdb.DbNFCPatrolLine
     * @instance
     */
    DbNFCPatrolLine.prototype.Note = ''

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     * @member {string} UpdatedAt
     * @memberof bysdb.DbNFCPatrolLine
     * @instance
     */
    DbNFCPatrolLine.prototype.UpdatedAt = ''

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     * @member {string} UpdatedDC
     * @memberof bysdb.DbNFCPatrolLine
     * @instance
     */
    DbNFCPatrolLine.prototype.UpdatedDC = ''

    /**
     * Encodes the specified DbNFCPatrolLine message. Does not implicitly {@link bysdb.DbNFCPatrolLine.verify|verify} messages.
     * @function encode
     * @memberof bysdb.DbNFCPatrolLine
     * @static
     * @param {bysdb.IDbNFCPatrolLine} message DbNFCPatrolLine message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    DbNFCPatrolLine.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (message.RID != null && Object.hasOwnProperty.call(message, 'RID'))
        writer.uint32(/* id 1, wireType 2 =*/ 10).string(message.RID)
      if (
        message.OrgRID != null &&
        Object.hasOwnProperty.call(message, 'OrgRID')
      )
        writer.uint32(/* id 2, wireType 2 =*/ 18).string(message.OrgRID)
      if (message.Name != null && Object.hasOwnProperty.call(message, 'Name'))
        writer.uint32(/* id 3, wireType 2 =*/ 26).string(message.Name)
      if (message.Note != null && Object.hasOwnProperty.call(message, 'Note'))
        writer.uint32(/* id 4, wireType 2 =*/ 34).string(message.Note)
      if (
        message.UpdatedAt != null &&
        Object.hasOwnProperty.call(message, 'UpdatedAt')
      )
        writer.uint32(/* id 5, wireType 2 =*/ 42).string(message.UpdatedAt)
      if (
        message.UpdatedDC != null &&
        Object.hasOwnProperty.call(message, 'UpdatedDC')
      )
        writer.uint32(/* id 6, wireType 2 =*/ 50).string(message.UpdatedDC)
      return writer
    }

    /**
     * Decodes a DbNFCPatrolLine message from the specified reader or buffer.
     * @function decode
     * @memberof bysdb.DbNFCPatrolLine
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {bysdb.DbNFCPatrolLine} DbNFCPatrolLine
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    DbNFCPatrolLine.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.bysdb.DbNFCPatrolLine()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.RID = reader.string()
            break
          case 2:
            message.OrgRID = reader.string()
            break
          case 3:
            message.Name = reader.string()
            break
          case 4:
            message.Note = reader.string()
            break
          case 5:
            message.UpdatedAt = reader.string()
            break
          case 6:
            message.UpdatedDC = reader.string()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return DbNFCPatrolLine
  })()

  bysdb.DbNFCPatrolLineDetail = (function () {
    /**
     * Properties of a DbNFCPatrolLineDetail.
     * @memberof bysdb
     * @interface IDbNFCPatrolLineDetail
     * @property {string|null} [RID] @db uuid primary key
     * 行ID
     * @property {string|null} [LineRID] @db uuid not null REFERENCES DbNFCPatrolLine(RID) ON DELETE CASCADE
     * 归属线路
     * @property {string|null} [MarkerRID] @db uuid not null REFERENCES DbBysMarker(RID) ON DELETE CASCADE
     * 线路下的巡查点
     * @property {string|null} [UpdatedAt] @db timestamp not null default now_utc()
     * 数据最后修改时间
     * @property {string|null} [OrgRID] @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 归属组，与路线的上线一致
     */

    /**
     * Constructs a new DbNFCPatrolLineDetail.
     * @memberof bysdb
     * @classdesc 巡查线路详细表，分开主要是方便使用数据库外键约束
     * @rpc pcrud
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbNFCPatrolLineDetailOrgRID on DbNFCPatrolLineDetail USING hash(OrgRID);
     * @dbpost CREATE INDEX IF NOT EXISTS idxDDbNFCPatrolLineDetailLineRID on DbNFCPatrolLineDetail USING hash(LineRID);
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbNFCPatrolLineDetailMarkerRID on DbNFCPatrolLineDetail USING hash(MarkerRID);
     * @implements IDbNFCPatrolLineDetail
     * @constructor
     * @param {bysdb.IDbNFCPatrolLineDetail=} [properties] Properties to set
     */
    function DbNFCPatrolLineDetail(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * @db uuid primary key
     * 行ID
     * @member {string} RID
     * @memberof bysdb.DbNFCPatrolLineDetail
     * @instance
     */
    DbNFCPatrolLineDetail.prototype.RID = ''

    /**
     * @db uuid not null REFERENCES DbNFCPatrolLine(RID) ON DELETE CASCADE
     * 归属线路
     * @member {string} LineRID
     * @memberof bysdb.DbNFCPatrolLineDetail
     * @instance
     */
    DbNFCPatrolLineDetail.prototype.LineRID = ''

    /**
     * @db uuid not null REFERENCES DbBysMarker(RID) ON DELETE CASCADE
     * 线路下的巡查点
     * @member {string} MarkerRID
     * @memberof bysdb.DbNFCPatrolLineDetail
     * @instance
     */
    DbNFCPatrolLineDetail.prototype.MarkerRID = ''

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     * @member {string} UpdatedAt
     * @memberof bysdb.DbNFCPatrolLineDetail
     * @instance
     */
    DbNFCPatrolLineDetail.prototype.UpdatedAt = ''

    /**
     * @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 归属组，与路线的上线一致
     * @member {string} OrgRID
     * @memberof bysdb.DbNFCPatrolLineDetail
     * @instance
     */
    DbNFCPatrolLineDetail.prototype.OrgRID = ''

    /**
     * Encodes the specified DbNFCPatrolLineDetail message. Does not implicitly {@link bysdb.DbNFCPatrolLineDetail.verify|verify} messages.
     * @function encode
     * @memberof bysdb.DbNFCPatrolLineDetail
     * @static
     * @param {bysdb.IDbNFCPatrolLineDetail} message DbNFCPatrolLineDetail message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    DbNFCPatrolLineDetail.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (message.RID != null && Object.hasOwnProperty.call(message, 'RID'))
        writer.uint32(/* id 1, wireType 2 =*/ 10).string(message.RID)
      if (
        message.LineRID != null &&
        Object.hasOwnProperty.call(message, 'LineRID')
      )
        writer.uint32(/* id 2, wireType 2 =*/ 18).string(message.LineRID)
      if (
        message.MarkerRID != null &&
        Object.hasOwnProperty.call(message, 'MarkerRID')
      )
        writer.uint32(/* id 3, wireType 2 =*/ 26).string(message.MarkerRID)
      if (
        message.UpdatedAt != null &&
        Object.hasOwnProperty.call(message, 'UpdatedAt')
      )
        writer.uint32(/* id 4, wireType 2 =*/ 34).string(message.UpdatedAt)
      if (
        message.OrgRID != null &&
        Object.hasOwnProperty.call(message, 'OrgRID')
      )
        writer.uint32(/* id 5, wireType 2 =*/ 42).string(message.OrgRID)
      return writer
    }

    /**
     * Decodes a DbNFCPatrolLineDetail message from the specified reader or buffer.
     * @function decode
     * @memberof bysdb.DbNFCPatrolLineDetail
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {bysdb.DbNFCPatrolLineDetail} DbNFCPatrolLineDetail
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    DbNFCPatrolLineDetail.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.bysdb.DbNFCPatrolLineDetail()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.RID = reader.string()
            break
          case 2:
            message.LineRID = reader.string()
            break
          case 3:
            message.MarkerRID = reader.string()
            break
          case 4:
            message.UpdatedAt = reader.string()
            break
          case 5:
            message.OrgRID = reader.string()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return DbNFCPatrolLineDetail
  })()

  bysdb.DbNFCPatrolLineRules = (function () {
    /**
     * Properties of a DbNFCPatrolLineRules.
     * @memberof bysdb
     * @interface IDbNFCPatrolLineRules
     * @property {string|null} [RID] @db uuid primary key
     * 行ID
     * @property {string|null} [OrgRID] @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 规则的归属组
     * @property {string|null} [Name] @db varchar(16) not null unique
     * 规则名称
     * @property {boolean|null} [Day1] @db boolean default false
     * 星期一
     * @property {boolean|null} [Day2] @db boolean default false
     * 星期二
     * @property {boolean|null} [Day3] @db boolean default false
     * 星期三
     * @property {boolean|null} [Day4] @db boolean default false
     * 星期四
     * @property {boolean|null} [Day5] @db boolean default false
     * 星期五
     * @property {boolean|null} [Day6] @db boolean default false
     * 星期六
     * @property {boolean|null} [Day7] @db boolean default false
     * 星期日
     * @property {string|null} [CheckStartTime] @db time
     * 巡查开始的时间
     * @property {string|null} [CheckEndTime] @db time
     * 巡查结束的时间
     * @property {number|null} [CheckCount] @db int not null default 1
     * 巡查次数
     * @property {number|null} [EffectiveType] @db int default 0
     * 线路巡查规则生效时间的类型 0:总是生效 1:按有效时间段
     * @property {string|null} [EffectiveStart] @db timestamp
     * 规则开始生效时间
     * @property {string|null} [EffectiveEnd] @db timestamp
     * 规则生效结束时间
     * @property {string|null} [Note] @db text
     * @property {string|null} [UpdatedAt] @db timestamp not null default now_utc()
     * 数据最后修改时间
     * @property {string|null} [UpdatedDC] @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */

    /**
     * Constructs a new DbNFCPatrolLineRules.
     * @memberof bysdb
     * @classdesc 界桩NFC巡查规则
     * @rpc pcrud
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbNFCPatrolLineRulesOrgRID on DbNFCPatrolLineRules USING hash(OrgRID);
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbNFCPatrolLineRulesName on DbNFCPatrolLineRules USING hash(Name);
     * @implements IDbNFCPatrolLineRules
     * @constructor
     * @param {bysdb.IDbNFCPatrolLineRules=} [properties] Properties to set
     */
    function DbNFCPatrolLineRules(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * @db uuid primary key
     * 行ID
     * @member {string} RID
     * @memberof bysdb.DbNFCPatrolLineRules
     * @instance
     */
    DbNFCPatrolLineRules.prototype.RID = ''

    /**
     * @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 规则的归属组
     * @member {string} OrgRID
     * @memberof bysdb.DbNFCPatrolLineRules
     * @instance
     */
    DbNFCPatrolLineRules.prototype.OrgRID = ''

    /**
     * @db varchar(16) not null unique
     * 规则名称
     * @member {string} Name
     * @memberof bysdb.DbNFCPatrolLineRules
     * @instance
     */
    DbNFCPatrolLineRules.prototype.Name = ''

    /**
     * @db boolean default false
     * 星期一
     * @member {boolean} Day1
     * @memberof bysdb.DbNFCPatrolLineRules
     * @instance
     */
    DbNFCPatrolLineRules.prototype.Day1 = false

    /**
     * @db boolean default false
     * 星期二
     * @member {boolean} Day2
     * @memberof bysdb.DbNFCPatrolLineRules
     * @instance
     */
    DbNFCPatrolLineRules.prototype.Day2 = false

    /**
     * @db boolean default false
     * 星期三
     * @member {boolean} Day3
     * @memberof bysdb.DbNFCPatrolLineRules
     * @instance
     */
    DbNFCPatrolLineRules.prototype.Day3 = false

    /**
     * @db boolean default false
     * 星期四
     * @member {boolean} Day4
     * @memberof bysdb.DbNFCPatrolLineRules
     * @instance
     */
    DbNFCPatrolLineRules.prototype.Day4 = false

    /**
     * @db boolean default false
     * 星期五
     * @member {boolean} Day5
     * @memberof bysdb.DbNFCPatrolLineRules
     * @instance
     */
    DbNFCPatrolLineRules.prototype.Day5 = false

    /**
     * @db boolean default false
     * 星期六
     * @member {boolean} Day6
     * @memberof bysdb.DbNFCPatrolLineRules
     * @instance
     */
    DbNFCPatrolLineRules.prototype.Day6 = false

    /**
     * @db boolean default false
     * 星期日
     * @member {boolean} Day7
     * @memberof bysdb.DbNFCPatrolLineRules
     * @instance
     */
    DbNFCPatrolLineRules.prototype.Day7 = false

    /**
     * @db time
     * 巡查开始的时间
     * @member {string} CheckStartTime
     * @memberof bysdb.DbNFCPatrolLineRules
     * @instance
     */
    DbNFCPatrolLineRules.prototype.CheckStartTime = ''

    /**
     * @db time
     * 巡查结束的时间
     * @member {string} CheckEndTime
     * @memberof bysdb.DbNFCPatrolLineRules
     * @instance
     */
    DbNFCPatrolLineRules.prototype.CheckEndTime = ''

    /**
     * @db int not null default 1
     * 巡查次数
     * @member {number} CheckCount
     * @memberof bysdb.DbNFCPatrolLineRules
     * @instance
     */
    DbNFCPatrolLineRules.prototype.CheckCount = 0

    /**
     * @db int default 0
     * 线路巡查规则生效时间的类型 0:总是生效 1:按有效时间段
     * @member {number} EffectiveType
     * @memberof bysdb.DbNFCPatrolLineRules
     * @instance
     */
    DbNFCPatrolLineRules.prototype.EffectiveType = 0

    /**
     * @db timestamp
     * 规则开始生效时间
     * @member {string} EffectiveStart
     * @memberof bysdb.DbNFCPatrolLineRules
     * @instance
     */
    DbNFCPatrolLineRules.prototype.EffectiveStart = ''

    /**
     * @db timestamp
     * 规则生效结束时间
     * @member {string} EffectiveEnd
     * @memberof bysdb.DbNFCPatrolLineRules
     * @instance
     */
    DbNFCPatrolLineRules.prototype.EffectiveEnd = ''

    /**
     * @db text
     * @member {string} Note
     * @memberof bysdb.DbNFCPatrolLineRules
     * @instance
     */
    DbNFCPatrolLineRules.prototype.Note = ''

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     * @member {string} UpdatedAt
     * @memberof bysdb.DbNFCPatrolLineRules
     * @instance
     */
    DbNFCPatrolLineRules.prototype.UpdatedAt = ''

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     * @member {string} UpdatedDC
     * @memberof bysdb.DbNFCPatrolLineRules
     * @instance
     */
    DbNFCPatrolLineRules.prototype.UpdatedDC = ''

    /**
     * Encodes the specified DbNFCPatrolLineRules message. Does not implicitly {@link bysdb.DbNFCPatrolLineRules.verify|verify} messages.
     * @function encode
     * @memberof bysdb.DbNFCPatrolLineRules
     * @static
     * @param {bysdb.IDbNFCPatrolLineRules} message DbNFCPatrolLineRules message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    DbNFCPatrolLineRules.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (message.RID != null && Object.hasOwnProperty.call(message, 'RID'))
        writer.uint32(/* id 1, wireType 2 =*/ 10).string(message.RID)
      if (
        message.OrgRID != null &&
        Object.hasOwnProperty.call(message, 'OrgRID')
      )
        writer.uint32(/* id 2, wireType 2 =*/ 18).string(message.OrgRID)
      if (message.Name != null && Object.hasOwnProperty.call(message, 'Name'))
        writer.uint32(/* id 3, wireType 2 =*/ 26).string(message.Name)
      if (message.Day1 != null && Object.hasOwnProperty.call(message, 'Day1'))
        writer.uint32(/* id 4, wireType 0 =*/ 32).bool(message.Day1)
      if (message.Day2 != null && Object.hasOwnProperty.call(message, 'Day2'))
        writer.uint32(/* id 5, wireType 0 =*/ 40).bool(message.Day2)
      if (message.Day3 != null && Object.hasOwnProperty.call(message, 'Day3'))
        writer.uint32(/* id 6, wireType 0 =*/ 48).bool(message.Day3)
      if (message.Day4 != null && Object.hasOwnProperty.call(message, 'Day4'))
        writer.uint32(/* id 7, wireType 0 =*/ 56).bool(message.Day4)
      if (message.Day5 != null && Object.hasOwnProperty.call(message, 'Day5'))
        writer.uint32(/* id 8, wireType 0 =*/ 64).bool(message.Day5)
      if (message.Day6 != null && Object.hasOwnProperty.call(message, 'Day6'))
        writer.uint32(/* id 9, wireType 0 =*/ 72).bool(message.Day6)
      if (message.Day7 != null && Object.hasOwnProperty.call(message, 'Day7'))
        writer.uint32(/* id 10, wireType 0 =*/ 80).bool(message.Day7)
      if (
        message.CheckStartTime != null &&
        Object.hasOwnProperty.call(message, 'CheckStartTime')
      )
        writer
          .uint32(/* id 11, wireType 2 =*/ 90)
          .string(message.CheckStartTime)
      if (
        message.CheckEndTime != null &&
        Object.hasOwnProperty.call(message, 'CheckEndTime')
      )
        writer.uint32(/* id 12, wireType 2 =*/ 98).string(message.CheckEndTime)
      if (
        message.CheckCount != null &&
        Object.hasOwnProperty.call(message, 'CheckCount')
      )
        writer.uint32(/* id 13, wireType 0 =*/ 104).int32(message.CheckCount)
      if (
        message.EffectiveType != null &&
        Object.hasOwnProperty.call(message, 'EffectiveType')
      )
        writer.uint32(/* id 14, wireType 0 =*/ 112).int32(message.EffectiveType)
      if (
        message.EffectiveStart != null &&
        Object.hasOwnProperty.call(message, 'EffectiveStart')
      )
        writer
          .uint32(/* id 15, wireType 2 =*/ 122)
          .string(message.EffectiveStart)
      if (
        message.EffectiveEnd != null &&
        Object.hasOwnProperty.call(message, 'EffectiveEnd')
      )
        writer.uint32(/* id 16, wireType 2 =*/ 130).string(message.EffectiveEnd)
      if (message.Note != null && Object.hasOwnProperty.call(message, 'Note'))
        writer.uint32(/* id 17, wireType 2 =*/ 138).string(message.Note)
      if (
        message.UpdatedAt != null &&
        Object.hasOwnProperty.call(message, 'UpdatedAt')
      )
        writer.uint32(/* id 18, wireType 2 =*/ 146).string(message.UpdatedAt)
      if (
        message.UpdatedDC != null &&
        Object.hasOwnProperty.call(message, 'UpdatedDC')
      )
        writer.uint32(/* id 19, wireType 2 =*/ 154).string(message.UpdatedDC)
      return writer
    }

    /**
     * Decodes a DbNFCPatrolLineRules message from the specified reader or buffer.
     * @function decode
     * @memberof bysdb.DbNFCPatrolLineRules
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {bysdb.DbNFCPatrolLineRules} DbNFCPatrolLineRules
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    DbNFCPatrolLineRules.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.bysdb.DbNFCPatrolLineRules()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.RID = reader.string()
            break
          case 2:
            message.OrgRID = reader.string()
            break
          case 3:
            message.Name = reader.string()
            break
          case 4:
            message.Day1 = reader.bool()
            break
          case 5:
            message.Day2 = reader.bool()
            break
          case 6:
            message.Day3 = reader.bool()
            break
          case 7:
            message.Day4 = reader.bool()
            break
          case 8:
            message.Day5 = reader.bool()
            break
          case 9:
            message.Day6 = reader.bool()
            break
          case 10:
            message.Day7 = reader.bool()
            break
          case 11:
            message.CheckStartTime = reader.string()
            break
          case 12:
            message.CheckEndTime = reader.string()
            break
          case 13:
            message.CheckCount = reader.int32()
            break
          case 14:
            message.EffectiveType = reader.int32()
            break
          case 15:
            message.EffectiveStart = reader.string()
            break
          case 16:
            message.EffectiveEnd = reader.string()
            break
          case 17:
            message.Note = reader.string()
            break
          case 18:
            message.UpdatedAt = reader.string()
            break
          case 19:
            message.UpdatedDC = reader.string()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return DbNFCPatrolLineRules
  })()

  bysdb.DbNFCPatrolLineAndRules = (function () {
    /**
     * Properties of a DbNFCPatrolLineAndRules.
     * @memberof bysdb
     * @interface IDbNFCPatrolLineAndRules
     * @property {string|null} [RID] @db uuid primary key
     * 行ID
     * @property {string|null} [LineRID] @db uuid not null REFERENCES DbNFCPatrolLine(RID) ON DELETE CASCADE
     * 归属线路
     * @property {string|null} [RuleRID] @db uuid not null REFERENCES DbNFCPatrolLineRules(RID) ON DELETE CASCADE
     * 线路规则
     * @property {string|null} [UpdatedAt] @db timestamp not null default now_utc()
     * 数据最后修改时间
     * @property {string|null} [OrgRID] @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 归属组，与路线的上线一致
     */

    /**
     * Constructs a new DbNFCPatrolLineAndRules.
     * @memberof bysdb
     * @classdesc 巡查线路和规则的关系表，分开主要是方便使用数据库外键约束
     * @rpc pcrud
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbNFCPatrolLineAndRulesOrgRID on DbNFCPatrolLineAndRules USING hash(OrgRID);
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbNFCPatrolLineAndRulesLineRID on DbNFCPatrolLineAndRules USING hash(LineRID);
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbNFCPatrolLineAndRulesRuleRID on DbNFCPatrolLineAndRules USING hash(RuleRID);
     * @implements IDbNFCPatrolLineAndRules
     * @constructor
     * @param {bysdb.IDbNFCPatrolLineAndRules=} [properties] Properties to set
     */
    function DbNFCPatrolLineAndRules(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * @db uuid primary key
     * 行ID
     * @member {string} RID
     * @memberof bysdb.DbNFCPatrolLineAndRules
     * @instance
     */
    DbNFCPatrolLineAndRules.prototype.RID = ''

    /**
     * @db uuid not null REFERENCES DbNFCPatrolLine(RID) ON DELETE CASCADE
     * 归属线路
     * @member {string} LineRID
     * @memberof bysdb.DbNFCPatrolLineAndRules
     * @instance
     */
    DbNFCPatrolLineAndRules.prototype.LineRID = ''

    /**
     * @db uuid not null REFERENCES DbNFCPatrolLineRules(RID) ON DELETE CASCADE
     * 线路规则
     * @member {string} RuleRID
     * @memberof bysdb.DbNFCPatrolLineAndRules
     * @instance
     */
    DbNFCPatrolLineAndRules.prototype.RuleRID = ''

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     * @member {string} UpdatedAt
     * @memberof bysdb.DbNFCPatrolLineAndRules
     * @instance
     */
    DbNFCPatrolLineAndRules.prototype.UpdatedAt = ''

    /**
     * @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 归属组，与路线的上线一致
     * @member {string} OrgRID
     * @memberof bysdb.DbNFCPatrolLineAndRules
     * @instance
     */
    DbNFCPatrolLineAndRules.prototype.OrgRID = ''

    /**
     * Encodes the specified DbNFCPatrolLineAndRules message. Does not implicitly {@link bysdb.DbNFCPatrolLineAndRules.verify|verify} messages.
     * @function encode
     * @memberof bysdb.DbNFCPatrolLineAndRules
     * @static
     * @param {bysdb.IDbNFCPatrolLineAndRules} message DbNFCPatrolLineAndRules message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    DbNFCPatrolLineAndRules.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (message.RID != null && Object.hasOwnProperty.call(message, 'RID'))
        writer.uint32(/* id 1, wireType 2 =*/ 10).string(message.RID)
      if (
        message.LineRID != null &&
        Object.hasOwnProperty.call(message, 'LineRID')
      )
        writer.uint32(/* id 2, wireType 2 =*/ 18).string(message.LineRID)
      if (
        message.RuleRID != null &&
        Object.hasOwnProperty.call(message, 'RuleRID')
      )
        writer.uint32(/* id 3, wireType 2 =*/ 26).string(message.RuleRID)
      if (
        message.UpdatedAt != null &&
        Object.hasOwnProperty.call(message, 'UpdatedAt')
      )
        writer.uint32(/* id 4, wireType 2 =*/ 34).string(message.UpdatedAt)
      if (
        message.OrgRID != null &&
        Object.hasOwnProperty.call(message, 'OrgRID')
      )
        writer.uint32(/* id 5, wireType 2 =*/ 42).string(message.OrgRID)
      return writer
    }

    /**
     * Decodes a DbNFCPatrolLineAndRules message from the specified reader or buffer.
     * @function decode
     * @memberof bysdb.DbNFCPatrolLineAndRules
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {bysdb.DbNFCPatrolLineAndRules} DbNFCPatrolLineAndRules
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    DbNFCPatrolLineAndRules.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.bysdb.DbNFCPatrolLineAndRules()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.RID = reader.string()
            break
          case 2:
            message.LineRID = reader.string()
            break
          case 3:
            message.RuleRID = reader.string()
            break
          case 4:
            message.UpdatedAt = reader.string()
            break
          case 5:
            message.OrgRID = reader.string()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return DbNFCPatrolLineAndRules
  })()

  bysdb.CamImageData = (function () {
    /**
     * Properties of a CamImageData.
     * @memberof bysdb
     * @interface ICamImageData
     * @property {string|null} [devId] 设备IMEI
     * @property {string|null} [ccid] 4G模块自动添加
     * @property {string|null} [firmwareVersion] 固件版本
     * @property {Long|null} [timestamp] 抓拍时间戳，Unix秒时间戳
     * @property {number|null} [battery] 电池电压单位mv
     * @property {string|null} [signal] 4G信号强度
     * @property {number|null} [tempEnv] 环境温度
     * @property {number|null} [tempCpu] CPU温度
     * @property {number|null} [type] 工作类型
     * 0 – 手动
     * 1 – 唤醒拍照
     * 2 – 定时拍照
     * 3 – 唤醒录像
     * 4 – 定时录像
     * @property {number|null} [zoomRate] 以下为可选字段
     * 放大系数
     * @property {number|null} [icharge] 充电电流，合方圆太阳能充电模块支持
     * @property {number|null} [iload] 负载电流，合方圆太阳能充电模块支持
     * @property {number|null} [vcharge] 充电电压，合方圆太阳能充电模块支持
     * @property {string|null} [custData] 上传图片时，界桩自定义的json数据
     * "{\"hwid\": 14, \"type\": 2}"
     * hwid: 界桩硬件ID
     * type: 1、红外感应抓拍，2、报警抓拍，3、定时抓拍，4、手动抓拍
     */

    /**
     * Constructs a new CamImageData.
     * @memberof bysdb
     * @classdesc 合方圆摄像机上传图片时，附带的json结构体
     * @implements ICamImageData
     * @constructor
     * @param {bysdb.ICamImageData=} [properties] Properties to set
     */
    function CamImageData(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 设备IMEI
     * @member {string} devId
     * @memberof bysdb.CamImageData
     * @instance
     */
    CamImageData.prototype.devId = ''

    /**
     * 4G模块自动添加
     * @member {string} ccid
     * @memberof bysdb.CamImageData
     * @instance
     */
    CamImageData.prototype.ccid = ''

    /**
     * 固件版本
     * @member {string} firmwareVersion
     * @memberof bysdb.CamImageData
     * @instance
     */
    CamImageData.prototype.firmwareVersion = ''

    /**
     * 抓拍时间戳，Unix秒时间戳
     * @member {Long} timestamp
     * @memberof bysdb.CamImageData
     * @instance
     */
    CamImageData.prototype.timestamp = $util.Long
      ? $util.Long.fromBits(0, 0, false)
      : 0

    /**
     * 电池电压单位mv
     * @member {number} battery
     * @memberof bysdb.CamImageData
     * @instance
     */
    CamImageData.prototype.battery = 0

    /**
     * 4G信号强度
     * @member {string} signal
     * @memberof bysdb.CamImageData
     * @instance
     */
    CamImageData.prototype.signal = ''

    /**
     * 环境温度
     * @member {number} tempEnv
     * @memberof bysdb.CamImageData
     * @instance
     */
    CamImageData.prototype.tempEnv = 0

    /**
     * CPU温度
     * @member {number} tempCpu
     * @memberof bysdb.CamImageData
     * @instance
     */
    CamImageData.prototype.tempCpu = 0

    /**
     * 工作类型
     * 0 – 手动
     * 1 – 唤醒拍照
     * 2 – 定时拍照
     * 3 – 唤醒录像
     * 4 – 定时录像
     * @member {number} type
     * @memberof bysdb.CamImageData
     * @instance
     */
    CamImageData.prototype.type = 0

    /**
     * 以下为可选字段
     * 放大系数
     * @member {number} zoomRate
     * @memberof bysdb.CamImageData
     * @instance
     */
    CamImageData.prototype.zoomRate = 0

    /**
     * 充电电流，合方圆太阳能充电模块支持
     * @member {number} icharge
     * @memberof bysdb.CamImageData
     * @instance
     */
    CamImageData.prototype.icharge = 0

    /**
     * 负载电流，合方圆太阳能充电模块支持
     * @member {number} iload
     * @memberof bysdb.CamImageData
     * @instance
     */
    CamImageData.prototype.iload = 0

    /**
     * 充电电压，合方圆太阳能充电模块支持
     * @member {number} vcharge
     * @memberof bysdb.CamImageData
     * @instance
     */
    CamImageData.prototype.vcharge = 0

    /**
     * 上传图片时，界桩自定义的json数据
     * "{\"hwid\": 14, \"type\": 2}"
     * hwid: 界桩硬件ID
     * type: 1、红外感应抓拍，2、报警抓拍，3、定时抓拍，4、手动抓拍
     * @member {string} custData
     * @memberof bysdb.CamImageData
     * @instance
     */
    CamImageData.prototype.custData = ''

    /**
     * Encodes the specified CamImageData message. Does not implicitly {@link bysdb.CamImageData.verify|verify} messages.
     * @function encode
     * @memberof bysdb.CamImageData
     * @static
     * @param {bysdb.ICamImageData} message CamImageData message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    CamImageData.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (message.devId != null && Object.hasOwnProperty.call(message, 'devId'))
        writer.uint32(/* id 1, wireType 2 =*/ 10).string(message.devId)
      if (message.ccid != null && Object.hasOwnProperty.call(message, 'ccid'))
        writer.uint32(/* id 2, wireType 2 =*/ 18).string(message.ccid)
      if (
        message.firmwareVersion != null &&
        Object.hasOwnProperty.call(message, 'firmwareVersion')
      )
        writer
          .uint32(/* id 3, wireType 2 =*/ 26)
          .string(message.firmwareVersion)
      if (
        message.timestamp != null &&
        Object.hasOwnProperty.call(message, 'timestamp')
      )
        writer.uint32(/* id 4, wireType 1 =*/ 33).sfixed64(message.timestamp)
      if (
        message.battery != null &&
        Object.hasOwnProperty.call(message, 'battery')
      )
        writer.uint32(/* id 5, wireType 5 =*/ 45).float(message.battery)
      if (
        message.signal != null &&
        Object.hasOwnProperty.call(message, 'signal')
      )
        writer.uint32(/* id 6, wireType 2 =*/ 50).string(message.signal)
      if (
        message.tempEnv != null &&
        Object.hasOwnProperty.call(message, 'tempEnv')
      )
        writer.uint32(/* id 7, wireType 0 =*/ 56).sint32(message.tempEnv)
      if (
        message.tempCpu != null &&
        Object.hasOwnProperty.call(message, 'tempCpu')
      )
        writer.uint32(/* id 8, wireType 0 =*/ 64).sint32(message.tempCpu)
      if (message.type != null && Object.hasOwnProperty.call(message, 'type'))
        writer.uint32(/* id 9, wireType 0 =*/ 72).sint32(message.type)
      if (
        message.zoomRate != null &&
        Object.hasOwnProperty.call(message, 'zoomRate')
      )
        writer.uint32(/* id 10, wireType 5 =*/ 85).float(message.zoomRate)
      if (
        message.icharge != null &&
        Object.hasOwnProperty.call(message, 'icharge')
      )
        writer.uint32(/* id 11, wireType 5 =*/ 93).float(message.icharge)
      if (message.iload != null && Object.hasOwnProperty.call(message, 'iload'))
        writer.uint32(/* id 12, wireType 5 =*/ 101).float(message.iload)
      if (
        message.vcharge != null &&
        Object.hasOwnProperty.call(message, 'vcharge')
      )
        writer.uint32(/* id 13, wireType 5 =*/ 109).float(message.vcharge)
      if (
        message.custData != null &&
        Object.hasOwnProperty.call(message, 'custData')
      )
        writer.uint32(/* id 14, wireType 2 =*/ 114).string(message.custData)
      return writer
    }

    /**
     * Decodes a CamImageData message from the specified reader or buffer.
     * @function decode
     * @memberof bysdb.CamImageData
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {bysdb.CamImageData} CamImageData
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    CamImageData.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.bysdb.CamImageData()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.devId = reader.string()
            break
          case 2:
            message.ccid = reader.string()
            break
          case 3:
            message.firmwareVersion = reader.string()
            break
          case 4:
            message.timestamp = reader.sfixed64()
            break
          case 5:
            message.battery = reader.float()
            break
          case 6:
            message.signal = reader.string()
            break
          case 7:
            message.tempEnv = reader.sint32()
            break
          case 8:
            message.tempCpu = reader.sint32()
            break
          case 9:
            message.type = reader.sint32()
            break
          case 10:
            message.zoomRate = reader.float()
            break
          case 11:
            message.icharge = reader.float()
            break
          case 12:
            message.iload = reader.float()
            break
          case 13:
            message.vcharge = reader.float()
            break
          case 14:
            message.custData = reader.string()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return CamImageData
  })()

  bysdb.DbMarkerUploadImageHistory = (function () {
    /**
     * Properties of a DbMarkerUploadImageHistory.
     * @memberof bysdb
     * @interface IDbMarkerUploadImageHistory
     * @property {string|null} [OrgRID] @db uuid not null
     * 所属的群组
     * @property {number|null} [MarkerHWID] @db int
     * 界桩硬件ID
     * @property {string|null} [CaptureTime] @db timestamp not null
     * 抓拍时间,utc
     * @property {number|null} [CaptureType] @db int
     * 抓拍的动作类型，与CamImageData.type一致
     * 0 – 手动
     * 1 – 唤醒拍照
     * 2 – 定时拍照
     * 3 – 唤醒录像
     * 4 – 定时录像
     * @property {string|null} [UploadTime] @db timestamp not null
     * 服务器接收上传时间,utc
     * @property {string|null} [FileName] @db text
     * 上传的图片保存路径，f512673f-0f6a-4b54-95bf-d377b78fba1a.jpg
     * @property {string|null} [FormData] @db jsonb not null default  '{}'::jsonb
     * 界桩上传图片时附带的json数据，CamImageData json字符串
     */

    /**
     * Constructs a new DbMarkerUploadImageHistory.
     * @memberof bysdb
     * @classdesc 4g界桩上传的图片历史表，以月为单位分表
     * @rpc pcrud
     * @dbend PARTITION BY RANGE (UploadTime)
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbMarkerUploadImageHistoryHWID on DbMarkerUploadImageHistory USING hash(MarkerHWID);
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbMarkerUploadImageHistoryOrgRID on DbMarkerUploadImageHistory USING hash(OrgRID);
     * @implements IDbMarkerUploadImageHistory
     * @constructor
     * @param {bysdb.IDbMarkerUploadImageHistory=} [properties] Properties to set
     */
    function DbMarkerUploadImageHistory(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * @db uuid not null
     * 所属的群组
     * @member {string} OrgRID
     * @memberof bysdb.DbMarkerUploadImageHistory
     * @instance
     */
    DbMarkerUploadImageHistory.prototype.OrgRID = ''

    /**
     * @db int
     * 界桩硬件ID
     * @member {number} MarkerHWID
     * @memberof bysdb.DbMarkerUploadImageHistory
     * @instance
     */
    DbMarkerUploadImageHistory.prototype.MarkerHWID = 0

    /**
     * @db timestamp not null
     * 抓拍时间,utc
     * @member {string} CaptureTime
     * @memberof bysdb.DbMarkerUploadImageHistory
     * @instance
     */
    DbMarkerUploadImageHistory.prototype.CaptureTime = ''

    /**
     * @db int
     * 抓拍的动作类型，与CamImageData.type一致
     * 0 – 手动
     * 1 – 唤醒拍照
     * 2 – 定时拍照
     * 3 – 唤醒录像
     * 4 – 定时录像
     * @member {number} CaptureType
     * @memberof bysdb.DbMarkerUploadImageHistory
     * @instance
     */
    DbMarkerUploadImageHistory.prototype.CaptureType = 0

    /**
     * @db timestamp not null
     * 服务器接收上传时间,utc
     * @member {string} UploadTime
     * @memberof bysdb.DbMarkerUploadImageHistory
     * @instance
     */
    DbMarkerUploadImageHistory.prototype.UploadTime = ''

    /**
     * @db text
     * 上传的图片保存路径，f512673f-0f6a-4b54-95bf-d377b78fba1a.jpg
     * @member {string} FileName
     * @memberof bysdb.DbMarkerUploadImageHistory
     * @instance
     */
    DbMarkerUploadImageHistory.prototype.FileName = ''

    /**
     * @db jsonb not null default  '{}'::jsonb
     * 界桩上传图片时附带的json数据，CamImageData json字符串
     * @member {string} FormData
     * @memberof bysdb.DbMarkerUploadImageHistory
     * @instance
     */
    DbMarkerUploadImageHistory.prototype.FormData = ''

    /**
     * Encodes the specified DbMarkerUploadImageHistory message. Does not implicitly {@link bysdb.DbMarkerUploadImageHistory.verify|verify} messages.
     * @function encode
     * @memberof bysdb.DbMarkerUploadImageHistory
     * @static
     * @param {bysdb.IDbMarkerUploadImageHistory} message DbMarkerUploadImageHistory message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    DbMarkerUploadImageHistory.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (
        message.OrgRID != null &&
        Object.hasOwnProperty.call(message, 'OrgRID')
      )
        writer.uint32(/* id 1, wireType 2 =*/ 10).string(message.OrgRID)
      if (
        message.MarkerHWID != null &&
        Object.hasOwnProperty.call(message, 'MarkerHWID')
      )
        writer.uint32(/* id 2, wireType 0 =*/ 16).sint32(message.MarkerHWID)
      if (
        message.CaptureTime != null &&
        Object.hasOwnProperty.call(message, 'CaptureTime')
      )
        writer.uint32(/* id 3, wireType 2 =*/ 26).string(message.CaptureTime)
      if (
        message.CaptureType != null &&
        Object.hasOwnProperty.call(message, 'CaptureType')
      )
        writer.uint32(/* id 4, wireType 0 =*/ 32).sint32(message.CaptureType)
      if (
        message.UploadTime != null &&
        Object.hasOwnProperty.call(message, 'UploadTime')
      )
        writer.uint32(/* id 5, wireType 2 =*/ 42).string(message.UploadTime)
      if (
        message.FileName != null &&
        Object.hasOwnProperty.call(message, 'FileName')
      )
        writer.uint32(/* id 6, wireType 2 =*/ 50).string(message.FileName)
      if (
        message.FormData != null &&
        Object.hasOwnProperty.call(message, 'FormData')
      )
        writer.uint32(/* id 7, wireType 2 =*/ 58).string(message.FormData)
      return writer
    }

    /**
     * Decodes a DbMarkerUploadImageHistory message from the specified reader or buffer.
     * @function decode
     * @memberof bysdb.DbMarkerUploadImageHistory
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {bysdb.DbMarkerUploadImageHistory} DbMarkerUploadImageHistory
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    DbMarkerUploadImageHistory.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.bysdb.DbMarkerUploadImageHistory()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.OrgRID = reader.string()
            break
          case 2:
            message.MarkerHWID = reader.sint32()
            break
          case 3:
            message.CaptureTime = reader.string()
            break
          case 4:
            message.CaptureType = reader.sint32()
            break
          case 5:
            message.UploadTime = reader.string()
            break
          case 6:
            message.FileName = reader.string()
            break
          case 7:
            message.FormData = reader.string()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return DbMarkerUploadImageHistory
  })()

  return bysdb
})())

export { $root as default }

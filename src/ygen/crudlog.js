/*eslint-disable block-scoped-var, id-length, no-control-regex, no-magic-numbers, no-prototype-builtins, no-redeclare, no-shadow, no-var, sort-vars*/
import * as $protobuf from 'protobufjs/minimal'

// Common aliases
const $Reader = $protobuf.Reader,
  $Writer = $protobuf.Writer,
  $util = $protobuf.util

// Exported root namespace
const $root = $protobuf.roots['default'] || ($protobuf.roots['default'] = {})

export const crudlog = ($root.crudlog = (() => {
  /**
   * Namespace crudlog.
   * @exports crudlog
   * @namespace
   */
  const crudlog = $root.crudlog || {}

  crudlog.DbCrudLog = (function () {
    /**
     * Properties of a DbCrudLog.
     * @memberof crudlog
     * @interface IDbCrudLog
     * @property {string|null} [OrgRID] @db uuid
     * 用户所属的群组
     * @property {string|null} [UserRID] @db uuid
     * 用户rid
     * @property {string|null} [Operation] @db text
     * 操作
     * @property {string|null} [Req] @db jsonb
     * req proto json
     * @property {string|null} [ReqOption] @db text
     * req option
     * @property {string|null} [Ipinfo] @db text
     * ipinfo
     * @property {string|null} [Note] @db jsonb
     * note
     * @property {string|null} [UpdatedAt] @db timestamp not null default now_utc()
     * 数据最后修改时间
     * @property {string|null} [UpdatedDC] @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */

    /**
     * Constructs a new DbCrudLog.
     * @memberof crudlog
     * @classdesc 用户crud log表
     * @rpc crud pcrud
     * @dbend PARTITION BY RANGE (UpdatedAt)
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbCrudLogOrgRID on DbCrudLog USING hash(OrgRID);
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbCrudLogUpdatedAt on DbCrudLog USING brin(UpdatedAt);
     * @implements IDbCrudLog
     * @constructor
     * @param {crudlog.IDbCrudLog=} [properties] Properties to set
     */
    function DbCrudLog(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * @db uuid
     * 用户所属的群组
     * @member {string} OrgRID
     * @memberof crudlog.DbCrudLog
     * @instance
     */
    DbCrudLog.prototype.OrgRID = ''

    /**
     * @db uuid
     * 用户rid
     * @member {string} UserRID
     * @memberof crudlog.DbCrudLog
     * @instance
     */
    DbCrudLog.prototype.UserRID = ''

    /**
     * @db text
     * 操作
     * @member {string} Operation
     * @memberof crudlog.DbCrudLog
     * @instance
     */
    DbCrudLog.prototype.Operation = ''

    /**
     * @db jsonb
     * req proto json
     * @member {string} Req
     * @memberof crudlog.DbCrudLog
     * @instance
     */
    DbCrudLog.prototype.Req = ''

    /**
     * @db text
     * req option
     * @member {string} ReqOption
     * @memberof crudlog.DbCrudLog
     * @instance
     */
    DbCrudLog.prototype.ReqOption = ''

    /**
     * @db text
     * ipinfo
     * @member {string} Ipinfo
     * @memberof crudlog.DbCrudLog
     * @instance
     */
    DbCrudLog.prototype.Ipinfo = ''

    /**
     * @db jsonb
     * note
     * @member {string} Note
     * @memberof crudlog.DbCrudLog
     * @instance
     */
    DbCrudLog.prototype.Note = ''

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     * @member {string} UpdatedAt
     * @memberof crudlog.DbCrudLog
     * @instance
     */
    DbCrudLog.prototype.UpdatedAt = ''

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     * @member {string} UpdatedDC
     * @memberof crudlog.DbCrudLog
     * @instance
     */
    DbCrudLog.prototype.UpdatedDC = ''

    /**
     * Encodes the specified DbCrudLog message. Does not implicitly {@link crudlog.DbCrudLog.verify|verify} messages.
     * @function encode
     * @memberof crudlog.DbCrudLog
     * @static
     * @param {crudlog.IDbCrudLog} message DbCrudLog message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    DbCrudLog.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (
        message.OrgRID != null &&
        Object.hasOwnProperty.call(message, 'OrgRID')
      )
        writer.uint32(/* id 2, wireType 2 =*/ 18).string(message.OrgRID)
      if (
        message.UserRID != null &&
        Object.hasOwnProperty.call(message, 'UserRID')
      )
        writer.uint32(/* id 3, wireType 2 =*/ 26).string(message.UserRID)
      if (
        message.Operation != null &&
        Object.hasOwnProperty.call(message, 'Operation')
      )
        writer.uint32(/* id 4, wireType 2 =*/ 34).string(message.Operation)
      if (message.Req != null && Object.hasOwnProperty.call(message, 'Req'))
        writer.uint32(/* id 5, wireType 2 =*/ 42).string(message.Req)
      if (
        message.ReqOption != null &&
        Object.hasOwnProperty.call(message, 'ReqOption')
      )
        writer.uint32(/* id 6, wireType 2 =*/ 50).string(message.ReqOption)
      if (
        message.Ipinfo != null &&
        Object.hasOwnProperty.call(message, 'Ipinfo')
      )
        writer.uint32(/* id 7, wireType 2 =*/ 58).string(message.Ipinfo)
      if (message.Note != null && Object.hasOwnProperty.call(message, 'Note'))
        writer.uint32(/* id 8, wireType 2 =*/ 66).string(message.Note)
      if (
        message.UpdatedAt != null &&
        Object.hasOwnProperty.call(message, 'UpdatedAt')
      )
        writer.uint32(/* id 14, wireType 2 =*/ 114).string(message.UpdatedAt)
      if (
        message.UpdatedDC != null &&
        Object.hasOwnProperty.call(message, 'UpdatedDC')
      )
        writer.uint32(/* id 15, wireType 2 =*/ 122).string(message.UpdatedDC)
      return writer
    }

    /**
     * Decodes a DbCrudLog message from the specified reader or buffer.
     * @function decode
     * @memberof crudlog.DbCrudLog
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {crudlog.DbCrudLog} DbCrudLog
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    DbCrudLog.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.crudlog.DbCrudLog()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 2:
            message.OrgRID = reader.string()
            break
          case 3:
            message.UserRID = reader.string()
            break
          case 4:
            message.Operation = reader.string()
            break
          case 5:
            message.Req = reader.string()
            break
          case 6:
            message.ReqOption = reader.string()
            break
          case 7:
            message.Ipinfo = reader.string()
            break
          case 8:
            message.Note = reader.string()
            break
          case 14:
            message.UpdatedAt = reader.string()
            break
          case 15:
            message.UpdatedDC = reader.string()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return DbCrudLog
  })()

  return crudlog
})())

export { $root as default }

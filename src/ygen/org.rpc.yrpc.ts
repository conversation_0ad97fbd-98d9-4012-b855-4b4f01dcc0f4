import { Writer } from 'protobufjs'
import { rpcCon } from 'jsyrpc'
import { ICallOption } from 'jsyrpc'

import { TRpcStream } from 'jsyrpc'
import * as OrgY from '@ygen/org'
import * as CrudY from '@ygen/crud'
import * as OrgListY from '@ygen/org.list'

export function RpcDbOrgInsert(
  req: OrgY.org.IDbOrg,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = OrgY.org.DbOrg.encode(req)
  let reqData = w.finish()

  const api = '/org.RpcDbOrg/Insert'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function RpcDbOrgUpdate(
  req: OrgY.org.IDbOrg,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = OrgY.org.DbOrg.encode(req)
  let reqData = w.finish()

  const api = '/org.RpcDbOrg/Update'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function RpcDbOrgPartialUpdate(
  req: OrgY.org.IDbOrg,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = OrgY.org.DbOrg.encode(req)
  let reqData = w.finish()

  const api = '/org.RpcDbOrg/PartialUpdate'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function RpcDbOrgDelete(
  req: OrgY.org.IDbOrg,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = OrgY.org.DbOrg.encode(req)
  let reqData = w.finish()

  const api = '/org.RpcDbOrg/Delete'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function RpcDbOrgSelectOne(
  req: CrudY.crud.IDMLParam,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = CrudY.crud.DMLParam.encode(req)
  let reqData = w.finish()

  const api = '/org.RpcDbOrg/SelectOne'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, OrgY.org.DbOrg, callOpt)
}

export function RpcDbOrgSelectMany(
  req: CrudY.crud.DMLParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/org.RpcDbOrg/SelectMany'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.DMLParam,
    OrgY.org.DbOrg,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function RpcDbOrgQuery(
  req: CrudY.crud.QueryParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/org.RpcDbOrg/Query'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.QueryParam,
    OrgY.org.DbOrg,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function RpcDbOrgQueryBatch(
  req: CrudY.crud.QueryParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/org.RpcDbOrg/QueryBatch'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.QueryParam,
    OrgListY.org.DbOrgList,
    callOpt,
  )
  r.sendFirst(req)
  return r
}

export class RpcDbOrg {
  public static Insert(req: OrgY.org.IDbOrg, callOpt?: ICallOption): boolean {
    return RpcDbOrgInsert(req, callOpt)
  }
  public static Update(req: OrgY.org.IDbOrg, callOpt?: ICallOption): boolean {
    return RpcDbOrgUpdate(req, callOpt)
  }
  public static PartialUpdate(
    req: OrgY.org.IDbOrg,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbOrgPartialUpdate(req, callOpt)
  }
  public static Delete(req: OrgY.org.IDbOrg, callOpt?: ICallOption): boolean {
    return RpcDbOrgDelete(req, callOpt)
  }
  public static SelectOne(
    req: CrudY.crud.IDMLParam,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbOrgSelectOne(req, callOpt)
  }
  public static SelectMany(
    req: CrudY.crud.DMLParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return RpcDbOrgSelectMany(req, callOpt)
  }
  public static Query(
    req: CrudY.crud.QueryParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return RpcDbOrgQuery(req, callOpt)
  }
  public static QueryBatch(
    req: CrudY.crud.QueryParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return RpcDbOrgQueryBatch(req, callOpt)
  }
}

export function PrpcDbOrgInsert(
  req: OrgY.org.IDbOrg,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = OrgY.org.DbOrg.encode(req)
  let reqData = w.finish()

  const api = '/org.PrpcDbOrg/Insert'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function PrpcDbOrgUpdate(
  req: OrgY.org.IDbOrg,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = OrgY.org.DbOrg.encode(req)
  let reqData = w.finish()

  const api = '/org.PrpcDbOrg/Update'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function PrpcDbOrgPartialUpdate(
  req: OrgY.org.IDbOrg,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = OrgY.org.DbOrg.encode(req)
  let reqData = w.finish()

  const api = '/org.PrpcDbOrg/PartialUpdate'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function PrpcDbOrgDelete(
  req: OrgY.org.IDbOrg,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = OrgY.org.DbOrg.encode(req)
  let reqData = w.finish()

  const api = '/org.PrpcDbOrg/Delete'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function PrpcDbOrgSelectOne(
  req: CrudY.crud.IDMLParam,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = CrudY.crud.DMLParam.encode(req)
  let reqData = w.finish()

  const api = '/org.PrpcDbOrg/SelectOne'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, OrgY.org.DbOrg, callOpt)
}

export function PrpcDbOrgSelectMany(
  req: CrudY.crud.DMLParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/org.PrpcDbOrg/SelectMany'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.DMLParam,
    OrgY.org.DbOrg,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function PrpcDbOrgQuery(
  req: CrudY.crud.PrivilegeParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/org.PrpcDbOrg/Query'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.PrivilegeParam,
    OrgY.org.DbOrg,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function PrpcDbOrgQueryBatch(
  req: CrudY.crud.PrivilegeParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/org.PrpcDbOrg/QueryBatch'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.PrivilegeParam,
    OrgListY.org.DbOrgList,
    callOpt,
  )
  r.sendFirst(req)
  return r
}

export class PrpcDbOrg {
  public static Insert(req: OrgY.org.IDbOrg, callOpt?: ICallOption): boolean {
    return PrpcDbOrgInsert(req, callOpt)
  }
  public static Update(req: OrgY.org.IDbOrg, callOpt?: ICallOption): boolean {
    return PrpcDbOrgUpdate(req, callOpt)
  }
  public static PartialUpdate(
    req: OrgY.org.IDbOrg,
    callOpt?: ICallOption,
  ): boolean {
    return PrpcDbOrgPartialUpdate(req, callOpt)
  }
  public static Delete(req: OrgY.org.IDbOrg, callOpt?: ICallOption): boolean {
    return PrpcDbOrgDelete(req, callOpt)
  }
  public static SelectOne(
    req: CrudY.crud.IDMLParam,
    callOpt?: ICallOption,
  ): boolean {
    return PrpcDbOrgSelectOne(req, callOpt)
  }
  public static SelectMany(
    req: CrudY.crud.DMLParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return PrpcDbOrgSelectMany(req, callOpt)
  }
  public static Query(
    req: CrudY.crud.PrivilegeParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return PrpcDbOrgQuery(req, callOpt)
  }
  public static QueryBatch(
    req: CrudY.crud.PrivilegeParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return PrpcDbOrgQueryBatch(req, callOpt)
  }
}
export default {
  RpcDbOrg,
  RpcDbOrgInsert,
  RpcDbOrgUpdate,
  RpcDbOrgPartialUpdate,
  RpcDbOrgDelete,
  RpcDbOrgSelectOne,
  RpcDbOrgSelectMany,
  RpcDbOrgQuery,
  RpcDbOrgQueryBatch,
  PrpcDbOrg,
  PrpcDbOrgInsert,
  PrpcDbOrgUpdate,
  PrpcDbOrgPartialUpdate,
  PrpcDbOrgDelete,
  PrpcDbOrgSelectOne,
  PrpcDbOrgSelectMany,
  PrpcDbOrgQuery,
  PrpcDbOrgQueryBatch,
}

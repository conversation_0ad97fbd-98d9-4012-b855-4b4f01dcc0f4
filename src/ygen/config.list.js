/*eslint-disable block-scoped-var, id-length, no-control-regex, no-magic-numbers, no-prototype-builtins, no-redeclare, no-shadow, no-var, sort-vars*/
import * as $protobuf from 'protobufjs/minimal'

// Common aliases
const $Reader = $protobuf.Reader,
  $Writer = $protobuf.Writer,
  $util = $protobuf.util

// Exported root namespace
const $root = $protobuf.roots['default'] || ($protobuf.roots['default'] = {})

export const config = ($root.config = (() => {
  /**
   * Namespace config.
   * @exports config
   * @namespace
   */
  const config = $root.config || {}

  config.DbConfigList = (function () {
    /**
     * Properties of a DbConfigList.
     * @memberof config
     * @interface IDbConfigList
     * @property {number|null} [BatchNo] 批次号，从0开始
     * @property {number|null} [RowsOffset] rows offset,从0开始
     * @property {Array.<config.IDbConfig>|null} [Rows] 本批次数据
     */

    /**
     * Constructs a new DbConfigList.
     * @memberof config
     * @classdesc DbConfig list
     * @implements IDbConfigList
     * @constructor
     * @param {config.IDbConfigList=} [properties] Properties to set
     */
    function DbConfigList(properties) {
      this.Rows = []
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 批次号，从0开始
     * @member {number} BatchNo
     * @memberof config.DbConfigList
     * @instance
     */
    DbConfigList.prototype.BatchNo = 0

    /**
     * rows offset,从0开始
     * @member {number} RowsOffset
     * @memberof config.DbConfigList
     * @instance
     */
    DbConfigList.prototype.RowsOffset = 0

    /**
     * 本批次数据
     * @member {Array.<config.IDbConfig>} Rows
     * @memberof config.DbConfigList
     * @instance
     */
    DbConfigList.prototype.Rows = $util.emptyArray

    /**
     * Encodes the specified DbConfigList message. Does not implicitly {@link config.DbConfigList.verify|verify} messages.
     * @function encode
     * @memberof config.DbConfigList
     * @static
     * @param {config.IDbConfigList} message DbConfigList message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    DbConfigList.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (
        message.BatchNo != null &&
        Object.hasOwnProperty.call(message, 'BatchNo')
      )
        writer.uint32(/* id 1, wireType 0 =*/ 8).uint32(message.BatchNo)
      if (
        message.RowsOffset != null &&
        Object.hasOwnProperty.call(message, 'RowsOffset')
      )
        writer.uint32(/* id 2, wireType 0 =*/ 16).uint32(message.RowsOffset)
      if (message.Rows != null && message.Rows.length)
        for (let i = 0; i < message.Rows.length; ++i)
          $root.config.DbConfig.encode(
            message.Rows[i],
            writer.uint32(/* id 3, wireType 2 =*/ 26).fork(),
          ).ldelim()
      return writer
    }

    /**
     * Decodes a DbConfigList message from the specified reader or buffer.
     * @function decode
     * @memberof config.DbConfigList
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {config.DbConfigList} DbConfigList
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    DbConfigList.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.config.DbConfigList()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.BatchNo = reader.uint32()
            break
          case 2:
            message.RowsOffset = reader.uint32()
            break
          case 3:
            if (!(message.Rows && message.Rows.length)) message.Rows = []
            message.Rows.push(
              $root.config.DbConfig.decode(reader, reader.uint32()),
            )
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return DbConfigList
  })()

  config.DbConfig = (function () {
    /**
     * Properties of a DbConfig.
     * @memberof config
     * @interface IDbConfig
     * @property {string|null} [RID] @db uuid primary key
     * 行ID
     * @property {string|null} [OrgRID] @db uuid  REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 所属的群组，如果是全系统通用的配置，此项为NULL
     * @property {string|null} [ConfKey] @db text not null
     * 配置名
     * @property {string|null} [ConfValue] @db text
     * 配置的值
     * @property {string|null} [UpdatedAt] @db timestamp not null default now_utc()
     * 数据最后修改时间
     * @property {string|null} [UpdatedDC] @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */

    /**
     * Constructs a new DbConfig.
     * @memberof config
     * @classdesc 系统配置信息表
     * @rpc crud
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbConfigOrgRID on DbConfig USING hash(OrgRID)
     * @implements IDbConfig
     * @constructor
     * @param {config.IDbConfig=} [properties] Properties to set
     */
    function DbConfig(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * @db uuid primary key
     * 行ID
     * @member {string} RID
     * @memberof config.DbConfig
     * @instance
     */
    DbConfig.prototype.RID = ''

    /**
     * @db uuid  REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 所属的群组，如果是全系统通用的配置，此项为NULL
     * @member {string} OrgRID
     * @memberof config.DbConfig
     * @instance
     */
    DbConfig.prototype.OrgRID = ''

    /**
     * @db text not null
     * 配置名
     * @member {string} ConfKey
     * @memberof config.DbConfig
     * @instance
     */
    DbConfig.prototype.ConfKey = ''

    /**
     * @db text
     * 配置的值
     * @member {string} ConfValue
     * @memberof config.DbConfig
     * @instance
     */
    DbConfig.prototype.ConfValue = ''

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     * @member {string} UpdatedAt
     * @memberof config.DbConfig
     * @instance
     */
    DbConfig.prototype.UpdatedAt = ''

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     * @member {string} UpdatedDC
     * @memberof config.DbConfig
     * @instance
     */
    DbConfig.prototype.UpdatedDC = ''

    /**
     * Encodes the specified DbConfig message. Does not implicitly {@link config.DbConfig.verify|verify} messages.
     * @function encode
     * @memberof config.DbConfig
     * @static
     * @param {config.IDbConfig} message DbConfig message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    DbConfig.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (message.RID != null && Object.hasOwnProperty.call(message, 'RID'))
        writer.uint32(/* id 1, wireType 2 =*/ 10).string(message.RID)
      if (
        message.OrgRID != null &&
        Object.hasOwnProperty.call(message, 'OrgRID')
      )
        writer.uint32(/* id 2, wireType 2 =*/ 18).string(message.OrgRID)
      if (
        message.ConfKey != null &&
        Object.hasOwnProperty.call(message, 'ConfKey')
      )
        writer.uint32(/* id 3, wireType 2 =*/ 26).string(message.ConfKey)
      if (
        message.ConfValue != null &&
        Object.hasOwnProperty.call(message, 'ConfValue')
      )
        writer.uint32(/* id 4, wireType 2 =*/ 34).string(message.ConfValue)
      if (
        message.UpdatedAt != null &&
        Object.hasOwnProperty.call(message, 'UpdatedAt')
      )
        writer.uint32(/* id 14, wireType 2 =*/ 114).string(message.UpdatedAt)
      if (
        message.UpdatedDC != null &&
        Object.hasOwnProperty.call(message, 'UpdatedDC')
      )
        writer.uint32(/* id 15, wireType 2 =*/ 122).string(message.UpdatedDC)
      return writer
    }

    /**
     * Decodes a DbConfig message from the specified reader or buffer.
     * @function decode
     * @memberof config.DbConfig
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {config.DbConfig} DbConfig
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    DbConfig.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.config.DbConfig()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.RID = reader.string()
            break
          case 2:
            message.OrgRID = reader.string()
            break
          case 3:
            message.ConfKey = reader.string()
            break
          case 4:
            message.ConfValue = reader.string()
            break
          case 14:
            message.UpdatedAt = reader.string()
            break
          case 15:
            message.UpdatedDC = reader.string()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return DbConfig
  })()

  return config
})())

export { $root as default }

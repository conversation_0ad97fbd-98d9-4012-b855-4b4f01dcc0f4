/*eslint-disable block-scoped-var, id-length, no-control-regex, no-magic-numbers, no-prototype-builtins, no-redeclare, no-shadow, no-var, sort-vars*/
import * as $protobuf from 'protobufjs/minimal'

// Common aliases
const $Reader = $protobuf.Reader,
  $Writer = $protobuf.Writer,
  $util = $protobuf.util

// Exported root namespace
const $root = $protobuf.roots['default'] || ($protobuf.roots['default'] = {})

export const bysproto = ($root.bysproto = (() => {
  /**
   * Namespace bysproto.
   * @exports bysproto
   * @namespace
   */
  const bysproto = $root.bysproto || {}

  bysproto.Bmsg = (function () {
    /**
     * Properties of a Bmsg.
     * @memberof bysproto
     * @interface IBmsg
     * @property {number|null} [Cmd] 命令字
     * 1:登录
     * 2:界桩打卡上传
     * 12:回应界桩上传
     * 13：修改控制器手台监听信道号
     * 15：更新控制器新的注册地址
     * 11:ping
     * ----4G界桩新增----
     * 21：信息上报
     * 31：上报应答
     * 22：报警
     * 23：拓展报警(外接传感器)
     * 32：报警应答
     * 33：报警解除
     * 28: 报警解除应答
     * 24：参数更新
     * 34：参数更新应答
     * 25：软件更新(预留)
     * 35：软件更新应答(预留)
     * 26：主动抓拍(预留)
     * 36：主动抓拍应答(预留)
     * 27：遥闭
     * 37：遥闭应答
     * @property {number|null} [No] 从0开始增1使用,用于区分相同命令重复的包
     * @property {number|null} [Res] response code
     * @property {Uint8Array|null} [Body] msg body
     * @property {string|null} [Optstr] optional str parameter,额外的信息,一般不会有,有些特殊命令里面可能用到
     * @property {Uint8Array|null} [Optbin] optional binary parameter,额外的信息,一般不会有,有些特殊命令里面可能用到
     */

    /**
     * Constructs a new Bmsg.
     * @memberof bysproto
     * @classdesc 系统与控制器所有的消息交互底层都以此为包装
     * @implements IBmsg
     * @constructor
     * @param {bysproto.IBmsg=} [properties] Properties to set
     */
    function Bmsg(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 命令字
     * 1:登录
     * 2:界桩打卡上传
     * 12:回应界桩上传
     * 13：修改控制器手台监听信道号
     * 15：更新控制器新的注册地址
     * 11:ping
     * ----4G界桩新增----
     * 21：信息上报
     * 31：上报应答
     * 22：报警
     * 23：拓展报警(外接传感器)
     * 32：报警应答
     * 33：报警解除
     * 28: 报警解除应答
     * 24：参数更新
     * 34：参数更新应答
     * 25：软件更新(预留)
     * 35：软件更新应答(预留)
     * 26：主动抓拍(预留)
     * 36：主动抓拍应答(预留)
     * 27：遥闭
     * 37：遥闭应答
     * @member {number} Cmd
     * @memberof bysproto.Bmsg
     * @instance
     */
    Bmsg.prototype.Cmd = 0

    /**
     * 从0开始增1使用,用于区分相同命令重复的包
     * @member {number} No
     * @memberof bysproto.Bmsg
     * @instance
     */
    Bmsg.prototype.No = 0

    /**
     * response code
     * @member {number} Res
     * @memberof bysproto.Bmsg
     * @instance
     */
    Bmsg.prototype.Res = 0

    /**
     * msg body
     * @member {Uint8Array} Body
     * @memberof bysproto.Bmsg
     * @instance
     */
    Bmsg.prototype.Body = $util.newBuffer([])

    /**
     * optional str parameter,额外的信息,一般不会有,有些特殊命令里面可能用到
     * @member {string} Optstr
     * @memberof bysproto.Bmsg
     * @instance
     */
    Bmsg.prototype.Optstr = ''

    /**
     * optional binary parameter,额外的信息,一般不会有,有些特殊命令里面可能用到
     * @member {Uint8Array} Optbin
     * @memberof bysproto.Bmsg
     * @instance
     */
    Bmsg.prototype.Optbin = $util.newBuffer([])

    /**
     * Encodes the specified Bmsg message. Does not implicitly {@link bysproto.Bmsg.verify|verify} messages.
     * @function encode
     * @memberof bysproto.Bmsg
     * @static
     * @param {bysproto.IBmsg} message Bmsg message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    Bmsg.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (message.Cmd != null && Object.hasOwnProperty.call(message, 'Cmd'))
        writer.uint32(/* id 2, wireType 5 =*/ 21).fixed32(message.Cmd)
      if (message.No != null && Object.hasOwnProperty.call(message, 'No'))
        writer.uint32(/* id 5, wireType 0 =*/ 40).uint32(message.No)
      if (message.Res != null && Object.hasOwnProperty.call(message, 'Res'))
        writer.uint32(/* id 9, wireType 0 =*/ 72).sint32(message.Res)
      if (message.Body != null && Object.hasOwnProperty.call(message, 'Body'))
        writer.uint32(/* id 10, wireType 2 =*/ 82).bytes(message.Body)
      if (
        message.Optstr != null &&
        Object.hasOwnProperty.call(message, 'Optstr')
      )
        writer.uint32(/* id 11, wireType 2 =*/ 90).string(message.Optstr)
      if (
        message.Optbin != null &&
        Object.hasOwnProperty.call(message, 'Optbin')
      )
        writer.uint32(/* id 12, wireType 2 =*/ 98).bytes(message.Optbin)
      return writer
    }

    /**
     * Decodes a Bmsg message from the specified reader or buffer.
     * @function decode
     * @memberof bysproto.Bmsg
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {bysproto.Bmsg} Bmsg
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    Bmsg.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.bysproto.Bmsg()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 2:
            message.Cmd = reader.fixed32()
            break
          case 5:
            message.No = reader.uint32()
            break
          case 9:
            message.Res = reader.sint32()
            break
          case 10:
            message.Body = reader.bytes()
            break
          case 11:
            message.Optstr = reader.string()
            break
          case 12:
            message.Optbin = reader.bytes()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return Bmsg
  })()

  bysproto.BloginReq = (function () {
    /**
     * Properties of a BloginReq.
     * @memberof bysproto
     * @interface IBloginReq
     * @property {string|null} [Name] 控制器名称，不是控制器硬件ID
     * @property {string|null} [PassHash] base64(sha256(base64(sha256(controllerName+controllerPass))+LoginTimeStr))
     * 4g界桩: base64(sha256(base64(sha256(IMEI+ICCID))+LoginTimeStr))
     * @property {string|null} [LoginTimeStr] yyyy-mm-dd HH:MM:SS, 必须是3分钟内的时间(utc)
     * @property {string|null} [Sys] 系统名称
     * @property {number|null} [Power] 电量,单位V,=0无效，7.2v = 72
     * @property {number|null} [ChannelNo] 控制器本地手台当前信道号(对界桩通讯的手台)，无效时为0
     * @property {Array.<number>|null} [TransferChannelNos] 本控制器数据中转的通道号和对应的信道号
     * [通道号1,通道1手台的信道号，通道号2,通道2手台的信道号，...]
     * 如[1,1,2,1]表示通道1 在1信道，通道2也在1信道
     * @property {number|null} [NetworkType] 登录渠道， 0:未知 1:有线网络， 2:4g网络 3:无线FSK
     * @property {number|null} [DeviceType] 登录设备类型 0:fsk控制器 1:4g界桩
     * @property {string|null} [IMEI] 4g界桩IMEI
     * @property {string|null} [ICCID] 4g界桩ICCID
     * @property {string|null} [dataVersion] 界桩参数版本时间
     * yyyy-mm-dd HH:MM:SS
     */

    /**
     * Constructs a new BloginReq.
     * @memberof bysproto
     * @classdesc 登录信息
     * bmsg.cmd=1
     * @implements IBloginReq
     * @constructor
     * @param {bysproto.IBloginReq=} [properties] Properties to set
     */
    function BloginReq(properties) {
      this.TransferChannelNos = []
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 控制器名称，不是控制器硬件ID
     * @member {string} Name
     * @memberof bysproto.BloginReq
     * @instance
     */
    BloginReq.prototype.Name = ''

    /**
     * base64(sha256(base64(sha256(controllerName+controllerPass))+LoginTimeStr))
     * 4g界桩: base64(sha256(base64(sha256(IMEI+ICCID))+LoginTimeStr))
     * @member {string} PassHash
     * @memberof bysproto.BloginReq
     * @instance
     */
    BloginReq.prototype.PassHash = ''

    /**
     * yyyy-mm-dd HH:MM:SS, 必须是3分钟内的时间(utc)
     * @member {string} LoginTimeStr
     * @memberof bysproto.BloginReq
     * @instance
     */
    BloginReq.prototype.LoginTimeStr = ''

    /**
     * 系统名称
     * @member {string} Sys
     * @memberof bysproto.BloginReq
     * @instance
     */
    BloginReq.prototype.Sys = ''

    /**
     * 电量,单位V,=0无效，7.2v = 72
     * @member {number} Power
     * @memberof bysproto.BloginReq
     * @instance
     */
    BloginReq.prototype.Power = 0

    /**
     * 控制器本地手台当前信道号(对界桩通讯的手台)，无效时为0
     * @member {number} ChannelNo
     * @memberof bysproto.BloginReq
     * @instance
     */
    BloginReq.prototype.ChannelNo = 0

    /**
     * 本控制器数据中转的通道号和对应的信道号
     * [通道号1,通道1手台的信道号，通道号2,通道2手台的信道号，...]
     * 如[1,1,2,1]表示通道1 在1信道，通道2也在1信道
     * @member {Array.<number>} TransferChannelNos
     * @memberof bysproto.BloginReq
     * @instance
     */
    BloginReq.prototype.TransferChannelNos = $util.emptyArray

    /**
     * 登录渠道， 0:未知 1:有线网络， 2:4g网络 3:无线FSK
     * @member {number} NetworkType
     * @memberof bysproto.BloginReq
     * @instance
     */
    BloginReq.prototype.NetworkType = 0

    /**
     * 登录设备类型 0:fsk控制器 1:4g界桩
     * @member {number} DeviceType
     * @memberof bysproto.BloginReq
     * @instance
     */
    BloginReq.prototype.DeviceType = 0

    /**
     * 4g界桩IMEI
     * @member {string} IMEI
     * @memberof bysproto.BloginReq
     * @instance
     */
    BloginReq.prototype.IMEI = ''

    /**
     * 4g界桩ICCID
     * @member {string} ICCID
     * @memberof bysproto.BloginReq
     * @instance
     */
    BloginReq.prototype.ICCID = ''

    /**
     * 界桩参数版本时间
     * yyyy-mm-dd HH:MM:SS
     * @member {string} dataVersion
     * @memberof bysproto.BloginReq
     * @instance
     */
    BloginReq.prototype.dataVersion = ''

    /**
     * Encodes the specified BloginReq message. Does not implicitly {@link bysproto.BloginReq.verify|verify} messages.
     * @function encode
     * @memberof bysproto.BloginReq
     * @static
     * @param {bysproto.IBloginReq} message BloginReq message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    BloginReq.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (message.Name != null && Object.hasOwnProperty.call(message, 'Name'))
        writer.uint32(/* id 1, wireType 2 =*/ 10).string(message.Name)
      if (
        message.PassHash != null &&
        Object.hasOwnProperty.call(message, 'PassHash')
      )
        writer.uint32(/* id 2, wireType 2 =*/ 18).string(message.PassHash)
      if (
        message.LoginTimeStr != null &&
        Object.hasOwnProperty.call(message, 'LoginTimeStr')
      )
        writer.uint32(/* id 3, wireType 2 =*/ 26).string(message.LoginTimeStr)
      if (message.Sys != null && Object.hasOwnProperty.call(message, 'Sys'))
        writer.uint32(/* id 4, wireType 2 =*/ 34).string(message.Sys)
      if (message.Power != null && Object.hasOwnProperty.call(message, 'Power'))
        writer.uint32(/* id 5, wireType 0 =*/ 40).sint32(message.Power)
      if (
        message.ChannelNo != null &&
        Object.hasOwnProperty.call(message, 'ChannelNo')
      )
        writer.uint32(/* id 6, wireType 0 =*/ 48).sint32(message.ChannelNo)
      if (
        message.TransferChannelNos != null &&
        message.TransferChannelNos.length
      ) {
        writer.uint32(/* id 7, wireType 2 =*/ 58).fork()
        for (let i = 0; i < message.TransferChannelNos.length; ++i)
          writer.sint32(message.TransferChannelNos[i])
        writer.ldelim()
      }
      if (
        message.NetworkType != null &&
        Object.hasOwnProperty.call(message, 'NetworkType')
      )
        writer.uint32(/* id 8, wireType 0 =*/ 64).int32(message.NetworkType)
      if (
        message.DeviceType != null &&
        Object.hasOwnProperty.call(message, 'DeviceType')
      )
        writer.uint32(/* id 9, wireType 0 =*/ 72).int32(message.DeviceType)
      if (message.IMEI != null && Object.hasOwnProperty.call(message, 'IMEI'))
        writer.uint32(/* id 10, wireType 2 =*/ 82).string(message.IMEI)
      if (message.ICCID != null && Object.hasOwnProperty.call(message, 'ICCID'))
        writer.uint32(/* id 11, wireType 2 =*/ 90).string(message.ICCID)
      if (
        message.dataVersion != null &&
        Object.hasOwnProperty.call(message, 'dataVersion')
      )
        writer.uint32(/* id 12, wireType 2 =*/ 98).string(message.dataVersion)
      return writer
    }

    /**
     * Decodes a BloginReq message from the specified reader or buffer.
     * @function decode
     * @memberof bysproto.BloginReq
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {bysproto.BloginReq} BloginReq
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    BloginReq.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.bysproto.BloginReq()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.Name = reader.string()
            break
          case 2:
            message.PassHash = reader.string()
            break
          case 3:
            message.LoginTimeStr = reader.string()
            break
          case 4:
            message.Sys = reader.string()
            break
          case 5:
            message.Power = reader.sint32()
            break
          case 6:
            message.ChannelNo = reader.sint32()
            break
          case 7:
            if (
              !(message.TransferChannelNos && message.TransferChannelNos.length)
            )
              message.TransferChannelNos = []
            if ((tag & 7) === 2) {
              let end2 = reader.uint32() + reader.pos
              while (reader.pos < end2)
                message.TransferChannelNos.push(reader.sint32())
            } else message.TransferChannelNos.push(reader.sint32())
            break
          case 8:
            message.NetworkType = reader.int32()
            break
          case 9:
            message.DeviceType = reader.int32()
            break
          case 10:
            message.IMEI = reader.string()
            break
          case 11:
            message.ICCID = reader.string()
            break
          case 12:
            message.dataVersion = reader.string()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return BloginReq
  })()

  bysproto.BloginRes = (function () {
    /**
     * Properties of a BloginRes.
     * @memberof bysproto
     * @interface IBloginRes
     * @property {number|null} [Code] 100: 登录成功 -1：无此控制器信息 -2:时间参数不对 -3: 未知错误 -4:pass 不对
     * @property {number|null} [ControllerID] 控制器硬件ID，登录成功时返回
     * @property {string|null} [ServerTime] 服务器时间yyyy-mm-dd HH:MM:SS.zzz(utc)
     * @property {string|null} [SystemPassword] 校验界桩crc2的系统密码
     * @property {string|null} [Err] 错误描述，未知错误时可能有
     */

    /**
     * Constructs a new BloginRes.
     * @memberof bysproto
     * @classdesc 登录回应
     * bmsy.cmd=1
     * bmsg.Res=1
     * bmsg.body=BloginRes
     * @implements IBloginRes
     * @constructor
     * @param {bysproto.IBloginRes=} [properties] Properties to set
     */
    function BloginRes(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 100: 登录成功 -1：无此控制器信息 -2:时间参数不对 -3: 未知错误 -4:pass 不对
     * @member {number} Code
     * @memberof bysproto.BloginRes
     * @instance
     */
    BloginRes.prototype.Code = 0

    /**
     * 控制器硬件ID，登录成功时返回
     * @member {number} ControllerID
     * @memberof bysproto.BloginRes
     * @instance
     */
    BloginRes.prototype.ControllerID = 0

    /**
     * 服务器时间yyyy-mm-dd HH:MM:SS.zzz(utc)
     * @member {string} ServerTime
     * @memberof bysproto.BloginRes
     * @instance
     */
    BloginRes.prototype.ServerTime = ''

    /**
     * 校验界桩crc2的系统密码
     * @member {string} SystemPassword
     * @memberof bysproto.BloginRes
     * @instance
     */
    BloginRes.prototype.SystemPassword = ''

    /**
     * 错误描述，未知错误时可能有
     * @member {string} Err
     * @memberof bysproto.BloginRes
     * @instance
     */
    BloginRes.prototype.Err = ''

    /**
     * Encodes the specified BloginRes message. Does not implicitly {@link bysproto.BloginRes.verify|verify} messages.
     * @function encode
     * @memberof bysproto.BloginRes
     * @static
     * @param {bysproto.IBloginRes} message BloginRes message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    BloginRes.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (message.Code != null && Object.hasOwnProperty.call(message, 'Code'))
        writer.uint32(/* id 1, wireType 0 =*/ 8).int32(message.Code)
      if (
        message.ControllerID != null &&
        Object.hasOwnProperty.call(message, 'ControllerID')
      )
        writer.uint32(/* id 2, wireType 5 =*/ 21).fixed32(message.ControllerID)
      if (
        message.ServerTime != null &&
        Object.hasOwnProperty.call(message, 'ServerTime')
      )
        writer.uint32(/* id 5, wireType 2 =*/ 42).string(message.ServerTime)
      if (
        message.SystemPassword != null &&
        Object.hasOwnProperty.call(message, 'SystemPassword')
      )
        writer.uint32(/* id 6, wireType 2 =*/ 50).string(message.SystemPassword)
      if (message.Err != null && Object.hasOwnProperty.call(message, 'Err'))
        writer.uint32(/* id 7, wireType 2 =*/ 58).string(message.Err)
      return writer
    }

    /**
     * Decodes a BloginRes message from the specified reader or buffer.
     * @function decode
     * @memberof bysproto.BloginRes
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {bysproto.BloginRes} BloginRes
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    BloginRes.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.bysproto.BloginRes()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.Code = reader.int32()
            break
          case 2:
            message.ControllerID = reader.fixed32()
            break
          case 5:
            message.ServerTime = reader.string()
            break
          case 6:
            message.SystemPassword = reader.string()
            break
          case 7:
            message.Err = reader.string()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return BloginRes
  })()

  bysproto.BGPS = (function () {
    /**
     * Properties of a BGPS.
     * @memberof bysproto
     * @interface IBGPS
     * @property {number|null} [Lon] 东经为+，西经为-,单位为度
     * @property {number|null} [Lat] 北纬为+，南纬为-,单位为度
     * @property {number|null} [Height] BGPS Height
     */

    /**
     * Constructs a new BGPS.
     * @memberof bysproto
     * @classdesc 界桩上传的gps信息,无效gps时为全0
     * @implements IBGPS
     * @constructor
     * @param {bysproto.IBGPS=} [properties] Properties to set
     */
    function BGPS(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 东经为+，西经为-,单位为度
     * @member {number} Lon
     * @memberof bysproto.BGPS
     * @instance
     */
    BGPS.prototype.Lon = 0

    /**
     * 北纬为+，南纬为-,单位为度
     * @member {number} Lat
     * @memberof bysproto.BGPS
     * @instance
     */
    BGPS.prototype.Lat = 0

    /**
     * BGPS Height.
     * @member {number} Height
     * @memberof bysproto.BGPS
     * @instance
     */
    BGPS.prototype.Height = 0

    /**
     * Encodes the specified BGPS message. Does not implicitly {@link bysproto.BGPS.verify|verify} messages.
     * @function encode
     * @memberof bysproto.BGPS
     * @static
     * @param {bysproto.IBGPS} message BGPS message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    BGPS.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (message.Lon != null && Object.hasOwnProperty.call(message, 'Lon'))
        writer.uint32(/* id 1, wireType 1 =*/ 9).double(message.Lon)
      if (message.Lat != null && Object.hasOwnProperty.call(message, 'Lat'))
        writer.uint32(/* id 2, wireType 1 =*/ 17).double(message.Lat)
      if (
        message.Height != null &&
        Object.hasOwnProperty.call(message, 'Height')
      )
        writer.uint32(/* id 3, wireType 0 =*/ 24).sint32(message.Height)
      return writer
    }

    /**
     * Decodes a BGPS message from the specified reader or buffer.
     * @function decode
     * @memberof bysproto.BGPS
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {bysproto.BGPS} BGPS
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    BGPS.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.bysproto.BGPS()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.Lon = reader.double()
            break
          case 2:
            message.Lat = reader.double()
            break
          case 3:
            message.Height = reader.sint32()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return BGPS
  })()

  bysproto.BdeviceUpdate = (function () {
    /**
     * Properties of a BdeviceUpdate.
     * @memberof bysproto
     * @interface IBdeviceUpdate
     * @property {number|null} [DeviceID] 界桩ID
     * @property {number|null} [Cmd] 上传的命令
     * 0xd1: 常规上传
     * 0xd2: 报警上传
     * @property {number|null} [Status] 界桩状态，目前只用低位一个字节
     * @property {number|null} [SystemPassCheckOK] 界桩上传的系统密码检验是否正确 0:正确 1：不正确
     * @property {bysproto.IBGPS|null} [GPS] gps信息，没有时不需要填写
     * @property {number|null} [ParamVersion] 界桩参数版本
     * @property {string|null} [ParamTime] 界桩参数更新时间
     * @property {string|null} [CmdTime] 指令时间
     * @property {number|null} [StationID] 接收的基站/中继控制器ID
     * @property {number|null} [StationDeviceNo] 基站控制器的接收通道号，编号从1开始,不是基站上传的，此值为0
     * @property {number|null} [DeviceFieldStrength] 接收设备的场强值
     * @property {number|null} [DeviceChannelNo] 接收设备的信道号
     */

    /**
     * Constructs a new BdeviceUpdate.
     * @memberof bysproto
     * @classdesc 界桩数据上传
     * bmsg.cmd=2
     * @implements IBdeviceUpdate
     * @constructor
     * @param {bysproto.IBdeviceUpdate=} [properties] Properties to set
     */
    function BdeviceUpdate(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 界桩ID
     * @member {number} DeviceID
     * @memberof bysproto.BdeviceUpdate
     * @instance
     */
    BdeviceUpdate.prototype.DeviceID = 0

    /**
     * 上传的命令
     * 0xd1: 常规上传
     * 0xd2: 报警上传
     * @member {number} Cmd
     * @memberof bysproto.BdeviceUpdate
     * @instance
     */
    BdeviceUpdate.prototype.Cmd = 0

    /**
     * 界桩状态，目前只用低位一个字节
     * @member {number} Status
     * @memberof bysproto.BdeviceUpdate
     * @instance
     */
    BdeviceUpdate.prototype.Status = 0

    /**
     * 界桩上传的系统密码检验是否正确 0:正确 1：不正确
     * @member {number} SystemPassCheckOK
     * @memberof bysproto.BdeviceUpdate
     * @instance
     */
    BdeviceUpdate.prototype.SystemPassCheckOK = 0

    /**
     * gps信息，没有时不需要填写
     * @member {bysproto.IBGPS|null|undefined} GPS
     * @memberof bysproto.BdeviceUpdate
     * @instance
     */
    BdeviceUpdate.prototype.GPS = null

    /**
     * 界桩参数版本
     * @member {number} ParamVersion
     * @memberof bysproto.BdeviceUpdate
     * @instance
     */
    BdeviceUpdate.prototype.ParamVersion = 0

    /**
     * 界桩参数更新时间
     * @member {string} ParamTime
     * @memberof bysproto.BdeviceUpdate
     * @instance
     */
    BdeviceUpdate.prototype.ParamTime = ''

    /**
     * 指令时间
     * @member {string} CmdTime
     * @memberof bysproto.BdeviceUpdate
     * @instance
     */
    BdeviceUpdate.prototype.CmdTime = ''

    /**
     * 接收的基站/中继控制器ID
     * @member {number} StationID
     * @memberof bysproto.BdeviceUpdate
     * @instance
     */
    BdeviceUpdate.prototype.StationID = 0

    /**
     * 基站控制器的接收通道号，编号从1开始,不是基站上传的，此值为0
     * @member {number} StationDeviceNo
     * @memberof bysproto.BdeviceUpdate
     * @instance
     */
    BdeviceUpdate.prototype.StationDeviceNo = 0

    /**
     * 接收设备的场强值
     * @member {number} DeviceFieldStrength
     * @memberof bysproto.BdeviceUpdate
     * @instance
     */
    BdeviceUpdate.prototype.DeviceFieldStrength = 0

    /**
     * 接收设备的信道号
     * @member {number} DeviceChannelNo
     * @memberof bysproto.BdeviceUpdate
     * @instance
     */
    BdeviceUpdate.prototype.DeviceChannelNo = 0

    /**
     * Encodes the specified BdeviceUpdate message. Does not implicitly {@link bysproto.BdeviceUpdate.verify|verify} messages.
     * @function encode
     * @memberof bysproto.BdeviceUpdate
     * @static
     * @param {bysproto.IBdeviceUpdate} message BdeviceUpdate message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    BdeviceUpdate.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (
        message.DeviceID != null &&
        Object.hasOwnProperty.call(message, 'DeviceID')
      )
        writer.uint32(/* id 1, wireType 0 =*/ 8).sint32(message.DeviceID)
      if (message.Cmd != null && Object.hasOwnProperty.call(message, 'Cmd'))
        writer.uint32(/* id 2, wireType 0 =*/ 16).sint32(message.Cmd)
      if (
        message.Status != null &&
        Object.hasOwnProperty.call(message, 'Status')
      )
        writer.uint32(/* id 3, wireType 0 =*/ 24).sint32(message.Status)
      if (
        message.SystemPassCheckOK != null &&
        Object.hasOwnProperty.call(message, 'SystemPassCheckOK')
      )
        writer
          .uint32(/* id 4, wireType 0 =*/ 32)
          .int32(message.SystemPassCheckOK)
      if (message.GPS != null && Object.hasOwnProperty.call(message, 'GPS'))
        $root.bysproto.BGPS.encode(
          message.GPS,
          writer.uint32(/* id 5, wireType 2 =*/ 42).fork(),
        ).ldelim()
      if (
        message.ParamVersion != null &&
        Object.hasOwnProperty.call(message, 'ParamVersion')
      )
        writer.uint32(/* id 6, wireType 0 =*/ 48).int32(message.ParamVersion)
      if (
        message.ParamTime != null &&
        Object.hasOwnProperty.call(message, 'ParamTime')
      )
        writer.uint32(/* id 7, wireType 2 =*/ 58).string(message.ParamTime)
      if (
        message.CmdTime != null &&
        Object.hasOwnProperty.call(message, 'CmdTime')
      )
        writer.uint32(/* id 8, wireType 2 =*/ 66).string(message.CmdTime)
      if (
        message.StationID != null &&
        Object.hasOwnProperty.call(message, 'StationID')
      )
        writer.uint32(/* id 9, wireType 0 =*/ 72).sint32(message.StationID)
      if (
        message.StationDeviceNo != null &&
        Object.hasOwnProperty.call(message, 'StationDeviceNo')
      )
        writer
          .uint32(/* id 10, wireType 0 =*/ 80)
          .sint32(message.StationDeviceNo)
      if (
        message.DeviceFieldStrength != null &&
        Object.hasOwnProperty.call(message, 'DeviceFieldStrength')
      )
        writer
          .uint32(/* id 12, wireType 0 =*/ 96)
          .sint32(message.DeviceFieldStrength)
      if (
        message.DeviceChannelNo != null &&
        Object.hasOwnProperty.call(message, 'DeviceChannelNo')
      )
        writer
          .uint32(/* id 13, wireType 0 =*/ 104)
          .sint32(message.DeviceChannelNo)
      return writer
    }

    /**
     * Decodes a BdeviceUpdate message from the specified reader or buffer.
     * @function decode
     * @memberof bysproto.BdeviceUpdate
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {bysproto.BdeviceUpdate} BdeviceUpdate
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    BdeviceUpdate.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.bysproto.BdeviceUpdate()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.DeviceID = reader.sint32()
            break
          case 2:
            message.Cmd = reader.sint32()
            break
          case 3:
            message.Status = reader.sint32()
            break
          case 4:
            message.SystemPassCheckOK = reader.int32()
            break
          case 5:
            message.GPS = $root.bysproto.BGPS.decode(reader, reader.uint32())
            break
          case 6:
            message.ParamVersion = reader.int32()
            break
          case 7:
            message.ParamTime = reader.string()
            break
          case 8:
            message.CmdTime = reader.string()
            break
          case 9:
            message.StationID = reader.sint32()
            break
          case 10:
            message.StationDeviceNo = reader.sint32()
            break
          case 12:
            message.DeviceFieldStrength = reader.sint32()
            break
          case 13:
            message.DeviceChannelNo = reader.sint32()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return BdeviceUpdate
  })()

  bysproto.ControllerStatus = (function () {
    /**
     * Properties of a ControllerStatus.
     * @memberof bysproto
     * @interface IControllerStatus
     * @property {number|null} [StationID] 接收的基站/中继控制器ID
     * @property {number|null} [StationDeviceNo] 基站控制器的接收手台号，编号从1开始,本控制器的状态时，此值为0
     * @property {number|null} [Power] 电量,单位V,=0无效，7.2v = 72
     * @property {number|null} [ChannelNo] 控制器手台当前信道号(与界桩通讯的手台)
     * @property {Array.<number>|null} [TransferChannelNos] 本控制器数据中转的通道号和对应的信道号
     * [通道号1,通道1手台的信道号，通道号2,通道2手台的信道号，...]
     * 如[1,1,2,1]表示通道1 在1信道，通道2也在1信道
     * @property {number|null} [Status] 控制器状态
     * bit0:时间异常 0:无异常 1:无法获取时间（可能gps异常)
     */

    /**
     * Constructs a new ControllerStatus.
     * @memberof bysproto
     * @classdesc 中继/基站状态信息
     * @implements IControllerStatus
     * @constructor
     * @param {bysproto.IControllerStatus=} [properties] Properties to set
     */
    function ControllerStatus(properties) {
      this.TransferChannelNos = []
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 接收的基站/中继控制器ID
     * @member {number} StationID
     * @memberof bysproto.ControllerStatus
     * @instance
     */
    ControllerStatus.prototype.StationID = 0

    /**
     * 基站控制器的接收手台号，编号从1开始,本控制器的状态时，此值为0
     * @member {number} StationDeviceNo
     * @memberof bysproto.ControllerStatus
     * @instance
     */
    ControllerStatus.prototype.StationDeviceNo = 0

    /**
     * 电量,单位V,=0无效，7.2v = 72
     * @member {number} Power
     * @memberof bysproto.ControllerStatus
     * @instance
     */
    ControllerStatus.prototype.Power = 0

    /**
     * 控制器手台当前信道号(与界桩通讯的手台)
     * @member {number} ChannelNo
     * @memberof bysproto.ControllerStatus
     * @instance
     */
    ControllerStatus.prototype.ChannelNo = 0

    /**
     * 本控制器数据中转的通道号和对应的信道号
     * [通道号1,通道1手台的信道号，通道号2,通道2手台的信道号，...]
     * 如[1,1,2,1]表示通道1 在1信道，通道2也在1信道
     * @member {Array.<number>} TransferChannelNos
     * @memberof bysproto.ControllerStatus
     * @instance
     */
    ControllerStatus.prototype.TransferChannelNos = $util.emptyArray

    /**
     * 控制器状态
     * bit0:时间异常 0:无异常 1:无法获取时间（可能gps异常)
     * @member {number} Status
     * @memberof bysproto.ControllerStatus
     * @instance
     */
    ControllerStatus.prototype.Status = 0

    /**
     * Encodes the specified ControllerStatus message. Does not implicitly {@link bysproto.ControllerStatus.verify|verify} messages.
     * @function encode
     * @memberof bysproto.ControllerStatus
     * @static
     * @param {bysproto.IControllerStatus} message ControllerStatus message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    ControllerStatus.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (
        message.StationID != null &&
        Object.hasOwnProperty.call(message, 'StationID')
      )
        writer.uint32(/* id 1, wireType 0 =*/ 8).sint32(message.StationID)
      if (
        message.StationDeviceNo != null &&
        Object.hasOwnProperty.call(message, 'StationDeviceNo')
      )
        writer
          .uint32(/* id 2, wireType 0 =*/ 16)
          .sint32(message.StationDeviceNo)
      if (message.Power != null && Object.hasOwnProperty.call(message, 'Power'))
        writer.uint32(/* id 3, wireType 0 =*/ 24).sint32(message.Power)
      if (
        message.ChannelNo != null &&
        Object.hasOwnProperty.call(message, 'ChannelNo')
      )
        writer.uint32(/* id 4, wireType 0 =*/ 32).sint32(message.ChannelNo)
      if (
        message.TransferChannelNos != null &&
        message.TransferChannelNos.length
      ) {
        writer.uint32(/* id 7, wireType 2 =*/ 58).fork()
        for (let i = 0; i < message.TransferChannelNos.length; ++i)
          writer.sint32(message.TransferChannelNos[i])
        writer.ldelim()
      }
      if (
        message.Status != null &&
        Object.hasOwnProperty.call(message, 'Status')
      )
        writer.uint32(/* id 8, wireType 0 =*/ 64).uint32(message.Status)
      return writer
    }

    /**
     * Decodes a ControllerStatus message from the specified reader or buffer.
     * @function decode
     * @memberof bysproto.ControllerStatus
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {bysproto.ControllerStatus} ControllerStatus
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    ControllerStatus.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.bysproto.ControllerStatus()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.StationID = reader.sint32()
            break
          case 2:
            message.StationDeviceNo = reader.sint32()
            break
          case 3:
            message.Power = reader.sint32()
            break
          case 4:
            message.ChannelNo = reader.sint32()
            break
          case 7:
            if (
              !(message.TransferChannelNos && message.TransferChannelNos.length)
            )
              message.TransferChannelNos = []
            if ((tag & 7) === 2) {
              let end2 = reader.uint32() + reader.pos
              while (reader.pos < end2)
                message.TransferChannelNos.push(reader.sint32())
            } else message.TransferChannelNos.push(reader.sint32())
            break
          case 8:
            message.Status = reader.uint32()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return ControllerStatus
  })()

  bysproto.BdeviceUpdateResponse = (function () {
    /**
     * Properties of a BdeviceUpdateResponse.
     * @memberof bysproto
     * @interface IBdeviceUpdateResponse
     * @property {number|null} [DeviceID] 界桩ID
     * @property {number|null} [StationID] 接收的基站/中继控制器ID
     * @property {number|null} [StationDeviceNo] 基站控制器的通道号，编号从1开始,不是基站上传的，此值为0
     * @property {Uint8Array|null} [Cmd0xD0] 回应命令数据
     */

    /**
     * Constructs a new BdeviceUpdateResponse.
     * @memberof bysproto
     * @classdesc 回应界桩上传（如果确实有参数需要修改的话）
     * bmsg.cmd=12
     * @implements IBdeviceUpdateResponse
     * @constructor
     * @param {bysproto.IBdeviceUpdateResponse=} [properties] Properties to set
     */
    function BdeviceUpdateResponse(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 界桩ID
     * @member {number} DeviceID
     * @memberof bysproto.BdeviceUpdateResponse
     * @instance
     */
    BdeviceUpdateResponse.prototype.DeviceID = 0

    /**
     * 接收的基站/中继控制器ID
     * @member {number} StationID
     * @memberof bysproto.BdeviceUpdateResponse
     * @instance
     */
    BdeviceUpdateResponse.prototype.StationID = 0

    /**
     * 基站控制器的通道号，编号从1开始,不是基站上传的，此值为0
     * @member {number} StationDeviceNo
     * @memberof bysproto.BdeviceUpdateResponse
     * @instance
     */
    BdeviceUpdateResponse.prototype.StationDeviceNo = 0

    /**
     * 回应命令数据
     * @member {Uint8Array} Cmd0xD0
     * @memberof bysproto.BdeviceUpdateResponse
     * @instance
     */
    BdeviceUpdateResponse.prototype.Cmd0xD0 = $util.newBuffer([])

    /**
     * Encodes the specified BdeviceUpdateResponse message. Does not implicitly {@link bysproto.BdeviceUpdateResponse.verify|verify} messages.
     * @function encode
     * @memberof bysproto.BdeviceUpdateResponse
     * @static
     * @param {bysproto.IBdeviceUpdateResponse} message BdeviceUpdateResponse message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    BdeviceUpdateResponse.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (
        message.DeviceID != null &&
        Object.hasOwnProperty.call(message, 'DeviceID')
      )
        writer.uint32(/* id 1, wireType 0 =*/ 8).sint32(message.DeviceID)
      if (
        message.StationID != null &&
        Object.hasOwnProperty.call(message, 'StationID')
      )
        writer.uint32(/* id 9, wireType 0 =*/ 72).sint32(message.StationID)
      if (
        message.StationDeviceNo != null &&
        Object.hasOwnProperty.call(message, 'StationDeviceNo')
      )
        writer
          .uint32(/* id 10, wireType 0 =*/ 80)
          .sint32(message.StationDeviceNo)
      if (
        message.Cmd0xD0 != null &&
        Object.hasOwnProperty.call(message, 'Cmd0xD0')
      )
        writer.uint32(/* id 11, wireType 2 =*/ 90).bytes(message.Cmd0xD0)
      return writer
    }

    /**
     * Decodes a BdeviceUpdateResponse message from the specified reader or buffer.
     * @function decode
     * @memberof bysproto.BdeviceUpdateResponse
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {bysproto.BdeviceUpdateResponse} BdeviceUpdateResponse
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    BdeviceUpdateResponse.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.bysproto.BdeviceUpdateResponse()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.DeviceID = reader.sint32()
            break
          case 9:
            message.StationID = reader.sint32()
            break
          case 10:
            message.StationDeviceNo = reader.sint32()
            break
          case 11:
            message.Cmd0xD0 = reader.bytes()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return BdeviceUpdateResponse
  })()

  bysproto.BControllerUpdateChannel = (function () {
    /**
     * Properties of a BControllerUpdateChannel.
     * @memberof bysproto
     * @interface IBControllerUpdateChannel
     * @property {number|null} [StationID] 控制器ID
     * @property {number|null} [StationDeviceNo] 控制器通道号
     * @property {number|null} [NewChannelNo] 新默认监听信道号
     */

    /**
     * Constructs a new BControllerUpdateChannel.
     * @memberof bysproto
     * @classdesc 修改控制器手台默认信道号（针对与界桩通讯的手台）
     * bmsg.cmd=13
     * @implements IBControllerUpdateChannel
     * @constructor
     * @param {bysproto.IBControllerUpdateChannel=} [properties] Properties to set
     */
    function BControllerUpdateChannel(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 控制器ID
     * @member {number} StationID
     * @memberof bysproto.BControllerUpdateChannel
     * @instance
     */
    BControllerUpdateChannel.prototype.StationID = 0

    /**
     * 控制器通道号
     * @member {number} StationDeviceNo
     * @memberof bysproto.BControllerUpdateChannel
     * @instance
     */
    BControllerUpdateChannel.prototype.StationDeviceNo = 0

    /**
     * 新默认监听信道号
     * @member {number} NewChannelNo
     * @memberof bysproto.BControllerUpdateChannel
     * @instance
     */
    BControllerUpdateChannel.prototype.NewChannelNo = 0

    /**
     * Encodes the specified BControllerUpdateChannel message. Does not implicitly {@link bysproto.BControllerUpdateChannel.verify|verify} messages.
     * @function encode
     * @memberof bysproto.BControllerUpdateChannel
     * @static
     * @param {bysproto.IBControllerUpdateChannel} message BControllerUpdateChannel message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    BControllerUpdateChannel.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (
        message.NewChannelNo != null &&
        Object.hasOwnProperty.call(message, 'NewChannelNo')
      )
        writer.uint32(/* id 3, wireType 0 =*/ 24).sint32(message.NewChannelNo)
      if (
        message.StationID != null &&
        Object.hasOwnProperty.call(message, 'StationID')
      )
        writer.uint32(/* id 9, wireType 0 =*/ 72).sint32(message.StationID)
      if (
        message.StationDeviceNo != null &&
        Object.hasOwnProperty.call(message, 'StationDeviceNo')
      )
        writer
          .uint32(/* id 10, wireType 0 =*/ 80)
          .sint32(message.StationDeviceNo)
      return writer
    }

    /**
     * Decodes a BControllerUpdateChannel message from the specified reader or buffer.
     * @function decode
     * @memberof bysproto.BControllerUpdateChannel
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {bysproto.BControllerUpdateChannel} BControllerUpdateChannel
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    BControllerUpdateChannel.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.bysproto.BControllerUpdateChannel()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 9:
            message.StationID = reader.sint32()
            break
          case 10:
            message.StationDeviceNo = reader.sint32()
            break
          case 3:
            message.NewChannelNo = reader.sint32()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return BControllerUpdateChannel
  })()

  bysproto.BControllerNewServerAddr = (function () {
    /**
     * Properties of a BControllerNewServerAddr.
     * @memberof bysproto
     * @interface IBControllerNewServerAddr
     * @property {number|null} [StationID] 基站控制器ID
     * @property {number|null} [ControllerChannelNo] 在上级控制器的通道号，中继时需要填写，基站本地通道时为0
     * @property {string|null} [Ip] 新服务器IP地址，支持域名和IP
     * @property {number|null} [Port] 新服务器端口
     */

    /**
     * Constructs a new BControllerNewServerAddr.
     * @memberof bysproto
     * @classdesc bmsg.cmd=15, 更新控制器新的注册地址
     * bmsg.body=BControllerNewServerAddr
     * bmsg.Res=1,控制器应答
     * @implements IBControllerNewServerAddr
     * @constructor
     * @param {bysproto.IBControllerNewServerAddr=} [properties] Properties to set
     */
    function BControllerNewServerAddr(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 基站控制器ID
     * @member {number} StationID
     * @memberof bysproto.BControllerNewServerAddr
     * @instance
     */
    BControllerNewServerAddr.prototype.StationID = 0

    /**
     * 在上级控制器的通道号，中继时需要填写，基站本地通道时为0
     * @member {number} ControllerChannelNo
     * @memberof bysproto.BControllerNewServerAddr
     * @instance
     */
    BControllerNewServerAddr.prototype.ControllerChannelNo = 0

    /**
     * 新服务器IP地址，支持域名和IP
     * @member {string} Ip
     * @memberof bysproto.BControllerNewServerAddr
     * @instance
     */
    BControllerNewServerAddr.prototype.Ip = ''

    /**
     * 新服务器端口
     * @member {number} Port
     * @memberof bysproto.BControllerNewServerAddr
     * @instance
     */
    BControllerNewServerAddr.prototype.Port = 0

    /**
     * Encodes the specified BControllerNewServerAddr message. Does not implicitly {@link bysproto.BControllerNewServerAddr.verify|verify} messages.
     * @function encode
     * @memberof bysproto.BControllerNewServerAddr
     * @static
     * @param {bysproto.IBControllerNewServerAddr} message BControllerNewServerAddr message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    BControllerNewServerAddr.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (
        message.StationID != null &&
        Object.hasOwnProperty.call(message, 'StationID')
      )
        writer.uint32(/* id 1, wireType 0 =*/ 8).sint32(message.StationID)
      if (
        message.ControllerChannelNo != null &&
        Object.hasOwnProperty.call(message, 'ControllerChannelNo')
      )
        writer
          .uint32(/* id 2, wireType 0 =*/ 16)
          .sint32(message.ControllerChannelNo)
      if (message.Ip != null && Object.hasOwnProperty.call(message, 'Ip'))
        writer.uint32(/* id 3, wireType 2 =*/ 26).string(message.Ip)
      if (message.Port != null && Object.hasOwnProperty.call(message, 'Port'))
        writer.uint32(/* id 4, wireType 0 =*/ 32).sint32(message.Port)
      return writer
    }

    /**
     * Decodes a BControllerNewServerAddr message from the specified reader or buffer.
     * @function decode
     * @memberof bysproto.BControllerNewServerAddr
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {bysproto.BControllerNewServerAddr} BControllerNewServerAddr
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    BControllerNewServerAddr.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.bysproto.BControllerNewServerAddr()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.StationID = reader.sint32()
            break
          case 2:
            message.ControllerChannelNo = reader.sint32()
            break
          case 3:
            message.Ip = reader.string()
            break
          case 4:
            message.Port = reader.sint32()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return BControllerNewServerAddr
  })()

  bysproto.BInfoReporting = (function () {
    /**
     * Properties of a BInfoReporting.
     * @memberof bysproto
     * @interface IBInfoReporting
     * @property {number|null} [deviceID] 本机设备编号
     * @property {number|null} [type] 1：开机上报 2：调试上报 3：定时上报
     * @property {string|null} [iccid] sim卡iccid 20位
     * @property {string|null} [softwareVersion] 格式：Vx.x.x_yyMMdd V1.0.1_231031
     * @property {string|null} [dataVersion] yyyy-mm-dd HH:MM:SS
     * @property {string|null} [time] 设备时间yyyy-mm-dd HH:MM:SS
     * @property {number|null} [battery] 设备电池电压单位mv
     * @property {number|null} [signal] 设备4G模块场强信号幅度 单位dbm
     * @property {number|null} [state] Bit0：报警锁定(0:正常，1:报警锁定)
     * Bit1：RTC时钟状态(0:正常，1:故障)
     * Bit2：GPS模块状态(0:正常，1:故障)
     * Bit3：三轴传感器状态(0:正常，1:故障)
     * Bit4：电池状态(0:正常，1:故障)
     * Bit5：摄像头状态(0:正常，1:故障)(预留)
     * Bit6：红外探头状态(0:正常，1:故障)(预留)
     * Bit7-Bit31：预留
     * @property {number|null} [alarmstate] Bit0：位移报警(0:正常，1:报警)
     * Bit1：震动报警(0:正常，1:报警)
     * Bit2：倾斜报警(0:正常，1:报警)
     * Bit3：红外报警(0:正常，1:报警)(预留)
     * Bit4-Bit31：预留
     * @property {number|null} [temp] 设备温度
     * @property {number|null} [rh] 湿度 单位：%RH
     */

    /**
     * Constructs a new BInfoReporting.
     * @memberof bysproto
     * @classdesc 信息上报
     * bmsg.cmd=21
     * @implements IBInfoReporting
     * @constructor
     * @param {bysproto.IBInfoReporting=} [properties] Properties to set
     */
    function BInfoReporting(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 本机设备编号
     * @member {number} deviceID
     * @memberof bysproto.BInfoReporting
     * @instance
     */
    BInfoReporting.prototype.deviceID = 0

    /**
     * 1：开机上报 2：调试上报 3：定时上报
     * @member {number} type
     * @memberof bysproto.BInfoReporting
     * @instance
     */
    BInfoReporting.prototype.type = 0

    /**
     * sim卡iccid 20位
     * @member {string} iccid
     * @memberof bysproto.BInfoReporting
     * @instance
     */
    BInfoReporting.prototype.iccid = ''

    /**
     * 格式：Vx.x.x_yyMMdd V1.0.1_231031
     * @member {string} softwareVersion
     * @memberof bysproto.BInfoReporting
     * @instance
     */
    BInfoReporting.prototype.softwareVersion = ''

    /**
     * yyyy-mm-dd HH:MM:SS
     * @member {string} dataVersion
     * @memberof bysproto.BInfoReporting
     * @instance
     */
    BInfoReporting.prototype.dataVersion = ''

    /**
     * 设备时间yyyy-mm-dd HH:MM:SS
     * @member {string} time
     * @memberof bysproto.BInfoReporting
     * @instance
     */
    BInfoReporting.prototype.time = ''

    /**
     * 设备电池电压单位mv
     * @member {number} battery
     * @memberof bysproto.BInfoReporting
     * @instance
     */
    BInfoReporting.prototype.battery = 0

    /**
     * 设备4G模块场强信号幅度 单位dbm
     * @member {number} signal
     * @memberof bysproto.BInfoReporting
     * @instance
     */
    BInfoReporting.prototype.signal = 0

    /**
     * Bit0：报警锁定(0:正常，1:报警锁定)
     * Bit1：RTC时钟状态(0:正常，1:故障)
     * Bit2：GPS模块状态(0:正常，1:故障)
     * Bit3：三轴传感器状态(0:正常，1:故障)
     * Bit4：电池状态(0:正常，1:故障)
     * Bit5：摄像头状态(0:正常，1:故障)(预留)
     * Bit6：红外探头状态(0:正常，1:故障)(预留)
     * Bit7-Bit31：预留
     * @member {number} state
     * @memberof bysproto.BInfoReporting
     * @instance
     */
    BInfoReporting.prototype.state = 0

    /**
     * Bit0：位移报警(0:正常，1:报警)
     * Bit1：震动报警(0:正常，1:报警)
     * Bit2：倾斜报警(0:正常，1:报警)
     * Bit3：红外报警(0:正常，1:报警)(预留)
     * Bit4-Bit31：预留
     * @member {number} alarmstate
     * @memberof bysproto.BInfoReporting
     * @instance
     */
    BInfoReporting.prototype.alarmstate = 0

    /**
     * 设备温度
     * @member {number} temp
     * @memberof bysproto.BInfoReporting
     * @instance
     */
    BInfoReporting.prototype.temp = 0

    /**
     * 湿度 单位：%RH
     * @member {number} rh
     * @memberof bysproto.BInfoReporting
     * @instance
     */
    BInfoReporting.prototype.rh = 0

    /**
     * Encodes the specified BInfoReporting message. Does not implicitly {@link bysproto.BInfoReporting.verify|verify} messages.
     * @function encode
     * @memberof bysproto.BInfoReporting
     * @static
     * @param {bysproto.IBInfoReporting} message BInfoReporting message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    BInfoReporting.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (
        message.deviceID != null &&
        Object.hasOwnProperty.call(message, 'deviceID')
      )
        writer.uint32(/* id 1, wireType 0 =*/ 8).uint32(message.deviceID)
      if (message.type != null && Object.hasOwnProperty.call(message, 'type'))
        writer.uint32(/* id 2, wireType 0 =*/ 16).uint32(message.type)
      if (message.iccid != null && Object.hasOwnProperty.call(message, 'iccid'))
        writer.uint32(/* id 3, wireType 2 =*/ 26).string(message.iccid)
      if (
        message.softwareVersion != null &&
        Object.hasOwnProperty.call(message, 'softwareVersion')
      )
        writer
          .uint32(/* id 4, wireType 2 =*/ 34)
          .string(message.softwareVersion)
      if (
        message.dataVersion != null &&
        Object.hasOwnProperty.call(message, 'dataVersion')
      )
        writer.uint32(/* id 5, wireType 2 =*/ 42).string(message.dataVersion)
      if (message.time != null && Object.hasOwnProperty.call(message, 'time'))
        writer.uint32(/* id 6, wireType 2 =*/ 50).string(message.time)
      if (
        message.battery != null &&
        Object.hasOwnProperty.call(message, 'battery')
      )
        writer.uint32(/* id 7, wireType 0 =*/ 56).uint32(message.battery)
      if (
        message.signal != null &&
        Object.hasOwnProperty.call(message, 'signal')
      )
        writer.uint32(/* id 8, wireType 0 =*/ 64).sint32(message.signal)
      if (message.state != null && Object.hasOwnProperty.call(message, 'state'))
        writer.uint32(/* id 9, wireType 0 =*/ 72).uint32(message.state)
      if (
        message.alarmstate != null &&
        Object.hasOwnProperty.call(message, 'alarmstate')
      )
        writer.uint32(/* id 10, wireType 0 =*/ 80).uint32(message.alarmstate)
      if (message.temp != null && Object.hasOwnProperty.call(message, 'temp'))
        writer.uint32(/* id 11, wireType 5 =*/ 93).float(message.temp)
      if (message.rh != null && Object.hasOwnProperty.call(message, 'rh'))
        writer.uint32(/* id 12, wireType 5 =*/ 101).float(message.rh)
      return writer
    }

    /**
     * Decodes a BInfoReporting message from the specified reader or buffer.
     * @function decode
     * @memberof bysproto.BInfoReporting
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {bysproto.BInfoReporting} BInfoReporting
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    BInfoReporting.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.bysproto.BInfoReporting()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.deviceID = reader.uint32()
            break
          case 2:
            message.type = reader.uint32()
            break
          case 3:
            message.iccid = reader.string()
            break
          case 4:
            message.softwareVersion = reader.string()
            break
          case 5:
            message.dataVersion = reader.string()
            break
          case 6:
            message.time = reader.string()
            break
          case 7:
            message.battery = reader.uint32()
            break
          case 8:
            message.signal = reader.sint32()
            break
          case 9:
            message.state = reader.uint32()
            break
          case 10:
            message.alarmstate = reader.uint32()
            break
          case 11:
            message.temp = reader.float()
            break
          case 12:
            message.rh = reader.float()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return BInfoReporting
  })()

  bysproto.BVamp = (function () {
    /**
     * Properties of a BVamp.
     * @memberof bysproto
     * @interface IBVamp
     * @property {number|null} [amplitude] 震动幅度
     * @property {number|null} [duration] 震动持续时间 s
     */

    /**
     * Constructs a new BVamp.
     * @memberof bysproto
     * @classdesc 震动报警参数
     * @implements IBVamp
     * @constructor
     * @param {bysproto.IBVamp=} [properties] Properties to set
     */
    function BVamp(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 震动幅度
     * @member {number} amplitude
     * @memberof bysproto.BVamp
     * @instance
     */
    BVamp.prototype.amplitude = 0

    /**
     * 震动持续时间 s
     * @member {number} duration
     * @memberof bysproto.BVamp
     * @instance
     */
    BVamp.prototype.duration = 0

    /**
     * Encodes the specified BVamp message. Does not implicitly {@link bysproto.BVamp.verify|verify} messages.
     * @function encode
     * @memberof bysproto.BVamp
     * @static
     * @param {bysproto.IBVamp} message BVamp message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    BVamp.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (
        message.amplitude != null &&
        Object.hasOwnProperty.call(message, 'amplitude')
      )
        writer.uint32(/* id 1, wireType 0 =*/ 8).uint32(message.amplitude)
      if (
        message.duration != null &&
        Object.hasOwnProperty.call(message, 'duration')
      )
        writer.uint32(/* id 2, wireType 0 =*/ 16).uint32(message.duration)
      return writer
    }

    /**
     * Decodes a BVamp message from the specified reader or buffer.
     * @function decode
     * @memberof bysproto.BVamp
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {bysproto.BVamp} BVamp
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    BVamp.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.bysproto.BVamp()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.amplitude = reader.uint32()
            break
          case 2:
            message.duration = reader.uint32()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return BVamp
  })()

  bysproto.BAlarmReporting = (function () {
    /**
     * Properties of a BAlarmReporting.
     * @memberof bysproto
     * @interface IBAlarmReporting
     * @property {number|null} [deviceID] 本机设备编号
     * @property {number|null} [type] 1：开机上报 2：调试上报 3：报警上报
     * @property {number|null} [alarmstate] Bit0：位移报警(0:正常，1:报警)
     * Bit1：震动报警(0:正常，1:报警)
     * Bit2：倾斜报警(0:正常，1:报警)
     * Bit3：红外报警(0:正常，1:报警)(预留)
     * Bit4-Bit31：预留
     * @property {bysproto.IBGPS|null} [locate] 定位坐标
     * @property {number|null} [attitude] 倾斜角度，单位：度
     * @property {bysproto.IBVamp|null} [vibration] 震动报警
     * @property {number|null} [infrared] 红外预留
     * @property {number|null} [camera] 摄像头预留
     * @property {Uint8Array|null} [reserve] 其他（预留）
     */

    /**
     * Constructs a new BAlarmReporting.
     * @memberof bysproto
     * @classdesc 报警上报
     * bmsg.cmd=22
     * @implements IBAlarmReporting
     * @constructor
     * @param {bysproto.IBAlarmReporting=} [properties] Properties to set
     */
    function BAlarmReporting(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 本机设备编号
     * @member {number} deviceID
     * @memberof bysproto.BAlarmReporting
     * @instance
     */
    BAlarmReporting.prototype.deviceID = 0

    /**
     * 1：开机上报 2：调试上报 3：报警上报
     * @member {number} type
     * @memberof bysproto.BAlarmReporting
     * @instance
     */
    BAlarmReporting.prototype.type = 0

    /**
     * Bit0：位移报警(0:正常，1:报警)
     * Bit1：震动报警(0:正常，1:报警)
     * Bit2：倾斜报警(0:正常，1:报警)
     * Bit3：红外报警(0:正常，1:报警)(预留)
     * Bit4-Bit31：预留
     * @member {number} alarmstate
     * @memberof bysproto.BAlarmReporting
     * @instance
     */
    BAlarmReporting.prototype.alarmstate = 0

    /**
     * 定位坐标
     * @member {bysproto.IBGPS|null|undefined} locate
     * @memberof bysproto.BAlarmReporting
     * @instance
     */
    BAlarmReporting.prototype.locate = null

    /**
     * 倾斜角度，单位：度
     * @member {number} attitude
     * @memberof bysproto.BAlarmReporting
     * @instance
     */
    BAlarmReporting.prototype.attitude = 0

    /**
     * 震动报警
     * @member {bysproto.IBVamp|null|undefined} vibration
     * @memberof bysproto.BAlarmReporting
     * @instance
     */
    BAlarmReporting.prototype.vibration = null

    /**
     * 红外预留
     * @member {number} infrared
     * @memberof bysproto.BAlarmReporting
     * @instance
     */
    BAlarmReporting.prototype.infrared = 0

    /**
     * 摄像头预留
     * @member {number} camera
     * @memberof bysproto.BAlarmReporting
     * @instance
     */
    BAlarmReporting.prototype.camera = 0

    /**
     * 其他（预留）
     * @member {Uint8Array} reserve
     * @memberof bysproto.BAlarmReporting
     * @instance
     */
    BAlarmReporting.prototype.reserve = $util.newBuffer([])

    /**
     * Encodes the specified BAlarmReporting message. Does not implicitly {@link bysproto.BAlarmReporting.verify|verify} messages.
     * @function encode
     * @memberof bysproto.BAlarmReporting
     * @static
     * @param {bysproto.IBAlarmReporting} message BAlarmReporting message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    BAlarmReporting.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (
        message.deviceID != null &&
        Object.hasOwnProperty.call(message, 'deviceID')
      )
        writer.uint32(/* id 1, wireType 0 =*/ 8).uint32(message.deviceID)
      if (message.type != null && Object.hasOwnProperty.call(message, 'type'))
        writer.uint32(/* id 2, wireType 0 =*/ 16).uint32(message.type)
      if (
        message.alarmstate != null &&
        Object.hasOwnProperty.call(message, 'alarmstate')
      )
        writer.uint32(/* id 3, wireType 0 =*/ 24).uint32(message.alarmstate)
      if (
        message.locate != null &&
        Object.hasOwnProperty.call(message, 'locate')
      )
        $root.bysproto.BGPS.encode(
          message.locate,
          writer.uint32(/* id 4, wireType 2 =*/ 34).fork(),
        ).ldelim()
      if (
        message.attitude != null &&
        Object.hasOwnProperty.call(message, 'attitude')
      )
        writer.uint32(/* id 5, wireType 0 =*/ 40).uint32(message.attitude)
      if (
        message.vibration != null &&
        Object.hasOwnProperty.call(message, 'vibration')
      )
        $root.bysproto.BVamp.encode(
          message.vibration,
          writer.uint32(/* id 6, wireType 2 =*/ 50).fork(),
        ).ldelim()
      if (
        message.infrared != null &&
        Object.hasOwnProperty.call(message, 'infrared')
      )
        writer.uint32(/* id 7, wireType 0 =*/ 56).uint32(message.infrared)
      if (
        message.camera != null &&
        Object.hasOwnProperty.call(message, 'camera')
      )
        writer.uint32(/* id 8, wireType 0 =*/ 64).uint32(message.camera)
      if (
        message.reserve != null &&
        Object.hasOwnProperty.call(message, 'reserve')
      )
        writer.uint32(/* id 9, wireType 2 =*/ 74).bytes(message.reserve)
      return writer
    }

    /**
     * Decodes a BAlarmReporting message from the specified reader or buffer.
     * @function decode
     * @memberof bysproto.BAlarmReporting
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {bysproto.BAlarmReporting} BAlarmReporting
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    BAlarmReporting.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.bysproto.BAlarmReporting()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.deviceID = reader.uint32()
            break
          case 2:
            message.type = reader.uint32()
            break
          case 3:
            message.alarmstate = reader.uint32()
            break
          case 4:
            message.locate = $root.bysproto.BGPS.decode(reader, reader.uint32())
            break
          case 5:
            message.attitude = reader.uint32()
            break
          case 6:
            message.vibration = $root.bysproto.BVamp.decode(
              reader,
              reader.uint32(),
            )
            break
          case 7:
            message.infrared = reader.uint32()
            break
          case 8:
            message.camera = reader.uint32()
            break
          case 9:
            message.reserve = reader.bytes()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return BAlarmReporting
  })()

  bysproto.BAlarmClear = (function () {
    /**
     * Properties of a BAlarmClear.
     * @memberof bysproto
     * @interface IBAlarmClear
     * @property {number|null} [deviceID] 目标设备编号
     * @property {number|null} [response] 1：报警解除 2：报警锁定
     */

    /**
     * Constructs a new BAlarmClear.
     * @memberof bysproto
     * @classdesc 报警解除
     * bmsg.cmd=33
     * @implements IBAlarmClear
     * @constructor
     * @param {bysproto.IBAlarmClear=} [properties] Properties to set
     */
    function BAlarmClear(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 目标设备编号
     * @member {number} deviceID
     * @memberof bysproto.BAlarmClear
     * @instance
     */
    BAlarmClear.prototype.deviceID = 0

    /**
     * 1：报警解除 2：报警锁定
     * @member {number} response
     * @memberof bysproto.BAlarmClear
     * @instance
     */
    BAlarmClear.prototype.response = 0

    /**
     * Encodes the specified BAlarmClear message. Does not implicitly {@link bysproto.BAlarmClear.verify|verify} messages.
     * @function encode
     * @memberof bysproto.BAlarmClear
     * @static
     * @param {bysproto.IBAlarmClear} message BAlarmClear message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    BAlarmClear.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (
        message.deviceID != null &&
        Object.hasOwnProperty.call(message, 'deviceID')
      )
        writer.uint32(/* id 1, wireType 0 =*/ 8).uint32(message.deviceID)
      if (
        message.response != null &&
        Object.hasOwnProperty.call(message, 'response')
      )
        writer.uint32(/* id 2, wireType 0 =*/ 16).uint32(message.response)
      return writer
    }

    /**
     * Decodes a BAlarmClear message from the specified reader or buffer.
     * @function decode
     * @memberof bysproto.BAlarmClear
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {bysproto.BAlarmClear} BAlarmClear
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    BAlarmClear.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.bysproto.BAlarmClear()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.deviceID = reader.uint32()
            break
          case 2:
            message.response = reader.uint32()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return BAlarmClear
  })()

  bysproto.BDataUpdate = (function () {
    /**
     * Properties of a BDataUpdate.
     * @memberof bysproto
     * @interface IBDataUpdate
     * @property {number|null} [deviceID] 目标设备编号
     * @property {number|null} [type] Bit0：解除报警锁定 Bit1：更新参数 Bit2：校准时间
     * @property {string|null} [dataVersion] 版本yyyy-mm-dd HH:MM:SS
     * @property {string|null} [addr] 服务器地址  xxx.xxx.xxx.xxx:xxxx   xxxxxx.com:xxxx
     * @property {string|null} [timerbase] 定时上报基准时间HH:MM:SS
     * @property {number|null} [timer] timer 6h、8h、12h、24h
     * @property {number|null} [attitude] 姿态报警阈值
     * @property {number|null} [dirft] 位移报警阈值
     * @property {bysproto.IBVamp|null} [vibration] 震动报警阈值
     * @property {number|null} [infrared] 红外报警阈值
     * @property {number|null} [t1] 延迟休眠时间 10s
     * @property {number|null} [t2] 调试模式时间 120s
     * @property {number|null} [t3] 报警间隔 10s
     * @property {number|null} [n1] 报警次数 10
     */

    /**
     * Constructs a new BDataUpdate.
     * @memberof bysproto
     * @classdesc 参数更新
     * bmsg.cmd=24
     * @implements IBDataUpdate
     * @constructor
     * @param {bysproto.IBDataUpdate=} [properties] Properties to set
     */
    function BDataUpdate(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 目标设备编号
     * @member {number} deviceID
     * @memberof bysproto.BDataUpdate
     * @instance
     */
    BDataUpdate.prototype.deviceID = 0

    /**
     * Bit0：解除报警锁定 Bit1：更新参数 Bit2：校准时间
     * @member {number} type
     * @memberof bysproto.BDataUpdate
     * @instance
     */
    BDataUpdate.prototype.type = 0

    /**
     * 版本yyyy-mm-dd HH:MM:SS
     * @member {string} dataVersion
     * @memberof bysproto.BDataUpdate
     * @instance
     */
    BDataUpdate.prototype.dataVersion = ''

    /**
     * 服务器地址  xxx.xxx.xxx.xxx:xxxx   xxxxxx.com:xxxx
     * @member {string} addr
     * @memberof bysproto.BDataUpdate
     * @instance
     */
    BDataUpdate.prototype.addr = ''

    /**
     * 定时上报基准时间HH:MM:SS
     * @member {string} timerbase
     * @memberof bysproto.BDataUpdate
     * @instance
     */
    BDataUpdate.prototype.timerbase = ''

    /**
     * timer 6h、8h、12h、24h
     * @member {number} timer
     * @memberof bysproto.BDataUpdate
     * @instance
     */
    BDataUpdate.prototype.timer = 0

    /**
     * 姿态报警阈值
     * @member {number} attitude
     * @memberof bysproto.BDataUpdate
     * @instance
     */
    BDataUpdate.prototype.attitude = 0

    /**
     * 位移报警阈值
     * @member {number} dirft
     * @memberof bysproto.BDataUpdate
     * @instance
     */
    BDataUpdate.prototype.dirft = 0

    /**
     * 震动报警阈值
     * @member {bysproto.IBVamp|null|undefined} vibration
     * @memberof bysproto.BDataUpdate
     * @instance
     */
    BDataUpdate.prototype.vibration = null

    /**
     * 红外报警阈值
     * @member {number} infrared
     * @memberof bysproto.BDataUpdate
     * @instance
     */
    BDataUpdate.prototype.infrared = 0

    /**
     * 延迟休眠时间 10s
     * @member {number} t1
     * @memberof bysproto.BDataUpdate
     * @instance
     */
    BDataUpdate.prototype.t1 = 0

    /**
     * 调试模式时间 120s
     * @member {number} t2
     * @memberof bysproto.BDataUpdate
     * @instance
     */
    BDataUpdate.prototype.t2 = 0

    /**
     * 报警间隔 10s
     * @member {number} t3
     * @memberof bysproto.BDataUpdate
     * @instance
     */
    BDataUpdate.prototype.t3 = 0

    /**
     * 报警次数 10
     * @member {number} n1
     * @memberof bysproto.BDataUpdate
     * @instance
     */
    BDataUpdate.prototype.n1 = 0

    /**
     * Encodes the specified BDataUpdate message. Does not implicitly {@link bysproto.BDataUpdate.verify|verify} messages.
     * @function encode
     * @memberof bysproto.BDataUpdate
     * @static
     * @param {bysproto.IBDataUpdate} message BDataUpdate message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    BDataUpdate.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (
        message.deviceID != null &&
        Object.hasOwnProperty.call(message, 'deviceID')
      )
        writer.uint32(/* id 1, wireType 0 =*/ 8).uint32(message.deviceID)
      if (message.type != null && Object.hasOwnProperty.call(message, 'type'))
        writer.uint32(/* id 2, wireType 0 =*/ 16).uint32(message.type)
      if (
        message.dataVersion != null &&
        Object.hasOwnProperty.call(message, 'dataVersion')
      )
        writer.uint32(/* id 3, wireType 2 =*/ 26).string(message.dataVersion)
      if (message.addr != null && Object.hasOwnProperty.call(message, 'addr'))
        writer.uint32(/* id 4, wireType 2 =*/ 34).string(message.addr)
      if (
        message.timerbase != null &&
        Object.hasOwnProperty.call(message, 'timerbase')
      )
        writer.uint32(/* id 5, wireType 2 =*/ 42).string(message.timerbase)
      if (message.timer != null && Object.hasOwnProperty.call(message, 'timer'))
        writer.uint32(/* id 6, wireType 0 =*/ 48).uint32(message.timer)
      if (
        message.attitude != null &&
        Object.hasOwnProperty.call(message, 'attitude')
      )
        writer.uint32(/* id 7, wireType 0 =*/ 56).uint32(message.attitude)
      if (message.dirft != null && Object.hasOwnProperty.call(message, 'dirft'))
        writer.uint32(/* id 8, wireType 0 =*/ 64).uint32(message.dirft)
      if (
        message.vibration != null &&
        Object.hasOwnProperty.call(message, 'vibration')
      )
        $root.bysproto.BVamp.encode(
          message.vibration,
          writer.uint32(/* id 9, wireType 2 =*/ 74).fork(),
        ).ldelim()
      if (
        message.infrared != null &&
        Object.hasOwnProperty.call(message, 'infrared')
      )
        writer.uint32(/* id 10, wireType 0 =*/ 80).uint32(message.infrared)
      if (message.t1 != null && Object.hasOwnProperty.call(message, 't1'))
        writer.uint32(/* id 11, wireType 0 =*/ 88).uint32(message.t1)
      if (message.t2 != null && Object.hasOwnProperty.call(message, 't2'))
        writer.uint32(/* id 12, wireType 0 =*/ 96).uint32(message.t2)
      if (message.t3 != null && Object.hasOwnProperty.call(message, 't3'))
        writer.uint32(/* id 13, wireType 0 =*/ 104).uint32(message.t3)
      if (message.n1 != null && Object.hasOwnProperty.call(message, 'n1'))
        writer.uint32(/* id 14, wireType 0 =*/ 112).uint32(message.n1)
      return writer
    }

    /**
     * Decodes a BDataUpdate message from the specified reader or buffer.
     * @function decode
     * @memberof bysproto.BDataUpdate
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {bysproto.BDataUpdate} BDataUpdate
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    BDataUpdate.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.bysproto.BDataUpdate()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.deviceID = reader.uint32()
            break
          case 2:
            message.type = reader.uint32()
            break
          case 3:
            message.dataVersion = reader.string()
            break
          case 4:
            message.addr = reader.string()
            break
          case 5:
            message.timerbase = reader.string()
            break
          case 6:
            message.timer = reader.uint32()
            break
          case 7:
            message.attitude = reader.uint32()
            break
          case 8:
            message.dirft = reader.uint32()
            break
          case 9:
            message.vibration = $root.bysproto.BVamp.decode(
              reader,
              reader.uint32(),
            )
            break
          case 10:
            message.infrared = reader.uint32()
            break
          case 11:
            message.t1 = reader.uint32()
            break
          case 12:
            message.t2 = reader.uint32()
            break
          case 13:
            message.t3 = reader.uint32()
            break
          case 14:
            message.n1 = reader.uint32()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return BDataUpdate
  })()

  bysproto.BShutDown = (function () {
    /**
     * Properties of a BShutDown.
     * @memberof bysproto
     * @interface IBShutDown
     * @property {number|null} [deviceID] 目标设备编号
     * @property {number|null} [type] 0:遥活 1:遥闭  2:遥晕
     * @property {number|null} [camType] 0:遥活 1:遥闭  2:遥晕
     */

    /**
     * Constructs a new BShutDown.
     * @memberof bysproto
     * @classdesc 遥闭
     * bmsg.cmd=27
     * @implements IBShutDown
     * @constructor
     * @param {bysproto.IBShutDown=} [properties] Properties to set
     */
    function BShutDown(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 目标设备编号
     * @member {number} deviceID
     * @memberof bysproto.BShutDown
     * @instance
     */
    BShutDown.prototype.deviceID = 0

    /**
     * 0:遥活 1:遥闭  2:遥晕
     * @member {number} type
     * @memberof bysproto.BShutDown
     * @instance
     */
    BShutDown.prototype.type = 0

    /**
     * 0:遥活 1:遥闭  2:遥晕
     * @member {number} camType
     * @memberof bysproto.BShutDown
     * @instance
     */
    BShutDown.prototype.camType = 0

    /**
     * Encodes the specified BShutDown message. Does not implicitly {@link bysproto.BShutDown.verify|verify} messages.
     * @function encode
     * @memberof bysproto.BShutDown
     * @static
     * @param {bysproto.IBShutDown} message BShutDown message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    BShutDown.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (
        message.deviceID != null &&
        Object.hasOwnProperty.call(message, 'deviceID')
      )
        writer.uint32(/* id 1, wireType 0 =*/ 8).uint32(message.deviceID)
      if (message.type != null && Object.hasOwnProperty.call(message, 'type'))
        writer.uint32(/* id 2, wireType 0 =*/ 16).uint32(message.type)
      if (
        message.camType != null &&
        Object.hasOwnProperty.call(message, 'camType')
      )
        writer.uint32(/* id 3, wireType 0 =*/ 24).uint32(message.camType)
      return writer
    }

    /**
     * Decodes a BShutDown message from the specified reader or buffer.
     * @function decode
     * @memberof bysproto.BShutDown
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {bysproto.BShutDown} BShutDown
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    BShutDown.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.bysproto.BShutDown()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.deviceID = reader.uint32()
            break
          case 2:
            message.type = reader.uint32()
            break
          case 3:
            message.camType = reader.uint32()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return BShutDown
  })()

  return bysproto
})())

export { $root as default }

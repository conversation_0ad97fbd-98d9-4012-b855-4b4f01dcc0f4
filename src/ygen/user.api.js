/*eslint-disable block-scoped-var, id-length, no-control-regex, no-magic-numbers, no-prototype-builtins, no-redeclare, no-shadow, no-var, sort-vars*/
import * as $protobuf from 'protobufjs/minimal'

// Common aliases
const $Reader = $protobuf.Reader,
  $Writer = $protobuf.Writer,
  $util = $protobuf.util

// Exported root namespace
const $root = $protobuf.roots['default'] || ($protobuf.roots['default'] = {})

export const user = ($root.user = (() => {
  /**
   * Namespace user.
   * @exports user
   * @namespace
   */
  const user = $root.user || {}

  user.ReqUserLogin = (function () {
    /**
     * Properties of a ReqUserLogin.
     * @memberof user
     * @interface IReqUserLogin
     * @property {number|null} [LoginType] 登录方式
     * 0：username+userpass
     * 1: session rid
     * @property {string|null} [LoginName] login name or session rid
     * @property {string|null} [LoginPass] login pass base64(sha256(base64(sha256(username+userpass))+LoginTimeStr))
     * @property {string|null} [LoginTimeStr] 时间字符串，必须是3分钟内的时间(utc)，格式 yyyy-mm-dd HH:MM:SS
     * @property {string|null} [System] 系统号
     */

    /**
     * Constructs a new ReqUserLogin.
     * @memberof user
     * @classdesc Represents a ReqUserLogin.
     * @implements IReqUserLogin
     * @constructor
     * @param {user.IReqUserLogin=} [properties] Properties to set
     */
    function ReqUserLogin(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 登录方式
     * 0：username+userpass
     * 1: session rid
     * @member {number} LoginType
     * @memberof user.ReqUserLogin
     * @instance
     */
    ReqUserLogin.prototype.LoginType = 0

    /**
     * login name or session rid
     * @member {string} LoginName
     * @memberof user.ReqUserLogin
     * @instance
     */
    ReqUserLogin.prototype.LoginName = ''

    /**
     * login pass base64(sha256(base64(sha256(username+userpass))+LoginTimeStr))
     * @member {string} LoginPass
     * @memberof user.ReqUserLogin
     * @instance
     */
    ReqUserLogin.prototype.LoginPass = ''

    /**
     * 时间字符串，必须是3分钟内的时间(utc)，格式 yyyy-mm-dd HH:MM:SS
     * @member {string} LoginTimeStr
     * @memberof user.ReqUserLogin
     * @instance
     */
    ReqUserLogin.prototype.LoginTimeStr = ''

    /**
     * 系统号
     * @member {string} System
     * @memberof user.ReqUserLogin
     * @instance
     */
    ReqUserLogin.prototype.System = ''

    /**
     * Encodes the specified ReqUserLogin message. Does not implicitly {@link user.ReqUserLogin.verify|verify} messages.
     * @function encode
     * @memberof user.ReqUserLogin
     * @static
     * @param {user.IReqUserLogin} message ReqUserLogin message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    ReqUserLogin.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (
        message.LoginType != null &&
        Object.hasOwnProperty.call(message, 'LoginType')
      )
        writer.uint32(/* id 1, wireType 0 =*/ 8).int32(message.LoginType)
      if (
        message.LoginName != null &&
        Object.hasOwnProperty.call(message, 'LoginName')
      )
        writer.uint32(/* id 2, wireType 2 =*/ 18).string(message.LoginName)
      if (
        message.LoginPass != null &&
        Object.hasOwnProperty.call(message, 'LoginPass')
      )
        writer.uint32(/* id 3, wireType 2 =*/ 26).string(message.LoginPass)
      if (
        message.LoginTimeStr != null &&
        Object.hasOwnProperty.call(message, 'LoginTimeStr')
      )
        writer.uint32(/* id 4, wireType 2 =*/ 34).string(message.LoginTimeStr)
      if (
        message.System != null &&
        Object.hasOwnProperty.call(message, 'System')
      )
        writer.uint32(/* id 5, wireType 2 =*/ 42).string(message.System)
      return writer
    }

    /**
     * Decodes a ReqUserLogin message from the specified reader or buffer.
     * @function decode
     * @memberof user.ReqUserLogin
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {user.ReqUserLogin} ReqUserLogin
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    ReqUserLogin.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.user.ReqUserLogin()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.LoginType = reader.int32()
            break
          case 2:
            message.LoginName = reader.string()
            break
          case 3:
            message.LoginPass = reader.string()
            break
          case 4:
            message.LoginTimeStr = reader.string()
            break
          case 5:
            message.System = reader.string()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return ReqUserLogin
  })()

  user.ResUserLogin = (function () {
    /**
     * Properties of a ResUserLogin.
     * @memberof user
     * @interface IResUserLogin
     * @property {number|null} [Code] 回应码
     * 100:ok,其它为错误
     * 1:密码错误 2: 无此sid 3:此sessionn用户已经删除 4:无此用户
     * @property {string|null} [Err] 错误信息,如果有的话
     * @property {string|null} [UserRID] user rid
     * @property {string|null} [UserOrgRID] user org rid
     * @property {string|null} [SessionRID] session rid
     * @property {string|null} [SysUTCTime] sys utc time yyyy-mm-dd HH:MM:SS
     */

    /**
     * Constructs a new ResUserLogin.
     * @memberof user
     * @classdesc Represents a ResUserLogin.
     * @implements IResUserLogin
     * @constructor
     * @param {user.IResUserLogin=} [properties] Properties to set
     */
    function ResUserLogin(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 回应码
     * 100:ok,其它为错误
     * 1:密码错误 2: 无此sid 3:此sessionn用户已经删除 4:无此用户
     * @member {number} Code
     * @memberof user.ResUserLogin
     * @instance
     */
    ResUserLogin.prototype.Code = 0

    /**
     * 错误信息,如果有的话
     * @member {string} Err
     * @memberof user.ResUserLogin
     * @instance
     */
    ResUserLogin.prototype.Err = ''

    /**
     * user rid
     * @member {string} UserRID
     * @memberof user.ResUserLogin
     * @instance
     */
    ResUserLogin.prototype.UserRID = ''

    /**
     * user org rid
     * @member {string} UserOrgRID
     * @memberof user.ResUserLogin
     * @instance
     */
    ResUserLogin.prototype.UserOrgRID = ''

    /**
     * session rid
     * @member {string} SessionRID
     * @memberof user.ResUserLogin
     * @instance
     */
    ResUserLogin.prototype.SessionRID = ''

    /**
     * sys utc time yyyy-mm-dd HH:MM:SS
     * @member {string} SysUTCTime
     * @memberof user.ResUserLogin
     * @instance
     */
    ResUserLogin.prototype.SysUTCTime = ''

    /**
     * Encodes the specified ResUserLogin message. Does not implicitly {@link user.ResUserLogin.verify|verify} messages.
     * @function encode
     * @memberof user.ResUserLogin
     * @static
     * @param {user.IResUserLogin} message ResUserLogin message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    ResUserLogin.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (message.Code != null && Object.hasOwnProperty.call(message, 'Code'))
        writer.uint32(/* id 1, wireType 0 =*/ 8).int32(message.Code)
      if (message.Err != null && Object.hasOwnProperty.call(message, 'Err'))
        writer.uint32(/* id 2, wireType 2 =*/ 18).string(message.Err)
      if (
        message.UserRID != null &&
        Object.hasOwnProperty.call(message, 'UserRID')
      )
        writer.uint32(/* id 3, wireType 2 =*/ 26).string(message.UserRID)
      if (
        message.UserOrgRID != null &&
        Object.hasOwnProperty.call(message, 'UserOrgRID')
      )
        writer.uint32(/* id 4, wireType 2 =*/ 34).string(message.UserOrgRID)
      if (
        message.SessionRID != null &&
        Object.hasOwnProperty.call(message, 'SessionRID')
      )
        writer.uint32(/* id 5, wireType 2 =*/ 42).string(message.SessionRID)
      if (
        message.SysUTCTime != null &&
        Object.hasOwnProperty.call(message, 'SysUTCTime')
      )
        writer.uint32(/* id 6, wireType 2 =*/ 50).string(message.SysUTCTime)
      return writer
    }

    /**
     * Decodes a ResUserLogin message from the specified reader or buffer.
     * @function decode
     * @memberof user.ResUserLogin
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {user.ResUserLogin} ResUserLogin
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    ResUserLogin.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.user.ResUserLogin()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.Code = reader.int32()
            break
          case 2:
            message.Err = reader.string()
            break
          case 3:
            message.UserRID = reader.string()
            break
          case 4:
            message.UserOrgRID = reader.string()
            break
          case 5:
            message.SessionRID = reader.string()
            break
          case 6:
            message.SysUTCTime = reader.string()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return ResUserLogin
  })()

  user.ReqUserHasOrgPrivilege = (function () {
    /**
     * Properties of a ReqUserHasOrgPrivilege.
     * @memberof user
     * @interface IReqUserHasOrgPrivilege
     * @property {string|null} [System] 系统号
     * @property {string|null} [UserRID] 要查询的用户RID
     * @property {string|null} [OrgRID] 是不是对此群组拥有权限
     * @property {string|null} [SessionID] 发起者的session id
     */

    /**
     * Constructs a new ReqUserHasOrgPrivilege.
     * @memberof user
     * @classdesc Represents a ReqUserHasOrgPrivilege.
     * @implements IReqUserHasOrgPrivilege
     * @constructor
     * @param {user.IReqUserHasOrgPrivilege=} [properties] Properties to set
     */
    function ReqUserHasOrgPrivilege(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 系统号
     * @member {string} System
     * @memberof user.ReqUserHasOrgPrivilege
     * @instance
     */
    ReqUserHasOrgPrivilege.prototype.System = ''

    /**
     * 要查询的用户RID
     * @member {string} UserRID
     * @memberof user.ReqUserHasOrgPrivilege
     * @instance
     */
    ReqUserHasOrgPrivilege.prototype.UserRID = ''

    /**
     * 是不是对此群组拥有权限
     * @member {string} OrgRID
     * @memberof user.ReqUserHasOrgPrivilege
     * @instance
     */
    ReqUserHasOrgPrivilege.prototype.OrgRID = ''

    /**
     * 发起者的session id
     * @member {string} SessionID
     * @memberof user.ReqUserHasOrgPrivilege
     * @instance
     */
    ReqUserHasOrgPrivilege.prototype.SessionID = ''

    /**
     * Encodes the specified ReqUserHasOrgPrivilege message. Does not implicitly {@link user.ReqUserHasOrgPrivilege.verify|verify} messages.
     * @function encode
     * @memberof user.ReqUserHasOrgPrivilege
     * @static
     * @param {user.IReqUserHasOrgPrivilege} message ReqUserHasOrgPrivilege message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    ReqUserHasOrgPrivilege.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (
        message.System != null &&
        Object.hasOwnProperty.call(message, 'System')
      )
        writer.uint32(/* id 1, wireType 2 =*/ 10).string(message.System)
      if (
        message.UserRID != null &&
        Object.hasOwnProperty.call(message, 'UserRID')
      )
        writer.uint32(/* id 2, wireType 2 =*/ 18).string(message.UserRID)
      if (
        message.OrgRID != null &&
        Object.hasOwnProperty.call(message, 'OrgRID')
      )
        writer.uint32(/* id 3, wireType 2 =*/ 26).string(message.OrgRID)
      if (
        message.SessionID != null &&
        Object.hasOwnProperty.call(message, 'SessionID')
      )
        writer.uint32(/* id 4, wireType 2 =*/ 34).string(message.SessionID)
      return writer
    }

    /**
     * Decodes a ReqUserHasOrgPrivilege message from the specified reader or buffer.
     * @function decode
     * @memberof user.ReqUserHasOrgPrivilege
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {user.ReqUserHasOrgPrivilege} ReqUserHasOrgPrivilege
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    ReqUserHasOrgPrivilege.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.user.ReqUserHasOrgPrivilege()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.System = reader.string()
            break
          case 2:
            message.UserRID = reader.string()
            break
          case 3:
            message.OrgRID = reader.string()
            break
          case 4:
            message.SessionID = reader.string()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return ReqUserHasOrgPrivilege
  })()

  user.ReqUserHasPermission = (function () {
    /**
     * Properties of a ReqUserHasPermission.
     * @memberof user
     * @interface IReqUserHasPermission
     * @property {string|null} [System] 系统号
     * @property {string|null} [UserRID] 要查询的用户RID
     * @property {string|null} [PermissionType] ReqUserHasPermission PermissionType
     * @property {string|null} [PermissionValue] ReqUserHasPermission PermissionValue
     * @property {string|null} [SessionID] 发起者的session id
     */

    /**
     * Constructs a new ReqUserHasPermission.
     * @memberof user
     * @classdesc Represents a ReqUserHasPermission.
     * @implements IReqUserHasPermission
     * @constructor
     * @param {user.IReqUserHasPermission=} [properties] Properties to set
     */
    function ReqUserHasPermission(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 系统号
     * @member {string} System
     * @memberof user.ReqUserHasPermission
     * @instance
     */
    ReqUserHasPermission.prototype.System = ''

    /**
     * 要查询的用户RID
     * @member {string} UserRID
     * @memberof user.ReqUserHasPermission
     * @instance
     */
    ReqUserHasPermission.prototype.UserRID = ''

    /**
     * ReqUserHasPermission PermissionType.
     * @member {string} PermissionType
     * @memberof user.ReqUserHasPermission
     * @instance
     */
    ReqUserHasPermission.prototype.PermissionType = ''

    /**
     * ReqUserHasPermission PermissionValue.
     * @member {string} PermissionValue
     * @memberof user.ReqUserHasPermission
     * @instance
     */
    ReqUserHasPermission.prototype.PermissionValue = ''

    /**
     * 发起者的session id
     * @member {string} SessionID
     * @memberof user.ReqUserHasPermission
     * @instance
     */
    ReqUserHasPermission.prototype.SessionID = ''

    /**
     * Encodes the specified ReqUserHasPermission message. Does not implicitly {@link user.ReqUserHasPermission.verify|verify} messages.
     * @function encode
     * @memberof user.ReqUserHasPermission
     * @static
     * @param {user.IReqUserHasPermission} message ReqUserHasPermission message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    ReqUserHasPermission.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (
        message.System != null &&
        Object.hasOwnProperty.call(message, 'System')
      )
        writer.uint32(/* id 1, wireType 2 =*/ 10).string(message.System)
      if (
        message.UserRID != null &&
        Object.hasOwnProperty.call(message, 'UserRID')
      )
        writer.uint32(/* id 2, wireType 2 =*/ 18).string(message.UserRID)
      if (
        message.PermissionType != null &&
        Object.hasOwnProperty.call(message, 'PermissionType')
      )
        writer.uint32(/* id 3, wireType 2 =*/ 26).string(message.PermissionType)
      if (
        message.PermissionValue != null &&
        Object.hasOwnProperty.call(message, 'PermissionValue')
      )
        writer
          .uint32(/* id 4, wireType 2 =*/ 34)
          .string(message.PermissionValue)
      if (
        message.SessionID != null &&
        Object.hasOwnProperty.call(message, 'SessionID')
      )
        writer.uint32(/* id 6, wireType 2 =*/ 50).string(message.SessionID)
      return writer
    }

    /**
     * Decodes a ReqUserHasPermission message from the specified reader or buffer.
     * @function decode
     * @memberof user.ReqUserHasPermission
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {user.ReqUserHasPermission} ReqUserHasPermission
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    ReqUserHasPermission.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.user.ReqUserHasPermission()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.System = reader.string()
            break
          case 2:
            message.UserRID = reader.string()
            break
          case 3:
            message.PermissionType = reader.string()
            break
          case 4:
            message.PermissionValue = reader.string()
            break
          case 6:
            message.SessionID = reader.string()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return ReqUserHasPermission
  })()

  user.RpcUser = (function () {
    /**
     * Constructs a new RpcUser service.
     * @memberof user
     * @classdesc Represents a RpcUser
     * @extends $protobuf.rpc.Service
     * @constructor
     * @param {$protobuf.RPCImpl} rpcImpl RPC implementation
     * @param {boolean} [requestDelimited=false] Whether requests are length-delimited
     * @param {boolean} [responseDelimited=false] Whether responses are length-delimited
     */
    function RpcUser(rpcImpl, requestDelimited, responseDelimited) {
      $protobuf.rpc.Service.call(
        this,
        rpcImpl,
        requestDelimited,
        responseDelimited,
      )
    }

    ;(RpcUser.prototype = Object.create(
      $protobuf.rpc.Service.prototype,
    )).constructor = RpcUser

    /**
     * Callback as used by {@link user.RpcUser#login}.
     * @memberof user.RpcUser
     * @typedef LoginCallback
     * @type {function}
     * @param {Error|null} error Error, if any
     * @param {user.ResUserLogin} [response] ResUserLogin
     */

    /**
     * 登录系统
     * @function login
     * @memberof user.RpcUser
     * @instance
     * @param {user.IReqUserLogin} request ReqUserLogin message or plain object
     * @param {user.RpcUser.LoginCallback} callback Node-style callback called with the error, if any, and ResUserLogin
     * @returns {undefined}
     * @variation 1
     */
    Object.defineProperty(
      (RpcUser.prototype.login = function login(request, callback) {
        return this.rpcCall(
          login,
          $root.user.ReqUserLogin,
          $root.user.ResUserLogin,
          request,
          callback,
        )
      }),
      'name',
      { value: 'Login' },
    )

    /**
     * 登录系统
     * @function login
     * @memberof user.RpcUser
     * @instance
     * @param {user.IReqUserLogin} request ReqUserLogin message or plain object
     * @returns {Promise<user.ResUserLogin>} Promise
     * @variation 2
     */

    /**
     * Callback as used by {@link user.RpcUser#isUserHasOrgPrivilege}.
     * @memberof user.RpcUser
     * @typedef IsUserHasOrgPrivilegeCallback
     * @type {function}
     * @param {Error|null} error Error, if any
     * @param {rpc.RpcCommon} [response] RpcCommon
     */

    /**
     * 查询用户对相应的群组是否有权限
     * 返回值RpcCommon.Code=100为拥有权限，其它为不拥有权限
     * @function isUserHasOrgPrivilege
     * @memberof user.RpcUser
     * @instance
     * @param {user.IReqUserHasOrgPrivilege} request ReqUserHasOrgPrivilege message or plain object
     * @param {user.RpcUser.IsUserHasOrgPrivilegeCallback} callback Node-style callback called with the error, if any, and RpcCommon
     * @returns {undefined}
     * @variation 1
     */
    Object.defineProperty(
      (RpcUser.prototype.isUserHasOrgPrivilege = function isUserHasOrgPrivilege(
        request,
        callback,
      ) {
        return this.rpcCall(
          isUserHasOrgPrivilege,
          $root.user.ReqUserHasOrgPrivilege,
          $root.rpc.RpcCommon,
          request,
          callback,
        )
      }),
      'name',
      { value: 'IsUserHasOrgPrivilege' },
    )

    /**
     * 查询用户对相应的群组是否有权限
     * 返回值RpcCommon.Code=100为拥有权限，其它为不拥有权限
     * @function isUserHasOrgPrivilege
     * @memberof user.RpcUser
     * @instance
     * @param {user.IReqUserHasOrgPrivilege} request ReqUserHasOrgPrivilege message or plain object
     * @returns {Promise<rpc.RpcCommon>} Promise
     * @variation 2
     */

    /**
     * Callback as used by {@link user.RpcUser#myOrgRID}.
     * @memberof user.RpcUser
     * @typedef MyOrgRIDCallback
     * @type {function}
     * @param {Error|null} error Error, if any
     * @param {org.DbOrg} [response] DbOrg
     */

    /**
     * 取得有权限的org rid列表
     * RpcCommon.Err = userrid
     * @function myOrgRID
     * @memberof user.RpcUser
     * @instance
     * @param {rpc.IRpcCommon} request RpcCommon message or plain object
     * @param {user.RpcUser.MyOrgRIDCallback} callback Node-style callback called with the error, if any, and DbOrg
     * @returns {undefined}
     * @variation 1
     */
    Object.defineProperty(
      (RpcUser.prototype.myOrgRID = function myOrgRID(request, callback) {
        return this.rpcCall(
          myOrgRID,
          $root.rpc.RpcCommon,
          $root.org.DbOrg,
          request,
          callback,
        )
      }),
      'name',
      { value: 'MyOrgRID' },
    )

    /**
     * 取得有权限的org rid列表
     * RpcCommon.Err = userrid
     * @function myOrgRID
     * @memberof user.RpcUser
     * @instance
     * @param {rpc.IRpcCommon} request RpcCommon message or plain object
     * @returns {Promise<org.DbOrg>} Promise
     * @variation 2
     */

    /**
     * Callback as used by {@link user.RpcUser#myPermission}.
     * @memberof user.RpcUser
     * @typedef MyPermissionCallback
     * @type {function}
     * @param {Error|null} error Error, if any
     * @param {user.DbPermission} [response] DbPermission
     */

    /**
     * 取得用户的权限列表
     * RpcCommon.Err = userrid
     * @function myPermission
     * @memberof user.RpcUser
     * @instance
     * @param {rpc.IRpcCommon} request RpcCommon message or plain object
     * @param {user.RpcUser.MyPermissionCallback} callback Node-style callback called with the error, if any, and DbPermission
     * @returns {undefined}
     * @variation 1
     */
    Object.defineProperty(
      (RpcUser.prototype.myPermission = function myPermission(
        request,
        callback,
      ) {
        return this.rpcCall(
          myPermission,
          $root.rpc.RpcCommon,
          $root.user.DbPermission,
          request,
          callback,
        )
      }),
      'name',
      { value: 'MyPermission' },
    )

    /**
     * 取得用户的权限列表
     * RpcCommon.Err = userrid
     * @function myPermission
     * @memberof user.RpcUser
     * @instance
     * @param {rpc.IRpcCommon} request RpcCommon message or plain object
     * @returns {Promise<user.DbPermission>} Promise
     * @variation 2
     */

    /**
     * Callback as used by {@link user.RpcUser#myRole}.
     * @memberof user.RpcUser
     * @typedef MyRoleCallback
     * @type {function}
     * @param {Error|null} error Error, if any
     * @param {user.DbRole} [response] DbRole
     */

    /**
     * 取得用户的角色列表
     * RpcCommon.Err = userrid
     * @function myRole
     * @memberof user.RpcUser
     * @instance
     * @param {rpc.IRpcCommon} request RpcCommon message or plain object
     * @param {user.RpcUser.MyRoleCallback} callback Node-style callback called with the error, if any, and DbRole
     * @returns {undefined}
     * @variation 1
     */
    Object.defineProperty(
      (RpcUser.prototype.myRole = function myRole(request, callback) {
        return this.rpcCall(
          myRole,
          $root.rpc.RpcCommon,
          $root.user.DbRole,
          request,
          callback,
        )
      }),
      'name',
      { value: 'MyRole' },
    )

    /**
     * 取得用户的角色列表
     * RpcCommon.Err = userrid
     * @function myRole
     * @memberof user.RpcUser
     * @instance
     * @param {rpc.IRpcCommon} request RpcCommon message or plain object
     * @returns {Promise<user.DbRole>} Promise
     * @variation 2
     */

    /**
     * Callback as used by {@link user.RpcUser#isUserHasPermission}.
     * @memberof user.RpcUser
     * @typedef IsUserHasPermissionCallback
     * @type {function}
     * @param {Error|null} error Error, if any
     * @param {rpc.RpcCommon} [response] RpcCommon
     */

    /**
     * 查询用户是否有特定的权限
     * 返回值RpcCommon.Code=100为拥有权限，其它为不拥有权限
     * @function isUserHasPermission
     * @memberof user.RpcUser
     * @instance
     * @param {user.IReqUserHasPermission} request ReqUserHasPermission message or plain object
     * @param {user.RpcUser.IsUserHasPermissionCallback} callback Node-style callback called with the error, if any, and RpcCommon
     * @returns {undefined}
     * @variation 1
     */
    Object.defineProperty(
      (RpcUser.prototype.isUserHasPermission = function isUserHasPermission(
        request,
        callback,
      ) {
        return this.rpcCall(
          isUserHasPermission,
          $root.user.ReqUserHasPermission,
          $root.rpc.RpcCommon,
          request,
          callback,
        )
      }),
      'name',
      { value: 'IsUserHasPermission' },
    )

    /**
     * 查询用户是否有特定的权限
     * 返回值RpcCommon.Code=100为拥有权限，其它为不拥有权限
     * @function isUserHasPermission
     * @memberof user.RpcUser
     * @instance
     * @param {user.IReqUserHasPermission} request ReqUserHasPermission message or plain object
     * @returns {Promise<rpc.RpcCommon>} Promise
     * @variation 2
     */

    /**
     * Callback as used by {@link user.RpcUser#updateMySetting}.
     * @memberof user.RpcUser
     * @typedef UpdateMySettingCallback
     * @type {function}
     * @param {Error|null} error Error, if any
     * @param {crud.DMLResult} [response] DMLResult
     */

    /**
     * 修改用户自己的配置信息
     * 以metadata.DMLParam.keyColumn为要修改的目标列
     * @function updateMySetting
     * @memberof user.RpcUser
     * @instance
     * @param {user.IDbUser} request DbUser message or plain object
     * @param {user.RpcUser.UpdateMySettingCallback} callback Node-style callback called with the error, if any, and DMLResult
     * @returns {undefined}
     * @variation 1
     */
    Object.defineProperty(
      (RpcUser.prototype.updateMySetting = function updateMySetting(
        request,
        callback,
      ) {
        return this.rpcCall(
          updateMySetting,
          $root.user.DbUser,
          $root.crud.DMLResult,
          request,
          callback,
        )
      }),
      'name',
      { value: 'UpdateMySetting' },
    )

    /**
     * 修改用户自己的配置信息
     * 以metadata.DMLParam.keyColumn为要修改的目标列
     * @function updateMySetting
     * @memberof user.RpcUser
     * @instance
     * @param {user.IDbUser} request DbUser message or plain object
     * @returns {Promise<crud.DMLResult>} Promise
     * @variation 2
     */

    /**
     * Callback as used by {@link user.RpcUser#sysUTCTime}.
     * @memberof user.RpcUser
     * @typedef SysUTCTimeCallback
     * @type {function}
     * @param {Error|null} error Error, if any
     * @param {rpc.RpcCommon} [response] RpcCommon
     */

    /**
     * 获取系统当前utc时间
     * 系统时间在RpcCommon.System=yyyy-mm-dd HH:MM:SS
     * @function sysUTCTime
     * @memberof user.RpcUser
     * @instance
     * @param {rpc.IRpcCommon} request RpcCommon message or plain object
     * @param {user.RpcUser.SysUTCTimeCallback} callback Node-style callback called with the error, if any, and RpcCommon
     * @returns {undefined}
     * @variation 1
     */
    Object.defineProperty(
      (RpcUser.prototype.sysUTCTime = function sysUTCTime(request, callback) {
        return this.rpcCall(
          sysUTCTime,
          $root.rpc.RpcCommon,
          $root.rpc.RpcCommon,
          request,
          callback,
        )
      }),
      'name',
      { value: 'SysUTCTime' },
    )

    /**
     * 获取系统当前utc时间
     * 系统时间在RpcCommon.System=yyyy-mm-dd HH:MM:SS
     * @function sysUTCTime
     * @memberof user.RpcUser
     * @instance
     * @param {rpc.IRpcCommon} request RpcCommon message or plain object
     * @returns {Promise<rpc.RpcCommon>} Promise
     * @variation 2
     */

    return RpcUser
  })()

  user.DbPermission = (function () {
    /**
     * Properties of a DbPermission.
     * @memberof user
     * @interface IDbPermission
     * @property {string|null} [RID] @db uuid primary key
     * 行ID,permission rid
     * @property {string|null} [PermissionType] @db text not null
     * 权限类别
     * @property {string|null} [PermissionName] @db text not null
     * permission 名称
     * @property {string|null} [PermissionValue] @db text not null
     * permission value
     * @property {string|null} [UpdatedAt] @db timestamp not null default now_utc()
     * 数据最后修改时间
     * @property {string|null} [UpdatedDC] @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */

    /**
     * Constructs a new DbPermission.
     * @memberof user
     * @classdesc 所有的权限信息表,此表信息为预置，一般不给删除
     * @rpc crud
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbPermissionPermissionValue on DbPermission USING hash(PermissionValue);
     * @implements IDbPermission
     * @constructor
     * @param {user.IDbPermission=} [properties] Properties to set
     */
    function DbPermission(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * @db uuid primary key
     * 行ID,permission rid
     * @member {string} RID
     * @memberof user.DbPermission
     * @instance
     */
    DbPermission.prototype.RID = ''

    /**
     * @db text not null
     * 权限类别
     * @member {string} PermissionType
     * @memberof user.DbPermission
     * @instance
     */
    DbPermission.prototype.PermissionType = ''

    /**
     * @db text not null
     * permission 名称
     * @member {string} PermissionName
     * @memberof user.DbPermission
     * @instance
     */
    DbPermission.prototype.PermissionName = ''

    /**
     * @db text not null
     * permission value
     * @member {string} PermissionValue
     * @memberof user.DbPermission
     * @instance
     */
    DbPermission.prototype.PermissionValue = ''

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     * @member {string} UpdatedAt
     * @memberof user.DbPermission
     * @instance
     */
    DbPermission.prototype.UpdatedAt = ''

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     * @member {string} UpdatedDC
     * @memberof user.DbPermission
     * @instance
     */
    DbPermission.prototype.UpdatedDC = ''

    /**
     * Encodes the specified DbPermission message. Does not implicitly {@link user.DbPermission.verify|verify} messages.
     * @function encode
     * @memberof user.DbPermission
     * @static
     * @param {user.IDbPermission} message DbPermission message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    DbPermission.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (message.RID != null && Object.hasOwnProperty.call(message, 'RID'))
        writer.uint32(/* id 1, wireType 2 =*/ 10).string(message.RID)
      if (
        message.PermissionType != null &&
        Object.hasOwnProperty.call(message, 'PermissionType')
      )
        writer.uint32(/* id 3, wireType 2 =*/ 26).string(message.PermissionType)
      if (
        message.PermissionName != null &&
        Object.hasOwnProperty.call(message, 'PermissionName')
      )
        writer.uint32(/* id 4, wireType 2 =*/ 34).string(message.PermissionName)
      if (
        message.PermissionValue != null &&
        Object.hasOwnProperty.call(message, 'PermissionValue')
      )
        writer
          .uint32(/* id 10, wireType 2 =*/ 82)
          .string(message.PermissionValue)
      if (
        message.UpdatedAt != null &&
        Object.hasOwnProperty.call(message, 'UpdatedAt')
      )
        writer.uint32(/* id 14, wireType 2 =*/ 114).string(message.UpdatedAt)
      if (
        message.UpdatedDC != null &&
        Object.hasOwnProperty.call(message, 'UpdatedDC')
      )
        writer.uint32(/* id 15, wireType 2 =*/ 122).string(message.UpdatedDC)
      return writer
    }

    /**
     * Decodes a DbPermission message from the specified reader or buffer.
     * @function decode
     * @memberof user.DbPermission
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {user.DbPermission} DbPermission
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    DbPermission.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.user.DbPermission()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.RID = reader.string()
            break
          case 3:
            message.PermissionType = reader.string()
            break
          case 4:
            message.PermissionName = reader.string()
            break
          case 10:
            message.PermissionValue = reader.string()
            break
          case 14:
            message.UpdatedAt = reader.string()
            break
          case 15:
            message.UpdatedDC = reader.string()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return DbPermission
  })()

  user.DbRole = (function () {
    /**
     * Properties of a DbRole.
     * @memberof user
     * @interface IDbRole
     * @property {string|null} [RID] @db uuid primary key
     * 行ID,role rid
     * @property {string|null} [OrgRID] @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 用户所属的群组
     * @property {string|null} [RoleName] @db text not null
     * role 名称
     * @property {string|null} [Creator] @db uuid --REFERENCES DbUser(RID) ON DELETE set null
     * role 创建者 rid
     * @property {number|null} [IsBuiltIn] @db int
     * 是否是内置的角色，内置角色不能删除
     * @property {string|null} [Setting] @db jsonb not null default  '{}'::jsonb
     * 其它设置，可以在里面扩展需要用到的其它信息
     * @property {number|null} [SortValue] @db int default 100
     * sort value
     * @property {string|null} [UpdatedAt] @db timestamp not null default now_utc()
     * 数据最后修改时间
     * @property {string|null} [UpdatedDC] @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */

    /**
     * Constructs a new DbRole.
     * @memberof user
     * @classdesc 所有的角色信息表
     * @rpc crud pcrud
     * @dbpost insert into DbRole (rid, orgrid, rolename, creator, isbuiltin, setting, sortvalue, updatedat, updateddc) VALUES('*************-5555-5555-************', '00000000-0000-0000-0000-000000000000', 'base', null, 1, '{}'::jsonb, 0, now_utc(), '') ON CONFLICT  DO NOTHING;
     * @dbpost insert into DbRole (rid, orgrid, rolename, creator, isbuiltin, setting, sortvalue, updatedat, updateddc) VALUES('00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', 'admin', null, 1, '{}'::jsonb, 0, now_utc(), '') ON CONFLICT  DO NOTHING;
     * @implements IDbRole
     * @constructor
     * @param {user.IDbRole=} [properties] Properties to set
     */
    function DbRole(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * @db uuid primary key
     * 行ID,role rid
     * @member {string} RID
     * @memberof user.DbRole
     * @instance
     */
    DbRole.prototype.RID = ''

    /**
     * @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 用户所属的群组
     * @member {string} OrgRID
     * @memberof user.DbRole
     * @instance
     */
    DbRole.prototype.OrgRID = ''

    /**
     * @db text not null
     * role 名称
     * @member {string} RoleName
     * @memberof user.DbRole
     * @instance
     */
    DbRole.prototype.RoleName = ''

    /**
     * @db uuid --REFERENCES DbUser(RID) ON DELETE set null
     * role 创建者 rid
     * @member {string} Creator
     * @memberof user.DbRole
     * @instance
     */
    DbRole.prototype.Creator = ''

    /**
     * @db int
     * 是否是内置的角色，内置角色不能删除
     * @member {number} IsBuiltIn
     * @memberof user.DbRole
     * @instance
     */
    DbRole.prototype.IsBuiltIn = 0

    /**
     * @db jsonb not null default  '{}'::jsonb
     * 其它设置，可以在里面扩展需要用到的其它信息
     * @member {string} Setting
     * @memberof user.DbRole
     * @instance
     */
    DbRole.prototype.Setting = ''

    /**
     * @db int default 100
     * sort value
     * @member {number} SortValue
     * @memberof user.DbRole
     * @instance
     */
    DbRole.prototype.SortValue = 0

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     * @member {string} UpdatedAt
     * @memberof user.DbRole
     * @instance
     */
    DbRole.prototype.UpdatedAt = ''

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     * @member {string} UpdatedDC
     * @memberof user.DbRole
     * @instance
     */
    DbRole.prototype.UpdatedDC = ''

    /**
     * Encodes the specified DbRole message. Does not implicitly {@link user.DbRole.verify|verify} messages.
     * @function encode
     * @memberof user.DbRole
     * @static
     * @param {user.IDbRole} message DbRole message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    DbRole.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (message.RID != null && Object.hasOwnProperty.call(message, 'RID'))
        writer.uint32(/* id 1, wireType 2 =*/ 10).string(message.RID)
      if (
        message.OrgRID != null &&
        Object.hasOwnProperty.call(message, 'OrgRID')
      )
        writer.uint32(/* id 2, wireType 2 =*/ 18).string(message.OrgRID)
      if (
        message.RoleName != null &&
        Object.hasOwnProperty.call(message, 'RoleName')
      )
        writer.uint32(/* id 4, wireType 2 =*/ 34).string(message.RoleName)
      if (
        message.Creator != null &&
        Object.hasOwnProperty.call(message, 'Creator')
      )
        writer.uint32(/* id 5, wireType 2 =*/ 42).string(message.Creator)
      if (
        message.IsBuiltIn != null &&
        Object.hasOwnProperty.call(message, 'IsBuiltIn')
      )
        writer.uint32(/* id 6, wireType 0 =*/ 48).int32(message.IsBuiltIn)
      if (
        message.Setting != null &&
        Object.hasOwnProperty.call(message, 'Setting')
      )
        writer.uint32(/* id 9, wireType 2 =*/ 74).string(message.Setting)
      if (
        message.SortValue != null &&
        Object.hasOwnProperty.call(message, 'SortValue')
      )
        writer.uint32(/* id 10, wireType 0 =*/ 80).int32(message.SortValue)
      if (
        message.UpdatedAt != null &&
        Object.hasOwnProperty.call(message, 'UpdatedAt')
      )
        writer.uint32(/* id 14, wireType 2 =*/ 114).string(message.UpdatedAt)
      if (
        message.UpdatedDC != null &&
        Object.hasOwnProperty.call(message, 'UpdatedDC')
      )
        writer.uint32(/* id 15, wireType 2 =*/ 122).string(message.UpdatedDC)
      return writer
    }

    /**
     * Decodes a DbRole message from the specified reader or buffer.
     * @function decode
     * @memberof user.DbRole
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {user.DbRole} DbRole
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    DbRole.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.user.DbRole()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.RID = reader.string()
            break
          case 2:
            message.OrgRID = reader.string()
            break
          case 4:
            message.RoleName = reader.string()
            break
          case 5:
            message.Creator = reader.string()
            break
          case 6:
            message.IsBuiltIn = reader.int32()
            break
          case 9:
            message.Setting = reader.string()
            break
          case 10:
            message.SortValue = reader.int32()
            break
          case 14:
            message.UpdatedAt = reader.string()
            break
          case 15:
            message.UpdatedDC = reader.string()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return DbRole
  })()

  user.DbRolePermission = (function () {
    /**
     * Properties of a DbRolePermission.
     * @memberof user
     * @interface IDbRolePermission
     * @property {string|null} [RID] @db uuid primary key
     * 行ID
     * @property {string|null} [RoleRID] @db uuid not null REFERENCES DbRole(RID) ON DELETE CASCADE
     * role rid
     * @property {string|null} [PermissionRID] @db uuid not null REFERENCES DbPermission(RID) ON DELETE CASCADE
     * permission rid
     * @property {string|null} [Creator] @db uuid --REFERENCES DbUser(RID) ON DELETE set null
     * role 创建者 rid
     * @property {string|null} [UpdatedAt] @db timestamp not null default now_utc()
     * 数据最后修改时间
     * @property {string|null} [UpdatedDC] @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */

    /**
     * Constructs a new DbRolePermission.
     * @memberof user
     * @classdesc 角色权限信息表
     * @rpc crud pcrud
     * @implements IDbRolePermission
     * @constructor
     * @param {user.IDbRolePermission=} [properties] Properties to set
     */
    function DbRolePermission(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * @db uuid primary key
     * 行ID
     * @member {string} RID
     * @memberof user.DbRolePermission
     * @instance
     */
    DbRolePermission.prototype.RID = ''

    /**
     * @db uuid not null REFERENCES DbRole(RID) ON DELETE CASCADE
     * role rid
     * @member {string} RoleRID
     * @memberof user.DbRolePermission
     * @instance
     */
    DbRolePermission.prototype.RoleRID = ''

    /**
     * @db uuid not null REFERENCES DbPermission(RID) ON DELETE CASCADE
     * permission rid
     * @member {string} PermissionRID
     * @memberof user.DbRolePermission
     * @instance
     */
    DbRolePermission.prototype.PermissionRID = ''

    /**
     * @db uuid --REFERENCES DbUser(RID) ON DELETE set null
     * role 创建者 rid
     * @member {string} Creator
     * @memberof user.DbRolePermission
     * @instance
     */
    DbRolePermission.prototype.Creator = ''

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     * @member {string} UpdatedAt
     * @memberof user.DbRolePermission
     * @instance
     */
    DbRolePermission.prototype.UpdatedAt = ''

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     * @member {string} UpdatedDC
     * @memberof user.DbRolePermission
     * @instance
     */
    DbRolePermission.prototype.UpdatedDC = ''

    /**
     * Encodes the specified DbRolePermission message. Does not implicitly {@link user.DbRolePermission.verify|verify} messages.
     * @function encode
     * @memberof user.DbRolePermission
     * @static
     * @param {user.IDbRolePermission} message DbRolePermission message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    DbRolePermission.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (message.RID != null && Object.hasOwnProperty.call(message, 'RID'))
        writer.uint32(/* id 1, wireType 2 =*/ 10).string(message.RID)
      if (
        message.RoleRID != null &&
        Object.hasOwnProperty.call(message, 'RoleRID')
      )
        writer.uint32(/* id 2, wireType 2 =*/ 18).string(message.RoleRID)
      if (
        message.PermissionRID != null &&
        Object.hasOwnProperty.call(message, 'PermissionRID')
      )
        writer.uint32(/* id 4, wireType 2 =*/ 34).string(message.PermissionRID)
      if (
        message.Creator != null &&
        Object.hasOwnProperty.call(message, 'Creator')
      )
        writer.uint32(/* id 5, wireType 2 =*/ 42).string(message.Creator)
      if (
        message.UpdatedAt != null &&
        Object.hasOwnProperty.call(message, 'UpdatedAt')
      )
        writer.uint32(/* id 14, wireType 2 =*/ 114).string(message.UpdatedAt)
      if (
        message.UpdatedDC != null &&
        Object.hasOwnProperty.call(message, 'UpdatedDC')
      )
        writer.uint32(/* id 15, wireType 2 =*/ 122).string(message.UpdatedDC)
      return writer
    }

    /**
     * Decodes a DbRolePermission message from the specified reader or buffer.
     * @function decode
     * @memberof user.DbRolePermission
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {user.DbRolePermission} DbRolePermission
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    DbRolePermission.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.user.DbRolePermission()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.RID = reader.string()
            break
          case 2:
            message.RoleRID = reader.string()
            break
          case 4:
            message.PermissionRID = reader.string()
            break
          case 5:
            message.Creator = reader.string()
            break
          case 14:
            message.UpdatedAt = reader.string()
            break
          case 15:
            message.UpdatedDC = reader.string()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return DbRolePermission
  })()

  user.DbUser = (function () {
    /**
     * Properties of a DbUser.
     * @memberof user
     * @interface IDbUser
     * @property {string|null} [RID] @db uuid primary key
     * 行ID
     * @property {string|null} [OrgRID] @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 用户所属的群组
     * @property {string|null} [UserID] @db varchar(16) not null unique
     * 用户编号
     * @property {number|null} [UserType] @db int
     * 用户类型
     * @property {string|null} [UserName] @db varchar(32) not null
     * 用户名
     * @property {string|null} [Note] @db text
     * 备注
     * @property {string|null} [Phone] @db text
     * 用户电话
     * @property {string|null} [Image] @db uuid REFERENCES DbImage(RID) ON DELETE SET NULL
     * 用户图片
     * @property {string|null} [Setting] @db jsonb not null default  '{}'::jsonb
     * 其它设置，可以在里面扩展需要用到的其它信息
     * @property {string|null} [LoginName] @db varchar(32) unique
     * 用户登录名,NULL(空)表示不能登录系统
     * @property {string|null} [LoginPass] @db text default ''
     * 用户登录密码,为base64(sha256(user_name+user_password)),系统不保存真实的密码
     * @property {string|null} [UpdatedAt] @db timestamp not null default now_utc()
     * 数据最后修改时间
     * @property {string|null} [UpdatedDC] @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */

    /**
     * Constructs a new DbUser.
     * @memberof user
     * @classdesc 用户表
     * @rpc crud pcrud
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbUserOrgRID on DbUser USING hash(OrgRID);
     * @dbpost INSERT INTO dbuser(rid, orgrid, userid, usertype, username, phone, image, setting, loginname, loginpass, updatedat, updateddc)VALUES('00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', 'ttt', 0, 'ttt', '', NULL, '{}'::jsonb, 'ttt', 'UwhpDj0xMGNzZw8sXYZOLGBHmH/q9Wxf7aB6/EbT43Q=', now_utc(), '') ON CONFLICT  DO NOTHING;
     * @implements IDbUser
     * @constructor
     * @param {user.IDbUser=} [properties] Properties to set
     */
    function DbUser(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * @db uuid primary key
     * 行ID
     * @member {string} RID
     * @memberof user.DbUser
     * @instance
     */
    DbUser.prototype.RID = ''

    /**
     * @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 用户所属的群组
     * @member {string} OrgRID
     * @memberof user.DbUser
     * @instance
     */
    DbUser.prototype.OrgRID = ''

    /**
     * @db varchar(16) not null unique
     * 用户编号
     * @member {string} UserID
     * @memberof user.DbUser
     * @instance
     */
    DbUser.prototype.UserID = ''

    /**
     * @db int
     * 用户类型
     * @member {number} UserType
     * @memberof user.DbUser
     * @instance
     */
    DbUser.prototype.UserType = 0

    /**
     * @db varchar(32) not null
     * 用户名
     * @member {string} UserName
     * @memberof user.DbUser
     * @instance
     */
    DbUser.prototype.UserName = ''

    /**
     * @db text
     * 备注
     * @member {string} Note
     * @memberof user.DbUser
     * @instance
     */
    DbUser.prototype.Note = ''

    /**
     * @db text
     * 用户电话
     * @member {string} Phone
     * @memberof user.DbUser
     * @instance
     */
    DbUser.prototype.Phone = ''

    /**
     * @db uuid REFERENCES DbImage(RID) ON DELETE SET NULL
     * 用户图片
     * @member {string} Image
     * @memberof user.DbUser
     * @instance
     */
    DbUser.prototype.Image = ''

    /**
     * @db jsonb not null default  '{}'::jsonb
     * 其它设置，可以在里面扩展需要用到的其它信息
     * @member {string} Setting
     * @memberof user.DbUser
     * @instance
     */
    DbUser.prototype.Setting = ''

    /**
     * @db varchar(32) unique
     * 用户登录名,NULL(空)表示不能登录系统
     * @member {string} LoginName
     * @memberof user.DbUser
     * @instance
     */
    DbUser.prototype.LoginName = ''

    /**
     * @db text default ''
     * 用户登录密码,为base64(sha256(user_name+user_password)),系统不保存真实的密码
     * @member {string} LoginPass
     * @memberof user.DbUser
     * @instance
     */
    DbUser.prototype.LoginPass = ''

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     * @member {string} UpdatedAt
     * @memberof user.DbUser
     * @instance
     */
    DbUser.prototype.UpdatedAt = ''

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     * @member {string} UpdatedDC
     * @memberof user.DbUser
     * @instance
     */
    DbUser.prototype.UpdatedDC = ''

    /**
     * Encodes the specified DbUser message. Does not implicitly {@link user.DbUser.verify|verify} messages.
     * @function encode
     * @memberof user.DbUser
     * @static
     * @param {user.IDbUser} message DbUser message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    DbUser.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (message.RID != null && Object.hasOwnProperty.call(message, 'RID'))
        writer.uint32(/* id 1, wireType 2 =*/ 10).string(message.RID)
      if (
        message.OrgRID != null &&
        Object.hasOwnProperty.call(message, 'OrgRID')
      )
        writer.uint32(/* id 2, wireType 2 =*/ 18).string(message.OrgRID)
      if (
        message.UserID != null &&
        Object.hasOwnProperty.call(message, 'UserID')
      )
        writer.uint32(/* id 3, wireType 2 =*/ 26).string(message.UserID)
      if (
        message.UserType != null &&
        Object.hasOwnProperty.call(message, 'UserType')
      )
        writer.uint32(/* id 4, wireType 0 =*/ 32).int32(message.UserType)
      if (
        message.UserName != null &&
        Object.hasOwnProperty.call(message, 'UserName')
      )
        writer.uint32(/* id 5, wireType 2 =*/ 42).string(message.UserName)
      if (message.Note != null && Object.hasOwnProperty.call(message, 'Note'))
        writer.uint32(/* id 6, wireType 2 =*/ 50).string(message.Note)
      if (message.Phone != null && Object.hasOwnProperty.call(message, 'Phone'))
        writer.uint32(/* id 7, wireType 2 =*/ 58).string(message.Phone)
      if (message.Image != null && Object.hasOwnProperty.call(message, 'Image'))
        writer.uint32(/* id 8, wireType 2 =*/ 66).string(message.Image)
      if (
        message.Setting != null &&
        Object.hasOwnProperty.call(message, 'Setting')
      )
        writer.uint32(/* id 9, wireType 2 =*/ 74).string(message.Setting)
      if (
        message.LoginName != null &&
        Object.hasOwnProperty.call(message, 'LoginName')
      )
        writer.uint32(/* id 10, wireType 2 =*/ 82).string(message.LoginName)
      if (
        message.LoginPass != null &&
        Object.hasOwnProperty.call(message, 'LoginPass')
      )
        writer.uint32(/* id 11, wireType 2 =*/ 90).string(message.LoginPass)
      if (
        message.UpdatedAt != null &&
        Object.hasOwnProperty.call(message, 'UpdatedAt')
      )
        writer.uint32(/* id 14, wireType 2 =*/ 114).string(message.UpdatedAt)
      if (
        message.UpdatedDC != null &&
        Object.hasOwnProperty.call(message, 'UpdatedDC')
      )
        writer.uint32(/* id 15, wireType 2 =*/ 122).string(message.UpdatedDC)
      return writer
    }

    /**
     * Decodes a DbUser message from the specified reader or buffer.
     * @function decode
     * @memberof user.DbUser
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {user.DbUser} DbUser
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    DbUser.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.user.DbUser()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.RID = reader.string()
            break
          case 2:
            message.OrgRID = reader.string()
            break
          case 3:
            message.UserID = reader.string()
            break
          case 4:
            message.UserType = reader.int32()
            break
          case 5:
            message.UserName = reader.string()
            break
          case 6:
            message.Note = reader.string()
            break
          case 7:
            message.Phone = reader.string()
            break
          case 8:
            message.Image = reader.string()
            break
          case 9:
            message.Setting = reader.string()
            break
          case 10:
            message.LoginName = reader.string()
            break
          case 11:
            message.LoginPass = reader.string()
            break
          case 14:
            message.UpdatedAt = reader.string()
            break
          case 15:
            message.UpdatedDC = reader.string()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return DbUser
  })()

  user.DbUserRole = (function () {
    /**
     * Properties of a DbUserRole.
     * @memberof user
     * @interface IDbUserRole
     * @property {string|null} [RID] @db uuid primary key
     * 行ID
     * @property {string|null} [OrgRID] @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 用户所属的群组
     * @property {string|null} [UserRID] @db uuid not null REFERENCES DbUser(RID) ON DELETE CASCADE
     * 用户rid
     * @property {string|null} [RoleRID] @db uuid not null REFERENCES DbRole(RID) ON DELETE CASCADE
     * role rid
     * @property {string|null} [UpdatedAt] @db timestamp not null default now_utc()
     * 数据最后修改时间
     * @property {string|null} [UpdatedDC] @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */

    /**
     * Constructs a new DbUserRole.
     * @memberof user
     * @classdesc 用户角色数据表
     * 一个用户可以有多个角色
     * @rpc crud pcrud
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbUserRoleUserRID on DbUserRole USING hash(UserRID);
     * @implements IDbUserRole
     * @constructor
     * @param {user.IDbUserRole=} [properties] Properties to set
     */
    function DbUserRole(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * @db uuid primary key
     * 行ID
     * @member {string} RID
     * @memberof user.DbUserRole
     * @instance
     */
    DbUserRole.prototype.RID = ''

    /**
     * @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 用户所属的群组
     * @member {string} OrgRID
     * @memberof user.DbUserRole
     * @instance
     */
    DbUserRole.prototype.OrgRID = ''

    /**
     * @db uuid not null REFERENCES DbUser(RID) ON DELETE CASCADE
     * 用户rid
     * @member {string} UserRID
     * @memberof user.DbUserRole
     * @instance
     */
    DbUserRole.prototype.UserRID = ''

    /**
     * @db uuid not null REFERENCES DbRole(RID) ON DELETE CASCADE
     * role rid
     * @member {string} RoleRID
     * @memberof user.DbUserRole
     * @instance
     */
    DbUserRole.prototype.RoleRID = ''

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     * @member {string} UpdatedAt
     * @memberof user.DbUserRole
     * @instance
     */
    DbUserRole.prototype.UpdatedAt = ''

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     * @member {string} UpdatedDC
     * @memberof user.DbUserRole
     * @instance
     */
    DbUserRole.prototype.UpdatedDC = ''

    /**
     * Encodes the specified DbUserRole message. Does not implicitly {@link user.DbUserRole.verify|verify} messages.
     * @function encode
     * @memberof user.DbUserRole
     * @static
     * @param {user.IDbUserRole} message DbUserRole message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    DbUserRole.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (message.RID != null && Object.hasOwnProperty.call(message, 'RID'))
        writer.uint32(/* id 1, wireType 2 =*/ 10).string(message.RID)
      if (
        message.OrgRID != null &&
        Object.hasOwnProperty.call(message, 'OrgRID')
      )
        writer.uint32(/* id 2, wireType 2 =*/ 18).string(message.OrgRID)
      if (
        message.UserRID != null &&
        Object.hasOwnProperty.call(message, 'UserRID')
      )
        writer.uint32(/* id 3, wireType 2 =*/ 26).string(message.UserRID)
      if (
        message.RoleRID != null &&
        Object.hasOwnProperty.call(message, 'RoleRID')
      )
        writer.uint32(/* id 4, wireType 2 =*/ 34).string(message.RoleRID)
      if (
        message.UpdatedAt != null &&
        Object.hasOwnProperty.call(message, 'UpdatedAt')
      )
        writer.uint32(/* id 14, wireType 2 =*/ 114).string(message.UpdatedAt)
      if (
        message.UpdatedDC != null &&
        Object.hasOwnProperty.call(message, 'UpdatedDC')
      )
        writer.uint32(/* id 15, wireType 2 =*/ 122).string(message.UpdatedDC)
      return writer
    }

    /**
     * Decodes a DbUserRole message from the specified reader or buffer.
     * @function decode
     * @memberof user.DbUserRole
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {user.DbUserRole} DbUserRole
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    DbUserRole.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.user.DbUserRole()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.RID = reader.string()
            break
          case 2:
            message.OrgRID = reader.string()
            break
          case 3:
            message.UserRID = reader.string()
            break
          case 4:
            message.RoleRID = reader.string()
            break
          case 14:
            message.UpdatedAt = reader.string()
            break
          case 15:
            message.UpdatedDC = reader.string()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return DbUserRole
  })()

  return user
})())

export const org = ($root.org = (() => {
  /**
   * Namespace org.
   * @exports org
   * @namespace
   */
  const org = $root.org || {}

  org.DbOrg = (function () {
    /**
     * Properties of a DbOrg.
     * @memberof org
     * @interface IDbOrg
     * @property {string|null} [RID] @db uuid primary key
     * 行ID
     * @property {string|null} [OrgID] @db varchar(64) unique not null
     * 组织机构自编号
     * @property {number|null} [OrgType] @db int default 0
     * @property {number|null} [SortValue] @db int default 100
     * 排序值,由用户指定,不用管拼音或者其它语种的排序
     * @property {string|null} [ShortName] @db varchar(32) not null
     * 机构名称,缩写
     * @property {string|null} [FullName] @db varchar(256)
     * 机构名称,全称
     * @property {string|null} [Note] @db text
     * 机构描述/备注信息
     * @property {string|null} [Setting] @db jsonb default  '{}'::jsonb
     * 其它设置，可以在里面扩展需要用到的其它信息
     * @property {string|null} [CreatorRID] @db uuid
     * 创建者的rid
     * @property {string|null} [OrgRID] @db uuid
     * 此组织的上级机构
     * @property {string|null} [UpdatedAt] @db timestamp not null default now_utc()
     * 数据最后修改时间
     * @property {string|null} [UpdatedDC] @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */

    /**
     * Constructs a new DbOrg.
     * @memberof org
     * @classdesc 群组/组织结构表
     * @rpc crud pcrud
     * @dbpost DO $$ BEGIN BEGIN
     * @dbpost   ALTER TABLE DbOrg ADD CONSTRAINT fkDbOrgParentRID  FOREIGN KEY (OrgRID) REFERENCES DbOrg(RID);
     * @dbpost   EXCEPTION WHEN duplicate_object THEN --do nothing
     * @dbpost END; END $$;
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbOrgCreatorRID on DbOrg USING hash(CreatorRID);
     * @dbpost INSERT INTO dborg(rid, orgid, orgtype, sortvalue, shortname, fullname, note, setting, OrgRID, updatedat, updateddc)VALUES('00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', 0, 100, 'root', 'root', '', '{}'::jsonb, NULL, now_utc(), '')  ON CONFLICT  DO NOTHING;
     * @implements IDbOrg
     * @constructor
     * @param {org.IDbOrg=} [properties] Properties to set
     */
    function DbOrg(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * @db uuid primary key
     * 行ID
     * @member {string} RID
     * @memberof org.DbOrg
     * @instance
     */
    DbOrg.prototype.RID = ''

    /**
     * @db varchar(64) unique not null
     * 组织机构自编号
     * @member {string} OrgID
     * @memberof org.DbOrg
     * @instance
     */
    DbOrg.prototype.OrgID = ''

    /**
     * @db int default 0
     * @member {number} OrgType
     * @memberof org.DbOrg
     * @instance
     */
    DbOrg.prototype.OrgType = 0

    /**
     * @db int default 100
     * 排序值,由用户指定,不用管拼音或者其它语种的排序
     * @member {number} SortValue
     * @memberof org.DbOrg
     * @instance
     */
    DbOrg.prototype.SortValue = 0

    /**
     * @db varchar(32) not null
     * 机构名称,缩写
     * @member {string} ShortName
     * @memberof org.DbOrg
     * @instance
     */
    DbOrg.prototype.ShortName = ''

    /**
     * @db varchar(256)
     * 机构名称,全称
     * @member {string} FullName
     * @memberof org.DbOrg
     * @instance
     */
    DbOrg.prototype.FullName = ''

    /**
     * @db text
     * 机构描述/备注信息
     * @member {string} Note
     * @memberof org.DbOrg
     * @instance
     */
    DbOrg.prototype.Note = ''

    /**
     * @db jsonb default  '{}'::jsonb
     * 其它设置，可以在里面扩展需要用到的其它信息
     * @member {string} Setting
     * @memberof org.DbOrg
     * @instance
     */
    DbOrg.prototype.Setting = ''

    /**
     * @db uuid
     * 创建者的rid
     * @member {string} CreatorRID
     * @memberof org.DbOrg
     * @instance
     */
    DbOrg.prototype.CreatorRID = ''

    /**
     * @db uuid
     * 此组织的上级机构
     * @member {string} OrgRID
     * @memberof org.DbOrg
     * @instance
     */
    DbOrg.prototype.OrgRID = ''

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     * @member {string} UpdatedAt
     * @memberof org.DbOrg
     * @instance
     */
    DbOrg.prototype.UpdatedAt = ''

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     * @member {string} UpdatedDC
     * @memberof org.DbOrg
     * @instance
     */
    DbOrg.prototype.UpdatedDC = ''

    /**
     * Encodes the specified DbOrg message. Does not implicitly {@link org.DbOrg.verify|verify} messages.
     * @function encode
     * @memberof org.DbOrg
     * @static
     * @param {org.IDbOrg} message DbOrg message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    DbOrg.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (message.RID != null && Object.hasOwnProperty.call(message, 'RID'))
        writer.uint32(/* id 1, wireType 2 =*/ 10).string(message.RID)
      if (message.OrgID != null && Object.hasOwnProperty.call(message, 'OrgID'))
        writer.uint32(/* id 2, wireType 2 =*/ 18).string(message.OrgID)
      if (
        message.OrgType != null &&
        Object.hasOwnProperty.call(message, 'OrgType')
      )
        writer.uint32(/* id 3, wireType 0 =*/ 24).int32(message.OrgType)
      if (
        message.SortValue != null &&
        Object.hasOwnProperty.call(message, 'SortValue')
      )
        writer.uint32(/* id 4, wireType 0 =*/ 32).int32(message.SortValue)
      if (
        message.ShortName != null &&
        Object.hasOwnProperty.call(message, 'ShortName')
      )
        writer.uint32(/* id 5, wireType 2 =*/ 42).string(message.ShortName)
      if (
        message.FullName != null &&
        Object.hasOwnProperty.call(message, 'FullName')
      )
        writer.uint32(/* id 6, wireType 2 =*/ 50).string(message.FullName)
      if (message.Note != null && Object.hasOwnProperty.call(message, 'Note'))
        writer.uint32(/* id 7, wireType 2 =*/ 58).string(message.Note)
      if (
        message.Setting != null &&
        Object.hasOwnProperty.call(message, 'Setting')
      )
        writer.uint32(/* id 8, wireType 2 =*/ 66).string(message.Setting)
      if (
        message.CreatorRID != null &&
        Object.hasOwnProperty.call(message, 'CreatorRID')
      )
        writer.uint32(/* id 9, wireType 2 =*/ 74).string(message.CreatorRID)
      if (
        message.OrgRID != null &&
        Object.hasOwnProperty.call(message, 'OrgRID')
      )
        writer.uint32(/* id 11, wireType 2 =*/ 90).string(message.OrgRID)
      if (
        message.UpdatedAt != null &&
        Object.hasOwnProperty.call(message, 'UpdatedAt')
      )
        writer.uint32(/* id 14, wireType 2 =*/ 114).string(message.UpdatedAt)
      if (
        message.UpdatedDC != null &&
        Object.hasOwnProperty.call(message, 'UpdatedDC')
      )
        writer.uint32(/* id 15, wireType 2 =*/ 122).string(message.UpdatedDC)
      return writer
    }

    /**
     * Decodes a DbOrg message from the specified reader or buffer.
     * @function decode
     * @memberof org.DbOrg
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {org.DbOrg} DbOrg
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    DbOrg.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.org.DbOrg()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.RID = reader.string()
            break
          case 2:
            message.OrgID = reader.string()
            break
          case 3:
            message.OrgType = reader.int32()
            break
          case 4:
            message.SortValue = reader.int32()
            break
          case 5:
            message.ShortName = reader.string()
            break
          case 6:
            message.FullName = reader.string()
            break
          case 7:
            message.Note = reader.string()
            break
          case 8:
            message.Setting = reader.string()
            break
          case 9:
            message.CreatorRID = reader.string()
            break
          case 11:
            message.OrgRID = reader.string()
            break
          case 14:
            message.UpdatedAt = reader.string()
            break
          case 15:
            message.UpdatedDC = reader.string()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return DbOrg
  })()

  return org
})())

export const rpc = ($root.rpc = (() => {
  /**
   * Namespace rpc.
   * @exports rpc
   * @namespace
   */
  const rpc = $root.rpc || {}

  rpc.RpcEmpty = (function () {
    /**
     * Properties of a RpcEmpty.
     * @memberof rpc
     * @interface IRpcEmpty
     */

    /**
     * Constructs a new RpcEmpty.
     * @memberof rpc
     * @classdesc 空消息，grpc要求必须有req,res message
     * @implements IRpcEmpty
     * @constructor
     * @param {rpc.IRpcEmpty=} [properties] Properties to set
     */
    function RpcEmpty(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * Encodes the specified RpcEmpty message. Does not implicitly {@link rpc.RpcEmpty.verify|verify} messages.
     * @function encode
     * @memberof rpc.RpcEmpty
     * @static
     * @param {rpc.IRpcEmpty} message RpcEmpty message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    RpcEmpty.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      return writer
    }

    /**
     * Decodes a RpcEmpty message from the specified reader or buffer.
     * @function decode
     * @memberof rpc.RpcEmpty
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {rpc.RpcEmpty} RpcEmpty
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    RpcEmpty.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.rpc.RpcEmpty()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return RpcEmpty
  })()

  rpc.RpcCommon = (function () {
    /**
     * Properties of a RpcCommon.
     * @memberof rpc
     * @interface IRpcCommon
     * @property {number|null} [Code] 代码，要求各个rpc的不同，会有不同的值
     * @property {string|null} [System] optional system
     * @property {string|null} [SessionID] optional session id
     * @property {string|null} [Err] optional Error
     * @property {Uint8Array|null} [Body] optional body
     */

    /**
     * Constructs a new RpcCommon.
     * @memberof rpc
     * @classdesc 一般消息
     * @implements IRpcCommon
     * @constructor
     * @param {rpc.IRpcCommon=} [properties] Properties to set
     */
    function RpcCommon(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 代码，要求各个rpc的不同，会有不同的值
     * @member {number} Code
     * @memberof rpc.RpcCommon
     * @instance
     */
    RpcCommon.prototype.Code = 0

    /**
     * optional system
     * @member {string} System
     * @memberof rpc.RpcCommon
     * @instance
     */
    RpcCommon.prototype.System = ''

    /**
     * optional session id
     * @member {string} SessionID
     * @memberof rpc.RpcCommon
     * @instance
     */
    RpcCommon.prototype.SessionID = ''

    /**
     * optional Error
     * @member {string} Err
     * @memberof rpc.RpcCommon
     * @instance
     */
    RpcCommon.prototype.Err = ''

    /**
     * optional body
     * @member {Uint8Array} Body
     * @memberof rpc.RpcCommon
     * @instance
     */
    RpcCommon.prototype.Body = $util.newBuffer([])

    /**
     * Encodes the specified RpcCommon message. Does not implicitly {@link rpc.RpcCommon.verify|verify} messages.
     * @function encode
     * @memberof rpc.RpcCommon
     * @static
     * @param {rpc.IRpcCommon} message RpcCommon message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    RpcCommon.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (message.Code != null && Object.hasOwnProperty.call(message, 'Code'))
        writer.uint32(/* id 1, wireType 0 =*/ 8).int32(message.Code)
      if (
        message.System != null &&
        Object.hasOwnProperty.call(message, 'System')
      )
        writer.uint32(/* id 2, wireType 2 =*/ 18).string(message.System)
      if (
        message.SessionID != null &&
        Object.hasOwnProperty.call(message, 'SessionID')
      )
        writer.uint32(/* id 3, wireType 2 =*/ 26).string(message.SessionID)
      if (message.Err != null && Object.hasOwnProperty.call(message, 'Err'))
        writer.uint32(/* id 4, wireType 2 =*/ 34).string(message.Err)
      if (message.Body != null && Object.hasOwnProperty.call(message, 'Body'))
        writer.uint32(/* id 5, wireType 2 =*/ 42).bytes(message.Body)
      return writer
    }

    /**
     * Decodes a RpcCommon message from the specified reader or buffer.
     * @function decode
     * @memberof rpc.RpcCommon
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {rpc.RpcCommon} RpcCommon
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    RpcCommon.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.rpc.RpcCommon()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.Code = reader.int32()
            break
          case 2:
            message.System = reader.string()
            break
          case 3:
            message.SessionID = reader.string()
            break
          case 4:
            message.Err = reader.string()
            break
          case 5:
            message.Body = reader.bytes()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return RpcCommon
  })()

  return rpc
})())

export const crud = ($root.crud = (() => {
  /**
   * Namespace crud.
   * @exports crud
   * @namespace
   */
  const crud = $root.crud || {}

  crud.DMLResult = (function () {
    /**
     * Properties of a DMLResult.
     * @memberof crud
     * @interface IDMLResult
     * @property {number|null} [AffectedRow] 影响的行数，如果成功>0,否则=0
     * @property {string|null} [errInfo] 错误信息，如果有的话
     * @property {string|null} [Note] 附加信息,json格式
     */

    /**
     * Constructs a new DMLResult.
     * @memberof crud
     * @classdesc CRUD DML结果
     * @implements IDMLResult
     * @constructor
     * @param {crud.IDMLResult=} [properties] Properties to set
     */
    function DMLResult(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 影响的行数，如果成功>0,否则=0
     * @member {number} AffectedRow
     * @memberof crud.DMLResult
     * @instance
     */
    DMLResult.prototype.AffectedRow = 0

    /**
     * 错误信息，如果有的话
     * @member {string} errInfo
     * @memberof crud.DMLResult
     * @instance
     */
    DMLResult.prototype.errInfo = ''

    /**
     * 附加信息,json格式
     * @member {string} Note
     * @memberof crud.DMLResult
     * @instance
     */
    DMLResult.prototype.Note = ''

    /**
     * Encodes the specified DMLResult message. Does not implicitly {@link crud.DMLResult.verify|verify} messages.
     * @function encode
     * @memberof crud.DMLResult
     * @static
     * @param {crud.IDMLResult} message DMLResult message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    DMLResult.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (
        message.AffectedRow != null &&
        Object.hasOwnProperty.call(message, 'AffectedRow')
      )
        writer.uint32(/* id 1, wireType 0 =*/ 8).int32(message.AffectedRow)
      if (
        message.errInfo != null &&
        Object.hasOwnProperty.call(message, 'errInfo')
      )
        writer.uint32(/* id 2, wireType 2 =*/ 18).string(message.errInfo)
      if (message.Note != null && Object.hasOwnProperty.call(message, 'Note'))
        writer.uint32(/* id 3, wireType 2 =*/ 26).string(message.Note)
      return writer
    }

    /**
     * Decodes a DMLResult message from the specified reader or buffer.
     * @function decode
     * @memberof crud.DMLResult
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {crud.DMLResult} DMLResult
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    DMLResult.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.crud.DMLResult()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.AffectedRow = reader.int32()
            break
          case 2:
            message.errInfo = reader.string()
            break
          case 3:
            message.Note = reader.string()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return DMLResult
  })()

  crud.DMLParam = (function () {
    /**
     * Properties of a DMLParam.
     * @memberof crud
     * @interface IDMLParam
     * @property {Array.<string>|null} [KeyColumn] 条件字段名
     * @property {Array.<string>|null} [ResultColumn] 结果字段名
     * @property {Array.<string>|null} [KeyValue] 条件值
     */

    /**
     * Constructs a new DMLParam.
     * @memberof crud
     * @classdesc 简单CRUD里面用到的条件和结果字段列表
     * @implements IDMLParam
     * @constructor
     * @param {crud.IDMLParam=} [properties] Properties to set
     */
    function DMLParam(properties) {
      this.KeyColumn = []
      this.ResultColumn = []
      this.KeyValue = []
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 条件字段名
     * @member {Array.<string>} KeyColumn
     * @memberof crud.DMLParam
     * @instance
     */
    DMLParam.prototype.KeyColumn = $util.emptyArray

    /**
     * 结果字段名
     * @member {Array.<string>} ResultColumn
     * @memberof crud.DMLParam
     * @instance
     */
    DMLParam.prototype.ResultColumn = $util.emptyArray

    /**
     * 条件值
     * @member {Array.<string>} KeyValue
     * @memberof crud.DMLParam
     * @instance
     */
    DMLParam.prototype.KeyValue = $util.emptyArray

    /**
     * Encodes the specified DMLParam message. Does not implicitly {@link crud.DMLParam.verify|verify} messages.
     * @function encode
     * @memberof crud.DMLParam
     * @static
     * @param {crud.IDMLParam} message DMLParam message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    DMLParam.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (message.KeyColumn != null && message.KeyColumn.length)
        for (let i = 0; i < message.KeyColumn.length; ++i)
          writer.uint32(/* id 1, wireType 2 =*/ 10).string(message.KeyColumn[i])
      if (message.ResultColumn != null && message.ResultColumn.length)
        for (let i = 0; i < message.ResultColumn.length; ++i)
          writer
            .uint32(/* id 2, wireType 2 =*/ 18)
            .string(message.ResultColumn[i])
      if (message.KeyValue != null && message.KeyValue.length)
        for (let i = 0; i < message.KeyValue.length; ++i)
          writer.uint32(/* id 3, wireType 2 =*/ 26).string(message.KeyValue[i])
      return writer
    }

    /**
     * Decodes a DMLParam message from the specified reader or buffer.
     * @function decode
     * @memberof crud.DMLParam
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {crud.DMLParam} DMLParam
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    DMLParam.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.crud.DMLParam()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            if (!(message.KeyColumn && message.KeyColumn.length))
              message.KeyColumn = []
            message.KeyColumn.push(reader.string())
            break
          case 2:
            if (!(message.ResultColumn && message.ResultColumn.length))
              message.ResultColumn = []
            message.ResultColumn.push(reader.string())
            break
          case 3:
            if (!(message.KeyValue && message.KeyValue.length))
              message.KeyValue = []
            message.KeyValue.push(reader.string())
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return DMLParam
  })()

  crud.WhereItem = (function () {
    /**
     * Properties of a WhereItem.
     * @memberof crud
     * @interface IWhereItem
     * @property {string|null} [Field] fieldname
     * @property {string|null} [FieldCompareOperator] field compare operator
     * @property {string|null} [FieldValue] Field value
     */

    /**
     * Constructs a new WhereItem.
     * @memberof crud
     * @classdesc sql where额外条件项
     * @implements IWhereItem
     * @constructor
     * @param {crud.IWhereItem=} [properties] Properties to set
     */
    function WhereItem(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * fieldname
     * @member {string} Field
     * @memberof crud.WhereItem
     * @instance
     */
    WhereItem.prototype.Field = ''

    /**
     * field compare operator
     * @member {string} FieldCompareOperator
     * @memberof crud.WhereItem
     * @instance
     */
    WhereItem.prototype.FieldCompareOperator = ''

    /**
     * Field value
     * @member {string} FieldValue
     * @memberof crud.WhereItem
     * @instance
     */
    WhereItem.prototype.FieldValue = ''

    /**
     * Encodes the specified WhereItem message. Does not implicitly {@link crud.WhereItem.verify|verify} messages.
     * @function encode
     * @memberof crud.WhereItem
     * @static
     * @param {crud.IWhereItem} message WhereItem message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    WhereItem.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (message.Field != null && Object.hasOwnProperty.call(message, 'Field'))
        writer.uint32(/* id 5, wireType 2 =*/ 42).string(message.Field)
      if (
        message.FieldCompareOperator != null &&
        Object.hasOwnProperty.call(message, 'FieldCompareOperator')
      )
        writer
          .uint32(/* id 6, wireType 2 =*/ 50)
          .string(message.FieldCompareOperator)
      if (
        message.FieldValue != null &&
        Object.hasOwnProperty.call(message, 'FieldValue')
      )
        writer.uint32(/* id 7, wireType 2 =*/ 58).string(message.FieldValue)
      return writer
    }

    /**
     * Decodes a WhereItem message from the specified reader or buffer.
     * @function decode
     * @memberof crud.WhereItem
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {crud.WhereItem} WhereItem
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    WhereItem.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.crud.WhereItem()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 5:
            message.Field = reader.string()
            break
          case 6:
            message.FieldCompareOperator = reader.string()
            break
          case 7:
            message.FieldValue = reader.string()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return WhereItem
  })()

  crud.QueryParam = (function () {
    /**
     * Properties of a QueryParam.
     * @memberof crud
     * @interface IQueryParam
     * @property {Array.<string>|null} [ResultColumn] 想要的结果字段名，不填写为全要
     * @property {Array.<string>|null} [TimeColumn] Query时需要处理时区的字段,单数为字段名，双数为需要加减的的分钟数
     * 需要此功能必须把所有的结果列名填写在ResultColumn，否则无法处理
     * TimeColumn = ['ActionTime','480'，‘RecvTime','-60']
     * @property {Array.<crud.IWhereItem>|null} [Where] where额外条件项,只支持 and
     * @property {number|null} [Limit] 分页，需要的数据，如果Limit=0 and Offset=0,则视为不需要分页
     * @property {number|null} [Offset] 分页，从什么位置开始，如果Limit=0 and
     * Offset!=0,为从Offset的位置开始的所有数据
     * @property {Array.<string>|null} [OrderBy] 排序条件，每项为sql的order by 条件
     * select * from a order by col1 asc,col2 desc
     * OrderBy=["col1 asc","col2 desc"]
     * @property {number|null} [Batch] QueryBatch调用时,指定一次返回的数据量，不指定时后端按每次一个返回
     */

    /**
     * Constructs a new QueryParam.
     * @memberof crud
     * @classdesc 查询条件
     * @implements IQueryParam
     * @constructor
     * @param {crud.IQueryParam=} [properties] Properties to set
     */
    function QueryParam(properties) {
      this.ResultColumn = []
      this.TimeColumn = []
      this.Where = []
      this.OrderBy = []
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 想要的结果字段名，不填写为全要
     * @member {Array.<string>} ResultColumn
     * @memberof crud.QueryParam
     * @instance
     */
    QueryParam.prototype.ResultColumn = $util.emptyArray

    /**
     * Query时需要处理时区的字段,单数为字段名，双数为需要加减的的分钟数
     * 需要此功能必须把所有的结果列名填写在ResultColumn，否则无法处理
     * TimeColumn = ['ActionTime','480'，‘RecvTime','-60']
     * @member {Array.<string>} TimeColumn
     * @memberof crud.QueryParam
     * @instance
     */
    QueryParam.prototype.TimeColumn = $util.emptyArray

    /**
     * where额外条件项,只支持 and
     * @member {Array.<crud.IWhereItem>} Where
     * @memberof crud.QueryParam
     * @instance
     */
    QueryParam.prototype.Where = $util.emptyArray

    /**
     * 分页，需要的数据，如果Limit=0 and Offset=0,则视为不需要分页
     * @member {number} Limit
     * @memberof crud.QueryParam
     * @instance
     */
    QueryParam.prototype.Limit = 0

    /**
     * 分页，从什么位置开始，如果Limit=0 and
     * Offset!=0,为从Offset的位置开始的所有数据
     * @member {number} Offset
     * @memberof crud.QueryParam
     * @instance
     */
    QueryParam.prototype.Offset = 0

    /**
     * 排序条件，每项为sql的order by 条件
     * select * from a order by col1 asc,col2 desc
     * OrderBy=["col1 asc","col2 desc"]
     * @member {Array.<string>} OrderBy
     * @memberof crud.QueryParam
     * @instance
     */
    QueryParam.prototype.OrderBy = $util.emptyArray

    /**
     * QueryBatch调用时,指定一次返回的数据量，不指定时后端按每次一个返回
     * @member {number} Batch
     * @memberof crud.QueryParam
     * @instance
     */
    QueryParam.prototype.Batch = 0

    /**
     * Encodes the specified QueryParam message. Does not implicitly {@link crud.QueryParam.verify|verify} messages.
     * @function encode
     * @memberof crud.QueryParam
     * @static
     * @param {crud.IQueryParam} message QueryParam message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    QueryParam.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (message.ResultColumn != null && message.ResultColumn.length)
        for (let i = 0; i < message.ResultColumn.length; ++i)
          writer
            .uint32(/* id 1, wireType 2 =*/ 10)
            .string(message.ResultColumn[i])
      if (message.TimeColumn != null && message.TimeColumn.length)
        for (let i = 0; i < message.TimeColumn.length; ++i)
          writer
            .uint32(/* id 2, wireType 2 =*/ 18)
            .string(message.TimeColumn[i])
      if (message.Where != null && message.Where.length)
        for (let i = 0; i < message.Where.length; ++i)
          $root.crud.WhereItem.encode(
            message.Where[i],
            writer.uint32(/* id 5, wireType 2 =*/ 42).fork(),
          ).ldelim()
      if (message.Limit != null && Object.hasOwnProperty.call(message, 'Limit'))
        writer.uint32(/* id 6, wireType 0 =*/ 48).uint32(message.Limit)
      if (
        message.Offset != null &&
        Object.hasOwnProperty.call(message, 'Offset')
      )
        writer.uint32(/* id 7, wireType 0 =*/ 56).uint32(message.Offset)
      if (message.OrderBy != null && message.OrderBy.length)
        for (let i = 0; i < message.OrderBy.length; ++i)
          writer.uint32(/* id 8, wireType 2 =*/ 66).string(message.OrderBy[i])
      if (message.Batch != null && Object.hasOwnProperty.call(message, 'Batch'))
        writer.uint32(/* id 9, wireType 0 =*/ 72).uint32(message.Batch)
      return writer
    }

    /**
     * Decodes a QueryParam message from the specified reader or buffer.
     * @function decode
     * @memberof crud.QueryParam
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {crud.QueryParam} QueryParam
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    QueryParam.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.crud.QueryParam()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            if (!(message.ResultColumn && message.ResultColumn.length))
              message.ResultColumn = []
            message.ResultColumn.push(reader.string())
            break
          case 2:
            if (!(message.TimeColumn && message.TimeColumn.length))
              message.TimeColumn = []
            message.TimeColumn.push(reader.string())
            break
          case 5:
            if (!(message.Where && message.Where.length)) message.Where = []
            message.Where.push(
              $root.crud.WhereItem.decode(reader, reader.uint32()),
            )
            break
          case 6:
            message.Limit = reader.uint32()
            break
          case 7:
            message.Offset = reader.uint32()
            break
          case 8:
            if (!(message.OrderBy && message.OrderBy.length))
              message.OrderBy = []
            message.OrderBy.push(reader.string())
            break
          case 9:
            message.Batch = reader.uint32()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return QueryParam
  })()

  crud.PrivilegeParam = (function () {
    /**
     * Properties of a PrivilegeParam.
     * @memberof crud
     * @interface IPrivilegeParam
     * @property {string|null} [System] 系统名称
     * @property {string|null} [SessionID] SessionID
     * @property {crud.IQueryParam|null} [QueryCondition] 查询条件
     */

    /**
     * Constructs a new PrivilegeParam.
     * @memberof crud
     * @classdesc 取得用户有权限的数据
     * @implements IPrivilegeParam
     * @constructor
     * @param {crud.IPrivilegeParam=} [properties] Properties to set
     */
    function PrivilegeParam(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 系统名称
     * @member {string} System
     * @memberof crud.PrivilegeParam
     * @instance
     */
    PrivilegeParam.prototype.System = ''

    /**
     * SessionID
     * @member {string} SessionID
     * @memberof crud.PrivilegeParam
     * @instance
     */
    PrivilegeParam.prototype.SessionID = ''

    /**
     * 查询条件
     * @member {crud.IQueryParam|null|undefined} QueryCondition
     * @memberof crud.PrivilegeParam
     * @instance
     */
    PrivilegeParam.prototype.QueryCondition = null

    /**
     * Encodes the specified PrivilegeParam message. Does not implicitly {@link crud.PrivilegeParam.verify|verify} messages.
     * @function encode
     * @memberof crud.PrivilegeParam
     * @static
     * @param {crud.IPrivilegeParam} message PrivilegeParam message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    PrivilegeParam.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (
        message.System != null &&
        Object.hasOwnProperty.call(message, 'System')
      )
        writer.uint32(/* id 1, wireType 2 =*/ 10).string(message.System)
      if (
        message.SessionID != null &&
        Object.hasOwnProperty.call(message, 'SessionID')
      )
        writer.uint32(/* id 3, wireType 2 =*/ 26).string(message.SessionID)
      if (
        message.QueryCondition != null &&
        Object.hasOwnProperty.call(message, 'QueryCondition')
      )
        $root.crud.QueryParam.encode(
          message.QueryCondition,
          writer.uint32(/* id 5, wireType 2 =*/ 42).fork(),
        ).ldelim()
      return writer
    }

    /**
     * Decodes a PrivilegeParam message from the specified reader or buffer.
     * @function decode
     * @memberof crud.PrivilegeParam
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {crud.PrivilegeParam} PrivilegeParam
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    PrivilegeParam.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.crud.PrivilegeParam()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.System = reader.string()
            break
          case 3:
            message.SessionID = reader.string()
            break
          case 5:
            message.QueryCondition = $root.crud.QueryParam.decode(
              reader,
              reader.uint32(),
            )
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return PrivilegeParam
  })()

  return crud
})())

export { $root as default }

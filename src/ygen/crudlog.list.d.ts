import * as $protobuf from 'protobufjs'
/** Namespace crudlog. */
export namespace crudlog {
  /** Properties of a DbCrudLogList. */
  interface IDbCrudLogList {
    /** 批次号，从0开始 */
    BatchNo?: number | null

    /** rows offset,从0开始 */
    RowsOffset?: number | null

    /** 本批次数据 */
    Rows?: crudlog.IDbCrudLog[] | null
  }

  /** DbCrudLog list */
  class DbCrudLogList implements IDbCrudLogList {
    /**
     * Constructs a new DbCrudLogList.
     * @param [properties] Properties to set
     */
    constructor(properties?: crudlog.IDbCrudLogList)

    /** 批次号，从0开始 */
    public BatchNo: number

    /** rows offset,从0开始 */
    public RowsOffset: number

    /** 本批次数据 */
    public Rows: crudlog.IDbCrudLog[]

    /**
     * Encodes the specified DbCrudLogList message. Does not implicitly {@link crudlog.DbCrudLogList.verify|verify} messages.
     * @param message DbCrudLogList message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: crudlog.IDbCrudLogList,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a DbCrudLogList message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns DbCrudLogList
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): crudlog.DbCrudLogList
  }

  /** Properties of a DbCrudLog. */
  interface IDbCrudLog {
    /**
     * @db uuid
     * 用户所属的群组
     */
    OrgRID?: string | null

    /**
     * @db uuid
     * 用户rid
     */
    UserRID?: string | null

    /**
     * @db text
     * 操作
     */
    Operation?: string | null

    /**
     * @db jsonb
     * req proto json
     */
    Req?: string | null

    /**
     * @db text
     * req option
     */
    ReqOption?: string | null

    /**
     * @db text
     * ipinfo
     */
    Ipinfo?: string | null

    /**
     * @db jsonb
     * note
     */
    Note?: string | null

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     */
    UpdatedAt?: string | null

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */
    UpdatedDC?: string | null
  }

  /** 用户crud log表 */
  class DbCrudLog implements IDbCrudLog {
    /**
     * Constructs a new DbCrudLog.
     * @rpc crud pcrud
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbCrudLogOrgRID on DbCrudLog USING hash(OrgRID);
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbCrudLogUpdatedAt on DbCrudLog USING brin(UpdatedAt);
     * @param [properties] Properties to set
     */
    constructor(properties?: crudlog.IDbCrudLog)

    /**
     * @db uuid
     * 用户所属的群组
     */
    public OrgRID: string

    /**
     * @db uuid
     * 用户rid
     */
    public UserRID: string

    /**
     * @db text
     * 操作
     */
    public Operation: string

    /**
     * @db jsonb
     * req proto json
     */
    public Req: string

    /**
     * @db text
     * req option
     */
    public ReqOption: string

    /**
     * @db text
     * ipinfo
     */
    public Ipinfo: string

    /**
     * @db jsonb
     * note
     */
    public Note: string

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     */
    public UpdatedAt: string

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */
    public UpdatedDC: string

    /**
     * Encodes the specified DbCrudLog message. Does not implicitly {@link crudlog.DbCrudLog.verify|verify} messages.
     * @param message DbCrudLog message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: crudlog.IDbCrudLog,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a DbCrudLog message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns DbCrudLog
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): crudlog.DbCrudLog
  }
}

import { Writer } from 'protobufjs'
import { rpcCon } from 'jsyrpc'
import { ICallOption } from 'jsyrpc'

import { TRpcStream } from 'jsyrpc'
import * as UserSessionY from '@ygen/userSession'
import * as CrudY from '@ygen/crud'
import * as UserSessionListY from '@ygen/userSession.list'

export function RpcDbUserSessionInsert(
  req: UserSessionY.user.IDbUserSession,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = UserSessionY.user.DbUserSession.encode(req)
  let reqData = w.finish()

  const api = '/user.RpcDbUserSession/Insert'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function RpcDbUserSessionUpdate(
  req: UserSessionY.user.IDbUserSession,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = UserSessionY.user.DbUserSession.encode(req)
  let reqData = w.finish()

  const api = '/user.RpcDbUserSession/Update'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function RpcDbUserSessionPartialUpdate(
  req: UserSessionY.user.IDbUserSession,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = UserSessionY.user.DbUserSession.encode(req)
  let reqData = w.finish()

  const api = '/user.RpcDbUserSession/PartialUpdate'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function RpcDbUserSessionDelete(
  req: UserSessionY.user.IDbUserSession,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = UserSessionY.user.DbUserSession.encode(req)
  let reqData = w.finish()

  const api = '/user.RpcDbUserSession/Delete'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function RpcDbUserSessionSelectOne(
  req: CrudY.crud.IDMLParam,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = CrudY.crud.DMLParam.encode(req)
  let reqData = w.finish()

  const api = '/user.RpcDbUserSession/SelectOne'
  const v = 0

  return rpcCon.UnaryCall(
    reqData,
    api,
    v,
    UserSessionY.user.DbUserSession,
    callOpt,
  )
}

export function RpcDbUserSessionSelectMany(
  req: CrudY.crud.DMLParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/user.RpcDbUserSession/SelectMany'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.DMLParam,
    UserSessionY.user.DbUserSession,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function RpcDbUserSessionQuery(
  req: CrudY.crud.QueryParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/user.RpcDbUserSession/Query'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.QueryParam,
    UserSessionY.user.DbUserSession,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function RpcDbUserSessionQueryBatch(
  req: CrudY.crud.QueryParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/user.RpcDbUserSession/QueryBatch'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.QueryParam,
    UserSessionListY.user.DbUserSessionList,
    callOpt,
  )
  r.sendFirst(req)
  return r
}

export class RpcDbUserSession {
  public static Insert(
    req: UserSessionY.user.IDbUserSession,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbUserSessionInsert(req, callOpt)
  }
  public static Update(
    req: UserSessionY.user.IDbUserSession,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbUserSessionUpdate(req, callOpt)
  }
  public static PartialUpdate(
    req: UserSessionY.user.IDbUserSession,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbUserSessionPartialUpdate(req, callOpt)
  }
  public static Delete(
    req: UserSessionY.user.IDbUserSession,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbUserSessionDelete(req, callOpt)
  }
  public static SelectOne(
    req: CrudY.crud.IDMLParam,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbUserSessionSelectOne(req, callOpt)
  }
  public static SelectMany(
    req: CrudY.crud.DMLParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return RpcDbUserSessionSelectMany(req, callOpt)
  }
  public static Query(
    req: CrudY.crud.QueryParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return RpcDbUserSessionQuery(req, callOpt)
  }
  public static QueryBatch(
    req: CrudY.crud.QueryParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return RpcDbUserSessionQueryBatch(req, callOpt)
  }
}
export default {
  RpcDbUserSession,
  RpcDbUserSessionInsert,
  RpcDbUserSessionUpdate,
  RpcDbUserSessionPartialUpdate,
  RpcDbUserSessionDelete,
  RpcDbUserSessionSelectOne,
  RpcDbUserSessionSelectMany,
  RpcDbUserSessionQuery,
  RpcDbUserSessionQueryBatch,
}

/*eslint-disable block-scoped-var, id-length, no-control-regex, no-magic-numbers, no-prototype-builtins, no-redeclare, no-shadow, no-var, sort-vars*/
import * as $protobuf from 'protobufjs/minimal'

// Common aliases
const $Reader = $protobuf.Reader,
  $Writer = $protobuf.Writer,
  $util = $protobuf.util

// Exported root namespace
const $root = $protobuf.roots['default'] || ($protobuf.roots['default'] = {})

export const image = ($root.image = (() => {
  /**
   * Namespace image.
   * @exports image
   * @namespace
   */
  const image = $root.image || {}

  image.DbImage = (function () {
    /**
     * Properties of a DbImage.
     * @memberof image
     * @interface IDbImage
     * @property {string|null} [RID] @db uuid primary key
     * 行ID
     * @property {string|null} [OrgRID] @db uuid REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 所属的群组
     * @property {string|null} [FileName] @db text
     * 文件名
     * @property {string|null} [FileContent] @db text
     * 文件内容,经过base64编码,就是html img的dataurl
     * @property {string|null} [Hash] @db text
     * 文件内容的hash=base64(sha256(file_content_binary)
     * @property {string|null} [AddUserRID] @db uuid
     * 添加的用户RID
     * @property {string|null} [UpdatedAt] @db timestamp not null default now_utc()
     * 数据最后修改时间
     * @property {string|null} [UpdatedDC] @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */

    /**
     * Constructs a new DbImage.
     * @memberof image
     * @classdesc 用户的一些头像图片数据,地图点icon等
     * @rpc crud
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbImageOrgRID on DbImage USING hash (OrgRID)
     * @implements IDbImage
     * @constructor
     * @param {image.IDbImage=} [properties] Properties to set
     */
    function DbImage(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * @db uuid primary key
     * 行ID
     * @member {string} RID
     * @memberof image.DbImage
     * @instance
     */
    DbImage.prototype.RID = ''

    /**
     * @db uuid REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 所属的群组
     * @member {string} OrgRID
     * @memberof image.DbImage
     * @instance
     */
    DbImage.prototype.OrgRID = ''

    /**
     * @db text
     * 文件名
     * @member {string} FileName
     * @memberof image.DbImage
     * @instance
     */
    DbImage.prototype.FileName = ''

    /**
     * @db text
     * 文件内容,经过base64编码,就是html img的dataurl
     * @member {string} FileContent
     * @memberof image.DbImage
     * @instance
     */
    DbImage.prototype.FileContent = ''

    /**
     * @db text
     * 文件内容的hash=base64(sha256(file_content_binary)
     * @member {string} Hash
     * @memberof image.DbImage
     * @instance
     */
    DbImage.prototype.Hash = ''

    /**
     * @db uuid
     * 添加的用户RID
     * @member {string} AddUserRID
     * @memberof image.DbImage
     * @instance
     */
    DbImage.prototype.AddUserRID = ''

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     * @member {string} UpdatedAt
     * @memberof image.DbImage
     * @instance
     */
    DbImage.prototype.UpdatedAt = ''

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     * @member {string} UpdatedDC
     * @memberof image.DbImage
     * @instance
     */
    DbImage.prototype.UpdatedDC = ''

    /**
     * Encodes the specified DbImage message. Does not implicitly {@link image.DbImage.verify|verify} messages.
     * @function encode
     * @memberof image.DbImage
     * @static
     * @param {image.IDbImage} message DbImage message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    DbImage.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (message.RID != null && Object.hasOwnProperty.call(message, 'RID'))
        writer.uint32(/* id 1, wireType 2 =*/ 10).string(message.RID)
      if (
        message.OrgRID != null &&
        Object.hasOwnProperty.call(message, 'OrgRID')
      )
        writer.uint32(/* id 2, wireType 2 =*/ 18).string(message.OrgRID)
      if (
        message.FileName != null &&
        Object.hasOwnProperty.call(message, 'FileName')
      )
        writer.uint32(/* id 4, wireType 2 =*/ 34).string(message.FileName)
      if (
        message.FileContent != null &&
        Object.hasOwnProperty.call(message, 'FileContent')
      )
        writer.uint32(/* id 5, wireType 2 =*/ 42).string(message.FileContent)
      if (message.Hash != null && Object.hasOwnProperty.call(message, 'Hash'))
        writer.uint32(/* id 6, wireType 2 =*/ 50).string(message.Hash)
      if (
        message.AddUserRID != null &&
        Object.hasOwnProperty.call(message, 'AddUserRID')
      )
        writer.uint32(/* id 7, wireType 2 =*/ 58).string(message.AddUserRID)
      if (
        message.UpdatedAt != null &&
        Object.hasOwnProperty.call(message, 'UpdatedAt')
      )
        writer.uint32(/* id 14, wireType 2 =*/ 114).string(message.UpdatedAt)
      if (
        message.UpdatedDC != null &&
        Object.hasOwnProperty.call(message, 'UpdatedDC')
      )
        writer.uint32(/* id 15, wireType 2 =*/ 122).string(message.UpdatedDC)
      return writer
    }

    /**
     * Decodes a DbImage message from the specified reader or buffer.
     * @function decode
     * @memberof image.DbImage
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {image.DbImage} DbImage
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    DbImage.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.image.DbImage()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.RID = reader.string()
            break
          case 2:
            message.OrgRID = reader.string()
            break
          case 4:
            message.FileName = reader.string()
            break
          case 5:
            message.FileContent = reader.string()
            break
          case 6:
            message.Hash = reader.string()
            break
          case 7:
            message.AddUserRID = reader.string()
            break
          case 14:
            message.UpdatedAt = reader.string()
            break
          case 15:
            message.UpdatedDC = reader.string()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return DbImage
  })()

  return image
})())

export { $root as default }

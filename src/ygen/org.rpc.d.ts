import * as $protobuf from 'protobufjs'
/** Namespace org. */
export namespace org {
  /** 普通crud，没有权限检查 */
  class RpcDbOrg extends $protobuf.rpc.Service {
    /**
     * Constructs a new RpcDbOrg service.
     * @param rpcImpl RPC implementation
     * @param [requestDelimited=false] Whether requests are length-delimited
     * @param [responseDelimited=false] Whether responses are length-delimited
     */
    constructor(
      rpcImpl: $protobuf.RPCImpl,
      requestDelimited?: boolean,
      responseDelimited?: boolean,
    )

    /**
     * 插入一行数据
     * @param request DbOrg message or plain object
     * @param callback Node-style callback called with the error, if any, and DMLResult
     */
    public insert(
      request: org.IDbOrg,
      callback: org.RpcDbOrg.InsertCallback,
    ): void

    /**
     * 插入一行数据
     * @param request DbOrg message or plain object
     * @returns Promise
     */
    public insert(request: org.IDbOrg): Promise<crud.DMLResult>

    /**
     * 以RID为条件,全量修改一行数据
     * @param request DbOrg message or plain object
     * @param callback Node-style callback called with the error, if any, and DMLResult
     */
    public update(
      request: org.IDbOrg,
      callback: org.RpcDbOrg.UpdateCallback,
    ): void

    /**
     * 以RID为条件,全量修改一行数据
     * @param request DbOrg message or plain object
     * @returns Promise
     */
    public update(request: org.IDbOrg): Promise<crud.DMLResult>

    /**
     * 以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
     * @param request DbOrg message or plain object
     * @param callback Node-style callback called with the error, if any, and DMLResult
     */
    public partialUpdate(
      request: org.IDbOrg,
      callback: org.RpcDbOrg.PartialUpdateCallback,
    ): void

    /**
     * 以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
     * @param request DbOrg message or plain object
     * @returns Promise
     */
    public partialUpdate(request: org.IDbOrg): Promise<crud.DMLResult>

    /**
     * 以RID为条件,删除一行数据
     * @param request DbOrg message or plain object
     * @param callback Node-style callback called with the error, if any, and DMLResult
     */
    public delete(
      request: org.IDbOrg,
      callback: org.RpcDbOrg.DeleteCallback,
    ): void

    /**
     * 以RID为条件,删除一行数据
     * @param request DbOrg message or plain object
     * @returns Promise
     */
    public delete(request: org.IDbOrg): Promise<crud.DMLResult>

    /**
     * 以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
     * @param request DMLParam message or plain object
     * @param callback Node-style callback called with the error, if any, and DbOrg
     */
    public selectOne(
      request: crud.IDMLParam,
      callback: org.RpcDbOrg.SelectOneCallback,
    ): void

    /**
     * 以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
     * @param request DMLParam message or plain object
     * @returns Promise
     */
    public selectOne(request: crud.IDMLParam): Promise<org.DbOrg>

    /**
     * 以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
     * @param request DMLParam message or plain object
     * @param callback Node-style callback called with the error, if any, and DbOrg
     */
    public selectMany(
      request: crud.IDMLParam,
      callback: org.RpcDbOrg.SelectManyCallback,
    ): void

    /**
     * 以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
     * @param request DMLParam message or plain object
     * @returns Promise
     */
    public selectMany(request: crud.IDMLParam): Promise<org.DbOrg>

    /**
     * 根据条件查询数据
     * @param request QueryParam message or plain object
     * @param callback Node-style callback called with the error, if any, and DbOrg
     */
    public query(
      request: crud.IQueryParam,
      callback: org.RpcDbOrg.QueryCallback,
    ): void

    /**
     * 根据条件查询数据
     * @param request QueryParam message or plain object
     * @returns Promise
     */
    public query(request: crud.IQueryParam): Promise<org.DbOrg>

    /**
     * 根据条件查询数据,批量返回数据,crud.QueryParam.Batch指定一次返回多少条数据
     * @param request QueryParam message or plain object
     * @param callback Node-style callback called with the error, if any, and DbOrgList
     */
    public queryBatch(
      request: crud.IQueryParam,
      callback: org.RpcDbOrg.QueryBatchCallback,
    ): void

    /**
     * 根据条件查询数据,批量返回数据,crud.QueryParam.Batch指定一次返回多少条数据
     * @param request QueryParam message or plain object
     * @returns Promise
     */
    public queryBatch(request: crud.IQueryParam): Promise<org.DbOrgList>
  }

  namespace RpcDbOrg {
    /**
     * Callback as used by {@link org.RpcDbOrg#insert}.
     * @param error Error, if any
     * @param [response] DMLResult
     */
    type InsertCallback = (
      error: Error | null,
      response?: crud.DMLResult,
    ) => void

    /**
     * Callback as used by {@link org.RpcDbOrg#update}.
     * @param error Error, if any
     * @param [response] DMLResult
     */
    type UpdateCallback = (
      error: Error | null,
      response?: crud.DMLResult,
    ) => void

    /**
     * Callback as used by {@link org.RpcDbOrg#partialUpdate}.
     * @param error Error, if any
     * @param [response] DMLResult
     */
    type PartialUpdateCallback = (
      error: Error | null,
      response?: crud.DMLResult,
    ) => void

    /**
     * Callback as used by {@link org.RpcDbOrg#delete_}.
     * @param error Error, if any
     * @param [response] DMLResult
     */
    type DeleteCallback = (
      error: Error | null,
      response?: crud.DMLResult,
    ) => void

    /**
     * Callback as used by {@link org.RpcDbOrg#selectOne}.
     * @param error Error, if any
     * @param [response] DbOrg
     */
    type SelectOneCallback = (error: Error | null, response?: org.DbOrg) => void

    /**
     * Callback as used by {@link org.RpcDbOrg#selectMany}.
     * @param error Error, if any
     * @param [response] DbOrg
     */
    type SelectManyCallback = (
      error: Error | null,
      response?: org.DbOrg,
    ) => void

    /**
     * Callback as used by {@link org.RpcDbOrg#query}.
     * @param error Error, if any
     * @param [response] DbOrg
     */
    type QueryCallback = (error: Error | null, response?: org.DbOrg) => void

    /**
     * Callback as used by {@link org.RpcDbOrg#queryBatch}.
     * @param error Error, if any
     * @param [response] DbOrgList
     */
    type QueryBatchCallback = (
      error: Error | null,
      response?: org.DbOrgList,
    ) => void
  }

  /** 带权限检查(有相应的群组权限和编辑权限)的crud */
  class PrpcDbOrg extends $protobuf.rpc.Service {
    /**
     * Constructs a new PrpcDbOrg service.
     * @param rpcImpl RPC implementation
     * @param [requestDelimited=false] Whether requests are length-delimited
     * @param [responseDelimited=false] Whether responses are length-delimited
     */
    constructor(
      rpcImpl: $protobuf.RPCImpl,
      requestDelimited?: boolean,
      responseDelimited?: boolean,
    )

    /**
     * 插入一行数据
     * @param request DbOrg message or plain object
     * @param callback Node-style callback called with the error, if any, and DMLResult
     */
    public insert(
      request: org.IDbOrg,
      callback: org.PrpcDbOrg.InsertCallback,
    ): void

    /**
     * 插入一行数据
     * @param request DbOrg message or plain object
     * @returns Promise
     */
    public insert(request: org.IDbOrg): Promise<crud.DMLResult>

    /**
     * 以RID为条件,全量修改一行数据
     * @param request DbOrg message or plain object
     * @param callback Node-style callback called with the error, if any, and DMLResult
     */
    public update(
      request: org.IDbOrg,
      callback: org.PrpcDbOrg.UpdateCallback,
    ): void

    /**
     * 以RID为条件,全量修改一行数据
     * @param request DbOrg message or plain object
     * @returns Promise
     */
    public update(request: org.IDbOrg): Promise<crud.DMLResult>

    /**
     * 以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
     * @param request DbOrg message or plain object
     * @param callback Node-style callback called with the error, if any, and DMLResult
     */
    public partialUpdate(
      request: org.IDbOrg,
      callback: org.PrpcDbOrg.PartialUpdateCallback,
    ): void

    /**
     * 以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
     * @param request DbOrg message or plain object
     * @returns Promise
     */
    public partialUpdate(request: org.IDbOrg): Promise<crud.DMLResult>

    /**
     * 以RID为条件,删除一行数据
     * @param request DbOrg message or plain object
     * @param callback Node-style callback called with the error, if any, and DMLResult
     */
    public delete(
      request: org.IDbOrg,
      callback: org.PrpcDbOrg.DeleteCallback,
    ): void

    /**
     * 以RID为条件,删除一行数据
     * @param request DbOrg message or plain object
     * @returns Promise
     */
    public delete(request: org.IDbOrg): Promise<crud.DMLResult>

    /**
     * 以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
     * @param request DMLParam message or plain object
     * @param callback Node-style callback called with the error, if any, and DbOrg
     */
    public selectOne(
      request: crud.IDMLParam,
      callback: org.PrpcDbOrg.SelectOneCallback,
    ): void

    /**
     * 以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
     * @param request DMLParam message or plain object
     * @returns Promise
     */
    public selectOne(request: crud.IDMLParam): Promise<org.DbOrg>

    /**
     * 以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
     * @param request DMLParam message or plain object
     * @param callback Node-style callback called with the error, if any, and DbOrg
     */
    public selectMany(
      request: crud.IDMLParam,
      callback: org.PrpcDbOrg.SelectManyCallback,
    ): void

    /**
     * 以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
     * @param request DMLParam message or plain object
     * @returns Promise
     */
    public selectMany(request: crud.IDMLParam): Promise<org.DbOrg>

    /**
     * 根据条件取得我有权限的数据
     * @param request PrivilegeParam message or plain object
     * @param callback Node-style callback called with the error, if any, and DbOrg
     */
    public query(
      request: crud.IPrivilegeParam,
      callback: org.PrpcDbOrg.QueryCallback,
    ): void

    /**
     * 根据条件取得我有权限的数据
     * @param request PrivilegeParam message or plain object
     * @returns Promise
     */
    public query(request: crud.IPrivilegeParam): Promise<org.DbOrg>

    /**
     * 根据条件取得我有权限的数据,批量返回数据,crud.PrivilegeParam.QueryParam.Batch指定一次返回多少条数据
     * @param request PrivilegeParam message or plain object
     * @param callback Node-style callback called with the error, if any, and DbOrgList
     */
    public queryBatch(
      request: crud.IPrivilegeParam,
      callback: org.PrpcDbOrg.QueryBatchCallback,
    ): void

    /**
     * 根据条件取得我有权限的数据,批量返回数据,crud.PrivilegeParam.QueryParam.Batch指定一次返回多少条数据
     * @param request PrivilegeParam message or plain object
     * @returns Promise
     */
    public queryBatch(request: crud.IPrivilegeParam): Promise<org.DbOrgList>
  }

  namespace PrpcDbOrg {
    /**
     * Callback as used by {@link org.PrpcDbOrg#insert}.
     * @param error Error, if any
     * @param [response] DMLResult
     */
    type InsertCallback = (
      error: Error | null,
      response?: crud.DMLResult,
    ) => void

    /**
     * Callback as used by {@link org.PrpcDbOrg#update}.
     * @param error Error, if any
     * @param [response] DMLResult
     */
    type UpdateCallback = (
      error: Error | null,
      response?: crud.DMLResult,
    ) => void

    /**
     * Callback as used by {@link org.PrpcDbOrg#partialUpdate}.
     * @param error Error, if any
     * @param [response] DMLResult
     */
    type PartialUpdateCallback = (
      error: Error | null,
      response?: crud.DMLResult,
    ) => void

    /**
     * Callback as used by {@link org.PrpcDbOrg#delete_}.
     * @param error Error, if any
     * @param [response] DMLResult
     */
    type DeleteCallback = (
      error: Error | null,
      response?: crud.DMLResult,
    ) => void

    /**
     * Callback as used by {@link org.PrpcDbOrg#selectOne}.
     * @param error Error, if any
     * @param [response] DbOrg
     */
    type SelectOneCallback = (error: Error | null, response?: org.DbOrg) => void

    /**
     * Callback as used by {@link org.PrpcDbOrg#selectMany}.
     * @param error Error, if any
     * @param [response] DbOrg
     */
    type SelectManyCallback = (
      error: Error | null,
      response?: org.DbOrg,
    ) => void

    /**
     * Callback as used by {@link org.PrpcDbOrg#query}.
     * @param error Error, if any
     * @param [response] DbOrg
     */
    type QueryCallback = (error: Error | null, response?: org.DbOrg) => void

    /**
     * Callback as used by {@link org.PrpcDbOrg#queryBatch}.
     * @param error Error, if any
     * @param [response] DbOrgList
     */
    type QueryBatchCallback = (
      error: Error | null,
      response?: org.DbOrgList,
    ) => void
  }

  /** Properties of a DbOrg. */
  interface IDbOrg {
    /**
     * @db uuid primary key
     * 行ID
     */
    RID?: string | null

    /**
     * @db varchar(64) unique not null
     * 组织机构自编号
     */
    OrgID?: string | null

    /**
     * @db int default 0
     */
    OrgType?: number | null

    /**
     * @db int default 100
     * 排序值,由用户指定,不用管拼音或者其它语种的排序
     */
    SortValue?: number | null

    /**
     * @db varchar(32) not null
     * 机构名称,缩写
     */
    ShortName?: string | null

    /**
     * @db varchar(256)
     * 机构名称,全称
     */
    FullName?: string | null

    /**
     * @db text
     * 机构描述/备注信息
     */
    Note?: string | null

    /**
     * @db jsonb default  '{}'::jsonb
     * 其它设置，可以在里面扩展需要用到的其它信息
     */
    Setting?: string | null

    /**
     * @db uuid
     * 创建者的rid
     */
    CreatorRID?: string | null

    /**
     * @db uuid
     * 此组织的上级机构
     */
    OrgRID?: string | null

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     */
    UpdatedAt?: string | null

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */
    UpdatedDC?: string | null
  }

  /** 群组/组织结构表 */
  class DbOrg implements IDbOrg {
    /**
     * Constructs a new DbOrg.
     * @rpc crud pcrud
     * @dbpost DO $$ BEGIN BEGIN
     * @dbpost ALTER TABLE DbOrg ADD CONSTRAINT fkDbOrgParentRID  FOREIGN KEY (OrgRID) REFERENCES DbOrg(RID);
     * @dbpost EXCEPTION WHEN duplicate_object THEN --do nothing
     * @dbpost END; END $$;
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbOrgCreatorRID on DbOrg USING hash(CreatorRID);
     * @dbpost INSERT INTO dborg(rid, orgid, orgtype, sortvalue, shortname, fullname, note, setting, OrgRID, updatedat, updateddc)VALUES('00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', 0, 100, 'root', 'root', '', '{}'::jsonb, NULL, now_utc(), '')  ON CONFLICT  DO NOTHING;
     * @param [properties] Properties to set
     */
    constructor(properties?: org.IDbOrg)

    /**
     * @db uuid primary key
     * 行ID
     */
    public RID: string

    /**
     * @db varchar(64) unique not null
     * 组织机构自编号
     */
    public OrgID: string

    /**
     * @db int default 0
     */
    public OrgType: number

    /**
     * @db int default 100
     * 排序值,由用户指定,不用管拼音或者其它语种的排序
     */
    public SortValue: number

    /**
     * @db varchar(32) not null
     * 机构名称,缩写
     */
    public ShortName: string

    /**
     * @db varchar(256)
     * 机构名称,全称
     */
    public FullName: string

    /**
     * @db text
     * 机构描述/备注信息
     */
    public Note: string

    /**
     * @db jsonb default  '{}'::jsonb
     * 其它设置，可以在里面扩展需要用到的其它信息
     */
    public Setting: string

    /**
     * @db uuid
     * 创建者的rid
     */
    public CreatorRID: string

    /**
     * @db uuid
     * 此组织的上级机构
     */
    public OrgRID: string

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     */
    public UpdatedAt: string

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */
    public UpdatedDC: string

    /**
     * Encodes the specified DbOrg message. Does not implicitly {@link org.DbOrg.verify|verify} messages.
     * @param message DbOrg message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: org.IDbOrg,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a DbOrg message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns DbOrg
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): org.DbOrg
  }

  /** Properties of a DbOrgList. */
  interface IDbOrgList {
    /** 批次号，从0开始 */
    BatchNo?: number | null

    /** rows offset,从0开始 */
    RowsOffset?: number | null

    /** 本批次数据 */
    Rows?: org.IDbOrg[] | null
  }

  /** DbOrg list */
  class DbOrgList implements IDbOrgList {
    /**
     * Constructs a new DbOrgList.
     * @param [properties] Properties to set
     */
    constructor(properties?: org.IDbOrgList)

    /** 批次号，从0开始 */
    public BatchNo: number

    /** rows offset,从0开始 */
    public RowsOffset: number

    /** 本批次数据 */
    public Rows: org.IDbOrg[]

    /**
     * Encodes the specified DbOrgList message. Does not implicitly {@link org.DbOrgList.verify|verify} messages.
     * @param message DbOrgList message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: org.IDbOrgList,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a DbOrgList message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns DbOrgList
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): org.DbOrgList
  }
}

/** Namespace crud. */
export namespace crud {
  /** Properties of a DMLResult. */
  interface IDMLResult {
    /** 影响的行数，如果成功>0,否则=0 */
    AffectedRow?: number | null

    /** 错误信息，如果有的话 */
    errInfo?: string | null

    /** 附加信息,json格式 */
    Note?: string | null
  }

  /** CRUD DML结果 */
  class DMLResult implements IDMLResult {
    /**
     * Constructs a new DMLResult.
     * @param [properties] Properties to set
     */
    constructor(properties?: crud.IDMLResult)

    /** 影响的行数，如果成功>0,否则=0 */
    public AffectedRow: number

    /** 错误信息，如果有的话 */
    public errInfo: string

    /** 附加信息,json格式 */
    public Note: string

    /**
     * Encodes the specified DMLResult message. Does not implicitly {@link crud.DMLResult.verify|verify} messages.
     * @param message DMLResult message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: crud.IDMLResult,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a DMLResult message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns DMLResult
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): crud.DMLResult
  }

  /** Properties of a DMLParam. */
  interface IDMLParam {
    /** 条件字段名 */
    KeyColumn?: string[] | null

    /** 结果字段名 */
    ResultColumn?: string[] | null

    /** 条件值 */
    KeyValue?: string[] | null
  }

  /** 简单CRUD里面用到的条件和结果字段列表 */
  class DMLParam implements IDMLParam {
    /**
     * Constructs a new DMLParam.
     * @param [properties] Properties to set
     */
    constructor(properties?: crud.IDMLParam)

    /** 条件字段名 */
    public KeyColumn: string[]

    /** 结果字段名 */
    public ResultColumn: string[]

    /** 条件值 */
    public KeyValue: string[]

    /**
     * Encodes the specified DMLParam message. Does not implicitly {@link crud.DMLParam.verify|verify} messages.
     * @param message DMLParam message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: crud.IDMLParam,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a DMLParam message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns DMLParam
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): crud.DMLParam
  }

  /** Properties of a WhereItem. */
  interface IWhereItem {
    /** fieldname */
    Field?: string | null

    /** field compare operator */
    FieldCompareOperator?: string | null

    /** Field value */
    FieldValue?: string | null
  }

  /** sql where额外条件项 */
  class WhereItem implements IWhereItem {
    /**
     * Constructs a new WhereItem.
     * @param [properties] Properties to set
     */
    constructor(properties?: crud.IWhereItem)

    /** fieldname */
    public Field: string

    /** field compare operator */
    public FieldCompareOperator: string

    /** Field value */
    public FieldValue: string

    /**
     * Encodes the specified WhereItem message. Does not implicitly {@link crud.WhereItem.verify|verify} messages.
     * @param message WhereItem message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: crud.IWhereItem,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a WhereItem message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns WhereItem
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): crud.WhereItem
  }

  /** Properties of a QueryParam. */
  interface IQueryParam {
    /** 想要的结果字段名，不填写为全要 */
    ResultColumn?: string[] | null

    /**
     * Query时需要处理时区的字段,单数为字段名，双数为需要加减的的分钟数
     * 需要此功能必须把所有的结果列名填写在ResultColumn，否则无法处理
     * TimeColumn = ['ActionTime','480'，‘RecvTime','-60']
     */
    TimeColumn?: string[] | null

    /** where额外条件项,只支持 and */
    Where?: crud.IWhereItem[] | null

    /** 分页，需要的数据，如果Limit=0 and Offset=0,则视为不需要分页 */
    Limit?: number | null

    /**
     * 分页，从什么位置开始，如果Limit=0 and
     * Offset!=0,为从Offset的位置开始的所有数据
     */
    Offset?: number | null

    /**
     * 排序条件，每项为sql的order by 条件
     * select * from a order by col1 asc,col2 desc
     * OrderBy=["col1 asc","col2 desc"]
     */
    OrderBy?: string[] | null

    /** QueryBatch调用时,指定一次返回的数据量，不指定时后端按每次一个返回 */
    Batch?: number | null
  }

  /** 查询条件 */
  class QueryParam implements IQueryParam {
    /**
     * Constructs a new QueryParam.
     * @param [properties] Properties to set
     */
    constructor(properties?: crud.IQueryParam)

    /** 想要的结果字段名，不填写为全要 */
    public ResultColumn: string[]

    /**
     * Query时需要处理时区的字段,单数为字段名，双数为需要加减的的分钟数
     * 需要此功能必须把所有的结果列名填写在ResultColumn，否则无法处理
     * TimeColumn = ['ActionTime','480'，‘RecvTime','-60']
     */
    public TimeColumn: string[]

    /** where额外条件项,只支持 and */
    public Where: crud.IWhereItem[]

    /** 分页，需要的数据，如果Limit=0 and Offset=0,则视为不需要分页 */
    public Limit: number

    /**
     * 分页，从什么位置开始，如果Limit=0 and
     * Offset!=0,为从Offset的位置开始的所有数据
     */
    public Offset: number

    /**
     * 排序条件，每项为sql的order by 条件
     * select * from a order by col1 asc,col2 desc
     * OrderBy=["col1 asc","col2 desc"]
     */
    public OrderBy: string[]

    /** QueryBatch调用时,指定一次返回的数据量，不指定时后端按每次一个返回 */
    public Batch: number

    /**
     * Encodes the specified QueryParam message. Does not implicitly {@link crud.QueryParam.verify|verify} messages.
     * @param message QueryParam message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: crud.IQueryParam,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a QueryParam message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns QueryParam
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): crud.QueryParam
  }

  /** Properties of a PrivilegeParam. */
  interface IPrivilegeParam {
    /** 系统名称 */
    System?: string | null

    /** SessionID */
    SessionID?: string | null

    /** 查询条件 */
    QueryCondition?: crud.IQueryParam | null
  }

  /** 取得用户有权限的数据 */
  class PrivilegeParam implements IPrivilegeParam {
    /**
     * Constructs a new PrivilegeParam.
     * @param [properties] Properties to set
     */
    constructor(properties?: crud.IPrivilegeParam)

    /** 系统名称 */
    public System: string

    /** SessionID */
    public SessionID: string

    /** 查询条件 */
    public QueryCondition?: crud.IQueryParam | null

    /**
     * Encodes the specified PrivilegeParam message. Does not implicitly {@link crud.PrivilegeParam.verify|verify} messages.
     * @param message PrivilegeParam message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: crud.IPrivilegeParam,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a PrivilegeParam message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns PrivilegeParam
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): crud.PrivilegeParam
  }
}

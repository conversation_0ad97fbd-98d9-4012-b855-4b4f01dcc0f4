/*eslint-disable block-scoped-var, id-length, no-control-regex, no-magic-numbers, no-prototype-builtins, no-redeclare, no-shadow, no-var, sort-vars*/
import * as $protobuf from 'protobufjs/minimal'

// Common aliases
const $Reader = $protobuf.Reader,
  $Writer = $protobuf.Writer,
  $util = $protobuf.util

// Exported root namespace
const $root = $protobuf.roots['default'] || ($protobuf.roots['default'] = {})

export const org = ($root.org = (() => {
  /**
   * Namespace org.
   * @exports org
   * @namespace
   */
  const org = $root.org || {}

  org.RpcDbOrg = (function () {
    /**
     * Constructs a new RpcDbOrg service.
     * @memberof org
     * @classdesc 普通crud，没有权限检查
     * @extends $protobuf.rpc.Service
     * @constructor
     * @param {$protobuf.RPCImpl} rpcImpl RPC implementation
     * @param {boolean} [requestDelimited=false] Whether requests are length-delimited
     * @param {boolean} [responseDelimited=false] Whether responses are length-delimited
     */
    function RpcDbOrg(rpcImpl, requestDelimited, responseDelimited) {
      $protobuf.rpc.Service.call(
        this,
        rpcImpl,
        requestDelimited,
        responseDelimited,
      )
    }

    ;(RpcDbOrg.prototype = Object.create(
      $protobuf.rpc.Service.prototype,
    )).constructor = RpcDbOrg

    /**
     * Callback as used by {@link org.RpcDbOrg#insert}.
     * @memberof org.RpcDbOrg
     * @typedef InsertCallback
     * @type {function}
     * @param {Error|null} error Error, if any
     * @param {crud.DMLResult} [response] DMLResult
     */

    /**
     * 插入一行数据
     * @function insert
     * @memberof org.RpcDbOrg
     * @instance
     * @param {org.IDbOrg} request DbOrg message or plain object
     * @param {org.RpcDbOrg.InsertCallback} callback Node-style callback called with the error, if any, and DMLResult
     * @returns {undefined}
     * @variation 1
     */
    Object.defineProperty(
      (RpcDbOrg.prototype.insert = function insert(request, callback) {
        return this.rpcCall(
          insert,
          $root.org.DbOrg,
          $root.crud.DMLResult,
          request,
          callback,
        )
      }),
      'name',
      { value: 'Insert' },
    )

    /**
     * 插入一行数据
     * @function insert
     * @memberof org.RpcDbOrg
     * @instance
     * @param {org.IDbOrg} request DbOrg message or plain object
     * @returns {Promise<crud.DMLResult>} Promise
     * @variation 2
     */

    /**
     * Callback as used by {@link org.RpcDbOrg#update}.
     * @memberof org.RpcDbOrg
     * @typedef UpdateCallback
     * @type {function}
     * @param {Error|null} error Error, if any
     * @param {crud.DMLResult} [response] DMLResult
     */

    /**
     * 以RID为条件,全量修改一行数据
     * @function update
     * @memberof org.RpcDbOrg
     * @instance
     * @param {org.IDbOrg} request DbOrg message or plain object
     * @param {org.RpcDbOrg.UpdateCallback} callback Node-style callback called with the error, if any, and DMLResult
     * @returns {undefined}
     * @variation 1
     */
    Object.defineProperty(
      (RpcDbOrg.prototype.update = function update(request, callback) {
        return this.rpcCall(
          update,
          $root.org.DbOrg,
          $root.crud.DMLResult,
          request,
          callback,
        )
      }),
      'name',
      { value: 'Update' },
    )

    /**
     * 以RID为条件,全量修改一行数据
     * @function update
     * @memberof org.RpcDbOrg
     * @instance
     * @param {org.IDbOrg} request DbOrg message or plain object
     * @returns {Promise<crud.DMLResult>} Promise
     * @variation 2
     */

    /**
     * Callback as used by {@link org.RpcDbOrg#partialUpdate}.
     * @memberof org.RpcDbOrg
     * @typedef PartialUpdateCallback
     * @type {function}
     * @param {Error|null} error Error, if any
     * @param {crud.DMLResult} [response] DMLResult
     */

    /**
     * 以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
     * @function partialUpdate
     * @memberof org.RpcDbOrg
     * @instance
     * @param {org.IDbOrg} request DbOrg message or plain object
     * @param {org.RpcDbOrg.PartialUpdateCallback} callback Node-style callback called with the error, if any, and DMLResult
     * @returns {undefined}
     * @variation 1
     */
    Object.defineProperty(
      (RpcDbOrg.prototype.partialUpdate = function partialUpdate(
        request,
        callback,
      ) {
        return this.rpcCall(
          partialUpdate,
          $root.org.DbOrg,
          $root.crud.DMLResult,
          request,
          callback,
        )
      }),
      'name',
      { value: 'PartialUpdate' },
    )

    /**
     * 以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
     * @function partialUpdate
     * @memberof org.RpcDbOrg
     * @instance
     * @param {org.IDbOrg} request DbOrg message or plain object
     * @returns {Promise<crud.DMLResult>} Promise
     * @variation 2
     */

    /**
     * Callback as used by {@link org.RpcDbOrg#delete_}.
     * @memberof org.RpcDbOrg
     * @typedef DeleteCallback
     * @type {function}
     * @param {Error|null} error Error, if any
     * @param {crud.DMLResult} [response] DMLResult
     */

    /**
     * 以RID为条件,删除一行数据
     * @function delete
     * @memberof org.RpcDbOrg
     * @instance
     * @param {org.IDbOrg} request DbOrg message or plain object
     * @param {org.RpcDbOrg.DeleteCallback} callback Node-style callback called with the error, if any, and DMLResult
     * @returns {undefined}
     * @variation 1
     */
    Object.defineProperty(
      (RpcDbOrg.prototype['delete'] = function delete_(request, callback) {
        return this.rpcCall(
          delete_,
          $root.org.DbOrg,
          $root.crud.DMLResult,
          request,
          callback,
        )
      }),
      'name',
      { value: 'Delete' },
    )

    /**
     * 以RID为条件,删除一行数据
     * @function delete
     * @memberof org.RpcDbOrg
     * @instance
     * @param {org.IDbOrg} request DbOrg message or plain object
     * @returns {Promise<crud.DMLResult>} Promise
     * @variation 2
     */

    /**
     * Callback as used by {@link org.RpcDbOrg#selectOne}.
     * @memberof org.RpcDbOrg
     * @typedef SelectOneCallback
     * @type {function}
     * @param {Error|null} error Error, if any
     * @param {org.DbOrg} [response] DbOrg
     */

    /**
     * 以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
     * @function selectOne
     * @memberof org.RpcDbOrg
     * @instance
     * @param {crud.IDMLParam} request DMLParam message or plain object
     * @param {org.RpcDbOrg.SelectOneCallback} callback Node-style callback called with the error, if any, and DbOrg
     * @returns {undefined}
     * @variation 1
     */
    Object.defineProperty(
      (RpcDbOrg.prototype.selectOne = function selectOne(request, callback) {
        return this.rpcCall(
          selectOne,
          $root.crud.DMLParam,
          $root.org.DbOrg,
          request,
          callback,
        )
      }),
      'name',
      { value: 'SelectOne' },
    )

    /**
     * 以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
     * @function selectOne
     * @memberof org.RpcDbOrg
     * @instance
     * @param {crud.IDMLParam} request DMLParam message or plain object
     * @returns {Promise<org.DbOrg>} Promise
     * @variation 2
     */

    /**
     * Callback as used by {@link org.RpcDbOrg#selectMany}.
     * @memberof org.RpcDbOrg
     * @typedef SelectManyCallback
     * @type {function}
     * @param {Error|null} error Error, if any
     * @param {org.DbOrg} [response] DbOrg
     */

    /**
     * 以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
     * @function selectMany
     * @memberof org.RpcDbOrg
     * @instance
     * @param {crud.IDMLParam} request DMLParam message or plain object
     * @param {org.RpcDbOrg.SelectManyCallback} callback Node-style callback called with the error, if any, and DbOrg
     * @returns {undefined}
     * @variation 1
     */
    Object.defineProperty(
      (RpcDbOrg.prototype.selectMany = function selectMany(request, callback) {
        return this.rpcCall(
          selectMany,
          $root.crud.DMLParam,
          $root.org.DbOrg,
          request,
          callback,
        )
      }),
      'name',
      { value: 'SelectMany' },
    )

    /**
     * 以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
     * @function selectMany
     * @memberof org.RpcDbOrg
     * @instance
     * @param {crud.IDMLParam} request DMLParam message or plain object
     * @returns {Promise<org.DbOrg>} Promise
     * @variation 2
     */

    /**
     * Callback as used by {@link org.RpcDbOrg#query}.
     * @memberof org.RpcDbOrg
     * @typedef QueryCallback
     * @type {function}
     * @param {Error|null} error Error, if any
     * @param {org.DbOrg} [response] DbOrg
     */

    /**
     * 根据条件查询数据
     * @function query
     * @memberof org.RpcDbOrg
     * @instance
     * @param {crud.IQueryParam} request QueryParam message or plain object
     * @param {org.RpcDbOrg.QueryCallback} callback Node-style callback called with the error, if any, and DbOrg
     * @returns {undefined}
     * @variation 1
     */
    Object.defineProperty(
      (RpcDbOrg.prototype.query = function query(request, callback) {
        return this.rpcCall(
          query,
          $root.crud.QueryParam,
          $root.org.DbOrg,
          request,
          callback,
        )
      }),
      'name',
      { value: 'Query' },
    )

    /**
     * 根据条件查询数据
     * @function query
     * @memberof org.RpcDbOrg
     * @instance
     * @param {crud.IQueryParam} request QueryParam message or plain object
     * @returns {Promise<org.DbOrg>} Promise
     * @variation 2
     */

    /**
     * Callback as used by {@link org.RpcDbOrg#queryBatch}.
     * @memberof org.RpcDbOrg
     * @typedef QueryBatchCallback
     * @type {function}
     * @param {Error|null} error Error, if any
     * @param {org.DbOrgList} [response] DbOrgList
     */

    /**
     * 根据条件查询数据,批量返回数据,crud.QueryParam.Batch指定一次返回多少条数据
     * @function queryBatch
     * @memberof org.RpcDbOrg
     * @instance
     * @param {crud.IQueryParam} request QueryParam message or plain object
     * @param {org.RpcDbOrg.QueryBatchCallback} callback Node-style callback called with the error, if any, and DbOrgList
     * @returns {undefined}
     * @variation 1
     */
    Object.defineProperty(
      (RpcDbOrg.prototype.queryBatch = function queryBatch(request, callback) {
        return this.rpcCall(
          queryBatch,
          $root.crud.QueryParam,
          $root.org.DbOrgList,
          request,
          callback,
        )
      }),
      'name',
      { value: 'QueryBatch' },
    )

    /**
     * 根据条件查询数据,批量返回数据,crud.QueryParam.Batch指定一次返回多少条数据
     * @function queryBatch
     * @memberof org.RpcDbOrg
     * @instance
     * @param {crud.IQueryParam} request QueryParam message or plain object
     * @returns {Promise<org.DbOrgList>} Promise
     * @variation 2
     */

    return RpcDbOrg
  })()

  org.PrpcDbOrg = (function () {
    /**
     * Constructs a new PrpcDbOrg service.
     * @memberof org
     * @classdesc 带权限检查(有相应的群组权限和编辑权限)的crud
     * @extends $protobuf.rpc.Service
     * @constructor
     * @param {$protobuf.RPCImpl} rpcImpl RPC implementation
     * @param {boolean} [requestDelimited=false] Whether requests are length-delimited
     * @param {boolean} [responseDelimited=false] Whether responses are length-delimited
     */
    function PrpcDbOrg(rpcImpl, requestDelimited, responseDelimited) {
      $protobuf.rpc.Service.call(
        this,
        rpcImpl,
        requestDelimited,
        responseDelimited,
      )
    }

    ;(PrpcDbOrg.prototype = Object.create(
      $protobuf.rpc.Service.prototype,
    )).constructor = PrpcDbOrg

    /**
     * Callback as used by {@link org.PrpcDbOrg#insert}.
     * @memberof org.PrpcDbOrg
     * @typedef InsertCallback
     * @type {function}
     * @param {Error|null} error Error, if any
     * @param {crud.DMLResult} [response] DMLResult
     */

    /**
     * 插入一行数据
     * @function insert
     * @memberof org.PrpcDbOrg
     * @instance
     * @param {org.IDbOrg} request DbOrg message or plain object
     * @param {org.PrpcDbOrg.InsertCallback} callback Node-style callback called with the error, if any, and DMLResult
     * @returns {undefined}
     * @variation 1
     */
    Object.defineProperty(
      (PrpcDbOrg.prototype.insert = function insert(request, callback) {
        return this.rpcCall(
          insert,
          $root.org.DbOrg,
          $root.crud.DMLResult,
          request,
          callback,
        )
      }),
      'name',
      { value: 'Insert' },
    )

    /**
     * 插入一行数据
     * @function insert
     * @memberof org.PrpcDbOrg
     * @instance
     * @param {org.IDbOrg} request DbOrg message or plain object
     * @returns {Promise<crud.DMLResult>} Promise
     * @variation 2
     */

    /**
     * Callback as used by {@link org.PrpcDbOrg#update}.
     * @memberof org.PrpcDbOrg
     * @typedef UpdateCallback
     * @type {function}
     * @param {Error|null} error Error, if any
     * @param {crud.DMLResult} [response] DMLResult
     */

    /**
     * 以RID为条件,全量修改一行数据
     * @function update
     * @memberof org.PrpcDbOrg
     * @instance
     * @param {org.IDbOrg} request DbOrg message or plain object
     * @param {org.PrpcDbOrg.UpdateCallback} callback Node-style callback called with the error, if any, and DMLResult
     * @returns {undefined}
     * @variation 1
     */
    Object.defineProperty(
      (PrpcDbOrg.prototype.update = function update(request, callback) {
        return this.rpcCall(
          update,
          $root.org.DbOrg,
          $root.crud.DMLResult,
          request,
          callback,
        )
      }),
      'name',
      { value: 'Update' },
    )

    /**
     * 以RID为条件,全量修改一行数据
     * @function update
     * @memberof org.PrpcDbOrg
     * @instance
     * @param {org.IDbOrg} request DbOrg message or plain object
     * @returns {Promise<crud.DMLResult>} Promise
     * @variation 2
     */

    /**
     * Callback as used by {@link org.PrpcDbOrg#partialUpdate}.
     * @memberof org.PrpcDbOrg
     * @typedef PartialUpdateCallback
     * @type {function}
     * @param {Error|null} error Error, if any
     * @param {crud.DMLResult} [response] DMLResult
     */

    /**
     * 以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
     * @function partialUpdate
     * @memberof org.PrpcDbOrg
     * @instance
     * @param {org.IDbOrg} request DbOrg message or plain object
     * @param {org.PrpcDbOrg.PartialUpdateCallback} callback Node-style callback called with the error, if any, and DMLResult
     * @returns {undefined}
     * @variation 1
     */
    Object.defineProperty(
      (PrpcDbOrg.prototype.partialUpdate = function partialUpdate(
        request,
        callback,
      ) {
        return this.rpcCall(
          partialUpdate,
          $root.org.DbOrg,
          $root.crud.DMLResult,
          request,
          callback,
        )
      }),
      'name',
      { value: 'PartialUpdate' },
    )

    /**
     * 以RID为条件,部分修改一行数据，以metadata.DMLParam.keyColumn为要修改的目标列
     * @function partialUpdate
     * @memberof org.PrpcDbOrg
     * @instance
     * @param {org.IDbOrg} request DbOrg message or plain object
     * @returns {Promise<crud.DMLResult>} Promise
     * @variation 2
     */

    /**
     * Callback as used by {@link org.PrpcDbOrg#delete_}.
     * @memberof org.PrpcDbOrg
     * @typedef DeleteCallback
     * @type {function}
     * @param {Error|null} error Error, if any
     * @param {crud.DMLResult} [response] DMLResult
     */

    /**
     * 以RID为条件,删除一行数据
     * @function delete
     * @memberof org.PrpcDbOrg
     * @instance
     * @param {org.IDbOrg} request DbOrg message or plain object
     * @param {org.PrpcDbOrg.DeleteCallback} callback Node-style callback called with the error, if any, and DMLResult
     * @returns {undefined}
     * @variation 1
     */
    Object.defineProperty(
      (PrpcDbOrg.prototype['delete'] = function delete_(request, callback) {
        return this.rpcCall(
          delete_,
          $root.org.DbOrg,
          $root.crud.DMLResult,
          request,
          callback,
        )
      }),
      'name',
      { value: 'Delete' },
    )

    /**
     * 以RID为条件,删除一行数据
     * @function delete
     * @memberof org.PrpcDbOrg
     * @instance
     * @param {org.IDbOrg} request DbOrg message or plain object
     * @returns {Promise<crud.DMLResult>} Promise
     * @variation 2
     */

    /**
     * Callback as used by {@link org.PrpcDbOrg#selectOne}.
     * @memberof org.PrpcDbOrg
     * @typedef SelectOneCallback
     * @type {function}
     * @param {Error|null} error Error, if any
     * @param {org.DbOrg} [response] DbOrg
     */

    /**
     * 以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
     * @function selectOne
     * @memberof org.PrpcDbOrg
     * @instance
     * @param {crud.IDMLParam} request DMLParam message or plain object
     * @param {org.PrpcDbOrg.SelectOneCallback} callback Node-style callback called with the error, if any, and DbOrg
     * @returns {undefined}
     * @variation 1
     */
    Object.defineProperty(
      (PrpcDbOrg.prototype.selectOne = function selectOne(request, callback) {
        return this.rpcCall(
          selectOne,
          $root.crud.DMLParam,
          $root.org.DbOrg,
          request,
          callback,
        )
      }),
      'name',
      { value: 'SelectOne' },
    )

    /**
     * 以RID为条件,查询一行数据，返回的数据列由DMLParam.ResultColumn指定,Rid由DMLParam.KeyValue[0]指定
     * @function selectOne
     * @memberof org.PrpcDbOrg
     * @instance
     * @param {crud.IDMLParam} request DMLParam message or plain object
     * @returns {Promise<org.DbOrg>} Promise
     * @variation 2
     */

    /**
     * Callback as used by {@link org.PrpcDbOrg#selectMany}.
     * @memberof org.PrpcDbOrg
     * @typedef SelectManyCallback
     * @type {function}
     * @param {Error|null} error Error, if any
     * @param {org.DbOrg} [response] DbOrg
     */

    /**
     * 以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
     * @function selectMany
     * @memberof org.PrpcDbOrg
     * @instance
     * @param {crud.IDMLParam} request DMLParam message or plain object
     * @param {org.PrpcDbOrg.SelectManyCallback} callback Node-style callback called with the error, if any, and DbOrg
     * @returns {undefined}
     * @variation 1
     */
    Object.defineProperty(
      (PrpcDbOrg.prototype.selectMany = function selectMany(request, callback) {
        return this.rpcCall(
          selectMany,
          $root.crud.DMLParam,
          $root.org.DbOrg,
          request,
          callback,
        )
      }),
      'name',
      { value: 'SelectMany' },
    )

    /**
     * 以多个RID为条件查询多行数据,返回的数据列由DMLParam.ResultColumn指定,Rids由DMLParam.KeyValue指定
     * @function selectMany
     * @memberof org.PrpcDbOrg
     * @instance
     * @param {crud.IDMLParam} request DMLParam message or plain object
     * @returns {Promise<org.DbOrg>} Promise
     * @variation 2
     */

    /**
     * Callback as used by {@link org.PrpcDbOrg#query}.
     * @memberof org.PrpcDbOrg
     * @typedef QueryCallback
     * @type {function}
     * @param {Error|null} error Error, if any
     * @param {org.DbOrg} [response] DbOrg
     */

    /**
     * 根据条件取得我有权限的数据
     * @function query
     * @memberof org.PrpcDbOrg
     * @instance
     * @param {crud.IPrivilegeParam} request PrivilegeParam message or plain object
     * @param {org.PrpcDbOrg.QueryCallback} callback Node-style callback called with the error, if any, and DbOrg
     * @returns {undefined}
     * @variation 1
     */
    Object.defineProperty(
      (PrpcDbOrg.prototype.query = function query(request, callback) {
        return this.rpcCall(
          query,
          $root.crud.PrivilegeParam,
          $root.org.DbOrg,
          request,
          callback,
        )
      }),
      'name',
      { value: 'Query' },
    )

    /**
     * 根据条件取得我有权限的数据
     * @function query
     * @memberof org.PrpcDbOrg
     * @instance
     * @param {crud.IPrivilegeParam} request PrivilegeParam message or plain object
     * @returns {Promise<org.DbOrg>} Promise
     * @variation 2
     */

    /**
     * Callback as used by {@link org.PrpcDbOrg#queryBatch}.
     * @memberof org.PrpcDbOrg
     * @typedef QueryBatchCallback
     * @type {function}
     * @param {Error|null} error Error, if any
     * @param {org.DbOrgList} [response] DbOrgList
     */

    /**
     * 根据条件取得我有权限的数据,批量返回数据,crud.PrivilegeParam.QueryParam.Batch指定一次返回多少条数据
     * @function queryBatch
     * @memberof org.PrpcDbOrg
     * @instance
     * @param {crud.IPrivilegeParam} request PrivilegeParam message or plain object
     * @param {org.PrpcDbOrg.QueryBatchCallback} callback Node-style callback called with the error, if any, and DbOrgList
     * @returns {undefined}
     * @variation 1
     */
    Object.defineProperty(
      (PrpcDbOrg.prototype.queryBatch = function queryBatch(request, callback) {
        return this.rpcCall(
          queryBatch,
          $root.crud.PrivilegeParam,
          $root.org.DbOrgList,
          request,
          callback,
        )
      }),
      'name',
      { value: 'QueryBatch' },
    )

    /**
     * 根据条件取得我有权限的数据,批量返回数据,crud.PrivilegeParam.QueryParam.Batch指定一次返回多少条数据
     * @function queryBatch
     * @memberof org.PrpcDbOrg
     * @instance
     * @param {crud.IPrivilegeParam} request PrivilegeParam message or plain object
     * @returns {Promise<org.DbOrgList>} Promise
     * @variation 2
     */

    return PrpcDbOrg
  })()

  org.DbOrg = (function () {
    /**
     * Properties of a DbOrg.
     * @memberof org
     * @interface IDbOrg
     * @property {string|null} [RID] @db uuid primary key
     * 行ID
     * @property {string|null} [OrgID] @db varchar(64) unique not null
     * 组织机构自编号
     * @property {number|null} [OrgType] @db int default 0
     * @property {number|null} [SortValue] @db int default 100
     * 排序值,由用户指定,不用管拼音或者其它语种的排序
     * @property {string|null} [ShortName] @db varchar(32) not null
     * 机构名称,缩写
     * @property {string|null} [FullName] @db varchar(256)
     * 机构名称,全称
     * @property {string|null} [Note] @db text
     * 机构描述/备注信息
     * @property {string|null} [Setting] @db jsonb default  '{}'::jsonb
     * 其它设置，可以在里面扩展需要用到的其它信息
     * @property {string|null} [CreatorRID] @db uuid
     * 创建者的rid
     * @property {string|null} [OrgRID] @db uuid
     * 此组织的上级机构
     * @property {string|null} [UpdatedAt] @db timestamp not null default now_utc()
     * 数据最后修改时间
     * @property {string|null} [UpdatedDC] @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */

    /**
     * Constructs a new DbOrg.
     * @memberof org
     * @classdesc 群组/组织结构表
     * @rpc crud pcrud
     * @dbpost DO $$ BEGIN BEGIN
     * @dbpost   ALTER TABLE DbOrg ADD CONSTRAINT fkDbOrgParentRID  FOREIGN KEY (OrgRID) REFERENCES DbOrg(RID);
     * @dbpost   EXCEPTION WHEN duplicate_object THEN --do nothing
     * @dbpost END; END $$;
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbOrgCreatorRID on DbOrg USING hash(CreatorRID);
     * @dbpost INSERT INTO dborg(rid, orgid, orgtype, sortvalue, shortname, fullname, note, setting, OrgRID, updatedat, updateddc)VALUES('00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', 0, 100, 'root', 'root', '', '{}'::jsonb, NULL, now_utc(), '')  ON CONFLICT  DO NOTHING;
     * @implements IDbOrg
     * @constructor
     * @param {org.IDbOrg=} [properties] Properties to set
     */
    function DbOrg(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * @db uuid primary key
     * 行ID
     * @member {string} RID
     * @memberof org.DbOrg
     * @instance
     */
    DbOrg.prototype.RID = ''

    /**
     * @db varchar(64) unique not null
     * 组织机构自编号
     * @member {string} OrgID
     * @memberof org.DbOrg
     * @instance
     */
    DbOrg.prototype.OrgID = ''

    /**
     * @db int default 0
     * @member {number} OrgType
     * @memberof org.DbOrg
     * @instance
     */
    DbOrg.prototype.OrgType = 0

    /**
     * @db int default 100
     * 排序值,由用户指定,不用管拼音或者其它语种的排序
     * @member {number} SortValue
     * @memberof org.DbOrg
     * @instance
     */
    DbOrg.prototype.SortValue = 0

    /**
     * @db varchar(32) not null
     * 机构名称,缩写
     * @member {string} ShortName
     * @memberof org.DbOrg
     * @instance
     */
    DbOrg.prototype.ShortName = ''

    /**
     * @db varchar(256)
     * 机构名称,全称
     * @member {string} FullName
     * @memberof org.DbOrg
     * @instance
     */
    DbOrg.prototype.FullName = ''

    /**
     * @db text
     * 机构描述/备注信息
     * @member {string} Note
     * @memberof org.DbOrg
     * @instance
     */
    DbOrg.prototype.Note = ''

    /**
     * @db jsonb default  '{}'::jsonb
     * 其它设置，可以在里面扩展需要用到的其它信息
     * @member {string} Setting
     * @memberof org.DbOrg
     * @instance
     */
    DbOrg.prototype.Setting = ''

    /**
     * @db uuid
     * 创建者的rid
     * @member {string} CreatorRID
     * @memberof org.DbOrg
     * @instance
     */
    DbOrg.prototype.CreatorRID = ''

    /**
     * @db uuid
     * 此组织的上级机构
     * @member {string} OrgRID
     * @memberof org.DbOrg
     * @instance
     */
    DbOrg.prototype.OrgRID = ''

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     * @member {string} UpdatedAt
     * @memberof org.DbOrg
     * @instance
     */
    DbOrg.prototype.UpdatedAt = ''

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     * @member {string} UpdatedDC
     * @memberof org.DbOrg
     * @instance
     */
    DbOrg.prototype.UpdatedDC = ''

    /**
     * Encodes the specified DbOrg message. Does not implicitly {@link org.DbOrg.verify|verify} messages.
     * @function encode
     * @memberof org.DbOrg
     * @static
     * @param {org.IDbOrg} message DbOrg message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    DbOrg.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (message.RID != null && Object.hasOwnProperty.call(message, 'RID'))
        writer.uint32(/* id 1, wireType 2 =*/ 10).string(message.RID)
      if (message.OrgID != null && Object.hasOwnProperty.call(message, 'OrgID'))
        writer.uint32(/* id 2, wireType 2 =*/ 18).string(message.OrgID)
      if (
        message.OrgType != null &&
        Object.hasOwnProperty.call(message, 'OrgType')
      )
        writer.uint32(/* id 3, wireType 0 =*/ 24).int32(message.OrgType)
      if (
        message.SortValue != null &&
        Object.hasOwnProperty.call(message, 'SortValue')
      )
        writer.uint32(/* id 4, wireType 0 =*/ 32).int32(message.SortValue)
      if (
        message.ShortName != null &&
        Object.hasOwnProperty.call(message, 'ShortName')
      )
        writer.uint32(/* id 5, wireType 2 =*/ 42).string(message.ShortName)
      if (
        message.FullName != null &&
        Object.hasOwnProperty.call(message, 'FullName')
      )
        writer.uint32(/* id 6, wireType 2 =*/ 50).string(message.FullName)
      if (message.Note != null && Object.hasOwnProperty.call(message, 'Note'))
        writer.uint32(/* id 7, wireType 2 =*/ 58).string(message.Note)
      if (
        message.Setting != null &&
        Object.hasOwnProperty.call(message, 'Setting')
      )
        writer.uint32(/* id 8, wireType 2 =*/ 66).string(message.Setting)
      if (
        message.CreatorRID != null &&
        Object.hasOwnProperty.call(message, 'CreatorRID')
      )
        writer.uint32(/* id 9, wireType 2 =*/ 74).string(message.CreatorRID)
      if (
        message.OrgRID != null &&
        Object.hasOwnProperty.call(message, 'OrgRID')
      )
        writer.uint32(/* id 11, wireType 2 =*/ 90).string(message.OrgRID)
      if (
        message.UpdatedAt != null &&
        Object.hasOwnProperty.call(message, 'UpdatedAt')
      )
        writer.uint32(/* id 14, wireType 2 =*/ 114).string(message.UpdatedAt)
      if (
        message.UpdatedDC != null &&
        Object.hasOwnProperty.call(message, 'UpdatedDC')
      )
        writer.uint32(/* id 15, wireType 2 =*/ 122).string(message.UpdatedDC)
      return writer
    }

    /**
     * Decodes a DbOrg message from the specified reader or buffer.
     * @function decode
     * @memberof org.DbOrg
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {org.DbOrg} DbOrg
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    DbOrg.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.org.DbOrg()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.RID = reader.string()
            break
          case 2:
            message.OrgID = reader.string()
            break
          case 3:
            message.OrgType = reader.int32()
            break
          case 4:
            message.SortValue = reader.int32()
            break
          case 5:
            message.ShortName = reader.string()
            break
          case 6:
            message.FullName = reader.string()
            break
          case 7:
            message.Note = reader.string()
            break
          case 8:
            message.Setting = reader.string()
            break
          case 9:
            message.CreatorRID = reader.string()
            break
          case 11:
            message.OrgRID = reader.string()
            break
          case 14:
            message.UpdatedAt = reader.string()
            break
          case 15:
            message.UpdatedDC = reader.string()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return DbOrg
  })()

  org.DbOrgList = (function () {
    /**
     * Properties of a DbOrgList.
     * @memberof org
     * @interface IDbOrgList
     * @property {number|null} [BatchNo] 批次号，从0开始
     * @property {number|null} [RowsOffset] rows offset,从0开始
     * @property {Array.<org.IDbOrg>|null} [Rows] 本批次数据
     */

    /**
     * Constructs a new DbOrgList.
     * @memberof org
     * @classdesc DbOrg list
     * @implements IDbOrgList
     * @constructor
     * @param {org.IDbOrgList=} [properties] Properties to set
     */
    function DbOrgList(properties) {
      this.Rows = []
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 批次号，从0开始
     * @member {number} BatchNo
     * @memberof org.DbOrgList
     * @instance
     */
    DbOrgList.prototype.BatchNo = 0

    /**
     * rows offset,从0开始
     * @member {number} RowsOffset
     * @memberof org.DbOrgList
     * @instance
     */
    DbOrgList.prototype.RowsOffset = 0

    /**
     * 本批次数据
     * @member {Array.<org.IDbOrg>} Rows
     * @memberof org.DbOrgList
     * @instance
     */
    DbOrgList.prototype.Rows = $util.emptyArray

    /**
     * Encodes the specified DbOrgList message. Does not implicitly {@link org.DbOrgList.verify|verify} messages.
     * @function encode
     * @memberof org.DbOrgList
     * @static
     * @param {org.IDbOrgList} message DbOrgList message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    DbOrgList.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (
        message.BatchNo != null &&
        Object.hasOwnProperty.call(message, 'BatchNo')
      )
        writer.uint32(/* id 1, wireType 0 =*/ 8).uint32(message.BatchNo)
      if (
        message.RowsOffset != null &&
        Object.hasOwnProperty.call(message, 'RowsOffset')
      )
        writer.uint32(/* id 2, wireType 0 =*/ 16).uint32(message.RowsOffset)
      if (message.Rows != null && message.Rows.length)
        for (let i = 0; i < message.Rows.length; ++i)
          $root.org.DbOrg.encode(
            message.Rows[i],
            writer.uint32(/* id 3, wireType 2 =*/ 26).fork(),
          ).ldelim()
      return writer
    }

    /**
     * Decodes a DbOrgList message from the specified reader or buffer.
     * @function decode
     * @memberof org.DbOrgList
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {org.DbOrgList} DbOrgList
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    DbOrgList.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.org.DbOrgList()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.BatchNo = reader.uint32()
            break
          case 2:
            message.RowsOffset = reader.uint32()
            break
          case 3:
            if (!(message.Rows && message.Rows.length)) message.Rows = []
            message.Rows.push($root.org.DbOrg.decode(reader, reader.uint32()))
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return DbOrgList
  })()

  return org
})())

export const crud = ($root.crud = (() => {
  /**
   * Namespace crud.
   * @exports crud
   * @namespace
   */
  const crud = $root.crud || {}

  crud.DMLResult = (function () {
    /**
     * Properties of a DMLResult.
     * @memberof crud
     * @interface IDMLResult
     * @property {number|null} [AffectedRow] 影响的行数，如果成功>0,否则=0
     * @property {string|null} [errInfo] 错误信息，如果有的话
     * @property {string|null} [Note] 附加信息,json格式
     */

    /**
     * Constructs a new DMLResult.
     * @memberof crud
     * @classdesc CRUD DML结果
     * @implements IDMLResult
     * @constructor
     * @param {crud.IDMLResult=} [properties] Properties to set
     */
    function DMLResult(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 影响的行数，如果成功>0,否则=0
     * @member {number} AffectedRow
     * @memberof crud.DMLResult
     * @instance
     */
    DMLResult.prototype.AffectedRow = 0

    /**
     * 错误信息，如果有的话
     * @member {string} errInfo
     * @memberof crud.DMLResult
     * @instance
     */
    DMLResult.prototype.errInfo = ''

    /**
     * 附加信息,json格式
     * @member {string} Note
     * @memberof crud.DMLResult
     * @instance
     */
    DMLResult.prototype.Note = ''

    /**
     * Encodes the specified DMLResult message. Does not implicitly {@link crud.DMLResult.verify|verify} messages.
     * @function encode
     * @memberof crud.DMLResult
     * @static
     * @param {crud.IDMLResult} message DMLResult message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    DMLResult.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (
        message.AffectedRow != null &&
        Object.hasOwnProperty.call(message, 'AffectedRow')
      )
        writer.uint32(/* id 1, wireType 0 =*/ 8).int32(message.AffectedRow)
      if (
        message.errInfo != null &&
        Object.hasOwnProperty.call(message, 'errInfo')
      )
        writer.uint32(/* id 2, wireType 2 =*/ 18).string(message.errInfo)
      if (message.Note != null && Object.hasOwnProperty.call(message, 'Note'))
        writer.uint32(/* id 3, wireType 2 =*/ 26).string(message.Note)
      return writer
    }

    /**
     * Decodes a DMLResult message from the specified reader or buffer.
     * @function decode
     * @memberof crud.DMLResult
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {crud.DMLResult} DMLResult
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    DMLResult.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.crud.DMLResult()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.AffectedRow = reader.int32()
            break
          case 2:
            message.errInfo = reader.string()
            break
          case 3:
            message.Note = reader.string()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return DMLResult
  })()

  crud.DMLParam = (function () {
    /**
     * Properties of a DMLParam.
     * @memberof crud
     * @interface IDMLParam
     * @property {Array.<string>|null} [KeyColumn] 条件字段名
     * @property {Array.<string>|null} [ResultColumn] 结果字段名
     * @property {Array.<string>|null} [KeyValue] 条件值
     */

    /**
     * Constructs a new DMLParam.
     * @memberof crud
     * @classdesc 简单CRUD里面用到的条件和结果字段列表
     * @implements IDMLParam
     * @constructor
     * @param {crud.IDMLParam=} [properties] Properties to set
     */
    function DMLParam(properties) {
      this.KeyColumn = []
      this.ResultColumn = []
      this.KeyValue = []
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 条件字段名
     * @member {Array.<string>} KeyColumn
     * @memberof crud.DMLParam
     * @instance
     */
    DMLParam.prototype.KeyColumn = $util.emptyArray

    /**
     * 结果字段名
     * @member {Array.<string>} ResultColumn
     * @memberof crud.DMLParam
     * @instance
     */
    DMLParam.prototype.ResultColumn = $util.emptyArray

    /**
     * 条件值
     * @member {Array.<string>} KeyValue
     * @memberof crud.DMLParam
     * @instance
     */
    DMLParam.prototype.KeyValue = $util.emptyArray

    /**
     * Encodes the specified DMLParam message. Does not implicitly {@link crud.DMLParam.verify|verify} messages.
     * @function encode
     * @memberof crud.DMLParam
     * @static
     * @param {crud.IDMLParam} message DMLParam message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    DMLParam.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (message.KeyColumn != null && message.KeyColumn.length)
        for (let i = 0; i < message.KeyColumn.length; ++i)
          writer.uint32(/* id 1, wireType 2 =*/ 10).string(message.KeyColumn[i])
      if (message.ResultColumn != null && message.ResultColumn.length)
        for (let i = 0; i < message.ResultColumn.length; ++i)
          writer
            .uint32(/* id 2, wireType 2 =*/ 18)
            .string(message.ResultColumn[i])
      if (message.KeyValue != null && message.KeyValue.length)
        for (let i = 0; i < message.KeyValue.length; ++i)
          writer.uint32(/* id 3, wireType 2 =*/ 26).string(message.KeyValue[i])
      return writer
    }

    /**
     * Decodes a DMLParam message from the specified reader or buffer.
     * @function decode
     * @memberof crud.DMLParam
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {crud.DMLParam} DMLParam
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    DMLParam.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.crud.DMLParam()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            if (!(message.KeyColumn && message.KeyColumn.length))
              message.KeyColumn = []
            message.KeyColumn.push(reader.string())
            break
          case 2:
            if (!(message.ResultColumn && message.ResultColumn.length))
              message.ResultColumn = []
            message.ResultColumn.push(reader.string())
            break
          case 3:
            if (!(message.KeyValue && message.KeyValue.length))
              message.KeyValue = []
            message.KeyValue.push(reader.string())
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return DMLParam
  })()

  crud.WhereItem = (function () {
    /**
     * Properties of a WhereItem.
     * @memberof crud
     * @interface IWhereItem
     * @property {string|null} [Field] fieldname
     * @property {string|null} [FieldCompareOperator] field compare operator
     * @property {string|null} [FieldValue] Field value
     */

    /**
     * Constructs a new WhereItem.
     * @memberof crud
     * @classdesc sql where额外条件项
     * @implements IWhereItem
     * @constructor
     * @param {crud.IWhereItem=} [properties] Properties to set
     */
    function WhereItem(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * fieldname
     * @member {string} Field
     * @memberof crud.WhereItem
     * @instance
     */
    WhereItem.prototype.Field = ''

    /**
     * field compare operator
     * @member {string} FieldCompareOperator
     * @memberof crud.WhereItem
     * @instance
     */
    WhereItem.prototype.FieldCompareOperator = ''

    /**
     * Field value
     * @member {string} FieldValue
     * @memberof crud.WhereItem
     * @instance
     */
    WhereItem.prototype.FieldValue = ''

    /**
     * Encodes the specified WhereItem message. Does not implicitly {@link crud.WhereItem.verify|verify} messages.
     * @function encode
     * @memberof crud.WhereItem
     * @static
     * @param {crud.IWhereItem} message WhereItem message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    WhereItem.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (message.Field != null && Object.hasOwnProperty.call(message, 'Field'))
        writer.uint32(/* id 5, wireType 2 =*/ 42).string(message.Field)
      if (
        message.FieldCompareOperator != null &&
        Object.hasOwnProperty.call(message, 'FieldCompareOperator')
      )
        writer
          .uint32(/* id 6, wireType 2 =*/ 50)
          .string(message.FieldCompareOperator)
      if (
        message.FieldValue != null &&
        Object.hasOwnProperty.call(message, 'FieldValue')
      )
        writer.uint32(/* id 7, wireType 2 =*/ 58).string(message.FieldValue)
      return writer
    }

    /**
     * Decodes a WhereItem message from the specified reader or buffer.
     * @function decode
     * @memberof crud.WhereItem
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {crud.WhereItem} WhereItem
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    WhereItem.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.crud.WhereItem()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 5:
            message.Field = reader.string()
            break
          case 6:
            message.FieldCompareOperator = reader.string()
            break
          case 7:
            message.FieldValue = reader.string()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return WhereItem
  })()

  crud.QueryParam = (function () {
    /**
     * Properties of a QueryParam.
     * @memberof crud
     * @interface IQueryParam
     * @property {Array.<string>|null} [ResultColumn] 想要的结果字段名，不填写为全要
     * @property {Array.<string>|null} [TimeColumn] Query时需要处理时区的字段,单数为字段名，双数为需要加减的的分钟数
     * 需要此功能必须把所有的结果列名填写在ResultColumn，否则无法处理
     * TimeColumn = ['ActionTime','480'，‘RecvTime','-60']
     * @property {Array.<crud.IWhereItem>|null} [Where] where额外条件项,只支持 and
     * @property {number|null} [Limit] 分页，需要的数据，如果Limit=0 and Offset=0,则视为不需要分页
     * @property {number|null} [Offset] 分页，从什么位置开始，如果Limit=0 and
     * Offset!=0,为从Offset的位置开始的所有数据
     * @property {Array.<string>|null} [OrderBy] 排序条件，每项为sql的order by 条件
     * select * from a order by col1 asc,col2 desc
     * OrderBy=["col1 asc","col2 desc"]
     * @property {number|null} [Batch] QueryBatch调用时,指定一次返回的数据量，不指定时后端按每次一个返回
     */

    /**
     * Constructs a new QueryParam.
     * @memberof crud
     * @classdesc 查询条件
     * @implements IQueryParam
     * @constructor
     * @param {crud.IQueryParam=} [properties] Properties to set
     */
    function QueryParam(properties) {
      this.ResultColumn = []
      this.TimeColumn = []
      this.Where = []
      this.OrderBy = []
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 想要的结果字段名，不填写为全要
     * @member {Array.<string>} ResultColumn
     * @memberof crud.QueryParam
     * @instance
     */
    QueryParam.prototype.ResultColumn = $util.emptyArray

    /**
     * Query时需要处理时区的字段,单数为字段名，双数为需要加减的的分钟数
     * 需要此功能必须把所有的结果列名填写在ResultColumn，否则无法处理
     * TimeColumn = ['ActionTime','480'，‘RecvTime','-60']
     * @member {Array.<string>} TimeColumn
     * @memberof crud.QueryParam
     * @instance
     */
    QueryParam.prototype.TimeColumn = $util.emptyArray

    /**
     * where额外条件项,只支持 and
     * @member {Array.<crud.IWhereItem>} Where
     * @memberof crud.QueryParam
     * @instance
     */
    QueryParam.prototype.Where = $util.emptyArray

    /**
     * 分页，需要的数据，如果Limit=0 and Offset=0,则视为不需要分页
     * @member {number} Limit
     * @memberof crud.QueryParam
     * @instance
     */
    QueryParam.prototype.Limit = 0

    /**
     * 分页，从什么位置开始，如果Limit=0 and
     * Offset!=0,为从Offset的位置开始的所有数据
     * @member {number} Offset
     * @memberof crud.QueryParam
     * @instance
     */
    QueryParam.prototype.Offset = 0

    /**
     * 排序条件，每项为sql的order by 条件
     * select * from a order by col1 asc,col2 desc
     * OrderBy=["col1 asc","col2 desc"]
     * @member {Array.<string>} OrderBy
     * @memberof crud.QueryParam
     * @instance
     */
    QueryParam.prototype.OrderBy = $util.emptyArray

    /**
     * QueryBatch调用时,指定一次返回的数据量，不指定时后端按每次一个返回
     * @member {number} Batch
     * @memberof crud.QueryParam
     * @instance
     */
    QueryParam.prototype.Batch = 0

    /**
     * Encodes the specified QueryParam message. Does not implicitly {@link crud.QueryParam.verify|verify} messages.
     * @function encode
     * @memberof crud.QueryParam
     * @static
     * @param {crud.IQueryParam} message QueryParam message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    QueryParam.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (message.ResultColumn != null && message.ResultColumn.length)
        for (let i = 0; i < message.ResultColumn.length; ++i)
          writer
            .uint32(/* id 1, wireType 2 =*/ 10)
            .string(message.ResultColumn[i])
      if (message.TimeColumn != null && message.TimeColumn.length)
        for (let i = 0; i < message.TimeColumn.length; ++i)
          writer
            .uint32(/* id 2, wireType 2 =*/ 18)
            .string(message.TimeColumn[i])
      if (message.Where != null && message.Where.length)
        for (let i = 0; i < message.Where.length; ++i)
          $root.crud.WhereItem.encode(
            message.Where[i],
            writer.uint32(/* id 5, wireType 2 =*/ 42).fork(),
          ).ldelim()
      if (message.Limit != null && Object.hasOwnProperty.call(message, 'Limit'))
        writer.uint32(/* id 6, wireType 0 =*/ 48).uint32(message.Limit)
      if (
        message.Offset != null &&
        Object.hasOwnProperty.call(message, 'Offset')
      )
        writer.uint32(/* id 7, wireType 0 =*/ 56).uint32(message.Offset)
      if (message.OrderBy != null && message.OrderBy.length)
        for (let i = 0; i < message.OrderBy.length; ++i)
          writer.uint32(/* id 8, wireType 2 =*/ 66).string(message.OrderBy[i])
      if (message.Batch != null && Object.hasOwnProperty.call(message, 'Batch'))
        writer.uint32(/* id 9, wireType 0 =*/ 72).uint32(message.Batch)
      return writer
    }

    /**
     * Decodes a QueryParam message from the specified reader or buffer.
     * @function decode
     * @memberof crud.QueryParam
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {crud.QueryParam} QueryParam
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    QueryParam.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.crud.QueryParam()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            if (!(message.ResultColumn && message.ResultColumn.length))
              message.ResultColumn = []
            message.ResultColumn.push(reader.string())
            break
          case 2:
            if (!(message.TimeColumn && message.TimeColumn.length))
              message.TimeColumn = []
            message.TimeColumn.push(reader.string())
            break
          case 5:
            if (!(message.Where && message.Where.length)) message.Where = []
            message.Where.push(
              $root.crud.WhereItem.decode(reader, reader.uint32()),
            )
            break
          case 6:
            message.Limit = reader.uint32()
            break
          case 7:
            message.Offset = reader.uint32()
            break
          case 8:
            if (!(message.OrderBy && message.OrderBy.length))
              message.OrderBy = []
            message.OrderBy.push(reader.string())
            break
          case 9:
            message.Batch = reader.uint32()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return QueryParam
  })()

  crud.PrivilegeParam = (function () {
    /**
     * Properties of a PrivilegeParam.
     * @memberof crud
     * @interface IPrivilegeParam
     * @property {string|null} [System] 系统名称
     * @property {string|null} [SessionID] SessionID
     * @property {crud.IQueryParam|null} [QueryCondition] 查询条件
     */

    /**
     * Constructs a new PrivilegeParam.
     * @memberof crud
     * @classdesc 取得用户有权限的数据
     * @implements IPrivilegeParam
     * @constructor
     * @param {crud.IPrivilegeParam=} [properties] Properties to set
     */
    function PrivilegeParam(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 系统名称
     * @member {string} System
     * @memberof crud.PrivilegeParam
     * @instance
     */
    PrivilegeParam.prototype.System = ''

    /**
     * SessionID
     * @member {string} SessionID
     * @memberof crud.PrivilegeParam
     * @instance
     */
    PrivilegeParam.prototype.SessionID = ''

    /**
     * 查询条件
     * @member {crud.IQueryParam|null|undefined} QueryCondition
     * @memberof crud.PrivilegeParam
     * @instance
     */
    PrivilegeParam.prototype.QueryCondition = null

    /**
     * Encodes the specified PrivilegeParam message. Does not implicitly {@link crud.PrivilegeParam.verify|verify} messages.
     * @function encode
     * @memberof crud.PrivilegeParam
     * @static
     * @param {crud.IPrivilegeParam} message PrivilegeParam message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    PrivilegeParam.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (
        message.System != null &&
        Object.hasOwnProperty.call(message, 'System')
      )
        writer.uint32(/* id 1, wireType 2 =*/ 10).string(message.System)
      if (
        message.SessionID != null &&
        Object.hasOwnProperty.call(message, 'SessionID')
      )
        writer.uint32(/* id 3, wireType 2 =*/ 26).string(message.SessionID)
      if (
        message.QueryCondition != null &&
        Object.hasOwnProperty.call(message, 'QueryCondition')
      )
        $root.crud.QueryParam.encode(
          message.QueryCondition,
          writer.uint32(/* id 5, wireType 2 =*/ 42).fork(),
        ).ldelim()
      return writer
    }

    /**
     * Decodes a PrivilegeParam message from the specified reader or buffer.
     * @function decode
     * @memberof crud.PrivilegeParam
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {crud.PrivilegeParam} PrivilegeParam
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    PrivilegeParam.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.crud.PrivilegeParam()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.System = reader.string()
            break
          case 3:
            message.SessionID = reader.string()
            break
          case 5:
            message.QueryCondition = $root.crud.QueryParam.decode(
              reader,
              reader.uint32(),
            )
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return PrivilegeParam
  })()

  return crud
})())

export { $root as default }

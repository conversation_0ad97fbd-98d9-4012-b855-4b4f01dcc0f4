import * as $protobuf from 'protobufjs'
/** Namespace user. */
export namespace user {
  /** Properties of a DbPermission. */
  interface IDbPermission {
    /**
     * @db uuid primary key
     * 行ID,permission rid
     */
    RID?: string | null

    /**
     * @db text not null
     * 权限类别
     */
    PermissionType?: string | null

    /**
     * @db text not null
     * permission 名称
     */
    PermissionName?: string | null

    /**
     * @db text not null
     * permission value
     */
    PermissionValue?: string | null

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     */
    UpdatedAt?: string | null

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */
    UpdatedDC?: string | null
  }

  /** 所有的权限信息表,此表信息为预置，一般不给删除 */
  class DbPermission implements IDbPermission {
    /**
     * Constructs a new DbPermission.
     * @rpc crud
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbPermissionPermissionValue on DbPermission USING hash(PermissionValue);
     * @param [properties] Properties to set
     */
    constructor(properties?: user.IDbPermission)

    /**
     * @db uuid primary key
     * 行ID,permission rid
     */
    public RID: string

    /**
     * @db text not null
     * 权限类别
     */
    public PermissionType: string

    /**
     * @db text not null
     * permission 名称
     */
    public PermissionName: string

    /**
     * @db text not null
     * permission value
     */
    public PermissionValue: string

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     */
    public UpdatedAt: string

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */
    public UpdatedDC: string

    /**
     * Encodes the specified DbPermission message. Does not implicitly {@link user.DbPermission.verify|verify} messages.
     * @param message DbPermission message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: user.IDbPermission,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a DbPermission message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns DbPermission
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): user.DbPermission
  }

  /** Properties of a DbRole. */
  interface IDbRole {
    /**
     * @db uuid primary key
     * 行ID,role rid
     */
    RID?: string | null

    /**
     * @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 用户所属的群组
     */
    OrgRID?: string | null

    /**
     * @db text not null
     * role 名称
     */
    RoleName?: string | null

    /**
     * @db uuid --REFERENCES DbUser(RID) ON DELETE set null
     * role 创建者 rid
     */
    Creator?: string | null

    /**
     * @db int
     * 是否是内置的角色，内置角色不能删除
     */
    IsBuiltIn?: number | null

    /**
     * @db jsonb not null default  '{}'::jsonb
     * 其它设置，可以在里面扩展需要用到的其它信息
     */
    Setting?: string | null

    /**
     * @db int default 100
     * sort value
     */
    SortValue?: number | null

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     */
    UpdatedAt?: string | null

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */
    UpdatedDC?: string | null
  }

  /** 所有的角色信息表 */
  class DbRole implements IDbRole {
    /**
     * Constructs a new DbRole.
     * @rpc crud pcrud
     * @dbpost insert into DbRole (rid, orgrid, rolename, creator, isbuiltin, setting, sortvalue, updatedat, updateddc) VALUES('55555555-5555-5555-5555-555555555555', '00000000-0000-0000-0000-000000000000', 'base', null, 1, '{}'::jsonb, 0, now_utc(), '') ON CONFLICT  DO NOTHING;
     * @dbpost insert into DbRole (rid, orgrid, rolename, creator, isbuiltin, setting, sortvalue, updatedat, updateddc) VALUES('00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', 'admin', null, 1, '{}'::jsonb, 0, now_utc(), '') ON CONFLICT  DO NOTHING;
     * @param [properties] Properties to set
     */
    constructor(properties?: user.IDbRole)

    /**
     * @db uuid primary key
     * 行ID,role rid
     */
    public RID: string

    /**
     * @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 用户所属的群组
     */
    public OrgRID: string

    /**
     * @db text not null
     * role 名称
     */
    public RoleName: string

    /**
     * @db uuid --REFERENCES DbUser(RID) ON DELETE set null
     * role 创建者 rid
     */
    public Creator: string

    /**
     * @db int
     * 是否是内置的角色，内置角色不能删除
     */
    public IsBuiltIn: number

    /**
     * @db jsonb not null default  '{}'::jsonb
     * 其它设置，可以在里面扩展需要用到的其它信息
     */
    public Setting: string

    /**
     * @db int default 100
     * sort value
     */
    public SortValue: number

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     */
    public UpdatedAt: string

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */
    public UpdatedDC: string

    /**
     * Encodes the specified DbRole message. Does not implicitly {@link user.DbRole.verify|verify} messages.
     * @param message DbRole message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: user.IDbRole,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a DbRole message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns DbRole
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): user.DbRole
  }

  /** Properties of a DbRolePermission. */
  interface IDbRolePermission {
    /**
     * @db uuid primary key
     * 行ID
     */
    RID?: string | null

    /**
     * @db uuid not null REFERENCES DbRole(RID) ON DELETE CASCADE
     * role rid
     */
    RoleRID?: string | null

    /**
     * @db uuid not null REFERENCES DbPermission(RID) ON DELETE CASCADE
     * permission rid
     */
    PermissionRID?: string | null

    /**
     * @db uuid --REFERENCES DbUser(RID) ON DELETE set null
     * role 创建者 rid
     */
    Creator?: string | null

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     */
    UpdatedAt?: string | null

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */
    UpdatedDC?: string | null
  }

  /** 角色权限信息表 */
  class DbRolePermission implements IDbRolePermission {
    /**
     * Constructs a new DbRolePermission.
     * @rpc crud pcrud
     * @param [properties] Properties to set
     */
    constructor(properties?: user.IDbRolePermission)

    /**
     * @db uuid primary key
     * 行ID
     */
    public RID: string

    /**
     * @db uuid not null REFERENCES DbRole(RID) ON DELETE CASCADE
     * role rid
     */
    public RoleRID: string

    /**
     * @db uuid not null REFERENCES DbPermission(RID) ON DELETE CASCADE
     * permission rid
     */
    public PermissionRID: string

    /**
     * @db uuid --REFERENCES DbUser(RID) ON DELETE set null
     * role 创建者 rid
     */
    public Creator: string

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     */
    public UpdatedAt: string

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */
    public UpdatedDC: string

    /**
     * Encodes the specified DbRolePermission message. Does not implicitly {@link user.DbRolePermission.verify|verify} messages.
     * @param message DbRolePermission message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: user.IDbRolePermission,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a DbRolePermission message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns DbRolePermission
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): user.DbRolePermission
  }
}

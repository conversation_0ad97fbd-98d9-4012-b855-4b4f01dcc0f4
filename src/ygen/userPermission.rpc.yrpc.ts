import { Writer } from 'protobufjs'
import { rpcCon } from 'jsyrpc'
import { ICallOption } from 'jsyrpc'

import { TRpcStream } from 'jsyrpc'
import * as UserPermissionY from '@ygen/userPermission'
import * as CrudY from '@ygen/crud'
import * as UserPermissionListY from '@ygen/userPermission.list'

export function RpcDbPermissionInsert(
  req: UserPermissionY.user.IDbPermission,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = UserPermissionY.user.DbPermission.encode(req)
  let reqData = w.finish()

  const api = '/user.RpcDbPermission/Insert'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function RpcDbPermissionUpdate(
  req: UserPermissionY.user.IDbPermission,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = UserPermissionY.user.DbPermission.encode(req)
  let reqData = w.finish()

  const api = '/user.RpcDbPermission/Update'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function RpcDbPermissionPartialUpdate(
  req: UserPermissionY.user.IDbPermission,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = UserPermissionY.user.DbPermission.encode(req)
  let reqData = w.finish()

  const api = '/user.RpcDbPermission/PartialUpdate'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function RpcDbPermissionDelete(
  req: UserPermissionY.user.IDbPermission,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = UserPermissionY.user.DbPermission.encode(req)
  let reqData = w.finish()

  const api = '/user.RpcDbPermission/Delete'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function RpcDbPermissionSelectOne(
  req: CrudY.crud.IDMLParam,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = CrudY.crud.DMLParam.encode(req)
  let reqData = w.finish()

  const api = '/user.RpcDbPermission/SelectOne'
  const v = 0

  return rpcCon.UnaryCall(
    reqData,
    api,
    v,
    UserPermissionY.user.DbPermission,
    callOpt,
  )
}

export function RpcDbPermissionSelectMany(
  req: CrudY.crud.DMLParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/user.RpcDbPermission/SelectMany'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.DMLParam,
    UserPermissionY.user.DbPermission,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function RpcDbPermissionQuery(
  req: CrudY.crud.QueryParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/user.RpcDbPermission/Query'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.QueryParam,
    UserPermissionY.user.DbPermission,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function RpcDbPermissionQueryBatch(
  req: CrudY.crud.QueryParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/user.RpcDbPermission/QueryBatch'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.QueryParam,
    UserPermissionListY.user.DbPermissionList,
    callOpt,
  )
  r.sendFirst(req)
  return r
}

export class RpcDbPermission {
  public static Insert(
    req: UserPermissionY.user.IDbPermission,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbPermissionInsert(req, callOpt)
  }
  public static Update(
    req: UserPermissionY.user.IDbPermission,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbPermissionUpdate(req, callOpt)
  }
  public static PartialUpdate(
    req: UserPermissionY.user.IDbPermission,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbPermissionPartialUpdate(req, callOpt)
  }
  public static Delete(
    req: UserPermissionY.user.IDbPermission,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbPermissionDelete(req, callOpt)
  }
  public static SelectOne(
    req: CrudY.crud.IDMLParam,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbPermissionSelectOne(req, callOpt)
  }
  public static SelectMany(
    req: CrudY.crud.DMLParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return RpcDbPermissionSelectMany(req, callOpt)
  }
  public static Query(
    req: CrudY.crud.QueryParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return RpcDbPermissionQuery(req, callOpt)
  }
  public static QueryBatch(
    req: CrudY.crud.QueryParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return RpcDbPermissionQueryBatch(req, callOpt)
  }
}

export function RpcDbRoleInsert(
  req: UserPermissionY.user.IDbRole,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = UserPermissionY.user.DbRole.encode(req)
  let reqData = w.finish()

  const api = '/user.RpcDbRole/Insert'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function RpcDbRoleUpdate(
  req: UserPermissionY.user.IDbRole,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = UserPermissionY.user.DbRole.encode(req)
  let reqData = w.finish()

  const api = '/user.RpcDbRole/Update'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function RpcDbRolePartialUpdate(
  req: UserPermissionY.user.IDbRole,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = UserPermissionY.user.DbRole.encode(req)
  let reqData = w.finish()

  const api = '/user.RpcDbRole/PartialUpdate'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function RpcDbRoleDelete(
  req: UserPermissionY.user.IDbRole,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = UserPermissionY.user.DbRole.encode(req)
  let reqData = w.finish()

  const api = '/user.RpcDbRole/Delete'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function RpcDbRoleSelectOne(
  req: CrudY.crud.IDMLParam,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = CrudY.crud.DMLParam.encode(req)
  let reqData = w.finish()

  const api = '/user.RpcDbRole/SelectOne'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, UserPermissionY.user.DbRole, callOpt)
}

export function RpcDbRoleSelectMany(
  req: CrudY.crud.DMLParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/user.RpcDbRole/SelectMany'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.DMLParam,
    UserPermissionY.user.DbRole,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function RpcDbRoleQuery(
  req: CrudY.crud.QueryParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/user.RpcDbRole/Query'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.QueryParam,
    UserPermissionY.user.DbRole,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function RpcDbRoleQueryBatch(
  req: CrudY.crud.QueryParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/user.RpcDbRole/QueryBatch'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.QueryParam,
    UserPermissionListY.user.DbRoleList,
    callOpt,
  )
  r.sendFirst(req)
  return r
}

export class RpcDbRole {
  public static Insert(
    req: UserPermissionY.user.IDbRole,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbRoleInsert(req, callOpt)
  }
  public static Update(
    req: UserPermissionY.user.IDbRole,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbRoleUpdate(req, callOpt)
  }
  public static PartialUpdate(
    req: UserPermissionY.user.IDbRole,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbRolePartialUpdate(req, callOpt)
  }
  public static Delete(
    req: UserPermissionY.user.IDbRole,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbRoleDelete(req, callOpt)
  }
  public static SelectOne(
    req: CrudY.crud.IDMLParam,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbRoleSelectOne(req, callOpt)
  }
  public static SelectMany(
    req: CrudY.crud.DMLParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return RpcDbRoleSelectMany(req, callOpt)
  }
  public static Query(
    req: CrudY.crud.QueryParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return RpcDbRoleQuery(req, callOpt)
  }
  public static QueryBatch(
    req: CrudY.crud.QueryParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return RpcDbRoleQueryBatch(req, callOpt)
  }
}

export function PrpcDbRoleInsert(
  req: UserPermissionY.user.IDbRole,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = UserPermissionY.user.DbRole.encode(req)
  let reqData = w.finish()

  const api = '/user.PrpcDbRole/Insert'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function PrpcDbRoleUpdate(
  req: UserPermissionY.user.IDbRole,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = UserPermissionY.user.DbRole.encode(req)
  let reqData = w.finish()

  const api = '/user.PrpcDbRole/Update'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function PrpcDbRolePartialUpdate(
  req: UserPermissionY.user.IDbRole,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = UserPermissionY.user.DbRole.encode(req)
  let reqData = w.finish()

  const api = '/user.PrpcDbRole/PartialUpdate'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function PrpcDbRoleDelete(
  req: UserPermissionY.user.IDbRole,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = UserPermissionY.user.DbRole.encode(req)
  let reqData = w.finish()

  const api = '/user.PrpcDbRole/Delete'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function PrpcDbRoleSelectOne(
  req: CrudY.crud.IDMLParam,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = CrudY.crud.DMLParam.encode(req)
  let reqData = w.finish()

  const api = '/user.PrpcDbRole/SelectOne'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, UserPermissionY.user.DbRole, callOpt)
}

export function PrpcDbRoleSelectMany(
  req: CrudY.crud.DMLParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/user.PrpcDbRole/SelectMany'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.DMLParam,
    UserPermissionY.user.DbRole,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function PrpcDbRoleQuery(
  req: CrudY.crud.PrivilegeParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/user.PrpcDbRole/Query'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.PrivilegeParam,
    UserPermissionY.user.DbRole,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function PrpcDbRoleQueryBatch(
  req: CrudY.crud.PrivilegeParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/user.PrpcDbRole/QueryBatch'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.PrivilegeParam,
    UserPermissionListY.user.DbRoleList,
    callOpt,
  )
  r.sendFirst(req)
  return r
}

export class PrpcDbRole {
  public static Insert(
    req: UserPermissionY.user.IDbRole,
    callOpt?: ICallOption,
  ): boolean {
    return PrpcDbRoleInsert(req, callOpt)
  }
  public static Update(
    req: UserPermissionY.user.IDbRole,
    callOpt?: ICallOption,
  ): boolean {
    return PrpcDbRoleUpdate(req, callOpt)
  }
  public static PartialUpdate(
    req: UserPermissionY.user.IDbRole,
    callOpt?: ICallOption,
  ): boolean {
    return PrpcDbRolePartialUpdate(req, callOpt)
  }
  public static Delete(
    req: UserPermissionY.user.IDbRole,
    callOpt?: ICallOption,
  ): boolean {
    return PrpcDbRoleDelete(req, callOpt)
  }
  public static SelectOne(
    req: CrudY.crud.IDMLParam,
    callOpt?: ICallOption,
  ): boolean {
    return PrpcDbRoleSelectOne(req, callOpt)
  }
  public static SelectMany(
    req: CrudY.crud.DMLParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return PrpcDbRoleSelectMany(req, callOpt)
  }
  public static Query(
    req: CrudY.crud.PrivilegeParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return PrpcDbRoleQuery(req, callOpt)
  }
  public static QueryBatch(
    req: CrudY.crud.PrivilegeParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return PrpcDbRoleQueryBatch(req, callOpt)
  }
}

export function RpcDbRolePermissionInsert(
  req: UserPermissionY.user.IDbRolePermission,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = UserPermissionY.user.DbRolePermission.encode(req)
  let reqData = w.finish()

  const api = '/user.RpcDbRolePermission/Insert'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function RpcDbRolePermissionUpdate(
  req: UserPermissionY.user.IDbRolePermission,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = UserPermissionY.user.DbRolePermission.encode(req)
  let reqData = w.finish()

  const api = '/user.RpcDbRolePermission/Update'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function RpcDbRolePermissionPartialUpdate(
  req: UserPermissionY.user.IDbRolePermission,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = UserPermissionY.user.DbRolePermission.encode(req)
  let reqData = w.finish()

  const api = '/user.RpcDbRolePermission/PartialUpdate'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function RpcDbRolePermissionDelete(
  req: UserPermissionY.user.IDbRolePermission,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = UserPermissionY.user.DbRolePermission.encode(req)
  let reqData = w.finish()

  const api = '/user.RpcDbRolePermission/Delete'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function RpcDbRolePermissionSelectOne(
  req: CrudY.crud.IDMLParam,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = CrudY.crud.DMLParam.encode(req)
  let reqData = w.finish()

  const api = '/user.RpcDbRolePermission/SelectOne'
  const v = 0

  return rpcCon.UnaryCall(
    reqData,
    api,
    v,
    UserPermissionY.user.DbRolePermission,
    callOpt,
  )
}

export function RpcDbRolePermissionSelectMany(
  req: CrudY.crud.DMLParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/user.RpcDbRolePermission/SelectMany'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.DMLParam,
    UserPermissionY.user.DbRolePermission,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function RpcDbRolePermissionQuery(
  req: CrudY.crud.QueryParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/user.RpcDbRolePermission/Query'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.QueryParam,
    UserPermissionY.user.DbRolePermission,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function RpcDbRolePermissionQueryBatch(
  req: CrudY.crud.QueryParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/user.RpcDbRolePermission/QueryBatch'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.QueryParam,
    UserPermissionListY.user.DbRolePermissionList,
    callOpt,
  )
  r.sendFirst(req)
  return r
}

export class RpcDbRolePermission {
  public static Insert(
    req: UserPermissionY.user.IDbRolePermission,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbRolePermissionInsert(req, callOpt)
  }
  public static Update(
    req: UserPermissionY.user.IDbRolePermission,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbRolePermissionUpdate(req, callOpt)
  }
  public static PartialUpdate(
    req: UserPermissionY.user.IDbRolePermission,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbRolePermissionPartialUpdate(req, callOpt)
  }
  public static Delete(
    req: UserPermissionY.user.IDbRolePermission,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbRolePermissionDelete(req, callOpt)
  }
  public static SelectOne(
    req: CrudY.crud.IDMLParam,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbRolePermissionSelectOne(req, callOpt)
  }
  public static SelectMany(
    req: CrudY.crud.DMLParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return RpcDbRolePermissionSelectMany(req, callOpt)
  }
  public static Query(
    req: CrudY.crud.QueryParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return RpcDbRolePermissionQuery(req, callOpt)
  }
  public static QueryBatch(
    req: CrudY.crud.QueryParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return RpcDbRolePermissionQueryBatch(req, callOpt)
  }
}

export function PrpcDbRolePermissionInsert(
  req: UserPermissionY.user.IDbRolePermission,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = UserPermissionY.user.DbRolePermission.encode(req)
  let reqData = w.finish()

  const api = '/user.PrpcDbRolePermission/Insert'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function PrpcDbRolePermissionUpdate(
  req: UserPermissionY.user.IDbRolePermission,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = UserPermissionY.user.DbRolePermission.encode(req)
  let reqData = w.finish()

  const api = '/user.PrpcDbRolePermission/Update'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function PrpcDbRolePermissionPartialUpdate(
  req: UserPermissionY.user.IDbRolePermission,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = UserPermissionY.user.DbRolePermission.encode(req)
  let reqData = w.finish()

  const api = '/user.PrpcDbRolePermission/PartialUpdate'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function PrpcDbRolePermissionDelete(
  req: UserPermissionY.user.IDbRolePermission,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = UserPermissionY.user.DbRolePermission.encode(req)
  let reqData = w.finish()

  const api = '/user.PrpcDbRolePermission/Delete'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function PrpcDbRolePermissionSelectOne(
  req: CrudY.crud.IDMLParam,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = CrudY.crud.DMLParam.encode(req)
  let reqData = w.finish()

  const api = '/user.PrpcDbRolePermission/SelectOne'
  const v = 0

  return rpcCon.UnaryCall(
    reqData,
    api,
    v,
    UserPermissionY.user.DbRolePermission,
    callOpt,
  )
}

export function PrpcDbRolePermissionSelectMany(
  req: CrudY.crud.DMLParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/user.PrpcDbRolePermission/SelectMany'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.DMLParam,
    UserPermissionY.user.DbRolePermission,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function PrpcDbRolePermissionQuery(
  req: CrudY.crud.PrivilegeParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/user.PrpcDbRolePermission/Query'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.PrivilegeParam,
    UserPermissionY.user.DbRolePermission,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function PrpcDbRolePermissionQueryBatch(
  req: CrudY.crud.PrivilegeParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/user.PrpcDbRolePermission/QueryBatch'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.PrivilegeParam,
    UserPermissionListY.user.DbRolePermissionList,
    callOpt,
  )
  r.sendFirst(req)
  return r
}

export class PrpcDbRolePermission {
  public static Insert(
    req: UserPermissionY.user.IDbRolePermission,
    callOpt?: ICallOption,
  ): boolean {
    return PrpcDbRolePermissionInsert(req, callOpt)
  }
  public static Update(
    req: UserPermissionY.user.IDbRolePermission,
    callOpt?: ICallOption,
  ): boolean {
    return PrpcDbRolePermissionUpdate(req, callOpt)
  }
  public static PartialUpdate(
    req: UserPermissionY.user.IDbRolePermission,
    callOpt?: ICallOption,
  ): boolean {
    return PrpcDbRolePermissionPartialUpdate(req, callOpt)
  }
  public static Delete(
    req: UserPermissionY.user.IDbRolePermission,
    callOpt?: ICallOption,
  ): boolean {
    return PrpcDbRolePermissionDelete(req, callOpt)
  }
  public static SelectOne(
    req: CrudY.crud.IDMLParam,
    callOpt?: ICallOption,
  ): boolean {
    return PrpcDbRolePermissionSelectOne(req, callOpt)
  }
  public static SelectMany(
    req: CrudY.crud.DMLParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return PrpcDbRolePermissionSelectMany(req, callOpt)
  }
  public static Query(
    req: CrudY.crud.PrivilegeParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return PrpcDbRolePermissionQuery(req, callOpt)
  }
  public static QueryBatch(
    req: CrudY.crud.PrivilegeParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return PrpcDbRolePermissionQueryBatch(req, callOpt)
  }
}
export default {
  RpcDbPermission,
  RpcDbPermissionInsert,
  RpcDbPermissionUpdate,
  RpcDbPermissionPartialUpdate,
  RpcDbPermissionDelete,
  RpcDbPermissionSelectOne,
  RpcDbPermissionSelectMany,
  RpcDbPermissionQuery,
  RpcDbPermissionQueryBatch,
  RpcDbRole,
  RpcDbRoleInsert,
  RpcDbRoleUpdate,
  RpcDbRolePartialUpdate,
  RpcDbRoleDelete,
  RpcDbRoleSelectOne,
  RpcDbRoleSelectMany,
  RpcDbRoleQuery,
  RpcDbRoleQueryBatch,
  PrpcDbRole,
  PrpcDbRoleInsert,
  PrpcDbRoleUpdate,
  PrpcDbRolePartialUpdate,
  PrpcDbRoleDelete,
  PrpcDbRoleSelectOne,
  PrpcDbRoleSelectMany,
  PrpcDbRoleQuery,
  PrpcDbRoleQueryBatch,
  RpcDbRolePermission,
  RpcDbRolePermissionInsert,
  RpcDbRolePermissionUpdate,
  RpcDbRolePermissionPartialUpdate,
  RpcDbRolePermissionDelete,
  RpcDbRolePermissionSelectOne,
  RpcDbRolePermissionSelectMany,
  RpcDbRolePermissionQuery,
  RpcDbRolePermissionQueryBatch,
  PrpcDbRolePermission,
  PrpcDbRolePermissionInsert,
  PrpcDbRolePermissionUpdate,
  PrpcDbRolePermissionPartialUpdate,
  PrpcDbRolePermissionDelete,
  PrpcDbRolePermissionSelectOne,
  PrpcDbRolePermissionSelectMany,
  PrpcDbRolePermissionQuery,
  PrpcDbRolePermissionQueryBatch,
}

import * as $protobuf from 'protobufjs'
/** Namespace user. */
export namespace user {
  /** Properties of a DbUserOrgPrivilege. */
  interface IDbUserOrgPrivilege {
    /**
     * @db uuid primary key
     * 行ID
     */
    RID?: string | null

    /**
     * @db uuid not null REFERENCES DbUser(RID) ON DELETE CASCADE
     * 用户rid
     */
    UserRID?: string | null

    /**
     * @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 有权限的群组
     */
    OrgRID?: string | null

    /**
     * @db int default 0
     * 是否包含下级群组
     */
    IncludeChildren?: number | null

    /**
     * @db jsonb not null default  '{}'::jsonb
     * 其它设置，可以在里面扩展需要用到的其它信息
     */
    Setting?: string | null

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     */
    UpdatedAt?: string | null

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */
    UpdatedDC?: string | null
  }

  /** 用户群组权限表 */
  class DbUserOrgPrivilege implements IDbUserOrgPrivilege {
    /**
     * Constructs a new DbUserOrgPrivilege.
     * @rpc crud pcrud
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbUserOrgPrivilegeUserRID on DbUserOrgPrivilege USING hash(UserRID);
     * @dbpost INSERT INTO dbuserorgprivilege(rid, userrid, orgrid, includechildren, setting, updatedat, updateddc)VALUES('00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', 1, '{}'::jsonb, now_utc(), '') ON CONFLICT  DO NOTHING;
     * @param [properties] Properties to set
     */
    constructor(properties?: user.IDbUserOrgPrivilege)

    /**
     * @db uuid primary key
     * 行ID
     */
    public RID: string

    /**
     * @db uuid not null REFERENCES DbUser(RID) ON DELETE CASCADE
     * 用户rid
     */
    public UserRID: string

    /**
     * @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 有权限的群组
     */
    public OrgRID: string

    /**
     * @db int default 0
     * 是否包含下级群组
     */
    public IncludeChildren: number

    /**
     * @db jsonb not null default  '{}'::jsonb
     * 其它设置，可以在里面扩展需要用到的其它信息
     */
    public Setting: string

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     */
    public UpdatedAt: string

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */
    public UpdatedDC: string

    /**
     * Encodes the specified DbUserOrgPrivilege message. Does not implicitly {@link user.DbUserOrgPrivilege.verify|verify} messages.
     * @param message DbUserOrgPrivilege message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: user.IDbUserOrgPrivilege,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a DbUserOrgPrivilege message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns DbUserOrgPrivilege
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): user.DbUserOrgPrivilege
  }
}

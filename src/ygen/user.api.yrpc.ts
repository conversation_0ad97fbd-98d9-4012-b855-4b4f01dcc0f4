import { Writer } from 'protobufjs'
import { rpcCon } from 'jsyrpc'
import { ICallOption } from 'jsyrpc'

import { TRpcStream } from 'jsyrpc'
import * as UserApiY from '@ygen/user.api'
import * as RpcY from '@ygen/rpc'
import * as OrgY from '@ygen/org'
import * as UserPermissionY from '@ygen/userPermission'
import * as UserY from '@ygen/user'
import * as CrudY from '@ygen/crud'

export function RpcUserLogin(
  req: UserApiY.user.IReqUserLogin,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = UserApiY.user.ReqUserLogin.encode(req)
  let reqData = w.finish()

  const api = '/user.RpcUser/Login'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, UserApiY.user.ResUserLogin, callOpt)
}
export function RpcUserIsUserHasOrgPrivilege(
  req: UserApiY.user.IReqUserHasOrgPrivilege,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = UserApiY.user.ReqUserHasOrgPrivilege.encode(req)
  let reqData = w.finish()

  const api = '/user.RpcUser/IsUserHasOrgPrivilege'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, RpcY.rpc.RpcCommon, callOpt)
}
export function RpcUserIsUserHasPermission(
  req: UserApiY.user.IReqUserHasPermission,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = UserApiY.user.ReqUserHasPermission.encode(req)
  let reqData = w.finish()

  const api = '/user.RpcUser/IsUserHasPermission'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, RpcY.rpc.RpcCommon, callOpt)
}
export function RpcUserUpdateMySetting(
  req: UserY.user.IDbUser,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = UserY.user.DbUser.encode(req)
  let reqData = w.finish()

  const api = '/user.RpcUser/UpdateMySetting'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function RpcUserSysUTCTime(
  req: RpcY.rpc.IRpcCommon,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = RpcY.rpc.RpcCommon.encode(req)
  let reqData = w.finish()

  const api = '/user.RpcUser/SysUTCTime'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, RpcY.rpc.RpcCommon, callOpt)
}

export function RpcUserMyOrgRID(
  req: RpcY.rpc.RpcCommon,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/user.RpcUser/MyOrgRID'
  const v = 0
  let r = new TRpcStream(api, v, 7, RpcY.rpc.RpcCommon, OrgY.org.DbOrg, callOpt)
  r.sendFirst(req)
  return r
}
export function RpcUserMyPermission(
  req: RpcY.rpc.RpcCommon,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/user.RpcUser/MyPermission'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    RpcY.rpc.RpcCommon,
    UserPermissionY.user.DbPermission,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function RpcUserMyRole(
  req: RpcY.rpc.RpcCommon,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/user.RpcUser/MyRole'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    RpcY.rpc.RpcCommon,
    UserPermissionY.user.DbRole,
    callOpt,
  )
  r.sendFirst(req)
  return r
}

export class RpcUser {
  public static Login(
    req: UserApiY.user.IReqUserLogin,
    callOpt?: ICallOption,
  ): boolean {
    return RpcUserLogin(req, callOpt)
  }
  public static IsUserHasOrgPrivilege(
    req: UserApiY.user.IReqUserHasOrgPrivilege,
    callOpt?: ICallOption,
  ): boolean {
    return RpcUserIsUserHasOrgPrivilege(req, callOpt)
  }
  public static IsUserHasPermission(
    req: UserApiY.user.IReqUserHasPermission,
    callOpt?: ICallOption,
  ): boolean {
    return RpcUserIsUserHasPermission(req, callOpt)
  }
  public static UpdateMySetting(
    req: UserY.user.IDbUser,
    callOpt?: ICallOption,
  ): boolean {
    return RpcUserUpdateMySetting(req, callOpt)
  }
  public static SysUTCTime(
    req: RpcY.rpc.IRpcCommon,
    callOpt?: ICallOption,
  ): boolean {
    return RpcUserSysUTCTime(req, callOpt)
  }
  public static MyOrgRID(
    req: RpcY.rpc.RpcCommon,
    callOpt?: ICallOption,
  ): TRpcStream {
    return RpcUserMyOrgRID(req, callOpt)
  }
  public static MyPermission(
    req: RpcY.rpc.RpcCommon,
    callOpt?: ICallOption,
  ): TRpcStream {
    return RpcUserMyPermission(req, callOpt)
  }
  public static MyRole(
    req: RpcY.rpc.RpcCommon,
    callOpt?: ICallOption,
  ): TRpcStream {
    return RpcUserMyRole(req, callOpt)
  }
}
export default {
  RpcUser,
  RpcUserLogin,
  RpcUserIsUserHasOrgPrivilege,
  RpcUserIsUserHasPermission,
  RpcUserUpdateMySetting,
  RpcUserSysUTCTime,
  RpcUserMyOrgRID,
  RpcUserMyPermission,
  RpcUserMyRole,
}

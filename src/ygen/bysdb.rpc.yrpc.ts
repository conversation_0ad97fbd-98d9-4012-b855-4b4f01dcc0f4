import { Writer } from 'protobufjs'
import { rpcCon } from 'jsyrpc'
import { ICallOption } from 'jsyrpc'

import { TRpcStream } from 'jsyrpc'
import * as BysdbListY from '@ygen/bysdb.list'
import * as BysdbY from '@ygen/bysdb'
import * as CrudY from '@ygen/crud'

export function RpcDbControllerInsert(
  req: BysdbY.bysdb.IDbController,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysdbY.bysdb.DbController.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.RpcDbController/Insert'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function RpcDbControllerUpdate(
  req: BysdbY.bysdb.IDbController,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysdbY.bysdb.DbController.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.RpcDbController/Update'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function RpcDbControllerPartialUpdate(
  req: BysdbY.bysdb.IDbController,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysdbY.bysdb.DbController.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.RpcDbController/PartialUpdate'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function RpcDbControllerDelete(
  req: BysdbY.bysdb.IDbController,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysdbY.bysdb.DbController.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.RpcDbController/Delete'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function RpcDbControllerSelectOne(
  req: CrudY.crud.IDMLParam,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = CrudY.crud.DMLParam.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.RpcDbController/SelectOne'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, BysdbY.bysdb.DbController, callOpt)
}

export function RpcDbControllerSelectMany(
  req: CrudY.crud.DMLParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/bysdb.RpcDbController/SelectMany'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.DMLParam,
    BysdbY.bysdb.DbController,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function RpcDbControllerQuery(
  req: CrudY.crud.QueryParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/bysdb.RpcDbController/Query'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.QueryParam,
    BysdbY.bysdb.DbController,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function RpcDbControllerQueryBatch(
  req: CrudY.crud.QueryParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/bysdb.RpcDbController/QueryBatch'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.QueryParam,
    BysdbListY.bysdb.DbControllerList,
    callOpt,
  )
  r.sendFirst(req)
  return r
}

export class RpcDbController {
  public static Insert(
    req: BysdbY.bysdb.IDbController,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbControllerInsert(req, callOpt)
  }
  public static Update(
    req: BysdbY.bysdb.IDbController,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbControllerUpdate(req, callOpt)
  }
  public static PartialUpdate(
    req: BysdbY.bysdb.IDbController,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbControllerPartialUpdate(req, callOpt)
  }
  public static Delete(
    req: BysdbY.bysdb.IDbController,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbControllerDelete(req, callOpt)
  }
  public static SelectOne(
    req: CrudY.crud.IDMLParam,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbControllerSelectOne(req, callOpt)
  }
  public static SelectMany(
    req: CrudY.crud.DMLParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return RpcDbControllerSelectMany(req, callOpt)
  }
  public static Query(
    req: CrudY.crud.QueryParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return RpcDbControllerQuery(req, callOpt)
  }
  public static QueryBatch(
    req: CrudY.crud.QueryParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return RpcDbControllerQueryBatch(req, callOpt)
  }
}

export function PrpcDbControllerInsert(
  req: BysdbY.bysdb.IDbController,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysdbY.bysdb.DbController.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.PrpcDbController/Insert'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function PrpcDbControllerUpdate(
  req: BysdbY.bysdb.IDbController,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysdbY.bysdb.DbController.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.PrpcDbController/Update'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function PrpcDbControllerPartialUpdate(
  req: BysdbY.bysdb.IDbController,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysdbY.bysdb.DbController.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.PrpcDbController/PartialUpdate'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function PrpcDbControllerDelete(
  req: BysdbY.bysdb.IDbController,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysdbY.bysdb.DbController.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.PrpcDbController/Delete'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function PrpcDbControllerSelectOne(
  req: CrudY.crud.IDMLParam,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = CrudY.crud.DMLParam.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.PrpcDbController/SelectOne'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, BysdbY.bysdb.DbController, callOpt)
}

export function PrpcDbControllerSelectMany(
  req: CrudY.crud.DMLParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/bysdb.PrpcDbController/SelectMany'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.DMLParam,
    BysdbY.bysdb.DbController,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function PrpcDbControllerQuery(
  req: CrudY.crud.PrivilegeParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/bysdb.PrpcDbController/Query'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.PrivilegeParam,
    BysdbY.bysdb.DbController,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function PrpcDbControllerQueryBatch(
  req: CrudY.crud.PrivilegeParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/bysdb.PrpcDbController/QueryBatch'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.PrivilegeParam,
    BysdbListY.bysdb.DbControllerList,
    callOpt,
  )
  r.sendFirst(req)
  return r
}

export class PrpcDbController {
  public static Insert(
    req: BysdbY.bysdb.IDbController,
    callOpt?: ICallOption,
  ): boolean {
    return PrpcDbControllerInsert(req, callOpt)
  }
  public static Update(
    req: BysdbY.bysdb.IDbController,
    callOpt?: ICallOption,
  ): boolean {
    return PrpcDbControllerUpdate(req, callOpt)
  }
  public static PartialUpdate(
    req: BysdbY.bysdb.IDbController,
    callOpt?: ICallOption,
  ): boolean {
    return PrpcDbControllerPartialUpdate(req, callOpt)
  }
  public static Delete(
    req: BysdbY.bysdb.IDbController,
    callOpt?: ICallOption,
  ): boolean {
    return PrpcDbControllerDelete(req, callOpt)
  }
  public static SelectOne(
    req: CrudY.crud.IDMLParam,
    callOpt?: ICallOption,
  ): boolean {
    return PrpcDbControllerSelectOne(req, callOpt)
  }
  public static SelectMany(
    req: CrudY.crud.DMLParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return PrpcDbControllerSelectMany(req, callOpt)
  }
  public static Query(
    req: CrudY.crud.PrivilegeParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return PrpcDbControllerQuery(req, callOpt)
  }
  public static QueryBatch(
    req: CrudY.crud.PrivilegeParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return PrpcDbControllerQueryBatch(req, callOpt)
  }
}

export function RpcDbBysMarkerInsert(
  req: BysdbY.bysdb.IDbBysMarker,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysdbY.bysdb.DbBysMarker.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.RpcDbBysMarker/Insert'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function RpcDbBysMarkerUpdate(
  req: BysdbY.bysdb.IDbBysMarker,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysdbY.bysdb.DbBysMarker.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.RpcDbBysMarker/Update'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function RpcDbBysMarkerPartialUpdate(
  req: BysdbY.bysdb.IDbBysMarker,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysdbY.bysdb.DbBysMarker.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.RpcDbBysMarker/PartialUpdate'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function RpcDbBysMarkerDelete(
  req: BysdbY.bysdb.IDbBysMarker,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysdbY.bysdb.DbBysMarker.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.RpcDbBysMarker/Delete'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function RpcDbBysMarkerSelectOne(
  req: CrudY.crud.IDMLParam,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = CrudY.crud.DMLParam.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.RpcDbBysMarker/SelectOne'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, BysdbY.bysdb.DbBysMarker, callOpt)
}

export function RpcDbBysMarkerSelectMany(
  req: CrudY.crud.DMLParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/bysdb.RpcDbBysMarker/SelectMany'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.DMLParam,
    BysdbY.bysdb.DbBysMarker,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function RpcDbBysMarkerQuery(
  req: CrudY.crud.QueryParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/bysdb.RpcDbBysMarker/Query'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.QueryParam,
    BysdbY.bysdb.DbBysMarker,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function RpcDbBysMarkerQueryBatch(
  req: CrudY.crud.QueryParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/bysdb.RpcDbBysMarker/QueryBatch'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.QueryParam,
    BysdbListY.bysdb.DbBysMarkerList,
    callOpt,
  )
  r.sendFirst(req)
  return r
}

export class RpcDbBysMarker {
  public static Insert(
    req: BysdbY.bysdb.IDbBysMarker,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbBysMarkerInsert(req, callOpt)
  }
  public static Update(
    req: BysdbY.bysdb.IDbBysMarker,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbBysMarkerUpdate(req, callOpt)
  }
  public static PartialUpdate(
    req: BysdbY.bysdb.IDbBysMarker,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbBysMarkerPartialUpdate(req, callOpt)
  }
  public static Delete(
    req: BysdbY.bysdb.IDbBysMarker,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbBysMarkerDelete(req, callOpt)
  }
  public static SelectOne(
    req: CrudY.crud.IDMLParam,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbBysMarkerSelectOne(req, callOpt)
  }
  public static SelectMany(
    req: CrudY.crud.DMLParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return RpcDbBysMarkerSelectMany(req, callOpt)
  }
  public static Query(
    req: CrudY.crud.QueryParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return RpcDbBysMarkerQuery(req, callOpt)
  }
  public static QueryBatch(
    req: CrudY.crud.QueryParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return RpcDbBysMarkerQueryBatch(req, callOpt)
  }
}

export function PrpcDbBysMarkerInsert(
  req: BysdbY.bysdb.IDbBysMarker,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysdbY.bysdb.DbBysMarker.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.PrpcDbBysMarker/Insert'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function PrpcDbBysMarkerUpdate(
  req: BysdbY.bysdb.IDbBysMarker,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysdbY.bysdb.DbBysMarker.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.PrpcDbBysMarker/Update'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function PrpcDbBysMarkerPartialUpdate(
  req: BysdbY.bysdb.IDbBysMarker,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysdbY.bysdb.DbBysMarker.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.PrpcDbBysMarker/PartialUpdate'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function PrpcDbBysMarkerDelete(
  req: BysdbY.bysdb.IDbBysMarker,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysdbY.bysdb.DbBysMarker.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.PrpcDbBysMarker/Delete'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function PrpcDbBysMarkerSelectOne(
  req: CrudY.crud.IDMLParam,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = CrudY.crud.DMLParam.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.PrpcDbBysMarker/SelectOne'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, BysdbY.bysdb.DbBysMarker, callOpt)
}

export function PrpcDbBysMarkerSelectMany(
  req: CrudY.crud.DMLParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/bysdb.PrpcDbBysMarker/SelectMany'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.DMLParam,
    BysdbY.bysdb.DbBysMarker,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function PrpcDbBysMarkerQuery(
  req: CrudY.crud.PrivilegeParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/bysdb.PrpcDbBysMarker/Query'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.PrivilegeParam,
    BysdbY.bysdb.DbBysMarker,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function PrpcDbBysMarkerQueryBatch(
  req: CrudY.crud.PrivilegeParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/bysdb.PrpcDbBysMarker/QueryBatch'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.PrivilegeParam,
    BysdbListY.bysdb.DbBysMarkerList,
    callOpt,
  )
  r.sendFirst(req)
  return r
}

export class PrpcDbBysMarker {
  public static Insert(
    req: BysdbY.bysdb.IDbBysMarker,
    callOpt?: ICallOption,
  ): boolean {
    return PrpcDbBysMarkerInsert(req, callOpt)
  }
  public static Update(
    req: BysdbY.bysdb.IDbBysMarker,
    callOpt?: ICallOption,
  ): boolean {
    return PrpcDbBysMarkerUpdate(req, callOpt)
  }
  public static PartialUpdate(
    req: BysdbY.bysdb.IDbBysMarker,
    callOpt?: ICallOption,
  ): boolean {
    return PrpcDbBysMarkerPartialUpdate(req, callOpt)
  }
  public static Delete(
    req: BysdbY.bysdb.IDbBysMarker,
    callOpt?: ICallOption,
  ): boolean {
    return PrpcDbBysMarkerDelete(req, callOpt)
  }
  public static SelectOne(
    req: CrudY.crud.IDMLParam,
    callOpt?: ICallOption,
  ): boolean {
    return PrpcDbBysMarkerSelectOne(req, callOpt)
  }
  public static SelectMany(
    req: CrudY.crud.DMLParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return PrpcDbBysMarkerSelectMany(req, callOpt)
  }
  public static Query(
    req: CrudY.crud.PrivilegeParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return PrpcDbBysMarkerQuery(req, callOpt)
  }
  public static QueryBatch(
    req: CrudY.crud.PrivilegeParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return PrpcDbBysMarkerQueryBatch(req, callOpt)
  }
}

export function RpcDbControllerOnlineHistoryInsert(
  req: BysdbY.bysdb.IDbControllerOnlineHistory,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysdbY.bysdb.DbControllerOnlineHistory.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.RpcDbControllerOnlineHistory/Insert'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}

export function RpcDbControllerOnlineHistoryQuery(
  req: CrudY.crud.QueryParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/bysdb.RpcDbControllerOnlineHistory/Query'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.QueryParam,
    BysdbY.bysdb.DbControllerOnlineHistory,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function RpcDbControllerOnlineHistoryQueryBatch(
  req: CrudY.crud.QueryParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/bysdb.RpcDbControllerOnlineHistory/QueryBatch'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.QueryParam,
    BysdbListY.bysdb.DbControllerOnlineHistoryList,
    callOpt,
  )
  r.sendFirst(req)
  return r
}

export class RpcDbControllerOnlineHistory {
  public static Insert(
    req: BysdbY.bysdb.IDbControllerOnlineHistory,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbControllerOnlineHistoryInsert(req, callOpt)
  }
  public static Query(
    req: CrudY.crud.QueryParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return RpcDbControllerOnlineHistoryQuery(req, callOpt)
  }
  public static QueryBatch(
    req: CrudY.crud.QueryParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return RpcDbControllerOnlineHistoryQueryBatch(req, callOpt)
  }
}

export function PrpcDbControllerOnlineHistoryInsert(
  req: BysdbY.bysdb.IDbControllerOnlineHistory,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysdbY.bysdb.DbControllerOnlineHistory.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.PrpcDbControllerOnlineHistory/Insert'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}

export function PrpcDbControllerOnlineHistoryQuery(
  req: CrudY.crud.PrivilegeParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/bysdb.PrpcDbControllerOnlineHistory/Query'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.PrivilegeParam,
    BysdbY.bysdb.DbControllerOnlineHistory,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function PrpcDbControllerOnlineHistoryQueryBatch(
  req: CrudY.crud.PrivilegeParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/bysdb.PrpcDbControllerOnlineHistory/QueryBatch'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.PrivilegeParam,
    BysdbListY.bysdb.DbControllerOnlineHistoryList,
    callOpt,
  )
  r.sendFirst(req)
  return r
}

export class PrpcDbControllerOnlineHistory {
  public static Insert(
    req: BysdbY.bysdb.IDbControllerOnlineHistory,
    callOpt?: ICallOption,
  ): boolean {
    return PrpcDbControllerOnlineHistoryInsert(req, callOpt)
  }
  public static Query(
    req: CrudY.crud.PrivilegeParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return PrpcDbControllerOnlineHistoryQuery(req, callOpt)
  }
  public static QueryBatch(
    req: CrudY.crud.PrivilegeParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return PrpcDbControllerOnlineHistoryQueryBatch(req, callOpt)
  }
}

export function RpcDbMediaInfoInsert(
  req: BysdbY.bysdb.IDbMediaInfo,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysdbY.bysdb.DbMediaInfo.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.RpcDbMediaInfo/Insert'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function RpcDbMediaInfoUpdate(
  req: BysdbY.bysdb.IDbMediaInfo,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysdbY.bysdb.DbMediaInfo.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.RpcDbMediaInfo/Update'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function RpcDbMediaInfoPartialUpdate(
  req: BysdbY.bysdb.IDbMediaInfo,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysdbY.bysdb.DbMediaInfo.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.RpcDbMediaInfo/PartialUpdate'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function RpcDbMediaInfoDelete(
  req: BysdbY.bysdb.IDbMediaInfo,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysdbY.bysdb.DbMediaInfo.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.RpcDbMediaInfo/Delete'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function RpcDbMediaInfoSelectOne(
  req: CrudY.crud.IDMLParam,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = CrudY.crud.DMLParam.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.RpcDbMediaInfo/SelectOne'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, BysdbY.bysdb.DbMediaInfo, callOpt)
}

export function RpcDbMediaInfoSelectMany(
  req: CrudY.crud.DMLParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/bysdb.RpcDbMediaInfo/SelectMany'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.DMLParam,
    BysdbY.bysdb.DbMediaInfo,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function RpcDbMediaInfoQuery(
  req: CrudY.crud.QueryParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/bysdb.RpcDbMediaInfo/Query'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.QueryParam,
    BysdbY.bysdb.DbMediaInfo,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function RpcDbMediaInfoQueryBatch(
  req: CrudY.crud.QueryParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/bysdb.RpcDbMediaInfo/QueryBatch'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.QueryParam,
    BysdbListY.bysdb.DbMediaInfoList,
    callOpt,
  )
  r.sendFirst(req)
  return r
}

export class RpcDbMediaInfo {
  public static Insert(
    req: BysdbY.bysdb.IDbMediaInfo,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbMediaInfoInsert(req, callOpt)
  }
  public static Update(
    req: BysdbY.bysdb.IDbMediaInfo,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbMediaInfoUpdate(req, callOpt)
  }
  public static PartialUpdate(
    req: BysdbY.bysdb.IDbMediaInfo,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbMediaInfoPartialUpdate(req, callOpt)
  }
  public static Delete(
    req: BysdbY.bysdb.IDbMediaInfo,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbMediaInfoDelete(req, callOpt)
  }
  public static SelectOne(
    req: CrudY.crud.IDMLParam,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbMediaInfoSelectOne(req, callOpt)
  }
  public static SelectMany(
    req: CrudY.crud.DMLParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return RpcDbMediaInfoSelectMany(req, callOpt)
  }
  public static Query(
    req: CrudY.crud.QueryParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return RpcDbMediaInfoQuery(req, callOpt)
  }
  public static QueryBatch(
    req: CrudY.crud.QueryParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return RpcDbMediaInfoQueryBatch(req, callOpt)
  }
}

export function PrpcDbMediaInfoInsert(
  req: BysdbY.bysdb.IDbMediaInfo,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysdbY.bysdb.DbMediaInfo.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.PrpcDbMediaInfo/Insert'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function PrpcDbMediaInfoUpdate(
  req: BysdbY.bysdb.IDbMediaInfo,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysdbY.bysdb.DbMediaInfo.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.PrpcDbMediaInfo/Update'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function PrpcDbMediaInfoPartialUpdate(
  req: BysdbY.bysdb.IDbMediaInfo,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysdbY.bysdb.DbMediaInfo.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.PrpcDbMediaInfo/PartialUpdate'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function PrpcDbMediaInfoDelete(
  req: BysdbY.bysdb.IDbMediaInfo,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysdbY.bysdb.DbMediaInfo.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.PrpcDbMediaInfo/Delete'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function PrpcDbMediaInfoSelectOne(
  req: CrudY.crud.IDMLParam,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = CrudY.crud.DMLParam.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.PrpcDbMediaInfo/SelectOne'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, BysdbY.bysdb.DbMediaInfo, callOpt)
}

export function PrpcDbMediaInfoSelectMany(
  req: CrudY.crud.DMLParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/bysdb.PrpcDbMediaInfo/SelectMany'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.DMLParam,
    BysdbY.bysdb.DbMediaInfo,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function PrpcDbMediaInfoQuery(
  req: CrudY.crud.PrivilegeParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/bysdb.PrpcDbMediaInfo/Query'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.PrivilegeParam,
    BysdbY.bysdb.DbMediaInfo,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function PrpcDbMediaInfoQueryBatch(
  req: CrudY.crud.PrivilegeParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/bysdb.PrpcDbMediaInfo/QueryBatch'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.PrivilegeParam,
    BysdbListY.bysdb.DbMediaInfoList,
    callOpt,
  )
  r.sendFirst(req)
  return r
}

export class PrpcDbMediaInfo {
  public static Insert(
    req: BysdbY.bysdb.IDbMediaInfo,
    callOpt?: ICallOption,
  ): boolean {
    return PrpcDbMediaInfoInsert(req, callOpt)
  }
  public static Update(
    req: BysdbY.bysdb.IDbMediaInfo,
    callOpt?: ICallOption,
  ): boolean {
    return PrpcDbMediaInfoUpdate(req, callOpt)
  }
  public static PartialUpdate(
    req: BysdbY.bysdb.IDbMediaInfo,
    callOpt?: ICallOption,
  ): boolean {
    return PrpcDbMediaInfoPartialUpdate(req, callOpt)
  }
  public static Delete(
    req: BysdbY.bysdb.IDbMediaInfo,
    callOpt?: ICallOption,
  ): boolean {
    return PrpcDbMediaInfoDelete(req, callOpt)
  }
  public static SelectOne(
    req: CrudY.crud.IDMLParam,
    callOpt?: ICallOption,
  ): boolean {
    return PrpcDbMediaInfoSelectOne(req, callOpt)
  }
  public static SelectMany(
    req: CrudY.crud.DMLParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return PrpcDbMediaInfoSelectMany(req, callOpt)
  }
  public static Query(
    req: CrudY.crud.PrivilegeParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return PrpcDbMediaInfoQuery(req, callOpt)
  }
  public static QueryBatch(
    req: CrudY.crud.PrivilegeParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return PrpcDbMediaInfoQueryBatch(req, callOpt)
  }
}

export function RpcDbMarkerHistoryInsert(
  req: BysdbY.bysdb.IDbMarkerHistory,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysdbY.bysdb.DbMarkerHistory.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.RpcDbMarkerHistory/Insert'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}

export function RpcDbMarkerHistoryQuery(
  req: CrudY.crud.QueryParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/bysdb.RpcDbMarkerHistory/Query'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.QueryParam,
    BysdbY.bysdb.DbMarkerHistory,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function RpcDbMarkerHistoryQueryBatch(
  req: CrudY.crud.QueryParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/bysdb.RpcDbMarkerHistory/QueryBatch'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.QueryParam,
    BysdbListY.bysdb.DbMarkerHistoryList,
    callOpt,
  )
  r.sendFirst(req)
  return r
}

export class RpcDbMarkerHistory {
  public static Insert(
    req: BysdbY.bysdb.IDbMarkerHistory,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbMarkerHistoryInsert(req, callOpt)
  }
  public static Query(
    req: CrudY.crud.QueryParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return RpcDbMarkerHistoryQuery(req, callOpt)
  }
  public static QueryBatch(
    req: CrudY.crud.QueryParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return RpcDbMarkerHistoryQueryBatch(req, callOpt)
  }
}

export function PrpcDbMarkerHistoryInsert(
  req: BysdbY.bysdb.IDbMarkerHistory,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysdbY.bysdb.DbMarkerHistory.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.PrpcDbMarkerHistory/Insert'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}

export function PrpcDbMarkerHistoryQuery(
  req: CrudY.crud.PrivilegeParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/bysdb.PrpcDbMarkerHistory/Query'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.PrivilegeParam,
    BysdbY.bysdb.DbMarkerHistory,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function PrpcDbMarkerHistoryQueryBatch(
  req: CrudY.crud.PrivilegeParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/bysdb.PrpcDbMarkerHistory/QueryBatch'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.PrivilegeParam,
    BysdbListY.bysdb.DbMarkerHistoryList,
    callOpt,
  )
  r.sendFirst(req)
  return r
}

export class PrpcDbMarkerHistory {
  public static Insert(
    req: BysdbY.bysdb.IDbMarkerHistory,
    callOpt?: ICallOption,
  ): boolean {
    return PrpcDbMarkerHistoryInsert(req, callOpt)
  }
  public static Query(
    req: CrudY.crud.PrivilegeParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return PrpcDbMarkerHistoryQuery(req, callOpt)
  }
  public static QueryBatch(
    req: CrudY.crud.PrivilegeParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return PrpcDbMarkerHistoryQueryBatch(req, callOpt)
  }
}

export function RpcDbMarkerPatrolHistoryInsert(
  req: BysdbY.bysdb.IDbMarkerPatrolHistory,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysdbY.bysdb.DbMarkerPatrolHistory.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.RpcDbMarkerPatrolHistory/Insert'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}

export function RpcDbMarkerPatrolHistoryQuery(
  req: CrudY.crud.QueryParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/bysdb.RpcDbMarkerPatrolHistory/Query'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.QueryParam,
    BysdbY.bysdb.DbMarkerPatrolHistory,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function RpcDbMarkerPatrolHistoryQueryBatch(
  req: CrudY.crud.QueryParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/bysdb.RpcDbMarkerPatrolHistory/QueryBatch'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.QueryParam,
    BysdbListY.bysdb.DbMarkerPatrolHistoryList,
    callOpt,
  )
  r.sendFirst(req)
  return r
}

export class RpcDbMarkerPatrolHistory {
  public static Insert(
    req: BysdbY.bysdb.IDbMarkerPatrolHistory,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbMarkerPatrolHistoryInsert(req, callOpt)
  }
  public static Query(
    req: CrudY.crud.QueryParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return RpcDbMarkerPatrolHistoryQuery(req, callOpt)
  }
  public static QueryBatch(
    req: CrudY.crud.QueryParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return RpcDbMarkerPatrolHistoryQueryBatch(req, callOpt)
  }
}

export function PrpcDbMarkerPatrolHistoryInsert(
  req: BysdbY.bysdb.IDbMarkerPatrolHistory,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysdbY.bysdb.DbMarkerPatrolHistory.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.PrpcDbMarkerPatrolHistory/Insert'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}

export function PrpcDbMarkerPatrolHistoryQuery(
  req: CrudY.crud.PrivilegeParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/bysdb.PrpcDbMarkerPatrolHistory/Query'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.PrivilegeParam,
    BysdbY.bysdb.DbMarkerPatrolHistory,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function PrpcDbMarkerPatrolHistoryQueryBatch(
  req: CrudY.crud.PrivilegeParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/bysdb.PrpcDbMarkerPatrolHistory/QueryBatch'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.PrivilegeParam,
    BysdbListY.bysdb.DbMarkerPatrolHistoryList,
    callOpt,
  )
  r.sendFirst(req)
  return r
}

export class PrpcDbMarkerPatrolHistory {
  public static Insert(
    req: BysdbY.bysdb.IDbMarkerPatrolHistory,
    callOpt?: ICallOption,
  ): boolean {
    return PrpcDbMarkerPatrolHistoryInsert(req, callOpt)
  }
  public static Query(
    req: CrudY.crud.PrivilegeParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return PrpcDbMarkerPatrolHistoryQuery(req, callOpt)
  }
  public static QueryBatch(
    req: CrudY.crud.PrivilegeParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return PrpcDbMarkerPatrolHistoryQueryBatch(req, callOpt)
  }
}

export function RpcDbNFCPatrolLineInsert(
  req: BysdbY.bysdb.IDbNFCPatrolLine,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysdbY.bysdb.DbNFCPatrolLine.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.RpcDbNFCPatrolLine/Insert'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function RpcDbNFCPatrolLineUpdate(
  req: BysdbY.bysdb.IDbNFCPatrolLine,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysdbY.bysdb.DbNFCPatrolLine.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.RpcDbNFCPatrolLine/Update'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function RpcDbNFCPatrolLinePartialUpdate(
  req: BysdbY.bysdb.IDbNFCPatrolLine,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysdbY.bysdb.DbNFCPatrolLine.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.RpcDbNFCPatrolLine/PartialUpdate'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function RpcDbNFCPatrolLineDelete(
  req: BysdbY.bysdb.IDbNFCPatrolLine,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysdbY.bysdb.DbNFCPatrolLine.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.RpcDbNFCPatrolLine/Delete'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function RpcDbNFCPatrolLineSelectOne(
  req: CrudY.crud.IDMLParam,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = CrudY.crud.DMLParam.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.RpcDbNFCPatrolLine/SelectOne'
  const v = 0

  return rpcCon.UnaryCall(
    reqData,
    api,
    v,
    BysdbY.bysdb.DbNFCPatrolLine,
    callOpt,
  )
}

export function RpcDbNFCPatrolLineSelectMany(
  req: CrudY.crud.DMLParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/bysdb.RpcDbNFCPatrolLine/SelectMany'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.DMLParam,
    BysdbY.bysdb.DbNFCPatrolLine,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function RpcDbNFCPatrolLineQuery(
  req: CrudY.crud.QueryParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/bysdb.RpcDbNFCPatrolLine/Query'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.QueryParam,
    BysdbY.bysdb.DbNFCPatrolLine,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function RpcDbNFCPatrolLineQueryBatch(
  req: CrudY.crud.QueryParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/bysdb.RpcDbNFCPatrolLine/QueryBatch'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.QueryParam,
    BysdbListY.bysdb.DbNFCPatrolLineList,
    callOpt,
  )
  r.sendFirst(req)
  return r
}

export class RpcDbNFCPatrolLine {
  public static Insert(
    req: BysdbY.bysdb.IDbNFCPatrolLine,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbNFCPatrolLineInsert(req, callOpt)
  }
  public static Update(
    req: BysdbY.bysdb.IDbNFCPatrolLine,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbNFCPatrolLineUpdate(req, callOpt)
  }
  public static PartialUpdate(
    req: BysdbY.bysdb.IDbNFCPatrolLine,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbNFCPatrolLinePartialUpdate(req, callOpt)
  }
  public static Delete(
    req: BysdbY.bysdb.IDbNFCPatrolLine,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbNFCPatrolLineDelete(req, callOpt)
  }
  public static SelectOne(
    req: CrudY.crud.IDMLParam,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbNFCPatrolLineSelectOne(req, callOpt)
  }
  public static SelectMany(
    req: CrudY.crud.DMLParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return RpcDbNFCPatrolLineSelectMany(req, callOpt)
  }
  public static Query(
    req: CrudY.crud.QueryParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return RpcDbNFCPatrolLineQuery(req, callOpt)
  }
  public static QueryBatch(
    req: CrudY.crud.QueryParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return RpcDbNFCPatrolLineQueryBatch(req, callOpt)
  }
}

export function PrpcDbNFCPatrolLineInsert(
  req: BysdbY.bysdb.IDbNFCPatrolLine,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysdbY.bysdb.DbNFCPatrolLine.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.PrpcDbNFCPatrolLine/Insert'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function PrpcDbNFCPatrolLineUpdate(
  req: BysdbY.bysdb.IDbNFCPatrolLine,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysdbY.bysdb.DbNFCPatrolLine.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.PrpcDbNFCPatrolLine/Update'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function PrpcDbNFCPatrolLinePartialUpdate(
  req: BysdbY.bysdb.IDbNFCPatrolLine,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysdbY.bysdb.DbNFCPatrolLine.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.PrpcDbNFCPatrolLine/PartialUpdate'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function PrpcDbNFCPatrolLineDelete(
  req: BysdbY.bysdb.IDbNFCPatrolLine,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysdbY.bysdb.DbNFCPatrolLine.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.PrpcDbNFCPatrolLine/Delete'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function PrpcDbNFCPatrolLineSelectOne(
  req: CrudY.crud.IDMLParam,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = CrudY.crud.DMLParam.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.PrpcDbNFCPatrolLine/SelectOne'
  const v = 0

  return rpcCon.UnaryCall(
    reqData,
    api,
    v,
    BysdbY.bysdb.DbNFCPatrolLine,
    callOpt,
  )
}

export function PrpcDbNFCPatrolLineSelectMany(
  req: CrudY.crud.DMLParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/bysdb.PrpcDbNFCPatrolLine/SelectMany'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.DMLParam,
    BysdbY.bysdb.DbNFCPatrolLine,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function PrpcDbNFCPatrolLineQuery(
  req: CrudY.crud.PrivilegeParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/bysdb.PrpcDbNFCPatrolLine/Query'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.PrivilegeParam,
    BysdbY.bysdb.DbNFCPatrolLine,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function PrpcDbNFCPatrolLineQueryBatch(
  req: CrudY.crud.PrivilegeParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/bysdb.PrpcDbNFCPatrolLine/QueryBatch'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.PrivilegeParam,
    BysdbListY.bysdb.DbNFCPatrolLineList,
    callOpt,
  )
  r.sendFirst(req)
  return r
}

export class PrpcDbNFCPatrolLine {
  public static Insert(
    req: BysdbY.bysdb.IDbNFCPatrolLine,
    callOpt?: ICallOption,
  ): boolean {
    return PrpcDbNFCPatrolLineInsert(req, callOpt)
  }
  public static Update(
    req: BysdbY.bysdb.IDbNFCPatrolLine,
    callOpt?: ICallOption,
  ): boolean {
    return PrpcDbNFCPatrolLineUpdate(req, callOpt)
  }
  public static PartialUpdate(
    req: BysdbY.bysdb.IDbNFCPatrolLine,
    callOpt?: ICallOption,
  ): boolean {
    return PrpcDbNFCPatrolLinePartialUpdate(req, callOpt)
  }
  public static Delete(
    req: BysdbY.bysdb.IDbNFCPatrolLine,
    callOpt?: ICallOption,
  ): boolean {
    return PrpcDbNFCPatrolLineDelete(req, callOpt)
  }
  public static SelectOne(
    req: CrudY.crud.IDMLParam,
    callOpt?: ICallOption,
  ): boolean {
    return PrpcDbNFCPatrolLineSelectOne(req, callOpt)
  }
  public static SelectMany(
    req: CrudY.crud.DMLParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return PrpcDbNFCPatrolLineSelectMany(req, callOpt)
  }
  public static Query(
    req: CrudY.crud.PrivilegeParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return PrpcDbNFCPatrolLineQuery(req, callOpt)
  }
  public static QueryBatch(
    req: CrudY.crud.PrivilegeParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return PrpcDbNFCPatrolLineQueryBatch(req, callOpt)
  }
}

export function RpcDbNFCPatrolLineDetailInsert(
  req: BysdbY.bysdb.IDbNFCPatrolLineDetail,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysdbY.bysdb.DbNFCPatrolLineDetail.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.RpcDbNFCPatrolLineDetail/Insert'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function RpcDbNFCPatrolLineDetailUpdate(
  req: BysdbY.bysdb.IDbNFCPatrolLineDetail,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysdbY.bysdb.DbNFCPatrolLineDetail.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.RpcDbNFCPatrolLineDetail/Update'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function RpcDbNFCPatrolLineDetailPartialUpdate(
  req: BysdbY.bysdb.IDbNFCPatrolLineDetail,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysdbY.bysdb.DbNFCPatrolLineDetail.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.RpcDbNFCPatrolLineDetail/PartialUpdate'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function RpcDbNFCPatrolLineDetailDelete(
  req: BysdbY.bysdb.IDbNFCPatrolLineDetail,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysdbY.bysdb.DbNFCPatrolLineDetail.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.RpcDbNFCPatrolLineDetail/Delete'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function RpcDbNFCPatrolLineDetailSelectOne(
  req: CrudY.crud.IDMLParam,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = CrudY.crud.DMLParam.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.RpcDbNFCPatrolLineDetail/SelectOne'
  const v = 0

  return rpcCon.UnaryCall(
    reqData,
    api,
    v,
    BysdbY.bysdb.DbNFCPatrolLineDetail,
    callOpt,
  )
}

export function RpcDbNFCPatrolLineDetailSelectMany(
  req: CrudY.crud.DMLParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/bysdb.RpcDbNFCPatrolLineDetail/SelectMany'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.DMLParam,
    BysdbY.bysdb.DbNFCPatrolLineDetail,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function RpcDbNFCPatrolLineDetailQuery(
  req: CrudY.crud.QueryParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/bysdb.RpcDbNFCPatrolLineDetail/Query'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.QueryParam,
    BysdbY.bysdb.DbNFCPatrolLineDetail,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function RpcDbNFCPatrolLineDetailQueryBatch(
  req: CrudY.crud.QueryParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/bysdb.RpcDbNFCPatrolLineDetail/QueryBatch'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.QueryParam,
    BysdbListY.bysdb.DbNFCPatrolLineDetailList,
    callOpt,
  )
  r.sendFirst(req)
  return r
}

export class RpcDbNFCPatrolLineDetail {
  public static Insert(
    req: BysdbY.bysdb.IDbNFCPatrolLineDetail,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbNFCPatrolLineDetailInsert(req, callOpt)
  }
  public static Update(
    req: BysdbY.bysdb.IDbNFCPatrolLineDetail,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbNFCPatrolLineDetailUpdate(req, callOpt)
  }
  public static PartialUpdate(
    req: BysdbY.bysdb.IDbNFCPatrolLineDetail,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbNFCPatrolLineDetailPartialUpdate(req, callOpt)
  }
  public static Delete(
    req: BysdbY.bysdb.IDbNFCPatrolLineDetail,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbNFCPatrolLineDetailDelete(req, callOpt)
  }
  public static SelectOne(
    req: CrudY.crud.IDMLParam,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbNFCPatrolLineDetailSelectOne(req, callOpt)
  }
  public static SelectMany(
    req: CrudY.crud.DMLParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return RpcDbNFCPatrolLineDetailSelectMany(req, callOpt)
  }
  public static Query(
    req: CrudY.crud.QueryParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return RpcDbNFCPatrolLineDetailQuery(req, callOpt)
  }
  public static QueryBatch(
    req: CrudY.crud.QueryParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return RpcDbNFCPatrolLineDetailQueryBatch(req, callOpt)
  }
}

export function PrpcDbNFCPatrolLineDetailInsert(
  req: BysdbY.bysdb.IDbNFCPatrolLineDetail,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysdbY.bysdb.DbNFCPatrolLineDetail.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.PrpcDbNFCPatrolLineDetail/Insert'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function PrpcDbNFCPatrolLineDetailUpdate(
  req: BysdbY.bysdb.IDbNFCPatrolLineDetail,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysdbY.bysdb.DbNFCPatrolLineDetail.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.PrpcDbNFCPatrolLineDetail/Update'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function PrpcDbNFCPatrolLineDetailPartialUpdate(
  req: BysdbY.bysdb.IDbNFCPatrolLineDetail,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysdbY.bysdb.DbNFCPatrolLineDetail.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.PrpcDbNFCPatrolLineDetail/PartialUpdate'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function PrpcDbNFCPatrolLineDetailDelete(
  req: BysdbY.bysdb.IDbNFCPatrolLineDetail,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysdbY.bysdb.DbNFCPatrolLineDetail.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.PrpcDbNFCPatrolLineDetail/Delete'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function PrpcDbNFCPatrolLineDetailSelectOne(
  req: CrudY.crud.IDMLParam,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = CrudY.crud.DMLParam.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.PrpcDbNFCPatrolLineDetail/SelectOne'
  const v = 0

  return rpcCon.UnaryCall(
    reqData,
    api,
    v,
    BysdbY.bysdb.DbNFCPatrolLineDetail,
    callOpt,
  )
}

export function PrpcDbNFCPatrolLineDetailSelectMany(
  req: CrudY.crud.DMLParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/bysdb.PrpcDbNFCPatrolLineDetail/SelectMany'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.DMLParam,
    BysdbY.bysdb.DbNFCPatrolLineDetail,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function PrpcDbNFCPatrolLineDetailQuery(
  req: CrudY.crud.PrivilegeParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/bysdb.PrpcDbNFCPatrolLineDetail/Query'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.PrivilegeParam,
    BysdbY.bysdb.DbNFCPatrolLineDetail,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function PrpcDbNFCPatrolLineDetailQueryBatch(
  req: CrudY.crud.PrivilegeParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/bysdb.PrpcDbNFCPatrolLineDetail/QueryBatch'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.PrivilegeParam,
    BysdbListY.bysdb.DbNFCPatrolLineDetailList,
    callOpt,
  )
  r.sendFirst(req)
  return r
}

export class PrpcDbNFCPatrolLineDetail {
  public static Insert(
    req: BysdbY.bysdb.IDbNFCPatrolLineDetail,
    callOpt?: ICallOption,
  ): boolean {
    return PrpcDbNFCPatrolLineDetailInsert(req, callOpt)
  }
  public static Update(
    req: BysdbY.bysdb.IDbNFCPatrolLineDetail,
    callOpt?: ICallOption,
  ): boolean {
    return PrpcDbNFCPatrolLineDetailUpdate(req, callOpt)
  }
  public static PartialUpdate(
    req: BysdbY.bysdb.IDbNFCPatrolLineDetail,
    callOpt?: ICallOption,
  ): boolean {
    return PrpcDbNFCPatrolLineDetailPartialUpdate(req, callOpt)
  }
  public static Delete(
    req: BysdbY.bysdb.IDbNFCPatrolLineDetail,
    callOpt?: ICallOption,
  ): boolean {
    return PrpcDbNFCPatrolLineDetailDelete(req, callOpt)
  }
  public static SelectOne(
    req: CrudY.crud.IDMLParam,
    callOpt?: ICallOption,
  ): boolean {
    return PrpcDbNFCPatrolLineDetailSelectOne(req, callOpt)
  }
  public static SelectMany(
    req: CrudY.crud.DMLParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return PrpcDbNFCPatrolLineDetailSelectMany(req, callOpt)
  }
  public static Query(
    req: CrudY.crud.PrivilegeParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return PrpcDbNFCPatrolLineDetailQuery(req, callOpt)
  }
  public static QueryBatch(
    req: CrudY.crud.PrivilegeParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return PrpcDbNFCPatrolLineDetailQueryBatch(req, callOpt)
  }
}

export function RpcDbNFCPatrolLineRulesInsert(
  req: BysdbY.bysdb.IDbNFCPatrolLineRules,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysdbY.bysdb.DbNFCPatrolLineRules.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.RpcDbNFCPatrolLineRules/Insert'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function RpcDbNFCPatrolLineRulesUpdate(
  req: BysdbY.bysdb.IDbNFCPatrolLineRules,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysdbY.bysdb.DbNFCPatrolLineRules.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.RpcDbNFCPatrolLineRules/Update'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function RpcDbNFCPatrolLineRulesPartialUpdate(
  req: BysdbY.bysdb.IDbNFCPatrolLineRules,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysdbY.bysdb.DbNFCPatrolLineRules.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.RpcDbNFCPatrolLineRules/PartialUpdate'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function RpcDbNFCPatrolLineRulesDelete(
  req: BysdbY.bysdb.IDbNFCPatrolLineRules,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysdbY.bysdb.DbNFCPatrolLineRules.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.RpcDbNFCPatrolLineRules/Delete'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function RpcDbNFCPatrolLineRulesSelectOne(
  req: CrudY.crud.IDMLParam,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = CrudY.crud.DMLParam.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.RpcDbNFCPatrolLineRules/SelectOne'
  const v = 0

  return rpcCon.UnaryCall(
    reqData,
    api,
    v,
    BysdbY.bysdb.DbNFCPatrolLineRules,
    callOpt,
  )
}

export function RpcDbNFCPatrolLineRulesSelectMany(
  req: CrudY.crud.DMLParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/bysdb.RpcDbNFCPatrolLineRules/SelectMany'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.DMLParam,
    BysdbY.bysdb.DbNFCPatrolLineRules,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function RpcDbNFCPatrolLineRulesQuery(
  req: CrudY.crud.QueryParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/bysdb.RpcDbNFCPatrolLineRules/Query'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.QueryParam,
    BysdbY.bysdb.DbNFCPatrolLineRules,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function RpcDbNFCPatrolLineRulesQueryBatch(
  req: CrudY.crud.QueryParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/bysdb.RpcDbNFCPatrolLineRules/QueryBatch'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.QueryParam,
    BysdbListY.bysdb.DbNFCPatrolLineRulesList,
    callOpt,
  )
  r.sendFirst(req)
  return r
}

export class RpcDbNFCPatrolLineRules {
  public static Insert(
    req: BysdbY.bysdb.IDbNFCPatrolLineRules,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbNFCPatrolLineRulesInsert(req, callOpt)
  }
  public static Update(
    req: BysdbY.bysdb.IDbNFCPatrolLineRules,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbNFCPatrolLineRulesUpdate(req, callOpt)
  }
  public static PartialUpdate(
    req: BysdbY.bysdb.IDbNFCPatrolLineRules,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbNFCPatrolLineRulesPartialUpdate(req, callOpt)
  }
  public static Delete(
    req: BysdbY.bysdb.IDbNFCPatrolLineRules,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbNFCPatrolLineRulesDelete(req, callOpt)
  }
  public static SelectOne(
    req: CrudY.crud.IDMLParam,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbNFCPatrolLineRulesSelectOne(req, callOpt)
  }
  public static SelectMany(
    req: CrudY.crud.DMLParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return RpcDbNFCPatrolLineRulesSelectMany(req, callOpt)
  }
  public static Query(
    req: CrudY.crud.QueryParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return RpcDbNFCPatrolLineRulesQuery(req, callOpt)
  }
  public static QueryBatch(
    req: CrudY.crud.QueryParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return RpcDbNFCPatrolLineRulesQueryBatch(req, callOpt)
  }
}

export function PrpcDbNFCPatrolLineRulesInsert(
  req: BysdbY.bysdb.IDbNFCPatrolLineRules,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysdbY.bysdb.DbNFCPatrolLineRules.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.PrpcDbNFCPatrolLineRules/Insert'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function PrpcDbNFCPatrolLineRulesUpdate(
  req: BysdbY.bysdb.IDbNFCPatrolLineRules,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysdbY.bysdb.DbNFCPatrolLineRules.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.PrpcDbNFCPatrolLineRules/Update'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function PrpcDbNFCPatrolLineRulesPartialUpdate(
  req: BysdbY.bysdb.IDbNFCPatrolLineRules,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysdbY.bysdb.DbNFCPatrolLineRules.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.PrpcDbNFCPatrolLineRules/PartialUpdate'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function PrpcDbNFCPatrolLineRulesDelete(
  req: BysdbY.bysdb.IDbNFCPatrolLineRules,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysdbY.bysdb.DbNFCPatrolLineRules.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.PrpcDbNFCPatrolLineRules/Delete'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function PrpcDbNFCPatrolLineRulesSelectOne(
  req: CrudY.crud.IDMLParam,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = CrudY.crud.DMLParam.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.PrpcDbNFCPatrolLineRules/SelectOne'
  const v = 0

  return rpcCon.UnaryCall(
    reqData,
    api,
    v,
    BysdbY.bysdb.DbNFCPatrolLineRules,
    callOpt,
  )
}

export function PrpcDbNFCPatrolLineRulesSelectMany(
  req: CrudY.crud.DMLParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/bysdb.PrpcDbNFCPatrolLineRules/SelectMany'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.DMLParam,
    BysdbY.bysdb.DbNFCPatrolLineRules,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function PrpcDbNFCPatrolLineRulesQuery(
  req: CrudY.crud.PrivilegeParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/bysdb.PrpcDbNFCPatrolLineRules/Query'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.PrivilegeParam,
    BysdbY.bysdb.DbNFCPatrolLineRules,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function PrpcDbNFCPatrolLineRulesQueryBatch(
  req: CrudY.crud.PrivilegeParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/bysdb.PrpcDbNFCPatrolLineRules/QueryBatch'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.PrivilegeParam,
    BysdbListY.bysdb.DbNFCPatrolLineRulesList,
    callOpt,
  )
  r.sendFirst(req)
  return r
}

export class PrpcDbNFCPatrolLineRules {
  public static Insert(
    req: BysdbY.bysdb.IDbNFCPatrolLineRules,
    callOpt?: ICallOption,
  ): boolean {
    return PrpcDbNFCPatrolLineRulesInsert(req, callOpt)
  }
  public static Update(
    req: BysdbY.bysdb.IDbNFCPatrolLineRules,
    callOpt?: ICallOption,
  ): boolean {
    return PrpcDbNFCPatrolLineRulesUpdate(req, callOpt)
  }
  public static PartialUpdate(
    req: BysdbY.bysdb.IDbNFCPatrolLineRules,
    callOpt?: ICallOption,
  ): boolean {
    return PrpcDbNFCPatrolLineRulesPartialUpdate(req, callOpt)
  }
  public static Delete(
    req: BysdbY.bysdb.IDbNFCPatrolLineRules,
    callOpt?: ICallOption,
  ): boolean {
    return PrpcDbNFCPatrolLineRulesDelete(req, callOpt)
  }
  public static SelectOne(
    req: CrudY.crud.IDMLParam,
    callOpt?: ICallOption,
  ): boolean {
    return PrpcDbNFCPatrolLineRulesSelectOne(req, callOpt)
  }
  public static SelectMany(
    req: CrudY.crud.DMLParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return PrpcDbNFCPatrolLineRulesSelectMany(req, callOpt)
  }
  public static Query(
    req: CrudY.crud.PrivilegeParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return PrpcDbNFCPatrolLineRulesQuery(req, callOpt)
  }
  public static QueryBatch(
    req: CrudY.crud.PrivilegeParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return PrpcDbNFCPatrolLineRulesQueryBatch(req, callOpt)
  }
}

export function RpcDbNFCPatrolLineAndRulesInsert(
  req: BysdbY.bysdb.IDbNFCPatrolLineAndRules,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysdbY.bysdb.DbNFCPatrolLineAndRules.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.RpcDbNFCPatrolLineAndRules/Insert'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function RpcDbNFCPatrolLineAndRulesUpdate(
  req: BysdbY.bysdb.IDbNFCPatrolLineAndRules,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysdbY.bysdb.DbNFCPatrolLineAndRules.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.RpcDbNFCPatrolLineAndRules/Update'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function RpcDbNFCPatrolLineAndRulesPartialUpdate(
  req: BysdbY.bysdb.IDbNFCPatrolLineAndRules,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysdbY.bysdb.DbNFCPatrolLineAndRules.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.RpcDbNFCPatrolLineAndRules/PartialUpdate'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function RpcDbNFCPatrolLineAndRulesDelete(
  req: BysdbY.bysdb.IDbNFCPatrolLineAndRules,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysdbY.bysdb.DbNFCPatrolLineAndRules.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.RpcDbNFCPatrolLineAndRules/Delete'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function RpcDbNFCPatrolLineAndRulesSelectOne(
  req: CrudY.crud.IDMLParam,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = CrudY.crud.DMLParam.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.RpcDbNFCPatrolLineAndRules/SelectOne'
  const v = 0

  return rpcCon.UnaryCall(
    reqData,
    api,
    v,
    BysdbY.bysdb.DbNFCPatrolLineAndRules,
    callOpt,
  )
}

export function RpcDbNFCPatrolLineAndRulesSelectMany(
  req: CrudY.crud.DMLParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/bysdb.RpcDbNFCPatrolLineAndRules/SelectMany'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.DMLParam,
    BysdbY.bysdb.DbNFCPatrolLineAndRules,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function RpcDbNFCPatrolLineAndRulesQuery(
  req: CrudY.crud.QueryParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/bysdb.RpcDbNFCPatrolLineAndRules/Query'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.QueryParam,
    BysdbY.bysdb.DbNFCPatrolLineAndRules,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function RpcDbNFCPatrolLineAndRulesQueryBatch(
  req: CrudY.crud.QueryParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/bysdb.RpcDbNFCPatrolLineAndRules/QueryBatch'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.QueryParam,
    BysdbListY.bysdb.DbNFCPatrolLineAndRulesList,
    callOpt,
  )
  r.sendFirst(req)
  return r
}

export class RpcDbNFCPatrolLineAndRules {
  public static Insert(
    req: BysdbY.bysdb.IDbNFCPatrolLineAndRules,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbNFCPatrolLineAndRulesInsert(req, callOpt)
  }
  public static Update(
    req: BysdbY.bysdb.IDbNFCPatrolLineAndRules,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbNFCPatrolLineAndRulesUpdate(req, callOpt)
  }
  public static PartialUpdate(
    req: BysdbY.bysdb.IDbNFCPatrolLineAndRules,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbNFCPatrolLineAndRulesPartialUpdate(req, callOpt)
  }
  public static Delete(
    req: BysdbY.bysdb.IDbNFCPatrolLineAndRules,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbNFCPatrolLineAndRulesDelete(req, callOpt)
  }
  public static SelectOne(
    req: CrudY.crud.IDMLParam,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbNFCPatrolLineAndRulesSelectOne(req, callOpt)
  }
  public static SelectMany(
    req: CrudY.crud.DMLParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return RpcDbNFCPatrolLineAndRulesSelectMany(req, callOpt)
  }
  public static Query(
    req: CrudY.crud.QueryParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return RpcDbNFCPatrolLineAndRulesQuery(req, callOpt)
  }
  public static QueryBatch(
    req: CrudY.crud.QueryParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return RpcDbNFCPatrolLineAndRulesQueryBatch(req, callOpt)
  }
}

export function PrpcDbNFCPatrolLineAndRulesInsert(
  req: BysdbY.bysdb.IDbNFCPatrolLineAndRules,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysdbY.bysdb.DbNFCPatrolLineAndRules.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.PrpcDbNFCPatrolLineAndRules/Insert'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function PrpcDbNFCPatrolLineAndRulesUpdate(
  req: BysdbY.bysdb.IDbNFCPatrolLineAndRules,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysdbY.bysdb.DbNFCPatrolLineAndRules.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.PrpcDbNFCPatrolLineAndRules/Update'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function PrpcDbNFCPatrolLineAndRulesPartialUpdate(
  req: BysdbY.bysdb.IDbNFCPatrolLineAndRules,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysdbY.bysdb.DbNFCPatrolLineAndRules.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.PrpcDbNFCPatrolLineAndRules/PartialUpdate'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function PrpcDbNFCPatrolLineAndRulesDelete(
  req: BysdbY.bysdb.IDbNFCPatrolLineAndRules,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysdbY.bysdb.DbNFCPatrolLineAndRules.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.PrpcDbNFCPatrolLineAndRules/Delete'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function PrpcDbNFCPatrolLineAndRulesSelectOne(
  req: CrudY.crud.IDMLParam,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = CrudY.crud.DMLParam.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.PrpcDbNFCPatrolLineAndRules/SelectOne'
  const v = 0

  return rpcCon.UnaryCall(
    reqData,
    api,
    v,
    BysdbY.bysdb.DbNFCPatrolLineAndRules,
    callOpt,
  )
}

export function PrpcDbNFCPatrolLineAndRulesSelectMany(
  req: CrudY.crud.DMLParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/bysdb.PrpcDbNFCPatrolLineAndRules/SelectMany'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.DMLParam,
    BysdbY.bysdb.DbNFCPatrolLineAndRules,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function PrpcDbNFCPatrolLineAndRulesQuery(
  req: CrudY.crud.PrivilegeParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/bysdb.PrpcDbNFCPatrolLineAndRules/Query'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.PrivilegeParam,
    BysdbY.bysdb.DbNFCPatrolLineAndRules,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function PrpcDbNFCPatrolLineAndRulesQueryBatch(
  req: CrudY.crud.PrivilegeParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/bysdb.PrpcDbNFCPatrolLineAndRules/QueryBatch'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.PrivilegeParam,
    BysdbListY.bysdb.DbNFCPatrolLineAndRulesList,
    callOpt,
  )
  r.sendFirst(req)
  return r
}

export class PrpcDbNFCPatrolLineAndRules {
  public static Insert(
    req: BysdbY.bysdb.IDbNFCPatrolLineAndRules,
    callOpt?: ICallOption,
  ): boolean {
    return PrpcDbNFCPatrolLineAndRulesInsert(req, callOpt)
  }
  public static Update(
    req: BysdbY.bysdb.IDbNFCPatrolLineAndRules,
    callOpt?: ICallOption,
  ): boolean {
    return PrpcDbNFCPatrolLineAndRulesUpdate(req, callOpt)
  }
  public static PartialUpdate(
    req: BysdbY.bysdb.IDbNFCPatrolLineAndRules,
    callOpt?: ICallOption,
  ): boolean {
    return PrpcDbNFCPatrolLineAndRulesPartialUpdate(req, callOpt)
  }
  public static Delete(
    req: BysdbY.bysdb.IDbNFCPatrolLineAndRules,
    callOpt?: ICallOption,
  ): boolean {
    return PrpcDbNFCPatrolLineAndRulesDelete(req, callOpt)
  }
  public static SelectOne(
    req: CrudY.crud.IDMLParam,
    callOpt?: ICallOption,
  ): boolean {
    return PrpcDbNFCPatrolLineAndRulesSelectOne(req, callOpt)
  }
  public static SelectMany(
    req: CrudY.crud.DMLParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return PrpcDbNFCPatrolLineAndRulesSelectMany(req, callOpt)
  }
  public static Query(
    req: CrudY.crud.PrivilegeParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return PrpcDbNFCPatrolLineAndRulesQuery(req, callOpt)
  }
  public static QueryBatch(
    req: CrudY.crud.PrivilegeParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return PrpcDbNFCPatrolLineAndRulesQueryBatch(req, callOpt)
  }
}

export function RpcDbMarkerUploadImageHistoryInsert(
  req: BysdbY.bysdb.IDbMarkerUploadImageHistory,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysdbY.bysdb.DbMarkerUploadImageHistory.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.RpcDbMarkerUploadImageHistory/Insert'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}

export function RpcDbMarkerUploadImageHistoryQuery(
  req: CrudY.crud.QueryParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/bysdb.RpcDbMarkerUploadImageHistory/Query'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.QueryParam,
    BysdbY.bysdb.DbMarkerUploadImageHistory,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function RpcDbMarkerUploadImageHistoryQueryBatch(
  req: CrudY.crud.QueryParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/bysdb.RpcDbMarkerUploadImageHistory/QueryBatch'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.QueryParam,
    BysdbListY.bysdb.DbMarkerUploadImageHistoryList,
    callOpt,
  )
  r.sendFirst(req)
  return r
}

export class RpcDbMarkerUploadImageHistory {
  public static Insert(
    req: BysdbY.bysdb.IDbMarkerUploadImageHistory,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbMarkerUploadImageHistoryInsert(req, callOpt)
  }
  public static Query(
    req: CrudY.crud.QueryParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return RpcDbMarkerUploadImageHistoryQuery(req, callOpt)
  }
  public static QueryBatch(
    req: CrudY.crud.QueryParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return RpcDbMarkerUploadImageHistoryQueryBatch(req, callOpt)
  }
}

export function PrpcDbMarkerUploadImageHistoryInsert(
  req: BysdbY.bysdb.IDbMarkerUploadImageHistory,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysdbY.bysdb.DbMarkerUploadImageHistory.encode(req)
  let reqData = w.finish()

  const api = '/bysdb.PrpcDbMarkerUploadImageHistory/Insert'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}

export function PrpcDbMarkerUploadImageHistoryQuery(
  req: CrudY.crud.PrivilegeParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/bysdb.PrpcDbMarkerUploadImageHistory/Query'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.PrivilegeParam,
    BysdbY.bysdb.DbMarkerUploadImageHistory,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function PrpcDbMarkerUploadImageHistoryQueryBatch(
  req: CrudY.crud.PrivilegeParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/bysdb.PrpcDbMarkerUploadImageHistory/QueryBatch'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.PrivilegeParam,
    BysdbListY.bysdb.DbMarkerUploadImageHistoryList,
    callOpt,
  )
  r.sendFirst(req)
  return r
}

export class PrpcDbMarkerUploadImageHistory {
  public static Insert(
    req: BysdbY.bysdb.IDbMarkerUploadImageHistory,
    callOpt?: ICallOption,
  ): boolean {
    return PrpcDbMarkerUploadImageHistoryInsert(req, callOpt)
  }
  public static Query(
    req: CrudY.crud.PrivilegeParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return PrpcDbMarkerUploadImageHistoryQuery(req, callOpt)
  }
  public static QueryBatch(
    req: CrudY.crud.PrivilegeParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return PrpcDbMarkerUploadImageHistoryQueryBatch(req, callOpt)
  }
}
export default {
  RpcDbController,
  RpcDbControllerInsert,
  RpcDbControllerUpdate,
  RpcDbControllerPartialUpdate,
  RpcDbControllerDelete,
  RpcDbControllerSelectOne,
  RpcDbControllerSelectMany,
  RpcDbControllerQuery,
  RpcDbControllerQueryBatch,
  PrpcDbController,
  PrpcDbControllerInsert,
  PrpcDbControllerUpdate,
  PrpcDbControllerPartialUpdate,
  PrpcDbControllerDelete,
  PrpcDbControllerSelectOne,
  PrpcDbControllerSelectMany,
  PrpcDbControllerQuery,
  PrpcDbControllerQueryBatch,
  RpcDbBysMarker,
  RpcDbBysMarkerInsert,
  RpcDbBysMarkerUpdate,
  RpcDbBysMarkerPartialUpdate,
  RpcDbBysMarkerDelete,
  RpcDbBysMarkerSelectOne,
  RpcDbBysMarkerSelectMany,
  RpcDbBysMarkerQuery,
  RpcDbBysMarkerQueryBatch,
  PrpcDbBysMarker,
  PrpcDbBysMarkerInsert,
  PrpcDbBysMarkerUpdate,
  PrpcDbBysMarkerPartialUpdate,
  PrpcDbBysMarkerDelete,
  PrpcDbBysMarkerSelectOne,
  PrpcDbBysMarkerSelectMany,
  PrpcDbBysMarkerQuery,
  PrpcDbBysMarkerQueryBatch,
  RpcDbControllerOnlineHistory,
  RpcDbControllerOnlineHistoryInsert,
  RpcDbControllerOnlineHistoryQuery,
  RpcDbControllerOnlineHistoryQueryBatch,
  PrpcDbControllerOnlineHistory,
  PrpcDbControllerOnlineHistoryInsert,
  PrpcDbControllerOnlineHistoryQuery,
  PrpcDbControllerOnlineHistoryQueryBatch,
  RpcDbMediaInfo,
  RpcDbMediaInfoInsert,
  RpcDbMediaInfoUpdate,
  RpcDbMediaInfoPartialUpdate,
  RpcDbMediaInfoDelete,
  RpcDbMediaInfoSelectOne,
  RpcDbMediaInfoSelectMany,
  RpcDbMediaInfoQuery,
  RpcDbMediaInfoQueryBatch,
  PrpcDbMediaInfo,
  PrpcDbMediaInfoInsert,
  PrpcDbMediaInfoUpdate,
  PrpcDbMediaInfoPartialUpdate,
  PrpcDbMediaInfoDelete,
  PrpcDbMediaInfoSelectOne,
  PrpcDbMediaInfoSelectMany,
  PrpcDbMediaInfoQuery,
  PrpcDbMediaInfoQueryBatch,
  RpcDbMarkerHistory,
  RpcDbMarkerHistoryInsert,
  RpcDbMarkerHistoryQuery,
  RpcDbMarkerHistoryQueryBatch,
  PrpcDbMarkerHistory,
  PrpcDbMarkerHistoryInsert,
  PrpcDbMarkerHistoryQuery,
  PrpcDbMarkerHistoryQueryBatch,
  RpcDbMarkerPatrolHistory,
  RpcDbMarkerPatrolHistoryInsert,
  RpcDbMarkerPatrolHistoryQuery,
  RpcDbMarkerPatrolHistoryQueryBatch,
  PrpcDbMarkerPatrolHistory,
  PrpcDbMarkerPatrolHistoryInsert,
  PrpcDbMarkerPatrolHistoryQuery,
  PrpcDbMarkerPatrolHistoryQueryBatch,
  RpcDbNFCPatrolLine,
  RpcDbNFCPatrolLineInsert,
  RpcDbNFCPatrolLineUpdate,
  RpcDbNFCPatrolLinePartialUpdate,
  RpcDbNFCPatrolLineDelete,
  RpcDbNFCPatrolLineSelectOne,
  RpcDbNFCPatrolLineSelectMany,
  RpcDbNFCPatrolLineQuery,
  RpcDbNFCPatrolLineQueryBatch,
  PrpcDbNFCPatrolLine,
  PrpcDbNFCPatrolLineInsert,
  PrpcDbNFCPatrolLineUpdate,
  PrpcDbNFCPatrolLinePartialUpdate,
  PrpcDbNFCPatrolLineDelete,
  PrpcDbNFCPatrolLineSelectOne,
  PrpcDbNFCPatrolLineSelectMany,
  PrpcDbNFCPatrolLineQuery,
  PrpcDbNFCPatrolLineQueryBatch,
  RpcDbNFCPatrolLineDetail,
  RpcDbNFCPatrolLineDetailInsert,
  RpcDbNFCPatrolLineDetailUpdate,
  RpcDbNFCPatrolLineDetailPartialUpdate,
  RpcDbNFCPatrolLineDetailDelete,
  RpcDbNFCPatrolLineDetailSelectOne,
  RpcDbNFCPatrolLineDetailSelectMany,
  RpcDbNFCPatrolLineDetailQuery,
  RpcDbNFCPatrolLineDetailQueryBatch,
  PrpcDbNFCPatrolLineDetail,
  PrpcDbNFCPatrolLineDetailInsert,
  PrpcDbNFCPatrolLineDetailUpdate,
  PrpcDbNFCPatrolLineDetailPartialUpdate,
  PrpcDbNFCPatrolLineDetailDelete,
  PrpcDbNFCPatrolLineDetailSelectOne,
  PrpcDbNFCPatrolLineDetailSelectMany,
  PrpcDbNFCPatrolLineDetailQuery,
  PrpcDbNFCPatrolLineDetailQueryBatch,
  RpcDbNFCPatrolLineRules,
  RpcDbNFCPatrolLineRulesInsert,
  RpcDbNFCPatrolLineRulesUpdate,
  RpcDbNFCPatrolLineRulesPartialUpdate,
  RpcDbNFCPatrolLineRulesDelete,
  RpcDbNFCPatrolLineRulesSelectOne,
  RpcDbNFCPatrolLineRulesSelectMany,
  RpcDbNFCPatrolLineRulesQuery,
  RpcDbNFCPatrolLineRulesQueryBatch,
  PrpcDbNFCPatrolLineRules,
  PrpcDbNFCPatrolLineRulesInsert,
  PrpcDbNFCPatrolLineRulesUpdate,
  PrpcDbNFCPatrolLineRulesPartialUpdate,
  PrpcDbNFCPatrolLineRulesDelete,
  PrpcDbNFCPatrolLineRulesSelectOne,
  PrpcDbNFCPatrolLineRulesSelectMany,
  PrpcDbNFCPatrolLineRulesQuery,
  PrpcDbNFCPatrolLineRulesQueryBatch,
  RpcDbNFCPatrolLineAndRules,
  RpcDbNFCPatrolLineAndRulesInsert,
  RpcDbNFCPatrolLineAndRulesUpdate,
  RpcDbNFCPatrolLineAndRulesPartialUpdate,
  RpcDbNFCPatrolLineAndRulesDelete,
  RpcDbNFCPatrolLineAndRulesSelectOne,
  RpcDbNFCPatrolLineAndRulesSelectMany,
  RpcDbNFCPatrolLineAndRulesQuery,
  RpcDbNFCPatrolLineAndRulesQueryBatch,
  PrpcDbNFCPatrolLineAndRules,
  PrpcDbNFCPatrolLineAndRulesInsert,
  PrpcDbNFCPatrolLineAndRulesUpdate,
  PrpcDbNFCPatrolLineAndRulesPartialUpdate,
  PrpcDbNFCPatrolLineAndRulesDelete,
  PrpcDbNFCPatrolLineAndRulesSelectOne,
  PrpcDbNFCPatrolLineAndRulesSelectMany,
  PrpcDbNFCPatrolLineAndRulesQuery,
  PrpcDbNFCPatrolLineAndRulesQueryBatch,
  RpcDbMarkerUploadImageHistory,
  RpcDbMarkerUploadImageHistoryInsert,
  RpcDbMarkerUploadImageHistoryQuery,
  RpcDbMarkerUploadImageHistoryQueryBatch,
  PrpcDbMarkerUploadImageHistory,
  PrpcDbMarkerUploadImageHistoryInsert,
  PrpcDbMarkerUploadImageHistoryQuery,
  PrpcDbMarkerUploadImageHistoryQueryBatch,
}

import * as $protobuf from 'protobufjs'
/** Namespace crud. */
export namespace crud {
  /** Properties of a DMLResult. */
  interface IDMLResult {
    /** 影响的行数，如果成功>0,否则=0 */
    AffectedRow?: number | null

    /** 错误信息，如果有的话 */
    errInfo?: string | null

    /** 附加信息,json格式 */
    Note?: string | null
  }

  /** CRUD DML结果 */
  class DMLResult implements IDMLResult {
    /**
     * Constructs a new DMLResult.
     * @param [properties] Properties to set
     */
    constructor(properties?: crud.IDMLResult)

    /** 影响的行数，如果成功>0,否则=0 */
    public AffectedRow: number

    /** 错误信息，如果有的话 */
    public errInfo: string

    /** 附加信息,json格式 */
    public Note: string

    /**
     * Encodes the specified DMLResult message. Does not implicitly {@link crud.DMLResult.verify|verify} messages.
     * @param message DMLResult message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: crud.<PERSON><PERSON><PERSON><PERSON><PERSON>,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a DMLResult message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns DMLResult
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): crud.DMLResult
  }

  /** Properties of a DMLParam. */
  interface IDMLParam {
    /** 条件字段名 */
    KeyColumn?: string[] | null

    /** 结果字段名 */
    ResultColumn?: string[] | null

    /** 条件值 */
    KeyValue?: string[] | null
  }

  /** 简单CRUD里面用到的条件和结果字段列表 */
  class DMLParam implements IDMLParam {
    /**
     * Constructs a new DMLParam.
     * @param [properties] Properties to set
     */
    constructor(properties?: crud.IDMLParam)

    /** 条件字段名 */
    public KeyColumn: string[]

    /** 结果字段名 */
    public ResultColumn: string[]

    /** 条件值 */
    public KeyValue: string[]

    /**
     * Encodes the specified DMLParam message. Does not implicitly {@link crud.DMLParam.verify|verify} messages.
     * @param message DMLParam message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: crud.IDMLParam,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a DMLParam message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns DMLParam
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): crud.DMLParam
  }

  /** Properties of a WhereItem. */
  interface IWhereItem {
    /** fieldname */
    Field?: string | null

    /** field compare operator */
    FieldCompareOperator?: string | null

    /** Field value */
    FieldValue?: string | null
  }

  /** sql where额外条件项 */
  class WhereItem implements IWhereItem {
    /**
     * Constructs a new WhereItem.
     * @param [properties] Properties to set
     */
    constructor(properties?: crud.IWhereItem)

    /** fieldname */
    public Field: string

    /** field compare operator */
    public FieldCompareOperator: string

    /** Field value */
    public FieldValue: string

    /**
     * Encodes the specified WhereItem message. Does not implicitly {@link crud.WhereItem.verify|verify} messages.
     * @param message WhereItem message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: crud.IWhereItem,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a WhereItem message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns WhereItem
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): crud.WhereItem
  }

  /** Properties of a QueryParam. */
  interface IQueryParam {
    /** 想要的结果字段名，不填写为全要 */
    ResultColumn?: string[] | null

    /**
     * Query时需要处理时区的字段,单数为字段名，双数为需要加减的的分钟数
     * 需要此功能必须把所有的结果列名填写在ResultColumn，否则无法处理
     * TimeColumn = ['ActionTime','480'，‘RecvTime','-60']
     */
    TimeColumn?: string[] | null

    /** where额外条件项,只支持 and */
    Where?: crud.IWhereItem[] | null

    /** 分页，需要的数据，如果Limit=0 and Offset=0,则视为不需要分页 */
    Limit?: number | null

    /**
     * 分页，从什么位置开始，如果Limit=0 and
     * Offset!=0,为从Offset的位置开始的所有数据
     */
    Offset?: number | null

    /**
     * 排序条件，每项为sql的order by 条件
     * select * from a order by col1 asc,col2 desc
     * OrderBy=["col1 asc","col2 desc"]
     */
    OrderBy?: string[] | null

    /** QueryBatch调用时,指定一次返回的数据量，不指定时后端按每次一个返回 */
    Batch?: number | null
  }

  /** 查询条件 */
  class QueryParam implements IQueryParam {
    /**
     * Constructs a new QueryParam.
     * @param [properties] Properties to set
     */
    constructor(properties?: crud.IQueryParam)

    /** 想要的结果字段名，不填写为全要 */
    public ResultColumn: string[]

    /**
     * Query时需要处理时区的字段,单数为字段名，双数为需要加减的的分钟数
     * 需要此功能必须把所有的结果列名填写在ResultColumn，否则无法处理
     * TimeColumn = ['ActionTime','480'，‘RecvTime','-60']
     */
    public TimeColumn: string[]

    /** where额外条件项,只支持 and */
    public Where: crud.IWhereItem[]

    /** 分页，需要的数据，如果Limit=0 and Offset=0,则视为不需要分页 */
    public Limit: number

    /**
     * 分页，从什么位置开始，如果Limit=0 and
     * Offset!=0,为从Offset的位置开始的所有数据
     */
    public Offset: number

    /**
     * 排序条件，每项为sql的order by 条件
     * select * from a order by col1 asc,col2 desc
     * OrderBy=["col1 asc","col2 desc"]
     */
    public OrderBy: string[]

    /** QueryBatch调用时,指定一次返回的数据量，不指定时后端按每次一个返回 */
    public Batch: number

    /**
     * Encodes the specified QueryParam message. Does not implicitly {@link crud.QueryParam.verify|verify} messages.
     * @param message QueryParam message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: crud.IQueryParam,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a QueryParam message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns QueryParam
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): crud.QueryParam
  }

  /** Properties of a PrivilegeParam. */
  interface IPrivilegeParam {
    /** 系统名称 */
    System?: string | null

    /** SessionID */
    SessionID?: string | null

    /** 查询条件 */
    QueryCondition?: crud.IQueryParam | null
  }

  /** 取得用户有权限的数据 */
  class PrivilegeParam implements IPrivilegeParam {
    /**
     * Constructs a new PrivilegeParam.
     * @param [properties] Properties to set
     */
    constructor(properties?: crud.IPrivilegeParam)

    /** 系统名称 */
    public System: string

    /** SessionID */
    public SessionID: string

    /** 查询条件 */
    public QueryCondition?: crud.IQueryParam | null

    /**
     * Encodes the specified PrivilegeParam message. Does not implicitly {@link crud.PrivilegeParam.verify|verify} messages.
     * @param message PrivilegeParam message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: crud.IPrivilegeParam,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a PrivilegeParam message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns PrivilegeParam
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): crud.PrivilegeParam
  }
}

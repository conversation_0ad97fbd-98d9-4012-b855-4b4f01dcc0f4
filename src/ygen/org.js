/*eslint-disable block-scoped-var, id-length, no-control-regex, no-magic-numbers, no-prototype-builtins, no-redeclare, no-shadow, no-var, sort-vars*/
import * as $protobuf from 'protobufjs/minimal'

// Common aliases
const $Reader = $protobuf.Reader,
  $Writer = $protobuf.Writer,
  $util = $protobuf.util

// Exported root namespace
const $root = $protobuf.roots['default'] || ($protobuf.roots['default'] = {})

export const org = ($root.org = (() => {
  /**
   * Namespace org.
   * @exports org
   * @namespace
   */
  const org = $root.org || {}

  org.DbOrg = (function () {
    /**
     * Properties of a DbOrg.
     * @memberof org
     * @interface IDbOrg
     * @property {string|null} [RID] @db uuid primary key
     * 行ID
     * @property {string|null} [OrgID] @db varchar(64) unique not null
     * 组织机构自编号
     * @property {number|null} [OrgType] @db int default 0
     * @property {number|null} [SortValue] @db int default 100
     * 排序值,由用户指定,不用管拼音或者其它语种的排序
     * @property {string|null} [ShortName] @db varchar(32) not null
     * 机构名称,缩写
     * @property {string|null} [FullName] @db varchar(256)
     * 机构名称,全称
     * @property {string|null} [Note] @db text
     * 机构描述/备注信息
     * @property {string|null} [Setting] @db jsonb default  '{}'::jsonb
     * 其它设置，可以在里面扩展需要用到的其它信息
     * @property {string|null} [CreatorRID] @db uuid
     * 创建者的rid
     * @property {string|null} [OrgRID] @db uuid
     * 此组织的上级机构
     * @property {string|null} [UpdatedAt] @db timestamp not null default now_utc()
     * 数据最后修改时间
     * @property {string|null} [UpdatedDC] @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */

    /**
     * Constructs a new DbOrg.
     * @memberof org
     * @classdesc 群组/组织结构表
     * @rpc crud pcrud
     * @dbpost DO $$ BEGIN BEGIN
     * @dbpost   ALTER TABLE DbOrg ADD CONSTRAINT fkDbOrgParentRID  FOREIGN KEY (OrgRID) REFERENCES DbOrg(RID);
     * @dbpost   EXCEPTION WHEN duplicate_object THEN --do nothing
     * @dbpost END; END $$;
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbOrgCreatorRID on DbOrg USING hash(CreatorRID);
     * @dbpost INSERT INTO dborg(rid, orgid, orgtype, sortvalue, shortname, fullname, note, setting, OrgRID, updatedat, updateddc)VALUES('00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', 0, 100, 'root', 'root', '', '{}'::jsonb, NULL, now_utc(), '')  ON CONFLICT  DO NOTHING;
     * @implements IDbOrg
     * @constructor
     * @param {org.IDbOrg=} [properties] Properties to set
     */
    function DbOrg(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * @db uuid primary key
     * 行ID
     * @member {string} RID
     * @memberof org.DbOrg
     * @instance
     */
    DbOrg.prototype.RID = ''

    /**
     * @db varchar(64) unique not null
     * 组织机构自编号
     * @member {string} OrgID
     * @memberof org.DbOrg
     * @instance
     */
    DbOrg.prototype.OrgID = ''

    /**
     * @db int default 0
     * @member {number} OrgType
     * @memberof org.DbOrg
     * @instance
     */
    DbOrg.prototype.OrgType = 0

    /**
     * @db int default 100
     * 排序值,由用户指定,不用管拼音或者其它语种的排序
     * @member {number} SortValue
     * @memberof org.DbOrg
     * @instance
     */
    DbOrg.prototype.SortValue = 0

    /**
     * @db varchar(32) not null
     * 机构名称,缩写
     * @member {string} ShortName
     * @memberof org.DbOrg
     * @instance
     */
    DbOrg.prototype.ShortName = ''

    /**
     * @db varchar(256)
     * 机构名称,全称
     * @member {string} FullName
     * @memberof org.DbOrg
     * @instance
     */
    DbOrg.prototype.FullName = ''

    /**
     * @db text
     * 机构描述/备注信息
     * @member {string} Note
     * @memberof org.DbOrg
     * @instance
     */
    DbOrg.prototype.Note = ''

    /**
     * @db jsonb default  '{}'::jsonb
     * 其它设置，可以在里面扩展需要用到的其它信息
     * @member {string} Setting
     * @memberof org.DbOrg
     * @instance
     */
    DbOrg.prototype.Setting = ''

    /**
     * @db uuid
     * 创建者的rid
     * @member {string} CreatorRID
     * @memberof org.DbOrg
     * @instance
     */
    DbOrg.prototype.CreatorRID = ''

    /**
     * @db uuid
     * 此组织的上级机构
     * @member {string} OrgRID
     * @memberof org.DbOrg
     * @instance
     */
    DbOrg.prototype.OrgRID = ''

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     * @member {string} UpdatedAt
     * @memberof org.DbOrg
     * @instance
     */
    DbOrg.prototype.UpdatedAt = ''

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     * @member {string} UpdatedDC
     * @memberof org.DbOrg
     * @instance
     */
    DbOrg.prototype.UpdatedDC = ''

    /**
     * Encodes the specified DbOrg message. Does not implicitly {@link org.DbOrg.verify|verify} messages.
     * @function encode
     * @memberof org.DbOrg
     * @static
     * @param {org.IDbOrg} message DbOrg message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    DbOrg.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (message.RID != null && Object.hasOwnProperty.call(message, 'RID'))
        writer.uint32(/* id 1, wireType 2 =*/ 10).string(message.RID)
      if (message.OrgID != null && Object.hasOwnProperty.call(message, 'OrgID'))
        writer.uint32(/* id 2, wireType 2 =*/ 18).string(message.OrgID)
      if (
        message.OrgType != null &&
        Object.hasOwnProperty.call(message, 'OrgType')
      )
        writer.uint32(/* id 3, wireType 0 =*/ 24).int32(message.OrgType)
      if (
        message.SortValue != null &&
        Object.hasOwnProperty.call(message, 'SortValue')
      )
        writer.uint32(/* id 4, wireType 0 =*/ 32).int32(message.SortValue)
      if (
        message.ShortName != null &&
        Object.hasOwnProperty.call(message, 'ShortName')
      )
        writer.uint32(/* id 5, wireType 2 =*/ 42).string(message.ShortName)
      if (
        message.FullName != null &&
        Object.hasOwnProperty.call(message, 'FullName')
      )
        writer.uint32(/* id 6, wireType 2 =*/ 50).string(message.FullName)
      if (message.Note != null && Object.hasOwnProperty.call(message, 'Note'))
        writer.uint32(/* id 7, wireType 2 =*/ 58).string(message.Note)
      if (
        message.Setting != null &&
        Object.hasOwnProperty.call(message, 'Setting')
      )
        writer.uint32(/* id 8, wireType 2 =*/ 66).string(message.Setting)
      if (
        message.CreatorRID != null &&
        Object.hasOwnProperty.call(message, 'CreatorRID')
      )
        writer.uint32(/* id 9, wireType 2 =*/ 74).string(message.CreatorRID)
      if (
        message.OrgRID != null &&
        Object.hasOwnProperty.call(message, 'OrgRID')
      )
        writer.uint32(/* id 11, wireType 2 =*/ 90).string(message.OrgRID)
      if (
        message.UpdatedAt != null &&
        Object.hasOwnProperty.call(message, 'UpdatedAt')
      )
        writer.uint32(/* id 14, wireType 2 =*/ 114).string(message.UpdatedAt)
      if (
        message.UpdatedDC != null &&
        Object.hasOwnProperty.call(message, 'UpdatedDC')
      )
        writer.uint32(/* id 15, wireType 2 =*/ 122).string(message.UpdatedDC)
      return writer
    }

    /**
     * Decodes a DbOrg message from the specified reader or buffer.
     * @function decode
     * @memberof org.DbOrg
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {org.DbOrg} DbOrg
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    DbOrg.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.org.DbOrg()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.RID = reader.string()
            break
          case 2:
            message.OrgID = reader.string()
            break
          case 3:
            message.OrgType = reader.int32()
            break
          case 4:
            message.SortValue = reader.int32()
            break
          case 5:
            message.ShortName = reader.string()
            break
          case 6:
            message.FullName = reader.string()
            break
          case 7:
            message.Note = reader.string()
            break
          case 8:
            message.Setting = reader.string()
            break
          case 9:
            message.CreatorRID = reader.string()
            break
          case 11:
            message.OrgRID = reader.string()
            break
          case 14:
            message.UpdatedAt = reader.string()
            break
          case 15:
            message.UpdatedDC = reader.string()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return DbOrg
  })()

  return org
})())

export { $root as default }

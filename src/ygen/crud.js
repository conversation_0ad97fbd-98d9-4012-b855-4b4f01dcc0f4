/*eslint-disable block-scoped-var, id-length, no-control-regex, no-magic-numbers, no-prototype-builtins, no-redeclare, no-shadow, no-var, sort-vars*/
import * as $protobuf from 'protobufjs/minimal'

// Common aliases
const $Reader = $protobuf.Reader,
  $Writer = $protobuf.Writer,
  $util = $protobuf.util

// Exported root namespace
const $root = $protobuf.roots['default'] || ($protobuf.roots['default'] = {})

export const crud = ($root.crud = (() => {
  /**
   * Namespace crud.
   * @exports crud
   * @namespace
   */
  const crud = $root.crud || {}

  crud.DMLResult = (function () {
    /**
     * Properties of a DMLResult.
     * @memberof crud
     * @interface IDMLResult
     * @property {number|null} [AffectedRow] 影响的行数，如果成功>0,否则=0
     * @property {string|null} [errInfo] 错误信息，如果有的话
     * @property {string|null} [Note] 附加信息,json格式
     */

    /**
     * Constructs a new DMLResult.
     * @memberof crud
     * @classdesc CRUD DML结果
     * @implements IDMLResult
     * @constructor
     * @param {crud.IDMLResult=} [properties] Properties to set
     */
    function DMLResult(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 影响的行数，如果成功>0,否则=0
     * @member {number} AffectedRow
     * @memberof crud.DMLResult
     * @instance
     */
    DMLResult.prototype.AffectedRow = 0

    /**
     * 错误信息，如果有的话
     * @member {string} errInfo
     * @memberof crud.DMLResult
     * @instance
     */
    DMLResult.prototype.errInfo = ''

    /**
     * 附加信息,json格式
     * @member {string} Note
     * @memberof crud.DMLResult
     * @instance
     */
    DMLResult.prototype.Note = ''

    /**
     * Encodes the specified DMLResult message. Does not implicitly {@link crud.DMLResult.verify|verify} messages.
     * @function encode
     * @memberof crud.DMLResult
     * @static
     * @param {crud.IDMLResult} message DMLResult message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    DMLResult.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (
        message.AffectedRow != null &&
        Object.hasOwnProperty.call(message, 'AffectedRow')
      )
        writer.uint32(/* id 1, wireType 0 =*/ 8).int32(message.AffectedRow)
      if (
        message.errInfo != null &&
        Object.hasOwnProperty.call(message, 'errInfo')
      )
        writer.uint32(/* id 2, wireType 2 =*/ 18).string(message.errInfo)
      if (message.Note != null && Object.hasOwnProperty.call(message, 'Note'))
        writer.uint32(/* id 3, wireType 2 =*/ 26).string(message.Note)
      return writer
    }

    /**
     * Decodes a DMLResult message from the specified reader or buffer.
     * @function decode
     * @memberof crud.DMLResult
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {crud.DMLResult} DMLResult
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    DMLResult.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.crud.DMLResult()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.AffectedRow = reader.int32()
            break
          case 2:
            message.errInfo = reader.string()
            break
          case 3:
            message.Note = reader.string()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return DMLResult
  })()

  crud.DMLParam = (function () {
    /**
     * Properties of a DMLParam.
     * @memberof crud
     * @interface IDMLParam
     * @property {Array.<string>|null} [KeyColumn] 条件字段名
     * @property {Array.<string>|null} [ResultColumn] 结果字段名
     * @property {Array.<string>|null} [KeyValue] 条件值
     */

    /**
     * Constructs a new DMLParam.
     * @memberof crud
     * @classdesc 简单CRUD里面用到的条件和结果字段列表
     * @implements IDMLParam
     * @constructor
     * @param {crud.IDMLParam=} [properties] Properties to set
     */
    function DMLParam(properties) {
      this.KeyColumn = []
      this.ResultColumn = []
      this.KeyValue = []
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 条件字段名
     * @member {Array.<string>} KeyColumn
     * @memberof crud.DMLParam
     * @instance
     */
    DMLParam.prototype.KeyColumn = $util.emptyArray

    /**
     * 结果字段名
     * @member {Array.<string>} ResultColumn
     * @memberof crud.DMLParam
     * @instance
     */
    DMLParam.prototype.ResultColumn = $util.emptyArray

    /**
     * 条件值
     * @member {Array.<string>} KeyValue
     * @memberof crud.DMLParam
     * @instance
     */
    DMLParam.prototype.KeyValue = $util.emptyArray

    /**
     * Encodes the specified DMLParam message. Does not implicitly {@link crud.DMLParam.verify|verify} messages.
     * @function encode
     * @memberof crud.DMLParam
     * @static
     * @param {crud.IDMLParam} message DMLParam message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    DMLParam.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (message.KeyColumn != null && message.KeyColumn.length)
        for (let i = 0; i < message.KeyColumn.length; ++i)
          writer.uint32(/* id 1, wireType 2 =*/ 10).string(message.KeyColumn[i])
      if (message.ResultColumn != null && message.ResultColumn.length)
        for (let i = 0; i < message.ResultColumn.length; ++i)
          writer
            .uint32(/* id 2, wireType 2 =*/ 18)
            .string(message.ResultColumn[i])
      if (message.KeyValue != null && message.KeyValue.length)
        for (let i = 0; i < message.KeyValue.length; ++i)
          writer.uint32(/* id 3, wireType 2 =*/ 26).string(message.KeyValue[i])
      return writer
    }

    /**
     * Decodes a DMLParam message from the specified reader or buffer.
     * @function decode
     * @memberof crud.DMLParam
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {crud.DMLParam} DMLParam
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    DMLParam.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.crud.DMLParam()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            if (!(message.KeyColumn && message.KeyColumn.length))
              message.KeyColumn = []
            message.KeyColumn.push(reader.string())
            break
          case 2:
            if (!(message.ResultColumn && message.ResultColumn.length))
              message.ResultColumn = []
            message.ResultColumn.push(reader.string())
            break
          case 3:
            if (!(message.KeyValue && message.KeyValue.length))
              message.KeyValue = []
            message.KeyValue.push(reader.string())
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return DMLParam
  })()

  crud.WhereItem = (function () {
    /**
     * Properties of a WhereItem.
     * @memberof crud
     * @interface IWhereItem
     * @property {string|null} [Field] fieldname
     * @property {string|null} [FieldCompareOperator] field compare operator
     * @property {string|null} [FieldValue] Field value
     */

    /**
     * Constructs a new WhereItem.
     * @memberof crud
     * @classdesc sql where额外条件项
     * @implements IWhereItem
     * @constructor
     * @param {crud.IWhereItem=} [properties] Properties to set
     */
    function WhereItem(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * fieldname
     * @member {string} Field
     * @memberof crud.WhereItem
     * @instance
     */
    WhereItem.prototype.Field = ''

    /**
     * field compare operator
     * @member {string} FieldCompareOperator
     * @memberof crud.WhereItem
     * @instance
     */
    WhereItem.prototype.FieldCompareOperator = ''

    /**
     * Field value
     * @member {string} FieldValue
     * @memberof crud.WhereItem
     * @instance
     */
    WhereItem.prototype.FieldValue = ''

    /**
     * Encodes the specified WhereItem message. Does not implicitly {@link crud.WhereItem.verify|verify} messages.
     * @function encode
     * @memberof crud.WhereItem
     * @static
     * @param {crud.IWhereItem} message WhereItem message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    WhereItem.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (message.Field != null && Object.hasOwnProperty.call(message, 'Field'))
        writer.uint32(/* id 5, wireType 2 =*/ 42).string(message.Field)
      if (
        message.FieldCompareOperator != null &&
        Object.hasOwnProperty.call(message, 'FieldCompareOperator')
      )
        writer
          .uint32(/* id 6, wireType 2 =*/ 50)
          .string(message.FieldCompareOperator)
      if (
        message.FieldValue != null &&
        Object.hasOwnProperty.call(message, 'FieldValue')
      )
        writer.uint32(/* id 7, wireType 2 =*/ 58).string(message.FieldValue)
      return writer
    }

    /**
     * Decodes a WhereItem message from the specified reader or buffer.
     * @function decode
     * @memberof crud.WhereItem
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {crud.WhereItem} WhereItem
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    WhereItem.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.crud.WhereItem()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 5:
            message.Field = reader.string()
            break
          case 6:
            message.FieldCompareOperator = reader.string()
            break
          case 7:
            message.FieldValue = reader.string()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return WhereItem
  })()

  crud.QueryParam = (function () {
    /**
     * Properties of a QueryParam.
     * @memberof crud
     * @interface IQueryParam
     * @property {Array.<string>|null} [ResultColumn] 想要的结果字段名，不填写为全要
     * @property {Array.<string>|null} [TimeColumn] Query时需要处理时区的字段,单数为字段名，双数为需要加减的的分钟数
     * 需要此功能必须把所有的结果列名填写在ResultColumn，否则无法处理
     * TimeColumn = ['ActionTime','480'，‘RecvTime','-60']
     * @property {Array.<crud.IWhereItem>|null} [Where] where额外条件项,只支持 and
     * @property {number|null} [Limit] 分页，需要的数据，如果Limit=0 and Offset=0,则视为不需要分页
     * @property {number|null} [Offset] 分页，从什么位置开始，如果Limit=0 and
     * Offset!=0,为从Offset的位置开始的所有数据
     * @property {Array.<string>|null} [OrderBy] 排序条件，每项为sql的order by 条件
     * select * from a order by col1 asc,col2 desc
     * OrderBy=["col1 asc","col2 desc"]
     * @property {number|null} [Batch] QueryBatch调用时,指定一次返回的数据量，不指定时后端按每次一个返回
     */

    /**
     * Constructs a new QueryParam.
     * @memberof crud
     * @classdesc 查询条件
     * @implements IQueryParam
     * @constructor
     * @param {crud.IQueryParam=} [properties] Properties to set
     */
    function QueryParam(properties) {
      this.ResultColumn = []
      this.TimeColumn = []
      this.Where = []
      this.OrderBy = []
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 想要的结果字段名，不填写为全要
     * @member {Array.<string>} ResultColumn
     * @memberof crud.QueryParam
     * @instance
     */
    QueryParam.prototype.ResultColumn = $util.emptyArray

    /**
     * Query时需要处理时区的字段,单数为字段名，双数为需要加减的的分钟数
     * 需要此功能必须把所有的结果列名填写在ResultColumn，否则无法处理
     * TimeColumn = ['ActionTime','480'，‘RecvTime','-60']
     * @member {Array.<string>} TimeColumn
     * @memberof crud.QueryParam
     * @instance
     */
    QueryParam.prototype.TimeColumn = $util.emptyArray

    /**
     * where额外条件项,只支持 and
     * @member {Array.<crud.IWhereItem>} Where
     * @memberof crud.QueryParam
     * @instance
     */
    QueryParam.prototype.Where = $util.emptyArray

    /**
     * 分页，需要的数据，如果Limit=0 and Offset=0,则视为不需要分页
     * @member {number} Limit
     * @memberof crud.QueryParam
     * @instance
     */
    QueryParam.prototype.Limit = 0

    /**
     * 分页，从什么位置开始，如果Limit=0 and
     * Offset!=0,为从Offset的位置开始的所有数据
     * @member {number} Offset
     * @memberof crud.QueryParam
     * @instance
     */
    QueryParam.prototype.Offset = 0

    /**
     * 排序条件，每项为sql的order by 条件
     * select * from a order by col1 asc,col2 desc
     * OrderBy=["col1 asc","col2 desc"]
     * @member {Array.<string>} OrderBy
     * @memberof crud.QueryParam
     * @instance
     */
    QueryParam.prototype.OrderBy = $util.emptyArray

    /**
     * QueryBatch调用时,指定一次返回的数据量，不指定时后端按每次一个返回
     * @member {number} Batch
     * @memberof crud.QueryParam
     * @instance
     */
    QueryParam.prototype.Batch = 0

    /**
     * Encodes the specified QueryParam message. Does not implicitly {@link crud.QueryParam.verify|verify} messages.
     * @function encode
     * @memberof crud.QueryParam
     * @static
     * @param {crud.IQueryParam} message QueryParam message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    QueryParam.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (message.ResultColumn != null && message.ResultColumn.length)
        for (let i = 0; i < message.ResultColumn.length; ++i)
          writer
            .uint32(/* id 1, wireType 2 =*/ 10)
            .string(message.ResultColumn[i])
      if (message.TimeColumn != null && message.TimeColumn.length)
        for (let i = 0; i < message.TimeColumn.length; ++i)
          writer
            .uint32(/* id 2, wireType 2 =*/ 18)
            .string(message.TimeColumn[i])
      if (message.Where != null && message.Where.length)
        for (let i = 0; i < message.Where.length; ++i)
          $root.crud.WhereItem.encode(
            message.Where[i],
            writer.uint32(/* id 5, wireType 2 =*/ 42).fork(),
          ).ldelim()
      if (message.Limit != null && Object.hasOwnProperty.call(message, 'Limit'))
        writer.uint32(/* id 6, wireType 0 =*/ 48).uint32(message.Limit)
      if (
        message.Offset != null &&
        Object.hasOwnProperty.call(message, 'Offset')
      )
        writer.uint32(/* id 7, wireType 0 =*/ 56).uint32(message.Offset)
      if (message.OrderBy != null && message.OrderBy.length)
        for (let i = 0; i < message.OrderBy.length; ++i)
          writer.uint32(/* id 8, wireType 2 =*/ 66).string(message.OrderBy[i])
      if (message.Batch != null && Object.hasOwnProperty.call(message, 'Batch'))
        writer.uint32(/* id 9, wireType 0 =*/ 72).uint32(message.Batch)
      return writer
    }

    /**
     * Decodes a QueryParam message from the specified reader or buffer.
     * @function decode
     * @memberof crud.QueryParam
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {crud.QueryParam} QueryParam
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    QueryParam.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.crud.QueryParam()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            if (!(message.ResultColumn && message.ResultColumn.length))
              message.ResultColumn = []
            message.ResultColumn.push(reader.string())
            break
          case 2:
            if (!(message.TimeColumn && message.TimeColumn.length))
              message.TimeColumn = []
            message.TimeColumn.push(reader.string())
            break
          case 5:
            if (!(message.Where && message.Where.length)) message.Where = []
            message.Where.push(
              $root.crud.WhereItem.decode(reader, reader.uint32()),
            )
            break
          case 6:
            message.Limit = reader.uint32()
            break
          case 7:
            message.Offset = reader.uint32()
            break
          case 8:
            if (!(message.OrderBy && message.OrderBy.length))
              message.OrderBy = []
            message.OrderBy.push(reader.string())
            break
          case 9:
            message.Batch = reader.uint32()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return QueryParam
  })()

  crud.PrivilegeParam = (function () {
    /**
     * Properties of a PrivilegeParam.
     * @memberof crud
     * @interface IPrivilegeParam
     * @property {string|null} [System] 系统名称
     * @property {string|null} [SessionID] SessionID
     * @property {crud.IQueryParam|null} [QueryCondition] 查询条件
     */

    /**
     * Constructs a new PrivilegeParam.
     * @memberof crud
     * @classdesc 取得用户有权限的数据
     * @implements IPrivilegeParam
     * @constructor
     * @param {crud.IPrivilegeParam=} [properties] Properties to set
     */
    function PrivilegeParam(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 系统名称
     * @member {string} System
     * @memberof crud.PrivilegeParam
     * @instance
     */
    PrivilegeParam.prototype.System = ''

    /**
     * SessionID
     * @member {string} SessionID
     * @memberof crud.PrivilegeParam
     * @instance
     */
    PrivilegeParam.prototype.SessionID = ''

    /**
     * 查询条件
     * @member {crud.IQueryParam|null|undefined} QueryCondition
     * @memberof crud.PrivilegeParam
     * @instance
     */
    PrivilegeParam.prototype.QueryCondition = null

    /**
     * Encodes the specified PrivilegeParam message. Does not implicitly {@link crud.PrivilegeParam.verify|verify} messages.
     * @function encode
     * @memberof crud.PrivilegeParam
     * @static
     * @param {crud.IPrivilegeParam} message PrivilegeParam message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    PrivilegeParam.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (
        message.System != null &&
        Object.hasOwnProperty.call(message, 'System')
      )
        writer.uint32(/* id 1, wireType 2 =*/ 10).string(message.System)
      if (
        message.SessionID != null &&
        Object.hasOwnProperty.call(message, 'SessionID')
      )
        writer.uint32(/* id 3, wireType 2 =*/ 26).string(message.SessionID)
      if (
        message.QueryCondition != null &&
        Object.hasOwnProperty.call(message, 'QueryCondition')
      )
        $root.crud.QueryParam.encode(
          message.QueryCondition,
          writer.uint32(/* id 5, wireType 2 =*/ 42).fork(),
        ).ldelim()
      return writer
    }

    /**
     * Decodes a PrivilegeParam message from the specified reader or buffer.
     * @function decode
     * @memberof crud.PrivilegeParam
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {crud.PrivilegeParam} PrivilegeParam
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    PrivilegeParam.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.crud.PrivilegeParam()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.System = reader.string()
            break
          case 3:
            message.SessionID = reader.string()
            break
          case 5:
            message.QueryCondition = $root.crud.QueryParam.decode(
              reader,
              reader.uint32(),
            )
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return PrivilegeParam
  })()

  return crud
})())

export { $root as default }

import * as $protobuf from 'protobufjs'
/** Namespace image. */
export namespace image {
  /** Properties of a DbImageList. */
  interface IDbImageList {
    /** 批次号，从0开始 */
    BatchNo?: number | null

    /** rows offset,从0开始 */
    RowsOffset?: number | null

    /** 本批次数据 */
    Rows?: image.IDbImage[] | null
  }

  /** DbImage list */
  class DbImageList implements IDbImageList {
    /**
     * Constructs a new DbImageList.
     * @param [properties] Properties to set
     */
    constructor(properties?: image.IDbImageList)

    /** 批次号，从0开始 */
    public BatchNo: number

    /** rows offset,从0开始 */
    public RowsOffset: number

    /** 本批次数据 */
    public Rows: image.IDbImage[]

    /**
     * Encodes the specified DbImageList message. Does not implicitly {@link image.DbImageList.verify|verify} messages.
     * @param message DbImageList message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: image.IDbImage<PERSON>ist,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a DbImageList message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns DbImageList
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): image.DbImageList
  }

  /** Properties of a DbImage. */
  interface IDbImage {
    /**
     * @db uuid primary key
     * 行ID
     */
    RID?: string | null

    /**
     * @db uuid REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 所属的群组
     */
    OrgRID?: string | null

    /**
     * @db text
     * 文件名
     */
    FileName?: string | null

    /**
     * @db text
     * 文件内容,经过base64编码,就是html img的dataurl
     */
    FileContent?: string | null

    /**
     * @db text
     * 文件内容的hash=base64(sha256(file_content_binary)
     */
    Hash?: string | null

    /**
     * @db uuid
     * 添加的用户RID
     */
    AddUserRID?: string | null

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     */
    UpdatedAt?: string | null

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */
    UpdatedDC?: string | null
  }

  /** 用户的一些头像图片数据,地图点icon等 */
  class DbImage implements IDbImage {
    /**
     * Constructs a new DbImage.
     * @rpc crud
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbImageOrgRID on DbImage USING hash (OrgRID)
     * @param [properties] Properties to set
     */
    constructor(properties?: image.IDbImage)

    /**
     * @db uuid primary key
     * 行ID
     */
    public RID: string

    /**
     * @db uuid REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 所属的群组
     */
    public OrgRID: string

    /**
     * @db text
     * 文件名
     */
    public FileName: string

    /**
     * @db text
     * 文件内容,经过base64编码,就是html img的dataurl
     */
    public FileContent: string

    /**
     * @db text
     * 文件内容的hash=base64(sha256(file_content_binary)
     */
    public Hash: string

    /**
     * @db uuid
     * 添加的用户RID
     */
    public AddUserRID: string

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     */
    public UpdatedAt: string

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */
    public UpdatedDC: string

    /**
     * Encodes the specified DbImage message. Does not implicitly {@link image.DbImage.verify|verify} messages.
     * @param message DbImage message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: image.IDbImage,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a DbImage message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns DbImage
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): image.DbImage
  }
}

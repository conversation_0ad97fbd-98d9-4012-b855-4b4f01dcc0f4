/*eslint-disable block-scoped-var, id-length, no-control-regex, no-magic-numbers, no-prototype-builtins, no-redeclare, no-shadow, no-var, sort-vars*/
import * as $protobuf from 'protobufjs/minimal'

// Common aliases
const $Reader = $protobuf.Reader,
  $Writer = $protobuf.Writer,
  $util = $protobuf.util

// Exported root namespace
const $root = $protobuf.roots['default'] || ($protobuf.roots['default'] = {})

export const user = ($root.user = (() => {
  /**
   * Namespace user.
   * @exports user
   * @namespace
   */
  const user = $root.user || {}

  user.DbUserSessionList = (function () {
    /**
     * Properties of a DbUserSessionList.
     * @memberof user
     * @interface IDbUserSessionList
     * @property {number|null} [BatchNo] 批次号，从0开始
     * @property {number|null} [RowsOffset] rows offset,从0开始
     * @property {Array.<user.IDbUserSession>|null} [Rows] 本批次数据
     */

    /**
     * Constructs a new DbUserSessionList.
     * @memberof user
     * @classdesc DbUserSession list
     * @implements IDbUserSessionList
     * @constructor
     * @param {user.IDbUserSessionList=} [properties] Properties to set
     */
    function DbUserSessionList(properties) {
      this.Rows = []
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 批次号，从0开始
     * @member {number} BatchNo
     * @memberof user.DbUserSessionList
     * @instance
     */
    DbUserSessionList.prototype.BatchNo = 0

    /**
     * rows offset,从0开始
     * @member {number} RowsOffset
     * @memberof user.DbUserSessionList
     * @instance
     */
    DbUserSessionList.prototype.RowsOffset = 0

    /**
     * 本批次数据
     * @member {Array.<user.IDbUserSession>} Rows
     * @memberof user.DbUserSessionList
     * @instance
     */
    DbUserSessionList.prototype.Rows = $util.emptyArray

    /**
     * Encodes the specified DbUserSessionList message. Does not implicitly {@link user.DbUserSessionList.verify|verify} messages.
     * @function encode
     * @memberof user.DbUserSessionList
     * @static
     * @param {user.IDbUserSessionList} message DbUserSessionList message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    DbUserSessionList.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (
        message.BatchNo != null &&
        Object.hasOwnProperty.call(message, 'BatchNo')
      )
        writer.uint32(/* id 1, wireType 0 =*/ 8).uint32(message.BatchNo)
      if (
        message.RowsOffset != null &&
        Object.hasOwnProperty.call(message, 'RowsOffset')
      )
        writer.uint32(/* id 2, wireType 0 =*/ 16).uint32(message.RowsOffset)
      if (message.Rows != null && message.Rows.length)
        for (let i = 0; i < message.Rows.length; ++i)
          $root.user.DbUserSession.encode(
            message.Rows[i],
            writer.uint32(/* id 3, wireType 2 =*/ 26).fork(),
          ).ldelim()
      return writer
    }

    /**
     * Decodes a DbUserSessionList message from the specified reader or buffer.
     * @function decode
     * @memberof user.DbUserSessionList
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {user.DbUserSessionList} DbUserSessionList
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    DbUserSessionList.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.user.DbUserSessionList()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.BatchNo = reader.uint32()
            break
          case 2:
            message.RowsOffset = reader.uint32()
            break
          case 3:
            if (!(message.Rows && message.Rows.length)) message.Rows = []
            message.Rows.push(
              $root.user.DbUserSession.decode(reader, reader.uint32()),
            )
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return DbUserSessionList
  })()

  user.DbUserSession = (function () {
    /**
     * Properties of a DbUserSession.
     * @memberof user
     * @interface IDbUserSession
     * @property {string|null} [RID] @db uuid primary key
     * 行ID,session rid
     * @property {string|null} [OrgRID] @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 用户所属的群组
     * @property {string|null} [UserRID] @db uuid not null REFERENCES DbUser(RID) ON DELETE CASCADE
     * 用户rid
     * @property {number|null} [SessionType] @db int
     * session类型 0:web client
     * @property {string|null} [LoginTime] @db timestamp not null default now_utc()
     * 用户登录的时间
     * @property {string|null} [LoginInfo] @db jsonb not null default  '{}'::jsonb
     * 用户登录信息
     * @property {string|null} [UpdatedAt] @db timestamp not null default now_utc()
     * 数据最后修改时间
     * @property {string|null} [UpdatedDC] @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */

    /**
     * Constructs a new DbUserSession.
     * @memberof user
     * @classdesc 用户session表
     * @rpc crud
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbUserSessionOrgRID on DbUserSession USING hash(OrgRID)
     * @implements IDbUserSession
     * @constructor
     * @param {user.IDbUserSession=} [properties] Properties to set
     */
    function DbUserSession(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * @db uuid primary key
     * 行ID,session rid
     * @member {string} RID
     * @memberof user.DbUserSession
     * @instance
     */
    DbUserSession.prototype.RID = ''

    /**
     * @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 用户所属的群组
     * @member {string} OrgRID
     * @memberof user.DbUserSession
     * @instance
     */
    DbUserSession.prototype.OrgRID = ''

    /**
     * @db uuid not null REFERENCES DbUser(RID) ON DELETE CASCADE
     * 用户rid
     * @member {string} UserRID
     * @memberof user.DbUserSession
     * @instance
     */
    DbUserSession.prototype.UserRID = ''

    /**
     * @db int
     * session类型 0:web client
     * @member {number} SessionType
     * @memberof user.DbUserSession
     * @instance
     */
    DbUserSession.prototype.SessionType = 0

    /**
     * @db timestamp not null default now_utc()
     * 用户登录的时间
     * @member {string} LoginTime
     * @memberof user.DbUserSession
     * @instance
     */
    DbUserSession.prototype.LoginTime = ''

    /**
     * @db jsonb not null default  '{}'::jsonb
     * 用户登录信息
     * @member {string} LoginInfo
     * @memberof user.DbUserSession
     * @instance
     */
    DbUserSession.prototype.LoginInfo = ''

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     * @member {string} UpdatedAt
     * @memberof user.DbUserSession
     * @instance
     */
    DbUserSession.prototype.UpdatedAt = ''

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     * @member {string} UpdatedDC
     * @memberof user.DbUserSession
     * @instance
     */
    DbUserSession.prototype.UpdatedDC = ''

    /**
     * Encodes the specified DbUserSession message. Does not implicitly {@link user.DbUserSession.verify|verify} messages.
     * @function encode
     * @memberof user.DbUserSession
     * @static
     * @param {user.IDbUserSession} message DbUserSession message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    DbUserSession.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (message.RID != null && Object.hasOwnProperty.call(message, 'RID'))
        writer.uint32(/* id 1, wireType 2 =*/ 10).string(message.RID)
      if (
        message.OrgRID != null &&
        Object.hasOwnProperty.call(message, 'OrgRID')
      )
        writer.uint32(/* id 2, wireType 2 =*/ 18).string(message.OrgRID)
      if (
        message.UserRID != null &&
        Object.hasOwnProperty.call(message, 'UserRID')
      )
        writer.uint32(/* id 3, wireType 2 =*/ 26).string(message.UserRID)
      if (
        message.SessionType != null &&
        Object.hasOwnProperty.call(message, 'SessionType')
      )
        writer.uint32(/* id 4, wireType 0 =*/ 32).int32(message.SessionType)
      if (
        message.LoginTime != null &&
        Object.hasOwnProperty.call(message, 'LoginTime')
      )
        writer.uint32(/* id 10, wireType 2 =*/ 82).string(message.LoginTime)
      if (
        message.LoginInfo != null &&
        Object.hasOwnProperty.call(message, 'LoginInfo')
      )
        writer.uint32(/* id 11, wireType 2 =*/ 90).string(message.LoginInfo)
      if (
        message.UpdatedAt != null &&
        Object.hasOwnProperty.call(message, 'UpdatedAt')
      )
        writer.uint32(/* id 14, wireType 2 =*/ 114).string(message.UpdatedAt)
      if (
        message.UpdatedDC != null &&
        Object.hasOwnProperty.call(message, 'UpdatedDC')
      )
        writer.uint32(/* id 15, wireType 2 =*/ 122).string(message.UpdatedDC)
      return writer
    }

    /**
     * Decodes a DbUserSession message from the specified reader or buffer.
     * @function decode
     * @memberof user.DbUserSession
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {user.DbUserSession} DbUserSession
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    DbUserSession.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.user.DbUserSession()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.RID = reader.string()
            break
          case 2:
            message.OrgRID = reader.string()
            break
          case 3:
            message.UserRID = reader.string()
            break
          case 4:
            message.SessionType = reader.int32()
            break
          case 10:
            message.LoginTime = reader.string()
            break
          case 11:
            message.LoginInfo = reader.string()
            break
          case 14:
            message.UpdatedAt = reader.string()
            break
          case 15:
            message.UpdatedDC = reader.string()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return DbUserSession
  })()

  return user
})())

export { $root as default }

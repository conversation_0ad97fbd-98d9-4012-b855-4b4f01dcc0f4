import { Writer } from 'protobufjs'
import { rpcCon } from 'jsyrpc'
import { ICallOption } from 'jsyrpc'

import { TRpcStream } from 'jsyrpc'
import * as BysdbY from '@ygen/bysdb'
import * as YrpcmsgY from '@ygen/yrpcmsg'
import * as BysApiY from '@ygen/bys.api'
import * as UserPermissionY from '@ygen/userPermission'
import * as UserPermissionListY from '@ygen/userPermission.list'
import * as ControllerY from '@ygen/controller'

export function RpcBysApiQueryMarkerQueueNo(
  req: BysApiY.doc.IMarkerQueueNoInfo,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysApiY.doc.MarkerQueueNoInfo.encode(req)
  let reqData = w.finish()

  const api = '/doc.RpcBysApi/QueryMarkerQueueNo'
  const v = 0

  return rpcCon.UnaryCall(
    reqData,
    api,
    v,
    BysApiY.doc.MarkerQueueNoInfo,
    callOpt,
  )
}
export function RpcBysApiCancelEmergency(
  req: BysApiY.doc.IMarkerInfo,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysApiY.doc.MarkerInfo.encode(req)
  let reqData = w.finish()

  const api = '/doc.RpcBysApi/CancelEmergency'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, YrpcmsgY.yrpcmsg.Yempty, callOpt)
}
export function RpcBysApiSend0xD0(
  req: BysApiY.doc.IMarkerInfo,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysApiY.doc.MarkerInfo.encode(req)
  let reqData = w.finish()

  const api = '/doc.RpcBysApi/Send0xD0'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, YrpcmsgY.yrpcmsg.Yempty, callOpt)
}
export function RpcBysApiSendControllerCmd(
  req: BysApiY.doc.IControllerCmd,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysApiY.doc.ControllerCmd.encode(req)
  let reqData = w.finish()

  const api = '/doc.RpcBysApi/SendControllerCmd'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, YrpcmsgY.yrpcmsg.Yempty, callOpt)
}
export function RpcBysApiUpdateMarkerParamter(
  req: BysApiY.doc.IUpdataMarkerParam,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysApiY.doc.UpdataMarkerParam.encode(req)
  let reqData = w.finish()

  const api = '/doc.RpcBysApi/UpdateMarkerParamter'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, YrpcmsgY.yrpcmsg.Yempty, callOpt)
}
export function RpcBysApiPingController(
  req: BysApiY.doc.IControllerCmd,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysApiY.doc.ControllerCmd.encode(req)
  let reqData = w.finish()

  const api = '/doc.RpcBysApi/PingController'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, YrpcmsgY.yrpcmsg.Yempty, callOpt)
}
export function RpcBysApiGetRolePermissions(
  req: UserPermissionY.user.IDbRole,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = UserPermissionY.user.DbRole.encode(req)
  let reqData = w.finish()

  const api = '/doc.RpcBysApi/GetRolePermissions'
  const v = 0

  return rpcCon.UnaryCall(
    reqData,
    api,
    v,
    UserPermissionListY.user.DbRolePermissionList,
    callOpt,
  )
}
export function RpcBysApiUpdateControllerServerAddr(
  req: BysApiY.doc.IControllerNewServerAddr,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysApiY.doc.ControllerNewServerAddr.encode(req)
  let reqData = w.finish()

  const api = '/doc.RpcBysApi/UpdateControllerServerAddr'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, YrpcmsgY.yrpcmsg.Yempty, callOpt)
}
export function RpcBysApiQueryMarkerOrControllerLinkList(
  req: BysApiY.doc.IMarkerOrControllerId,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysApiY.doc.MarkerOrControllerId.encode(req)
  let reqData = w.finish()

  const api = '/doc.RpcBysApi/QueryMarkerOrControllerLinkList'
  const v = 0

  return rpcCon.UnaryCall(
    reqData,
    api,
    v,
    BysApiY.doc.MarkerOrControllerId,
    callOpt,
  )
}
export function RpcBysApiMarkerRemoteKillOrActive(
  req: ControllerY.bysproto.IBShutDown,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = ControllerY.bysproto.BShutDown.encode(req)
  let reqData = w.finish()

  const api = '/doc.RpcBysApi/MarkerRemoteKillOrActive'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, ControllerY.bysproto.Bmsg, callOpt)
}
export function RpcBysApiMarkerUploadNFCRecord(
  req: BysdbY.bysdb.IDbMarkerPatrolHistory,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = BysdbY.bysdb.DbMarkerPatrolHistory.encode(req)
  let reqData = w.finish()

  const api = '/doc.RpcBysApi/MarkerUploadNFCRecord'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, YrpcmsgY.yrpcmsg.Yempty, callOpt)
}

export function RpcBysApiOnlineController(
  req: YrpcmsgY.yrpcmsg.Yempty,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/doc.RpcBysApi/OnlineController'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    YrpcmsgY.yrpcmsg.Yempty,
    BysApiY.doc.OnlineControllerInfo,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function RpcBysApiNFCPatrolStatistics(
  req: BysApiY.doc.PatrolStatisticsQuery,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/doc.RpcBysApi/NFCPatrolStatistics'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    BysApiY.doc.PatrolStatisticsQuery,
    BysApiY.doc.PatrolStatisticsQueryResult,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function RpcBysApiMarkerLatestInfo(
  req: BysApiY.doc.IMarkerInfo,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/doc.RpcBysApi/MarkerLatestInfo'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    8,
    BysApiY.doc.MarkerInfo,
    BysApiY.doc.MarkerInfo,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export class RpcBysApi {
  public static QueryMarkerQueueNo(
    req: BysApiY.doc.IMarkerQueueNoInfo,
    callOpt?: ICallOption,
  ): boolean {
    return RpcBysApiQueryMarkerQueueNo(req, callOpt)
  }
  public static CancelEmergency(
    req: BysApiY.doc.IMarkerInfo,
    callOpt?: ICallOption,
  ): boolean {
    return RpcBysApiCancelEmergency(req, callOpt)
  }
  public static Send0xD0(
    req: BysApiY.doc.IMarkerInfo,
    callOpt?: ICallOption,
  ): boolean {
    return RpcBysApiSend0xD0(req, callOpt)
  }
  public static SendControllerCmd(
    req: BysApiY.doc.IControllerCmd,
    callOpt?: ICallOption,
  ): boolean {
    return RpcBysApiSendControllerCmd(req, callOpt)
  }
  public static UpdateMarkerParamter(
    req: BysApiY.doc.IUpdataMarkerParam,
    callOpt?: ICallOption,
  ): boolean {
    return RpcBysApiUpdateMarkerParamter(req, callOpt)
  }
  public static PingController(
    req: BysApiY.doc.IControllerCmd,
    callOpt?: ICallOption,
  ): boolean {
    return RpcBysApiPingController(req, callOpt)
  }
  public static GetRolePermissions(
    req: UserPermissionY.user.IDbRole,
    callOpt?: ICallOption,
  ): boolean {
    return RpcBysApiGetRolePermissions(req, callOpt)
  }
  public static UpdateControllerServerAddr(
    req: BysApiY.doc.IControllerNewServerAddr,
    callOpt?: ICallOption,
  ): boolean {
    return RpcBysApiUpdateControllerServerAddr(req, callOpt)
  }
  public static QueryMarkerOrControllerLinkList(
    req: BysApiY.doc.IMarkerOrControllerId,
    callOpt?: ICallOption,
  ): boolean {
    return RpcBysApiQueryMarkerOrControllerLinkList(req, callOpt)
  }
  public static MarkerRemoteKillOrActive(
    req: ControllerY.bysproto.IBShutDown,
    callOpt?: ICallOption,
  ): boolean {
    return RpcBysApiMarkerRemoteKillOrActive(req, callOpt)
  }
  public static MarkerUploadNFCRecord(
    req: BysdbY.bysdb.IDbMarkerPatrolHistory,
    callOpt?: ICallOption,
  ): boolean {
    return RpcBysApiMarkerUploadNFCRecord(req, callOpt)
  }
  public static OnlineController(
    req: YrpcmsgY.yrpcmsg.Yempty,
    callOpt?: ICallOption,
  ): TRpcStream {
    return RpcBysApiOnlineController(req, callOpt)
  }
  public static NFCPatrolStatistics(
    req: BysApiY.doc.PatrolStatisticsQuery,
    callOpt?: ICallOption,
  ): TRpcStream {
    return RpcBysApiNFCPatrolStatistics(req, callOpt)
  }
  public static MarkerLatestInfo(
    req: BysApiY.doc.IMarkerInfo,
    callOpt?: ICallOption,
  ): TRpcStream {
    return RpcBysApiMarkerLatestInfo(req, callOpt)
  }
}
export default {
  RpcBysApi,
  RpcBysApiQueryMarkerQueueNo,
  RpcBysApiCancelEmergency,
  RpcBysApiSend0xD0,
  RpcBysApiSendControllerCmd,
  RpcBysApiUpdateMarkerParamter,
  RpcBysApiPingController,
  RpcBysApiGetRolePermissions,
  RpcBysApiUpdateControllerServerAddr,
  RpcBysApiQueryMarkerOrControllerLinkList,
  RpcBysApiMarkerRemoteKillOrActive,
  RpcBysApiMarkerUploadNFCRecord,
  RpcBysApiOnlineController,
  RpcBysApiNFCPatrolStatistics,
  RpcBysApiMarkerLatestInfo,
}

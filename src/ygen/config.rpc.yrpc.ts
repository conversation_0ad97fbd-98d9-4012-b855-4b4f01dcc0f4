import { Writer } from 'protobufjs'
import { rpcCon } from 'jsyrpc'
import { ICallOption } from 'jsyrpc'

import { TRpcStream } from 'jsyrpc'
import * as ConfigListY from '@ygen/config.list'
import * as ConfigY from '@ygen/config'
import * as CrudY from '@ygen/crud'

export function RpcDbConfigInsert(
  req: ConfigY.config.IDbConfig,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = ConfigY.config.DbConfig.encode(req)
  let reqData = w.finish()

  const api = '/config.RpcDbConfig/Insert'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function RpcDbConfigUpdate(
  req: ConfigY.config.IDbConfig,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = ConfigY.config.DbConfig.encode(req)
  let reqData = w.finish()

  const api = '/config.RpcDbConfig/Update'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function RpcDbConfigPartialUpdate(
  req: ConfigY.config.IDbConfig,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = ConfigY.config.DbConfig.encode(req)
  let reqData = w.finish()

  const api = '/config.RpcDbConfig/PartialUpdate'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function RpcDbConfigDelete(
  req: ConfigY.config.IDbConfig,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = ConfigY.config.DbConfig.encode(req)
  let reqData = w.finish()

  const api = '/config.RpcDbConfig/Delete'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function RpcDbConfigSelectOne(
  req: CrudY.crud.IDMLParam,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = CrudY.crud.DMLParam.encode(req)
  let reqData = w.finish()

  const api = '/config.RpcDbConfig/SelectOne'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, ConfigY.config.DbConfig, callOpt)
}

export function RpcDbConfigSelectMany(
  req: CrudY.crud.DMLParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/config.RpcDbConfig/SelectMany'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.DMLParam,
    ConfigY.config.DbConfig,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function RpcDbConfigQuery(
  req: CrudY.crud.QueryParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/config.RpcDbConfig/Query'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.QueryParam,
    ConfigY.config.DbConfig,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function RpcDbConfigQueryBatch(
  req: CrudY.crud.QueryParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/config.RpcDbConfig/QueryBatch'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.QueryParam,
    ConfigListY.config.DbConfigList,
    callOpt,
  )
  r.sendFirst(req)
  return r
}

export class RpcDbConfig {
  public static Insert(
    req: ConfigY.config.IDbConfig,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbConfigInsert(req, callOpt)
  }
  public static Update(
    req: ConfigY.config.IDbConfig,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbConfigUpdate(req, callOpt)
  }
  public static PartialUpdate(
    req: ConfigY.config.IDbConfig,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbConfigPartialUpdate(req, callOpt)
  }
  public static Delete(
    req: ConfigY.config.IDbConfig,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbConfigDelete(req, callOpt)
  }
  public static SelectOne(
    req: CrudY.crud.IDMLParam,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbConfigSelectOne(req, callOpt)
  }
  public static SelectMany(
    req: CrudY.crud.DMLParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return RpcDbConfigSelectMany(req, callOpt)
  }
  public static Query(
    req: CrudY.crud.QueryParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return RpcDbConfigQuery(req, callOpt)
  }
  public static QueryBatch(
    req: CrudY.crud.QueryParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return RpcDbConfigQueryBatch(req, callOpt)
  }
}
export default {
  RpcDbConfig,
  RpcDbConfigInsert,
  RpcDbConfigUpdate,
  RpcDbConfigPartialUpdate,
  RpcDbConfigDelete,
  RpcDbConfigSelectOne,
  RpcDbConfigSelectMany,
  RpcDbConfigQuery,
  RpcDbConfigQueryBatch,
}

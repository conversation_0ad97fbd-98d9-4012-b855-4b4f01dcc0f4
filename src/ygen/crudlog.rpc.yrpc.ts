import { Writer } from 'protobufjs'
import { rpcCon } from 'jsyrpc'
import { ICallOption } from 'jsyrpc'

import { TRpcStream } from 'jsyrpc'
import * as CrudY from '@ygen/crud'
import * as CrudlogListY from '@ygen/crudlog.list'
import * as CrudlogY from '@ygen/crudlog'

export function RpcDbCrudLogInsert(
  req: CrudlogY.crudlog.IDbCrudLog,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = CrudlogY.crudlog.DbCrudLog.encode(req)
  let reqData = w.finish()

  const api = '/crudlog.RpcDbCrudLog/Insert'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}

export function RpcDbCrudLogQuery(
  req: CrudY.crud.QueryParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/crudlog.RpcDbCrudLog/Query'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.QueryParam,
    CrudlogY.crudlog.DbCrudLog,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function RpcDbCrudLogQueryBatch(
  req: CrudY.crud.QueryParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/crudlog.RpcDbCrudLog/QueryBatch'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.QueryParam,
    CrudlogListY.crudlog.DbCrudLogList,
    callOpt,
  )
  r.sendFirst(req)
  return r
}

export class RpcDbCrudLog {
  public static Insert(
    req: CrudlogY.crudlog.IDbCrudLog,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbCrudLogInsert(req, callOpt)
  }
  public static Query(
    req: CrudY.crud.QueryParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return RpcDbCrudLogQuery(req, callOpt)
  }
  public static QueryBatch(
    req: CrudY.crud.QueryParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return RpcDbCrudLogQueryBatch(req, callOpt)
  }
}

export function PrpcDbCrudLogInsert(
  req: CrudlogY.crudlog.IDbCrudLog,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = CrudlogY.crudlog.DbCrudLog.encode(req)
  let reqData = w.finish()

  const api = '/crudlog.PrpcDbCrudLog/Insert'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}

export function PrpcDbCrudLogQuery(
  req: CrudY.crud.PrivilegeParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/crudlog.PrpcDbCrudLog/Query'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.PrivilegeParam,
    CrudlogY.crudlog.DbCrudLog,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function PrpcDbCrudLogQueryBatch(
  req: CrudY.crud.PrivilegeParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/crudlog.PrpcDbCrudLog/QueryBatch'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.PrivilegeParam,
    CrudlogListY.crudlog.DbCrudLogList,
    callOpt,
  )
  r.sendFirst(req)
  return r
}

export class PrpcDbCrudLog {
  public static Insert(
    req: CrudlogY.crudlog.IDbCrudLog,
    callOpt?: ICallOption,
  ): boolean {
    return PrpcDbCrudLogInsert(req, callOpt)
  }
  public static Query(
    req: CrudY.crud.PrivilegeParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return PrpcDbCrudLogQuery(req, callOpt)
  }
  public static QueryBatch(
    req: CrudY.crud.PrivilegeParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return PrpcDbCrudLogQueryBatch(req, callOpt)
  }
}
export default {
  RpcDbCrudLog,
  RpcDbCrudLogInsert,
  RpcDbCrudLogQuery,
  RpcDbCrudLogQueryBatch,
  PrpcDbCrudLog,
  PrpcDbCrudLogInsert,
  PrpcDbCrudLogQuery,
  PrpcDbCrudLogQueryBatch,
}

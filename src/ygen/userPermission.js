/*eslint-disable block-scoped-var, id-length, no-control-regex, no-magic-numbers, no-prototype-builtins, no-redeclare, no-shadow, no-var, sort-vars*/
import * as $protobuf from 'protobufjs/minimal'

// Common aliases
const $Reader = $protobuf.Reader,
  $Writer = $protobuf.Writer,
  $util = $protobuf.util

// Exported root namespace
const $root = $protobuf.roots['default'] || ($protobuf.roots['default'] = {})

export const user = ($root.user = (() => {
  /**
   * Namespace user.
   * @exports user
   * @namespace
   */
  const user = $root.user || {}

  user.DbPermission = (function () {
    /**
     * Properties of a DbPermission.
     * @memberof user
     * @interface IDbPermission
     * @property {string|null} [RID] @db uuid primary key
     * 行ID,permission rid
     * @property {string|null} [PermissionType] @db text not null
     * 权限类别
     * @property {string|null} [PermissionName] @db text not null
     * permission 名称
     * @property {string|null} [PermissionValue] @db text not null
     * permission value
     * @property {string|null} [UpdatedAt] @db timestamp not null default now_utc()
     * 数据最后修改时间
     * @property {string|null} [UpdatedDC] @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */

    /**
     * Constructs a new DbPermission.
     * @memberof user
     * @classdesc 所有的权限信息表,此表信息为预置，一般不给删除
     * @rpc crud
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbPermissionPermissionValue on DbPermission USING hash(PermissionValue);
     * @implements IDbPermission
     * @constructor
     * @param {user.IDbPermission=} [properties] Properties to set
     */
    function DbPermission(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * @db uuid primary key
     * 行ID,permission rid
     * @member {string} RID
     * @memberof user.DbPermission
     * @instance
     */
    DbPermission.prototype.RID = ''

    /**
     * @db text not null
     * 权限类别
     * @member {string} PermissionType
     * @memberof user.DbPermission
     * @instance
     */
    DbPermission.prototype.PermissionType = ''

    /**
     * @db text not null
     * permission 名称
     * @member {string} PermissionName
     * @memberof user.DbPermission
     * @instance
     */
    DbPermission.prototype.PermissionName = ''

    /**
     * @db text not null
     * permission value
     * @member {string} PermissionValue
     * @memberof user.DbPermission
     * @instance
     */
    DbPermission.prototype.PermissionValue = ''

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     * @member {string} UpdatedAt
     * @memberof user.DbPermission
     * @instance
     */
    DbPermission.prototype.UpdatedAt = ''

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     * @member {string} UpdatedDC
     * @memberof user.DbPermission
     * @instance
     */
    DbPermission.prototype.UpdatedDC = ''

    /**
     * Encodes the specified DbPermission message. Does not implicitly {@link user.DbPermission.verify|verify} messages.
     * @function encode
     * @memberof user.DbPermission
     * @static
     * @param {user.IDbPermission} message DbPermission message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    DbPermission.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (message.RID != null && Object.hasOwnProperty.call(message, 'RID'))
        writer.uint32(/* id 1, wireType 2 =*/ 10).string(message.RID)
      if (
        message.PermissionType != null &&
        Object.hasOwnProperty.call(message, 'PermissionType')
      )
        writer.uint32(/* id 3, wireType 2 =*/ 26).string(message.PermissionType)
      if (
        message.PermissionName != null &&
        Object.hasOwnProperty.call(message, 'PermissionName')
      )
        writer.uint32(/* id 4, wireType 2 =*/ 34).string(message.PermissionName)
      if (
        message.PermissionValue != null &&
        Object.hasOwnProperty.call(message, 'PermissionValue')
      )
        writer
          .uint32(/* id 10, wireType 2 =*/ 82)
          .string(message.PermissionValue)
      if (
        message.UpdatedAt != null &&
        Object.hasOwnProperty.call(message, 'UpdatedAt')
      )
        writer.uint32(/* id 14, wireType 2 =*/ 114).string(message.UpdatedAt)
      if (
        message.UpdatedDC != null &&
        Object.hasOwnProperty.call(message, 'UpdatedDC')
      )
        writer.uint32(/* id 15, wireType 2 =*/ 122).string(message.UpdatedDC)
      return writer
    }

    /**
     * Decodes a DbPermission message from the specified reader or buffer.
     * @function decode
     * @memberof user.DbPermission
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {user.DbPermission} DbPermission
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    DbPermission.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.user.DbPermission()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.RID = reader.string()
            break
          case 3:
            message.PermissionType = reader.string()
            break
          case 4:
            message.PermissionName = reader.string()
            break
          case 10:
            message.PermissionValue = reader.string()
            break
          case 14:
            message.UpdatedAt = reader.string()
            break
          case 15:
            message.UpdatedDC = reader.string()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return DbPermission
  })()

  user.DbRole = (function () {
    /**
     * Properties of a DbRole.
     * @memberof user
     * @interface IDbRole
     * @property {string|null} [RID] @db uuid primary key
     * 行ID,role rid
     * @property {string|null} [OrgRID] @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 用户所属的群组
     * @property {string|null} [RoleName] @db text not null
     * role 名称
     * @property {string|null} [Creator] @db uuid --REFERENCES DbUser(RID) ON DELETE set null
     * role 创建者 rid
     * @property {number|null} [IsBuiltIn] @db int
     * 是否是内置的角色，内置角色不能删除
     * @property {string|null} [Setting] @db jsonb not null default  '{}'::jsonb
     * 其它设置，可以在里面扩展需要用到的其它信息
     * @property {number|null} [SortValue] @db int default 100
     * sort value
     * @property {string|null} [UpdatedAt] @db timestamp not null default now_utc()
     * 数据最后修改时间
     * @property {string|null} [UpdatedDC] @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */

    /**
     * Constructs a new DbRole.
     * @memberof user
     * @classdesc 所有的角色信息表
     * @rpc crud pcrud
     * @dbpost insert into DbRole (rid, orgrid, rolename, creator, isbuiltin, setting, sortvalue, updatedat, updateddc) VALUES('*************-5555-5555-************', '00000000-0000-0000-0000-000000000000', 'base', null, 1, '{}'::jsonb, 0, now_utc(), '') ON CONFLICT  DO NOTHING;
     * @dbpost insert into DbRole (rid, orgrid, rolename, creator, isbuiltin, setting, sortvalue, updatedat, updateddc) VALUES('00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', 'admin', null, 1, '{}'::jsonb, 0, now_utc(), '') ON CONFLICT  DO NOTHING;
     * @implements IDbRole
     * @constructor
     * @param {user.IDbRole=} [properties] Properties to set
     */
    function DbRole(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * @db uuid primary key
     * 行ID,role rid
     * @member {string} RID
     * @memberof user.DbRole
     * @instance
     */
    DbRole.prototype.RID = ''

    /**
     * @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 用户所属的群组
     * @member {string} OrgRID
     * @memberof user.DbRole
     * @instance
     */
    DbRole.prototype.OrgRID = ''

    /**
     * @db text not null
     * role 名称
     * @member {string} RoleName
     * @memberof user.DbRole
     * @instance
     */
    DbRole.prototype.RoleName = ''

    /**
     * @db uuid --REFERENCES DbUser(RID) ON DELETE set null
     * role 创建者 rid
     * @member {string} Creator
     * @memberof user.DbRole
     * @instance
     */
    DbRole.prototype.Creator = ''

    /**
     * @db int
     * 是否是内置的角色，内置角色不能删除
     * @member {number} IsBuiltIn
     * @memberof user.DbRole
     * @instance
     */
    DbRole.prototype.IsBuiltIn = 0

    /**
     * @db jsonb not null default  '{}'::jsonb
     * 其它设置，可以在里面扩展需要用到的其它信息
     * @member {string} Setting
     * @memberof user.DbRole
     * @instance
     */
    DbRole.prototype.Setting = ''

    /**
     * @db int default 100
     * sort value
     * @member {number} SortValue
     * @memberof user.DbRole
     * @instance
     */
    DbRole.prototype.SortValue = 0

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     * @member {string} UpdatedAt
     * @memberof user.DbRole
     * @instance
     */
    DbRole.prototype.UpdatedAt = ''

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     * @member {string} UpdatedDC
     * @memberof user.DbRole
     * @instance
     */
    DbRole.prototype.UpdatedDC = ''

    /**
     * Encodes the specified DbRole message. Does not implicitly {@link user.DbRole.verify|verify} messages.
     * @function encode
     * @memberof user.DbRole
     * @static
     * @param {user.IDbRole} message DbRole message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    DbRole.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (message.RID != null && Object.hasOwnProperty.call(message, 'RID'))
        writer.uint32(/* id 1, wireType 2 =*/ 10).string(message.RID)
      if (
        message.OrgRID != null &&
        Object.hasOwnProperty.call(message, 'OrgRID')
      )
        writer.uint32(/* id 2, wireType 2 =*/ 18).string(message.OrgRID)
      if (
        message.RoleName != null &&
        Object.hasOwnProperty.call(message, 'RoleName')
      )
        writer.uint32(/* id 4, wireType 2 =*/ 34).string(message.RoleName)
      if (
        message.Creator != null &&
        Object.hasOwnProperty.call(message, 'Creator')
      )
        writer.uint32(/* id 5, wireType 2 =*/ 42).string(message.Creator)
      if (
        message.IsBuiltIn != null &&
        Object.hasOwnProperty.call(message, 'IsBuiltIn')
      )
        writer.uint32(/* id 6, wireType 0 =*/ 48).int32(message.IsBuiltIn)
      if (
        message.Setting != null &&
        Object.hasOwnProperty.call(message, 'Setting')
      )
        writer.uint32(/* id 9, wireType 2 =*/ 74).string(message.Setting)
      if (
        message.SortValue != null &&
        Object.hasOwnProperty.call(message, 'SortValue')
      )
        writer.uint32(/* id 10, wireType 0 =*/ 80).int32(message.SortValue)
      if (
        message.UpdatedAt != null &&
        Object.hasOwnProperty.call(message, 'UpdatedAt')
      )
        writer.uint32(/* id 14, wireType 2 =*/ 114).string(message.UpdatedAt)
      if (
        message.UpdatedDC != null &&
        Object.hasOwnProperty.call(message, 'UpdatedDC')
      )
        writer.uint32(/* id 15, wireType 2 =*/ 122).string(message.UpdatedDC)
      return writer
    }

    /**
     * Decodes a DbRole message from the specified reader or buffer.
     * @function decode
     * @memberof user.DbRole
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {user.DbRole} DbRole
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    DbRole.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.user.DbRole()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.RID = reader.string()
            break
          case 2:
            message.OrgRID = reader.string()
            break
          case 4:
            message.RoleName = reader.string()
            break
          case 5:
            message.Creator = reader.string()
            break
          case 6:
            message.IsBuiltIn = reader.int32()
            break
          case 9:
            message.Setting = reader.string()
            break
          case 10:
            message.SortValue = reader.int32()
            break
          case 14:
            message.UpdatedAt = reader.string()
            break
          case 15:
            message.UpdatedDC = reader.string()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return DbRole
  })()

  user.DbRolePermission = (function () {
    /**
     * Properties of a DbRolePermission.
     * @memberof user
     * @interface IDbRolePermission
     * @property {string|null} [RID] @db uuid primary key
     * 行ID
     * @property {string|null} [RoleRID] @db uuid not null REFERENCES DbRole(RID) ON DELETE CASCADE
     * role rid
     * @property {string|null} [PermissionRID] @db uuid not null REFERENCES DbPermission(RID) ON DELETE CASCADE
     * permission rid
     * @property {string|null} [Creator] @db uuid --REFERENCES DbUser(RID) ON DELETE set null
     * role 创建者 rid
     * @property {string|null} [UpdatedAt] @db timestamp not null default now_utc()
     * 数据最后修改时间
     * @property {string|null} [UpdatedDC] @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */

    /**
     * Constructs a new DbRolePermission.
     * @memberof user
     * @classdesc 角色权限信息表
     * @rpc crud pcrud
     * @implements IDbRolePermission
     * @constructor
     * @param {user.IDbRolePermission=} [properties] Properties to set
     */
    function DbRolePermission(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * @db uuid primary key
     * 行ID
     * @member {string} RID
     * @memberof user.DbRolePermission
     * @instance
     */
    DbRolePermission.prototype.RID = ''

    /**
     * @db uuid not null REFERENCES DbRole(RID) ON DELETE CASCADE
     * role rid
     * @member {string} RoleRID
     * @memberof user.DbRolePermission
     * @instance
     */
    DbRolePermission.prototype.RoleRID = ''

    /**
     * @db uuid not null REFERENCES DbPermission(RID) ON DELETE CASCADE
     * permission rid
     * @member {string} PermissionRID
     * @memberof user.DbRolePermission
     * @instance
     */
    DbRolePermission.prototype.PermissionRID = ''

    /**
     * @db uuid --REFERENCES DbUser(RID) ON DELETE set null
     * role 创建者 rid
     * @member {string} Creator
     * @memberof user.DbRolePermission
     * @instance
     */
    DbRolePermission.prototype.Creator = ''

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     * @member {string} UpdatedAt
     * @memberof user.DbRolePermission
     * @instance
     */
    DbRolePermission.prototype.UpdatedAt = ''

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     * @member {string} UpdatedDC
     * @memberof user.DbRolePermission
     * @instance
     */
    DbRolePermission.prototype.UpdatedDC = ''

    /**
     * Encodes the specified DbRolePermission message. Does not implicitly {@link user.DbRolePermission.verify|verify} messages.
     * @function encode
     * @memberof user.DbRolePermission
     * @static
     * @param {user.IDbRolePermission} message DbRolePermission message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    DbRolePermission.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (message.RID != null && Object.hasOwnProperty.call(message, 'RID'))
        writer.uint32(/* id 1, wireType 2 =*/ 10).string(message.RID)
      if (
        message.RoleRID != null &&
        Object.hasOwnProperty.call(message, 'RoleRID')
      )
        writer.uint32(/* id 2, wireType 2 =*/ 18).string(message.RoleRID)
      if (
        message.PermissionRID != null &&
        Object.hasOwnProperty.call(message, 'PermissionRID')
      )
        writer.uint32(/* id 4, wireType 2 =*/ 34).string(message.PermissionRID)
      if (
        message.Creator != null &&
        Object.hasOwnProperty.call(message, 'Creator')
      )
        writer.uint32(/* id 5, wireType 2 =*/ 42).string(message.Creator)
      if (
        message.UpdatedAt != null &&
        Object.hasOwnProperty.call(message, 'UpdatedAt')
      )
        writer.uint32(/* id 14, wireType 2 =*/ 114).string(message.UpdatedAt)
      if (
        message.UpdatedDC != null &&
        Object.hasOwnProperty.call(message, 'UpdatedDC')
      )
        writer.uint32(/* id 15, wireType 2 =*/ 122).string(message.UpdatedDC)
      return writer
    }

    /**
     * Decodes a DbRolePermission message from the specified reader or buffer.
     * @function decode
     * @memberof user.DbRolePermission
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {user.DbRolePermission} DbRolePermission
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    DbRolePermission.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.user.DbRolePermission()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.RID = reader.string()
            break
          case 2:
            message.RoleRID = reader.string()
            break
          case 4:
            message.PermissionRID = reader.string()
            break
          case 5:
            message.Creator = reader.string()
            break
          case 14:
            message.UpdatedAt = reader.string()
            break
          case 15:
            message.UpdatedDC = reader.string()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return DbRolePermission
  })()

  return user
})())

export { $root as default }

import { Writer } from 'protobufjs'
import { rpcCon } from 'jsyrpc'
import { ICallOption } from 'jsyrpc'

import { TRpcStream } from 'jsyrpc'
import * as ImageY from '@ygen/image'
import * as CrudY from '@ygen/crud'
import * as ImageListY from '@ygen/image.list'

export function RpcDbImageInsert(
  req: ImageY.image.IDbImage,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = ImageY.image.DbImage.encode(req)
  let reqData = w.finish()

  const api = '/image.RpcDbImage/Insert'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function RpcDbImageUpdate(
  req: ImageY.image.IDbImage,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = ImageY.image.DbImage.encode(req)
  let reqData = w.finish()

  const api = '/image.RpcDbImage/Update'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function RpcDbImagePartialUpdate(
  req: ImageY.image.IDbImage,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = ImageY.image.DbImage.encode(req)
  let reqData = w.finish()

  const api = '/image.RpcDbImage/PartialUpdate'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function RpcDbImageDelete(
  req: ImageY.image.IDbImage,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = ImageY.image.DbImage.encode(req)
  let reqData = w.finish()

  const api = '/image.RpcDbImage/Delete'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, CrudY.crud.DMLResult, callOpt)
}
export function RpcDbImageSelectOne(
  req: CrudY.crud.IDMLParam,
  callOpt?: ICallOption,
): boolean {
  let w: Writer = CrudY.crud.DMLParam.encode(req)
  let reqData = w.finish()

  const api = '/image.RpcDbImage/SelectOne'
  const v = 0

  return rpcCon.UnaryCall(reqData, api, v, ImageY.image.DbImage, callOpt)
}

export function RpcDbImageSelectMany(
  req: CrudY.crud.DMLParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/image.RpcDbImage/SelectMany'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.DMLParam,
    ImageY.image.DbImage,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function RpcDbImageQuery(
  req: CrudY.crud.QueryParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/image.RpcDbImage/Query'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.QueryParam,
    ImageY.image.DbImage,
    callOpt,
  )
  r.sendFirst(req)
  return r
}
export function RpcDbImageQueryBatch(
  req: CrudY.crud.QueryParam,
  callOpt?: ICallOption,
): TRpcStream {
  const api = '/image.RpcDbImage/QueryBatch'
  const v = 0
  let r = new TRpcStream(
    api,
    v,
    7,
    CrudY.crud.QueryParam,
    ImageListY.image.DbImageList,
    callOpt,
  )
  r.sendFirst(req)
  return r
}

export class RpcDbImage {
  public static Insert(
    req: ImageY.image.IDbImage,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbImageInsert(req, callOpt)
  }
  public static Update(
    req: ImageY.image.IDbImage,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbImageUpdate(req, callOpt)
  }
  public static PartialUpdate(
    req: ImageY.image.IDbImage,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbImagePartialUpdate(req, callOpt)
  }
  public static Delete(
    req: ImageY.image.IDbImage,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbImageDelete(req, callOpt)
  }
  public static SelectOne(
    req: CrudY.crud.IDMLParam,
    callOpt?: ICallOption,
  ): boolean {
    return RpcDbImageSelectOne(req, callOpt)
  }
  public static SelectMany(
    req: CrudY.crud.DMLParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return RpcDbImageSelectMany(req, callOpt)
  }
  public static Query(
    req: CrudY.crud.QueryParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return RpcDbImageQuery(req, callOpt)
  }
  public static QueryBatch(
    req: CrudY.crud.QueryParam,
    callOpt?: ICallOption,
  ): TRpcStream {
    return RpcDbImageQueryBatch(req, callOpt)
  }
}
export default {
  RpcDbImage,
  RpcDbImageInsert,
  RpcDbImageUpdate,
  RpcDbImagePartialUpdate,
  RpcDbImageDelete,
  RpcDbImageSelectOne,
  RpcDbImageSelectMany,
  RpcDbImageQuery,
  RpcDbImageQueryBatch,
}

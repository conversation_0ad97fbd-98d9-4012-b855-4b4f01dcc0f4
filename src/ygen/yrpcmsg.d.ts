import { Long } from 'protobufjs'
import * as $protobuf from 'protobufjs'
/** Namespace yrpcmsg. */
export namespace yrpcmsg {
  /** Properties of a Ymsg. */
  interface IYmsg {
    /**
     * 整个rpc msg的长度，不包含此字段
     * 虽然这个长度可以很长，但是为了避免大包阻塞其它操作，通常都要限制长度,采用分包多发机制
     * 当使用基于包的传输通道时(udp/kcp/websocket)，此值可能为0(此时长度为收到的整个包的长度)
     */
    Len?: number | null

    /**
     * rpc command,rpc的命令和option
     * b15-b0(uint16):低16为rpc命令
     * b19-b16:body压缩方式 0:无压缩 1:lz4 2:zlib inflate/deflate
     * b23-b20:optbin压缩方式 0:无压缩 1:lz4 2:zlib inflate/deflate
     * b31-b24: not used
     */
    Cmd?: number | null

    /** session id，登录后一定会有,用于后台区分不同的用户请求 */
    Sid?: Uint8Array | null

    /** rpc call id,给分辨不同的rpc调用使用,调用方增1循环使用 */
    Cid?: number | null

    /** rpc no,从0开始增1使用,用于区分收到重复的包,特别是udp的情况下 */
    No?: number | null

    /** response code */
    Res?: number | null

    /** msg body */
    Body?: Uint8Array | null

    /** optional str parameter,额外的信息,一般不会有,有些特殊命令里面可能用到 */
    Optstr?: string | null

    /** optional binary parameter,额外的信息,一般不会有,有些特殊命令里面可能用到 */
    Optbin?: Uint8Array | null

    /** optional grpc meta */
    MetaInfo?: yrpcmsg.IMeta | null
  }

  /**
   * 系统中所有的消息交互底层都以此为包装
   * ymsg multiline comment
   */
  class Ymsg implements IYmsg {
    /**
     * Constructs a new Ymsg.
     * @param [properties] Properties to set
     */
    constructor(properties?: yrpcmsg.IYmsg)

    /**
     * 整个rpc msg的长度，不包含此字段
     * 虽然这个长度可以很长，但是为了避免大包阻塞其它操作，通常都要限制长度,采用分包多发机制
     * 当使用基于包的传输通道时(udp/kcp/websocket)，此值可能为0(此时长度为收到的整个包的长度)
     */
    public Len: number

    /**
     * rpc command,rpc的命令和option
     * b15-b0(uint16):低16为rpc命令
     * b19-b16:body压缩方式 0:无压缩 1:lz4 2:zlib inflate/deflate
     * b23-b20:optbin压缩方式 0:无压缩 1:lz4 2:zlib inflate/deflate
     * b31-b24: not used
     */
    public Cmd: number

    /** session id，登录后一定会有,用于后台区分不同的用户请求 */
    public Sid: Uint8Array

    /** rpc call id,给分辨不同的rpc调用使用,调用方增1循环使用 */
    public Cid: number

    /** rpc no,从0开始增1使用,用于区分收到重复的包,特别是udp的情况下 */
    public No: number

    /** response code */
    public Res: number

    /** msg body */
    public Body: Uint8Array

    /** optional str parameter,额外的信息,一般不会有,有些特殊命令里面可能用到 */
    public Optstr: string

    /** optional binary parameter,额外的信息,一般不会有,有些特殊命令里面可能用到 */
    public Optbin: Uint8Array

    /** optional grpc meta */
    public MetaInfo?: yrpcmsg.IMeta | null

    /**
     * Encodes the specified Ymsg message. Does not implicitly {@link yrpcmsg.Ymsg.verify|verify} messages.
     * @param message Ymsg message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: yrpcmsg.IYmsg,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a Ymsg message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns Ymsg
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): yrpcmsg.Ymsg
  }

  /** Properties of a MetaItem. */
  interface IMetaItem {
    /** MetaItem key */
    key?: string | null

    /** MetaItem vals */
    vals?: string[] | null
  }

  /** grpc meta data item */
  class MetaItem implements IMetaItem {
    /**
     * Constructs a new MetaItem.
     * @param [properties] Properties to set
     */
    constructor(properties?: yrpcmsg.IMetaItem)

    /** MetaItem key. */
    public key: string

    /** MetaItem vals. */
    public vals: string[]

    /**
     * Encodes the specified MetaItem message. Does not implicitly {@link yrpcmsg.MetaItem.verify|verify} messages.
     * @param message MetaItem message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: yrpcmsg.IMetaItem,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a MetaItem message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns MetaItem
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): yrpcmsg.MetaItem
  }

  /** Properties of a Meta. */
  interface IMeta {
    /** grpc meta */
    val?: yrpcmsg.IMetaItem[] | null
  }

  /** grpc meta */
  class Meta implements IMeta {
    /**
     * Constructs a new Meta.
     * @param [properties] Properties to set
     */
    constructor(properties?: yrpcmsg.IMeta)

    /** grpc meta */
    public val: yrpcmsg.IMetaItem[]

    /**
     * Encodes the specified Meta message. Does not implicitly {@link yrpcmsg.Meta.verify|verify} messages.
     * @param message Meta message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: yrpcmsg.IMeta,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a Meta message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns Meta
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): yrpcmsg.Meta
  }

  /** Properties of a GrpcMeta. */
  interface IGrpcMeta {
    /** GrpcMeta Header */
    Header?: yrpcmsg.IMeta | null

    /** GrpcMeta Trailer */
    Trailer?: yrpcmsg.IMeta | null
  }

  /** grpc Header Trailer meta */
  class GrpcMeta implements IGrpcMeta {
    /**
     * Constructs a new GrpcMeta.
     * @param [properties] Properties to set
     */
    constructor(properties?: yrpcmsg.IGrpcMeta)

    /** GrpcMeta Header. */
    public Header?: yrpcmsg.IMeta | null

    /** GrpcMeta Trailer. */
    public Trailer?: yrpcmsg.IMeta | null

    /**
     * Encodes the specified GrpcMeta message. Does not implicitly {@link yrpcmsg.GrpcMeta.verify|verify} messages.
     * @param message GrpcMeta message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: yrpcmsg.IGrpcMeta,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a GrpcMeta message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns GrpcMeta
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): yrpcmsg.GrpcMeta
  }

  /** Properties of a Yempty. */
  interface IYempty {}

  /** Represents a Yempty. */
  class Yempty implements IYempty {
    /**
     * Constructs a new Yempty.
     * @param [properties] Properties to set
     */
    constructor(properties?: yrpcmsg.IYempty)

    /**
     * Encodes the specified Yempty message. Does not implicitly {@link yrpcmsg.Yempty.verify|verify} messages.
     * @param message Yempty message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: yrpcmsg.IYempty,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a Yempty message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns Yempty
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): yrpcmsg.Yempty
  }

  /** Properties of a Ynocare. */
  interface IYnocare {}

  /**
   * A generic nocare message that you can use to info the call is not important
   * and no care the result. A typical example is to use it in report log/trace.
   * For instance:
   *
   * service Log {
   * rpc Log(infos) returns (yrpc.Ynocare);
   * }
   */
  class Ynocare implements IYnocare {
    /**
     * Constructs a new Ynocare.
     * @param [properties] Properties to set
     */
    constructor(properties?: yrpcmsg.IYnocare)

    /**
     * Encodes the specified Ynocare message. Does not implicitly {@link yrpcmsg.Ynocare.verify|verify} messages.
     * @param message Ynocare message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: yrpcmsg.IYnocare,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a Ynocare message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns Ynocare
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): yrpcmsg.Ynocare
  }

  /** Properties of an UnixTime. */
  interface IUnixTime {
    /** Unix time, the number of miliseconds elapsed since January 1, 1970 UTC */
    TimeUnix?: Long | null

    /** utc time yyyy-MM-dd hh:mm:ss.zzz */
    TimeStr?: string | null
  }

  /** Represents an UnixTime. */
  class UnixTime implements IUnixTime {
    /**
     * Constructs a new UnixTime.
     * @param [properties] Properties to set
     */
    constructor(properties?: yrpcmsg.IUnixTime)

    /** Unix time, the number of miliseconds elapsed since January 1, 1970 UTC */
    public TimeUnix: Long

    /** utc time yyyy-MM-dd hh:mm:ss.zzz */
    public TimeStr: string

    /**
     * Encodes the specified UnixTime message. Does not implicitly {@link yrpcmsg.UnixTime.verify|verify} messages.
     * @param message UnixTime message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: yrpcmsg.IUnixTime,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes an UnixTime message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns UnixTime
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): yrpcmsg.UnixTime
  }
}

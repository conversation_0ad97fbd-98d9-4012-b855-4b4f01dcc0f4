import { Long } from 'protobufjs'
import * as $protobuf from 'protobufjs'
/** Namespace doc. */
export namespace doc {
  /** Properties of an OnlineControllerInfo. */
  interface IOnlineControllerInfo {
    /** 连接时间 */
    ConnectTime?: string | null

    /** 控制器ID */
    ControllerID?: number | null

    /** 登录网络方式 */
    NetworkType?: number | null

    /** 最后数据时间 */
    LastDataTime?: string | null

    /** 最后电量 */
    Power?: number | null

    /** 通道手台信道号 */
    ChannelNo?: number | null

    /**
     * 本控制器数据中转的通道号和对应的信道号
     * [通道号1,通道1手台的信道号，通道号2,通道2手台的信道号，...]
     * 如[1,1,2,1]表示通道1 在1信道，通道2也在1信道
     */
    TransferChannelNos?: number[] | null

    /** 登录设备类型 0:fsk控制器 1:4g界桩 */
    DeviceType?: number | null
  }

  /** Represents an OnlineControllerInfo. */
  class OnlineControllerInfo implements IOnlineControllerInfo {
    /**
     * Constructs a new OnlineControllerInfo.
     * @param [properties] Properties to set
     */
    constructor(properties?: doc.IOnlineControllerInfo)

    /** 连接时间 */
    public ConnectTime: string

    /** 控制器ID */
    public ControllerID: number

    /** 登录网络方式 */
    public NetworkType: number

    /** 最后数据时间 */
    public LastDataTime: string

    /** 最后电量 */
    public Power: number

    /** 通道手台信道号 */
    public ChannelNo: number

    /**
     * 本控制器数据中转的通道号和对应的信道号
     * [通道号1,通道1手台的信道号，通道号2,通道2手台的信道号，...]
     * 如[1,1,2,1]表示通道1 在1信道，通道2也在1信道
     */
    public TransferChannelNos: number[]

    /** 登录设备类型 0:fsk控制器 1:4g界桩 */
    public DeviceType: number

    /**
     * Encodes the specified OnlineControllerInfo message. Does not implicitly {@link doc.OnlineControllerInfo.verify|verify} messages.
     * @param message OnlineControllerInfo message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: doc.IOnlineControllerInfo,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes an OnlineControllerInfo message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns OnlineControllerInfo
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): doc.OnlineControllerInfo
  }

  /** Properties of a MarkerQueueNoInfo. */
  interface IMarkerQueueNoInfo {
    /** 控制器RID */
    ControllerRID?: string | null

    /** 控制器通道号 */
    ControllerChannelNo?: number | null

    /**
     * 0:查询可用的排队号
     * >0:此排队号是否可用
     */
    QueueNoAvailable?: number | null

    /**
     * 结果
     * 可用的排队号,或者=QueueNoAvailable，或者是新的可用排队号
     */
    Result?: number | null
  }

  /** Represents a MarkerQueueNoInfo. */
  class MarkerQueueNoInfo implements IMarkerQueueNoInfo {
    /**
     * Constructs a new MarkerQueueNoInfo.
     * @param [properties] Properties to set
     */
    constructor(properties?: doc.IMarkerQueueNoInfo)

    /** 控制器RID */
    public ControllerRID: string

    /** 控制器通道号 */
    public ControllerChannelNo: number

    /**
     * 0:查询可用的排队号
     * >0:此排队号是否可用
     */
    public QueueNoAvailable: number

    /**
     * 结果
     * 可用的排队号,或者=QueueNoAvailable，或者是新的可用排队号
     */
    public Result: number

    /**
     * Encodes the specified MarkerQueueNoInfo message. Does not implicitly {@link doc.MarkerQueueNoInfo.verify|verify} messages.
     * @param message MarkerQueueNoInfo message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: doc.IMarkerQueueNoInfo,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a MarkerQueueNoInfo message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns MarkerQueueNoInfo
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): doc.MarkerQueueNoInfo
  }

  /** Properties of a MarkerInfo. */
  interface IMarkerInfo {
    /** 控制器ID */
    ControllerID?: number | null

    /** 控制器通道 */
    ControllerChannelNo?: number | null

    /** 界桩ID */
    MarkerID?: number | null

    /** 界桩RID */
    MarkerRID?: string | null

    /** 上来的指令，0xd1,0xd2,0xd3 */
    MarkerUploadCmd?: number | null

    /** 界桩所在信道,未知设置为0 */
    MarkerChannel?: number | null

    /** 上传的命令时间 yyyy-mm-dd HH:MM:SS(utc) */
    MarkerCmdTime?: string | null

    /** 上传的命令系统密码是否正确 */
    MarkerSysPassOK?: boolean | null

    /** 界桩的参数配置时间 */
    MarkerParamTime?: string | null

    /** 界桩状态，目前只用低位一个字节 */
    Status?: number | null

    /** 界桩上报 */
    InfoReporting?: bysproto.IBInfoReporting | null

    /** MarkerInfo AlarmReporting */
    AlarmReporting?: bysproto.IBAlarmReporting | null

    /** 常规界桩：0, 4g界桩: 1 */
    MarkerType?: number | null

    /** 4g界桩报警状态，0:无报警，1:报警 */
    AlarmStatusWith4G?: number | null
  }

  /** 界桩信息 */
  class MarkerInfo implements IMarkerInfo {
    /**
     * Constructs a new MarkerInfo.
     * @param [properties] Properties to set
     */
    constructor(properties?: doc.IMarkerInfo)

    /** 控制器ID */
    public ControllerID: number

    /** 控制器通道 */
    public ControllerChannelNo: number

    /** 界桩ID */
    public MarkerID: number

    /** 界桩RID */
    public MarkerRID: string

    /** 上来的指令，0xd1,0xd2,0xd3 */
    public MarkerUploadCmd: number

    /** 界桩所在信道,未知设置为0 */
    public MarkerChannel: number

    /** 上传的命令时间 yyyy-mm-dd HH:MM:SS(utc) */
    public MarkerCmdTime: string

    /** 上传的命令系统密码是否正确 */
    public MarkerSysPassOK: boolean

    /** 界桩的参数配置时间 */
    public MarkerParamTime: string

    /** 界桩状态，目前只用低位一个字节 */
    public Status: number

    /** 界桩上报 */
    public InfoReporting?: bysproto.IBInfoReporting | null

    /** MarkerInfo AlarmReporting. */
    public AlarmReporting?: bysproto.IBAlarmReporting | null

    /** 常规界桩：0, 4g界桩: 1 */
    public MarkerType: number

    /** 4g界桩报警状态，0:无报警，1:报警 */
    public AlarmStatusWith4G: number

    /**
     * Encodes the specified MarkerInfo message. Does not implicitly {@link doc.MarkerInfo.verify|verify} messages.
     * @param message MarkerInfo message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: doc.IMarkerInfo,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a MarkerInfo message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns MarkerInfo
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): doc.MarkerInfo
  }

  /** Properties of a ControllerCmd. */
  interface IControllerCmd {
    /** 目标控制器ID */
    ControllerID?: number | null

    /** 在上级控制器的通道号，中继时需要填写，基站本地通道时为0 */
    ControllerChannelNo?: number | null

    /** 新监听信道号 */
    NewChannelNo?: number | null

    /**
     * 命令
     * 13,14
     */
    Cmd?: number | null
  }

  /** Represents a ControllerCmd. */
  class ControllerCmd implements IControllerCmd {
    /**
     * Constructs a new ControllerCmd.
     * @param [properties] Properties to set
     */
    constructor(properties?: doc.IControllerCmd)

    /** 目标控制器ID */
    public ControllerID: number

    /** 在上级控制器的通道号，中继时需要填写，基站本地通道时为0 */
    public ControllerChannelNo: number

    /** 新监听信道号 */
    public NewChannelNo: number

    /**
     * 命令
     * 13,14
     */
    public Cmd: number

    /**
     * Encodes the specified ControllerCmd message. Does not implicitly {@link doc.ControllerCmd.verify|verify} messages.
     * @param message ControllerCmd message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: doc.IControllerCmd,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a ControllerCmd message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns ControllerCmd
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): doc.ControllerCmd
  }

  /** Properties of an UpdataMarkerParam. */
  interface IUpdataMarkerParam {
    /** 系统设置上级控制器HWID,用于后台缓存找不到markerinfo时使用 */
    controllerID?: number | null

    /** 界桩更新后的参数 */
    markerData?: bysdb.IDbBysMarker | null
  }

  /** Represents an UpdataMarkerParam. */
  class UpdataMarkerParam implements IUpdataMarkerParam {
    /**
     * Constructs a new UpdataMarkerParam.
     * @param [properties] Properties to set
     */
    constructor(properties?: doc.IUpdataMarkerParam)

    /** 系统设置上级控制器HWID,用于后台缓存找不到markerinfo时使用 */
    public controllerID: number

    /** 界桩更新后的参数 */
    public markerData?: bysdb.IDbBysMarker | null

    /**
     * Encodes the specified UpdataMarkerParam message. Does not implicitly {@link doc.UpdataMarkerParam.verify|verify} messages.
     * @param message UpdataMarkerParam message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: doc.IUpdataMarkerParam,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes an UpdataMarkerParam message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns UpdataMarkerParam
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): doc.UpdataMarkerParam
  }

  /** Properties of a ControllerTarget. */
  interface IControllerTarget {
    /** 基站控制器ID */
    StationID?: number | null

    /** 在上级控制器的通道号，中继时需要填写，基站本地通道时为0 */
    ControllerChannelNo?: number | null
  }

  /** 更新控制器新服务器的地址。如果更改的地址错误，需要维护人员到现场修改控制器配置 */
  class ControllerTarget implements IControllerTarget {
    /**
     * Constructs a new ControllerTarget.
     * @param [properties] Properties to set
     */
    constructor(properties?: doc.IControllerTarget)

    /** 基站控制器ID */
    public StationID: number

    /** 在上级控制器的通道号，中继时需要填写，基站本地通道时为0 */
    public ControllerChannelNo: number

    /**
     * Encodes the specified ControllerTarget message. Does not implicitly {@link doc.ControllerTarget.verify|verify} messages.
     * @param message ControllerTarget message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: doc.IControllerTarget,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a ControllerTarget message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns ControllerTarget
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): doc.ControllerTarget
  }

  /** Properties of a ControllerNewServerAddr. */
  interface IControllerNewServerAddr {
    /** 基站控制器ID */
    Target?: doc.IControllerTarget[] | null

    /** 新服务器IP地址 */
    Ip?: string | null

    /** 新服务器端口 */
    Port?: number | null
  }

  /**
   * 控制器回应对应 controller.proto ： BControllerNewServerAddr
   * bmsg.cmd=15 bmsg.Res=1
   * Nats事件主题：device.OrgRID.ControllerID.
   */
  class ControllerNewServerAddr implements IControllerNewServerAddr {
    /**
     * Constructs a new ControllerNewServerAddr.
     * @param [properties] Properties to set
     */
    constructor(properties?: doc.IControllerNewServerAddr)

    /** 基站控制器ID */
    public Target: doc.IControllerTarget[]

    /** 新服务器IP地址 */
    public Ip: string

    /** 新服务器端口 */
    public Port: number

    /**
     * Encodes the specified ControllerNewServerAddr message. Does not implicitly {@link doc.ControllerNewServerAddr.verify|verify} messages.
     * @param message ControllerNewServerAddr message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: doc.IControllerNewServerAddr,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a ControllerNewServerAddr message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns ControllerNewServerAddr
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): doc.ControllerNewServerAddr
  }

  /** Properties of a MarkerOrControllerId. */
  interface IMarkerOrControllerId {
    /** req: 界桩/控制器id  resp: 上级控制器id */
    id?: number | null

    /** resp: 上级控制器通道 */
    controllerChannelNo?: number | null

    /** 界桩=1,控制器=2 */
    isMarker?: boolean | null
  }

  /** 查询界桩/控制器链路通道 */
  class MarkerOrControllerId implements IMarkerOrControllerId {
    /**
     * Constructs a new MarkerOrControllerId.
     * @param [properties] Properties to set
     */
    constructor(properties?: doc.IMarkerOrControllerId)

    /** req: 界桩/控制器id  resp: 上级控制器id */
    public id: number

    /** resp: 上级控制器通道 */
    public controllerChannelNo: number

    /** 界桩=1,控制器=2 */
    public isMarker: boolean

    /**
     * Encodes the specified MarkerOrControllerId message. Does not implicitly {@link doc.MarkerOrControllerId.verify|verify} messages.
     * @param message MarkerOrControllerId message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: doc.IMarkerOrControllerId,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a MarkerOrControllerId message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns MarkerOrControllerId
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): doc.MarkerOrControllerId
  }

  /** bys 常规 api */
  class RpcBysApi extends $protobuf.rpc.Service {
    /**
     * Constructs a new RpcBysApi service.
     * @param rpcImpl RPC implementation
     * @param [requestDelimited=false] Whether requests are length-delimited
     * @param [responseDelimited=false] Whether responses are length-delimited
     */
    constructor(
      rpcImpl: $protobuf.RPCImpl,
      requestDelimited?: boolean,
      responseDelimited?: boolean,
    )

    /**
     * 获取有权限的在线的控制器列表信息
     * @param request Yempty message or plain object
     * @param callback Node-style callback called with the error, if any, and OnlineControllerInfo
     */
    public onlineController(
      request: yrpcmsg.IYempty,
      callback: doc.RpcBysApi.OnlineControllerCallback,
    ): void

    /**
     * 获取有权限的在线的控制器列表信息
     * @param request Yempty message or plain object
     * @returns Promise
     */
    public onlineController(
      request: yrpcmsg.IYempty,
    ): Promise<doc.OnlineControllerInfo>

    /**
     * 获取或查询控制器下界桩排队号
     * @param request MarkerQueueNoInfo message or plain object
     * @param callback Node-style callback called with the error, if any, and MarkerQueueNoInfo
     */
    public queryMarkerQueueNo(
      request: doc.IMarkerQueueNoInfo,
      callback: doc.RpcBysApi.QueryMarkerQueueNoCallback,
    ): void

    /**
     * 获取或查询控制器下界桩排队号
     * @param request MarkerQueueNoInfo message or plain object
     * @returns Promise
     */
    public queryMarkerQueueNo(
      request: doc.IMarkerQueueNoInfo,
    ): Promise<doc.MarkerQueueNoInfo>

    /**
     * 界桩取消报警
     * @param request MarkerInfo message or plain object
     * @param callback Node-style callback called with the error, if any, and Yempty
     */
    public cancelEmergency(
      request: doc.IMarkerInfo,
      callback: doc.RpcBysApi.CancelEmergencyCallback,
    ): void

    /**
     * 界桩取消报警
     * @param request MarkerInfo message or plain object
     * @returns Promise
     */
    public cancelEmergency(request: doc.IMarkerInfo): Promise<yrpcmsg.Yempty>

    /**
     * 界桩下发参数
     * @param request MarkerInfo message or plain object
     * @param callback Node-style callback called with the error, if any, and Yempty
     */
    public send0xD0(
      request: doc.IMarkerInfo,
      callback: doc.RpcBysApi.Send0xD0Callback,
    ): void

    /**
     * 界桩下发参数
     * @param request MarkerInfo message or plain object
     * @returns Promise
     */
    public send0xD0(request: doc.IMarkerInfo): Promise<yrpcmsg.Yempty>

    /**
     * 获取界桩最后数据，
     * 参数MarkerInfo需要填写MarkerID,MarkerCmdTime
     * MarkerCmdTime作为查询数据库时最早的时间(缓存失效的情况)，第一个必须填写，以后的不需要
     * @param request MarkerInfo message or plain object
     * @param callback Node-style callback called with the error, if any, and MarkerInfo
     */
    public markerLatestInfo(
      request: doc.IMarkerInfo,
      callback: doc.RpcBysApi.MarkerLatestInfoCallback,
    ): void

    /**
     * 获取界桩最后数据，
     * 参数MarkerInfo需要填写MarkerID,MarkerCmdTime
     * MarkerCmdTime作为查询数据库时最早的时间(缓存失效的情况)，第一个必须填写，以后的不需要
     * @param request MarkerInfo message or plain object
     * @returns Promise
     */
    public markerLatestInfo(request: doc.IMarkerInfo): Promise<doc.MarkerInfo>

    /**
     * 修改基站/中继默认信道号
     * @param request ControllerCmd message or plain object
     * @param callback Node-style callback called with the error, if any, and Yempty
     */
    public sendControllerCmd(
      request: doc.IControllerCmd,
      callback: doc.RpcBysApi.SendControllerCmdCallback,
    ): void

    /**
     * 修改基站/中继默认信道号
     * @param request ControllerCmd message or plain object
     * @returns Promise
     */
    public sendControllerCmd(
      request: doc.IControllerCmd,
    ): Promise<yrpcmsg.Yempty>

    /**
     * 下发指令 更新界桩参数
     * @param request UpdataMarkerParam message or plain object
     * @param callback Node-style callback called with the error, if any, and Yempty
     */
    public updateMarkerParamter(
      request: doc.IUpdataMarkerParam,
      callback: doc.RpcBysApi.UpdateMarkerParamterCallback,
    ): void

    /**
     * 下发指令 更新界桩参数
     * @param request UpdataMarkerParam message or plain object
     * @returns Promise
     */
    public updateMarkerParamter(
      request: doc.IUpdataMarkerParam,
    ): Promise<yrpcmsg.Yempty>

    /**
     * 实时ping控制器
     * ControllerCmd.ControllerID为基站ID, Ping中继时为中继控制器的上级控制器ID
     * ControllerCmd.ControllerChannelNo 通道，ping基站时为0,ping中继时为中继对应基站下的通道号
     * ControllerCmd.NewChannelNo,ping基站时为0,ping中继时为中继的ID
     * @param request ControllerCmd message or plain object
     * @param callback Node-style callback called with the error, if any, and Yempty
     */
    public pingController(
      request: doc.IControllerCmd,
      callback: doc.RpcBysApi.PingControllerCallback,
    ): void

    /**
     * 实时ping控制器
     * ControllerCmd.ControllerID为基站ID, Ping中继时为中继控制器的上级控制器ID
     * ControllerCmd.ControllerChannelNo 通道，ping基站时为0,ping中继时为中继对应基站下的通道号
     * ControllerCmd.NewChannelNo,ping基站时为0,ping中继时为中继的ID
     * @param request ControllerCmd message or plain object
     * @returns Promise
     */
    public pingController(request: doc.IControllerCmd): Promise<yrpcmsg.Yempty>

    /**
     * 请求角色拥有的权限
     * @param request DbRole message or plain object
     * @param callback Node-style callback called with the error, if any, and DbRolePermissionList
     */
    public getRolePermissions(
      request: user.IDbRole,
      callback: doc.RpcBysApi.GetRolePermissionsCallback,
    ): void

    /**
     * 请求角色拥有的权限
     * @param request DbRole message or plain object
     * @returns Promise
     */
    public getRolePermissions(
      request: user.IDbRole,
    ): Promise<user.DbRolePermissionList>

    /**
     * 更新指定控制器连接的服务器地址
     * @param request ControllerNewServerAddr message or plain object
     * @param callback Node-style callback called with the error, if any, and Yempty
     */
    public updateControllerServerAddr(
      request: doc.IControllerNewServerAddr,
      callback: doc.RpcBysApi.UpdateControllerServerAddrCallback,
    ): void

    /**
     * 更新指定控制器连接的服务器地址
     * @param request ControllerNewServerAddr message or plain object
     * @returns Promise
     */
    public updateControllerServerAddr(
      request: doc.IControllerNewServerAddr,
    ): Promise<yrpcmsg.Yempty>

    /**
     * 查询界桩/控制器链路信息
     * @param request MarkerOrControllerId message or plain object
     * @param callback Node-style callback called with the error, if any, and MarkerOrControllerId
     */
    public queryMarkerOrControllerLinkList(
      request: doc.IMarkerOrControllerId,
      callback: doc.RpcBysApi.QueryMarkerOrControllerLinkListCallback,
    ): void

    /**
     * 查询界桩/控制器链路信息
     * @param request MarkerOrControllerId message or plain object
     * @returns Promise
     */
    public queryMarkerOrControllerLinkList(
      request: doc.IMarkerOrControllerId,
    ): Promise<doc.MarkerOrControllerId>

    /**
     * 4g界桩遥毙/遥晕/遥活
     * 应答的Bmsg中，Res=1 表示下发成功，Res=2 表示已缓存到数据库中，等待界桩唤醒再下发
     * @param request BShutDown message or plain object
     * @param callback Node-style callback called with the error, if any, and Bmsg
     */
    public markerRemoteKillOrActive(
      request: bysproto.IBShutDown,
      callback: doc.RpcBysApi.MarkerRemoteKillOrActiveCallback,
    ): void

    /**
     * 4g界桩遥毙/遥晕/遥活
     * 应答的Bmsg中，Res=1 表示下发成功，Res=2 表示已缓存到数据库中，等待界桩唤醒再下发
     * @param request BShutDown message or plain object
     * @returns Promise
     */
    public markerRemoteKillOrActive(
      request: bysproto.IBShutDown,
    ): Promise<bysproto.Bmsg>

    /**
     * 4g界桩NFC巡查打卡上传
     * @param request DbMarkerPatrolHistory message or plain object
     * @param callback Node-style callback called with the error, if any, and Yempty
     */
    public markerUploadNFCRecord(
      request: bysdb.IDbMarkerPatrolHistory,
      callback: doc.RpcBysApi.MarkerUploadNFCRecordCallback,
    ): void

    /**
     * 4g界桩NFC巡查打卡上传
     * @param request DbMarkerPatrolHistory message or plain object
     * @returns Promise
     */
    public markerUploadNFCRecord(
      request: bysdb.IDbMarkerPatrolHistory,
    ): Promise<yrpcmsg.Yempty>

    /**
     * nfc巡查统计查询
     * @param request PatrolStatisticsQuery message or plain object
     * @param callback Node-style callback called with the error, if any, and PatrolStatisticsQueryResult
     */
    public nFCPatrolStatistics(
      request: doc.IPatrolStatisticsQuery,
      callback: doc.RpcBysApi.NFCPatrolStatisticsCallback,
    ): void

    /**
     * nfc巡查统计查询
     * @param request PatrolStatisticsQuery message or plain object
     * @returns Promise
     */
    public nFCPatrolStatistics(
      request: doc.IPatrolStatisticsQuery,
    ): Promise<doc.PatrolStatisticsQueryResult>
  }

  namespace RpcBysApi {
    /**
     * Callback as used by {@link doc.RpcBysApi#onlineController}.
     * @param error Error, if any
     * @param [response] OnlineControllerInfo
     */
    type OnlineControllerCallback = (
      error: Error | null,
      response?: doc.OnlineControllerInfo,
    ) => void

    /**
     * Callback as used by {@link doc.RpcBysApi#queryMarkerQueueNo}.
     * @param error Error, if any
     * @param [response] MarkerQueueNoInfo
     */
    type QueryMarkerQueueNoCallback = (
      error: Error | null,
      response?: doc.MarkerQueueNoInfo,
    ) => void

    /**
     * Callback as used by {@link doc.RpcBysApi#cancelEmergency}.
     * @param error Error, if any
     * @param [response] Yempty
     */
    type CancelEmergencyCallback = (
      error: Error | null,
      response?: yrpcmsg.Yempty,
    ) => void

    /**
     * Callback as used by {@link doc.RpcBysApi#send0xD0}.
     * @param error Error, if any
     * @param [response] Yempty
     */
    type Send0xD0Callback = (
      error: Error | null,
      response?: yrpcmsg.Yempty,
    ) => void

    /**
     * Callback as used by {@link doc.RpcBysApi#markerLatestInfo}.
     * @param error Error, if any
     * @param [response] MarkerInfo
     */
    type MarkerLatestInfoCallback = (
      error: Error | null,
      response?: doc.MarkerInfo,
    ) => void

    /**
     * Callback as used by {@link doc.RpcBysApi#sendControllerCmd}.
     * @param error Error, if any
     * @param [response] Yempty
     */
    type SendControllerCmdCallback = (
      error: Error | null,
      response?: yrpcmsg.Yempty,
    ) => void

    /**
     * Callback as used by {@link doc.RpcBysApi#updateMarkerParamter}.
     * @param error Error, if any
     * @param [response] Yempty
     */
    type UpdateMarkerParamterCallback = (
      error: Error | null,
      response?: yrpcmsg.Yempty,
    ) => void

    /**
     * Callback as used by {@link doc.RpcBysApi#pingController}.
     * @param error Error, if any
     * @param [response] Yempty
     */
    type PingControllerCallback = (
      error: Error | null,
      response?: yrpcmsg.Yempty,
    ) => void

    /**
     * Callback as used by {@link doc.RpcBysApi#getRolePermissions}.
     * @param error Error, if any
     * @param [response] DbRolePermissionList
     */
    type GetRolePermissionsCallback = (
      error: Error | null,
      response?: user.DbRolePermissionList,
    ) => void

    /**
     * Callback as used by {@link doc.RpcBysApi#updateControllerServerAddr}.
     * @param error Error, if any
     * @param [response] Yempty
     */
    type UpdateControllerServerAddrCallback = (
      error: Error | null,
      response?: yrpcmsg.Yempty,
    ) => void

    /**
     * Callback as used by {@link doc.RpcBysApi#queryMarkerOrControllerLinkList}.
     * @param error Error, if any
     * @param [response] MarkerOrControllerId
     */
    type QueryMarkerOrControllerLinkListCallback = (
      error: Error | null,
      response?: doc.MarkerOrControllerId,
    ) => void

    /**
     * Callback as used by {@link doc.RpcBysApi#markerRemoteKillOrActive}.
     * @param error Error, if any
     * @param [response] Bmsg
     */
    type MarkerRemoteKillOrActiveCallback = (
      error: Error | null,
      response?: bysproto.Bmsg,
    ) => void

    /**
     * Callback as used by {@link doc.RpcBysApi#markerUploadNFCRecord}.
     * @param error Error, if any
     * @param [response] Yempty
     */
    type MarkerUploadNFCRecordCallback = (
      error: Error | null,
      response?: yrpcmsg.Yempty,
    ) => void

    /**
     * Callback as used by {@link doc.RpcBysApi#nFCPatrolStatistics}.
     * @param error Error, if any
     * @param [response] PatrolStatisticsQueryResult
     */
    type NFCPatrolStatisticsCallback = (
      error: Error | null,
      response?: doc.PatrolStatisticsQueryResult,
    ) => void
  }

  /** Properties of a PatrolStatisticsQuery. */
  interface IPatrolStatisticsQuery {
    /** 开始时间(utc)， 格式：yyyy-mm-dd HH:MM:SS */
    StartTime?: string | null

    /** 结束时间(utc) */
    EndTime?: string | null

    /** 巡查路线RID */
    LineRid?: string | null
  }

  /** 巡查统计条件 */
  class PatrolStatisticsQuery implements IPatrolStatisticsQuery {
    /**
     * Constructs a new PatrolStatisticsQuery.
     * @param [properties] Properties to set
     */
    constructor(properties?: doc.IPatrolStatisticsQuery)

    /** 开始时间(utc)， 格式：yyyy-mm-dd HH:MM:SS */
    public StartTime: string

    /** 结束时间(utc) */
    public EndTime: string

    /** 巡查路线RID */
    public LineRid: string

    /**
     * Encodes the specified PatrolStatisticsQuery message. Does not implicitly {@link doc.PatrolStatisticsQuery.verify|verify} messages.
     * @param message PatrolStatisticsQuery message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: doc.IPatrolStatisticsQuery,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a PatrolStatisticsQuery message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns PatrolStatisticsQuery
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): doc.PatrolStatisticsQuery
  }

  /** Properties of a PatrolCheckItem. */
  interface IPatrolCheckItem {
    /** 巡查的界桩的HWID */
    MarkerHWID?: number | null

    /** 巡查时间 格式：yyyy-mm-dd HH:MM:SS */
    CheckTime?: string | null

    /** 巡查用户 */
    CheckUserRID?: string | null

    /** 巡查结果, 0:正常, 1: 未打卡 */
    CheckResult?: number | null
  }

  /** Represents a PatrolCheckItem. */
  class PatrolCheckItem implements IPatrolCheckItem {
    /**
     * Constructs a new PatrolCheckItem.
     * @param [properties] Properties to set
     */
    constructor(properties?: doc.IPatrolCheckItem)

    /** 巡查的界桩的HWID */
    public MarkerHWID: number

    /** 巡查时间 格式：yyyy-mm-dd HH:MM:SS */
    public CheckTime: string

    /** 巡查用户 */
    public CheckUserRID: string

    /** 巡查结果, 0:正常, 1: 未打卡 */
    public CheckResult: number

    /**
     * Encodes the specified PatrolCheckItem message. Does not implicitly {@link doc.PatrolCheckItem.verify|verify} messages.
     * @param message PatrolCheckItem message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: doc.IPatrolCheckItem,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a PatrolCheckItem message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns PatrolCheckItem
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): doc.PatrolCheckItem
  }

  /** Properties of a PatrolStatisticsQueryResult. */
  interface IPatrolStatisticsQueryResult {
    /** 巡查路线RID */
    LineRid?: string | null

    /** 路线规则RID */
    RuleRID?: string | null

    /** 应查数目 */
    ShouldCheckCount?: number | null

    /** 巡查日期 yyyy-mm-dd */
    CheckDate?: string | null

    /** 巡查结果 */
    CheckResult?: doc.IPatrolCheckItem[] | null
  }

  /** 巡查统计结果 */
  class PatrolStatisticsQueryResult implements IPatrolStatisticsQueryResult {
    /**
     * Constructs a new PatrolStatisticsQueryResult.
     * @param [properties] Properties to set
     */
    constructor(properties?: doc.IPatrolStatisticsQueryResult)

    /** 巡查路线RID */
    public LineRid: string

    /** 路线规则RID */
    public RuleRID: string

    /** 应查数目 */
    public ShouldCheckCount: number

    /** 巡查日期 yyyy-mm-dd */
    public CheckDate: string

    /** 巡查结果 */
    public CheckResult: doc.IPatrolCheckItem[]

    /**
     * Encodes the specified PatrolStatisticsQueryResult message. Does not implicitly {@link doc.PatrolStatisticsQueryResult.verify|verify} messages.
     * @param message PatrolStatisticsQueryResult message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: doc.IPatrolStatisticsQueryResult,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a PatrolStatisticsQueryResult message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns PatrolStatisticsQueryResult
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): doc.PatrolStatisticsQueryResult
  }
}

/** Namespace yrpcmsg. */
export namespace yrpcmsg {
  /** Properties of a Ymsg. */
  interface IYmsg {
    /**
     * 整个rpc msg的长度，不包含此字段
     * 虽然这个长度可以很长，但是为了避免大包阻塞其它操作，通常都要限制长度,采用分包多发机制
     * 当使用基于包的传输通道时(udp/kcp/websocket)，此值可能为0(此时长度为收到的整个包的长度)
     */
    Len?: number | null

    /**
     * rpc command,rpc的命令和option
     * b15-b0(uint16):低16为rpc命令
     * b19-b16:body压缩方式 0:无压缩 1:lz4 2:zlib inflate/deflate
     * b23-b20:optbin压缩方式 0:无压缩 1:lz4 2:zlib inflate/deflate
     * b31-b24: not used
     */
    Cmd?: number | null

    /** session id，登录后一定会有,用于后台区分不同的用户请求 */
    Sid?: Uint8Array | null

    /** rpc call id,给分辨不同的rpc调用使用,调用方增1循环使用 */
    Cid?: number | null

    /** rpc no,从0开始增1使用,用于区分收到重复的包,特别是udp的情况下 */
    No?: number | null

    /** response code */
    Res?: number | null

    /** msg body */
    Body?: Uint8Array | null

    /** optional str parameter,额外的信息,一般不会有,有些特殊命令里面可能用到 */
    Optstr?: string | null

    /** optional binary parameter,额外的信息,一般不会有,有些特殊命令里面可能用到 */
    Optbin?: Uint8Array | null

    /** optional grpc meta */
    MetaInfo?: yrpcmsg.IMeta | null
  }

  /**
   * 系统中所有的消息交互底层都以此为包装
   * ymsg multiline comment
   */
  class Ymsg implements IYmsg {
    /**
     * Constructs a new Ymsg.
     * @param [properties] Properties to set
     */
    constructor(properties?: yrpcmsg.IYmsg)

    /**
     * 整个rpc msg的长度，不包含此字段
     * 虽然这个长度可以很长，但是为了避免大包阻塞其它操作，通常都要限制长度,采用分包多发机制
     * 当使用基于包的传输通道时(udp/kcp/websocket)，此值可能为0(此时长度为收到的整个包的长度)
     */
    public Len: number

    /**
     * rpc command,rpc的命令和option
     * b15-b0(uint16):低16为rpc命令
     * b19-b16:body压缩方式 0:无压缩 1:lz4 2:zlib inflate/deflate
     * b23-b20:optbin压缩方式 0:无压缩 1:lz4 2:zlib inflate/deflate
     * b31-b24: not used
     */
    public Cmd: number

    /** session id，登录后一定会有,用于后台区分不同的用户请求 */
    public Sid: Uint8Array

    /** rpc call id,给分辨不同的rpc调用使用,调用方增1循环使用 */
    public Cid: number

    /** rpc no,从0开始增1使用,用于区分收到重复的包,特别是udp的情况下 */
    public No: number

    /** response code */
    public Res: number

    /** msg body */
    public Body: Uint8Array

    /** optional str parameter,额外的信息,一般不会有,有些特殊命令里面可能用到 */
    public Optstr: string

    /** optional binary parameter,额外的信息,一般不会有,有些特殊命令里面可能用到 */
    public Optbin: Uint8Array

    /** optional grpc meta */
    public MetaInfo?: yrpcmsg.IMeta | null

    /**
     * Encodes the specified Ymsg message. Does not implicitly {@link yrpcmsg.Ymsg.verify|verify} messages.
     * @param message Ymsg message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: yrpcmsg.IYmsg,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a Ymsg message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns Ymsg
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): yrpcmsg.Ymsg
  }

  /** Properties of a MetaItem. */
  interface IMetaItem {
    /** MetaItem key */
    key?: string | null

    /** MetaItem vals */
    vals?: string[] | null
  }

  /** grpc meta data item */
  class MetaItem implements IMetaItem {
    /**
     * Constructs a new MetaItem.
     * @param [properties] Properties to set
     */
    constructor(properties?: yrpcmsg.IMetaItem)

    /** MetaItem key. */
    public key: string

    /** MetaItem vals. */
    public vals: string[]

    /**
     * Encodes the specified MetaItem message. Does not implicitly {@link yrpcmsg.MetaItem.verify|verify} messages.
     * @param message MetaItem message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: yrpcmsg.IMetaItem,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a MetaItem message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns MetaItem
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): yrpcmsg.MetaItem
  }

  /** Properties of a Meta. */
  interface IMeta {
    /** grpc meta */
    val?: yrpcmsg.IMetaItem[] | null
  }

  /** grpc meta */
  class Meta implements IMeta {
    /**
     * Constructs a new Meta.
     * @param [properties] Properties to set
     */
    constructor(properties?: yrpcmsg.IMeta)

    /** grpc meta */
    public val: yrpcmsg.IMetaItem[]

    /**
     * Encodes the specified Meta message. Does not implicitly {@link yrpcmsg.Meta.verify|verify} messages.
     * @param message Meta message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: yrpcmsg.IMeta,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a Meta message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns Meta
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): yrpcmsg.Meta
  }

  /** Properties of a GrpcMeta. */
  interface IGrpcMeta {
    /** GrpcMeta Header */
    Header?: yrpcmsg.IMeta | null

    /** GrpcMeta Trailer */
    Trailer?: yrpcmsg.IMeta | null
  }

  /** grpc Header Trailer meta */
  class GrpcMeta implements IGrpcMeta {
    /**
     * Constructs a new GrpcMeta.
     * @param [properties] Properties to set
     */
    constructor(properties?: yrpcmsg.IGrpcMeta)

    /** GrpcMeta Header. */
    public Header?: yrpcmsg.IMeta | null

    /** GrpcMeta Trailer. */
    public Trailer?: yrpcmsg.IMeta | null

    /**
     * Encodes the specified GrpcMeta message. Does not implicitly {@link yrpcmsg.GrpcMeta.verify|verify} messages.
     * @param message GrpcMeta message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: yrpcmsg.IGrpcMeta,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a GrpcMeta message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns GrpcMeta
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): yrpcmsg.GrpcMeta
  }

  /** Properties of a Yempty. */
  interface IYempty {}

  /** Represents a Yempty. */
  class Yempty implements IYempty {
    /**
     * Constructs a new Yempty.
     * @param [properties] Properties to set
     */
    constructor(properties?: yrpcmsg.IYempty)

    /**
     * Encodes the specified Yempty message. Does not implicitly {@link yrpcmsg.Yempty.verify|verify} messages.
     * @param message Yempty message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: yrpcmsg.IYempty,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a Yempty message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns Yempty
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): yrpcmsg.Yempty
  }

  /** Properties of a Ynocare. */
  interface IYnocare {}

  /**
   * A generic nocare message that you can use to info the call is not important
   * and no care the result. A typical example is to use it in report log/trace.
   * For instance:
   *
   * service Log {
   * rpc Log(infos) returns (yrpc.Ynocare);
   * }
   */
  class Ynocare implements IYnocare {
    /**
     * Constructs a new Ynocare.
     * @param [properties] Properties to set
     */
    constructor(properties?: yrpcmsg.IYnocare)

    /**
     * Encodes the specified Ynocare message. Does not implicitly {@link yrpcmsg.Ynocare.verify|verify} messages.
     * @param message Ynocare message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: yrpcmsg.IYnocare,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a Ynocare message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns Ynocare
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): yrpcmsg.Ynocare
  }

  /** Properties of an UnixTime. */
  interface IUnixTime {
    /** Unix time, the number of miliseconds elapsed since January 1, 1970 UTC */
    TimeUnix?: Long | null

    /** utc time yyyy-MM-dd hh:mm:ss.zzz */
    TimeStr?: string | null
  }

  /** Represents an UnixTime. */
  class UnixTime implements IUnixTime {
    /**
     * Constructs a new UnixTime.
     * @param [properties] Properties to set
     */
    constructor(properties?: yrpcmsg.IUnixTime)

    /** Unix time, the number of miliseconds elapsed since January 1, 1970 UTC */
    public TimeUnix: Long

    /** utc time yyyy-MM-dd hh:mm:ss.zzz */
    public TimeStr: string

    /**
     * Encodes the specified UnixTime message. Does not implicitly {@link yrpcmsg.UnixTime.verify|verify} messages.
     * @param message UnixTime message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: yrpcmsg.IUnixTime,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes an UnixTime message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns UnixTime
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): yrpcmsg.UnixTime
  }
}

/** Namespace bysdb. */
export namespace bysdb {
  /** Properties of a DbController. */
  interface IDbController {
    /**
     * @db uuid primary key
     * 行ID
     */
    RID?: string | null

    /**
     * @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 控制器所属的群组
     */
    OrgRID?: string | null

    /**
     * @db varchar(16) not null unique
     * controller编号
     */
    ControllerNo?: string | null

    /**
     * @db text
     * controller描述信息
     */
    ControllerDescription?: string | null

    /**
     * @db int
     * 控制器类型 1:中继控制器  2:基站控制器
     */
    ControllerType?: number | null

    /**
     * @db uuid
     * 上级控制器RID, 可以没有，中继控制器一般都有
     */
    ParentRID?: string | null

    /**
     * @db double precision
     * 经度
     */
    Lon?: number | null

    /**
     * @db double precision
     * 纬度
     */
    Lat?: number | null

    /**
     * @db jsonb not null default  '{}'::jsonb
     * 其它设置，可以在里面扩展需要用到的其它信息
     */
    Setting?: string | null

    /**
     * @db int not null unique
     * 控制器硬件ID，int32 > 0
     */
    ControllerHWID?: number | null

    /**
     * @db int not null default 1
     * 控制器可用信道数，中继控制器为1,基站按实际情况
     */
    ChannelCount?: number | null

    /**
     * @db int default 12
     * 地图开始显示级别
     */
    MapShowLevel?: number | null

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     */
    UpdatedAt?: string | null

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */
    UpdatedDC?: string | null

    /**
     * @db int
     * 默认通讯方式， 1:有线网络， 2:4g网络 3:无线FSK
     */
    DefaultNetworkType?: number | null

    /**
     * @db int
     * 中继对应的上级控制器的通道
     */
    ParentChannelNo?: number | null
  }

  /** 控制器信息表 */
  class DbController implements IDbController {
    /**
     * Constructs a new DbController.
     * @rpc crud pcrud
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbControllerOrgRID on DbController USING hash(OrgRID);
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbControllerParentRID on DbController USING hash(ParentRID);
     * @dbpost CREATE UNIQUE INDEX IF NOT EXISTS uidxDbControllerParent ON DbController (ParentRID, ParentChannelNo) WHERE ControllerType=1;
     * @param [properties] Properties to set
     */
    constructor(properties?: bysdb.IDbController)

    /**
     * @db uuid primary key
     * 行ID
     */
    public RID: string

    /**
     * @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 控制器所属的群组
     */
    public OrgRID: string

    /**
     * @db varchar(16) not null unique
     * controller编号
     */
    public ControllerNo: string

    /**
     * @db text
     * controller描述信息
     */
    public ControllerDescription: string

    /**
     * @db int
     * 控制器类型 1:中继控制器  2:基站控制器
     */
    public ControllerType: number

    /**
     * @db uuid
     * 上级控制器RID, 可以没有，中继控制器一般都有
     */
    public ParentRID: string

    /**
     * @db double precision
     * 经度
     */
    public Lon: number

    /**
     * @db double precision
     * 纬度
     */
    public Lat: number

    /**
     * @db jsonb not null default  '{}'::jsonb
     * 其它设置，可以在里面扩展需要用到的其它信息
     */
    public Setting: string

    /**
     * @db int not null unique
     * 控制器硬件ID，int32 > 0
     */
    public ControllerHWID: number

    /**
     * @db int not null default 1
     * 控制器可用信道数，中继控制器为1,基站按实际情况
     */
    public ChannelCount: number

    /**
     * @db int default 12
     * 地图开始显示级别
     */
    public MapShowLevel: number

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     */
    public UpdatedAt: string

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */
    public UpdatedDC: string

    /**
     * @db int
     * 默认通讯方式， 1:有线网络， 2:4g网络 3:无线FSK
     */
    public DefaultNetworkType: number

    /**
     * @db int
     * 中继对应的上级控制器的通道
     */
    public ParentChannelNo: number

    /**
     * Encodes the specified DbController message. Does not implicitly {@link bysdb.DbController.verify|verify} messages.
     * @param message DbController message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: bysdb.IDbController,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a DbController message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns DbController
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): bysdb.DbController
  }

  /** Properties of a DbBysMarker. */
  interface IDbBysMarker {
    /**
     * @db uuid primary key
     * 行ID
     */
    RID?: string | null

    /**
     * @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 界桩所属的群组
     */
    OrgRID?: string | null

    /**
     * @db varchar(16) not null unique
     * 界桩编号
     */
    MarkerNo?: string | null

    /**
     * @db text
     * 界桩描述信息
     */
    MarkerDescription?: string | null

    /**
     * @db uuid REFERENCES DbController(RID) ON DELETE RESTRICT
     * 所属控制器RID
     */
    ControllerRID?: string | null

    /**
     * @db int not null
     * 所属的基站通道，如果所属的控制器为中继控制器，此值固定为1
     */
    ControllerChannel?: number | null

    /**
     * @db double precision
     * 经度
     */
    Lon?: number | null

    /**
     * @db double precision
     * 纬度
     */
    Lat?: number | null

    /**
     * @db int not null unique
     * 界桩硬件ID,范围
     */
    MarkerHWID?: number | null

    /**
     * @db jsonb not null default  '{}'::jsonb
     * 其它设置，可以在里面扩展需要用到的其它信息
     * 新的红线图规则属性为：redLine，不存在该字段为默认连接，空数组为不连接，
     * 数组元素为界桩硬件ID，每个都连接
     */
    Setting?: string | null

    /**
     * @db int
     * 界桩型号 1:长型 X*Y*Z cm, 2:短型 x*y*z cm
     */
    MarkerModel?: number | null

    /**
     * @db int default 12
     * 地图开始显示级别
     */
    MapShowLevel?: number | null

    /**
     * @db int not null
     * 在所属于基站下的排队号
     */
    MarkerQueueNo?: number | null

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     */
    UpdatedAt?: string | null

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */
    UpdatedDC?: string | null

    /**
     * @db timestamp not null default now_utc()
     * 参数更新时间
     */
    MarkerParamTime?: string | null

    /**
     * @db int not null default 24
     * 打卡时间间隔(6-24小时)
     */
    MarkerDayInterval?: number | null

    /**
     * @db int not null default 6
     * 排队发射间隔时间（秒）
     */
    MarkerQueueInterval?: number | null

    /**
     * @db int not null default 60
     * 报警后发射间隔(30-240秒)
     */
    MarkerEmergentInterval?: number | null

    /**
     * @db int not null default 1
     * 界桩通信信道
     */
    MarkerChannel?: number | null

    /**
     * @db time not null
     * 界桩苏醒基准时间
     */
    MarkerWakeupBaseTime?: string | null

    /**
     * @db int not null default 0
     * 界桩是否已经被遥毙, 0:遥活 1:遥闭  2:遥晕
     */
    MarkerDisabled?: number | null

    /**
     * @db boolean default true
     * 界桩是否有安装电子设备
     */
    HasInstallDevice?: boolean | null

    /**
     * @db boolean default false
     * 石头界桩是否已安装
     */
    HasInstallStone?: boolean | null

    /**
     * @db int not null default 0
     * 常规界桩：0, 4g界桩(BF-DTD002): 1, 4g界桩带摄像机(BF-DTD002(Pro)): 2, 专网界桩dmr版(BF-DTD003): 3
     */
    MarkerType?: number | null

    /**
     * @db jsonb not null default  '{}'::jsonb
     * MarkerType=1时，填写bysproto.BDataUpdate
     */
    MarkerSettings?: string | null

    /**
     * @db varchar(20)
     * 4G界桩的iccid卡号，20位数字字符串
     */
    ICCID?: string | null

    /**
     * @db timestamp
     * iccid卡号到期日期
     */
    ExpirationDate?: string | null

    /**
     * @db text
     * 4g界桩的设备唯一标识
     */
    IMEI?: string | null

    /**
     * @db int not null default 0
     * 摄像机是否已经被遥毙, 0:遥活 1:遥闭  2:遥晕
     */
    CameraDisabled?: number | null
  }

  /** 界桩信息表 */
  class DbBysMarker implements IDbBysMarker {
    /**
     * Constructs a new DbBysMarker.
     * @rpc crud pcrud
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbBysMarkerOrgRID on DbBysMarker USING hash(OrgRID);
     * @dbpost ALTER TABLE DbBysMarker ADD COLUMN IF NOT EXISTS MarkerType integer not null default 0;
     * @dbpost ALTER TABLE DbBysMarker ADD COLUMN IF NOT EXISTS MarkerSettings jsonb not null default  '{}'::jsonb;
     * @dbpost ALTER TABLE dbbysmarker DROP CONSTRAINT if exists dbbysmarker_controllerrid_controllerchannel_markerqueueno_key;
     * @dbpost drop index if exists dbbysmarker_controllerrid_controllerchannel_markerqueueno_key;
     * @dbpost CREATE UNIQUE INDEX IF NOT EXISTS idxdbbysmarker_controllerrid_controllerchannel_markerqueueno_key on DbBysMarker (ControllerRID, ControllerChannel, MarkerQueueNo) where MarkerType=0; --同一个信道的排队号必须唯一,只针对旧的界桩
     * @dbpost ALTER TABLE DbBysMarker ALTER COLUMN ControllerRID DROP NOT NULL;
     * @dbpost ALTER TABLE DbBysMarker ADD COLUMN IF NOT EXISTS ICCID varchar(20);
     * @dbpost ALTER TABLE DbBysMarker ADD COLUMN IF NOT EXISTS ExpirationDate timestamp;
     * @dbpost CREATE UNIQUE INDEX IF NOT EXISTS idxDbBysMarkerICCID on DbBysMarker (ICCID) where MarkerType=1;
     * @dbpost ALTER TABLE DbBysMarker ADD COLUMN IF NOT EXISTS IMEI text;
     * @dbpost ALTER TABLE DbBysMarker ADD COLUMN IF NOT EXISTS CameraDisabled integer not null default 0;
     * @param [properties] Properties to set
     */
    constructor(properties?: bysdb.IDbBysMarker)

    /**
     * @db uuid primary key
     * 行ID
     */
    public RID: string

    /**
     * @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 界桩所属的群组
     */
    public OrgRID: string

    /**
     * @db varchar(16) not null unique
     * 界桩编号
     */
    public MarkerNo: string

    /**
     * @db text
     * 界桩描述信息
     */
    public MarkerDescription: string

    /**
     * @db uuid REFERENCES DbController(RID) ON DELETE RESTRICT
     * 所属控制器RID
     */
    public ControllerRID: string

    /**
     * @db int not null
     * 所属的基站通道，如果所属的控制器为中继控制器，此值固定为1
     */
    public ControllerChannel: number

    /**
     * @db double precision
     * 经度
     */
    public Lon: number

    /**
     * @db double precision
     * 纬度
     */
    public Lat: number

    /**
     * @db int not null unique
     * 界桩硬件ID,范围
     */
    public MarkerHWID: number

    /**
     * @db jsonb not null default  '{}'::jsonb
     * 其它设置，可以在里面扩展需要用到的其它信息
     * 新的红线图规则属性为：redLine，不存在该字段为默认连接，空数组为不连接，
     * 数组元素为界桩硬件ID，每个都连接
     */
    public Setting: string

    /**
     * @db int
     * 界桩型号 1:长型 X*Y*Z cm, 2:短型 x*y*z cm
     */
    public MarkerModel: number

    /**
     * @db int default 12
     * 地图开始显示级别
     */
    public MapShowLevel: number

    /**
     * @db int not null
     * 在所属于基站下的排队号
     */
    public MarkerQueueNo: number

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     */
    public UpdatedAt: string

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */
    public UpdatedDC: string

    /**
     * @db timestamp not null default now_utc()
     * 参数更新时间
     */
    public MarkerParamTime: string

    /**
     * @db int not null default 24
     * 打卡时间间隔(6-24小时)
     */
    public MarkerDayInterval: number

    /**
     * @db int not null default 6
     * 排队发射间隔时间（秒）
     */
    public MarkerQueueInterval: number

    /**
     * @db int not null default 60
     * 报警后发射间隔(30-240秒)
     */
    public MarkerEmergentInterval: number

    /**
     * @db int not null default 1
     * 界桩通信信道
     */
    public MarkerChannel: number

    /**
     * @db time not null
     * 界桩苏醒基准时间
     */
    public MarkerWakeupBaseTime: string

    /**
     * @db int not null default 0
     * 界桩是否已经被遥毙, 0:遥活 1:遥闭  2:遥晕
     */
    public MarkerDisabled: number

    /**
     * @db boolean default true
     * 界桩是否有安装电子设备
     */
    public HasInstallDevice: boolean

    /**
     * @db boolean default false
     * 石头界桩是否已安装
     */
    public HasInstallStone: boolean

    /**
     * @db int not null default 0
     * 常规界桩：0, 4g界桩(BF-DTD002): 1, 4g界桩带摄像机(BF-DTD002(Pro)): 2, 专网界桩dmr版(BF-DTD003): 3
     */
    public MarkerType: number

    /**
     * @db jsonb not null default  '{}'::jsonb
     * MarkerType=1时，填写bysproto.BDataUpdate
     */
    public MarkerSettings: string

    /**
     * @db varchar(20)
     * 4G界桩的iccid卡号，20位数字字符串
     */
    public ICCID: string

    /**
     * @db timestamp
     * iccid卡号到期日期
     */
    public ExpirationDate: string

    /**
     * @db text
     * 4g界桩的设备唯一标识
     */
    public IMEI: string

    /**
     * @db int not null default 0
     * 摄像机是否已经被遥毙, 0:遥活 1:遥闭  2:遥晕
     */
    public CameraDisabled: number

    /**
     * Encodes the specified DbBysMarker message. Does not implicitly {@link bysdb.DbBysMarker.verify|verify} messages.
     * @param message DbBysMarker message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: bysdb.IDbBysMarker,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a DbBysMarker message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns DbBysMarker
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): bysdb.DbBysMarker
  }

  /** Properties of a DbControllerOnlineHistory. */
  interface IDbControllerOnlineHistory {
    /**
     * @db uuid not null
     * 所属的群组
     */
    OrgRID?: string | null

    /**
     * @db timestamp not null
     * 动作时间,utc
     */
    ActionTime?: string | null

    /**
     * @db int
     * 控制器硬件ID
     */
    ControllerHWID?: number | null

    /**
     * @db int
     * action 1:上线 2:下线 11:ping信息
     */
    ActionCode?: number | null

    /**
     * @db text
     * ip/状态信息
     */
    IpInfo?: string | null

    /**
     * @db int
     * 登录网络类型,只对上线有效
     */
    NetworkType?: number | null

    /**
     * @db real
     * 电量V
     */
    Power?: number | null

    /**
     * @db int
     * 状态
     */
    Status?: number | null

    /** 登录设备类型 0:fsk控制器 1:4g界桩 */
    DeviceType?: number | null
  }

  /** 控制器上下线历史表，以月为单位分表 */
  class DbControllerOnlineHistory implements IDbControllerOnlineHistory {
    /**
     * Constructs a new DbControllerOnlineHistory.
     * @rpc pcrud
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbControllerOnlineHistoryControllerHWID on DbControllerOnlineHistory USING hash(ControllerHWID);
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbControllerOnlineHistoryOrgRID on DbControllerOnlineHistory USING hash(OrgRID);
     * @dbpost ALTER TABLE dbcontrolleronlinehistory ADD COLUMN IF NOT EXISTS Status integer;
     * @dbpost ALTER TABLE dbcontrolleronlinehistory ADD COLUMN IF NOT EXISTS DeviceType integer;
     * @param [properties] Properties to set
     */
    constructor(properties?: bysdb.IDbControllerOnlineHistory)

    /**
     * @db uuid not null
     * 所属的群组
     */
    public OrgRID: string

    /**
     * @db timestamp not null
     * 动作时间,utc
     */
    public ActionTime: string

    /**
     * @db int
     * 控制器硬件ID
     */
    public ControllerHWID: number

    /**
     * @db int
     * action 1:上线 2:下线 11:ping信息
     */
    public ActionCode: number

    /**
     * @db text
     * ip/状态信息
     */
    public IpInfo: string

    /**
     * @db int
     * 登录网络类型,只对上线有效
     */
    public NetworkType: number

    /**
     * @db real
     * 电量V
     */
    public Power: number

    /**
     * @db int
     * 状态
     */
    public Status: number

    /** 登录设备类型 0:fsk控制器 1:4g界桩 */
    public DeviceType: number

    /**
     * Encodes the specified DbControllerOnlineHistory message. Does not implicitly {@link bysdb.DbControllerOnlineHistory.verify|verify} messages.
     * @param message DbControllerOnlineHistory message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: bysdb.IDbControllerOnlineHistory,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a DbControllerOnlineHistory message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns DbControllerOnlineHistory
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): bysdb.DbControllerOnlineHistory
  }

  /** Properties of a DbMediaInfo. */
  interface IDbMediaInfo {
    /**
     * @db uuid primary key
     * 行ID
     */
    RID?: string | null

    /**
     * @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 物体所属的群组
     */
    OrgRID?: string | null

    /**
     * @db uuid null REFERENCES DbBysMarker(RID) ON DELETE set null
     * 界桩的RID
     */
    MarkerRID?: string | null

    /**
     * @db uuid null REFERENCES DbController(RID) ON DELETE set null
     * 控制器的RID
     */
    ControllerRID?: string | null

    /**
     * @db int
     * 媒体类型 1:normal pic 2:3d pic 3:video
     */
    MediaType?: number | null

    /**
     * @db text
     * 媒体原始文件名
     */
    MediaOrigFileName?: string | null

    /**
     * @db uuid REFERENCES DbUser(RID) ON DELETE set null
     * 上传用户rid
     */
    UploadUserRID?: string | null

    /**
     * @db text
     * 描述信息
     */
    MediaDescription?: string | null

    /**
     * @db timestamp not null
     * 上传时间,utc
     */
    UploadTime?: string | null

    /**
     * @db jsonb not null default  '{}'::jsonb
     * 其它设置，可以在里面扩展需要用到的其它信息
     */
    Setting?: string | null

    /**
     * @db uuid REFERENCES DbUser(RID) ON DELETE set null
     * 最新编辑用户rid
     */
    LastUpdateUserRID?: string | null

    /**
     * @db timestamp not null default now_utc()
     * 最新编辑时间,utc
     */
    LastUpdateTime?: string | null
  }

  /** 界桩/控制器媒体信息表 */
  class DbMediaInfo implements IDbMediaInfo {
    /**
     * Constructs a new DbMediaInfo.
     * @rpc pcrud
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbMediaInfoOrgRID on DbMediaInfo USING hash(OrgRID);
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbMediaInfoMarkerRID on DbMediaInfo USING hash(MarkerRID);
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbMediaInfoControllerRID on DbMediaInfo USING hash(ControllerRID);
     * @param [properties] Properties to set
     */
    constructor(properties?: bysdb.IDbMediaInfo)

    /**
     * @db uuid primary key
     * 行ID
     */
    public RID: string

    /**
     * @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 物体所属的群组
     */
    public OrgRID: string

    /**
     * @db uuid null REFERENCES DbBysMarker(RID) ON DELETE set null
     * 界桩的RID
     */
    public MarkerRID: string

    /**
     * @db uuid null REFERENCES DbController(RID) ON DELETE set null
     * 控制器的RID
     */
    public ControllerRID: string

    /**
     * @db int
     * 媒体类型 1:normal pic 2:3d pic 3:video
     */
    public MediaType: number

    /**
     * @db text
     * 媒体原始文件名
     */
    public MediaOrigFileName: string

    /**
     * @db uuid REFERENCES DbUser(RID) ON DELETE set null
     * 上传用户rid
     */
    public UploadUserRID: string

    /**
     * @db text
     * 描述信息
     */
    public MediaDescription: string

    /**
     * @db timestamp not null
     * 上传时间,utc
     */
    public UploadTime: string

    /**
     * @db jsonb not null default  '{}'::jsonb
     * 其它设置，可以在里面扩展需要用到的其它信息
     */
    public Setting: string

    /**
     * @db uuid REFERENCES DbUser(RID) ON DELETE set null
     * 最新编辑用户rid
     */
    public LastUpdateUserRID: string

    /**
     * @db timestamp not null default now_utc()
     * 最新编辑时间,utc
     */
    public LastUpdateTime: string

    /**
     * Encodes the specified DbMediaInfo message. Does not implicitly {@link bysdb.DbMediaInfo.verify|verify} messages.
     * @param message DbMediaInfo message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: bysdb.IDbMediaInfo,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a DbMediaInfo message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns DbMediaInfo
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): bysdb.DbMediaInfo
  }

  /** Properties of a DbMarkerHistory. */
  interface IDbMarkerHistory {
    /**
     * @db int
     * 接收的控制器ID
     */
    ControllerID?: number | null

    /**
     * @db int
     * 接收的控制器通道
     */
    ControllerChannel?: number | null

    /**
     * @db uuid not null
     * 所属的群组
     */
    OrgRID?: string | null

    /**
     * @db timestamp not null
     * 动作时间,utc
     */
    ActionTime?: string | null

    /**
     * @db int
     * 界桩硬件ID
     */
    MarkerHWID?: number | null

    /**
     * @db int
     * action 0xd1, 0xd2
     * 4g界桩信息上报21, ReportInfo=bysproto.BInfoReporting
     * 4g界桩报警22, ReportInfo=bysproto.BAlarmReporting
     */
    ActionCode?: number | null

    /**
     * @db int
     */
    Status?: number | null

    /**
     * @db text
     * 配置参数信息
     */
    ParamInfo?: string | null

    /**
     * @db double precision
     * gps lon
     */
    Lon?: number | null

    /**
     * @db double precision
     * gps lat
     */
    Lat?: number | null

    /**
     * @db timestamp
     * cmd time
     */
    CmdTime?: string | null

    /**
     * @db int
     * 实际接收的控制器ID（中继/基站）
     */
    RecvControllerID?: number | null

    /**
     * @db jsonb not null default  '{}'::jsonb
     */
    ReportInfo?: string | null
  }

  /** 界桩上传数据历史表，以月为单位分表 */
  class DbMarkerHistory implements IDbMarkerHistory {
    /**
     * Constructs a new DbMarkerHistory.
     * @rpc pcrud
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbMarkerHistoryMarkerHWID on DbMarkerHistory USING hash(MarkerHWID);
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbMarkerHistoryOrgRID on DbMarkerHistory USING hash(OrgRID);
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbMarkerHistoryEmergency on DbMarkerHistory (Status) where (Status & 128) >0;
     * @dbpost alter table DbMarkerHistory ADD COLUMN IF NOT EXISTS RecvControllerID int;
     * @dbpost alter table DbMarkerHistory ADD COLUMN IF NOT EXISTS ReportInfo jsonb not null default  '{}'::jsonb;
     * @param [properties] Properties to set
     */
    constructor(properties?: bysdb.IDbMarkerHistory)

    /**
     * @db int
     * 接收的控制器ID
     */
    public ControllerID: number

    /**
     * @db int
     * 接收的控制器通道
     */
    public ControllerChannel: number

    /**
     * @db uuid not null
     * 所属的群组
     */
    public OrgRID: string

    /**
     * @db timestamp not null
     * 动作时间,utc
     */
    public ActionTime: string

    /**
     * @db int
     * 界桩硬件ID
     */
    public MarkerHWID: number

    /**
     * @db int
     * action 0xd1, 0xd2
     * 4g界桩信息上报21, ReportInfo=bysproto.BInfoReporting
     * 4g界桩报警22, ReportInfo=bysproto.BAlarmReporting
     */
    public ActionCode: number

    /**
     * @db int
     */
    public Status: number

    /**
     * @db text
     * 配置参数信息
     */
    public ParamInfo: string

    /**
     * @db double precision
     * gps lon
     */
    public Lon: number

    /**
     * @db double precision
     * gps lat
     */
    public Lat: number

    /**
     * @db timestamp
     * cmd time
     */
    public CmdTime: string

    /**
     * @db int
     * 实际接收的控制器ID（中继/基站）
     */
    public RecvControllerID: number

    /**
     * @db jsonb not null default  '{}'::jsonb
     */
    public ReportInfo: string

    /**
     * Encodes the specified DbMarkerHistory message. Does not implicitly {@link bysdb.DbMarkerHistory.verify|verify} messages.
     * @param message DbMarkerHistory message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: bysdb.IDbMarkerHistory,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a DbMarkerHistory message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns DbMarkerHistory
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): bysdb.DbMarkerHistory
  }

  /** Properties of a DbMarkerPatrolHistory. */
  interface IDbMarkerPatrolHistory {
    /**
     * @db text
     * NFC卡片ID，hex string
     */
    NFCID?: string | null

    /**
     * @db timestamp not null
     * NFC打卡时间,utc
     */
    ActionTime?: string | null

    /**
     * @db int
     * 界桩硬件ID
     */
    MarkerHWID?: number | null

    /**
     * @db uuid not null
     * 打卡用户
     */
    UserID?: string | null

    /**
     * @db uuid not null
     * 所属的群组
     */
    OrgRID?: string | null
  }

  /** 4g界桩NFC巡查打卡历史表，以月为单位分表 */
  class DbMarkerPatrolHistory implements IDbMarkerPatrolHistory {
    /**
     * Constructs a new DbMarkerPatrolHistory.
     * @rpc pcrud
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbMarkerPatrolHistoryHWID on DbMarkerPatrolHistory USING hash(MarkerHWID);
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbMarkerPatrolHistoryOrgRID on DbMarkerPatrolHistory USING hash(OrgRID);
     * @param [properties] Properties to set
     */
    constructor(properties?: bysdb.IDbMarkerPatrolHistory)

    /**
     * @db text
     * NFC卡片ID，hex string
     */
    public NFCID: string

    /**
     * @db timestamp not null
     * NFC打卡时间,utc
     */
    public ActionTime: string

    /**
     * @db int
     * 界桩硬件ID
     */
    public MarkerHWID: number

    /**
     * @db uuid not null
     * 打卡用户
     */
    public UserID: string

    /**
     * @db uuid not null
     * 所属的群组
     */
    public OrgRID: string

    /**
     * Encodes the specified DbMarkerPatrolHistory message. Does not implicitly {@link bysdb.DbMarkerPatrolHistory.verify|verify} messages.
     * @param message DbMarkerPatrolHistory message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: bysdb.IDbMarkerPatrolHistory,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a DbMarkerPatrolHistory message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns DbMarkerPatrolHistory
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): bysdb.DbMarkerPatrolHistory
  }

  /** Properties of a DbNFCPatrolLine. */
  interface IDbNFCPatrolLine {
    /**
     * @db uuid primary key
     * 行ID
     */
    RID?: string | null

    /**
     * @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 线路的归属组
     */
    OrgRID?: string | null

    /**
     * @db varchar(16) not null unique
     * 线路名称
     */
    Name?: string | null

    /**
     * @db text
     */
    Note?: string | null

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     */
    UpdatedAt?: string | null

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */
    UpdatedDC?: string | null
  }

  /** 巡查线路表 */
  class DbNFCPatrolLine implements IDbNFCPatrolLine {
    /**
     * Constructs a new DbNFCPatrolLine.
     * @rpc pcrud
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbNFCPatrolLineOrgRID on DbNFCPatrolLine USING hash(OrgRID);
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbNFCPatrolLineName on DbNFCPatrolLine USING hash(Name);
     * @param [properties] Properties to set
     */
    constructor(properties?: bysdb.IDbNFCPatrolLine)

    /**
     * @db uuid primary key
     * 行ID
     */
    public RID: string

    /**
     * @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 线路的归属组
     */
    public OrgRID: string

    /**
     * @db varchar(16) not null unique
     * 线路名称
     */
    public Name: string

    /**
     * @db text
     */
    public Note: string

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     */
    public UpdatedAt: string

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */
    public UpdatedDC: string

    /**
     * Encodes the specified DbNFCPatrolLine message. Does not implicitly {@link bysdb.DbNFCPatrolLine.verify|verify} messages.
     * @param message DbNFCPatrolLine message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: bysdb.IDbNFCPatrolLine,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a DbNFCPatrolLine message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns DbNFCPatrolLine
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): bysdb.DbNFCPatrolLine
  }

  /** Properties of a DbNFCPatrolLineDetail. */
  interface IDbNFCPatrolLineDetail {
    /**
     * @db uuid primary key
     * 行ID
     */
    RID?: string | null

    /**
     * @db uuid not null REFERENCES DbNFCPatrolLine(RID) ON DELETE CASCADE
     * 归属线路
     */
    LineRID?: string | null

    /**
     * @db uuid not null REFERENCES DbBysMarker(RID) ON DELETE CASCADE
     * 线路下的巡查点
     */
    MarkerRID?: string | null

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     */
    UpdatedAt?: string | null

    /**
     * @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 归属组，与路线的上线一致
     */
    OrgRID?: string | null
  }

  /** 巡查线路详细表，分开主要是方便使用数据库外键约束 */
  class DbNFCPatrolLineDetail implements IDbNFCPatrolLineDetail {
    /**
     * Constructs a new DbNFCPatrolLineDetail.
     * @rpc pcrud
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbNFCPatrolLineDetailOrgRID on DbNFCPatrolLineDetail USING hash(OrgRID);
     * @dbpost CREATE INDEX IF NOT EXISTS idxDDbNFCPatrolLineDetailLineRID on DbNFCPatrolLineDetail USING hash(LineRID);
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbNFCPatrolLineDetailMarkerRID on DbNFCPatrolLineDetail USING hash(MarkerRID);
     * @param [properties] Properties to set
     */
    constructor(properties?: bysdb.IDbNFCPatrolLineDetail)

    /**
     * @db uuid primary key
     * 行ID
     */
    public RID: string

    /**
     * @db uuid not null REFERENCES DbNFCPatrolLine(RID) ON DELETE CASCADE
     * 归属线路
     */
    public LineRID: string

    /**
     * @db uuid not null REFERENCES DbBysMarker(RID) ON DELETE CASCADE
     * 线路下的巡查点
     */
    public MarkerRID: string

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     */
    public UpdatedAt: string

    /**
     * @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 归属组，与路线的上线一致
     */
    public OrgRID: string

    /**
     * Encodes the specified DbNFCPatrolLineDetail message. Does not implicitly {@link bysdb.DbNFCPatrolLineDetail.verify|verify} messages.
     * @param message DbNFCPatrolLineDetail message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: bysdb.IDbNFCPatrolLineDetail,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a DbNFCPatrolLineDetail message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns DbNFCPatrolLineDetail
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): bysdb.DbNFCPatrolLineDetail
  }

  /** Properties of a DbNFCPatrolLineRules. */
  interface IDbNFCPatrolLineRules {
    /**
     * @db uuid primary key
     * 行ID
     */
    RID?: string | null

    /**
     * @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 规则的归属组
     */
    OrgRID?: string | null

    /**
     * @db varchar(16) not null unique
     * 规则名称
     */
    Name?: string | null

    /**
     * @db boolean default false
     * 星期一
     */
    Day1?: boolean | null

    /**
     * @db boolean default false
     * 星期二
     */
    Day2?: boolean | null

    /**
     * @db boolean default false
     * 星期三
     */
    Day3?: boolean | null

    /**
     * @db boolean default false
     * 星期四
     */
    Day4?: boolean | null

    /**
     * @db boolean default false
     * 星期五
     */
    Day5?: boolean | null

    /**
     * @db boolean default false
     * 星期六
     */
    Day6?: boolean | null

    /**
     * @db boolean default false
     * 星期日
     */
    Day7?: boolean | null

    /**
     * @db time
     * 巡查开始的时间
     */
    CheckStartTime?: string | null

    /**
     * @db time
     * 巡查结束的时间
     */
    CheckEndTime?: string | null

    /**
     * @db int not null default 1
     * 巡查次数
     */
    CheckCount?: number | null

    /**
     * @db int default 0
     * 线路巡查规则生效时间的类型 0:总是生效 1:按有效时间段
     */
    EffectiveType?: number | null

    /**
     * @db timestamp
     * 规则开始生效时间
     */
    EffectiveStart?: string | null

    /**
     * @db timestamp
     * 规则生效结束时间
     */
    EffectiveEnd?: string | null

    /**
     * @db text
     */
    Note?: string | null

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     */
    UpdatedAt?: string | null

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */
    UpdatedDC?: string | null
  }

  /** 界桩NFC巡查规则 */
  class DbNFCPatrolLineRules implements IDbNFCPatrolLineRules {
    /**
     * Constructs a new DbNFCPatrolLineRules.
     * @rpc pcrud
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbNFCPatrolLineRulesOrgRID on DbNFCPatrolLineRules USING hash(OrgRID);
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbNFCPatrolLineRulesName on DbNFCPatrolLineRules USING hash(Name);
     * @param [properties] Properties to set
     */
    constructor(properties?: bysdb.IDbNFCPatrolLineRules)

    /**
     * @db uuid primary key
     * 行ID
     */
    public RID: string

    /**
     * @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 规则的归属组
     */
    public OrgRID: string

    /**
     * @db varchar(16) not null unique
     * 规则名称
     */
    public Name: string

    /**
     * @db boolean default false
     * 星期一
     */
    public Day1: boolean

    /**
     * @db boolean default false
     * 星期二
     */
    public Day2: boolean

    /**
     * @db boolean default false
     * 星期三
     */
    public Day3: boolean

    /**
     * @db boolean default false
     * 星期四
     */
    public Day4: boolean

    /**
     * @db boolean default false
     * 星期五
     */
    public Day5: boolean

    /**
     * @db boolean default false
     * 星期六
     */
    public Day6: boolean

    /**
     * @db boolean default false
     * 星期日
     */
    public Day7: boolean

    /**
     * @db time
     * 巡查开始的时间
     */
    public CheckStartTime: string

    /**
     * @db time
     * 巡查结束的时间
     */
    public CheckEndTime: string

    /**
     * @db int not null default 1
     * 巡查次数
     */
    public CheckCount: number

    /**
     * @db int default 0
     * 线路巡查规则生效时间的类型 0:总是生效 1:按有效时间段
     */
    public EffectiveType: number

    /**
     * @db timestamp
     * 规则开始生效时间
     */
    public EffectiveStart: string

    /**
     * @db timestamp
     * 规则生效结束时间
     */
    public EffectiveEnd: string

    /**
     * @db text
     */
    public Note: string

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     */
    public UpdatedAt: string

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */
    public UpdatedDC: string

    /**
     * Encodes the specified DbNFCPatrolLineRules message. Does not implicitly {@link bysdb.DbNFCPatrolLineRules.verify|verify} messages.
     * @param message DbNFCPatrolLineRules message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: bysdb.IDbNFCPatrolLineRules,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a DbNFCPatrolLineRules message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns DbNFCPatrolLineRules
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): bysdb.DbNFCPatrolLineRules
  }

  /** Properties of a DbNFCPatrolLineAndRules. */
  interface IDbNFCPatrolLineAndRules {
    /**
     * @db uuid primary key
     * 行ID
     */
    RID?: string | null

    /**
     * @db uuid not null REFERENCES DbNFCPatrolLine(RID) ON DELETE CASCADE
     * 归属线路
     */
    LineRID?: string | null

    /**
     * @db uuid not null REFERENCES DbNFCPatrolLineRules(RID) ON DELETE CASCADE
     * 线路规则
     */
    RuleRID?: string | null

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     */
    UpdatedAt?: string | null

    /**
     * @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 归属组，与路线的上线一致
     */
    OrgRID?: string | null
  }

  /** 巡查线路和规则的关系表，分开主要是方便使用数据库外键约束 */
  class DbNFCPatrolLineAndRules implements IDbNFCPatrolLineAndRules {
    /**
     * Constructs a new DbNFCPatrolLineAndRules.
     * @rpc pcrud
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbNFCPatrolLineAndRulesOrgRID on DbNFCPatrolLineAndRules USING hash(OrgRID);
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbNFCPatrolLineAndRulesLineRID on DbNFCPatrolLineAndRules USING hash(LineRID);
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbNFCPatrolLineAndRulesRuleRID on DbNFCPatrolLineAndRules USING hash(RuleRID);
     * @param [properties] Properties to set
     */
    constructor(properties?: bysdb.IDbNFCPatrolLineAndRules)

    /**
     * @db uuid primary key
     * 行ID
     */
    public RID: string

    /**
     * @db uuid not null REFERENCES DbNFCPatrolLine(RID) ON DELETE CASCADE
     * 归属线路
     */
    public LineRID: string

    /**
     * @db uuid not null REFERENCES DbNFCPatrolLineRules(RID) ON DELETE CASCADE
     * 线路规则
     */
    public RuleRID: string

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     */
    public UpdatedAt: string

    /**
     * @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 归属组，与路线的上线一致
     */
    public OrgRID: string

    /**
     * Encodes the specified DbNFCPatrolLineAndRules message. Does not implicitly {@link bysdb.DbNFCPatrolLineAndRules.verify|verify} messages.
     * @param message DbNFCPatrolLineAndRules message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: bysdb.IDbNFCPatrolLineAndRules,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a DbNFCPatrolLineAndRules message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns DbNFCPatrolLineAndRules
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): bysdb.DbNFCPatrolLineAndRules
  }

  /** Properties of a CamImageData. */
  interface ICamImageData {
    /** 设备IMEI */
    devId?: string | null

    /** 4G模块自动添加 */
    ccid?: string | null

    /** 固件版本 */
    firmwareVersion?: string | null

    /** 抓拍时间戳，Unix秒时间戳 */
    timestamp?: Long | null

    /** 电池电压单位mv */
    battery?: number | null

    /** 4G信号强度 */
    signal?: string | null

    /** 环境温度 */
    tempEnv?: number | null

    /** CPU温度 */
    tempCpu?: number | null

    /**
     * 工作类型
     * 0 – 手动
     * 1 – 唤醒拍照
     * 2 – 定时拍照
     * 3 – 唤醒录像
     * 4 – 定时录像
     */
    type?: number | null

    /**
     * 以下为可选字段
     * 放大系数
     */
    zoomRate?: number | null

    /** 充电电流，合方圆太阳能充电模块支持 */
    icharge?: number | null

    /** 负载电流，合方圆太阳能充电模块支持 */
    iload?: number | null

    /** 充电电压，合方圆太阳能充电模块支持 */
    vcharge?: number | null

    /**
     * 上传图片时，界桩自定义的json数据
     * "{\"hwid\": 14, \"type\": 2}"
     * hwid: 界桩硬件ID
     * type: 1、红外感应抓拍，2、报警抓拍，3、定时抓拍，4、手动抓拍
     */
    custData?: string | null
  }

  /** 合方圆摄像机上传图片时，附带的json结构体 */
  class CamImageData implements ICamImageData {
    /**
     * Constructs a new CamImageData.
     * @param [properties] Properties to set
     */
    constructor(properties?: bysdb.ICamImageData)

    /** 设备IMEI */
    public devId: string

    /** 4G模块自动添加 */
    public ccid: string

    /** 固件版本 */
    public firmwareVersion: string

    /** 抓拍时间戳，Unix秒时间戳 */
    public timestamp: Long

    /** 电池电压单位mv */
    public battery: number

    /** 4G信号强度 */
    public signal: string

    /** 环境温度 */
    public tempEnv: number

    /** CPU温度 */
    public tempCpu: number

    /**
     * 工作类型
     * 0 – 手动
     * 1 – 唤醒拍照
     * 2 – 定时拍照
     * 3 – 唤醒录像
     * 4 – 定时录像
     */
    public type: number

    /**
     * 以下为可选字段
     * 放大系数
     */
    public zoomRate: number

    /** 充电电流，合方圆太阳能充电模块支持 */
    public icharge: number

    /** 负载电流，合方圆太阳能充电模块支持 */
    public iload: number

    /** 充电电压，合方圆太阳能充电模块支持 */
    public vcharge: number

    /**
     * 上传图片时，界桩自定义的json数据
     * "{\"hwid\": 14, \"type\": 2}"
     * hwid: 界桩硬件ID
     * type: 1、红外感应抓拍，2、报警抓拍，3、定时抓拍，4、手动抓拍
     */
    public custData: string

    /**
     * Encodes the specified CamImageData message. Does not implicitly {@link bysdb.CamImageData.verify|verify} messages.
     * @param message CamImageData message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: bysdb.ICamImageData,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a CamImageData message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns CamImageData
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): bysdb.CamImageData
  }

  /** Properties of a DbMarkerUploadImageHistory. */
  interface IDbMarkerUploadImageHistory {
    /**
     * @db uuid not null
     * 所属的群组
     */
    OrgRID?: string | null

    /**
     * @db int
     * 界桩硬件ID
     */
    MarkerHWID?: number | null

    /**
     * @db timestamp not null
     * 抓拍时间,utc
     */
    CaptureTime?: string | null

    /**
     * @db int
     * 抓拍的动作类型，与CamImageData.type一致
     * 0 – 手动
     * 1 – 唤醒拍照
     * 2 – 定时拍照
     * 3 – 唤醒录像
     * 4 – 定时录像
     */
    CaptureType?: number | null

    /**
     * @db timestamp not null
     * 服务器接收上传时间,utc
     */
    UploadTime?: string | null

    /**
     * @db text
     * 上传的图片保存路径，f512673f-0f6a-4b54-95bf-d377b78fba1a.jpg
     */
    FileName?: string | null

    /**
     * @db jsonb not null default  '{}'::jsonb
     * 界桩上传图片时附带的json数据，CamImageData json字符串
     */
    FormData?: string | null
  }

  /** 4g界桩上传的图片历史表，以月为单位分表 */
  class DbMarkerUploadImageHistory implements IDbMarkerUploadImageHistory {
    /**
     * Constructs a new DbMarkerUploadImageHistory.
     * @rpc pcrud
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbMarkerUploadImageHistoryHWID on DbMarkerUploadImageHistory USING hash(MarkerHWID);
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbMarkerUploadImageHistoryOrgRID on DbMarkerUploadImageHistory USING hash(OrgRID);
     * @param [properties] Properties to set
     */
    constructor(properties?: bysdb.IDbMarkerUploadImageHistory)

    /**
     * @db uuid not null
     * 所属的群组
     */
    public OrgRID: string

    /**
     * @db int
     * 界桩硬件ID
     */
    public MarkerHWID: number

    /**
     * @db timestamp not null
     * 抓拍时间,utc
     */
    public CaptureTime: string

    /**
     * @db int
     * 抓拍的动作类型，与CamImageData.type一致
     * 0 – 手动
     * 1 – 唤醒拍照
     * 2 – 定时拍照
     * 3 – 唤醒录像
     * 4 – 定时录像
     */
    public CaptureType: number

    /**
     * @db timestamp not null
     * 服务器接收上传时间,utc
     */
    public UploadTime: string

    /**
     * @db text
     * 上传的图片保存路径，f512673f-0f6a-4b54-95bf-d377b78fba1a.jpg
     */
    public FileName: string

    /**
     * @db jsonb not null default  '{}'::jsonb
     * 界桩上传图片时附带的json数据，CamImageData json字符串
     */
    public FormData: string

    /**
     * Encodes the specified DbMarkerUploadImageHistory message. Does not implicitly {@link bysdb.DbMarkerUploadImageHistory.verify|verify} messages.
     * @param message DbMarkerUploadImageHistory message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: bysdb.IDbMarkerUploadImageHistory,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a DbMarkerUploadImageHistory message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns DbMarkerUploadImageHistory
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): bysdb.DbMarkerUploadImageHistory
  }
}

/** Namespace user. */
export namespace user {
  /** Properties of a DbPermission. */
  interface IDbPermission {
    /**
     * @db uuid primary key
     * 行ID,permission rid
     */
    RID?: string | null

    /**
     * @db text not null
     * 权限类别
     */
    PermissionType?: string | null

    /**
     * @db text not null
     * permission 名称
     */
    PermissionName?: string | null

    /**
     * @db text not null
     * permission value
     */
    PermissionValue?: string | null

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     */
    UpdatedAt?: string | null

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */
    UpdatedDC?: string | null
  }

  /** 所有的权限信息表,此表信息为预置，一般不给删除 */
  class DbPermission implements IDbPermission {
    /**
     * Constructs a new DbPermission.
     * @rpc crud
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbPermissionPermissionValue on DbPermission USING hash(PermissionValue);
     * @param [properties] Properties to set
     */
    constructor(properties?: user.IDbPermission)

    /**
     * @db uuid primary key
     * 行ID,permission rid
     */
    public RID: string

    /**
     * @db text not null
     * 权限类别
     */
    public PermissionType: string

    /**
     * @db text not null
     * permission 名称
     */
    public PermissionName: string

    /**
     * @db text not null
     * permission value
     */
    public PermissionValue: string

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     */
    public UpdatedAt: string

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */
    public UpdatedDC: string

    /**
     * Encodes the specified DbPermission message. Does not implicitly {@link user.DbPermission.verify|verify} messages.
     * @param message DbPermission message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: user.IDbPermission,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a DbPermission message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns DbPermission
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): user.DbPermission
  }

  /** Properties of a DbRole. */
  interface IDbRole {
    /**
     * @db uuid primary key
     * 行ID,role rid
     */
    RID?: string | null

    /**
     * @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 用户所属的群组
     */
    OrgRID?: string | null

    /**
     * @db text not null
     * role 名称
     */
    RoleName?: string | null

    /**
     * @db uuid --REFERENCES DbUser(RID) ON DELETE set null
     * role 创建者 rid
     */
    Creator?: string | null

    /**
     * @db int
     * 是否是内置的角色，内置角色不能删除
     */
    IsBuiltIn?: number | null

    /**
     * @db jsonb not null default  '{}'::jsonb
     * 其它设置，可以在里面扩展需要用到的其它信息
     */
    Setting?: string | null

    /**
     * @db int default 100
     * sort value
     */
    SortValue?: number | null

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     */
    UpdatedAt?: string | null

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */
    UpdatedDC?: string | null
  }

  /** 所有的角色信息表 */
  class DbRole implements IDbRole {
    /**
     * Constructs a new DbRole.
     * @rpc crud pcrud
     * @dbpost insert into DbRole (rid, orgrid, rolename, creator, isbuiltin, setting, sortvalue, updatedat, updateddc) VALUES('55555555-5555-5555-5555-555555555555', '00000000-0000-0000-0000-000000000000', 'base', null, 1, '{}'::jsonb, 0, now_utc(), '') ON CONFLICT  DO NOTHING;
     * @dbpost insert into DbRole (rid, orgrid, rolename, creator, isbuiltin, setting, sortvalue, updatedat, updateddc) VALUES('00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', 'admin', null, 1, '{}'::jsonb, 0, now_utc(), '') ON CONFLICT  DO NOTHING;
     * @param [properties] Properties to set
     */
    constructor(properties?: user.IDbRole)

    /**
     * @db uuid primary key
     * 行ID,role rid
     */
    public RID: string

    /**
     * @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 用户所属的群组
     */
    public OrgRID: string

    /**
     * @db text not null
     * role 名称
     */
    public RoleName: string

    /**
     * @db uuid --REFERENCES DbUser(RID) ON DELETE set null
     * role 创建者 rid
     */
    public Creator: string

    /**
     * @db int
     * 是否是内置的角色，内置角色不能删除
     */
    public IsBuiltIn: number

    /**
     * @db jsonb not null default  '{}'::jsonb
     * 其它设置，可以在里面扩展需要用到的其它信息
     */
    public Setting: string

    /**
     * @db int default 100
     * sort value
     */
    public SortValue: number

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     */
    public UpdatedAt: string

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */
    public UpdatedDC: string

    /**
     * Encodes the specified DbRole message. Does not implicitly {@link user.DbRole.verify|verify} messages.
     * @param message DbRole message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: user.IDbRole,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a DbRole message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns DbRole
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): user.DbRole
  }

  /** Properties of a DbRolePermission. */
  interface IDbRolePermission {
    /**
     * @db uuid primary key
     * 行ID
     */
    RID?: string | null

    /**
     * @db uuid not null REFERENCES DbRole(RID) ON DELETE CASCADE
     * role rid
     */
    RoleRID?: string | null

    /**
     * @db uuid not null REFERENCES DbPermission(RID) ON DELETE CASCADE
     * permission rid
     */
    PermissionRID?: string | null

    /**
     * @db uuid --REFERENCES DbUser(RID) ON DELETE set null
     * role 创建者 rid
     */
    Creator?: string | null

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     */
    UpdatedAt?: string | null

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */
    UpdatedDC?: string | null
  }

  /** 角色权限信息表 */
  class DbRolePermission implements IDbRolePermission {
    /**
     * Constructs a new DbRolePermission.
     * @rpc crud pcrud
     * @param [properties] Properties to set
     */
    constructor(properties?: user.IDbRolePermission)

    /**
     * @db uuid primary key
     * 行ID
     */
    public RID: string

    /**
     * @db uuid not null REFERENCES DbRole(RID) ON DELETE CASCADE
     * role rid
     */
    public RoleRID: string

    /**
     * @db uuid not null REFERENCES DbPermission(RID) ON DELETE CASCADE
     * permission rid
     */
    public PermissionRID: string

    /**
     * @db uuid --REFERENCES DbUser(RID) ON DELETE set null
     * role 创建者 rid
     */
    public Creator: string

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     */
    public UpdatedAt: string

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */
    public UpdatedDC: string

    /**
     * Encodes the specified DbRolePermission message. Does not implicitly {@link user.DbRolePermission.verify|verify} messages.
     * @param message DbRolePermission message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: user.IDbRolePermission,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a DbRolePermission message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns DbRolePermission
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): user.DbRolePermission
  }

  /** Properties of a DbPermissionList. */
  interface IDbPermissionList {
    /** 批次号，从0开始 */
    BatchNo?: number | null

    /** rows offset,从0开始 */
    RowsOffset?: number | null

    /** 本批次数据 */
    Rows?: user.IDbPermission[] | null
  }

  /** DbPermission list */
  class DbPermissionList implements IDbPermissionList {
    /**
     * Constructs a new DbPermissionList.
     * @param [properties] Properties to set
     */
    constructor(properties?: user.IDbPermissionList)

    /** 批次号，从0开始 */
    public BatchNo: number

    /** rows offset,从0开始 */
    public RowsOffset: number

    /** 本批次数据 */
    public Rows: user.IDbPermission[]

    /**
     * Encodes the specified DbPermissionList message. Does not implicitly {@link user.DbPermissionList.verify|verify} messages.
     * @param message DbPermissionList message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: user.IDbPermissionList,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a DbPermissionList message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns DbPermissionList
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): user.DbPermissionList
  }

  /** Properties of a DbRoleList. */
  interface IDbRoleList {
    /** 批次号，从0开始 */
    BatchNo?: number | null

    /** rows offset,从0开始 */
    RowsOffset?: number | null

    /** 本批次数据 */
    Rows?: user.IDbRole[] | null
  }

  /** DbRole list */
  class DbRoleList implements IDbRoleList {
    /**
     * Constructs a new DbRoleList.
     * @param [properties] Properties to set
     */
    constructor(properties?: user.IDbRoleList)

    /** 批次号，从0开始 */
    public BatchNo: number

    /** rows offset,从0开始 */
    public RowsOffset: number

    /** 本批次数据 */
    public Rows: user.IDbRole[]

    /**
     * Encodes the specified DbRoleList message. Does not implicitly {@link user.DbRoleList.verify|verify} messages.
     * @param message DbRoleList message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: user.IDbRoleList,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a DbRoleList message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns DbRoleList
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): user.DbRoleList
  }

  /** Properties of a DbRolePermissionList. */
  interface IDbRolePermissionList {
    /** 批次号，从0开始 */
    BatchNo?: number | null

    /** rows offset,从0开始 */
    RowsOffset?: number | null

    /** 本批次数据 */
    Rows?: user.IDbRolePermission[] | null
  }

  /** DbRolePermission list */
  class DbRolePermissionList implements IDbRolePermissionList {
    /**
     * Constructs a new DbRolePermissionList.
     * @param [properties] Properties to set
     */
    constructor(properties?: user.IDbRolePermissionList)

    /** 批次号，从0开始 */
    public BatchNo: number

    /** rows offset,从0开始 */
    public RowsOffset: number

    /** 本批次数据 */
    public Rows: user.IDbRolePermission[]

    /**
     * Encodes the specified DbRolePermissionList message. Does not implicitly {@link user.DbRolePermissionList.verify|verify} messages.
     * @param message DbRolePermissionList message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: user.IDbRolePermissionList,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a DbRolePermissionList message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns DbRolePermissionList
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): user.DbRolePermissionList
  }
}

/** Namespace bysproto. */
export namespace bysproto {
  /** Properties of a Bmsg. */
  interface IBmsg {
    /**
     * 命令字
     * 1:登录
     * 2:界桩打卡上传
     * 12:回应界桩上传
     * 13：修改控制器手台监听信道号
     * 15：更新控制器新的注册地址
     * 11:ping
     * ----4G界桩新增----
     * 21：信息上报
     * 31：上报应答
     * 22：报警
     * 23：拓展报警(外接传感器)
     * 32：报警应答
     * 33：报警解除
     * 28: 报警解除应答
     * 24：参数更新
     * 34：参数更新应答
     * 25：软件更新(预留)
     * 35：软件更新应答(预留)
     * 26：主动抓拍(预留)
     * 36：主动抓拍应答(预留)
     * 27：遥闭
     * 37：遥闭应答
     */
    Cmd?: number | null

    /** 从0开始增1使用,用于区分相同命令重复的包 */
    No?: number | null

    /** response code */
    Res?: number | null

    /** msg body */
    Body?: Uint8Array | null

    /** optional str parameter,额外的信息,一般不会有,有些特殊命令里面可能用到 */
    Optstr?: string | null

    /** optional binary parameter,额外的信息,一般不会有,有些特殊命令里面可能用到 */
    Optbin?: Uint8Array | null
  }

  /** 系统与控制器所有的消息交互底层都以此为包装 */
  class Bmsg implements IBmsg {
    /**
     * Constructs a new Bmsg.
     * @param [properties] Properties to set
     */
    constructor(properties?: bysproto.IBmsg)

    /**
     * 命令字
     * 1:登录
     * 2:界桩打卡上传
     * 12:回应界桩上传
     * 13：修改控制器手台监听信道号
     * 15：更新控制器新的注册地址
     * 11:ping
     * ----4G界桩新增----
     * 21：信息上报
     * 31：上报应答
     * 22：报警
     * 23：拓展报警(外接传感器)
     * 32：报警应答
     * 33：报警解除
     * 28: 报警解除应答
     * 24：参数更新
     * 34：参数更新应答
     * 25：软件更新(预留)
     * 35：软件更新应答(预留)
     * 26：主动抓拍(预留)
     * 36：主动抓拍应答(预留)
     * 27：遥闭
     * 37：遥闭应答
     */
    public Cmd: number

    /** 从0开始增1使用,用于区分相同命令重复的包 */
    public No: number

    /** response code */
    public Res: number

    /** msg body */
    public Body: Uint8Array

    /** optional str parameter,额外的信息,一般不会有,有些特殊命令里面可能用到 */
    public Optstr: string

    /** optional binary parameter,额外的信息,一般不会有,有些特殊命令里面可能用到 */
    public Optbin: Uint8Array

    /**
     * Encodes the specified Bmsg message. Does not implicitly {@link bysproto.Bmsg.verify|verify} messages.
     * @param message Bmsg message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: bysproto.IBmsg,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a Bmsg message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns Bmsg
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): bysproto.Bmsg
  }

  /** Properties of a BloginReq. */
  interface IBloginReq {
    /** 控制器名称，不是控制器硬件ID */
    Name?: string | null

    /**
     * base64(sha256(base64(sha256(controllerName+controllerPass))+LoginTimeStr))
     * 4g界桩: base64(sha256(base64(sha256(IMEI+ICCID))+LoginTimeStr))
     */
    PassHash?: string | null

    /** yyyy-mm-dd HH:MM:SS, 必须是3分钟内的时间(utc) */
    LoginTimeStr?: string | null

    /** 系统名称 */
    Sys?: string | null

    /** 电量,单位V,=0无效，7.2v = 72 */
    Power?: number | null

    /** 控制器本地手台当前信道号(对界桩通讯的手台)，无效时为0 */
    ChannelNo?: number | null

    /**
     * 本控制器数据中转的通道号和对应的信道号
     * [通道号1,通道1手台的信道号，通道号2,通道2手台的信道号，...]
     * 如[1,1,2,1]表示通道1 在1信道，通道2也在1信道
     */
    TransferChannelNos?: number[] | null

    /** 登录渠道， 0:未知 1:有线网络， 2:4g网络 3:无线FSK */
    NetworkType?: number | null

    /** 登录设备类型 0:fsk控制器 1:4g界桩 */
    DeviceType?: number | null

    /** 4g界桩IMEI */
    IMEI?: string | null

    /** 4g界桩ICCID */
    ICCID?: string | null

    /**
     * 界桩参数版本时间
     * yyyy-mm-dd HH:MM:SS
     */
    dataVersion?: string | null
  }

  /**
   * 登录信息
   * bmsg.cmd=1
   */
  class BloginReq implements IBloginReq {
    /**
     * Constructs a new BloginReq.
     * @param [properties] Properties to set
     */
    constructor(properties?: bysproto.IBloginReq)

    /** 控制器名称，不是控制器硬件ID */
    public Name: string

    /**
     * base64(sha256(base64(sha256(controllerName+controllerPass))+LoginTimeStr))
     * 4g界桩: base64(sha256(base64(sha256(IMEI+ICCID))+LoginTimeStr))
     */
    public PassHash: string

    /** yyyy-mm-dd HH:MM:SS, 必须是3分钟内的时间(utc) */
    public LoginTimeStr: string

    /** 系统名称 */
    public Sys: string

    /** 电量,单位V,=0无效，7.2v = 72 */
    public Power: number

    /** 控制器本地手台当前信道号(对界桩通讯的手台)，无效时为0 */
    public ChannelNo: number

    /**
     * 本控制器数据中转的通道号和对应的信道号
     * [通道号1,通道1手台的信道号，通道号2,通道2手台的信道号，...]
     * 如[1,1,2,1]表示通道1 在1信道，通道2也在1信道
     */
    public TransferChannelNos: number[]

    /** 登录渠道， 0:未知 1:有线网络， 2:4g网络 3:无线FSK */
    public NetworkType: number

    /** 登录设备类型 0:fsk控制器 1:4g界桩 */
    public DeviceType: number

    /** 4g界桩IMEI */
    public IMEI: string

    /** 4g界桩ICCID */
    public ICCID: string

    /**
     * 界桩参数版本时间
     * yyyy-mm-dd HH:MM:SS
     */
    public dataVersion: string

    /**
     * Encodes the specified BloginReq message. Does not implicitly {@link bysproto.BloginReq.verify|verify} messages.
     * @param message BloginReq message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: bysproto.IBloginReq,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a BloginReq message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns BloginReq
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): bysproto.BloginReq
  }

  /** Properties of a BloginRes. */
  interface IBloginRes {
    /** 100: 登录成功 -1：无此控制器信息 -2:时间参数不对 -3: 未知错误 -4:pass 不对 */
    Code?: number | null

    /** 控制器硬件ID，登录成功时返回 */
    ControllerID?: number | null

    /** 服务器时间yyyy-mm-dd HH:MM:SS.zzz(utc) */
    ServerTime?: string | null

    /** 校验界桩crc2的系统密码 */
    SystemPassword?: string | null

    /** 错误描述，未知错误时可能有 */
    Err?: string | null
  }

  /**
   * 登录回应
   * bmsy.cmd=1
   * bmsg.Res=1
   * bmsg.body=BloginRes
   */
  class BloginRes implements IBloginRes {
    /**
     * Constructs a new BloginRes.
     * @param [properties] Properties to set
     */
    constructor(properties?: bysproto.IBloginRes)

    /** 100: 登录成功 -1：无此控制器信息 -2:时间参数不对 -3: 未知错误 -4:pass 不对 */
    public Code: number

    /** 控制器硬件ID，登录成功时返回 */
    public ControllerID: number

    /** 服务器时间yyyy-mm-dd HH:MM:SS.zzz(utc) */
    public ServerTime: string

    /** 校验界桩crc2的系统密码 */
    public SystemPassword: string

    /** 错误描述，未知错误时可能有 */
    public Err: string

    /**
     * Encodes the specified BloginRes message. Does not implicitly {@link bysproto.BloginRes.verify|verify} messages.
     * @param message BloginRes message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: bysproto.IBloginRes,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a BloginRes message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns BloginRes
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): bysproto.BloginRes
  }

  /** Properties of a BGPS. */
  interface IBGPS {
    /** 东经为+，西经为-,单位为度 */
    Lon?: number | null

    /** 北纬为+，南纬为-,单位为度 */
    Lat?: number | null

    /** BGPS Height */
    Height?: number | null
  }

  /** 界桩上传的gps信息,无效gps时为全0 */
  class BGPS implements IBGPS {
    /**
     * Constructs a new BGPS.
     * @param [properties] Properties to set
     */
    constructor(properties?: bysproto.IBGPS)

    /** 东经为+，西经为-,单位为度 */
    public Lon: number

    /** 北纬为+，南纬为-,单位为度 */
    public Lat: number

    /** BGPS Height. */
    public Height: number

    /**
     * Encodes the specified BGPS message. Does not implicitly {@link bysproto.BGPS.verify|verify} messages.
     * @param message BGPS message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: bysproto.IBGPS,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a BGPS message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns BGPS
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): bysproto.BGPS
  }

  /** Properties of a BdeviceUpdate. */
  interface IBdeviceUpdate {
    /** 界桩ID */
    DeviceID?: number | null

    /**
     * 上传的命令
     * 0xd1: 常规上传
     * 0xd2: 报警上传
     */
    Cmd?: number | null

    /** 界桩状态，目前只用低位一个字节 */
    Status?: number | null

    /** 界桩上传的系统密码检验是否正确 0:正确 1：不正确 */
    SystemPassCheckOK?: number | null

    /** gps信息，没有时不需要填写 */
    GPS?: bysproto.IBGPS | null

    /** 界桩参数版本 */
    ParamVersion?: number | null

    /** 界桩参数更新时间 */
    ParamTime?: string | null

    /** 指令时间 */
    CmdTime?: string | null

    /** 接收的基站/中继控制器ID */
    StationID?: number | null

    /** 基站控制器的接收通道号，编号从1开始,不是基站上传的，此值为0 */
    StationDeviceNo?: number | null

    /** 接收设备的场强值 */
    DeviceFieldStrength?: number | null

    /** 接收设备的信道号 */
    DeviceChannelNo?: number | null
  }

  /**
   * 界桩数据上传
   * bmsg.cmd=2
   */
  class BdeviceUpdate implements IBdeviceUpdate {
    /**
     * Constructs a new BdeviceUpdate.
     * @param [properties] Properties to set
     */
    constructor(properties?: bysproto.IBdeviceUpdate)

    /** 界桩ID */
    public DeviceID: number

    /**
     * 上传的命令
     * 0xd1: 常规上传
     * 0xd2: 报警上传
     */
    public Cmd: number

    /** 界桩状态，目前只用低位一个字节 */
    public Status: number

    /** 界桩上传的系统密码检验是否正确 0:正确 1：不正确 */
    public SystemPassCheckOK: number

    /** gps信息，没有时不需要填写 */
    public GPS?: bysproto.IBGPS | null

    /** 界桩参数版本 */
    public ParamVersion: number

    /** 界桩参数更新时间 */
    public ParamTime: string

    /** 指令时间 */
    public CmdTime: string

    /** 接收的基站/中继控制器ID */
    public StationID: number

    /** 基站控制器的接收通道号，编号从1开始,不是基站上传的，此值为0 */
    public StationDeviceNo: number

    /** 接收设备的场强值 */
    public DeviceFieldStrength: number

    /** 接收设备的信道号 */
    public DeviceChannelNo: number

    /**
     * Encodes the specified BdeviceUpdate message. Does not implicitly {@link bysproto.BdeviceUpdate.verify|verify} messages.
     * @param message BdeviceUpdate message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: bysproto.IBdeviceUpdate,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a BdeviceUpdate message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns BdeviceUpdate
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): bysproto.BdeviceUpdate
  }

  /** Properties of a ControllerStatus. */
  interface IControllerStatus {
    /** 接收的基站/中继控制器ID */
    StationID?: number | null

    /** 基站控制器的接收手台号，编号从1开始,本控制器的状态时，此值为0 */
    StationDeviceNo?: number | null

    /** 电量,单位V,=0无效，7.2v = 72 */
    Power?: number | null

    /** 控制器手台当前信道号(与界桩通讯的手台) */
    ChannelNo?: number | null

    /**
     * 本控制器数据中转的通道号和对应的信道号
     * [通道号1,通道1手台的信道号，通道号2,通道2手台的信道号，...]
     * 如[1,1,2,1]表示通道1 在1信道，通道2也在1信道
     */
    TransferChannelNos?: number[] | null

    /**
     * 控制器状态
     * bit0:时间异常 0:无异常 1:无法获取时间（可能gps异常)
     */
    Status?: number | null
  }

  /** 中继/基站状态信息 */
  class ControllerStatus implements IControllerStatus {
    /**
     * Constructs a new ControllerStatus.
     * @param [properties] Properties to set
     */
    constructor(properties?: bysproto.IControllerStatus)

    /** 接收的基站/中继控制器ID */
    public StationID: number

    /** 基站控制器的接收手台号，编号从1开始,本控制器的状态时，此值为0 */
    public StationDeviceNo: number

    /** 电量,单位V,=0无效，7.2v = 72 */
    public Power: number

    /** 控制器手台当前信道号(与界桩通讯的手台) */
    public ChannelNo: number

    /**
     * 本控制器数据中转的通道号和对应的信道号
     * [通道号1,通道1手台的信道号，通道号2,通道2手台的信道号，...]
     * 如[1,1,2,1]表示通道1 在1信道，通道2也在1信道
     */
    public TransferChannelNos: number[]

    /**
     * 控制器状态
     * bit0:时间异常 0:无异常 1:无法获取时间（可能gps异常)
     */
    public Status: number

    /**
     * Encodes the specified ControllerStatus message. Does not implicitly {@link bysproto.ControllerStatus.verify|verify} messages.
     * @param message ControllerStatus message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: bysproto.IControllerStatus,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a ControllerStatus message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns ControllerStatus
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): bysproto.ControllerStatus
  }

  /** Properties of a BdeviceUpdateResponse. */
  interface IBdeviceUpdateResponse {
    /** 界桩ID */
    DeviceID?: number | null

    /** 接收的基站/中继控制器ID */
    StationID?: number | null

    /** 基站控制器的通道号，编号从1开始,不是基站上传的，此值为0 */
    StationDeviceNo?: number | null

    /** 回应命令数据 */
    Cmd0xD0?: Uint8Array | null
  }

  /**
   * 回应界桩上传（如果确实有参数需要修改的话）
   * bmsg.cmd=12
   */
  class BdeviceUpdateResponse implements IBdeviceUpdateResponse {
    /**
     * Constructs a new BdeviceUpdateResponse.
     * @param [properties] Properties to set
     */
    constructor(properties?: bysproto.IBdeviceUpdateResponse)

    /** 界桩ID */
    public DeviceID: number

    /** 接收的基站/中继控制器ID */
    public StationID: number

    /** 基站控制器的通道号，编号从1开始,不是基站上传的，此值为0 */
    public StationDeviceNo: number

    /** 回应命令数据 */
    public Cmd0xD0: Uint8Array

    /**
     * Encodes the specified BdeviceUpdateResponse message. Does not implicitly {@link bysproto.BdeviceUpdateResponse.verify|verify} messages.
     * @param message BdeviceUpdateResponse message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: bysproto.IBdeviceUpdateResponse,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a BdeviceUpdateResponse message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns BdeviceUpdateResponse
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): bysproto.BdeviceUpdateResponse
  }

  /** Properties of a BControllerUpdateChannel. */
  interface IBControllerUpdateChannel {
    /** 控制器ID */
    StationID?: number | null

    /** 控制器通道号 */
    StationDeviceNo?: number | null

    /** 新默认监听信道号 */
    NewChannelNo?: number | null
  }

  /**
   * 修改控制器手台默认信道号（针对与界桩通讯的手台）
   * bmsg.cmd=13
   */
  class BControllerUpdateChannel implements IBControllerUpdateChannel {
    /**
     * Constructs a new BControllerUpdateChannel.
     * @param [properties] Properties to set
     */
    constructor(properties?: bysproto.IBControllerUpdateChannel)

    /** 控制器ID */
    public StationID: number

    /** 控制器通道号 */
    public StationDeviceNo: number

    /** 新默认监听信道号 */
    public NewChannelNo: number

    /**
     * Encodes the specified BControllerUpdateChannel message. Does not implicitly {@link bysproto.BControllerUpdateChannel.verify|verify} messages.
     * @param message BControllerUpdateChannel message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: bysproto.IBControllerUpdateChannel,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a BControllerUpdateChannel message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns BControllerUpdateChannel
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): bysproto.BControllerUpdateChannel
  }

  /** Properties of a BControllerNewServerAddr. */
  interface IBControllerNewServerAddr {
    /** 基站控制器ID */
    StationID?: number | null

    /** 在上级控制器的通道号，中继时需要填写，基站本地通道时为0 */
    ControllerChannelNo?: number | null

    /** 新服务器IP地址，支持域名和IP */
    Ip?: string | null

    /** 新服务器端口 */
    Port?: number | null
  }

  /**
   * bmsg.cmd=15, 更新控制器新的注册地址
   * bmsg.body=BControllerNewServerAddr
   * bmsg.Res=1,控制器应答
   */
  class BControllerNewServerAddr implements IBControllerNewServerAddr {
    /**
     * Constructs a new BControllerNewServerAddr.
     * @param [properties] Properties to set
     */
    constructor(properties?: bysproto.IBControllerNewServerAddr)

    /** 基站控制器ID */
    public StationID: number

    /** 在上级控制器的通道号，中继时需要填写，基站本地通道时为0 */
    public ControllerChannelNo: number

    /** 新服务器IP地址，支持域名和IP */
    public Ip: string

    /** 新服务器端口 */
    public Port: number

    /**
     * Encodes the specified BControllerNewServerAddr message. Does not implicitly {@link bysproto.BControllerNewServerAddr.verify|verify} messages.
     * @param message BControllerNewServerAddr message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: bysproto.IBControllerNewServerAddr,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a BControllerNewServerAddr message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns BControllerNewServerAddr
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): bysproto.BControllerNewServerAddr
  }

  /** Properties of a BInfoReporting. */
  interface IBInfoReporting {
    /** 本机设备编号 */
    deviceID?: number | null

    /** 1：开机上报 2：调试上报 3：定时上报 */
    type?: number | null

    /** sim卡iccid 20位 */
    iccid?: string | null

    /** 格式：Vx.x.x_yyMMdd V1.0.1_231031 */
    softwareVersion?: string | null

    /** yyyy-mm-dd HH:MM:SS */
    dataVersion?: string | null

    /** 设备时间yyyy-mm-dd HH:MM:SS */
    time?: string | null

    /** 设备电池电压单位mv */
    battery?: number | null

    /** 设备4G模块场强信号幅度 单位dbm */
    signal?: number | null

    /**
     * Bit0：报警锁定(0:正常，1:报警锁定)
     * Bit1：RTC时钟状态(0:正常，1:故障)
     * Bit2：GPS模块状态(0:正常，1:故障)
     * Bit3：三轴传感器状态(0:正常，1:故障)
     * Bit4：电池状态(0:正常，1:故障)
     * Bit5：摄像头状态(0:正常，1:故障)(预留)
     * Bit6：红外探头状态(0:正常，1:故障)(预留)
     * Bit7-Bit31：预留
     */
    state?: number | null

    /**
     * Bit0：位移报警(0:正常，1:报警)
     * Bit1：震动报警(0:正常，1:报警)
     * Bit2：倾斜报警(0:正常，1:报警)
     * Bit3：红外报警(0:正常，1:报警)(预留)
     * Bit4-Bit31：预留
     */
    alarmstate?: number | null

    /** 设备温度 */
    temp?: number | null

    /** 湿度 单位：%RH */
    rh?: number | null
  }

  /**
   * 信息上报
   * bmsg.cmd=21
   */
  class BInfoReporting implements IBInfoReporting {
    /**
     * Constructs a new BInfoReporting.
     * @param [properties] Properties to set
     */
    constructor(properties?: bysproto.IBInfoReporting)

    /** 本机设备编号 */
    public deviceID: number

    /** 1：开机上报 2：调试上报 3：定时上报 */
    public type: number

    /** sim卡iccid 20位 */
    public iccid: string

    /** 格式：Vx.x.x_yyMMdd V1.0.1_231031 */
    public softwareVersion: string

    /** yyyy-mm-dd HH:MM:SS */
    public dataVersion: string

    /** 设备时间yyyy-mm-dd HH:MM:SS */
    public time: string

    /** 设备电池电压单位mv */
    public battery: number

    /** 设备4G模块场强信号幅度 单位dbm */
    public signal: number

    /**
     * Bit0：报警锁定(0:正常，1:报警锁定)
     * Bit1：RTC时钟状态(0:正常，1:故障)
     * Bit2：GPS模块状态(0:正常，1:故障)
     * Bit3：三轴传感器状态(0:正常，1:故障)
     * Bit4：电池状态(0:正常，1:故障)
     * Bit5：摄像头状态(0:正常，1:故障)(预留)
     * Bit6：红外探头状态(0:正常，1:故障)(预留)
     * Bit7-Bit31：预留
     */
    public state: number

    /**
     * Bit0：位移报警(0:正常，1:报警)
     * Bit1：震动报警(0:正常，1:报警)
     * Bit2：倾斜报警(0:正常，1:报警)
     * Bit3：红外报警(0:正常，1:报警)(预留)
     * Bit4-Bit31：预留
     */
    public alarmstate: number

    /** 设备温度 */
    public temp: number

    /** 湿度 单位：%RH */
    public rh: number

    /**
     * Encodes the specified BInfoReporting message. Does not implicitly {@link bysproto.BInfoReporting.verify|verify} messages.
     * @param message BInfoReporting message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: bysproto.IBInfoReporting,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a BInfoReporting message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns BInfoReporting
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): bysproto.BInfoReporting
  }

  /** Properties of a BVamp. */
  interface IBVamp {
    /** 震动幅度 */
    amplitude?: number | null

    /** 震动持续时间 s */
    duration?: number | null
  }

  /** 震动报警参数 */
  class BVamp implements IBVamp {
    /**
     * Constructs a new BVamp.
     * @param [properties] Properties to set
     */
    constructor(properties?: bysproto.IBVamp)

    /** 震动幅度 */
    public amplitude: number

    /** 震动持续时间 s */
    public duration: number

    /**
     * Encodes the specified BVamp message. Does not implicitly {@link bysproto.BVamp.verify|verify} messages.
     * @param message BVamp message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: bysproto.IBVamp,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a BVamp message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns BVamp
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): bysproto.BVamp
  }

  /** Properties of a BAlarmReporting. */
  interface IBAlarmReporting {
    /** 本机设备编号 */
    deviceID?: number | null

    /** 1：开机上报 2：调试上报 3：报警上报 */
    type?: number | null

    /**
     * Bit0：位移报警(0:正常，1:报警)
     * Bit1：震动报警(0:正常，1:报警)
     * Bit2：倾斜报警(0:正常，1:报警)
     * Bit3：红外报警(0:正常，1:报警)(预留)
     * Bit4-Bit31：预留
     */
    alarmstate?: number | null

    /** 定位坐标 */
    locate?: bysproto.IBGPS | null

    /** 倾斜角度，单位：度 */
    attitude?: number | null

    /** 震动报警 */
    vibration?: bysproto.IBVamp | null

    /** 红外预留 */
    infrared?: number | null

    /** 摄像头预留 */
    camera?: number | null

    /** 其他（预留） */
    reserve?: Uint8Array | null
  }

  /**
   * 报警上报
   * bmsg.cmd=22
   */
  class BAlarmReporting implements IBAlarmReporting {
    /**
     * Constructs a new BAlarmReporting.
     * @param [properties] Properties to set
     */
    constructor(properties?: bysproto.IBAlarmReporting)

    /** 本机设备编号 */
    public deviceID: number

    /** 1：开机上报 2：调试上报 3：报警上报 */
    public type: number

    /**
     * Bit0：位移报警(0:正常，1:报警)
     * Bit1：震动报警(0:正常，1:报警)
     * Bit2：倾斜报警(0:正常，1:报警)
     * Bit3：红外报警(0:正常，1:报警)(预留)
     * Bit4-Bit31：预留
     */
    public alarmstate: number

    /** 定位坐标 */
    public locate?: bysproto.IBGPS | null

    /** 倾斜角度，单位：度 */
    public attitude: number

    /** 震动报警 */
    public vibration?: bysproto.IBVamp | null

    /** 红外预留 */
    public infrared: number

    /** 摄像头预留 */
    public camera: number

    /** 其他（预留） */
    public reserve: Uint8Array

    /**
     * Encodes the specified BAlarmReporting message. Does not implicitly {@link bysproto.BAlarmReporting.verify|verify} messages.
     * @param message BAlarmReporting message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: bysproto.IBAlarmReporting,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a BAlarmReporting message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns BAlarmReporting
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): bysproto.BAlarmReporting
  }

  /** Properties of a BAlarmClear. */
  interface IBAlarmClear {
    /** 目标设备编号 */
    deviceID?: number | null

    /** 1：报警解除 2：报警锁定 */
    response?: number | null
  }

  /**
   * 报警解除
   * bmsg.cmd=33
   */
  class BAlarmClear implements IBAlarmClear {
    /**
     * Constructs a new BAlarmClear.
     * @param [properties] Properties to set
     */
    constructor(properties?: bysproto.IBAlarmClear)

    /** 目标设备编号 */
    public deviceID: number

    /** 1：报警解除 2：报警锁定 */
    public response: number

    /**
     * Encodes the specified BAlarmClear message. Does not implicitly {@link bysproto.BAlarmClear.verify|verify} messages.
     * @param message BAlarmClear message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: bysproto.IBAlarmClear,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a BAlarmClear message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns BAlarmClear
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): bysproto.BAlarmClear
  }

  /** Properties of a BDataUpdate. */
  interface IBDataUpdate {
    /** 目标设备编号 */
    deviceID?: number | null

    /** Bit0：解除报警锁定 Bit1：更新参数 Bit2：校准时间 */
    type?: number | null

    /** 版本yyyy-mm-dd HH:MM:SS */
    dataVersion?: string | null

    /** 服务器地址  xxx.xxx.xxx.xxx:xxxx   xxxxxx.com:xxxx */
    addr?: string | null

    /** 定时上报基准时间HH:MM:SS */
    timerbase?: string | null

    /** timer 6h、8h、12h、24h */
    timer?: number | null

    /** 姿态报警阈值 */
    attitude?: number | null

    /** 位移报警阈值 */
    dirft?: number | null

    /** 震动报警阈值 */
    vibration?: bysproto.IBVamp | null

    /** 红外报警阈值 */
    infrared?: number | null

    /** 延迟休眠时间 10s */
    t1?: number | null

    /** 调试模式时间 120s */
    t2?: number | null

    /** 报警间隔 10s */
    t3?: number | null

    /** 报警次数 10 */
    n1?: number | null
  }

  /**
   * 参数更新
   * bmsg.cmd=24
   */
  class BDataUpdate implements IBDataUpdate {
    /**
     * Constructs a new BDataUpdate.
     * @param [properties] Properties to set
     */
    constructor(properties?: bysproto.IBDataUpdate)

    /** 目标设备编号 */
    public deviceID: number

    /** Bit0：解除报警锁定 Bit1：更新参数 Bit2：校准时间 */
    public type: number

    /** 版本yyyy-mm-dd HH:MM:SS */
    public dataVersion: string

    /** 服务器地址  xxx.xxx.xxx.xxx:xxxx   xxxxxx.com:xxxx */
    public addr: string

    /** 定时上报基准时间HH:MM:SS */
    public timerbase: string

    /** timer 6h、8h、12h、24h */
    public timer: number

    /** 姿态报警阈值 */
    public attitude: number

    /** 位移报警阈值 */
    public dirft: number

    /** 震动报警阈值 */
    public vibration?: bysproto.IBVamp | null

    /** 红外报警阈值 */
    public infrared: number

    /** 延迟休眠时间 10s */
    public t1: number

    /** 调试模式时间 120s */
    public t2: number

    /** 报警间隔 10s */
    public t3: number

    /** 报警次数 10 */
    public n1: number

    /**
     * Encodes the specified BDataUpdate message. Does not implicitly {@link bysproto.BDataUpdate.verify|verify} messages.
     * @param message BDataUpdate message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: bysproto.IBDataUpdate,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a BDataUpdate message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns BDataUpdate
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): bysproto.BDataUpdate
  }

  /** Properties of a BShutDown. */
  interface IBShutDown {
    /** 目标设备编号 */
    deviceID?: number | null

    /** 0:遥活 1:遥闭  2:遥晕 */
    type?: number | null

    /** 0:遥活 1:遥闭  2:遥晕 */
    camType?: number | null
  }

  /**
   * 遥闭
   * bmsg.cmd=27
   */
  class BShutDown implements IBShutDown {
    /**
     * Constructs a new BShutDown.
     * @param [properties] Properties to set
     */
    constructor(properties?: bysproto.IBShutDown)

    /** 目标设备编号 */
    public deviceID: number

    /** 0:遥活 1:遥闭  2:遥晕 */
    public type: number

    /** 0:遥活 1:遥闭  2:遥晕 */
    public camType: number

    /**
     * Encodes the specified BShutDown message. Does not implicitly {@link bysproto.BShutDown.verify|verify} messages.
     * @param message BShutDown message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: bysproto.IBShutDown,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a BShutDown message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns BShutDown
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): bysproto.BShutDown
  }
}

/*eslint-disable block-scoped-var, id-length, no-control-regex, no-magic-numbers, no-prototype-builtins, no-redeclare, no-shadow, no-var, sort-vars*/
import * as $protobuf from 'protobufjs/minimal'

// Common aliases
const $Reader = $protobuf.Reader,
  $Writer = $protobuf.Writer,
  $util = $protobuf.util

// Exported root namespace
const $root = $protobuf.roots['default'] || ($protobuf.roots['default'] = {})

export const user = ($root.user = (() => {
  /**
   * Namespace user.
   * @exports user
   * @namespace
   */
  const user = $root.user || {}

  user.DbUserList = (function () {
    /**
     * Properties of a DbUserList.
     * @memberof user
     * @interface IDbUserList
     * @property {number|null} [BatchNo] 批次号，从0开始
     * @property {number|null} [RowsOffset] rows offset,从0开始
     * @property {Array.<user.IDbUser>|null} [Rows] 本批次数据
     */

    /**
     * Constructs a new DbUserList.
     * @memberof user
     * @classdesc DbUser list
     * @implements IDbUserList
     * @constructor
     * @param {user.IDbUserList=} [properties] Properties to set
     */
    function DbUserList(properties) {
      this.Rows = []
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 批次号，从0开始
     * @member {number} BatchNo
     * @memberof user.DbUserList
     * @instance
     */
    DbUserList.prototype.BatchNo = 0

    /**
     * rows offset,从0开始
     * @member {number} RowsOffset
     * @memberof user.DbUserList
     * @instance
     */
    DbUserList.prototype.RowsOffset = 0

    /**
     * 本批次数据
     * @member {Array.<user.IDbUser>} Rows
     * @memberof user.DbUserList
     * @instance
     */
    DbUserList.prototype.Rows = $util.emptyArray

    /**
     * Encodes the specified DbUserList message. Does not implicitly {@link user.DbUserList.verify|verify} messages.
     * @function encode
     * @memberof user.DbUserList
     * @static
     * @param {user.IDbUserList} message DbUserList message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    DbUserList.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (
        message.BatchNo != null &&
        Object.hasOwnProperty.call(message, 'BatchNo')
      )
        writer.uint32(/* id 1, wireType 0 =*/ 8).uint32(message.BatchNo)
      if (
        message.RowsOffset != null &&
        Object.hasOwnProperty.call(message, 'RowsOffset')
      )
        writer.uint32(/* id 2, wireType 0 =*/ 16).uint32(message.RowsOffset)
      if (message.Rows != null && message.Rows.length)
        for (let i = 0; i < message.Rows.length; ++i)
          $root.user.DbUser.encode(
            message.Rows[i],
            writer.uint32(/* id 3, wireType 2 =*/ 26).fork(),
          ).ldelim()
      return writer
    }

    /**
     * Decodes a DbUserList message from the specified reader or buffer.
     * @function decode
     * @memberof user.DbUserList
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {user.DbUserList} DbUserList
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    DbUserList.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.user.DbUserList()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.BatchNo = reader.uint32()
            break
          case 2:
            message.RowsOffset = reader.uint32()
            break
          case 3:
            if (!(message.Rows && message.Rows.length)) message.Rows = []
            message.Rows.push($root.user.DbUser.decode(reader, reader.uint32()))
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return DbUserList
  })()

  user.DbUserRoleList = (function () {
    /**
     * Properties of a DbUserRoleList.
     * @memberof user
     * @interface IDbUserRoleList
     * @property {number|null} [BatchNo] 批次号，从0开始
     * @property {number|null} [RowsOffset] rows offset,从0开始
     * @property {Array.<user.IDbUserRole>|null} [Rows] 本批次数据
     */

    /**
     * Constructs a new DbUserRoleList.
     * @memberof user
     * @classdesc DbUserRole list
     * @implements IDbUserRoleList
     * @constructor
     * @param {user.IDbUserRoleList=} [properties] Properties to set
     */
    function DbUserRoleList(properties) {
      this.Rows = []
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 批次号，从0开始
     * @member {number} BatchNo
     * @memberof user.DbUserRoleList
     * @instance
     */
    DbUserRoleList.prototype.BatchNo = 0

    /**
     * rows offset,从0开始
     * @member {number} RowsOffset
     * @memberof user.DbUserRoleList
     * @instance
     */
    DbUserRoleList.prototype.RowsOffset = 0

    /**
     * 本批次数据
     * @member {Array.<user.IDbUserRole>} Rows
     * @memberof user.DbUserRoleList
     * @instance
     */
    DbUserRoleList.prototype.Rows = $util.emptyArray

    /**
     * Encodes the specified DbUserRoleList message. Does not implicitly {@link user.DbUserRoleList.verify|verify} messages.
     * @function encode
     * @memberof user.DbUserRoleList
     * @static
     * @param {user.IDbUserRoleList} message DbUserRoleList message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    DbUserRoleList.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (
        message.BatchNo != null &&
        Object.hasOwnProperty.call(message, 'BatchNo')
      )
        writer.uint32(/* id 1, wireType 0 =*/ 8).uint32(message.BatchNo)
      if (
        message.RowsOffset != null &&
        Object.hasOwnProperty.call(message, 'RowsOffset')
      )
        writer.uint32(/* id 2, wireType 0 =*/ 16).uint32(message.RowsOffset)
      if (message.Rows != null && message.Rows.length)
        for (let i = 0; i < message.Rows.length; ++i)
          $root.user.DbUserRole.encode(
            message.Rows[i],
            writer.uint32(/* id 3, wireType 2 =*/ 26).fork(),
          ).ldelim()
      return writer
    }

    /**
     * Decodes a DbUserRoleList message from the specified reader or buffer.
     * @function decode
     * @memberof user.DbUserRoleList
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {user.DbUserRoleList} DbUserRoleList
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    DbUserRoleList.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.user.DbUserRoleList()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.BatchNo = reader.uint32()
            break
          case 2:
            message.RowsOffset = reader.uint32()
            break
          case 3:
            if (!(message.Rows && message.Rows.length)) message.Rows = []
            message.Rows.push(
              $root.user.DbUserRole.decode(reader, reader.uint32()),
            )
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return DbUserRoleList
  })()

  user.DbUser = (function () {
    /**
     * Properties of a DbUser.
     * @memberof user
     * @interface IDbUser
     * @property {string|null} [RID] @db uuid primary key
     * 行ID
     * @property {string|null} [OrgRID] @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 用户所属的群组
     * @property {string|null} [UserID] @db varchar(16) not null unique
     * 用户编号
     * @property {number|null} [UserType] @db int
     * 用户类型
     * @property {string|null} [UserName] @db varchar(32) not null
     * 用户名
     * @property {string|null} [Note] @db text
     * 备注
     * @property {string|null} [Phone] @db text
     * 用户电话
     * @property {string|null} [Image] @db uuid REFERENCES DbImage(RID) ON DELETE SET NULL
     * 用户图片
     * @property {string|null} [Setting] @db jsonb not null default  '{}'::jsonb
     * 其它设置，可以在里面扩展需要用到的其它信息
     * @property {string|null} [LoginName] @db varchar(32) unique
     * 用户登录名,NULL(空)表示不能登录系统
     * @property {string|null} [LoginPass] @db text default ''
     * 用户登录密码,为base64(sha256(user_name+user_password)),系统不保存真实的密码
     * @property {string|null} [UpdatedAt] @db timestamp not null default now_utc()
     * 数据最后修改时间
     * @property {string|null} [UpdatedDC] @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */

    /**
     * Constructs a new DbUser.
     * @memberof user
     * @classdesc 用户表
     * @rpc crud pcrud
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbUserOrgRID on DbUser USING hash(OrgRID);
     * @dbpost INSERT INTO dbuser(rid, orgrid, userid, usertype, username, phone, image, setting, loginname, loginpass, updatedat, updateddc)VALUES('00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', 'ttt', 0, 'ttt', '', NULL, '{}'::jsonb, 'ttt', 'UwhpDj0xMGNzZw8sXYZOLGBHmH/q9Wxf7aB6/EbT43Q=', now_utc(), '') ON CONFLICT  DO NOTHING;
     * @implements IDbUser
     * @constructor
     * @param {user.IDbUser=} [properties] Properties to set
     */
    function DbUser(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * @db uuid primary key
     * 行ID
     * @member {string} RID
     * @memberof user.DbUser
     * @instance
     */
    DbUser.prototype.RID = ''

    /**
     * @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 用户所属的群组
     * @member {string} OrgRID
     * @memberof user.DbUser
     * @instance
     */
    DbUser.prototype.OrgRID = ''

    /**
     * @db varchar(16) not null unique
     * 用户编号
     * @member {string} UserID
     * @memberof user.DbUser
     * @instance
     */
    DbUser.prototype.UserID = ''

    /**
     * @db int
     * 用户类型
     * @member {number} UserType
     * @memberof user.DbUser
     * @instance
     */
    DbUser.prototype.UserType = 0

    /**
     * @db varchar(32) not null
     * 用户名
     * @member {string} UserName
     * @memberof user.DbUser
     * @instance
     */
    DbUser.prototype.UserName = ''

    /**
     * @db text
     * 备注
     * @member {string} Note
     * @memberof user.DbUser
     * @instance
     */
    DbUser.prototype.Note = ''

    /**
     * @db text
     * 用户电话
     * @member {string} Phone
     * @memberof user.DbUser
     * @instance
     */
    DbUser.prototype.Phone = ''

    /**
     * @db uuid REFERENCES DbImage(RID) ON DELETE SET NULL
     * 用户图片
     * @member {string} Image
     * @memberof user.DbUser
     * @instance
     */
    DbUser.prototype.Image = ''

    /**
     * @db jsonb not null default  '{}'::jsonb
     * 其它设置，可以在里面扩展需要用到的其它信息
     * @member {string} Setting
     * @memberof user.DbUser
     * @instance
     */
    DbUser.prototype.Setting = ''

    /**
     * @db varchar(32) unique
     * 用户登录名,NULL(空)表示不能登录系统
     * @member {string} LoginName
     * @memberof user.DbUser
     * @instance
     */
    DbUser.prototype.LoginName = ''

    /**
     * @db text default ''
     * 用户登录密码,为base64(sha256(user_name+user_password)),系统不保存真实的密码
     * @member {string} LoginPass
     * @memberof user.DbUser
     * @instance
     */
    DbUser.prototype.LoginPass = ''

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     * @member {string} UpdatedAt
     * @memberof user.DbUser
     * @instance
     */
    DbUser.prototype.UpdatedAt = ''

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     * @member {string} UpdatedDC
     * @memberof user.DbUser
     * @instance
     */
    DbUser.prototype.UpdatedDC = ''

    /**
     * Encodes the specified DbUser message. Does not implicitly {@link user.DbUser.verify|verify} messages.
     * @function encode
     * @memberof user.DbUser
     * @static
     * @param {user.IDbUser} message DbUser message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    DbUser.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (message.RID != null && Object.hasOwnProperty.call(message, 'RID'))
        writer.uint32(/* id 1, wireType 2 =*/ 10).string(message.RID)
      if (
        message.OrgRID != null &&
        Object.hasOwnProperty.call(message, 'OrgRID')
      )
        writer.uint32(/* id 2, wireType 2 =*/ 18).string(message.OrgRID)
      if (
        message.UserID != null &&
        Object.hasOwnProperty.call(message, 'UserID')
      )
        writer.uint32(/* id 3, wireType 2 =*/ 26).string(message.UserID)
      if (
        message.UserType != null &&
        Object.hasOwnProperty.call(message, 'UserType')
      )
        writer.uint32(/* id 4, wireType 0 =*/ 32).int32(message.UserType)
      if (
        message.UserName != null &&
        Object.hasOwnProperty.call(message, 'UserName')
      )
        writer.uint32(/* id 5, wireType 2 =*/ 42).string(message.UserName)
      if (message.Note != null && Object.hasOwnProperty.call(message, 'Note'))
        writer.uint32(/* id 6, wireType 2 =*/ 50).string(message.Note)
      if (message.Phone != null && Object.hasOwnProperty.call(message, 'Phone'))
        writer.uint32(/* id 7, wireType 2 =*/ 58).string(message.Phone)
      if (message.Image != null && Object.hasOwnProperty.call(message, 'Image'))
        writer.uint32(/* id 8, wireType 2 =*/ 66).string(message.Image)
      if (
        message.Setting != null &&
        Object.hasOwnProperty.call(message, 'Setting')
      )
        writer.uint32(/* id 9, wireType 2 =*/ 74).string(message.Setting)
      if (
        message.LoginName != null &&
        Object.hasOwnProperty.call(message, 'LoginName')
      )
        writer.uint32(/* id 10, wireType 2 =*/ 82).string(message.LoginName)
      if (
        message.LoginPass != null &&
        Object.hasOwnProperty.call(message, 'LoginPass')
      )
        writer.uint32(/* id 11, wireType 2 =*/ 90).string(message.LoginPass)
      if (
        message.UpdatedAt != null &&
        Object.hasOwnProperty.call(message, 'UpdatedAt')
      )
        writer.uint32(/* id 14, wireType 2 =*/ 114).string(message.UpdatedAt)
      if (
        message.UpdatedDC != null &&
        Object.hasOwnProperty.call(message, 'UpdatedDC')
      )
        writer.uint32(/* id 15, wireType 2 =*/ 122).string(message.UpdatedDC)
      return writer
    }

    /**
     * Decodes a DbUser message from the specified reader or buffer.
     * @function decode
     * @memberof user.DbUser
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {user.DbUser} DbUser
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    DbUser.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.user.DbUser()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.RID = reader.string()
            break
          case 2:
            message.OrgRID = reader.string()
            break
          case 3:
            message.UserID = reader.string()
            break
          case 4:
            message.UserType = reader.int32()
            break
          case 5:
            message.UserName = reader.string()
            break
          case 6:
            message.Note = reader.string()
            break
          case 7:
            message.Phone = reader.string()
            break
          case 8:
            message.Image = reader.string()
            break
          case 9:
            message.Setting = reader.string()
            break
          case 10:
            message.LoginName = reader.string()
            break
          case 11:
            message.LoginPass = reader.string()
            break
          case 14:
            message.UpdatedAt = reader.string()
            break
          case 15:
            message.UpdatedDC = reader.string()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return DbUser
  })()

  user.DbUserRole = (function () {
    /**
     * Properties of a DbUserRole.
     * @memberof user
     * @interface IDbUserRole
     * @property {string|null} [RID] @db uuid primary key
     * 行ID
     * @property {string|null} [OrgRID] @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 用户所属的群组
     * @property {string|null} [UserRID] @db uuid not null REFERENCES DbUser(RID) ON DELETE CASCADE
     * 用户rid
     * @property {string|null} [RoleRID] @db uuid not null REFERENCES DbRole(RID) ON DELETE CASCADE
     * role rid
     * @property {string|null} [UpdatedAt] @db timestamp not null default now_utc()
     * 数据最后修改时间
     * @property {string|null} [UpdatedDC] @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */

    /**
     * Constructs a new DbUserRole.
     * @memberof user
     * @classdesc 用户角色数据表
     * 一个用户可以有多个角色
     * @rpc crud pcrud
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbUserRoleUserRID on DbUserRole USING hash(UserRID);
     * @implements IDbUserRole
     * @constructor
     * @param {user.IDbUserRole=} [properties] Properties to set
     */
    function DbUserRole(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * @db uuid primary key
     * 行ID
     * @member {string} RID
     * @memberof user.DbUserRole
     * @instance
     */
    DbUserRole.prototype.RID = ''

    /**
     * @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 用户所属的群组
     * @member {string} OrgRID
     * @memberof user.DbUserRole
     * @instance
     */
    DbUserRole.prototype.OrgRID = ''

    /**
     * @db uuid not null REFERENCES DbUser(RID) ON DELETE CASCADE
     * 用户rid
     * @member {string} UserRID
     * @memberof user.DbUserRole
     * @instance
     */
    DbUserRole.prototype.UserRID = ''

    /**
     * @db uuid not null REFERENCES DbRole(RID) ON DELETE CASCADE
     * role rid
     * @member {string} RoleRID
     * @memberof user.DbUserRole
     * @instance
     */
    DbUserRole.prototype.RoleRID = ''

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     * @member {string} UpdatedAt
     * @memberof user.DbUserRole
     * @instance
     */
    DbUserRole.prototype.UpdatedAt = ''

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     * @member {string} UpdatedDC
     * @memberof user.DbUserRole
     * @instance
     */
    DbUserRole.prototype.UpdatedDC = ''

    /**
     * Encodes the specified DbUserRole message. Does not implicitly {@link user.DbUserRole.verify|verify} messages.
     * @function encode
     * @memberof user.DbUserRole
     * @static
     * @param {user.IDbUserRole} message DbUserRole message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    DbUserRole.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (message.RID != null && Object.hasOwnProperty.call(message, 'RID'))
        writer.uint32(/* id 1, wireType 2 =*/ 10).string(message.RID)
      if (
        message.OrgRID != null &&
        Object.hasOwnProperty.call(message, 'OrgRID')
      )
        writer.uint32(/* id 2, wireType 2 =*/ 18).string(message.OrgRID)
      if (
        message.UserRID != null &&
        Object.hasOwnProperty.call(message, 'UserRID')
      )
        writer.uint32(/* id 3, wireType 2 =*/ 26).string(message.UserRID)
      if (
        message.RoleRID != null &&
        Object.hasOwnProperty.call(message, 'RoleRID')
      )
        writer.uint32(/* id 4, wireType 2 =*/ 34).string(message.RoleRID)
      if (
        message.UpdatedAt != null &&
        Object.hasOwnProperty.call(message, 'UpdatedAt')
      )
        writer.uint32(/* id 14, wireType 2 =*/ 114).string(message.UpdatedAt)
      if (
        message.UpdatedDC != null &&
        Object.hasOwnProperty.call(message, 'UpdatedDC')
      )
        writer.uint32(/* id 15, wireType 2 =*/ 122).string(message.UpdatedDC)
      return writer
    }

    /**
     * Decodes a DbUserRole message from the specified reader or buffer.
     * @function decode
     * @memberof user.DbUserRole
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {user.DbUserRole} DbUserRole
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    DbUserRole.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.user.DbUserRole()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.RID = reader.string()
            break
          case 2:
            message.OrgRID = reader.string()
            break
          case 3:
            message.UserRID = reader.string()
            break
          case 4:
            message.RoleRID = reader.string()
            break
          case 14:
            message.UpdatedAt = reader.string()
            break
          case 15:
            message.UpdatedDC = reader.string()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return DbUserRole
  })()

  return user
})())

export { $root as default }

/*eslint-disable block-scoped-var, id-length, no-control-regex, no-magic-numbers, no-prototype-builtins, no-redeclare, no-shadow, no-var, sort-vars*/
import * as $protobuf from 'protobufjs/minimal'

// Common aliases
const $Reader = $protobuf.Reader,
  $Writer = $protobuf.Writer,
  $util = $protobuf.util

// Exported root namespace
const $root = $protobuf.roots['default'] || ($protobuf.roots['default'] = {})

export const yrpcmsg = ($root.yrpcmsg = (() => {
  /**
   * Namespace yrpcmsg.
   * @exports yrpcmsg
   * @namespace
   */
  const yrpcmsg = $root.yrpcmsg || {}

  yrpcmsg.Ymsg = (function () {
    /**
     * Properties of a Ymsg.
     * @memberof yrpcmsg
     * @interface IYmsg
     * @property {number|null} [Len] 整个rpc msg的长度，不包含此字段
     * 虽然这个长度可以很长，但是为了避免大包阻塞其它操作，通常都要限制长度,采用分包多发机制
     * 当使用基于包的传输通道时(udp/kcp/websocket)，此值可能为0(此时长度为收到的整个包的长度)
     * @property {number|null} [Cmd] rpc command,rpc的命令和option
     * b15-b0(uint16):低16为rpc命令
     * b19-b16:body压缩方式 0:无压缩 1:lz4 2:zlib inflate/deflate
     * b23-b20:optbin压缩方式 0:无压缩 1:lz4 2:zlib inflate/deflate
     * b31-b24: not used
     * @property {Uint8Array|null} [Sid] session id，登录后一定会有,用于后台区分不同的用户请求
     * @property {number|null} [Cid] rpc call id,给分辨不同的rpc调用使用,调用方增1循环使用
     * @property {number|null} [No] rpc no,从0开始增1使用,用于区分收到重复的包,特别是udp的情况下
     * @property {number|null} [Res] response code
     * @property {Uint8Array|null} [Body] msg body
     * @property {string|null} [Optstr] optional str parameter,额外的信息,一般不会有,有些特殊命令里面可能用到
     * @property {Uint8Array|null} [Optbin] optional binary parameter,额外的信息,一般不会有,有些特殊命令里面可能用到
     * @property {yrpcmsg.IMeta|null} [MetaInfo] optional grpc meta
     */

    /**
     * Constructs a new Ymsg.
     * @memberof yrpcmsg
     * @classdesc 系统中所有的消息交互底层都以此为包装
     * ymsg multiline comment
     * @implements IYmsg
     * @constructor
     * @param {yrpcmsg.IYmsg=} [properties] Properties to set
     */
    function Ymsg(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * 整个rpc msg的长度，不包含此字段
     * 虽然这个长度可以很长，但是为了避免大包阻塞其它操作，通常都要限制长度,采用分包多发机制
     * 当使用基于包的传输通道时(udp/kcp/websocket)，此值可能为0(此时长度为收到的整个包的长度)
     * @member {number} Len
     * @memberof yrpcmsg.Ymsg
     * @instance
     */
    Ymsg.prototype.Len = 0

    /**
     * rpc command,rpc的命令和option
     * b15-b0(uint16):低16为rpc命令
     * b19-b16:body压缩方式 0:无压缩 1:lz4 2:zlib inflate/deflate
     * b23-b20:optbin压缩方式 0:无压缩 1:lz4 2:zlib inflate/deflate
     * b31-b24: not used
     * @member {number} Cmd
     * @memberof yrpcmsg.Ymsg
     * @instance
     */
    Ymsg.prototype.Cmd = 0

    /**
     * session id，登录后一定会有,用于后台区分不同的用户请求
     * @member {Uint8Array} Sid
     * @memberof yrpcmsg.Ymsg
     * @instance
     */
    Ymsg.prototype.Sid = $util.newBuffer([])

    /**
     * rpc call id,给分辨不同的rpc调用使用,调用方增1循环使用
     * @member {number} Cid
     * @memberof yrpcmsg.Ymsg
     * @instance
     */
    Ymsg.prototype.Cid = 0

    /**
     * rpc no,从0开始增1使用,用于区分收到重复的包,特别是udp的情况下
     * @member {number} No
     * @memberof yrpcmsg.Ymsg
     * @instance
     */
    Ymsg.prototype.No = 0

    /**
     * response code
     * @member {number} Res
     * @memberof yrpcmsg.Ymsg
     * @instance
     */
    Ymsg.prototype.Res = 0

    /**
     * msg body
     * @member {Uint8Array} Body
     * @memberof yrpcmsg.Ymsg
     * @instance
     */
    Ymsg.prototype.Body = $util.newBuffer([])

    /**
     * optional str parameter,额外的信息,一般不会有,有些特殊命令里面可能用到
     * @member {string} Optstr
     * @memberof yrpcmsg.Ymsg
     * @instance
     */
    Ymsg.prototype.Optstr = ''

    /**
     * optional binary parameter,额外的信息,一般不会有,有些特殊命令里面可能用到
     * @member {Uint8Array} Optbin
     * @memberof yrpcmsg.Ymsg
     * @instance
     */
    Ymsg.prototype.Optbin = $util.newBuffer([])

    /**
     * optional grpc meta
     * @member {yrpcmsg.IMeta|null|undefined} MetaInfo
     * @memberof yrpcmsg.Ymsg
     * @instance
     */
    Ymsg.prototype.MetaInfo = null

    /**
     * Encodes the specified Ymsg message. Does not implicitly {@link yrpcmsg.Ymsg.verify|verify} messages.
     * @function encode
     * @memberof yrpcmsg.Ymsg
     * @static
     * @param {yrpcmsg.IYmsg} message Ymsg message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    Ymsg.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (message.Len != null && Object.hasOwnProperty.call(message, 'Len'))
        writer.uint32(/* id 1, wireType 5 =*/ 13).fixed32(message.Len)
      if (message.Cmd != null && Object.hasOwnProperty.call(message, 'Cmd'))
        writer.uint32(/* id 2, wireType 5 =*/ 21).fixed32(message.Cmd)
      if (message.Sid != null && Object.hasOwnProperty.call(message, 'Sid'))
        writer.uint32(/* id 3, wireType 2 =*/ 26).bytes(message.Sid)
      if (message.Cid != null && Object.hasOwnProperty.call(message, 'Cid'))
        writer.uint32(/* id 4, wireType 0 =*/ 32).uint32(message.Cid)
      if (message.No != null && Object.hasOwnProperty.call(message, 'No'))
        writer.uint32(/* id 5, wireType 0 =*/ 40).uint32(message.No)
      if (message.Res != null && Object.hasOwnProperty.call(message, 'Res'))
        writer.uint32(/* id 9, wireType 0 =*/ 72).sint32(message.Res)
      if (message.Body != null && Object.hasOwnProperty.call(message, 'Body'))
        writer.uint32(/* id 10, wireType 2 =*/ 82).bytes(message.Body)
      if (
        message.Optstr != null &&
        Object.hasOwnProperty.call(message, 'Optstr')
      )
        writer.uint32(/* id 11, wireType 2 =*/ 90).string(message.Optstr)
      if (
        message.Optbin != null &&
        Object.hasOwnProperty.call(message, 'Optbin')
      )
        writer.uint32(/* id 12, wireType 2 =*/ 98).bytes(message.Optbin)
      if (
        message.MetaInfo != null &&
        Object.hasOwnProperty.call(message, 'MetaInfo')
      )
        $root.yrpcmsg.Meta.encode(
          message.MetaInfo,
          writer.uint32(/* id 13, wireType 2 =*/ 106).fork(),
        ).ldelim()
      return writer
    }

    /**
     * Decodes a Ymsg message from the specified reader or buffer.
     * @function decode
     * @memberof yrpcmsg.Ymsg
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {yrpcmsg.Ymsg} Ymsg
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    Ymsg.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.yrpcmsg.Ymsg()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.Len = reader.fixed32()
            break
          case 2:
            message.Cmd = reader.fixed32()
            break
          case 3:
            message.Sid = reader.bytes()
            break
          case 4:
            message.Cid = reader.uint32()
            break
          case 5:
            message.No = reader.uint32()
            break
          case 9:
            message.Res = reader.sint32()
            break
          case 10:
            message.Body = reader.bytes()
            break
          case 11:
            message.Optstr = reader.string()
            break
          case 12:
            message.Optbin = reader.bytes()
            break
          case 13:
            message.MetaInfo = $root.yrpcmsg.Meta.decode(
              reader,
              reader.uint32(),
            )
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return Ymsg
  })()

  yrpcmsg.MetaItem = (function () {
    /**
     * Properties of a MetaItem.
     * @memberof yrpcmsg
     * @interface IMetaItem
     * @property {string|null} [key] MetaItem key
     * @property {Array.<string>|null} [vals] MetaItem vals
     */

    /**
     * Constructs a new MetaItem.
     * @memberof yrpcmsg
     * @classdesc grpc meta data item
     * @implements IMetaItem
     * @constructor
     * @param {yrpcmsg.IMetaItem=} [properties] Properties to set
     */
    function MetaItem(properties) {
      this.vals = []
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * MetaItem key.
     * @member {string} key
     * @memberof yrpcmsg.MetaItem
     * @instance
     */
    MetaItem.prototype.key = ''

    /**
     * MetaItem vals.
     * @member {Array.<string>} vals
     * @memberof yrpcmsg.MetaItem
     * @instance
     */
    MetaItem.prototype.vals = $util.emptyArray

    /**
     * Encodes the specified MetaItem message. Does not implicitly {@link yrpcmsg.MetaItem.verify|verify} messages.
     * @function encode
     * @memberof yrpcmsg.MetaItem
     * @static
     * @param {yrpcmsg.IMetaItem} message MetaItem message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    MetaItem.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (message.key != null && Object.hasOwnProperty.call(message, 'key'))
        writer.uint32(/* id 1, wireType 2 =*/ 10).string(message.key)
      if (message.vals != null && message.vals.length)
        for (let i = 0; i < message.vals.length; ++i)
          writer.uint32(/* id 2, wireType 2 =*/ 18).string(message.vals[i])
      return writer
    }

    /**
     * Decodes a MetaItem message from the specified reader or buffer.
     * @function decode
     * @memberof yrpcmsg.MetaItem
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {yrpcmsg.MetaItem} MetaItem
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    MetaItem.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.yrpcmsg.MetaItem()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.key = reader.string()
            break
          case 2:
            if (!(message.vals && message.vals.length)) message.vals = []
            message.vals.push(reader.string())
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return MetaItem
  })()

  yrpcmsg.Meta = (function () {
    /**
     * Properties of a Meta.
     * @memberof yrpcmsg
     * @interface IMeta
     * @property {Array.<yrpcmsg.IMetaItem>|null} [val] grpc meta
     */

    /**
     * Constructs a new Meta.
     * @memberof yrpcmsg
     * @classdesc grpc meta
     * @implements IMeta
     * @constructor
     * @param {yrpcmsg.IMeta=} [properties] Properties to set
     */
    function Meta(properties) {
      this.val = []
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * grpc meta
     * @member {Array.<yrpcmsg.IMetaItem>} val
     * @memberof yrpcmsg.Meta
     * @instance
     */
    Meta.prototype.val = $util.emptyArray

    /**
     * Encodes the specified Meta message. Does not implicitly {@link yrpcmsg.Meta.verify|verify} messages.
     * @function encode
     * @memberof yrpcmsg.Meta
     * @static
     * @param {yrpcmsg.IMeta} message Meta message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    Meta.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (message.val != null && message.val.length)
        for (let i = 0; i < message.val.length; ++i)
          $root.yrpcmsg.MetaItem.encode(
            message.val[i],
            writer.uint32(/* id 1, wireType 2 =*/ 10).fork(),
          ).ldelim()
      return writer
    }

    /**
     * Decodes a Meta message from the specified reader or buffer.
     * @function decode
     * @memberof yrpcmsg.Meta
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {yrpcmsg.Meta} Meta
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    Meta.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.yrpcmsg.Meta()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            if (!(message.val && message.val.length)) message.val = []
            message.val.push(
              $root.yrpcmsg.MetaItem.decode(reader, reader.uint32()),
            )
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return Meta
  })()

  yrpcmsg.GrpcMeta = (function () {
    /**
     * Properties of a GrpcMeta.
     * @memberof yrpcmsg
     * @interface IGrpcMeta
     * @property {yrpcmsg.IMeta|null} [Header] GrpcMeta Header
     * @property {yrpcmsg.IMeta|null} [Trailer] GrpcMeta Trailer
     */

    /**
     * Constructs a new GrpcMeta.
     * @memberof yrpcmsg
     * @classdesc grpc Header Trailer meta
     * @implements IGrpcMeta
     * @constructor
     * @param {yrpcmsg.IGrpcMeta=} [properties] Properties to set
     */
    function GrpcMeta(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * GrpcMeta Header.
     * @member {yrpcmsg.IMeta|null|undefined} Header
     * @memberof yrpcmsg.GrpcMeta
     * @instance
     */
    GrpcMeta.prototype.Header = null

    /**
     * GrpcMeta Trailer.
     * @member {yrpcmsg.IMeta|null|undefined} Trailer
     * @memberof yrpcmsg.GrpcMeta
     * @instance
     */
    GrpcMeta.prototype.Trailer = null

    /**
     * Encodes the specified GrpcMeta message. Does not implicitly {@link yrpcmsg.GrpcMeta.verify|verify} messages.
     * @function encode
     * @memberof yrpcmsg.GrpcMeta
     * @static
     * @param {yrpcmsg.IGrpcMeta} message GrpcMeta message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    GrpcMeta.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (
        message.Header != null &&
        Object.hasOwnProperty.call(message, 'Header')
      )
        $root.yrpcmsg.Meta.encode(
          message.Header,
          writer.uint32(/* id 1, wireType 2 =*/ 10).fork(),
        ).ldelim()
      if (
        message.Trailer != null &&
        Object.hasOwnProperty.call(message, 'Trailer')
      )
        $root.yrpcmsg.Meta.encode(
          message.Trailer,
          writer.uint32(/* id 2, wireType 2 =*/ 18).fork(),
        ).ldelim()
      return writer
    }

    /**
     * Decodes a GrpcMeta message from the specified reader or buffer.
     * @function decode
     * @memberof yrpcmsg.GrpcMeta
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {yrpcmsg.GrpcMeta} GrpcMeta
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    GrpcMeta.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.yrpcmsg.GrpcMeta()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.Header = $root.yrpcmsg.Meta.decode(reader, reader.uint32())
            break
          case 2:
            message.Trailer = $root.yrpcmsg.Meta.decode(reader, reader.uint32())
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return GrpcMeta
  })()

  yrpcmsg.Yempty = (function () {
    /**
     * Properties of a Yempty.
     * @memberof yrpcmsg
     * @interface IYempty
     */

    /**
     * Constructs a new Yempty.
     * @memberof yrpcmsg
     * @classdesc Represents a Yempty.
     * @implements IYempty
     * @constructor
     * @param {yrpcmsg.IYempty=} [properties] Properties to set
     */
    function Yempty(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * Encodes the specified Yempty message. Does not implicitly {@link yrpcmsg.Yempty.verify|verify} messages.
     * @function encode
     * @memberof yrpcmsg.Yempty
     * @static
     * @param {yrpcmsg.IYempty} message Yempty message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    Yempty.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      return writer
    }

    /**
     * Decodes a Yempty message from the specified reader or buffer.
     * @function decode
     * @memberof yrpcmsg.Yempty
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {yrpcmsg.Yempty} Yempty
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    Yempty.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.yrpcmsg.Yempty()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return Yempty
  })()

  yrpcmsg.Ynocare = (function () {
    /**
     * Properties of a Ynocare.
     * @memberof yrpcmsg
     * @interface IYnocare
     */

    /**
     * Constructs a new Ynocare.
     * @memberof yrpcmsg
     * @classdesc A generic nocare message that you can use to info the call is not important
     * and no care the result. A typical example is to use it in report log/trace.
     * For instance:
     *
     * service Log {
     * rpc Log(infos) returns (yrpc.Ynocare);
     * }
     * @implements IYnocare
     * @constructor
     * @param {yrpcmsg.IYnocare=} [properties] Properties to set
     */
    function Ynocare(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * Encodes the specified Ynocare message. Does not implicitly {@link yrpcmsg.Ynocare.verify|verify} messages.
     * @function encode
     * @memberof yrpcmsg.Ynocare
     * @static
     * @param {yrpcmsg.IYnocare} message Ynocare message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    Ynocare.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      return writer
    }

    /**
     * Decodes a Ynocare message from the specified reader or buffer.
     * @function decode
     * @memberof yrpcmsg.Ynocare
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {yrpcmsg.Ynocare} Ynocare
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    Ynocare.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.yrpcmsg.Ynocare()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return Ynocare
  })()

  yrpcmsg.UnixTime = (function () {
    /**
     * Properties of an UnixTime.
     * @memberof yrpcmsg
     * @interface IUnixTime
     * @property {Long|null} [TimeUnix] Unix time, the number of miliseconds elapsed since January 1, 1970 UTC
     * @property {string|null} [TimeStr] utc time yyyy-MM-dd hh:mm:ss.zzz
     */

    /**
     * Constructs a new UnixTime.
     * @memberof yrpcmsg
     * @classdesc Represents an UnixTime.
     * @implements IUnixTime
     * @constructor
     * @param {yrpcmsg.IUnixTime=} [properties] Properties to set
     */
    function UnixTime(properties) {
      if (properties)
        for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
          if (properties[keys[i]] != null) this[keys[i]] = properties[keys[i]]
    }

    /**
     * Unix time, the number of miliseconds elapsed since January 1, 1970 UTC
     * @member {Long} TimeUnix
     * @memberof yrpcmsg.UnixTime
     * @instance
     */
    UnixTime.prototype.TimeUnix = $util.Long
      ? $util.Long.fromBits(0, 0, false)
      : 0

    /**
     * utc time yyyy-MM-dd hh:mm:ss.zzz
     * @member {string} TimeStr
     * @memberof yrpcmsg.UnixTime
     * @instance
     */
    UnixTime.prototype.TimeStr = ''

    /**
     * Encodes the specified UnixTime message. Does not implicitly {@link yrpcmsg.UnixTime.verify|verify} messages.
     * @function encode
     * @memberof yrpcmsg.UnixTime
     * @static
     * @param {yrpcmsg.IUnixTime} message UnixTime message or plain object to encode
     * @param {$protobuf.Writer} [writer] Writer to encode to
     * @returns {$protobuf.Writer} Writer
     */
    UnixTime.encode = function encode(message, writer) {
      if (!writer) writer = $Writer.create()
      if (
        message.TimeUnix != null &&
        Object.hasOwnProperty.call(message, 'TimeUnix')
      )
        writer.uint32(/* id 1, wireType 0 =*/ 8).sint64(message.TimeUnix)
      if (
        message.TimeStr != null &&
        Object.hasOwnProperty.call(message, 'TimeStr')
      )
        writer.uint32(/* id 2, wireType 2 =*/ 18).string(message.TimeStr)
      return writer
    }

    /**
     * Decodes an UnixTime message from the specified reader or buffer.
     * @function decode
     * @memberof yrpcmsg.UnixTime
     * @static
     * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
     * @param {number} [length] Message length if known beforehand
     * @returns {yrpcmsg.UnixTime} UnixTime
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    UnixTime.decode = function decode(reader, length) {
      if (!(reader instanceof $Reader)) reader = $Reader.create(reader)
      let end = length === undefined ? reader.len : reader.pos + length,
        message = new $root.yrpcmsg.UnixTime()
      while (reader.pos < end) {
        let tag = reader.uint32()
        switch (tag >>> 3) {
          case 1:
            message.TimeUnix = reader.sint64()
            break
          case 2:
            message.TimeStr = reader.string()
            break
          default:
            reader.skipType(tag & 7)
            break
        }
      }
      return message
    }

    return UnixTime
  })()

  return yrpcmsg
})())

export { $root as default }

import * as $protobuf from 'protobufjs'
/** Namespace user. */
export namespace user {
  /** Properties of a DbUserList. */
  interface IDbUserList {
    /** 批次号，从0开始 */
    BatchNo?: number | null

    /** rows offset,从0开始 */
    RowsOffset?: number | null

    /** 本批次数据 */
    Rows?: user.IDbUser[] | null
  }

  /** DbUser list */
  class DbUserList implements IDbUserList {
    /**
     * Constructs a new DbUserList.
     * @param [properties] Properties to set
     */
    constructor(properties?: user.IDbUserList)

    /** 批次号，从0开始 */
    public BatchNo: number

    /** rows offset,从0开始 */
    public RowsOffset: number

    /** 本批次数据 */
    public Rows: user.IDbUser[]

    /**
     * Encodes the specified DbUserList message. Does not implicitly {@link user.DbUserList.verify|verify} messages.
     * @param message DbUserList message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: user.IDbUser<PERSON>ist,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a DbUserList message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns DbUserList
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): user.DbUserList
  }

  /** Properties of a DbUserRoleList. */
  interface IDbUserRoleList {
    /** 批次号，从0开始 */
    BatchNo?: number | null

    /** rows offset,从0开始 */
    RowsOffset?: number | null

    /** 本批次数据 */
    Rows?: user.IDbUserRole[] | null
  }

  /** DbUserRole list */
  class DbUserRoleList implements IDbUserRoleList {
    /**
     * Constructs a new DbUserRoleList.
     * @param [properties] Properties to set
     */
    constructor(properties?: user.IDbUserRoleList)

    /** 批次号，从0开始 */
    public BatchNo: number

    /** rows offset,从0开始 */
    public RowsOffset: number

    /** 本批次数据 */
    public Rows: user.IDbUserRole[]

    /**
     * Encodes the specified DbUserRoleList message. Does not implicitly {@link user.DbUserRoleList.verify|verify} messages.
     * @param message DbUserRoleList message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: user.IDbUserRoleList,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a DbUserRoleList message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns DbUserRoleList
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): user.DbUserRoleList
  }

  /** Properties of a DbUser. */
  interface IDbUser {
    /**
     * @db uuid primary key
     * 行ID
     */
    RID?: string | null

    /**
     * @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 用户所属的群组
     */
    OrgRID?: string | null

    /**
     * @db varchar(16) not null unique
     * 用户编号
     */
    UserID?: string | null

    /**
     * @db int
     * 用户类型
     */
    UserType?: number | null

    /**
     * @db varchar(32) not null
     * 用户名
     */
    UserName?: string | null

    /**
     * @db text
     * 备注
     */
    Note?: string | null

    /**
     * @db text
     * 用户电话
     */
    Phone?: string | null

    /**
     * @db uuid REFERENCES DbImage(RID) ON DELETE SET NULL
     * 用户图片
     */
    Image?: string | null

    /**
     * @db jsonb not null default  '{}'::jsonb
     * 其它设置，可以在里面扩展需要用到的其它信息
     */
    Setting?: string | null

    /**
     * @db varchar(32) unique
     * 用户登录名,NULL(空)表示不能登录系统
     */
    LoginName?: string | null

    /**
     * @db text default ''
     * 用户登录密码,为base64(sha256(user_name+user_password)),系统不保存真实的密码
     */
    LoginPass?: string | null

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     */
    UpdatedAt?: string | null

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */
    UpdatedDC?: string | null
  }

  /** 用户表 */
  class DbUser implements IDbUser {
    /**
     * Constructs a new DbUser.
     * @rpc crud pcrud
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbUserOrgRID on DbUser USING hash(OrgRID);
     * @dbpost INSERT INTO dbuser(rid, orgrid, userid, usertype, username, phone, image, setting, loginname, loginpass, updatedat, updateddc)VALUES('00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', 'ttt', 0, 'ttt', '', NULL, '{}'::jsonb, 'ttt', 'UwhpDj0xMGNzZw8sXYZOLGBHmH/q9Wxf7aB6/EbT43Q=', now_utc(), '') ON CONFLICT  DO NOTHING;
     * @param [properties] Properties to set
     */
    constructor(properties?: user.IDbUser)

    /**
     * @db uuid primary key
     * 行ID
     */
    public RID: string

    /**
     * @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 用户所属的群组
     */
    public OrgRID: string

    /**
     * @db varchar(16) not null unique
     * 用户编号
     */
    public UserID: string

    /**
     * @db int
     * 用户类型
     */
    public UserType: number

    /**
     * @db varchar(32) not null
     * 用户名
     */
    public UserName: string

    /**
     * @db text
     * 备注
     */
    public Note: string

    /**
     * @db text
     * 用户电话
     */
    public Phone: string

    /**
     * @db uuid REFERENCES DbImage(RID) ON DELETE SET NULL
     * 用户图片
     */
    public Image: string

    /**
     * @db jsonb not null default  '{}'::jsonb
     * 其它设置，可以在里面扩展需要用到的其它信息
     */
    public Setting: string

    /**
     * @db varchar(32) unique
     * 用户登录名,NULL(空)表示不能登录系统
     */
    public LoginName: string

    /**
     * @db text default ''
     * 用户登录密码,为base64(sha256(user_name+user_password)),系统不保存真实的密码
     */
    public LoginPass: string

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     */
    public UpdatedAt: string

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */
    public UpdatedDC: string

    /**
     * Encodes the specified DbUser message. Does not implicitly {@link user.DbUser.verify|verify} messages.
     * @param message DbUser message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: user.IDbUser,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a DbUser message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns DbUser
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): user.DbUser
  }

  /** Properties of a DbUserRole. */
  interface IDbUserRole {
    /**
     * @db uuid primary key
     * 行ID
     */
    RID?: string | null

    /**
     * @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 用户所属的群组
     */
    OrgRID?: string | null

    /**
     * @db uuid not null REFERENCES DbUser(RID) ON DELETE CASCADE
     * 用户rid
     */
    UserRID?: string | null

    /**
     * @db uuid not null REFERENCES DbRole(RID) ON DELETE CASCADE
     * role rid
     */
    RoleRID?: string | null

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     */
    UpdatedAt?: string | null

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */
    UpdatedDC?: string | null
  }

  /**
   * 用户角色数据表
   * 一个用户可以有多个角色
   */
  class DbUserRole implements IDbUserRole {
    /**
     * Constructs a new DbUserRole.
     * @rpc crud pcrud
     * @dbpost CREATE INDEX IF NOT EXISTS idxDbUserRoleUserRID on DbUserRole USING hash(UserRID);
     * @param [properties] Properties to set
     */
    constructor(properties?: user.IDbUserRole)

    /**
     * @db uuid primary key
     * 行ID
     */
    public RID: string

    /**
     * @db uuid not null REFERENCES DbOrg(RID) ON DELETE CASCADE
     * 用户所属的群组
     */
    public OrgRID: string

    /**
     * @db uuid not null REFERENCES DbUser(RID) ON DELETE CASCADE
     * 用户rid
     */
    public UserRID: string

    /**
     * @db uuid not null REFERENCES DbRole(RID) ON DELETE CASCADE
     * role rid
     */
    public RoleRID: string

    /**
     * @db timestamp not null default now_utc()
     * 数据最后修改时间
     */
    public UpdatedAt: string

    /**
     * @db text
     * 数据修改的数据中心,多数据中心时用于指示是那个数据中心出来的数据
     */
    public UpdatedDC: string

    /**
     * Encodes the specified DbUserRole message. Does not implicitly {@link user.DbUserRole.verify|verify} messages.
     * @param message DbUserRole message or plain object to encode
     * @param [writer] Writer to encode to
     * @returns Writer
     */
    public static encode(
      message: user.IDbUserRole,
      writer?: $protobuf.Writer,
    ): $protobuf.Writer

    /**
     * Decodes a DbUserRole message from the specified reader or buffer.
     * @param reader Reader or buffer to decode from
     * @param [length] Message length if known beforehand
     * @returns DbUserRole
     * @throws {Error} If the payload is not a reader or valid buffer
     * @throws {$protobuf.util.ProtocolError} If required fields are missing
     */
    public static decode(
      reader: $protobuf.Reader | Uint8Array,
      length?: number,
    ): user.DbUserRole
  }
}

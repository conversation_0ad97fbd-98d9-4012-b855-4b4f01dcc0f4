import { user } from '@ygen/user'
import { org } from '@ygen/org'
import { user as perm } from '@ygen/userPermission'
import { bysdb } from '@ygen/bysdb'
import log from 'loglevel'

import dataStore, { Bys<PERSON>ark<PERSON>, Controller } from '@services/dataStore'
import { yrpcmsg } from 'yrpcmsg'
import { StrPubSub } from 'ypubsub'
import { String2UInt8Array, UInt8Array2String } from '@utils/crypto'
import { Store } from '@src/store'
import { i18n } from '@boot/i18n'
import { Notify } from 'quasar'
import { rpcCon } from 'jsyrpc'
import {
  UpdateDbBysMarker,
  UpdateDbController,
  UpdateDbOrg,
  UpdateDbRole,
  UpdateDbUser,
  UpdateMediaInfo,
} from '@utils/pubSubSubject'

import { throttle, cloneDeep } from 'lodash'

import { user as userOrgPrivilege } from '@ygen/userOrgPrivilege'
import Ymsg = yrpcmsg.Ymsg
import { BysMarkerAndUpdateInfo, ControllerAndUpdateCmd } from '@utils/bysdb.type'
import { crud } from '@ygen/crud'
import { deleteOneICCIDAndIMEI } from '@services/controller'
import { updateMarkerStatusAfterAction } from './common'

const StoreSyncMethods: { [key: string]: (data: commonDbType, dataType: string, Cmd: number, dmlParam: crud.IDMLParam) => boolean } = {
  'Insert': InsertData,
  'Delete': DeleteData,
  'Update': UpdateData,
  'PartialUpdate': PartialUpdateData,
}

//用于约束变量类型
interface commonDbType {
  RID: string,
  OrgRID: string,

  [key: string]: any
}

const StorePubSubEvt = {
  DbUser: 'dbUser',
  DbUserRole: 'dbUserRole',
  DbRole: 'dbRole',
  DbOrg: 'dbOrg',
  DbController: 'dbController',
  DbBysMarker: 'dbBysMarker',
  DbMediaInfo: 'dbMediaInfo',

  DbUserOrgPrivilege: 'dbUserPrivilege',

  Insert: 'insert-',
  Update: 'update-',
  Delete: 'delete-',
}

const DecodeMap = {
  DbUser: user.DbUser,
  DbUserRole: user.DbUserRole,
  DbRole: perm.DbRole,
  DbOrg: org.DbOrg,

  DbUserOrgPrivilege: userOrgPrivilege.DbUserOrgPrivilege,

  DbController: bysdb.DbController,
  DbBysMarker: bysdb.DbBysMarker,
  DbMediaInfo: bysdb.DbMediaInfo,
}

const notifyTextMap = {
  DbUser: i18n.global.t('menus.user'),
  DbUserRole: i18n.global.t('userRole.userRole'),
  DbRole: i18n.global.t('menus.role'),
  DbOrg: i18n.global.t('menus.organization'),

  DbController: i18n.global.t('menus.controller'),
  DbBysMarker: i18n.global.t('menus.boundaryMarker'),
  DbMediaInfo: i18n.global.t('menus.images'),

  Sync: i18n.global.t('PermTab.Sync'),
}

const DataStore = {
  DbUser: dataStore.User,
  DbUserRole: dataStore.UserRole,
  DbRole: dataStore.Role,
  DbOrg: dataStore.Unit,

  DbUserOrgPrivilege: dataStore.UserOrgPrivilege,

  DbController: dataStore.Controller,
  DbBysMarker: dataStore.BysMarker,
  DbMediaInfo: dataStore.MediaInfo,
}

function IsExistInMyOrg(orgRID: string): boolean {
  if (!orgRID) {
    return false
  }
  return !!dataStore.Unit.getData(orgRID)
}

function IsExistDataInStore(rid: string, dataType: string): boolean {
  return !!rid && (!!DataStore[dataType]?.getData(rid))
}

function InsertData(data: commonDbType, dataType: string): boolean {
  if (!IsExistInMyOrg(data.OrgRID)) {
    return false
  }
  log.info('sync InsertData:', dataType, data)
  DataStore[dataType]?.setData(data.RID, data)
  StrPubSub.publish(StorePubSubEvt['Insert'] + StorePubSubEvt[dataType], data)
  return true
}

function DeleteData(data: commonDbType, dataType: string): boolean {
  DataStore[dataType]?.deleteData(data.RID)
  log.info('sync DeleteData:', dataType, data)
  // 发布删除数据通知
  StrPubSub.publish(StorePubSubEvt['Delete'] + StorePubSubEvt[dataType], data)
  return true
}

function PartialUpdateData(data: commonDbType, dataType: string, Cmd: number,dmlParam: crud.IDMLParam): boolean {
  const markerPartData = {}
  if (dataType === 'DbBysMarker') {
    dmlParam.KeyColumn?.forEach(key => {
      markerPartData[key] = data[key]
    })
    DataStore.DbBysMarker.setPartData(data.RID, markerPartData)
    return true
  }
  return false
}

function UpdateData(data: commonDbType, dataType: string, Cmd: number): boolean {
  if (Cmd === 2) {
    // data.cmd == 2 这里的2 是客户端定义得到命令号
    setTimeout(() => {
      if (DataStore[dataType]?.getData(data.RID)?.OrgRID === data.OrgRID) {
        DeleteData(data, dataType)
        return true
      }
    }, 0)
    return false
  }
  // data.cmd == 1 这里的1 是服务器定义的命令号
  if (!IsExistInMyOrg(data.OrgRID)) {
    DeleteData(data, dataType)
    return true
  }

  if (IsExistDataInStore(data.RID, dataType)) {
    log.info('sync UpdateData:', dataType, data)
    // 如果是界桩，需要更新红线图和界桩标志等地图图层
    // 应该将界桩和控制器的自定义属性继承下来
    if (dataType === 'DbBysMarker') {
      const oldData = cloneDeep(BysMarker.getData(data.RID)) as BysMarkerAndUpdateInfo
      const customProps = {
        isAlreadyRemoveAlarm: oldData.isAlreadyRemoveAlarm,
        removeAlarm: oldData.removeAlarm,
        updateInfo: oldData.updateInfo,
        GCJ02LngLat: oldData.GCJ02LngLat,
        // 针对4g界桩新添加字段属性
        onlineInfo: oldData.onlineInfo,
        markerInfo: oldData.markerInfo,
      }
      Object.assign(data, customProps)
      StrPubSub.publish(UpdateDbBysMarker, data, oldData, 'cdc')
      // 同步删除IMEI和ICCID预选框中已被选择使用掉的
      deleteOneICCIDAndIMEI(data.IMEI)
      // 界桩数据同步后 更新设备树和地图marker状态
      updateMarkerStatusAfterAction(data)
    } else if (dataType === 'DbController') {
      const oldData = cloneDeep(Controller.getData(data.RID)) as ControllerAndUpdateCmd
      const customProps = {
        controllerState: oldData.controllerState,
        GCJ02LngLat: oldData.GCJ02LngLat,
        Error: oldData.Error,
      }
      Object.assign(data, customProps)
      StrPubSub.publish(UpdateDbController, data, oldData, 'cdc')
    }
    // update
    DataStore[dataType]?.setData(data.RID, data)
    return true
  }

  InsertData(data, dataType)
  return true
}

const myNotify = throttle((dbType: string) => {
  Notify.create({
    type: 'info',
    message: notifyTextMap[dbType] + notifyTextMap['Sync'],
    position: 'top',
  })
}, 10 * 1000)

const notifyHistory: { [key: string]: Function } = (() => {
  const notNeedNotifyDbNames: string[] = ['DbUserOrgPrivilege']

  return Object.keys(DataStore)
    //DecodeMap 部分数据不显示同步处理
    .filter(item => !notNeedNotifyDbNames.includes(item))
    .map(key => {
      return { [key]: myNotify }
    })
    .reduce((p, c) => Object.assign(p, c), {})
})()

export function syncLocalStore(myMsg: Ymsg) {
  if (UInt8Array2String(myMsg.Sid) === Store.state.SessionRID) {
    return
  }
  const { Optstr, Body, Cmd } = myMsg
  const [dbType, opt] = Optstr.split('.', 2)
  const objData = DecodeMap[dbType]?.decode(Body)

  if (typeof objData === 'undefined') {
    log.error('[syncLocalStore] error', myMsg, DecodeMap[dbType])
    return
  }
  const optBin = crud.DMLParam.decode(myMsg.Optbin)
  StoreSyncMethods[opt]?.(objData, dbType, Cmd, optBin)
    && notifyHistory[dbType]?.(dbType)
}

export function publishUpdateDataOrg(OrgRID: string, data: commonDbType, dataType: string) {
  const msg: yrpcmsg.Ymsg = new yrpcmsg.Ymsg({
    Sid: String2UInt8Array(Store.state.SessionRID as string),
    Cmd: 2,
    Body: DecodeMap[dataType]?.encode(data).finish(),
    Optstr: `${dataType}.Update`,
  })
  const bytes = yrpcmsg.Ymsg.encode(msg).finish()
  rpcCon.NatsPublish(`cdc.${Store.state.System}.${OrgRID}`, bytes)
}

export function IsDataOrgRIDChanged(newdata: commonDbType, olddata: commonDbType) {
  return olddata.OrgRID !== newdata.OrgRID
}

export function SubScribeUpdateEvt() {
  const evts = [UpdateDbBysMarker, UpdateDbOrg, UpdateDbUser, UpdateDbController, UpdateMediaInfo, UpdateDbRole]
  const typeMap = {
    [UpdateDbUser]: 'DbUser',
    [UpdateDbRole]: 'DbRole',
    [UpdateDbOrg]: 'DbOrg',

    [UpdateDbController]: 'DbController',
    [UpdateDbBysMarker]: 'DbBysMarker',
    [UpdateMediaInfo]: 'DbMediaInfo',
  }
  for (let evt of evts) {
    StrPubSub.subscribe(evt, (newData, oldData) => {
      if (IsDataOrgRIDChanged(newData, oldData)) {
        publishUpdateDataOrg(oldData.OrgRID, oldData, typeMap[evt])
      }
    })
  }
}

// 连接服务器
import { Store } from '@src/store'
import { rpcCon } from 'jsyrpc'
import { Platform } from 'quasar'
import log from '@src/utils/log'

if (process.env.DEV) {
  log.info('Platform:', Platform)
}

let { protocol, hostname, port } = window.location

// app和本地开发环境下，则需要使用指定的地址
if (Platform.is.cordova || process.env.DEV) {
  protocol = process.env.protocol
  hostname = process.env.hostname
  port = process.env.port
}

const path = 'yrpc'
const isSSl = protocol.includes('https')
const defPort = isSSl ? 443 : 80
const wss = isSSl ? 'wss' : 'ws'
const wsUrl = `${wss}://${hostname}:${port || defPort}/${path}`

// 连接yrpc
rpcCon.on('onopen', () => {
  Store.commit('updateServerStatus', { status: true })
})
rpcCon.on('onclose', () => {
  Store.commit('updateServerStatus', { status: false })
})
rpcCon.initWsCon(wsUrl)

import { Permission } from '@src/services/dataStore'
import { yrpcmsg } from 'yrpcmsg'
import { i18n } from '@boot/i18n'

export interface SortOption {
  descending?: boolean,
  prop?: string
}

export function sortData<T extends object>(data: T[], option: SortOption = {}): T[] {
  return data.sort((a, b) => {
    const prop = option.prop || ''
    if (option.descending) {
      // 降序排序
      return a[prop] > b[prop]? -1: a[prop] === b[prop]? 0: 1
    } else {
      // 升序排序
      return a[prop] > b[prop]? 1: a[prop] === b[prop]? 0: -1
    }
  })
}

// 权限数据管理
export enum PermissionType {
  db = 'db',
  menu = 'menu',
  cmd = 'cmd',
}

//Db权限管理组件需要显示的行
export enum DbPermShowRows {
  DbOrg = 'DbOrg',
  DbUser = 'DbUser',
  DbRole = 'DbRole',
  DbController = 'DbController',
  DbBysMarker = 'DbBysMarker',
  DbMediaInfo = 'DbMediaInfo',
  DbControllerOnlineHistory = 'DbControllerOnlineHistory',
  DbMarkerHistory = 'DbMarkerHistory',
  DbMarkerPatrolHistory = 'DbMarkerPatrolHistory',
}

export enum DbName {
  DbOrg = 'DbOrg',
  DbUser = 'DbUser',
  DbRole = 'DbRole',
  DbController = 'DbController',
  DbBysMarker = 'DbBysMarker',
  DbMediaInfo = 'DbMediaInfo',
  DbControllerOnlineHistory = 'DbControllerOnlineHistory',
  DbMarkerHistory = 'DbMarkerHistory',
  DbMarkerUploadImageHistory = 'DbMarkerUploadImageHistory',
  DbMarkerPatrolHistory = 'DbMarkerPatrolHistory',
  DbConfig = 'DbConfig',
  DbUserRole = 'DbUserRole',
  DbUserOrgPrivilege = 'DbUserOrgPrivilege',
  DbUserSession = 'DbUserSession',
  DbImage = 'DbImage',
  DbPermission = 'DbPermission',
  DbRolePermission = 'DbRolePermission',
  PatrolStatistics = 'PatrolStatistics'
}

export enum OperationType {
  Insert = 'Insert',
  Query = 'Query',
  Update = 'Update',
  Delete = 'Delete'
}

// 检查登录用户是有指定的权限 permissionIndex: DbOrg.Query
export function checkSpecifyPermission(permissionIndex: string): boolean {
  return !!Permission.getDataByIndex(permissionIndex)
}

// 检查对应的操作是否有权限
export function checkOperationPermission(dbName: DbName, optName: OperationType): boolean {
  // 使用权限名称索引，从权限数据中读取权限数据，能查到权限数据，则视为有权限
  return checkSpecifyPermission(`${dbName}.${optName}`)
}

// 检查登录用户是否有对应的菜单权限
export function checkMenuPermission(dbName: DbName): boolean {
  return checkSpecifyPermission(`Data.${dbName}`)
}

// 检查登录用户是否有对应的历史查询权限
export function checkQueryHistoryPermission(dbName: DbName): boolean {
  return checkSpecifyPermission(`Query.${dbName}`)
}

// 所有用户都拥有的角色的RID
export const BaseRoleRids = [
  '55555555-5555-5555-5555-555555555555',
]

// 当登录用户修改 角色/角色权限/用户-角色 数据时，会确认该角色是否属于操作用户。不是则返回错误信息：
// operation user not hava this role
export function checkRoleAndUserError(errRpc: yrpcmsg.Ymsg, defReason = i18n.global.t('message.addFailed')): string {
  let reason = defReason
  if (errRpc.Optstr.includes('operation user not hava this role')) {
    reason = i18n.global.t('message.noOptPerm')
  }
  return reason
}

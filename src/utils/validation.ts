export function required(data?: any): boolean {
  return data !== undefined && data !== null && data !== ''
}

export function maxLength(data: string | any[], max = 16): boolean {
  return isEmpty(data) ? true : data.length <= max
}

export function maxValue(data: number, max = 0xFFFFFFFF): boolean {
  return data <= max
}

export function minValue(data: number, min = 0): boolean {
  return data >= min
}

export function mobilePhone(data: string): boolean {
  return /^1[3456789]\d{9}$/.test(data)
}

export function fixedPhone(data: string): boolean {
  return /^((0\d{2,3}-)|(\(0\d{2,3}\)))?\d{7,8}$/.test(data)
}

// 检测是否为移动或固定电话
export function isPhone(data: string): boolean {
  const isMobilePhone = mobilePhone(data)
  // 不是移动号码，则判断是否为固定电话
  if (!isMobilePhone) {
    return fixedPhone(data)
  }
  return true
}

// 允许为空值
export function isEmpty(data: string | any[]): boolean {
  return !data || data.length === 0
}

// 是否为BCD码
export function isBCDCode(data: string): boolean {
  return /^[0-9]+$/.test(data) && data.length % 2 === 0
}

// 必须是合法的经度，经度在-180～180
export function isLon(data: string | number): boolean {
  const value = typeof data === 'string' ? parseFloat(data) : data
  return value >= -180 && value <= 180
}

// 必须是合法的经度，经度在-90~90
export function isLat(data: string | number): boolean {
  const value = typeof data === 'string' ? parseFloat(data) : data
  return value >= -90 && value <= 90
}

// 大于0的正整数
export function isPositiveInteger(data: string | number): boolean {
  const val = Number(data)
  if (isNaN(val) || val <= 0) {
    return false
  }

  return Math.floor(val) === val
}

// 大于等于0的正整数
export function isEG2Zero(data: string | number): boolean {
  const val = Number(data)
  if (isNaN(val) || val < 0) {
    return false
  }

  return Math.floor(val) === val
}

// 是否为有效的时间字符串 hh:mm:ss
export function isValidTime(data: string): boolean {
  return /^([01][0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$/.test(data)
}

export interface InRange {
  (data: number): boolean
}

// 参数范围规则生成函数
export function genRangeRule(min: number, max: number): InRange {
  return (data: number) => {
    return data >= min && data <= max
  }
}

// 默认的ID取值范围，控制器、界桩
export const Sint32Limit = 0xFFFFFFF
export const IDRangeRule = genRangeRule(0, Sint32Limit)

// ipv4校验
export function isIPV4ValidIP(ip: string) {
  const v4reg = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/
  return v4reg.test(ip)
}

// ipv6校验
export function isIPV6ValidIP(ip: string) {
  const v6reg = /^\s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))(%.+)?\s*$/gm
  return v6reg.test(ip)
}

// ip校验
export function isIPValid(ip: string) {
  return isIPV4ValidIP(ip) || isIPV6ValidIP(ip)
}

// 域名校验
export function isDomain(domain: string) {
  const domainCheck = /^(?=^.{3,255}$)[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+$/
  return domainCheck.test(domain)
}

// 20位字符串 https://baike.baidu.com/item/iccid/5181544
// 898600MFSSYYGXXXXXXP
// 898601YY8SSXXXXXXXXP
// 898603YYXMHHHXXXXXXP
//(?<M>[0-9,a-f,A-F])(?<F>\d)(?<SS>\d{2})(?<YY>\d{2})(?<G>[0-9,a-f,A-F])(?<userNo>[0-9,a-f,A-F]{6})(?<P>[0-9,a-f,A-F])
export function iccidReg(str: string) {
  // 89： 国际编号
  // 86： 国家编号，86：中国
  const iccidReg = /^8986(?<yunyingshang>|(?<liantong>01|06|09)|(?<dianxin>03|11)(?<移动>00|02|04|07))/
  return iccidReg.test(str)
}

// 有效的日期字符串 YYYY-MM-DD
// 参考： https://www.cnblogs.com/cxjchen/p/3147641.html

// 平年：年份可统一写作：(?!0000)[0-9]{4}
// 包括平年在内的所有年份的月份都包含1-28日：(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-8])
// 包括平年在内的所有年份除2月外都包含29和30日：(0[13-9]|1[0-2])-(29|30)
// 包括平年在内的所有年份1、3、5、7、8、10、12月都包含31日：(0[13578]|1[02])-31)
// 合起来就是除闰年的2月29日外的其它所有日期：(?!0000)[0-9]{4}-((0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-8])|(0[13-9]|1[0-2])-(29|30)|(0[13578]|1[02])-31)

// 润年
// 能被4整除但不能被100整除的年份：([0-9]{2}(0[48]|[2468][048]|[13579][26])
// 能被400整除的年份。能被400整除的数肯定能被100整除，因此后两位肯定是00：(0[48]|[2468][048]|[13579][26])00
// 合起来就是所有闰年的2月29日：([0-9]{2}(0[48]|[2468][048]|[13579][26])|(0[48]|[2468][048]|[13579][26])00)-02-29)
// 四条规则都已实现，且互相间没有影响，合起来就是所有符合DateTime范围的日期的正则
// ^((?!0000)[0-9]{4}-((0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-8])|(0[13-9]|1[0-2])-(29|30)|(0[13578]|1[02])-31)|([0-9]{2}(0[48]|[2468][048]|[13579][26])|(0[48]|[2468][048]|[13579][26])00)-02-29)$
//
// 考虑到这个正则表达式仅仅是用作验证，所以捕获组没有意义，只会占用资源，影响匹配效率，所以可以使用非捕获组来进行优化。
// ^(?:(?!0000)[0-9]{4}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1[0-9]|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[0-9]{2}(?:0[48]|[2468][048]|[13579][26])|(?:0[48]|[2468][048]|[13579][26])00)-02-29)$
export function isValidDay(data) {
  const reg = /^(?:(?!0000)[0-9]{4}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1[0-9]|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[0-9]{2}(?:0[48]|[2468][048]|[13579][26])|(?:0[48]|[2468][048]|[13579][26])00)-02-29)$/
  return reg.test(data)
}

// 校验IMEI
export function validateIMEI(imei: string) {
  const arr = imei.slice(0, 14).split('')
  const sp = arr.map((s ,i) => {
    const n = Number(s)
    if (i % 2 === 1) {
      const v = n * 2
      return v < 10 ? v : (~~(v/10) + (v%10)) 
    }
    return n
  })
    .reduce((p, c) => p+c, 0)
  if (sp === 0) {
    return sp === Number(imei[14])
  }
  return 10 - (sp % 10) === Number(imei[14])
}
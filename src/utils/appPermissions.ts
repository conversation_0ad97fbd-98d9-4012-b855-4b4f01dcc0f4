import { Platform } from 'quasar'
import log from '@utils/log'

export function requestPermission(permission: string): Promise<boolean> {
  return new Promise(resolve => {
    cordova.plugins.permissions?.checkPermission(permission, (status: { [key: string]: any }) => {
      if (status.hasPermission) {
        resolve(status.hasPermission)
        return
      }
      cordova.plugins.permissions?.requestPermission(permission, (status: { [key: string]: any }) => {
        resolve(status.hasPermission)
      }, log.error)
    }, log.error)
  })
}

export async function requestPermissions(permissionList: any[]) {
  const it: Iterator<string> = permissionList[Symbol.iterator]()
  const request = async ({ done, value }: IteratorResult<string>) => {
    if (done) { return }
    await requestPermission(value)
      .catch(log.error)
    await request(it.next())
  }
  await request(it.next())
}

// 申请app使用的权限
export async function requirePerms() {
  if (Platform.is.cordova || Platform.is.capacitor) {
    const permissions = cordova.plugins.permissions
    if (!permissions) { return }
    const AppPermissions: any[] = [
      permissions.WRITE_EXTERNAL_STORAGE, // 外部存储
      permissions.ACCESS_FINE_LOCATION, // GPS
      permissions.WAKE_LOCK, // 防止休眠
      permissions.CAMERA, // 相机
      permissions.VIBRATE, // 震动
      permissions.POST_NOTIFICATIONS, // 推送通知消息
      permissions.NFC, // NFC
    ]
    await requestPermissions(AppPermissions)
  }
}

/*function main() {
  const permissions = cordova.plugins.permissions
  if (!permissions) { return }
  const AppPermissions: any[] = [
    permissions.WRITE_EXTERNAL_STORAGE, // 外部存储
    permissions.ACCESS_FINE_LOCATION, // GPS
    permissions.WAKE_LOCK, // 防止休眠
    permissions.CAMERA, // 相机
    permissions.VIBRATE, // 震动
  ]
  requestPermissions(AppPermissions)
}*/

/*if (Platform.is.cordova || Platform.is.capacitor) {
  // main()
}*/


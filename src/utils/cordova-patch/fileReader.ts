import { Platform } from 'quasar'

const IsApp = Platform.is.cordova || Platform.is.capacitor

function main() {
  if (!IsApp) {
    return
  }

  if (!FileReader.prototype.addEventListener) {
    // @ts-ignore
    FileReader.prototype.addEventListener = function (type: string, listener: Function): void {
      let ctx = this
      let method = 'on' + type
      let events = ctx.onevents = ctx.onevents || {}
      let listeners = events[method] = events[method] || []
      listeners.push(listener)

      ctx[method] = function (e) {
        listeners.forEach((fn) => {
          fn.call(ctx, new Event(e, ctx))
        })
      }.bind(ctx)
    }

    // @ts-ignore
    FileReader.prototype.removeEventListener = function (type: string, listener: Function): void {
      let ctx = this
      let method = 'on' + type
      let ListenersName = method + 'Listeners'
      let listeners = ctx[ListenersName] = ctx[ListenersName] || []
      let length = listeners.length

      for (let i = length; i > 0; i--) {
        if (listeners[i] === listener) {
          listeners.splice(i, 1)
        }
      }
    }
  }
}

main()


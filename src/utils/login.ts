import { user as userApi } from '@ygen/user.api'
import { ICallOption, rpcCon } from 'jsyrpc'
import { yrpcmsg } from 'yrpcmsg'
import log from './log'
import { RpcUser } from '@ygen/user.api.yrpc'
import { StrPubSub } from 'ypubsub'
import { Store } from '@src/store'
import { i18n } from '@src/boot/i18n'
import { Notify } from 'quasar'
import storage, { StoreLangKey, StoreLoginInfo } from './storage'
import { utcTime } from './dayjs'
import { encodeLoginPassword } from './crypto'
import { CLEAN, NS } from '@src/store/data/methodTypes'
import { natsSubscribeCdcEvents, natsSubsribeUserSettings, queryBaseData, resetQueryFinishStatus, QueryFinishStatus } from '@services/queryData'
import { CleanAllAlarmMarkerData, CleanDeviceTree, ReloginDataQueryStarted, LoginFailed, LoginSuccess, RefreshServerVersion } from './pubSubSubject'
import { queryNotificationPerm } from '@utils/common'
import { rpc } from '@ygen/rpc'
import { controllerCmdProcess } from '@src/services/controller'
import { initPatrolReminderMonitor } from '@services/patrolReminder'
import { QueryBysMarkerFinish, QueryRolePermissionFinish } from '@utils/pubSubSubject'
import { DataName } from '@src/store'

// 统一处理登录请求
export function requestLogin(data: userApi.IReqUserLogin): Promise<userApi.IResUserLogin> {
  return new Promise((resolve) => {
    const callOpt: ICallOption = {
      OnResult: (resUserLogin: userApi.IResUserLogin) => {
        resolve(resUserLogin)
      },
      OnServerErr: (errRpc: yrpcmsg.Ymsg) => {
        log.error('Login server error', errRpc)
        let Code = 0
        if (errRpc.Optstr.includes('no rows')) {
          Code = 3
        } else if (errRpc.Optstr.includes('no such system')) {
          Code = 5
        } else if (errRpc.Optstr.includes('no such')) {
          Code = 2
        } else if (errRpc.Optstr.includes('time need update')) {
          Code = 13
        }
        const resLogin: userApi.IResUserLogin = { Code }
        resolve(resLogin)
      },
      OnLocalErr: (err: any) => {
        log.error('Login local error', err)
        const Code = 11
        const Err = err
        const resLogin: userApi.IResUserLogin = { Code, Err }
        resolve(resLogin)
      },
      OnTimeout: (v: any) => {
        log.warn('Login timeout', v)
        const Code = 12
        const Err = v
        const resLogin: userApi.IResUserLogin = { Code, Err }
        resolve(resLogin)
      },
    }
    RpcUser.Login(data, callOpt)
  })
}

let time = 5000
let reLoginHandler = 0
let closeTime = 0

function getReLoginTimeout(): number {
  // 重连间隔最大为5分钟
  const maxTime = 5 * 60 * 1000
  if (time >= maxTime) {
    time = maxTime
  } else {
    time += 1000
  }

  return time
}

async function relogin() {
  // 重新登录 1: session rid
  const LoginName: string = Store.state.SessionRID
  const LoginTimeStr: string = utcTime()
  const reqUserLogin: userApi.IReqUserLogin = {
    LoginName,
    LoginPass: encodeLoginPassword(LoginName, '', LoginTimeStr),
    LoginTimeStr,
    LoginType: 1,
    System: Store.state.System,
  }
  const resLogin = await requestLogin(reqUserLogin)
  // Code:100 为正常登录，其他为异常
  if (resLogin.Code === 100) {
    time = 5000
    // 发布登录成功事件
    StrPubSub.publish(LoginSuccess, resLogin)
    StrPubSub.publish(RefreshServerVersion)

    // 重新登录后，需要重新注册nats事件
    setTimeout(() => {
      controllerCmdProcess()
      natsSubsribeUserSettings()
      natsSubscribeCdcEvents()
    }, 0)
  } else {
    // 登录失败处理
    StrPubSub.publish(LoginFailed, resLogin)
    reLoginHandler = window.setTimeout(() => {
      relogin()
    }, getReLoginTimeout())
  }
}

rpcCon.on('onopen', () => {
  // SessionRID如果存在，则说明为重连，需要重新登录和请求数据
  if (Store.state.SessionRID) {
    window.setTimeout(() => {
      relogin()
    }, 0)
  }
})

rpcCon.on('onclose', () => {
  // webSocket已经关闭，不需要一直尝试重新登录
  clearTimeout(reLoginHandler)
  closeTime === 0 && (closeTime = Date.now())
})

function reQueryData(resLogin) {
  if (Date.now() - closeTime >= 10 * 1000) {
    // 重新请求数据前先清除本地的数据
    Store.commit(`${NS}/${CLEAN}`)
    // 重新缓存登录数据，如果没有缓存，则说明用户没有记住密码，则不需要重新缓存登录账号信息
    const loginCache: any = storage.fetch(StoreLoginInfo)
    if (loginCache) {
      StrPubSub.publish(StoreLoginInfo, {
        ...loginCache,
        SessionRID: resLogin.SessionRID,
      })
    }
    storage.save(StoreLangKey, i18n.global.locale.value)
    // 发布清除树节点的事件
    StrPubSub.publish(CleanDeviceTree)
    // 重新自动登录后，尝试删除当前的界桩的报警状态
    // 重新请求数据后，界桩数据中已经没有相关的状态
    StrPubSub.publish(CleanAllAlarmMarkerData)

    // 重置数据查询完成状态标志
    resetQueryFinishStatus()
    // 发布数据开始查询事件
    StrPubSub.publish(ReloginDataQueryStarted)

    queryBaseData()
  }
  closeTime = 0
}

// 同步服务器时间定时器句柄
let sysUTCTimeInterval = -1

function syncSysUTCTime() {
  const req: rpc.IRpcCommon = {
    System: Store.state.System,
    SessionID: Store.state.SessionRID,
  }
  const options: ICallOption = {
    OnResult: (rpcCommon: rpc.IRpcCommon) => {
      // log.info('syncSysUTCTime result rpcCommon', rpcCommon)
      Store.commit('syncSysUTCTimeDiff', { value: rpcCommon.System })
    },
    OnServerErr: (errRpc: yrpcmsg.Ymsg) => {
      log.error('Login server error', errRpc)
    },
    OnLocalErr: (err: any) => {
      log.error('Login local error', err)
    },
    OnTimeout: (v: any) => {
      log.warn('Login timeout', v)
    },
  }
  RpcUser.SysUTCTime(req, options)
}

let patrolReminderInited = false
function tryInitPatrolReminder() {
  if (patrolReminderInited) return
  if (QueryFinishStatus[DataName.BysMarker] && QueryFinishStatus[DataName.RolePermission]) {
    patrolReminderInited = true
    initPatrolReminderMonitor(!!Store.state.Settings.patrolReminder)
  }
}

StrPubSub.subscribe(QueryBysMarkerFinish, tryInitPatrolReminder)
StrPubSub.subscribe(QueryRolePermissionFinish, tryInitPatrolReminder)

StrPubSub.subscribe(LoginSuccess, (resLogin: userApi.IResUserLogin) => {
  // 发布全局登录成功事件，并更新登录状态
  Store.commit('updateLoginStatus', { value: true })
  Store.commit('updateSessionRID', { value: resLogin.SessionRID })
  Store.commit('saveUserOrgRID', { value: resLogin.UserOrgRID })
  Store.commit('saveUserRID', { value: resLogin.UserRID })
  Store.commit('syncSysUTCTimeDiff', { value: resLogin.SysUTCTime })

  reQueryData(resLogin)
  //请求推送通知的权限
  setTimeout(queryNotificationPerm, 0)
  // 每隔1小时同步一次服务器的时间
  if (sysUTCTimeInterval > -1) { clearInterval(sysUTCTimeInterval) }
  sysUTCTimeInterval = window.setInterval(syncSysUTCTime, 60 * 60 * 1000)
})

StrPubSub.subscribe(LoginFailed, (resLogin: userApi.IResUserLogin) => {
  //1:密码错误 2: 无此sid 3:此sessionn用户已经删除 4:无此用户
  const errorMessage = {
    1: i18n.global.t('message.passwordErr'),
    2: i18n.global.t('message.autoLoginExpired'),
    3: i18n.global.t('message.noSuchUser'),
    4: i18n.global.t('message.noSuchUser'),
    5: i18n.global.t('message.noSuchSys'),
    12: i18n.global.t('message.loginTimeout'),
    13: i18n.global.t('message.loginTimeError'),
  }
  Notify.create({
    type: 'negative',
    icon: 'warning',
    message: errorMessage[resLogin.Code as number] || resLogin.Err,
    position: 'top',
  })
})

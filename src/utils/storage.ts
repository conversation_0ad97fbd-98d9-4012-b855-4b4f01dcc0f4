import { LocalStorage, SessionStorage, WebStorageGetMethodReturnType, LooseDictionary } from 'quasar'

export type store = LocalStorage | SessionStorage
export type storeValue = WebStorageGetMethodReturnType | Function | LooseDictionary | any[] | null

export const Prefix = 'bys:'
export const StoreLangKey = 'locale'
export const StoreLoginInfo = 'login_info'
export const MapInfoKey='map_info'

function getStoreKey(key: string): string {
  return `${Prefix}:${key}`
}

function getStore(isLocal = true): store {
  return isLocal? LocalStorage: SessionStorage
}

export function fetch(key: string, isLocal = true): storeValue {
  return getStore(isLocal).getItem(getStoreKey(key))
}

export function save(key: string, value: storeValue, isLocal = true): void {
  getStore(isLocal).set(getStoreKey(key), value)
}

export function remove(key: string, isLocal = true): void {
  getStore(isLocal).remove(getStoreKey(key))
}

export { LocalStorage }
export { SessionStorage }

export default {
  fetch,
  save,
  remove,

  StoreLangKey,
  StoreLoginInfo,
}

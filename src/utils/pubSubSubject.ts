// Query data subject
export const QueryRolePermissionFinish = 'query-rolePermission-finish'
export const QueryUnitFinish = 'query-unit-finish'
export const QueryControllerFinish = 'query-controller-finish'
export const QueryBysMarkerFinish = 'query-bysMarker-finish'
export const QueryRoleFinish = 'query-role-finish'
export const QueryUserOrgPrivilegeFinish = 'query-userOrgPrivilege-finish'
export const QueryUserFinish = 'query-user-finish'
export const QueryMediaInfoFinish = 'query-mediaInfo-finish'
export const QueryUserRoleFinish = 'query-userRole-finish'
export const QueryOptionHistory = 'query-op-history'
export const QueryMarkerHistory = 'query-marker-history'
export const QueryMarkerUploadImageHistory = 'query-marker-upload-image-history'
export const QueryMarkerPatrolHistory = 'query-marker-patrol-history'
export const QueryControllerHistory = 'query-controller-history'
export const QueryUnknownUnitDataOnceFinish = 'query-unknown-unit-data-once-finish'
export const QueryConfigDataFinish = 'query-config-data-finish'
export const QueryPatrolStatistics = 'query-patrol-statistics'
export const ReloginDataQueryStarted = 'relogin-data-query-started'

// global data subject insert/delete/update
// org subject
export const InsertDbOrg = 'insert-dbOrg'
export const UpdateDbOrg = 'update-dbOrg'
export const DeleteDbOrg = 'delete-dbOrg'

// role
export const UpdateDbRole = 'update-dbRole'

// user
export const InsertDbUser = 'insert-dbUser'
export const UpdateDbUser = 'update-dbUser'
export const DeleteDbUser = 'delete-dbUser'

// controller
export const InsertDbController = 'insert-dbController'
export const UpdateDbController = 'update-dbController'
export const DeleteDbController = 'delete-dbController'

// bysMarker
export const InsertDbBysMarker = 'insert-dbBysMarker'
export const UpdateDbBysMarker = 'update-dbBysMarker'
export const DeleteDbBysMarker = 'delete-dbBysMarker'
export const BysMarkerAlarm = 'bysMarker-alarm'
export const UpdateMarkerForm = 'update-marker-form'
//只将倾倒报警显示到图层及tree
export const BysMarkerDumpingAlarm = 'bysMarker-alarm-to-page'
export const RemoveBysMarkerAlarm = 'remove-bysMarker-alarm'
export const MarkerHasSomeAbnormal = 'marker-has-some-abnormal'

// media
export const InsertMediaInfo = 'insert-dbMediaInfo'
export const UpdateMediaInfo = 'update-dbMediaInfo'
export const DeleteMediaInfo = 'delete-dbMediaInfo'

// user org privilege
export const InsertUserOrgPrivilege = 'insert-dbUserPrivilege'
export const UpdateUserOrgPrivilege = 'update-dbUserPrivilege'
export const DeleteUserOrgPrivilege = 'delete-dbUserPrivilege'

// map subject
export const BysMarkerForceUpdate = 'bysMarker-update-force'
export const ControllerForceUpdate = 'controller-update-force'
export const ToggleMapStyleFinish = 'toggle-map-style-finish'
export const FullscreenIsActive = 'fullscreen-isActive'

// login
export const ReLogin = 're-login'
export const LoginSuccess = 'login-success'
export const LoginFailed = 'login-failed'

// command
export const ControllerOnline = 'controller-online'
export const ControllerOffline = 'controller-offline'
export const ControllerOfflinePanic = 'controller-offline-panic'
export const ControllerHeartbeat = 'controller-heartbeat'
export const ControllerAlarm = 'controller-alarm'
export const DeviceReportInfo = 'device-report-info'
export const DeviceAlarmTest = 'device-alarm-test'
export const removeBysMarkerAlarmCmd = 'remove-bysMarker-alarm-cmd'
export const removeBysMarkerAlarmCmdAuto = 'remove-bysMarker-alarm-cmd-auto'
export const UpdateBysMarkerTreeNodeBattery = 'update-bysMarker-tree-node-battery'

// other subject
export const CleanDeviceTree = 'clean-device-tree'
export const ToggleI18nLang = 'toggle-i18n-lang'
export const OpenMenu = 'open-menu'
export const PlayAlarmAudio = 'play-alarm-audio'
export const StopAlarmAudio = 'stop-alarm-audio'
export const RefreshServerVersion = 'refresh-server-version'
export const InitDeviceTreeFinish = 'init-device-tree-finish'
export const SocketClosed = 'webSocket-closed'
export const SortDeviceTree = 'sort-device-tree'
export const Jump2Node = 'jump-to-node'

//baidu map
export const WakeupBaiduMap = 'wake-up-baiduMap'
export const ShowTrackHistory = 'show-track-history'
export const ShowNotInstalledMarker = 'show-not-installed-marker'

export const ExitApp = 'exit-app'

//controller statue changed
export const ControllerStateChanged = 'controller-state-changed'
export const CleanAllAlarmMarkerData = 'clean-all-alarm-marker-data'

//界桩抓拍上传
export const MarkerPhotographUpload = 'marker-photograph-upload'
// 系统内iccid与上报界桩的iccid重复
export const NotifyICCIDAlreadyExist = 'notify-iccid-already-exist'
// 系统内imie与上线界桩的imei重复
export const NotifyICCIDAndIMEIConflict = 'notify-iccid-imei-conflict'

// 巡查提醒开关变更
export const PatrolReminderChanged = 'patrol-reminder-changed'

import { toGCJ02 } from '@utils/gcoord'
import { debounce } from 'lodash'

// GPS位置数据接口
export interface LocationData {
  longitude: number
  latitude: number
  accuracy: number
  timestamp: number
}

// 位置管理器配置
export interface LocationManagerConfig {
  // GPS精度阈值（米），超过此值的定位数据将被过滤
  accuracyThreshold: number
  // 最小移动距离（米），小于此距离的位置变化将被忽略
  minMoveDistance: number
  // 位置平滑窗口大小
  smoothingWindow: number
  // 防抖延迟（毫秒）
  debounceDelay: number
  // 最大缓存时间（毫秒）
  maxCacheAge: number
}

// 默认配置
const DEFAULT_CONFIG: LocationManagerConfig = {
  accuracyThreshold: 50, // 50米精度阈值
  minMoveDistance: 5,    // 5米最小移动距离
  smoothingWindow: 3,    // 3个点的平滑窗口
  debounceDelay: 1000,   // 1秒防抖
  maxCacheAge: 30000     // 30秒缓存
}

// 优化的定位选项
export const OPTIMIZED_POSITION_OPTIONS: PositionOptions = {
  enableHighAccuracy: true,
  timeout: 15000,        // 增加超时时间
  maximumAge: 10000      // 允许使用10秒内的缓存位置
}

export class LocationManager {
  private config: LocationManagerConfig
  private locationHistory: LocationData[] = []
  private lastValidLocation: LocationData | null = null
  private debouncedCallback: ((location: LocationData) => void) | null = null

  constructor(config: Partial<LocationManagerConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config }
  }

  // 设置位置更新回调（带防抖）
  setLocationCallback(callback: (location: LocationData) => void) {
    this.debouncedCallback = debounce(callback, this.config.debounceDelay)
  }

  // 处理新的位置数据
  processLocation(position: GeolocationPosition): LocationData | null {
    const rawLocation: LocationData = {
      longitude: position.coords.longitude,
      latitude: position.coords.latitude,
      accuracy: position.coords.accuracy,
      timestamp: position.timestamp
    }

    // 1. 精度过滤
    if (!this.isAccuracyAcceptable(rawLocation)) {
      console.warn('Location filtered due to poor accuracy:', rawLocation.accuracy)
      return null
    }

    // 2. 移动距离过滤
    if (!this.isMovementSignificant(rawLocation)) {
      console.log('Location filtered due to insufficient movement')
      return null
    }

    // 3. 添加到历史记录
    this.addToHistory(rawLocation)

    // 4. 平滑处理
    const smoothedLocation = this.smoothLocation()

    // 5. 坐标转换
    const [gcjLon, gcjLat] = toGCJ02([smoothedLocation.longitude, smoothedLocation.latitude])
    const finalLocation: LocationData = {
      ...smoothedLocation,
      longitude: gcjLon,
      latitude: gcjLat
    }

    // 6. 更新最后有效位置
    this.lastValidLocation = finalLocation

    // 7. 触发回调
    if (this.debouncedCallback) {
      this.debouncedCallback(finalLocation)
    }

    return finalLocation
  }

  // 检查GPS精度是否可接受
  private isAccuracyAcceptable(location: LocationData): boolean {
    return location.accuracy <= this.config.accuracyThreshold
  }

  // 检查移动距离是否显著
  private isMovementSignificant(location: LocationData): boolean {
    if (!this.lastValidLocation) {
      return true // 第一个位置总是显著的
    }

    const distance = this.calculateDistance(
      this.lastValidLocation.latitude,
      this.lastValidLocation.longitude,
      location.latitude,
      location.longitude
    )

    return distance >= this.config.minMoveDistance
  }

  // 添加位置到历史记录
  private addToHistory(location: LocationData) {
    this.locationHistory.push(location)
    
    // 清理过期数据
    const cutoffTime = Date.now() - this.config.maxCacheAge
    this.locationHistory = this.locationHistory.filter(loc => loc.timestamp > cutoffTime)
    
    // 限制历史记录大小
    if (this.locationHistory.length > this.config.smoothingWindow) {
      this.locationHistory = this.locationHistory.slice(-this.config.smoothingWindow)
    }
  }

  // 位置平滑处理
  private smoothLocation(): LocationData {
    if (this.locationHistory.length === 0) {
      throw new Error('No location history available')
    }

    if (this.locationHistory.length === 1) {
      return this.locationHistory[0]
    }

    // 使用加权平均进行平滑，最新的位置权重更高
    let totalWeight = 0
    let weightedLon = 0
    let weightedLat = 0
    let bestAccuracy = Infinity
    let latestTimestamp = 0

    this.locationHistory.forEach((location, index) => {
      const weight = index + 1 // 权重递增
      totalWeight += weight
      weightedLon += location.longitude * weight
      weightedLat += location.latitude * weight
      bestAccuracy = Math.min(bestAccuracy, location.accuracy)
      latestTimestamp = Math.max(latestTimestamp, location.timestamp)
    })

    return {
      longitude: weightedLon / totalWeight,
      latitude: weightedLat / totalWeight,
      accuracy: bestAccuracy,
      timestamp: latestTimestamp
    }
  }

  // 计算两点间距离（米）
  private calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
    const R = 6371e3 // 地球半径（米）
    const φ1 = lat1 * Math.PI / 180
    const φ2 = lat2 * Math.PI / 180
    const Δφ = (lat2 - lat1) * Math.PI / 180
    const Δλ = (lon2 - lon1) * Math.PI / 180

    const a = Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
              Math.cos(φ1) * Math.cos(φ2) *
              Math.sin(Δλ / 2) * Math.sin(Δλ / 2)
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))

    return R * c
  }

  // 获取最后有效位置
  getLastValidLocation(): LocationData | null {
    return this.lastValidLocation
  }

  // 清理历史数据
  clearHistory() {
    this.locationHistory = []
    this.lastValidLocation = null
  }

  // 更新配置
  updateConfig(newConfig: Partial<LocationManagerConfig>) {
    this.config = { ...this.config, ...newConfig }
  }
}

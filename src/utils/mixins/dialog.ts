import { defineComponent } from 'vue'
import { StrPubSub } from 'ypubsub'
import Modal from '@src/components/modal/Modal.vue'

export default defineComponent({
  data() {
    return {
      visible: false,
      maximized: false,
    }
  },
  beforeMount(): void {
    StrPubSub.subscribe(`${this.$options.name}-visible`, (visible: boolean) => {
      this.visible = visible
    })
  },
  beforeUnmount(): void {
    StrPubSub.unsubscribe(`${this.$options.name}-visible`)
  },
  components: {
    Modal,
  },
})

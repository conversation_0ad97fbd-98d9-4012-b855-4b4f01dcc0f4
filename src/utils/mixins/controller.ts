import { defineComponent } from 'vue'
import { ControllerAndUpdateCmd, FormRuleItem, FormRules } from '@utils/bysdb.type'
import globalConfig from '@src/config'
import maplibreGl from 'maplibre-gl'
import { IDRangeRule, isLat, isLon, maxLength, required, Sint32Limit } from '@utils/validation'
import { bysdb } from '@ygen/bysdb'
import { GET_DATA, NS } from '@store/data/methodTypes'
import { DataName } from '@src/store'
import { Controller } from '@src/services/dataStore'
import { ControllerTypes, outputDBError } from '@utils/common'
import { cloneDeep } from 'lodash'
import { getGCJ02LngLat, setGCJ02LngLat } from '@utils/gcoord'
import { ICallOption } from 'jsyrpc'
import { crud } from '@ygen/crud'
import { yrpcmsg } from 'yrpcmsg'
import log from '@utils/log'
import { StrPubSub } from 'ypubsub'
import { UpdateDbController } from '@utils/pubSubSubject'
import { PrpcDbController } from '@ygen/bysdb.rpc.yrpc'
import { i18n } from '@boot/i18n'
import { Dialog } from 'quasar'

const controllerError = {
  dbcontroller_orgrid_fkey: i18n.global.t('dataBaseError.parUnitErr'),
  controllerhwid_key: i18n.global.t('dataBaseError.controllerHwidErr'),
  controller_pkey: i18n.global.t('dataBaseError.controllerPKeyErr'),
  controllerno_key: i18n.global.t('dataBaseError.controllerNoErr'),
  'No operation permission': i18n.global.t('dataBaseError.permissionErr'),
  dbbysmarker_controllerrid_fkey: i18n.global.t('dataBaseError.childMarkerExist'),
}

export default defineComponent({
  data() {
    return {
      currentRow: {} as bysdb.IDbController,
    }
  },
  computed: {
    data(): bysdb.IDbController[] {
      const data = this.$store.getters[`${NS}/${GET_DATA}`](DataName.Controller)
      return Controller.sortBy(data, { sortBy: 'ControllerHWID' })
    },
    availableParentChannelCounts() {
      const parent = this.data.find(item => item.RID === this.currentRow.ParentRID)
      return parent ? (parent.ChannelCount ?? 1) : 1
    },
    AllControllerOptions() {
      return this.data
        .map((data: bysdb.IDbController) => {
          return {
            label: data.ControllerNo,
            value: data.RID,
            ControllerType: data.ControllerType,
            ControllerHWID: data.ControllerHWID,
          }
        })
    },
    rules(): FormRules {
      const map = globalConfig.map as maplibreGl.Map
      const maxLevel = ~~map.getMaxZoom()
      const minLevel = ~~map.getMinZoom()
      const _required = (val) => required(val) || this.$t('rules.required')
      const _maxLength = (val) => maxLength(val, 16) || this.$t('rules.maxLength', { len: 16 })
      const ParentRIDRules: FormRuleItem[] = []
      if (this.currentRow.ControllerType === 1) {
        ParentRIDRules.push(val => _required(val))
      }

      const rules: FormRules = {
        ControllerNo: [
          val => _required(val),
          val => _maxLength(val),
        ],
        OrgRID: [
          val => _required(val),
        ],
        ControllerType: [
          val => _required(val),
        ],
        ParentRID: ParentRIDRules,
        DefaultNetworkType: [],
        Lon: [
          val => _required(val),
          val => isLon(val) || this.$t('rules.mustBeLon'),
        ],
        Lat: [
          val => _required(val),
          val => isLat(val) || this.$t('rules.mustBeLat'),
        ],
        ControllerHWID: [
          val => _required(val),
          val => IDRangeRule(val) || this.$t('rules.availableRange', { min: 0, max: Sint32Limit }),
          val => !this.AllControllerOptions.some(opt => {
            // 控制器ID唯一，如果在控制器中查找到相同的ID，且不是当前编辑的数据，则验证不通过
            if (this.currentRow) {
              return opt.ControllerHWID === val && opt.value !== this.currentRow.RID
            }
            return opt.ControllerHWID === val
          }) || this.$t('rules.IDAlreadyExists'),
        ],
        ChannelCount: [
          val => _required(val),
        ],
        ParentChannelCount: [],
        MapShowLevel: [
          val => _required(val),
          val => (val >= minLevel && val <= maxLevel) ||
            this.$t('rules.availableRange', { min: minLevel, max: maxLevel }),
        ],
        ControllerDescription: [
          val => maxLength(val, 4096) || this.$t('rules.maxLength', { len: 4096 }),
        ],
      }

      if (this.currentRow.ParentRID) {
        rules.ParentChannelCount = [
          val => _required(val),
          val => (val >= 1 && val <= this.availableParentChannelCounts) ||
            this.$t('rules.availableRange', { min: 1, max: this.availableParentChannelCounts }),
        ]
      }

      return rules
    },
    ControllerTypeOptions() {
      return [
        { label: this.$t('form.repeater'), value: ControllerTypes.relayController },
        { label: this.$t('form.baseStation'), value: ControllerTypes.baseController },
      ]
    },
    NetworkType() {
      return [
        { label: this.$t('form.limitedNetwork'), value: 1 },
        { label: this.$t('form.fourG'), value: 2 },
        { label: this.$t('form.fsk'), value: 3 },
      ]
    },
  },
  methods: {
    // 控制器名称变更则返回true
    checkControllerNoChange(data: bysdb.IDbController): boolean {
      const RID = data.RID as string
      const oldData = cloneDeep(Controller.getData(RID) as ControllerAndUpdateCmd)
      return oldData.ControllerNo !== data.ControllerNo
    },
    // 提示用户要同步更改控制器硬件配置
    warningControllerNoChange(data: bysdb.IDbController): Promise<boolean> {
      return new Promise((resolve) => {
        if (this.checkControllerNoChange(data)) {
          // 提示如果修改控制器名称，需要同步修改控制器硬件配置，使用新的名称进行登录
          Dialog.create({
            // title: this.$t('download.checkUpdate') as string,
            message: this.$t('message.modifyControllerNoWarn'),
            class: 'z-top',
            ok: true,
            cancel: true,
            persistent: true,
          }).onOk(() => {
            return resolve(true)
          }).onCancel(() => {
            return resolve(false)
          }).onDismiss(() => {
            return resolve(false)
          })
        } else {
          return resolve(true)
        }
      })
    },
    processControllerChannelCannotRepeated(reason: string): string {
      if (reason.includes('uidxdbcontrollerparent')) {
        return this.$t('dataBaseError.controllerChannelCannotRepeated')
      }
      return ''
    },
    updateData(data: bysdb.IDbController): Promise<boolean> {
      return new Promise((resolve, reject) => {
        const RID = data.RID as string
        const oldData = cloneDeep(Controller.getData(RID) as ControllerAndUpdateCmd)
        const options: ICallOption = {
          OnResult: (res: crud.IDMLResult, rpcCmd: yrpcmsg.Ymsg, meta?: yrpcmsg.IGrpcMeta) => {
            log.info('IDbController Update result', res, rpcCmd, meta)
            if (res.AffectedRow === 0) {
              reject('failed')
            } else {
              resolve(true)
              // 同步到数据容器对象
              const newController = new bysdb.DbController(data) as ControllerAndUpdateCmd
              newController.controllerState = cloneDeep(oldData.controllerState ?? {})
              setGCJ02LngLat(newController, getGCJ02LngLat(data))
              Controller.setData(RID, newController)
                .setDataIndex(newController.ControllerHWID + '', RID)
              // 发布更新数据通知
              StrPubSub.publish(UpdateDbController, newController, oldData)
            }
          },
          OnServerErr: (errRpc: yrpcmsg.Ymsg) => {
            log.error('IDbController Update server error', errRpc)
            // 处理服务器响应的错误
            let reason = outputDBError(controllerError, errRpc.Optstr)
            // 同一基站下控制器通道不能重复
            const r = this.processControllerChannelCannotRepeated(errRpc.Optstr)
            if (r) { reason = r }

            reject(reason)
          },
          OnLocalErr: (err: any) => {
            log.error('IDbController Update local error', err)
            reject('local')
          },
          OnTimeout: (v: any) => {
            log.warn('IDbController Update timeout', v)
            const options = {
              action: this.$t('common.modify'),
              name: data.ControllerNo,
            }
            const reason = this.$t('message.timeout', options)
            reject(reason)
          },
        }
        PrpcDbController.Update(data, options)
      })
    },
  },
})

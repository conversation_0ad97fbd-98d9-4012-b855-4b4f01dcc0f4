import { defineComponent } from 'vue'
import { DefUuid } from '@src/config'
import { GET_DATA, NS } from '@src/store/data/methodTypes'
import { DataName } from '@src/store/data'
import { org } from '@ygen/org'

import { sortData } from '@utils/permission'
import QueryHistory from '@components/queryHistory/QueryHistory.vue'

export default defineComponent({
  data() {
    return {
      orgFilter: '',
    }
  },
  computed: {
    orgData(): Array<org.IDbOrg> {
      const dataList = this.$store.getters[`${NS}/${GET_DATA}`](DataName.Unit).filter(item => item.RID !== DefUuid)
      return sortData<org.IDbOrg>(dataList, {
        descending: this.$store.state.Settings.descending,
        prop: 'SortValue',
      })
    },
    orgRIDOptions(): Array<{ [key: string]: any }> {
      const options = this.orgData.map((data: org.IDbOrg) => {
        return {
          label: data.ShortName,
          value: data.RID,
        }
      })

      if (!this.orgFilter) { return options }

      return options.filter(option => {
        const needle = this.orgFilter.toLowerCase()
        return option.label.toLowerCase().includes(needle)
      })
    },
  },
  components: {
    QueryHistory,
  },
})

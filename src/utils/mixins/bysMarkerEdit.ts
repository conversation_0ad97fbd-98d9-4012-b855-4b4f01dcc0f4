import { defineComponent } from 'vue'
import { doc } from '@ygen/bys.api'
import { ICallOption, TrpcMeta } from 'jsyrpc'
import { yrpcmsg } from 'yrpcmsg'
import log from '@utils/log'
import { RpcBysApi } from '@ygen/bys.api.yrpc'
import { GET_DATA, NS } from '@store/data/methodTypes'
import { DataName } from '@src/store'
import { bysdb } from '@ygen/bysdb'
import { getGCJ02LngLat, setGCJ02LngLat } from '@utils/gcoord'
import { crud } from '@ygen/crud'
import { cloneDeep } from 'lodash'
import { BysMarker } from '@services/dataStore'
import { StrPubSub } from 'ypubsub'
import { BysMarkerForceUpdate, UpdateDbBysMarker } from '@utils/pubSubSubject'
import { PrpcDbBysMarker } from '@ygen/bysdb.rpc.yrpc'
import { MarkerType, outputDBError, rawObjectProps, checkIs4GMarker } from '@utils/common'
import { i18n } from '@boot/i18n'
import globalConfig from '@src/config'
import maplibreGl from 'maplibre-gl'
import {
  genRangeRule,
  IDRangeRule,
  isLat,
  isLon,
  isValidTime,
  maxLength,
  required,
  isDomain,
  Sint32Limit, isIPValid, iccidReg, isValidDay,
  validateIMEI,
} from '@utils/validation'
import { sysUtcTime } from '@utils/dayjs'
import { BysMarkerAndUpdateInfo, FormRules } from '@utils/bysdb.type'
import { pakoDeflate, toBase64 } from '@utils/crypto'
import { useBysMarkerData } from '@utils/vueUse/bysMarkerData'
import { check4GMarkerIsNeedUpdateParamTime } from '@app/src/services/controller'

const { bysMarkerData } = useBysMarkerData()

export default defineComponent({
  computed: {
    bysMarkerError() {
      return {
        controllerrid_fkey: i18n.global.t('dataBaseError.parControllerErr'),
        orgrid_fkey: i18n.global.t('dataBaseError.parUnitErr'),
        controllerrid_controllerchannel_markerqueueno_key: i18n.global.t('dataBaseError.queueNoErr'),
        markerhwid_key: i18n.global.t('dataBaseError.markerHwidErr'),
        markerno_key: i18n.global.t('dataBaseError.markerNoErr'),
        markerredlineno_key: i18n.global.t('dataBaseError.redLineNoErr'),
        'No operation permission': i18n.global.t('dataBaseError.permissionErr'),
      }
    },
    bysMarkerData() {
      return bysMarkerData.value
    },
    allMarkerICCID() {
      let iccids: { RID: any; ICCID: any }[] = []
      bysMarkerData.value.forEach(item => {
        if (checkIs4GMarker(item.MarkerType) && item.ICCID) {
          iccids.push({
            RID: item.RID,
            ICCID: item.ICCID,
          })
        }
      })
      return iccids
    },
    allMarkerIMEI() {
      let iemis: { RID: string; IMEI: string }[] = []
      bysMarkerData.value.forEach((item: bysdb.DbBysMarker) => {
        if (checkIs4GMarker(item.MarkerType) && item.IMEI) {
          iemis.push({
            RID: item.RID,
            IMEI: item.IMEI,
          })
        }
      })
      return iemis
    },
    MarkerTypeOptions() {
      return [
        { label: this.$t('form.normalMarkers'), value: MarkerType.Regular },
        { label: this.$t('form.4GMarkers'), value: MarkerType.Net4G },
        { label: this.$t('form.4GMarkersPro'), value: MarkerType.Net4GPro },
      ]
    },
    MarkerModelOptions() {
      return [
        { label: this.$t('form.long'), value: 1 },
        { label: this.$t('form.short'), value: 2 },
      ]
    },
    controllerData() {
      return this.$store.getters[`${NS}/${GET_DATA}`](DataName.Controller)
    },
    AllMarkerOptions() {
      return this.bysMarkerData.map(
        (data: bysdb.IDbBysMarker) => {
          return {
            label: data.MarkerNo,
            value: data.RID,
            MarkerHWID: data.MarkerHWID,
          }
        },
      )
    },
    paramsWith4gAddrRules(): FormRules {
      const _required = (val) => required(val) || this.$t('rules.required')
      const _port = genRangeRule(1, 0xFFFF)
      return {
        addr: [
          val => _required(val),
          val => (isIPValid(val) || isDomain(val)) || this.$t('rules.mustBeIpOrDomain')
        ],
        port: [
          val => _required(val),
          val => _port(val) || this.$t('rules.availableRange', { min: 1, max: 0xFFFF }),
        ]
      }
    },
    formRules(): Record<number, FormRules> {
      const map = globalConfig.map as maplibreGl.Map
      const maxLevel = ~~map.getMaxZoom()
      const minLevel = ~~map.getMinZoom()
      const _required = (val) => required(val) || this.$t('rules.required')
      const _maxLength = (val) => maxLength(val, 16) || this.$t('rules.maxLength', { len: 16 })
      const slope = genRangeRule(15, 90)
      const distance = genRangeRule(10, 100)
      const vibrationAmplitude = genRangeRule(1, 10)
      const vibrationDuration = genRangeRule(1, 60)
      // const infraredLimlit = genRangeRule(1, 10)
      const deLaySlTime = genRangeRule(10, 60)
      const DebugModeTime = genRangeRule(120, 240)
      const AlarmInterval = genRangeRule(10, 60)
      const alarmNum = genRangeRule(10, 60)
      const queueNoInterval = genRangeRule(0, 0xFFFF)
      const markerQueueInterval = genRangeRule(20, 0xFF)
      const markerDayInterval = genRangeRule(1, 24)

      const sharedRules = {
        MarkerNo: [
          val => _required(val),
          val => _maxLength(val),
        ],
        OrgRID: [
          val => _required(val),
        ],
        MarkerHWID: [
          val => val !== null || this.$t('rules.required'),
          val => IDRangeRule(val) || this.$t('rules.availableRange', { min: 1, max: Sint32Limit }),
          val => !this.AllMarkerOptions.some(opt => {
            // 控制器ID唯一，如果在控制器中查找到相同的ID，且不是当前编辑的数据，则验证不通过
            if (this.currentRow) {
              return opt.MarkerHWID === val && opt.value !== this.currentRow.RID
            }
            return opt.MarkerHWID === val
          }) || this.$t('rules.IDAlreadyExists'),
        ],
        MarkerModel: [
          val => val !== null || this.$t('rules.required'),
        ],
        MarkerType: [
          val => val !== null || this.$t('rules.required'),
        ],
        Lon: [
          val => _required(val),
          val => isLon(val) || this.$t('rules.mustBeLon'),
        ],
        Lat: [
          val => _required(val),
          val => isLat(val) || this.$t('rules.mustBeLat'),
        ],
        MapShowLevel: [
          val => _required(val),
          val => (val >= minLevel && val <= maxLevel) ||
            this.$t('rules.availableRange', { min: minLevel, max: maxLevel }),
        ],
        MarkerDescription: [
          val => maxLength(val, 4096) || this.$t('rules.maxLength', { len: 4096 }),
        ],
      }
      const regularRules = {
        ControllerRID: [
          val => _required(val),
        ],
        ControllerChannel: [
          val => _required(val),
        ],
        MarkerChannel: [
          val => _required(val),
        ],
        MarkerQueueNo: [
          val => _required(val),
          val => queueNoInterval(val) || this.$t('rules.availableRange', { min: 0, max: 0xFFFF }),
        ],
        MarkerDayInterval: [
          val => _required(val),
          val => markerDayInterval(val) || this.$t('rules.availableRange', { min: 1, max: 24 }),
        ],
        MarkerQueueInterval: [
          val => _required(val),
          val => markerQueueInterval(val) || this.$t('rules.availableRange', { min: 20, max: 0xFF }),
        ],
        MarkerEmergentInterval: [
          val => _required(val),
          val => markerQueueInterval(val) || this.$t('rules.availableRange', { min: 20, max: 0xFF }),
        ],
        MarkerWakeupBaseTime: [
          val => _required(val),
          val => isValidTime(val) || this.$t('rules.invalidTime'),
        ],
      }
      const fourGRules = {
        timer: [
          val => _required(val),
          val => markerDayInterval(val) || this.$t('rules.availableRange', { min: 1, max: 24 }),
        ],
        n1: [
          val => _required(val),
          val => alarmNum(val) || this.$t('rules.availableRange', { min: 10, max: 60 }),
        ],
        t3: [
          val => _required(val),
          val => AlarmInterval(val) || this.$t('rules.availableRange', { min: 5, max: 180 }),
        ],
        t2: [
          val => _required(val),
          val => DebugModeTime(val) || this.$t('rules.availableRange', { min: 30, max: 300 }),
        ],
        t1: [
          val => _required(val),
          val => deLaySlTime(val) || this.$t('rules.availableRange', { min: 5, max: 180 }),
        ],
        // infrared: [
        //   val => _required(val),
        //   val => infraredLimlit(val) || this.$t('rules.availableRange', { min: 1, max: 10 }),
        // ],
        duration: [
          val => _required(val),
          val => vibrationDuration(val) || this.$t('rules.availableRange', { min: 1, max: 60 }),
        ],
        amplitude: [
          val => _required(val),
          val => vibrationAmplitude(val) || this.$t('rules.availableRange', { min: 10, max: 10 }),
        ],
        dirft: [
          val => _required(val),
          val => distance(val) || this.$t('rules.availableRange', { min: 10, max: 100 }),
        ],
        attitude: [
          val => _required(val),
          val => slope(val) || this.$t('rules.availableRange', { min: 15, max: 90 }),
        ],
        // 表单的域名/ip地址和端口分开配置处理，在this.paramsWith4gAddrRules中的addr和port进行校验
        // 此处的addr规则用于导入界桩数据时，需使用rules的key（addr）与界桩数据中的row.MarkerSettings.addr进行校验
        addr: [
          val => _required(val),
          val => {
            const addrRules = this.paramsWith4gAddrRules.addr
            const portRules = this.paramsWith4gAddrRules.port
            const addrAndPort = val.split(':')
            if (addrAndPort.length !== 2) {
              return this.$t('form.addrRulesFail')
            }
            const [a, p] = addrAndPort
            for (let i = 0; i < addrRules.length; i++) {
              let rule = addrRules[i]
              let ruleRes = rule(a)
              if (ruleRes !== true) {
                return ruleRes
              }
            }
            for (let i = 0; i < portRules.length; i++) {
              let rule = portRules[i]
              let ruleRes = rule(Number(p))
              if (ruleRes !== true) {
                return ruleRes
              }
            }
            return true
          }
        ],
        ICCID: [
          val => !val || (val?.length ?? 0) === 19 || this.$t('form.iccidRuleCheckFail'),
          val => !val || iccidReg(val) || this.$t('form.iccidRuleCheckFail'),
          val => !val || !this.allMarkerICCID.some(item => {
            // iccid唯一，如果不是当前编辑的数据，则验证不通过
            if (this.currentRow) {
              return item.ICCID === val && item.RID !== this.currentRow.RID
            }
            return item.ICCID === val
          }) || i18n.global.t('form.iccidExists'),
        ],
        IMEI: [
          val => !val || (val?.length ?? 0) === 15 || i18n.global.t('rules.validIMEILength'),
          val => !val || validateIMEI(val) || i18n.global.t('rules.validIMEI'),
          val => !val || !this.allMarkerIMEI.some(item => {
            // imei唯一，如果不是当前编辑的数据，则验证不通过
            if (this.currentRow) {
              return item.IMEI === val && item.RID !== this.currentRow.RID
            }
            return item.IMEI === val
          }) || i18n.global.t('rules.IMEIExists')
        ],
        ExpirationDate: this.expirationDateRules[0],
      }
      const RulesMap = {
        0: Object.assign(regularRules, sharedRules),
        1: Object.assign(fourGRules, sharedRules),
      }
      return RulesMap
    },
    rules(): FormRules {
      return this.formRules[this.currentRow.MarkerType ?? 0]
    },
    expirationDateRules(): FormRules {
      const _required = (val) => required(val) || this.$t('rules.required')
      return {
        0: [
          val => !val || isValidDay(val?.split(' ')[0] ?? '') || this.$t('form.expirationDateFail'),
        ],
        1: [
          val => _required(val),
          val => !val || isValidDay(val?.split(' ')[0] ?? '') || this.$t('form.expirationDateFail'),
        ]
      }
    },
    redLineLinksRules() {
      const _required = (val) => required(val) || this.$t('rules.required')
      return [
        val => _required(val),
      ]
    },
  },
  methods: {
    updateData(data: bysdb.IDbBysMarker): Promise<boolean> {
      return new Promise((resolve, reject) => {
        const RID = data.RID as string
        const oldData = cloneDeep(BysMarker.getData(RID)) as BysMarkerAndUpdateInfo
        const needUpdateParamTime = check4GMarkerIsNeedUpdateParamTime(data, oldData)
        if (needUpdateParamTime) {
          // 更新界桩参数时间
          data.MarkerParamTime = sysUtcTime()
        }
        const d = bysdb.DbBysMarker.encode(oldData).finish()
        const meta = new TrpcMeta()
        meta.SetMeta('old-data', toBase64(pakoDeflate(d)))
        const options: ICallOption = {
          OnResult: (res: crud.IDMLResult, rpcCmd: yrpcmsg.Ymsg, meta?: yrpcmsg.IGrpcMeta) => {
            log.info('IDbBysMarker Update result', res, rpcCmd, meta)
            if (res.AffectedRow === 0) {
              reject('failed')
            } else {
              resolve(true)
              // 同步到数据容器对象
              const newMarker = new bysdb.DbBysMarker(data) as BysMarkerAndUpdateInfo
              const partdata = {
                isAlreadyRemoveAlarm: oldData.isAlreadyRemoveAlarm,
                removeAlarm: oldData.removeAlarm,
              }
              if (oldData.updateInfo) {
                Object.assign(partdata, {
                  updateInfo: rawObjectProps(oldData.updateInfo)
                })
              }
              // 只有4g界桩才需要拷贝旧的数据
              if (newMarker.MarkerType === oldData.MarkerType && checkIs4GMarker(oldData.MarkerType)) {
                Object.assign(partdata, {
                  // 针对4g界桩新添加字段属性
                  onlineInfo: oldData.onlineInfo,
                  markerInfo: oldData.markerInfo,
                })
              }
              setGCJ02LngLat(newMarker, getGCJ02LngLat(data))
              BysMarker.setData(RID, newMarker)
                .setPartData(RID, partdata)
                .setDataIndex(newMarker.MarkerHWID + '', RID)
              // 发布更新数据通知
              StrPubSub.publish(UpdateDbBysMarker, newMarker, oldData)
              StrPubSub.publish(BysMarkerForceUpdate, newMarker.RID)
            }
          },
          OnServerErr: (errRpc: yrpcmsg.Ymsg) => {
            log.error('IDbBysMarker Update server error', errRpc)
            // 处理服务器响应的错误
            let reason = outputDBError(this.bysMarkerError, errRpc.Optstr)
            reject(reason)
          },
          OnLocalErr: (err: any) => {
            log.error('IDbBysMarker Update local error', err)
            reject('local')
          },
          OnTimeout: (v: any) => {
            log.warn('IDbBysMarker Update timeout', v)
            const options = {
              action: this.$t('common.modify'),
              name: data.MarkerNo,
            }
            const reason = this.$t('message.timeout', options)
            reject(reason)
          },
          rpcMeta: meta,
        }
        PrpcDbBysMarker.Update(data, options)
      })
    },
    markerQueueNoChange(val, isDefault = true) {
      // 不在有效的范围内
      if (!(val >= 1 && val <= 256)) {
        return
      }
      const data: doc.IMarkerQueueNoInfo = {
        ControllerRID: this.currentRow.ControllerRID,
        ControllerChannelNo: this.currentRow.ControllerChannel,
        QueueNoAvailable: val,
      }
      if (!data.ControllerRID || !data.ControllerChannelNo || !data.QueueNoAvailable) {
        return
      }
      const options: ICallOption = {
        OnResult: (res: doc.IMarkerQueueNoInfo) => {
          // 过滤与请求参数不一致的结果
          if (res.ControllerRID !== this.currentRow.ControllerRID
            || res.ControllerChannelNo !== this.currentRow.ControllerChannel
          ) {
            return
          }
          if (this.currentRow.MarkerQueueNo !== res.Result && isDefault) {
            this.$q.notify({
              message: this.$t('form.availableQueueNo') + res.Result + '',
              type: 'warning',
              position: 'top',
            })
          }
          this.currentRow.MarkerQueueNo = res.Result
          this.$refs.queueNoInput?.validate()
        },
        OnServerErr: (errRpc: yrpcmsg.Ymsg) => {
          log.error('query IMarkerQueueNoInfo server error', errRpc)
        },
        OnLocalErr: (err: any) => {
          log.error('query IMarkerQueueNoInfo local error', err)
        },
        OnTimeout: (v: any) => {
          log.warn('query IMarkerQueueNoInfo timeout', v)
        },
      }
      RpcBysApi.QueryMarkerQueueNo(data, options)
    },
  },
})

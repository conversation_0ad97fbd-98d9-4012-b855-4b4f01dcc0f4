import { defineComponent } from 'vue'
import { DefUuid } from '@src/config'
import { GET_DATA, NS } from '@src/store/data/methodTypes'
import { DataName } from '@src/store/data'
import modal from '@src/components/modal'
import DataEdit from '@src/components/dataEdit'
import { fixedNumber } from '@utils/gcoord'
import { unknownUnitData } from '@src/services/dataStore'
import { org } from '@ygen/org'
import { StrPubSub } from 'ypubsub'
import { QueryUnknownUnitDataOnceFinish } from '@utils/pubSubSubject'
import PermissionMixin from '@utils/mixins/permission'
import { FormRuleItemFunc, FormRules } from '@utils/bysdb.type'
import { testPattern } from 'quasar/src/utils/patterns.js'
import { sortData } from '@utils/permission'

export default defineComponent({
  mixins: [PermissionMixin],
  data() {
    return {
      parentFilter: '',
    }
  },
  computed: {
    parentOptions(): Array<{ [key: string]: any }> {
      return this.unitData
        .filter(item => item.RID !== DefUuid)
        .map(data => {
          return {
            label: data.RID === DefUuid ? this.$t('form.default') : data.ShortName,
            value: data.RID,
          }
        })
        .filter(option => {
          const needle = this.parentFilter.toLowerCase()
          return option.label.toLowerCase().includes(needle)
        })
    },
    unitData(): Array<org.IDbOrg> {
      const dataList = this.$store.getters[`${NS}/${GET_DATA}`](DataName.Unit).filter(item => item.RID !== DefUuid)
      return sortData<org.IDbOrg>(dataList, {
        descending: this.$store.state.Settings.descending,
        prop: 'SortValue',
      })
    },
    unknownUnitData(): Array<org.IDbOrg> {
      return unknownUnitData ?? []
    },
  },
  methods: {
    filterParent(val: string, update: Function) {
      this.parentFilter = val
      update()
    },
    coverPropToNumber(data: { [key: string]: any }, propList: string[]) {
      propList.forEach(key => {
        data[key] = data[key] ? Number(data[key]) : 0
      })
    },
    coverPropToString(data: { [key: string]: any }, propList: string[]) {
      propList.forEach(key => {
        data[key] = data[key] ? String(data[key]) : ''
      })
    },
    formatLngLat(value: number | undefined): number | undefined {
      if (!value) { return value }
      return fixedNumber(value, 7)
    },
    checkDataByRules(data: { [key: string]: any }, rules: FormRules, getFieldValue?: (obj: Record<string, any>, key: string) => any): boolean | string {
      // 根据rule校验数据，兼容quasar内置的支持使用string来进行检验
      const keys = Object.keys(rules)
      const _getFieldValue = getFieldValue ?? ((obj, k) => obj[k])
      for (let k = 0; k < keys.length; k++) {
        const key = keys[k]
        let item = rules[key]
        for (let i = 0; i < item.length; i++) {
          let rule = item[i]
          // 使用字符串来匹配内置默认的规则，不匹配则不进行检验
          if (typeof rule === 'string') {
            if (!testPattern[rule]) {
              return true
            }
            rule = testPattern[rule] as FormRuleItemFunc
          }

          const valid = rule(_getFieldValue(data, key))
          if (valid !== true) {
            return valid
          }
        }
      }

      return true
    },
    getParentOrgName(rid: string): string {
      // 从本地有权限的单位查找
      const parentOption = this.parentOptions.find(item => item.value === rid)
      if (parentOption) {
        return parentOption.label ?? ''
      }
      // 从没有权限的单位查找
      const parentData = this.unknownUnitData.find(data => data.RID === rid)
      return parentData?.ShortName ?? ''
    },
    forceUpdate() {
      // not empty
    },
  },
  components: {
    modal,
    DataEdit,
  },
  beforeMount() {
    // 接收到一次请求无权限的单位数据时，则强制刷新一次
    StrPubSub.subscribe(QueryUnknownUnitDataOnceFinish, this.forceUpdate)
  },
  beforeUnmount() {
    StrPubSub.unsubscribe(QueryUnknownUnitDataOnceFinish, this.forceUpdate)
  },
})

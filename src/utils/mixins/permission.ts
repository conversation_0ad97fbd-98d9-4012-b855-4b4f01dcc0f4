import { defineComponent } from 'vue'
import { checkOperationPermission, OperationType } from '@utils/permission'

export default defineComponent({
  computed: {
    dbName() {
      return ''
    },
    canAdd(): boolean {
      return checkOperationPermission(this.dbName, OperationType.Insert)
    },
    canEdit(): boolean {
      return checkOperationPermission(this.dbName, OperationType.Update)
    },
    canDelete(): boolean {
      return checkOperationPermission(this.dbName, OperationType.Delete)
    },
    canQuery(): boolean {
      return checkOperationPermission(this.dbName, OperationType.Query)
    },
  },
})

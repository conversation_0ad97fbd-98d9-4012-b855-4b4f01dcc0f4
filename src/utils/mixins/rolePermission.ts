import { defineComponent } from 'vue'
import { LocalRole } from '@utils/bysdb.type'
import { GET_DATA, NS } from '@store/data/methodTypes'
import { DataName } from '@src/store'

export default defineComponent({
  computed: {
    currentRoleData(): LocalRole | undefined {
      return this.$store.getters[`${NS}/${GET_DATA}`](DataName.Role, this.roleRID)
    },
    IsBuiltIn(): boolean {
      return this.currentRoleData?.IsBuiltIn === 1
    },
    isMyRole(): boolean {
      return this.currentRoleData?.isMyRole ?? false
    },
    isNoPermission(): boolean {
      return this.IsBuiltIn || !this.isMyRole
    },
  },
})

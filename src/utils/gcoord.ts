import gcoord, { Position } from 'gcoord'
import { bysdb } from '@ygen/bysdb'
import { BysMarkerAndUpdateInfo, ControllerAndUpdateCmd, GCJ02GPS } from '@utils/bysdb.type'

const { GCJ02, transform, WGS84 } = gcoord

export function toWGS84(lngLat: Position): Position {
  return transform(lngLat, GCJ02, WGS84)
}

export function toGCJ02(lngLat: Position): Position {
  return transform(lngLat, WGS84, GCJ02)
}

// 读取原始定位数据
export function getLngLat(target: bysdb.IDbBysMarker | bysdb.IDbController | GCJ02GPS): Position {
  return [target?.Lon ?? 0, target?.Lat ?? 0]
}

// 设置原始定位数据
export function setLngLat(target: BysMarkerAndUpdateInfo | ControllerAndUpdateCmd, lngLat: Position): void {
  target.Lon = lngLat[0]
  target.Lat = lngLat[1]
}

// 设置GCJ02坐标定位数据
export function setGCJ02LngLat(target: BysMarkerAndUpdateInfo | ControllerAndUpdateCmd, lngLat?: Position): void {
  try {
    target.GCJ02LngLat = lngLat ?? toGCJ02(getLngLat(target))
  } catch (error) {
    // console.error('setGCJ02LngLat catch:', target, lngLat, error)
  }
}

// 读取GCJ02坐标定位数据
export function getGCJ02LngLat(target: BysMarkerAndUpdateInfo | ControllerAndUpdateCmd | GCJ02GPS): Position {
  if (target.GCJ02LngLat) {
    return target.GCJ02LngLat as Position
  }

  setGCJ02LngLat(target)
  return target.GCJ02LngLat ?? [0, 0]
}

export function fixedNumber(num: number, digits = 2): number {
  return parseFloat(num.toFixed(digits))
}

// 格式化经纬度长度，默认最大保留7位小数
export function lngLat2Fixed(lngLat: Position, digits = 7): Position {
  return lngLat.map(item => {
    return fixedNumber(item, digits)
  }) as Position
}

// 判断数据更新前后的坐标是否有变更
export function dataLngLatChanged(target: Position, source: Position): boolean {
  return (target[0] !== source[0] || target[1] !== source[1])
}

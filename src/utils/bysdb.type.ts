import { bysdb } from '@ygen/bysdb'
import { bysproto } from '@ygen/controller'
import { DeviceStatus } from '@utils/common'
import { doc } from '@ygen/bys.api'
import { user as userPermission } from '@ygen/userPermission'
import { QTableColumn, ValidationRule } from 'quasar'
import IOnlineControllerInfo = doc.IOnlineControllerInfo;

export type ExtendsQTableColumn<T extends Record<string, any> = any> = Array<T & QTableColumn> | undefined

export interface GCJ02GPS extends bysproto.IBGPS {
  GCJ02LngLat?: number[]
}

export interface DeviceUpdateInfo extends bysproto.IBdeviceUpdate {
  [key: string]: any

  StatusInfo?: DeviceStatus
}

export interface BysMarkerAndUpdateInfo extends bysdb.IDbBysMarker {
  [key: string]: any

  updateInfo?: DeviceUpdateInfo
  removeAlarm?: boolean
  GCJ02LngLat?: number[]
  isAlreadyRemoveAlarm?: boolean
  markerInfo?: doc.IMarkerInfo // 包含4g界桩的上报信息

  // 针对4g界桩新添加字段属性
  onlineInfo?: IOnlineControllerInfo
}

// 解析IControllerStatus.Status的结果集
export interface ControllerAbnormalStatus {
  [key: string]: any

  timeError: boolean
}

export interface ControllerStatus extends bysproto.IBloginReq, bysproto.IControllerStatus, IOnlineControllerInfo {
  [key: string]: any

  online?: boolean
  status?: ControllerAbnormalStatus
}

export interface ControllerCustomError {
  err?: boolean
  status?: Array<string>
}

export interface ControllerAndUpdateCmd extends bysdb.IDbController {
  [key: string]: any

  controllerState?: ControllerStatus
  Error?: ControllerCustomError
  GCJ02LngLat?: number[]
}

export interface LocalRole extends userPermission.IDbRole {
  isMyRole?: boolean
}

export interface FormRuleItemFunc {
  (val: any): boolean | string
}

// If String, then it must be a name of one of the embedded validation rules
export type FormRuleItem = ValidationRule

// Array of Functions/Strings;
export interface FormRules {
  [key: string]: ValidationRule[]
}

// 界桩抓拍照片上传的自定义参数
export interface ICustData {
  // 界桩硬件ID
  hwid: number
  // 1、红外感应抓拍，2、报警抓拍，3、定时抓拍，4、手动抓拍
  type: number
}

import log from '@utils/log'
import { resolveAndroidFileURL } from './path'

export async function openPdf(url: string) {
  const _url = cordova.file.applicationDirectory + 'www/' + url
  try {
    const fileEntry = await resolveAndroidFileURL(_url)
    const dirEntry = await resolveAndroidFileURL(cordova.file.cacheDirectory)
    fileEntry.copyTo(dirEntry, url.split('/').pop()!, function(newFileEntry) {
      cordova.plugins.fileOpener2.open(newFileEntry.nativeURL, 'application/pdf', {
        error: function(error: any) {
          log.error('fileOpener2 open pdf error', url, JSON.stringify(error))
        },
        success: function() {
          log.info('fileOpener2 open pdf success')
        },
      })
    })
  } catch (err) {
    log.error('openPdf error', JSON.stringify(err))
  }
}

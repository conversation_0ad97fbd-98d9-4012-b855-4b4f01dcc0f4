import { isApp, serverBaseUrl } from '@src/config'

// 处理服务器路径
export function resolveServerPath(src: string): string {
  if (isApp) {
    return `${serverBaseUrl}${src}`
  }
  return src
}

// // 处理app本机file资源路径
// export const AndroidRoot = 'android_asset/www'
// export function localFilePath(src: string): string {
//   return `file://${AndroidRoot}/${src}`
// }

// // 统一处理静态资源路径，匹配PC、Mobile、App
// export function assetsPath(src: string): string {
//   const { protocol } = window.location
//   if (isApp && protocol.includes('file:')) {
//     return localFilePath(src)
//   }
//   return src
// }

// // mapbox字体库和图标库资源路径，必须是包含协议类型的全路径
// export function mapboxAssetsPath(src: string): string {
//   const { protocol } = window.location
//   if (isApp && protocol.includes('file:')) {
//     return localFilePath(src)
//   }

//   let { hostname, port } = window.location
//   if (!port) {
//     port = '' + (protocol === 'https:'? 443: 80)
//   }

//   return `${protocol}//${hostname}:${port}${src}`
// }

export interface AndroidFileEntry {
  [prop: string]: any

  isFile: boolean
  isDirectory: boolean
  name: string
  fullPath: string
  nativeURL: string
  filesystem: any
  copyTo: (dirPath: AndroidFileEntry, fileName: string, success?: (res: AndroidFileEntry) => void, failed?: (error: any) => void) => void
  getDirectory: (path: string, option: Record<string, any>, success?: (res: AndroidFileEntry) => void, failed?: (error: any) => void) => void
  getFile: (path: string, option: Record<string, any>, success?: (res: AndroidFileEntry) => void, failed?: (error: any) => void) => void
}

// 完成Android路径解析处理
export function resolveAndroidFileURL(url: string): Promise<AndroidFileEntry> {
  return new Promise((resolve, reject) => {
    window.resolveLocalFileSystemURL(url, resolve, reject)
  })
}

// 请求android文件系统
export function requestAndroidFileSystem(): Promise<any> {
  return new Promise((resolve, reject) => {
    window.requestFileSystem(LocalFileSystem.PERSISTENT, 0, resolve, reject)
  })
}

const getDirectory = (dirEntry: AndroidFileEntry, path: string): Promise<{ done: boolean, result: AndroidFileEntry }> => {
  return new Promise((resolve) => {
    const success = (result: AndroidFileEntry) => {
      resolve({
        result,
        done: true,
      })
    }
    const failed = (error) => {
      resolve({
        result: error,
        done: false,
      })
    }
    dirEntry.getDirectory(path, { create: true }, success, failed)
  })
}

export async function createDirectory(startDirEntry: AndroidFileEntry, dirPath: string) {
  const paths = dirPath.split('/')
  let _dirEntry = startDirEntry
  for (let i = 0; i < paths.length; i++) {
    const path = paths[i]
    const res: any = await getDirectory(_dirEntry, path)
    const { result, done } = res
    if (!done) {
      return _dirEntry
    }
    _dirEntry = result
  }

  return _dirEntry
}

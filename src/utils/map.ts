import { QBtn, colors, colorsRg<PERSON>, Quasar, QBtnGroup, Notify } from 'quasar'
import maplibreGl, {
  StyleSetterOptions,
  GeoJSONSource,
  LngLatLike,
  FilterSpecification,
  GeoJSONSourceSpecification,
  SymbolLayerSpecification, MapLayerEventType,
} from 'maplibre-gl'
import globalConfig, { appLang, webConfig } from '@src/config'
import { cloneDeep, debounce, merge, throttle } from 'lodash'
import { bysdb } from '@ygen/bysdb'
import { BysMarker, Controller } from '@services/dataStore'
import { StrPubSub } from 'ypubsub'
import { DataName } from '@store/data'
import {
  BysMarkerDumpingAlarm,
  DeleteDbBysMarker,
  DeleteDbController,
  DeviceReportInfo,
  InsertDbBysMarker,
  InsertDbController,
  RemoveBysMarkerAlarm,
  ToggleMapStyleFinish,
  UpdateDbBysMarker,
  UpdateDbController,
  ShowNotInstalledMarker
} from './pubSubSubject'
import { getGCJ02LngLat, toGCJ02 } from './gcoord'
import { deviceTreeId } from '@utils/tree'
import { distance, along, lineString, point } from 'turf'
import {
  checkBysMarkerIsAlarm, checkIs4GMarker,
  checkoutAbnormalBysMarker,
  checkReportingIsPowerOn,
  isHaveMaintainPerm
} from '@utils/common'
import { BysMarkerAndUpdateInfo, DeviceUpdateInfo } from './bysdb.type'
import { Feature, GeoJsonProperties, LineString } from 'geojson'
import log from '@utils/log'
import { Store } from '@src/store'
import { bysproto } from '@ygen/controller'
import { h, createApp } from 'vue'
import { i18n } from '@boot/i18n'
import satellitePng from '@assets/map/satellite.png'
import streetPng from '@assets/map/street.png'
import { utcTime } from '@utils/dayjs'
import { TracePlayerData } from '@components/tracePlayer/types';

// 红线图规则默认选项
export const RedLineRuleOptVal = {
  Default: -20,
  NotConnect: -10
}

// 利用Promise特性，在指定的时间内等待地图样式加载完成，默认3分钟
export function waitingStyleLoaded(duration = 180): Promise<boolean> {
  const start = Date.now()
  return new Promise((resolve, reject) => {
    const waiting = () => {
      if (globalConfig.map?.isStyleLoaded()) {
        return resolve(true)
      }
      // 等待指定时间，如果地图没有加载完成，则结果待
      if (Date.now() - start > duration * 1000) {
        return reject('timeout')
      }
      window.setTimeout(waiting, 60)
    }
    waiting()
  })
}

const { hexToRgb } = colors
export type TLngLat = [number, number]

export interface ICustomControlOptions {
  [key: string]: any

  buttons?: HTMLElement[]
  onAdded?: (container: HTMLElement) => void
}

export class CustomControl implements maplibreGl.IControl {
  _map?: maplibreGl.Map
  _container?: HTMLElement
  buttons?: HTMLElement[]
  options: ICustomControlOptions

  constructor(options?: ICustomControlOptions) {
    this.options = Object.assign({}, options)
    this.buttons = this.options.buttons
  }

  onAdd(map: maplibreGl.Map): HTMLElement {
    this._map = map
    this._container = document.createElement('div')
    this._container.className =
      'custom-maplibregl-ctrl maplibregl-ctrl maplibregl-ctrl-group'
    if (this.buttons) {
      for (let i = 0; i < this.buttons.length; i++) {
        this._container.appendChild(this.buttons[i])
        this.buttons[i].classList.remove('not-display')
      }
    }

    window.setTimeout(() => {
      this.options.onAdded?.(this._container!)
    }, 0)

    return this._container
  }

  onRemove() {
    if (this._container && this._container.parentNode) {
      this._container.parentNode.removeChild(this._container)
    }
    this._map = undefined
  }
}

let rippleId = 0

function nextRippleId(): number {
  if (rippleId >= Number.MAX_SAFE_INTEGER) {
    rippleId = 0
  }
  return rippleId++
}

export interface RippleEffectOptions {
  [key: string]: any

  lngLat?: TLngLat
  duration?: number
  circleColor?: string
  color?: string
}

export class RippleEffect {
  id = `ripple-effect-${nextRippleId()}`;
  width = 60;
  height = 60;
  color: colorsRgba = { r: 255, g: 255, b: 255, a: 0 };
  duration = 350;
  lngLat: TLngLat = [0, 0];
  data: Uint8Array | Uint8ClampedArray = new Uint8Array(this.width ** 2 * 4);
  context: CanvasRenderingContext2D | null = null;
  radius = 0;
  speed: number = this.width / 2 / 24; // max radius/base number

  constructor(options?: RippleEffectOptions) {
    const opts: RippleEffectOptions = {
      color: '#fff',
      ...options
    }

    if (opts.duration) {
      this.duration = opts.duration
    }
    if (opts.lngLat) {
      this.lngLat = opts.lngLat
    }
    if (opts.color) {
      this.color = hexToRgb(opts.color as string)
    }
  }

  // get rendering context for the map canvas when layer is added to the map
  onAdd() {
    const canvas = document.createElement('canvas')
    canvas.width = this.width
    canvas.height = this.height
    this.context = canvas.getContext('2d')
  }

  // called once before every frame where the icon will be used
  render() {
    if (!globalConfig.map) {
      return true
    }
    let width = this.width
    let height = this.height
    let maxRadius = width / 2
    let context = this.context as CanvasRenderingContext2D
    this.radius += this.speed
    if (this.radius > maxRadius) {
      this.radius = this.speed
    }
    this.color.a = (1 - this.radius / maxRadius) * 0.9

    // draw ripple circle
    context.clearRect(0, 0, width, height)
    context.beginPath()
    context.arc(width / 2, height / 2, this.radius, 0, Math.PI * 2)
    context.fillStyle = `rgba(${this.color.r},${this.color.g},${this.color.b},${this.color.a})`
    context.fill()

    context.arc(width / 2, height / 2, this.radius / 2, 0, Math.PI * 2)
    context.fillStyle = `rgba(${this.color.r},${this.color.g},${this.color.b
    },${this.color.a + 0.25})`
    context.fill()
    context.strokeStyle = `rgba(${this.color.r},${this.color.g},${this.color.b
    },${this.color.a + 0.25})`
    context.lineWidth = 2
    context.stroke()

    // update this image's data with data from the canvas
    this.data = context.getImageData(0, 0, width, height).data

    // continuously repaint the map, resulting in the smooth animation of the dot
    globalConfig.map.triggerRepaint()

    return true
  }

  addToMap(lngLat?: TLngLat) {
    if (!globalConfig.map) {
      return
    }
    if (!lngLat) {
      lngLat = this.lngLat
    }

    // 添加Canvas图片资源
    globalConfig.map.addImage(this.id, this, { pixelRatio: 2 })
    // 添加source
    const sourceOptions: GeoJSONSourceSpecification = {
      type: 'geojson',
      data: {
        type: 'FeatureCollection',
        features: [
          {
            type: 'Feature',
            geometry: {
              type: 'Point',
              coordinates: lngLat as TLngLat
            },
            properties: {
              name: this.id
            }
          }
        ]
      },
      cluster: true,
      clusterMaxZoom: 14,
      clusterRadius: 50
    }
    globalConfig.map.addSource(this.id, sourceOptions)

    // let textColor = '#2196f3'
    // const mapStyle: any = globalConfig.map.getStyle()
    // if (mapStyle.name.includes('Satellite')) {
    //   textColor = '#0d47a1'
    // }
    // 添加layer
    globalConfig.map.addLayer({
      id: this.id,
      type: 'symbol',
      source: this.id,
      layout: {
        'icon-image': this.id,
        'icon-allow-overlap': true
      },
      paint: {}
    })

    window.setTimeout(() => {
      this.remove()
    }, this.duration)
  }

  remove() {
    if (!globalConfig.map) {
      return
    }

    if (globalConfig.map.getLayer(this.id)) {
      globalConfig.map.removeLayer(this.id)
    }
    if (globalConfig.map.getSource(this.id)) {
      globalConfig.map.removeSource(this.id)
    }
    if (globalConfig.map.hasImage(this.id)) {
      globalConfig.map.removeImage(this.id)
    }

    this.context = null
    this.data = new Uint8Array()
  }
}

export function setMapStyleCursor(styleValue = 'default'): void {
  if (!globalConfig.map) {
    return
  }
  globalConfig.map.getCanvas().style.cursor = styleValue
}

// 获取地图中心坐标
export function getMapCenter(): LngLatLike | undefined {
  if (!globalConfig.map) {
    return
  }
  return globalConfig.map.getCenter()
}

export function setMapCenter(lngLat: LngLatLike) {
  if (!globalConfig.map) {
    return
  }
  return globalConfig.map.setCenter(lngLat)
}

export enum MapStyleName {
  Satellite = 'satellite',
  Streets = 'streets'
}

let currentStyle = MapStyleName.Satellite

export function syncMapStyle(styleName: MapStyleName): void {
  currentStyle = styleName
}

// layer图层绘制marker的默认参数
const symbolLayout: Partial<SymbolLayerSpecification['layout']> = {
  'text-field': ['get', 'name'],
  'text-size': 12,
  'text-offset': [0, 1],
  'text-anchor': 'top',
  'text-letter-spacing': 0.1,
  'text-allow-overlap': true,
  'text-ignore-placement': true,

  // 'icon-padding': 0,
  'icon-allow-overlap': false,
  'icon-ignore-placement': false,
  'icon-size': 1,
  'icon-image': ['get', 'image']
}
const symbolPaint: Partial<SymbolLayerSpecification['paint']> = {
  'text-halo-color': '#000',
  'text-halo-width': 0.4

  // 更改图标颜色，图标必须设置{sdf:true}，项目暂不支持该功能
  // https://github.com/mapbox/maki/
  // 'icon-color': '#ff9800',
}

function getBysMarkerLayerOption(): Partial<SymbolLayerSpecification> {
  // 默认为卫星图配置
  const options: Partial<SymbolLayerSpecification> = {
    layout: {
      ...symbolLayout,
      'icon-size': 0.6,
      'text-offset': [0, 0.8]
    },
    paint: {
      ...symbolPaint,
      'text-color': '#027BE3',
      'text-halo-color': '#fff'
    }
  }

  // 如果是街道图，重围配置
  if (currentStyle === MapStyleName.Streets) {
    // Object.assign(options.layout, {})
    Object.assign(options.paint!, {
      'text-color': '#027BE3'
    })
  }

  return options
}

function getControllerLayerOption(): Partial<SymbolLayerSpecification> {
  // 默认为卫星图配置
  const options: Partial<SymbolLayerSpecification> = {
    layout: {
      ...symbolLayout,
      'icon-size': 1,
      'text-offset': [0, 1.2],
      'text-allow-overlap': true,
      'icon-allow-overlap': true
    },
    paint: {
      ...symbolPaint,
      'text-color': '#55ff00',
      'text-halo-color': '#fff'
    }
  }

  // 如果是街道图，重围配置
  if (currentStyle === MapStyleName.Streets) {
    // Object.assign(options.layout, {})
    Object.assign(options.paint!, {
      'text-color': '#9c27b0'
    })
  }

  return options
}

// 根据地图样式，返回不同的字体颜色和阴影
export function getMarkerLayerOptions(name: string): Partial<SymbolLayerSpecification> {
  if (name === DataName.BysMarker) {
    return getBysMarkerLayerOption()
  }

  return getControllerLayerOption()
}

export type CustomGeojsonFeature = GeoJSON.Feature<
  GeoJSON.Point,
  { [key: string]: any }
>
export type CustomLayerSources = Array<CustomGeojsonFeature>
// 控制器、界桩标记图层的数据源
export const CustomLayerSourcesV2: {
  [key: string]: Map<string, CustomGeojsonFeature>
} = {}

export function pointFeatureMap2List(
  map: Map<string, CustomGeojsonFeature>
): CustomLayerSources {
  return Array.from(map.values())
}

export function pointFeatureList2Map(
  list: CustomLayerSources
): Map<string, CustomGeojsonFeature> {
  return new Map(list.map(value => [value.id + '', value]))
}

const AlarmLayerSuffix = 'alarm'

export function getAlarmLayerId(dbName: string): string {
  return `${dbName}-${AlarmLayerSuffix}`
}

const NotFoundMap = 'can not found map'
export const CustomImages = {
  [DataName.BysMarker]: 'jz-green.png',
  [getAlarmLayerId(DataName.BysMarker)]: 'jz-red.png',
  [DataName.BysMarker + 'uninstalled']: 'jz-uninstalled.png',
  [DataName.BysMarker + 'uninstalled-stone']: 'jz-gray.png',
  [DataName.BysMarker + 'abnormal']: 'jz-yellow.png',
  [DataName.Controller]: 'bs-blue.png',
  [getAlarmLayerId(DataName.Controller)]: 'bs-red.png',
  // 后缀1为控制器类型 1:中继控制器
  [DataName.Controller + 'rp']: 'rp-blue.png',
  [getAlarmLayerId(DataName.Controller + 'rp')]: 'rp-red.png'
}

// 加载地图marker需要使用的图标
export const CustomImagePrefix = 'bys-'

const _markerAssets: Record<string, any> = import.meta.glob('assets/map/markers/*', { eager: true })
export const markerAssets = Object.keys(_markerAssets)
  .map((key) => {
    const name = key.split('/').pop() ?? ''

    return {
      [name]: _markerAssets[key].default,
    }
  })
  .reduce((p, c) => Object.assign(p, c), {})

export function loadCustomImage(path: string): Promise<boolean> {
  return new Promise((resolve, reject) => {
    if (!globalConfig.map) {
      return reject(NotFoundMap)
    }

    const name = CustomImagePrefix + path
    if (globalConfig.map.hasImage(name)) {
      return resolve(true)
    }

    globalConfig.map.loadImage(markerAssets[path])
      .then((res) => {
        // 再做一次判断
        if (globalConfig.map!.hasImage(name)) {
          return resolve(true)
        }
        globalConfig.map?.addImage(name, res.data)
        resolve(true)
      })
      .catch(reject)
  })
}

export function setPointLayerData(sourceID, features: CustomLayerSources) {
  if (!globalConfig.map) {
    return
  }

  const source = globalConfig.map.getSource(sourceID) as
    | maplibreGl.GeoJSONSource
    | undefined
  if (!source) {
    return
  }

  const sourceData: GeoJSON.FeatureCollection<GeoJSON.Point> = {
    type: 'FeatureCollection',
    features: features
  }
  source.setData(sourceData)
}

// 初始化图层marker
function addLayerMarkerSource(
  id: string,
  sourceData: maplibreGl.GeoJSONSourceSpecification
) {
  if (!globalConfig.map) {
    return
  }

  const source = globalConfig.map.getSource(id) as maplibreGl.GeoJSONSource | undefined
  if (source) {
    source.setData(
      sourceData.data as GeoJSON.FeatureCollection<GeoJSON.Point, any>
    )
  } else {
    globalConfig.map.addSource(
      id,
      merge(
        {
          type: 'geojson'
        },
        sourceData
      )
    )
  }
}

function addLayerMarker(layerData: Partial<SymbolLayerSpecification> & { id: string | DataName }) {
  if (!globalConfig.map) {
    return
  }
  if (!globalConfig.map.getLayer(layerData.id)) {
    const layer = merge(
      {
        type: 'symbol',
        source: layerData.source || layerData.id
      },
      layerData
    )
    globalConfig.map.addLayer(layer)
  }
}

export function createEmptyGeoJSONSource(): GeoJSONSourceSpecification {
  const sourceData: GeoJSONSourceSpecification = {
    type: 'geojson',
    data: {
      type: 'FeatureCollection',
      features: []
    }
  }
  return sourceData
}

export async function initLayerMarker(
  name: DataName = DataName.BysMarker
): Promise<boolean> {
  const uninstalledImageName = name + 'uninstalled'
  if (CustomImages[uninstalledImageName]) {
    await loadCustomImage(CustomImages[uninstalledImageName])
  }

  const uninstalledStoneImageName = name + 'uninstalled-stone'
  if (CustomImages[uninstalledStoneImageName]) {
    await loadCustomImage(CustomImages[uninstalledStoneImageName])
  }

  const abnormalImageName = name + 'abnormal'
  if (CustomImages[abnormalImageName]) {
    await loadCustomImage(CustomImages[abnormalImageName])
  }

  if (name === DataName.Controller) {
    await loadCustomImage(CustomImages[name + 'rp'])
  }

  // add custom symbol icon
  await loadCustomImage(CustomImages[name])

  // add source
  const sourceData: GeoJSONSourceSpecification = createEmptyGeoJSONSource()
  addLayerMarkerSource(name, sourceData)
  // 缓存数据源
  const features = (sourceData.data as GeoJSON.FeatureCollection<
    GeoJSON.Point,
    { [key: string]: any }
  >).features
  CustomLayerSourcesV2[name] = pointFeatureList2Map(features)

  // add layer
  const layerData = {
    id: name,
    ...getMarkerLayerOptions(name)
  }
  addLayerMarker(layerData)

  // listen mouse events
  globalConfig.map?.on('mouseenter', name, () => {
    setMapStyleCursor('pointer')
  })
  globalConfig.map?.on('mouseleave', name, () => {
    setMapStyleCursor()
  })

  return true
}

export function destroyLayerMarker(name: DataName = DataName.BysMarker) {
  if (!globalConfig.map) {
    return
  }
  // remove layer
  const layer = globalConfig.map.getLayer(name)
  if (layer) {
    globalConfig.map.removeLayer(name)
  }

  // remove source
  const source = globalConfig.map.getSource(name)
  if (source) {
    globalConfig.map.removeSource(name)
  }

  // remove symbol icon
  const imageID = CustomImagePrefix + CustomImages[name]
  if (globalConfig.map.hasImage(imageID)) {
    globalConfig.map.removeImage(imageID)
  }
}

// 切换Marker图层显示/隐藏
export function toggleLayerMarkers(layerName: string, visible: boolean) {
  if (!globalConfig.map?.getLayer(layerName)) {
    return
  }
  const visibility = globalConfig.map.getLayoutProperty(
    layerName,
    'visibility'
  )
  const value = visible ? 'visible' : 'none'
  if (visibility === value) {
    return
  }
  globalConfig.map.setLayoutProperty(layerName, 'visibility', value)
}

export function addGeometryToFeatures(
  features: Map<string, CustomGeojsonFeature>,
  geometry: CustomGeojsonFeature
): Map<string, CustomGeojsonFeature> {
  const key = geometry.id + ''
  if (features.has(key)) {
    const oldData = features.get(key)
    const data = merge(oldData, geometry)
    features.set(key, data)
  } else {
    features.set(key, geometry)
  }
  return features
}

function removeGeometryFromFeatures(features: any[], id: string) {
  const index = features.findIndex(item => item.id === id)
  features.splice(index, 1)
}

// 界桩图层marker
export function createDeviceMarkerFeature(
  device: BysMarkerAndUpdateInfo
): GeoJSON.Feature<GeoJSON.Point, { [name: string]: any }> {
  // @ts-ignore
  const tree = $.ui?.fancytree.getTree(`#${deviceTreeId}`)
  const node = tree?.getNodeByKey(device.RID as string)

  return {
    id: device.RID as string,
    type: 'Feature',
    geometry: {
      type: 'Point',
      coordinates: getGCJ02LngLat(device) as GeoJSON.Position
    },
    properties: {
      OrgRID: device.OrgRID,
      RID: device.RID,
      name: device.MarkerNo,
      image: device.HasInstallStone
        ? device.HasInstallDevice
          ? checkoutAbnormalBysMarker(device, utcTime()) ? `bys-${CustomImages[DataName.BysMarker + 'abnormal']}` : `bys-${CustomImages[DataName.BysMarker]}`
          : `bys-${CustomImages[DataName.BysMarker + 'uninstalled']}`
        : `bys-${CustomImages[DataName.BysMarker + 'uninstalled-stone']}`,
      MapShowLevel: device.MapShowLevel,
      HasInstallDevice: device.HasInstallDevice,
      TreeNodeSelected: node?.isSelected() ?? true,
    }
  }
}

// filter name => filter
// MapShowLevel: ['>=', +(zoom.toFixed(2)), ['get', 'MapShowLevel']]
const MapFilters: { [key: string]: any } = {}

function wrapMapFilterKey(layer: string, key: string) {
  return `${layer}__${key}`
}

function mapSetFilter(
  layer: string,
  mapFilters: { [key: string]: any },
  options?: StyleSetterOptions
) {
  if (!globalConfig.map || !globalConfig.map.getLayer(layer)) {
    return
  }

  const filters: FilterSpecification = ['all']
  // 找到当前图层的过滤器
  for (let key in mapFilters) {
    if (key.includes(`${layer}__`)) {
      const filter = mapFilters[key]
      filters.push(filter)
    }
  }
  globalConfig.map.setFilter(layer, filters, options)
}

//根据地图缩放等级，决定是否显示界桩
export function filterMarkerByShowLevel(zoom, layerId = DataName.BysMarker) {
  if (!globalConfig.map || !globalConfig.map.getLayer(layerId)) {
    return
  }

  const showLevelFilter = ['>=', +zoom.toFixed(2), ['get', 'MapShowLevel']]
  MapFilters[wrapMapFilterKey(layerId, 'MapShowLevel')] = showLevelFilter
  mapSetFilter(layerId, MapFilters)
}

// 根据界桩安装情况，是否显示界桩标记
// 地图缩放时也需要更新
export function filterMarkerByHasInstallDevice() {
  if (!globalConfig.map) {
    return
  }

  const showNotInstalledMarker = Store.state.Settings.showNotInstalledMarker
  const layerIds = [DataName.BysMarker, getAlarmLayerId(DataName.BysMarker)]
  for (let i = 0; i < layerIds.length; i++) {
    const layerId = layerIds[i]
    if (!globalConfig.map.getLayer(layerId)) {
      continue
    }

    let filter: any[] = ['has', 'HasInstallDevice']
    if (!showNotInstalledMarker) {
      filter = ['==', true, ['get', 'HasInstallDevice']]
    }

    MapFilters[wrapMapFilterKey(layerId, 'HasInstallDevice')] = filter
    mapSetFilter(layerId, MapFilters)
  }
}

export function filterMarkerByTreeNodeSelect(layer: string | DataName = DataName.BysMarker) {
  if (!globalConfig.map) {
    return
  }

  const layerIds = [layer, getAlarmLayerId(layer)]
  for (let i = 0; i < layerIds.length; i++) {
    const layerId = layerIds[i]
    if (!globalConfig.map.getLayer(layerId)) {
      continue
    }

    const filter = ['==', true, ['get', 'TreeNodeSelected']]

    MapFilters[wrapMapFilterKey(layerId, 'TreeNodeSelected')] = filter
    mapSetFilter(layerId, MapFilters)
  }
}

export function syncDeviceLayerMarker(device: { [key: string]: any }) {
  if (!CustomLayerSourcesV2[DataName.BysMarker]) {
    CustomLayerSourcesV2[DataName.BysMarker] = new Map<
      string,
      CustomGeojsonFeature
    >()
  }
  addGeometryToFeatures(
    CustomLayerSourcesV2[DataName.BysMarker],
    createDeviceMarkerFeature(device)
  )
  setPointLayerData(
    DataName.BysMarker,
    pointFeatureMap2List(CustomLayerSourcesV2[DataName.BysMarker])
  )

  // 显示layer
  toggleLayerMarkers(DataName.BysMarker, true)
  filterMarkerByShowLevel(globalConfig.map?.getZoom())
  filterMarkerByHasInstallDevice()
  filterMarkerByTreeNodeSelect()
}

export function removeDeviceLayerMarker(id: string) {
  if (!globalConfig.map) {
    return
  }

  const source: any = globalConfig.map.getSource(DataName.BysMarker)
  if (!source) {
    return
  }

  removeGeometryFromFeatures(source._data.features, id)
  CustomLayerSourcesV2[DataName.BysMarker] = pointFeatureList2Map(
    source._data.features
  )
  source.setData(source._data)
}

function setBysMarkerAlarmFeature(
  targetList: Map<string, CustomGeojsonFeature>,
  data: BysMarkerAndUpdateInfo
) {
  const id = getAlarmLayerId(DataName.BysMarker)
  // 常规图标feature
  const feature: CustomGeojsonFeature = createDeviceMarkerFeature(data)
  const alarmFeature = cloneDeep(feature)
  alarmFeature.id += AlarmLayerSuffix
  alarmFeature.properties.image = `bys-${CustomImages[id]}`
  // 设置报警图标feature，如果没有有效的定位数据，则只显示一个报警状态的Point数据
  let gpsIsValid = true
  if (checkIs4GMarker(data.MarkerType)) {
    const { locate } = data.markerInfo?.AlarmReporting ?? {}
    alarmFeature.geometry.coordinates = getGCJ02LngLat(locate ?? data)
    gpsIsValid = checkGpsIsValidWith4gMarker(locate)
  } else {
    const updateInfo = data?.updateInfo as DeviceUpdateInfo | undefined
    alarmFeature.geometry.coordinates = getGCJ02LngLat(updateInfo?.GPS ?? data)
    gpsIsValid = updateInfo?.StatusInfo?.isValidPosition ?? false
  }

  // 上传有效的定位数据，则计算报警的坐标距离与系统设置的距离，如果小于10米，则只显示报警图层图标数据
  if (gpsIsValid) {
    const diff = distance(feature, alarmFeature, 'meters')
    if (diff >= webConfig.alarmDistance) {
      addGeometryToFeatures(targetList, feature)
    }
  } else {
    alarmFeature.geometry.coordinates = feature.geometry.coordinates
  }

  // 如果有维护权限，则所有报警都要更新状态，没有维护权限，则只能看到界桩倾倒报警
  if (!isHaveMaintainPerm()) {
    alarmFeature.geometry.coordinates = feature.geometry.coordinates
    alarmFeature.properties.image = feature.properties.image
  }
  addGeometryToFeatures(targetList, alarmFeature)
}

// 加载界桩图层markers，需要判断是否处于报警状态，同时加载报警图层
export async function loadBysMarkers(
  // dataList: Array<BysMarkerAndUpdateInfo> = BysMarker.getDataList()
) {
  if (!globalConfig.map) {
    return
  }
  window.console.time('loadBysMarkers')
  const dataList: Array<BysMarkerAndUpdateInfo> = BysMarker.getDataList()
  const features: Map<string, CustomGeojsonFeature> = new Map()
  const alarmFeatures: Map<string, CustomGeojsonFeature> = new Map()
  for (let i = 0; i < dataList.length; i++) {
    const item = dataList[i]
    if (checkBysMarkerIsAlarm(item)) {
      setBysMarkerAlarmFeature(alarmFeatures, item)
      continue
    }

    addGeometryToFeatures(features, createDeviceMarkerFeature(item))
  }

  // 更新界桩标记点图层
  const source = globalConfig.map.getSource(DataName.BysMarker) as
    | maplibreGl.GeoJSONSource
    | undefined
  if (source) {
    const data: GeoJSON.FeatureCollection<GeoJSON.Point> = {
      type: 'FeatureCollection',
      features: pointFeatureMap2List(features)
    }
    source.setData(data)
    CustomLayerSourcesV2[DataName.BysMarker] = features
  }

  // 有报警的界桩数据，需要创建报警图层
  if (alarmFeatures.size > 0) {
    const id = getAlarmLayerId(DataName.BysMarker)
    // 如果界桩报警图层不存在，则先创建图层
    let alarmLayer = globalConfig.map.getLayer(id)
    if (!alarmLayer) {
      await createBysMarkerAlarmLayer()
    }

    CustomLayerSourcesV2[id] = alarmFeatures
    setPointLayerData(id, pointFeatureMap2List(alarmFeatures))
  }

  toggleLayerMarkers(DataName.BysMarker, dataList.length !== 0)
  filterMarkerByShowLevel(globalConfig.map.getZoom())
  filterMarkerByHasInstallDevice()
  filterMarkerByTreeNodeSelect()
  window.console.timeEnd('loadBysMarkers')
}

// 处理界桩图层
StrPubSub.subscribe(InsertDbBysMarker, (data: bysdb.IDbBysMarker) => {
  syncDeviceLayerMarker(data)
})
StrPubSub.subscribe(UpdateDbBysMarker, (data: bysdb.IDbBysMarker) => {
  syncDeviceLayerMarker(data)
})
StrPubSub.subscribe(DeleteDbBysMarker, (data: bysdb.IDbBysMarker) => {
  removeDeviceLayerMarker(data.RID as string)
})

// 订阅是否显示未安装的界桩事件
StrPubSub.subscribe(ShowNotInstalledMarker, () => {
  filterMarkerByHasInstallDevice()
  filterMarkerByTreeNodeSelect()
})

async function createBysMarkerAlarmLayer() {
  const layerId = getAlarmLayerId(DataName.BysMarker)
  // add image
  await loadCustomImage(CustomImages[layerId])

  // add source
  const sourceData: GeoJSONSourceSpecification = createEmptyGeoJSONSource()
  addLayerMarkerSource(layerId, sourceData)
  // 缓存数据源
  const features = (sourceData.data as GeoJSON.FeatureCollection<
    GeoJSON.Point,
    { [key: string]: any }
  >).features
  CustomLayerSourcesV2[layerId] = pointFeatureList2Map(features)

  // 报警图层要强制显示出来
  const defLayerOption = {
    id: layerId,
    ...getBysMarkerLayerOption()
  }
  const allowLayerOption = {
    layout: {
      'text-allow-overlap': true,
      'text-ignore-placement': true,
      'icon-allow-overlap': true,
      'icon-ignore-placement': true
    }
  }
  const markerLayer = merge(defLayerOption, allowLayerOption)

  addLayerMarker(markerLayer)

  // listen mouse events
  globalConfig.map?.on('mouseenter', layerId, () => {
    setMapStyleCursor('pointer')
  })
  globalConfig.map?.on('mouseleave', layerId, () => {
    setMapStyleCursor()
  })
}

// 订阅界桩报警事件，更新对应图标报警状态
// let bysMarkerTimer: number | undefined = undefined
// let bysMarkerAlarmTipCount = 0
//
// function nextBysMarkerAlarmTipCount(): number {
//   if (bysMarkerAlarmTipCount >= Number.MAX_SAFE_INTEGER) {
//     bysMarkerAlarmTipCount = 0
//   }
//   return bysMarkerAlarmTipCount++
// }

async function bysMarkerAlarm(data: BysMarkerAndUpdateInfo) {
  if (!globalConfig.map) {
    return false
  }

  const id = getAlarmLayerId(DataName.BysMarker)
  // 如果界桩报警图层不存在，则先创建图层
  let alarmLayer = globalConfig.map.getLayer(id)
  if (!alarmLayer) {
    await createBysMarkerAlarmLayer()
  }

  // 报警图层每个界桩都要有两个数据，一个是常规图标，一个是报警图标，以便对比
  // 更新界桩报警图层数据源
  if (!CustomLayerSourcesV2[id]) {
    CustomLayerSourcesV2[id] = new Map<string, CustomGeojsonFeature>()
  }
  setBysMarkerAlarmFeature(CustomLayerSourcesV2[id], data)

  // 更新报警图层
  setPointLayerData(id, pointFeatureMap2List(CustomLayerSourcesV2[id]))

  // // 报警图层间隔250毫秒切换一次图标以突显报警状态
  // if (bysMarkerTimer !== undefined) {
  //   clearInterval(bysMarkerTimer)
  // }
  // bysMarkerTimer = window.setInterval(() => {
  //   const image = nextBysMarkerAlarmTipCount() % 2 === 1
  //     ? `bys-${CustomImages[id]}`
  //     : `bys-${CustomImages[DataName.BysMarker]}`
  //   globalConfig.map?.setLayoutProperty(id, 'icon-image', image)
  // }, 250)

  // 更新界桩默认标记图层
  syncDeviceLayerMarker(data)
}

function bysMarkerRemoveAlarm(data: bysdb.IDbBysMarker) {
  const id = getAlarmLayerId(DataName.BysMarker)
  // 从报警图层中清除界桩数据
  if (!CustomLayerSourcesV2[id]) {
    return
  }
  CustomLayerSourcesV2[id].delete(data.RID + '')
  CustomLayerSourcesV2[id].delete(data.RID + AlarmLayerSuffix)
  setPointLayerData(id, pointFeatureMap2List(CustomLayerSourcesV2[id]))

  // 当界桩报警图层数据源没有数据时，则停止切换报警图标
  /*if (CustomLayerSources[id].length === 0) {
    clearInterval(bysMarkerTimer)
    bysMarkerTimer = undefined
  }*/
  // 添加界桩数据到默认标记图层
  syncDeviceLayerMarker(data)
}

//订阅界桩消息
StrPubSub.subscribe(DeviceReportInfo, (data: BysMarkerAndUpdateInfo) => {
  // 4g界桩，打卡上报时，不需要处理报警的状态
  if (checkIs4GMarker(data.MarkerType)) {
    return
  }

  const updateInfo = data.updateInfo as DeviceUpdateInfo | null
  !updateInfo?.StatusInfo?.isAlarm && bysMarkerRemoveAlarm(data)
})

StrPubSub.subscribe(BysMarkerDumpingAlarm, (data: BysMarkerAndUpdateInfo) => {
  // 4g界桩的开机上报的指令，不作为报警处理
  if (checkIs4GMarker(data.MarkerType)) {
    if (checkReportingIsPowerOn(data.markerInfo ?? {}, true)) return
  }
  bysMarkerAlarm(data)
})

StrPubSub.subscribe(RemoveBysMarkerAlarm, bysMarkerRemoveAlarm)

// 控制器图层marker
export function createControllerMarkerFeature(
  data: bysdb.IDbController
): CustomGeojsonFeature {
  const typeName =
    DataName.Controller + (data.ControllerType === 1 ? 'rp' : '')

  // @ts-ignore
  const tree = $.ui.fancytree.getTree(`#${deviceTreeId}`)
  const node = tree?.getNodeByKey(data.RID as string)

  return {
    id: data.RID as string,
    type: 'Feature',
    geometry: {
      type: 'Point',
      coordinates: getGCJ02LngLat(data) as GeoJSON.Position
    },
    properties: {
      OrgRID: data.OrgRID,
      RID: data.RID,
      name: data.ControllerNo,
      image: `bys-${CustomImages[typeName]}`,
      MapShowLevel: data.MapShowLevel,
      TreeNodeSelected: node?.isSelected() ?? true,
    }
  }
}

export function syncControllerLayerMarker(data: { [key: string]: any }) {
  if (!CustomLayerSourcesV2[DataName.Controller]) {
    CustomLayerSourcesV2[DataName.Controller] = new Map<
      string,
      CustomGeojsonFeature
    >()
  }
  addGeometryToFeatures(
    CustomLayerSourcesV2[DataName.Controller],
    createControllerMarkerFeature(data)
  )
  setPointLayerData(
    DataName.Controller,
    pointFeatureMap2List(CustomLayerSourcesV2[DataName.Controller])
  )

  // 显示layer
  toggleLayerMarkers(DataName.Controller, true)
  filterMarkerByShowLevel(globalConfig.map?.getZoom(), DataName.Controller)
  filterMarkerByTreeNodeSelect(DataName.Controller)
}

export function removeControllerLayerMarker(id: string) {
  if (!globalConfig.map) {
    return
  }

  const source: any = globalConfig.map.getSource(DataName.Controller)
  if (!source) {
    return
  }

  removeGeometryFromFeatures(source._data.features, id)
  CustomLayerSourcesV2[DataName.Controller] = pointFeatureList2Map(
    source._data.features
  )
  source.setData(source._data)
}

// 加载控制器地图标记
export function loadControllerMarker(
  /*  dataList: bysdb.IDbController[] = Controller.getDataList() */
) {
  if (!globalConfig.map) {
    return
  }

  window.console.time('loadControllerMarker')
  const dataList: bysdb.IDbController[] = Controller.getDataList()
  const features = new Map<string, CustomGeojsonFeature>()
  for (let i = 0; i < dataList.length; i++) {
    const item = dataList[i]
    addGeometryToFeatures(features, createControllerMarkerFeature(item))
  }

  const source = globalConfig.map.getSource(DataName.Controller) as
    | maplibreGl.GeoJSONSource
    | undefined
  if (source) {
    const data: GeoJSON.FeatureCollection<GeoJSON.Point> = {
      type: 'FeatureCollection',
      features: pointFeatureMap2List(features)
    }
    source.setData(data)
    CustomLayerSourcesV2[DataName.Controller] = features
  }

  toggleLayerMarkers(DataName.Controller, dataList.length !== 0)
  filterMarkerByShowLevel(globalConfig.map?.getZoom(), DataName.Controller)
  filterMarkerByTreeNodeSelect(DataName.Controller)
  window.console.timeEnd('loadControllerMarker')
}

StrPubSub.subscribe(InsertDbController, (data: bysdb.IDbController) => {
  syncControllerLayerMarker(data)
})
StrPubSub.subscribe(UpdateDbController, (data: bysdb.IDbController) => {
  syncControllerLayerMarker(data)
})
StrPubSub.subscribe(DeleteDbController, (data: bysdb.IDbController) => {
  removeControllerLayerMarker(data.RID as string)
})

// 红线图图层
export const RedLineGraphLayerId = 'redLineGraphLayerId'
export let showRedLineGraph = true

export function enableRedLineGraph(isShow = true) {
  showRedLineGraph = isShow
}

export async function toggleRedLineGraphV2() {
  if (!globalConfig.map) {
    return false
  }

  if (!globalConfig.map.getLayer(RedLineGraphLayerId)) {
    await loadRedLineGraphV2()
  }

  globalConfig.map.setLayoutProperty(
    RedLineGraphLayerId,
    'visibility',
    showRedLineGraph ? 'visible' : 'none'
  )

  return true
}

function createEmptyLineStringFeature(properties?: {
  [key: string]: any
}): Feature<LineString> {
  return {
    type: 'Feature',
    geometry: {
      type: 'LineString',
      coordinates: []
    },
    properties: {
      ...properties
    } as GeoJsonProperties
  }
}

function renderRedLineGraphLayer(features: Array<Feature<LineString>>) {
  if (!globalConfig.map) {
    return
  }

  // 生成LineString图层数据源
  const redLineGraphSource: GeoJSON.FeatureCollection<GeoJSON.LineString> = {
    type: 'FeatureCollection',
    features: features
  }
  // 绘制图层数
  const source = globalConfig.map.getSource(RedLineGraphLayerId) as
    | GeoJSONSource
    | undefined
  if (source) {
    source.setData(redLineGraphSource)
  } else {
    globalConfig.map.addSource(RedLineGraphLayerId, {
      type: 'geojson',
      data: redLineGraphSource
    })
  }

  if (!globalConfig.map.getLayer(RedLineGraphLayerId)) {
    globalConfig.map.addLayer({
      id: RedLineGraphLayerId,
      type: 'line',
      source: RedLineGraphLayerId,
      layout: {
        'line-cap': 'round',
        'line-join': 'round'
      },
      paint: {
        'line-color': '#c10015',
        'line-width': 1
      }
    })
  }
}

export async function loadRedLineGraphV2() {
  if (!globalConfig.map || !showRedLineGraph) {
    return
  }

  window.console.time('loadRedLineGraphV2')
  const allBysMarkers = BysMarker.getDataList()
  const allMarkerIdSet = new Set<number>(allBysMarkers.map(item => item.MarkerHWID as number))
  const markerIdIterator = allMarkerIdSet.values()
  let lastFeature: Feature<LineString> = createEmptyLineStringFeature({ keys: [] })
  let features: Array<Feature<LineString>> = [lastFeature]
  const enableNextFeature = () => {
    if (lastFeature.geometry.coordinates.length === 0) {
      return
    }
    lastFeature = createEmptyLineStringFeature({ keys: [] })
    features.push(lastFeature)
  }
  const appendToLastFeature = (item: bysdb.IDbBysMarker, feature: Feature<LineString> = lastFeature) => {
    const lngLat = getGCJ02LngLat(item)
    feature.geometry.coordinates.push(lngLat)
    feature.properties?.keys?.push(item.MarkerHWID)
    allMarkerIdSet.delete(item.MarkerHWID as number)
  }
  const getRedLineSetting = (item: bysdb.IDbBysMarker): Array<number> => {
    try {
      const settings = JSON.parse(item.Setting ?? '{}')
      return settings.redLine ?? []
    } catch (e) {
      log.warn('getRedLineSetting error:', e, item)
      return []
    }
  }
  const processDefaultFeature = (item: bysdb.IDbBysMarker) => {
    const MarkerHWID = item.MarkerHWID as number
    appendToLastFeature(item)

    // 查找下一个ID的结点，没有则当前的feature结束
    const nextId = MarkerHWID + 1
    const nextMarker: bysdb.IDbBysMarker | undefined = BysMarker.getDataByIndex(nextId + '')
    if (!nextMarker) {
      // 开启新的图层片段
      enableNextFeature()
      return
    }

    // 避免重复处理
    if (!allMarkerIdSet.has(nextMarker.MarkerHWID as number)) return

    // 递归处理
    processBysMarkerRedLineGraph(nextMarker)
  }
  const processLinksFeature = (item: bysdb.IDbBysMarker, redLineLinks: number[]) => {
    const MarkerHWID = item.MarkerHWID as number
    appendToLastFeature(item)

    for (let i = 0; i < redLineLinks.length; i++) {
      const id = redLineLinks[i]
      // 红线图配置中可能选择了自己，需要过滤
      if (MarkerHWID === id) {
        continue
      }

      const nextMarker = BysMarker.getDataByIndex(id + '') as bysdb.IDbBysMarker | undefined
      if (!nextMarker) {
        continue
      }

      processBysMarkerRedLineGraph(nextMarker)
    }

    // 还有界桩要遍历，则开启新的图层片段
    if (allMarkerIdSet.size > 0) {
      enableNextFeature()
    }
  }
  const processBysMarkerRedLineGraph = (item: bysdb.IDbBysMarker) => {
    const redLineLinks = getRedLineSetting(item)

    // 没有则为默认，则添加到默认的红线图中，只连接下一个ID的结点
    if (redLineLinks.length === 0) {
      processDefaultFeature(item)
      return
    }

    // 按照配置的数据，进行生成
    processLinksFeature(item, redLineLinks)
  }

  // 遍历界桩，按照红线图规则生成图层的features
  // 使用while循环配置栈结构可以减少循环次数
  while (allMarkerIdSet.size > 0) {
    const { done, value } = markerIdIterator.next()
    if (done) break

    const item = BysMarker.getDataByIndex(value + '')
    if (!item) continue

    processBysMarkerRedLineGraph(item)
  }

  // 两点一线，过滤结点小于2的feature
  features = features.filter(feature => feature.geometry.coordinates.length >= 2)
  renderRedLineGraphLayer(features)
  window.console.timeEnd('loadRedLineGraphV2')
}

// 订阅界桩数据变化，重给红线图层
StrPubSub.subscribe(InsertDbBysMarker, loadRedLineGraphV2)
StrPubSub.subscribe(UpdateDbBysMarker, loadRedLineGraphV2)
// 删除单位时，可能会批量删除界桩，导致频繁触发重绘红线图，所以需要进行防抖处理
StrPubSub.subscribe(DeleteDbBysMarker, debounce(loadRedLineGraphV2), 300)

// 我的位置
class MyLocationImage {
  size: number
  width: number
  height: number
  data: Uint8Array | Uint8ClampedArray
  context: CanvasRenderingContext2D | null = null;

  constructor(size = 60) {
    this.width = this.height = this.size = size
    this.data = new Uint8Array(size * size * 4)
  }

  // get rendering context for the map canvas when layer is added to the map
  onAdd() {
    const canvas = document.createElement('canvas')
    canvas.width = this.width
    canvas.height = this.height
    this.context = canvas.getContext('2d')
  }

  // called once before every frame where the icon will be used
  render() {
    let duration = 1000
    let t = (performance.now() % duration) / duration

    let x = this.width / 2
    let y = this.height / 2
    let radius = (this.size / 2) * 0.35
    let outerRadius = (this.size / 2) * 0.6 * t + radius
    let context: CanvasRenderingContext2D | null = this.context
    if (!context) {
      return
    }

    // draw outer circle
    context.clearRect(0, 0, this.width, this.height)
    context.beginPath()
    context.arc(x, y, outerRadius, 0, Math.PI * 2)
    context.fillStyle = `rgba(2, 136, 209, ${1 - t})`
    context.fill()

    // draw inner circle
    context.beginPath()
    context.arc(x, y, radius, 0, Math.PI * 2)
    context.fillStyle = 'rgba(2, 136, 209, 1)'
    context.strokeStyle = 'white'
    context.lineWidth = 1 + 4 * (1 - t)
    context.fill()
    context.stroke()

    // update this image's data with data from the canvas
    this.data = context.getImageData(0, 0, this.width, this.height).data

    // continuously repaint the map, resulting in the smooth animation of the dot
    globalConfig.map?.triggerRepaint()

    // return `true` to let the map know that the image was updated
    return true
  }
}

export const myLocation = (() => {
  const image = new MyLocationImage()
  const id = 'myLocation'
  let features: Array<GeoJSON.Feature<GeoJSON.Point, any>> = []
  let isAdd = false
  let _lngLat = [0, 0]
  let _map

  const _addTo = map => {
    if (!map.hasImage(id)) {
      map.addImage(id, image, { pixelRatio: 2 })
    }

    const sourceObj = map.getSource(id) as maplibreGl.GeoJSONSource | undefined
    if (sourceObj) {
      features[0].geometry.coordinates = _lngLat
      const source: GeoJSON.FeatureCollection<GeoJSON.Point, any> = {
        type: 'FeatureCollection',
        features
      }
      sourceObj.setData(source)
      return
    }

    features.push({
      type: 'Feature',
      geometry: {
        type: 'Point',
        coordinates: _lngLat
      },
      properties: {}
    })
    const source: GeoJSONSourceSpecification = {
      type: 'geojson',
      data: {
        type: 'FeatureCollection',
        features
      }
    }
    map.addSource(id, source)
    map.addLayer({
      id: id,
      type: 'symbol',
      source: id,
      layout: {
        'icon-image': id
      }
    })

    StrPubSub.subscribe(ToggleMapStyleFinish, reDrawMyLocate)
  }
  const reDrawMyLocate = () => {
    _addTo(_map)
  }

  return {
    setLngLat(lngLat: number[]) {
      const sourceObj = globalConfig.map?.getSource(id) as
        | maplibreGl.GeoJSONSource
        | undefined
      if (!sourceObj) {
        StrPubSub.publish('geolocation', 'myLocation not fond layer:', id)
        return this
      }

      features[0].geometry.coordinates = lngLat
      const source: GeoJSON.FeatureCollection<GeoJSON.Point, any> = {
        type: 'FeatureCollection',
        features
      }
      sourceObj.setData(source)
      StrPubSub.publish('geolocation', 'myLocation setLngLat', lngLat)

      return this
    },
    addTo(map = globalConfig.map) {
      if (!map || !globalConfig.map?.isStyleLoaded() || isAdd) {
        return this
      }

      _map = map
      _addTo(map)
      isAdd = true

      return this
    },
    remove() {
      const map = globalConfig.map
      if (!map || !globalConfig.map?.isStyleLoaded() || !isAdd) {
        return this
      }

      map.removeImage(id)
      map.removeLayer(id)
      map.removeSource(id)
      StrPubSub.unsubscribe(ToggleMapStyleFinish, reDrawMyLocate)
      _map = undefined

      isAdd = false
      return this
    }
  }
})()

export class MyGeolocate extends maplibreGl.GeolocateControl {
  constructor(options: any) {
    super(options)
  }

  // @ts-ignore
  _onSuccess(position: any) {
    let _position: any
    if (position) {
      const [longitude, latitude] = toGCJ02([
        position.coords.longitude,
        position.coords.latitude
      ])
      _position = {
        coords: {
          accuracy: position.coords.accuracy,
          altitude: position.coords.altitude,
          altitudeAccuracy: position.coords.altitudeAccuracy,
          heading: position.coords.heading,
          latitude: Number(latitude.toFixed(7)),
          longitude: Number(longitude.toFixed(7)),
          speed: position.coords.speed
        },
        timestamp: position.timestamp
      }
    }
    // @ts-ignore
    super._onSuccess(_position)
  }
}

export function flyTo(lngLat: LngLatLike, mapZoom = 12) {
  const map = globalConfig.map
  if (!map) {
    return
  }

  // 跳转过程中，触发地图尺寸变化，则重新跳转
  const _flyTo = throttle(() => {
    flyTo(lngLat, mapZoom)
  }, 350)
  map?.on('resize', _flyTo)
  // 禁用地图相关操作
  map.doubleClickZoom.disable()

  map.flyTo({
    center: lngLat,
    zoom: Math.max(mapZoom, 16)
  })

  window.setTimeout(() => {
    map.doubleClickZoom.enable()
    map?.off('resize', _flyTo)
  })
}

// 界桩上传的gps信息,无效gps时为全0
export function checkGpsIsValidWith4gMarker(locate?: bysproto.IBGPS | null | undefined): boolean {
  if (!locate) return false
  if (locate.Lon === 0 && locate.Lat === 0 && locate.Height === 0) return false
  // @ts-ignore
  return !(locate.Lon > 180 || locate.Lon < -180 || locate.Lat > 90 || locate.Lat < -90)
}

//  从界桩上获取坐标，如果有报警则使用当前报警的坐标
export function getLngLatFromBysMarker(data: BysMarkerAndUpdateInfo): number[] {
  if (checkBysMarkerIsAlarm(data)) {
    if (checkIs4GMarker(data.MarkerType)) {
      const { AlarmReporting } = data.markerInfo ?? {}
      const { locate } = AlarmReporting ?? {}
      if (checkGpsIsValidWith4gMarker(locate)) {
        return getGCJ02LngLat(locate!)
      }
    }

    const updateInfo = data.updateInfo as DeviceUpdateInfo | undefined
    if (updateInfo?.StatusInfo?.isValidPosition && updateInfo?.GPS) {
      return getGCJ02LngLat(updateInfo.GPS)
    }
  }

  return getGCJ02LngLat(data)
}

export type getMapStyleMethod = (mapStyleType: MapStyleName, locale: string) => maplibreGl.StyleSpecification

// 使用高德地图 https://webst04.is.autonavi.com
// https://lbs.amap.com/api/javascript-api/example/map-lifecycle/map-create-destroy
// 街道图层只有一个图层，卫星影像图层是底图+标注图
export function useGaodeMapStyle(): getMapStyleMethod {
  const mapTypes = {
    streets: 'webrd',
    satellite: 'webst',
    satelliteSign: 'wprd',
  }
  const sn = Math.floor(Math.random() * 4 + 1)
    .toString()
    .padStart(2, '0')

  const satelliteSignUrl = `https://${mapTypes.satelliteSign}${sn}.is.autonavi.com/appmaptile?`
  const params = '&x={x}&y={y}&z={z}'

  const path = `${window.location.origin}/mapbox`
  const glyphs = `${path}/glyphs/{fontstack}/{range}.pbf`
  const sprite = `${path}/sprite/sprite`
  const commonOptions = {
    version: 8,
    glyphs: glyphs,
    sprite: sprite,
  }
  const sourceCommonOptions = {
    type: 'raster',
    tileSize: 256,
    minzoom: 1,
    maxzoom: 18,
  }

  return (mapStyleType: MapStyleName, locale: string) => {
    const baseUrl = `https://${mapTypes[mapStyleType]}${sn}.is.autonavi.com/appmaptile?`
    const langTypeKey = mapStyleType + locale
    const lang = locale == appLang.enUs ? 'en' : 'zh_cn'

    const mapStyles = {
      // 街道地图图层
      [MapStyleName.Streets]: {
        ...commonOptions,
        sources: {
          [mapStyleType]: {
            ...sourceCommonOptions,
            tiles: [`${baseUrl}lang=${lang}&size=1&scale=1&style=8${params}`],
          },
        },
        layers: [
          {
            id: mapStyleType,
            type: 'raster',
            source: mapStyleType,
          },
        ],
      },
      // 卫星影像图层
      [MapStyleName.Satellite]: {
        ...commonOptions,
        sources: {
          [mapStyleType]: {
            ...sourceCommonOptions,
            tiles: [`${baseUrl}style=6${params}`],
          },
          [langTypeKey]: {
            ...sourceCommonOptions,
            tiles: [
              `${satelliteSignUrl}lang=${lang}&size=1&$scl=1&style=8${params}`,
            ],
          },
        },
        layers: [
          {
            id: mapStyleType,
            type: 'raster',
            source: mapStyleType,
          },
          {
            id: langTypeKey,
            type: 'raster',
            source: langTypeKey,
          },
        ],
      }
    }

    return mapStyles[mapStyleType] as maplibreGl.StyleSpecification
  }
}

// 使用天地图 `https://t0.tianditu.gov.cn/DataServer?tk=${tiandituKey}`
export function useTiandituMapStyle(tiandituKey: string): getMapStyleMethod {
  // 地图瓦片数据和中/英文名称标记类型名称
  const mapTypes = {
    streets: 'vec_w',
    satellite: 'img_w',
    ['streets' + appLang.zhCN]: 'cva_w',
    ['satellite' + appLang.zhCN]: 'cia_w',
    ['streets' + appLang.enUs]: 'eva_w',
    ['satellite' + appLang.enUs]: 'eia_w',
  }

  const baseUrl = `/DataServer?tk=${tiandituKey}`
  const params = '&x={x}&y={y}&l={z}'

  const path = `${window.location.origin}/mapbox`
  const glyphs = `${path}/glyphs/{fontstack}/{range}.pbf`
  const sprite = `${path}/sprite/sprite`
  const commonOptions = {
    version: 8,
    glyphs: glyphs,
    sprite: sprite,
  }
  const sourceCommonOptions = {
    type: 'raster',
    tileSize: 256,
    minzoom: 1,
    maxzoom: 18,
  }

  return (mapStyleType: MapStyleName, locale: string) => {
    const mapType = mapTypes[mapStyleType]
    const langTypeKey = mapStyleType + locale
    const langType = mapTypes[langTypeKey]

    return {
      ...commonOptions,
      sources: {
        [mapStyleType]: {
          ...sourceCommonOptions,
          tiles: [`${baseUrl}&T=${mapType}${params}`],
        },
        [langTypeKey]: {
          ...sourceCommonOptions,
          tiles: [
            `${baseUrl}&T=${langType}${params}`,
          ],
        },
      },
      layers: [
        {
          id: mapStyleType,
          type: 'raster',
          source: mapStyleType,
        },
        {
          id: langTypeKey,
          type: 'raster',
          source: langTypeKey,
        },
      ],
    } as maplibreGl.StyleSpecification
  }
}

// 地图绽放等信息控件
export class MapInfoControl implements maplibreGl.IControl {
  map: maplibreGl.Map | undefined
  container: HTMLElement
  name = 'map-info-control'

  constructor() {
    this.container = document.createElement('div')
    this.container.className = `maplibregl-ctrl maplibregl-ctrl-group ${this.name}`
  }

  onAdd(map: maplibreGl.Map) {
    this.map = map

    this.container.textContent = ''
    const zoom = document.createElement('div')
    zoom.className = 'px-1 zoom-info'
    zoom.textContent = this.map.getZoom().toFixed(1)
    this.container.appendChild(zoom)

    map.on('zoom', () => {
      zoom.textContent = map.getZoom().toFixed(1)
    })

    return this.container
  }

  onRemove() {
    this.container.parentNode?.removeChild(this.container)
    this.map = undefined
  }
}


export interface MapStyleControlOptions {
  currentStyle?: MapStyleName
  getMapStyle?: getMapStyleMethod
  onChanged?: (currentStyle: MapStyleName) => void
}

// 地图样式切换组件
export class MapStyleControl implements maplibreGl.IControl {
  map: maplibreGl.Map | undefined
  container: HTMLElement
  name = 'map-style-control'
  app?: any
  options: MapStyleControlOptions & Required<Pick<MapStyleControlOptions, 'getMapStyle'>>
  style?: maplibreGl.StyleSpecification

  _initVueApp() {
    const self = this
    this.app = createApp({
      data() {
        return {
          currentStyle: self.options.currentStyle,
        }
      },
      computed: {
        btnImg() {
          return this.currentStyle === MapStyleName.Satellite ? streetPng : satellitePng
        },
        title() {
          return this.currentStyle === MapStyleName.Satellite ? this.$t('maps.switchStreetLayer') : this.$t('maps.switchSatelliteLayer')
        },
      },
      methods: {
        switchMapStyle() {
          self.options.currentStyle = this.currentStyle
          const style = self.options.getMapStyle(this.currentStyle!, this.$i18n.locale)
          safeSetStyle(self.map!, style)
          self.options.onChanged?.(this.currentStyle)
        },
        handleClick() {
          if (this.currentStyle === MapStyleName.Satellite) {
            this.currentStyle = MapStyleName.Streets
          } else {
            this.currentStyle = MapStyleName.Satellite
          }
          this.switchMapStyle()
        },
      },
      watch: {
        '$i18n.locale'() {
          this.switchMapStyle()
        },
      },
      render() {
        return h(QBtn, {
          icon: `img:${this.btnImg}`,
          dense: true,
          flat: true,
          unelevated: true,
          size: '12px',
          title: this.title,
          class: `${self.name}-btn`,
          onClick: this.handleClick
        })
      }
    })
    this.app.use(i18n)
    this.app.use(Quasar)
  }

  constructor(options?: MapStyleControlOptions) {
    this.container = document.createElement('div')
    this.container.className = `maplibregl-ctrl maplibregl-ctrl-group ${this.name}`
    this.options = Object.assign({
      currentStyle: MapStyleName.Satellite,
      getMapStyle: useGaodeMapStyle(),
    }, options)

    this._initVueApp()
  }

  onAdd(map: maplibreGl.Map) {
    this.map = map
    this.app?.mount(this.container)
    return this.container
  }

  onRemove() {
    this.app?.unmount()
    this.app = undefined
    this.container.parentNode?.removeChild(this.container)
    this.map = undefined
  }
}

// 加载并添加一个图片到地图上
// src可能是一个base64的图片数据，必须指定名称
export async function loadAndAddImage(map: maplibregl.Map, src: string, name: string): Promise<boolean> {
  return new Promise((resolve, reject) => {
    if (map.hasImage(name)) {
      return resolve(true)
    }

    map.loadImage(src)
      .then((res) => {
        // 再做一次判断
        if (map.hasImage(name)) {
          return resolve(true)
        }
        map.addImage(name, res.data)
        resolve(true)
      })
      .catch(reject)
  })
}

// 跳转到地图指定坐标，有动画过程
export function flyToMap(map: maplibreGl.Map, lngLat: LngLatLike, options?: maplibreGl.FlyToOptions) {
  // 禁用地图相关操作
  map.doubleClickZoom.disable()

  const opts: maplibreGl.FlyToOptions = {
    center: lngLat,
    ...options,
  }
  map.flyTo(opts)

  map.doubleClickZoom.enable()
}

export interface GpsTrackPlayerControlOptions {
  getTrackPopupHtml?: (data: TracePlayerData) => string
  pointInter?: number
  pointSize?: number
  pointColor?: string
  routeWidth?: number
  routeColor?: string
  playedColor?: string
  otherRouteColor?: string
  otherPointColor?: string
}

// 轨迹回放插件
export class GpsTrackPlayerControl<T extends Record<string, any> = any> implements maplibreGl.IControl {
  map: maplibreGl.Map | undefined
  container: HTMLElement
  name = 'gps-track-player'
  // 原始坐标集
  coordinates: Array<[number, number]> = []
  // 额外展示轨迹数据的坐标集
  otherCoordinates: Array<[number, number]> = []
  // 补充模拟坐标后的坐标集，动画使用，相对平滑些
  alongCoordinates: Array<[number, number]> = []
  data: Array<TracePlayerData<T>> = []
  // 额外展示轨迹数据集合
  otherData: Array<TracePlayerData<T>> = []
  isPlaying = false
  marker?: maplibreGl.Marker
  idx = 0
  trackLineLayerId = 'gps-track-line'
  closeMapByMySelf = false

  get trackLinePointId() { return this.trackLineLayerId + '--point' }

  get trackLineArrowId() { return this.trackLineLayerId + '--arrow' }

  // 巡查点名字图层id
  get trackPointNameId() { return this.trackLineLayerId + '--name' }

  options: Required<GpsTrackPlayerControlOptions>

  app?: any // vue app

  _initVueApp() {
    const self = this
    this.app = createApp({
      data() {
        return {
          status: 0, // 0: 就绪，1：开始，2：暂停
        }
      },
      computed: {
        isPlaying() {
          return this.status === 1
        },
        isPaused() {
          return this.status === 2
        },
        isStoped() {
          return this.status === 0
        },
      },
      methods: {
        play() {
          this.status = 1
          self.play()
        },
        pause() {
          this.status = 2
          self.pause()
        },
        stop() {
          this.status = 0
          self.stop()
        },
      },
      render() {
        return h(QBtnGroup, {}, () => [
          h(QBtn, {
            icon: 'mdi-play',
            color: 'accent',
            class: 'play-btn',
            title: this.$t('maps.play'),
            onClick: this.play,
            disable: this.isPlaying,
          }),
          h(QBtn, {
            icon: 'mdi-pause',
            color: 'accent',
            class: 'pause-btn',
            title: this.$t('maps.pause'),
            onClick: this.pause,
            disable: !this.isPlaying,
          }),
          h(QBtn, {
            icon: 'mdi-stop',
            color: 'accent',
            class: 'stop-btn',
            title: this.$t('maps.stop'),
            onClick: this.stop,
            disable: this.isStoped,
          }),
        ])
      }
    })
    this.app.use(i18n)
    this.app.use(Quasar)
  }

  constructor(options?: Partial<GpsTrackPlayerControlOptions>) {
    this.options = Object.assign({
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      getTrackPopupHtml: (data: TracePlayerData<T>) => '',
      pointInter: 60,
      pointSize: 4,
      pointColor: '#027BE3',
      routeWidth: 2,
      routeColor: '#4DD0E1',
      playedColor: '#304FFE',
      otherRouteColor: '#ff5722',
      otherPointColor: '#F44336'
    }, options)
    this.container = document.createElement('div')
    this.container.className = `maplibregl-ctrl maplibregl-ctrl-group ${this.name}-control`

    this._initVueApp()
  }

  onAdd(map: maplibregl.Map) {
    this.map = map
    // this.addGpsTrackArrowImage()
    if (!this.app) {
      this._initVueApp()
    }
    this.app!.mount(this.container)

    return this.container
  }

  onRemove() {
    this.close()
    this.app?.unmount()
    this.app = undefined
    this.container?.parentNode?.removeChild(this.container)
    this.map = undefined
  }

  // 加载轨迹路线方向图片
  addGpsTrackArrowImage() {
    if ((this.map as maplibreGl.Map).hasImage('gpsTrackArrow')) {
      return Promise.resolve(true)
    }

    return import('@assets/map/gpsTrackArrow.png')
      .then((res) => {
        return loadAndAddImage(this.map as maplibreGl.Map, res.default, 'gpsTrackArrow')
      })
      .catch((error) => {
        log.error('addGpsTrackArrowImage error', error)
        return false
      })
  }

  // 生成轨迹路线图层
  addTrackLayers() {
    if (!this.map) {
      log.error('GpsTrackPlayerControl can not add layers, the map not found')
      return
    }

    // 计算每个坐标之间的距离，每隔{inter}米补充一个坐标，以便动画能平滑过渡
    // 补充坐标的距离间隔，间隔越小，动画速度越慢
    let inter = this.options.pointInter
    // 补充后的坐标集
    const alongCoordinates: Array<[number, number]> = [this.coordinates[0]]
    // 轨迹点的集合
    const trackPoints: Array<GeoJSON.Feature<GeoJSON.Point, GeoJsonProperties>> = []

    for (let i = 0; i < this.otherCoordinates.length; i++) {
      const from = this.otherCoordinates[i]
      const pointForm = point(from, { data: this.otherData[i], displayName: this.otherData[i].displayName, pointColor: this.options.otherPointColor })
      trackPoints.push(pointForm)
    }

    for (let i = 0; i < this.coordinates.length - 1; i++) {
      const from = this.coordinates[i]
      const to = this.coordinates[i + 1]
      const pointForm = point(from, { data: this.data[i], displayName: this.data[i].displayName, pointColor: this.options.pointColor })

      trackPoints.push(pointForm)

      // 设置的单位参数无效？？？默认为千米
      const dis = distance(pointForm, point(to)) * 1000
      if (dis > 6000) {
        inter = Math.floor(dis / 100)
      }
      const count = Math.floor(dis / inter)
      // 生成两个坐标之间的线段
      const line = lineString([from, to])
      // 按间隔获取线段上指定距离的坐标，添加到新的坐标集中
      for (let k = 1; k <= count; k++) {
        const p = along(line, (k * inter) / 1000)
        alongCoordinates.push(p.geometry.coordinates as [number, number])
      }

      // 将线段的结束坐标添加到坐标集中
      alongCoordinates.push(to)
    }
    const lastIndex = this.coordinates.length - 1
    trackPoints.push(point(this.coordinates[lastIndex], { data: this.data[lastIndex], displayName: this.data[lastIndex].displayName, pointColor: this.options.pointColor}))

    // 保存补充后的坐标集，动画使用
    this.alongCoordinates = alongCoordinates
    // 生成新的轨迹路线
    const alongLine = lineString(alongCoordinates, {
      lineColor: this.options.routeColor,
    })
    // 已经播放完的轨迹路线
    const playedLine = lineString([alongCoordinates[0], alongCoordinates[0]], {
      lineColor: this.options.playedColor,
    })
    // 其他轨迹
    // 不运动的轨迹不需要连线
    // const otherTrace = lineString(this.otherCoordinates, { lineColor: this.options.otherRouteColor })

    const trackLineFeatures: Array<GeoJSON.Feature<GeoJSON.LineString, GeoJsonProperties>> = [
      alongLine,
      playedLine,
      // otherTrace,
    ]

    const mapAddSource = (id: string, features: Array<GeoJSON.Feature<GeoJSON.LineString | GeoJSON.Point, GeoJsonProperties>>) => {
      this.map?.addSource(id, {
        type: 'geojson',
        data: {
          type: 'FeatureCollection',
          features,
        },
      })
    }
    // 轨迹线路图层
    mapAddSource(this.trackLineLayerId, trackLineFeatures)

    const mapAddLayer = (id, type: 'line' | 'circle' | 'symbol', layout: Record<string, any> = {}, paint: Record<string, any> = {}) => {
      this.map?.addLayer({
        id,
        type,
        source: id,
        layout,
        paint,
      })
    }
    const lineLayout = {
      'line-join': 'round',
      'line-cap': 'round',
    }
    const linePaint = {
      'line-color': ['get', 'lineColor'],
      'line-width': this.options.routeWidth,
    }
    mapAddLayer(this.trackLineLayerId, 'line', lineLayout, linePaint)

    // // 添加轨迹线路方向箭头
    // this.map.addLayer({
    //   id: this.trackLineArrowId,
    //   type: 'symbol',
    //   source: this.trackLineLayerId,
    //   layout: {
    //     'symbol-placement': 'line',
    //     'icon-image': 'gpsTrackArrow',
    //     'icon-offset': [0, 0],
    //     'icon-rotate': 90,
    //     'icon-size': 0.6,
    //   },
    // })

    // 轨迹点图层
    mapAddSource(this.trackLinePointId, trackPoints)

    const pointPaint = {
      'circle-radius': this.options.pointSize,
      'circle-color': ['get', 'pointColor'],
    }
    mapAddLayer(this.trackLinePointId, 'circle', {}, pointPaint)

    // 绘制名字图层
    mapAddSource(this.trackPointNameId, trackPoints)
    const textLayout = {
      'text-field': ['get', 'displayName'],
      'text-size': 12,
      'text-offset': [0, 1],
    }
    const textPaint = {
      'text-halo-color': '#fff',
      'text-halo-width': 1,
    }
    mapAddLayer(this.trackPointNameId, 'symbol', textLayout, textPaint)

    // Create a popup, but don't add it to the map yet.
    const popup = new maplibreGl.Popup({
      closeButton: false,
      closeOnClick: true
    })
    const bindMapClickEvent = (evt: keyof MapLayerEventType, id: string) => {
      this.map?.on(evt, id, (e) => {
        // @ts-ignore
        const feature = e.features[0] as GeoJSON.Feature<GeoJSON.Point, GeoJsonProperties>
        const coordinates = feature.geometry.coordinates.slice() as maplibreGl.LngLatLike
        const data = JSON.parse(feature.properties!.data)
        const description = this.options.getTrackPopupHtml(data)
        popup.setLngLat(coordinates).setHTML(description).addTo(this.map!)
      })
    }

    bindMapClickEvent('click', this.trackLinePointId)

    const bindMapMouseEvent = (evt: keyof MapLayerEventType, id: string, cursor: string) => {
      this.map?.on(evt, id, () => {
        this.map!.getCanvas().style.cursor = cursor
      })
    }
    bindMapMouseEvent('mouseenter', this.trackLinePointId, 'pointer')
    bindMapMouseEvent('mouseleave', this.trackLinePointId, 'default')
  }

  // 删除图层
  removeTrackLayers() {
    if (!this.map) {
      return
    }
    if (this.map.getLayer(this.trackLineLayerId)) {
      this.map.removeLayer(this.trackLineLayerId)
    }
    if (this.map.getLayer(this.trackLinePointId)) {
      this.map.removeLayer(this.trackLinePointId)
    }
    // if (this.map.getLayer(this.trackLineArrowId)) {
    //   this.map.removeLayer(this.trackLineArrowId)
    // }
    if (this.map.getSource(this.trackLineLayerId)) {
      this.map.removeSource(this.trackLineLayerId)
    }
    if (this.map.getSource(this.trackLinePointId)) {
      this.map.removeSource(this.trackLinePointId)
    }
  }

  // 创建轨迹动画的marker
  createMarker() {
    if (!this.marker) {
      this.marker = window.trackMarker = new maplibreGl.Marker()
    }

    this.marker.setLngLat(this.coordinates[0]).addTo(this.map as maplibreGl.Map)
  }

  // 设置定位轨迹数据，加载图层和显示对应的动画控件
  setData(data: Array<TracePlayerData<T>>, otherData: Array<TracePlayerData<T>> = []) {
    this.data = data
    this.coordinates = this.data.map((item) => item.lonLat)
    this.otherData = otherData
    this.otherCoordinates = this.otherData.map((item) => item.lonLat)

    this.addTrackLayers()
    this.createMarker()

    // 计算地图边界
    const bounds = this.coordinates.reduce((bounds, coord) => {
      return bounds.extend(coord)
    }, new maplibreGl.LngLatBounds(this.coordinates[0], this.coordinates[0]))
    this.map!.fitBounds(bounds, {
      padding: 30,
    })
  }

  // 更新图层数据源方法
  updateSourceData(update: (data: GeoJSON.FeatureCollection) => GeoJSON.GeoJSON) {
    if (!this.map) {
      return
    }
    const source = this.map.getSource(this.trackLineLayerId) as GeoJSONSource | undefined
    if (!source) {
      return
    }

    // @ts-ignore
    const data = update(source.serialize().data)
    // this.map.setZoom(16)
    // this.map.setCenter(data.features[1].geometry.coordinates[data.features[1].geometry.coordinates.length - 1])
    source.setData(data)
  }

  // 开始播放轨迹动画
  play() {
    this.isPlaying = true
    const map = this.map as maplibreGl.Map

    // 播放结束后，再点击播放则会触发该条件，需要重置起始坐标点
    if (this.idx >= this.alongCoordinates.length) {
      this.idx = 0
      this.updateSourceData((data: GeoJSON.FeatureCollection) => {
        const geometry = data.features[1].geometry as GeoJSON.LineString
        geometry.coordinates = [this.alongCoordinates[0]]
        return data
      })
    }

    const playStopNotify = (msg: string, notShow: boolean = true) => {
      if (notShow) return
      Notify.create({
        message: msg,
        position: 'top',
        type: 'positive',
      })
    }

    const animateMarker = () => {
      // 暂停或结束了
      if (!this.isPlaying) {
        playStopNotify(i18n.global.t('message.paused'), this.closeMapByMySelf)
        return
      }
      // 播放完成
      if (this.idx >= this.alongCoordinates.length) {
        this.isPlaying = false
        playStopNotify(i18n.global.t('message.playFinished'))
        return
      }

      // 设置marker坐标
      const coordinate = this.alongCoordinates[this.idx]
      this.marker?.setLngLat(coordinate).addTo(map)

      // 更新已经播放过的路线
      this.updateSourceData((data: GeoJSON.FeatureCollection) => {
        const geometry = data.features[1].geometry as GeoJSON.LineString
        geometry.coordinates.push(coordinate)
        return data
      })
      // map.panTo(coordinate)

      this.idx++
      requestAnimationFrame(animateMarker)
    }

    // Start the animation.
    requestAnimationFrame(animateMarker)
  }

  // 暂停播放
  pause() {
    this.isPlaying = false
  }

  // 结束播放
  stop() {
    this.isPlaying = false
    this.closeMapByMySelf = true
    this.idx = 0
    const coordinate = this.coordinates[0]
    this.marker?.setLngLat(coordinate).addTo(this.map as maplibreGl.Map)
    this.updateSourceData((data: GeoJSON.FeatureCollection) => {
      const geometry = data.features[1].geometry as GeoJSON.LineString
      geometry.coordinates = [coordinate]
      return data
    })
  }

  // 关闭轨迹播放，隐藏控件
  close() {
    this.isPlaying = false
    this.idx = 0
    this.marker?.remove()
    this.marker = undefined
    this.removeTrackLayers()
    this.data = []
    this.coordinates = []
  }
}

export class GpsTrackIntroControl implements maplibreGl.IControl {
  domNode?: HTMLElement
  $container?: HTMLElement

  constructor(domNode: HTMLElement) {
    this.domNode = domNode
  }

  onAdd(/*map: maplibreGl.Map*/): HTMLElement {
    const container = document.createElement('div')
    container.className = 'maplibregl-ctrl maplibregl-ctrl-group gps-track-intro-control'
    this.domNode && container.appendChild(this.domNode!)
    this.$container = container

    return container
  }

  onRemove(/*map: maplibreGl.Map*/) {
    this.$container?.parentNode?.removeChild(this.$container!)
    this.$container = undefined
    this.domNode = undefined
  }
}

// 设置地图图层样式，保留非raster类型的所有图层资源
export function safeSetStyle(map: maplibreGl.Map, newStyle: maplibreGl.StyleSpecification) {
  const originStyle = map.getStyle()

  const layers = originStyle.layers.filter(item => item.type !== 'raster')
  const sources = {}
  for (let k in originStyle.sources) {
    const item = originStyle.sources[k]
    if (item.type === 'raster') continue
    sources[k] = item
  }

  const style = cloneDeep(newStyle)
  style.layers = [...style.layers, ...layers]
  style.sources = { ...style.sources, ...sources }
  map.setStyle(style)
}

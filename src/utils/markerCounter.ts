import { BysMarker, Unit } from '@src/services/dataStore'
import { org } from '@ygen/org'
import IDbOrg = org.IDbOrg

interface IUnitChildrenMarkerCount {
  self?: number,//仅于自身的总界桩数
  sum?: number,//包含子级单位的界桩数
  selfInstalled?: number // 仅单位下已安装的界桩数量
  sumInstalled?: number // 包含子级单位下已安装的界桩数量
  selfInstallStone?: number // 仅单位下只安装石桩的数量
  sumInstallStone?: number // 包含单位下只安装石桩的数量
}

// 储存界桩数量
const CounterMap: Map<string, IUnitChildrenMarkerCount> = new Map<string, IUnitChildrenMarkerCount>()

// 读取单位下界桩数量
export function getMarkerCount(rid: string): number {
  return CounterMap.get(rid)?.sum ?? 0
}

function getSubMarkerCountSum(rid: string): number {
  return getMarkerCount(rid)
}

export function getSubMarkerInstalledSum(rid: string): number {
  return CounterMap.get(rid)?.sumInstalled ?? 0
}

export function getSubMarkerInstallStoneSum(rid: string): number {
  return CounterMap.get(rid)?.sumInstallStone ?? 0
}

function setSubMarkerCountSum(rid: string, count: number) {
  const counter = CounterMap.get(rid)
  CounterMap.set(rid, { ...counter, sum: count })
}

export function getMarkerSelfInstalledCount(rid: string): number {
  return CounterMap.get(rid)?.selfInstalled ?? 0
}

// 设置单位下界桩已经安装设备数量
export function setMarkerSelfInstalledCount(rid: string, count: number): void {
  const counter = CounterMap.get(rid)
  CounterMap.set(rid, { ...counter, selfInstalled: count })
}

function setSubMarkerInstalledSum(rid: string, count: number) {
  const counter = CounterMap.get(rid)
  CounterMap.set(rid, { ...counter, sumInstalled: count })
}

function setSubMarkerInstallStoneSum(rid: string, count: number) {
  const counter = CounterMap.get(rid)
  CounterMap.set(rid, { ...counter, sumInstallStone: count })
}

// 设置单位下界桩只安装石桩数量
export function setMarkerSelfInstallStoneCount(rid: string, count: number): void {
  const counter = CounterMap.get(rid)
  CounterMap.set(rid, { ...counter, selfInstallStone: count })
}

export function getMarkerSelfInstallStoneCount(rid: string): number {
  return CounterMap.get(rid)?.selfInstallStone ?? 0
}

// 设置单位下界桩数量
export function getMarkerSelfCount(rid: string): number {
  return CounterMap.get(rid)?.self ?? 0
}

// 设置单位下界桩数量
export function setMarkerSelfCount(rid: string, count: number): void {
  const counter = CounterMap.get(rid)
  CounterMap.set(rid, { ...counter, self: count })
}

export function deleteMarkerCount(rid: string): void {
  CounterMap.delete(rid)
}

// 统计单个单位下的界桩数量
export function countUnitMarker(rid: string) {
  const markers = BysMarker.getDataList().filter(item => item.OrgRID === rid)
  setMarkerSelfCount(rid, markers.length)

  // 统计界桩安装信息
  let installDevice = 0
  let installStone = 0
  for (let i = 0; i < markers.length; i++) {
    const item = markers[i]

    if (item.HasInstallDevice && item.HasInstallStone) {
      installDevice++
    } else if (item.HasInstallStone) {
      installStone++
    }
  }

  setMarkerSelfInstalledCount(rid, installDevice)
  setMarkerSelfInstallStoneCount(rid, installStone)
}

// 按单位统计界桩数量
export function countMarkers(): void {
  const markers = BysMarker.getDataList()
  const subtotals: { [key: string]: number } = {}
  const installed: { [key: string]: number } = {} // 单位下已安装的界桩数量
  const installStone: { [key: string]: number } = {} // 单位下只安装石桩的数量

  for (let i = 0; i < markers.length; i++) {
    const item = markers[i]
    const OrgRID = item.OrgRID + ''
    subtotals[OrgRID] = subtotals[OrgRID] ?? 0
    subtotals[OrgRID]++

    // 统计界桩安装数据
    if (item.HasInstallDevice && item.HasInstallStone) {
      installed[OrgRID] = installed[OrgRID] ?? 0
      installed[OrgRID]++
    } else if (item.HasInstallStone) {
      installStone[OrgRID] = installStone[OrgRID] ?? 0
      installStone[OrgRID]++
    }
  }

  for (let key in subtotals) {
    setMarkerSelfCount(key, subtotals[key])
  }

  for (let key in installed) {
    setMarkerSelfInstalledCount(key, installed[key])
  }

  for (let key in installStone) {
    setMarkerSelfInstallStoneCount(key, installStone[key])
  }

  updateAllParOrgNode()
}

export function updateAllParOrgNode() {
  const alreadyDealUnit = new Map<string, boolean>()
  //上级单位+=子级单位
  const unitData = Unit.getDataList()

  const getChildrenMarkerCount = (childUnit: IDbOrg) => {
    if (alreadyDealUnit.get(childUnit.RID ?? '')) { return }
    alreadyDealUnit.set(childUnit.RID ?? '', true)
    let totalCount = getMarkerSelfCount(childUnit.RID ?? '')
    let installDeviceCount: number = getMarkerSelfInstalledCount(childUnit.RID ?? '')
    let installStoneCount: number = getMarkerSelfInstallStoneCount(childUnit.RID ?? '')
    for (let unit of unitData) {
      if (unit.OrgRID === childUnit.RID) {
        if (!alreadyDealUnit.get(unit.RID ?? '')) {
          getChildrenMarkerCount(unit)
        }

        totalCount += getSubMarkerCountSum(unit.RID ?? '')
        installDeviceCount += getSubMarkerInstalledSum(unit.RID ?? '')
        installStoneCount += getSubMarkerInstallStoneSum(unit.RID ?? '')
      }
    }
    setSubMarkerCountSum(childUnit.RID ?? '', totalCount)
    setSubMarkerInstallStoneSum(childUnit.RID ?? '', installStoneCount)
    setSubMarkerInstalledSum(childUnit.RID ?? '', installDeviceCount)
  }

  for (let unitDatum of unitData) {
    getChildrenMarkerCount(unitDatum)
  }
}

import sha256 from 'crypto-js/sha256'
import Base64 from 'crypto-js/enc-base64'
import { lib, enc, mode, AES, MD5 } from 'crypto-js'
import pako from 'pako'

//用户登录密码,为base64(sha256(user_name+user_password)),系统不保存真实的密码
export function encodeUserPassword(name: string, pwd: string): string {
  return Base64.stringify(sha256(name + pwd))
}

// login pass base64(sha256(base64(sha256(username+userpass))+LoginTimeStr))
export function encodeLoginPassword(name: string, pwd: string, timeStr: string): string {
  return Base64.stringify(sha256(Base64.stringify(sha256(name + pwd)) + timeStr))
}

// 图像hash计算 hash=base64(sha256(file_content_binary)
export function imageHash(fileContent): string {
  return Base64.stringify(sha256(fileContent))
}

export function String2UInt8Array(encode: string): Uint8Array {
  return Uint8Array.from(encode, s => s.charCodeAt(0))
}

export function UInt8Array2String(decode: Uint8Array): string {
  let str = ''
  decode.forEach(el => str += String.fromCharCode(el))
  return str
}

export function toBase64(bytes: Uint8Array): string {
  const word = lib.WordArray.create((bytes.buffer))
  return Base64.stringify(word)
}

export function pakoDeflate(str: pako.Data): Uint8Array {
  return pako.deflateRaw(str)
}

export function encrypt(data: string, key, iv) {
  let encrypted = AES.encrypt(data, key, { iv: iv, mode: mode.CBC});
  return  encrypted.ciphertext.toString().toUpperCase();;
}

export function decrypt(data: string, key, iv) {
  let dataHexStr = enc.Hex.parse(data);
  let dataBase64 = enc.Base64.stringify(dataHexStr);
  // 接收的数据是 base64
  let decrypt = AES.decrypt(dataBase64, key, { iv: iv, mode: mode.CBC});
  let decryptedStr = decrypt.toString(enc.Utf8);
  return decryptedStr.toString();
}

// 给定字符串数组，返回数组元素字符串拼接后的md5值
export function md5StringArray(arr: string[]): string {
  const md5 = MD5(arr.join(','))
  return md5.toString(enc.Hex)
}

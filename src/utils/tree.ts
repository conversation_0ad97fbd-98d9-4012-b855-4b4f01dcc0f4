import { org } from '@ygen/org'
import { DefUuid } from '@src/config'
import { DataName } from '@store/data'
import { bysdb } from '@ygen/bysdb'
import { Store } from '@src/store'
import { Bys<PERSON>ark<PERSON>, Controller, Unit } from '@services/dataStore'
import { checkBysMarkerIsAlarm, isHaveMaintainPerm } from './common'
import { BysMarkerAndUpdateInfo, ControllerAndUpdateCmd } from './bysdb.type'
import {
  getMarkerCount,
  getSubMarkerInstalledSum,
  getSubMarkerInstallStoneSum
} from '@utils/markerCounter'
import { i18n } from '@boot/i18n'
import IDbOrg = org.IDbOrg

export const deviceTreeId = 'device-tree'

// 更新树形viewport可视区
export function redrawViewport(tree) {
  if (!tree || !tree.ext.grid) {
    return
  }
  tree.adjustViewportSize()
  tree.redrawViewport()
}

// a,b为树节点对象
export function treeSortMethod(
  a: { [key: string]: any },
  b: { [key: string]: any }
): number {
  // controllerFirst:true是控制器在前，界桩在后，false则相反
  const { descending, controllerFirst } = Store.state.Settings
  const aCustomType = a.data.customType
  const bCustomType = b.data.customType

  // a/b均为单位节点，按单位排序值排序
  if (aCustomType === DataName.Unit && bCustomType === DataName.Unit) {
    const aData = (Unit.getData(a.key) || {}) as org.IDbOrg
    const bData = (Unit.getData(b.key) || {}) as org.IDbOrg
    let aSortValue: number | string = aData.SortValue as number
    let bSortValue: number | string = bData.SortValue as number
    // 如果排序值相同，则先按排序值排序再按简称排序
    if (aSortValue === bSortValue) {
      aSortValue = aData.ShortName as string
      bSortValue = bData.ShortName as string
    }

    // 降序排序
    if (descending) {
      return aSortValue > bSortValue ? -1 : aSortValue === bSortValue ? 0 : 1
    }
    // 升序排序
    return aSortValue > bSortValue ? 1 : aSortValue === bSortValue ? 0 : -1
  }

  // a/b均为控制器，按ID排序
  if (
    aCustomType == DataName.Controller &&
    bCustomType === DataName.Controller
  ) {
    const aSortValue = ((Controller.getData(a.key) ||
      {}) as bysdb.IDbController).ControllerHWID as number
    const bSortValue = ((Controller.getData(b.key) ||
      {}) as bysdb.IDbController).ControllerHWID as number
    // 升序
    if (!descending) {
      return aSortValue === bSortValue ? 0 : aSortValue > bSortValue ? 1 : -1
    }
    return aSortValue === bSortValue ? 0 : aSortValue > bSortValue ? -1 : 1
  }

  // a/b均为界桩，按ID排序
  if (aCustomType == DataName.BysMarker && bCustomType === DataName.BysMarker) {
    const aSortValue = ((BysMarker.getData(a.key) || {}) as bysdb.IDbBysMarker)
      .MarkerHWID as number
    const bSortValue = ((BysMarker.getData(b.key) || {}) as bysdb.IDbBysMarker)
      .MarkerHWID as number
    // 升序
    if (!descending) {
      return aSortValue === bSortValue ? 0 : aSortValue > bSortValue ? 1 : -1
    }
    return aSortValue === bSortValue ? 0 : aSortValue > bSortValue ? -1 : 1
  }

  // a为单位节点，b为控制器/界桩节点
  if (aCustomType === DataName.Unit && bCustomType !== DataName.Unit) {
    return -1
  }
  // a为控制器/界桩节点，b为单位节点
  if (aCustomType !== DataName.Unit && bCustomType === DataName.Unit) {
    return 1
  }

  // a为控制器节点，b为界桩节点
  if (
    aCustomType == DataName.Controller &&
    bCustomType === DataName.BysMarker
  ) {
    return controllerFirst ? -1 : 1
  }
  // a为界桩节点，b为控制器节点
  if (
    aCustomType == DataName.BysMarker &&
    bCustomType === DataName.Controller
  ) {
    return controllerFirst ? 1 : -1
  }

  return 0
}

export function sortNodeChildren(node, cmp: Function = treeSortMethod) {
  node.sortChildren(cmp, true)
}

export function sortTree(tree, cmp: Function = treeSortMethod) {
  tree?.rootNode?.sortChildren(cmp, true)
  redrawViewport(tree)
}

// 创建树节点title属性方法集
export function createUnitNodeTitle(data: org.IDbOrg): string {
  const RID = data.RID + ''
  let title: string = data.ShortName + ''

  // 只显示未安装设备的界桩模式下，只显示已安装的数量统计
  const showNotInstalledMarker = Store.state.Settings.showNotInstalledMarker
  if (showNotInstalledMarker) {
    const count: number = getMarkerCount(RID)
    if (count > 0) {
      title += `<span class='unit-badge'>${count}</span>`
    }
    const installStoneCount: number = getSubMarkerInstallStoneSum(RID)
    if (installStoneCount > 0) {
      title += `<span class='installed-stone-badge'>${installStoneCount}</span>`
    }
  }

  const installedCount: number = getSubMarkerInstalledSum(RID)
  if (installedCount > 0) {
    title += `<span class='installed-device-badge'>${installedCount}</span>`
  }

  return title
}

export function createUnitNodeTitleTooltip(data: org.IDbOrg): string {
  const RID = data.RID + ''
  let tooltip = ''
  const showNotInstalledMarker = Store.state.Settings.showNotInstalledMarker
  if (showNotInstalledMarker) {
    const count: number = getMarkerCount(RID)
    if (count > 0) {
      tooltip += `${i18n.global.t('markerStatistics.markerTotal')}: ${count}`
    }
    const installStoneCount: number = getSubMarkerInstallStoneSum(RID)
    if (installStoneCount > 0) {
      tooltip += `\n${i18n.global.t(
        'markerStatistics.notInstallDevice'
      )}: ${installStoneCount}`
    }
  }

  const installedCount: number = getSubMarkerInstalledSum(RID)
  if (installedCount > 0) {
    tooltip += `\n${i18n.global.t('markerStatistics.installed')}：${installedCount}`
  }

  return tooltip
}

// 有维护权限的账号，才显示电量百分比
export function createBysMarkerNodeTitle(data: BysMarkerAndUpdateInfo): string {
  let title: string = data.MarkerNo as string
  // const batteryPower = data.updateInfo?.StatusInfo?.batteryPower
  // if (isHaveMaintainPerm() && batteryPower) {
  //   const batteryLabel = calcMarkerPower(batteryPower + '')
  //   title += `(${batteryLabel})`
  // }
  return title
}

export function createControllerNodeTitle(
  data: ControllerAndUpdateCmd
): string {
  let title = data.ControllerNo as string
  if (isHaveMaintainPerm() && data.controllerState?.ChannelNo) {
    return (
      title +
      ` <span style="color:#027be3">[${data.controllerState?.ChannelNo}]</span>`
    )
  }
  return title
}

const CreateNodeTitleMethods = {
  [DataName.Unit]: createUnitNodeTitle,
  [DataName.BysMarker]: createBysMarkerNodeTitle,
  [DataName.Controller]: createControllerNodeTitle
}

export function getCreateNodeTitleMethod(dbName: DataName): Function {
  return CreateNodeTitleMethods[dbName] || (() => '')
}

const CreateNodeTooltipMethods = {
  [DataName.Unit]: createUnitNodeTitleTooltip
}

export function getCreateNodeTooltipMethod(dbName: DataName): Function {
  return CreateNodeTooltipMethods[dbName] || (() => '')
}

// 创建树节点对象方法集
export function createUnitNode(data: org.IDbOrg): { [key: string]: any } {
  // 如果是lazy懒加载，children必须是undefined或null
  const lazy = data.OrgRID !== DefUuid
  return {
    key: data.RID as string,
    title: createUnitNodeTitle(data),
    tooltip: createUnitNodeTitleTooltip(data),
    icon: false,
    children: lazy ? undefined : [],
    expanded: !lazy,
    selected: true,
    lazy,

    // custom props in node.data
    customType: DataName.Unit
  }
}

export function createBysMarkerStatusCls(data: BysMarkerAndUpdateInfo): string {
  if (!data.HasInstallStone) {
    return 'uninstall-stone'
  }
  if (!data.HasInstallDevice) {
    return 'uninstall'
  }

  const isAlarming = checkBysMarkerIsAlarm(data)
  if (isAlarming) {
    return data.removeAlarm ? 'removeWarning' : 'warning'
  }

  return 'marker-blue'
}

export function createBysMarkerNode(
  data: bysdb.IDbBysMarker
): { [key: string]: any } {
  return {
    key: data.RID as string,
    title: createBysMarkerNodeTitle(data),
    icon: 'iconfont bys-jiezhuang ' + createBysMarkerStatusCls(data),
    selected: true,
    customType: DataName.BysMarker,
    customMarkerType: data.MarkerType,
  }
}

export function createControllerNode(
  data: bysdb.IDbController
): { [key: string]: any } {
  return {
    key: data.RID as string,
    title: createControllerNodeTitle(data),
    icon: 'iconfont bys-base-station',
    selected: true,

    customType: DataName.Controller
  }
}

const CreateNodeMethods = {
  [DataName.Unit]: createUnitNode,
  [DataName.BysMarker]: createBysMarkerNode,
  [DataName.Controller]: createControllerNode
}

export function getCreateNodeMethod(dbName: DataName): Function {
  return CreateNodeMethods[dbName] || (() => undefined)
}

// 树节点操作的配置
export interface NodeActionOptions {
  [key: string]: any

  dbName: DataName
  mode?: string
}

// 更新树节点title
export function renderNodeTitle(node: any, title: string) {
  if (!node) {
    return
  }
  node.setTitle(title)
  node.renderTitle()
}

// 删除节点
export function deleteNode(tree, key: string) {
  if (!tree) {
    return
  }

  const node = tree.getNodeByKey(key)
  if (!node) {
    return
  }

  node.remove()
  // 更新viewport
  redrawViewport(tree)
}

// 插入新节点
export function insertNode(
  tree,
  data: { [key: string]: any },
  options?: NodeActionOptions
) {
  if (!tree) {
    return
  }

  const opts: NodeActionOptions = {
    parentKey: 'OrgRID',
    dbName: DataName.BysMarker,
    ...options
  }
  let parentNode = tree.getNodeByKey(data[opts.parentKey as string])
  if (!parentNode || (parentNode.lazy && !parentNode.children)) {
    return
  }
  const nodeData = getCreateNodeMethod(opts.dbName)(data)
  if (nodeData) {
    nodeData.selected = parentNode.isSelected()
    parentNode.addChildren(nodeData)
    sortNodeChildren(parentNode)
  }

  // 更新viewport
  redrawViewport(tree)
}

// 更新节点
export function updateNode(
  tree,
  data: { [key: string]: any },
  oldData: { [key: string]: any },
  options?: NodeActionOptions
) {
  if (!tree) {
    return
  }

  const opts: NodeActionOptions = {
    parentKey: 'OrgRID',
    dbName: DataName.BysMarker,
    mode: 'child',
    ...options
  }

  DataName.BysMarker
  const key = data.RID as string

  // 界桩可能从已安装设备变更为未安装设备，如果个人设置的"显示未安装界桩"设置没有选中，则需要从树中删除该节点
  const showNotInstalledMarker = Store.state.Settings.showNotInstalledMarker
  if (
    opts.dbName === DataName.BysMarker &&
    !showNotInstalledMarker &&
    !data.HasInstallDevice
  ) {
    const sourceNode = tree.getNodeByKey(key)
    sourceNode?.remove()
    return
  }

  const parentKey = data[opts.parentKey as string] as string
  const updateNodeTitle = node => {
    opts.icon && (node.icon = opts.icon)
    const title = getCreateNodeTitleMethod(opts.dbName)(data)
    node.tooltip = getCreateNodeTooltipMethod(opts.dbName)(data)
    renderNodeTitle(node, title)
  }

  // 移动节点到新的上级节点下
  const sourceNode = tree.getNodeByKey(key)
  // 找不到旧的节点，则创建新的节点
  if (!sourceNode) {
    insertNode(tree, data, opts)
    return
  }

  // 如果更新前、后的上级节点相同，则更新节点title，否则移动节点到新的上级节点下
  // 更新节点
  if (parentKey === (oldData[opts.parentKey as string] as string)) {
    updateNodeTitle(sourceNode)
    return
  }

  // 无法查找到新的上级节点，则删除该节点，如果上级是懒加载节点，则删除节点即可
  let targetParentNode = tree.getNodeByKey(parentKey)
  if (
    !targetParentNode ||
    (targetParentNode.lazy && !targetParentNode.children)
  ) {
    sourceNode.remove()
    return
  }

  // 将旧节点移动到新的上级节点
  sourceNode.moveTo(targetParentNode, opts.mode)
  sourceNode.setSelected(targetParentNode.isSelected())

  // 移动节点后，也需要更新title属性
  updateNodeTitle(sourceNode)

  sortNodeChildren(targetParentNode)

  // 更新viewport
  redrawViewport(tree)
}

// 缓存所有懒加载节点
// export const LazyNodes: { [key: string]: any } = window.LazyNodes = {}
//
// export function createTreeUnitNodes(orgList: Array<org.IDbOrg | null> = []): any[] {
//   const result: any[] = []
//   if (orgList.length === 0) {
//     return result
//   }
//
//   const processCache = {}
//
//   const addNode = (parentNode: { [key: string]: any }, orgItem: org.IDbOrg, itemPos: number) => {
//     if (orgItem.RID === DefUuid) return null
//
//     const currentNode = createUnitNode(orgItem)
//     processCache[orgItem.RID as string] = currentNode
//
//     // 缓存lazy标记节点
//     if (currentNode.lazy) {
//       LazyNodes[orgItem.RID as string] = []
//     }
//
//     if (parentNode) {
//       // 判断父节点是否为懒加载节点
//       if (parentNode.lazy) {
//         LazyNodes[orgItem.OrgRID as string].push(currentNode)
//       } else {
//         parentNode.children.push(currentNode)
//       }
//     } else {
//       result.push(currentNode)
//     }
//     orgList[itemPos] = null
//
//     return currentNode
//   }
//
//   //返回currentData的父级Node,可以为null
//   const addParent = (currentPos: number, currentData: org.IDbOrg) => {
//     //可能前面已经加入了
//     let parentNode = processCache[currentData.OrgRID as string]
//
//     if (parentNode) {
//       return parentNode
//     }
//
//     //在后面的找一遍
//     let parentPos = 0
//     let parentItem: org.IDbOrg | null = null
//     for (let i = currentPos + 1; i < orgList.length; i++) {
//       const item = orgList[i]
//
//       if (!item) continue
//
//       if (item.RID === currentData.OrgRID) {
//         parentPos = i
//         parentItem = item
//         break
//       }
//     }
//     //没有parent
//     if (parentPos === 0) return null
//     //循环添加上级
//     parentNode = addParent(parentPos, parentItem as org.IDbOrg)
//     //返回currentData的上级
//     return addNode(parentNode, parentItem as org.IDbOrg, parentPos)
//   }
//
//   for (let i = 0; i < orgList.length; i++) {
//     const idata = orgList[i]
//     // 已经处理过了
//     if (!idata) continue
//     // 忽略root
//     if (idata.RID === DefUuid) continue
//     // 添加parent
//     const parentNode = addParent(i, idata)
//     //添加自己
//     addNode(parentNode, idata, i)
//   }
//
//   return result
// }
//
// export function createSliceBysMarkerNodes(tree, dataList: Array<bysdb.IDbBysMarker>) {
//   const list: Array<bysdb.IDbBysMarker[]> = []
//   while (dataList.length) {
//     list.push(dataList.splice(0, Math.min(500, dataList.length)))
//   }
//   const it: Iterator<bysdb.IDbBysMarker[]> = list[Symbol.iterator]()
//   const process = (itRes: IteratorResult<bysdb.IDbBysMarker[]>) => {
//     if (itRes.done) {
//       return
//     }
//     const dataList = itRes.value
//     // 生成界桩节点数据
//     const childrenMap: { [key: string]: any } = {}
//     for (let i = 0; i < dataList.length; i++) {
//       const device = dataList[i]
//       const OrgRID = device.OrgRID as string
//       if (!childrenMap[OrgRID]) {
//         childrenMap[OrgRID] = []
//       }
//
//       const node = createBysMarkerNode(device)
//       childrenMap[OrgRID].push(node)
//     }
//
//     // 遍历当前界桩的上级节点数据，批量处理子节点数据
//     for (let k in childrenMap) {
//       const children = childrenMap[k]
//       let parentNode = tree.getNodeByKey(k)
//       if (!parentNode) {
//         parentNode = tree.rootNode
//       }
//
//       // 如果是懒加载节点，则缓存节点数据
//       if (parentNode.lazy) {
//         if (!LazyNodes[parentNode.key]) {
//           LazyNodes[parentNode.key] = []
//         }
//         LazyNodes[parentNode.key] = LazyNodes[parentNode.key].concat(children)
//
//         continue
//       }
//
//       parentNode.addChildren(children)
//       sortNodeChildren(parentNode)
//     }
//
//     window.setTimeout(() => {
//       process(it.next())
//     }, 0)
//   }
//   process(it.next())
// }
//
// export function createSliceControllerNodes(tree, dataList: Array<bysdb.IDbController>) {
//   window.setTimeout(() => {
//     // 生成控制器节点数据
//     const childrenMap: { [key: string]: any } = {}
//     for (let i = 0; i < dataList.length; i++) {
//       const data = dataList[i]
//       const OrgRID = data.OrgRID as string
//       if (!childrenMap[OrgRID]) {
//         childrenMap[OrgRID] = []
//       }
//
//       const node = createControllerNode(data)
//       childrenMap[OrgRID].push(node)
//     }
//
//     // 遍历当前界桩的上级节点数据，批量处理子节点数据
//     for (let k in childrenMap) {
//       const children = childrenMap[k]
//       let parentNode = tree.getNodeByKey(k)
//       if (!parentNode) {
//         parentNode = tree.rootNode
//       }
//
//       // 如果是懒加载节点，则缓存节点数据
//       if (parentNode.lazy) {
//         if (!LazyNodes[parentNode.key]) {
//           LazyNodes[parentNode.key] = []
//         }
//         LazyNodes[parentNode.key] = LazyNodes[parentNode.key].concat(children)
//
//         continue
//       }
//
//       parentNode.addChildren(children)
//       sortNodeChildren(parentNode)
//     }
//   }, 0)
// }

// 加载单位数据节点
export function createTreeUnitNodes(orgList: Array<org.IDbOrg> = []): any[] {
  const result: any[] = []
  if (orgList.length === 0) {
    return result
  }

  const processCache = {}
  const topDataList = orgList.filter(item => {
    let isTop = item.OrgRID === DefUuid
    if (isTop) {
      return true
    }
    return !orgList.some(org => org.RID === item.OrgRID)
  })
  const topDataChildren = orgList.filter(item =>
    topDataList.some(v => item.OrgRID === v.RID)
  )

  topDataList.forEach(item => {
    const node = createUnitNode(item)
    processCache[item.RID as string] = node
    result.push(node)
  })
  topDataChildren.forEach(item => {
    const node = createUnitNode(item)
    const parent = processCache[item.OrgRID as string]
    if (parent) {
      !parent.lazy && !parent.children && (parent.children = [])
      parent.children?.push(node)
    }
  })

  return result
}

export function lazyLoadUnitNodes(key: string): any[] {
  const result: any[] = []
  const orgList: org.IDbOrg[] = Unit.getDataList()
  const children = orgList.filter(item => item.OrgRID === key)

  children.forEach(item => {
    result.push(createUnitNode(item))
  })

  return result
}

export function updateUnitCounter(data: org.IDbOrg) {
  // @ts-ignore
  const tree = $.ui.fancytree.getTree(`#${deviceTreeId}`)
  updateNode(tree, data, data, {
    dbName: DataName.Unit
  })
}

export function updateParentsNodeTitle(tree, data: IDbOrg) {
  const sourceNode: Fancytree.FancytreeNode | undefined = tree.getNodeByKey(
    data.RID
  )
  if (!sourceNode) {
    return
  }

  let parent: Fancytree.FancytreeNode = sourceNode
  while (!parent.isRootNode()) {
    const pData = Unit.getData(parent.key)
    if (!pData) {
      continue
    }
    parent.title = createUnitNodeTitle(pData)
    parent.tooltip = createUnitNodeTitleTooltip(pData)
    parent.renderTitle()

    parent = parent.getParent()
  }
}

export function refreshUnitCounter() {
  const units = Unit.getDataList()
  for (let i = 0; i < units.length; i++) {
    updateUnitCounter(units[i])
  }
}

// 判断界桩节点是否加载显示
export function showNotInstalledMarker(data: bysdb.IDbBysMarker): boolean {
  const showNotInstalledMarker = Store.state.Settings.showNotInstalledMarker
  if (showNotInstalledMarker) {
    return true
  }

  return data.HasInstallDevice ?? true
}

// 清除未安装界桩设备节点
export function clearNotInstallDeviceBysMarkers(tree: Fancytree.Fancytree) {
  const rootNode = tree.getRootNode()
  const clearn = (children: Fancytree.FancytreeNode[]) => {
    for (let i = children.length - 1; i >= 0; i--) {
      const node = children[i]
      if (node.data.customType === DataName.BysMarker) {
        const data = BysMarker.getData(node.key)
        if (!data) {
          continue
        }

        const isShow = showNotInstalledMarker(data)
        if (!isShow) {
          node.remove()
        }
      } else if (node.data.customType === DataName.Unit) {
        clearn(node.getChildren() ?? [])
      }
    }
  }

  clearn(rootNode.getChildren() ?? [])
}

// 重新将未安装的界桩添加到树形上
export function reloadNotInstallDeviceBysMarkers(tree: Fancytree.Fancytree) {
  const dataList = BysMarker.getDataList()

  // 生成界桩节点数据
  // parent key => [childNode, ...]
  const childrenMap: { [key: string]: any } = {}
  // parent cache
  const parentNodes = {}

  for (let i = 0; i < dataList.length; i++) {
    const device = dataList[i]

    // 只处理未安装界桩的数据
    if (device.HasInstallDevice == true) {
      continue
    }

    // 已经在，删除后重新添加
    const oldNode = tree.getNodeByKey(device.RID + '')
    if (oldNode) {
      oldNode.remove()
    }

    // 设置父级关系
    const OrgRID = device.OrgRID as string
    let parentNode =
      parentNodes[OrgRID] || (parentNodes[OrgRID] = tree.getNodeByKey(OrgRID))
    if (!parentNode) {
      parentNode = tree.rootNode
    }

    // 父节点为懒加载节点，且没有加载则跳过不处理
    if (parentNode.lazy && !parentNode.isLoaded()) {
      continue
    }

    if (!childrenMap[OrgRID]) {
      childrenMap[OrgRID] = []
    }

    // 生成界桩节点，缓存到map中，统一由父级节点添加
    const node = createBysMarkerNode(device)
    node.selected = parentNode.isSelected()
    childrenMap[OrgRID].push(node)
  }

  // 遍历当前界桩的上级节点数据，批量处理子节点数据
  for (let k in childrenMap) {
    const children = childrenMap[k]
    let parentNode = parentNodes[k] || (parentNodes[k] = tree.getNodeByKey(k))
    if (!parentNode) {
      // parentNode = tree.rootNode
      continue
    }

    parentNode.addChildren(children)
    sortNodeChildren(parentNode)
  }
}

// 更新单位节点title和tooltip
export function updateUnitNodeBadgeInfo(tree: Fancytree.Fancytree) {
  const rootNode = tree.getRootNode()
  const updateBadgeInfo = (children: Fancytree.FancytreeNode[]) => {
    for (let i = children.length - 1; i >= 0; i--) {
      const node = children[i]
      if (node.data.customType !== DataName.Unit) {
        continue
      }
      updateBadgeInfo(node.getChildren() ?? [])
      const data = Unit.getData(node.key)
      if (!data) {
        continue
      }

      node.title = createUnitNodeTitle(data)
      node.tooltip = createUnitNodeTitleTooltip(data)
      node.renderTitle()
    }
  }

  updateBadgeInfo(rootNode.getChildren() ?? [])
}

// 加载界桩数据节点
export function createSliceBysMarkerNodes(
  tree: Fancytree.Fancytree,
  dataList: Array<bysdb.IDbBysMarker>
) {
  const list: Array<bysdb.IDbBysMarker[]> = []
  while (dataList.length) {
    list.push(dataList.splice(0, Math.min(500, dataList.length)))
  }
  const it: Iterator<bysdb.IDbBysMarker[]> = list[Symbol.iterator]()
  const parentNodes = {}
  const process = (itRes: IteratorResult<bysdb.IDbBysMarker[]>) => {
    if (itRes.done) {
      return
    }
    const dataList = itRes.value
    // 生成界桩节点数据
    const childrenMap: { [key: string]: any } = {}
    for (let i = 0; i < dataList.length; i++) {
      const device = dataList[i]
      // 初始化时根据个人设置加载界桩
      const isShow = showNotInstalledMarker(device)
      if (!isShow) {
        continue
      }

      const OrgRID = device.OrgRID as string
      let parentNode =
        parentNodes[OrgRID] ||
        (parentNodes[OrgRID] = tree.getNodeByKey(OrgRID))
      if (!parentNode) {
        parentNode = tree.rootNode
      }
      // 父节点为懒加载节点，则跳过不处理
      if (parentNode.lazy && !parentNode.isLoaded()) {
        continue
      }

      if (!childrenMap[OrgRID]) {
        childrenMap[OrgRID] = []
      }

      const node = createBysMarkerNode(device)
      childrenMap[OrgRID].push(node)
    }

    // 遍历当前界桩的上级节点数据，批量处理子节点数据
    for (let k in childrenMap) {
      const children = childrenMap[k]
      let parentNode =
        parentNodes[k] || (parentNodes[k] = tree.getNodeByKey(k))
      if (!parentNode) {
        // parentNode = tree.rootNode
        continue
      }

      parentNode.addChildren(children)
      sortNodeChildren(parentNode)
    }

    window.setTimeout(() => {
      process(it.next())
    }, 0)
  }
  process(it.next())
}

// 懒加载子节点，需要根据设置过滤界桩节点
export function lazyLoadBysMarkerNodes(key: string): any[] {
  const result: any[] = []
  const bysMarkerList: bysdb.IDbBysMarker[] = BysMarker.getDataList()
  const children = bysMarkerList.filter(item => {
    const isChild = item.OrgRID === key
    // 如果是子节点，则判断该界桩是否要加载
    if (isChild) {
      return showNotInstalledMarker(item)
    }

    return false
  })

  children.forEach(item => {
    result.push(createBysMarkerNode(item))
  })

  return result
}

// 加载控制器数据节点
export function createSliceControllerNodes(
  tree,
  dataList: Array<bysdb.IDbController>
) {
  const list: Array<bysdb.IDbController[]> = []
  while (dataList.length) {
    list.push(dataList.splice(0, Math.min(500, dataList.length)))
  }
  const it: Iterator<bysdb.IDbController[]> = list[Symbol.iterator]()
  const parentNodes = {}
  const process = (itRes: IteratorResult<bysdb.IDbController[]>) => {
    if (itRes.done) {
      return
    }
    const dataList = itRes.value
    // 生成界桩节点数据
    const childrenMap: { [key: string]: any } = {}
    for (let i = 0; i < dataList.length; i++) {
      const controller = dataList[i]
      const OrgRID = controller.OrgRID as string
      let parentNode =
        parentNodes[OrgRID] ||
        (parentNodes[OrgRID] = tree.getNodeByKey(OrgRID))
      if (!parentNode) {
        parentNode = tree.rootNode
      }
      // 父节点为懒加载节点，则跳过不处理
      if (parentNode.lazy) {
        continue
      }

      if (!childrenMap[OrgRID]) {
        childrenMap[OrgRID] = []
      }

      const node = createControllerNode(controller)
      childrenMap[OrgRID].push(node)
    }

    // 遍历当前界桩的上级节点数据，批量处理子节点数据
    for (let k in childrenMap) {
      const children = childrenMap[k]
      let parentNode =
        parentNodes[k] || (parentNodes[k] = tree.getNodeByKey(k))
      if (!parentNode) {
        parentNode = tree.rootNode
      }

      parentNode.addChildren(children)
      sortNodeChildren(parentNode)
    }

    window.setTimeout(() => {
      process(it.next())
    }, 0)
  }
  process(it.next())
}

export function lazyLoadControllerNodes(key: string): any[] {
  const result: any[] = []
  const controllerList: bysdb.IDbController[] = Controller.getDataList()
  const children = controllerList.filter(item => item.OrgRID === key)

  children.forEach(item => {
    result.push(createControllerNode(item))
  })

  return result
}

function loadLazyNodes(lazyNodes: any[]): Promise<void> {
  return new Promise(resolve => {
    // 待处理的懒加载节点
    const it: Iterator<any> = lazyNodes[Symbol.iterator]()
    const lazyLoad = (itRes: IteratorResult<any>) => {
      if (itRes.done) {
        return resolve()
      }
      const node = itRes.value
      node.load().done(async () => {
        node.visit(child => {
          child.setSelected(true)
        })

        if (node.children && node.children.length > 0) {
          const lazyChildren = node.children.filter(
            item =>
              item.data.customType === DataName.Unit &&
              item.lazy &&
              !item.isLoaded()
          )
          await loadLazyNodes(lazyChildren)
        }

        lazyLoad(it.next())
      })
    }
    lazyLoad(it.next())
  })
}

export async function getAllSelectNodes(
  tree: Fancytree.Fancytree
): Promise<any[]> {
  let selected = tree.getSelectedNodes()
  // 找出未加载的懒加载节点，调用load方法，先加载子级节点，再重新找出所有选中的节点
  const lazyNodes = selected.filter(
    item =>
      item.data.customType === DataName.Unit && item.lazy && !item.isLoaded()
  )
  if (lazyNodes.length) {
    await loadLazyNodes(lazyNodes)
    selected = tree.getSelectedNodes()
  }

  return selected
}

// 加载所有子级节点，父节点不一定是懒加载节点
export function lazyLoadChildren(
  node: Fancytree.FancytreeNode
): Promise<boolean> {
  return new Promise(resolve => {
    // 不是单位节点，不需要懒加载
    if (node.data.customType !== DataName.Unit) {
      return resolve(true)
    }

    // 已经加载，则处理子级节点
    if (node.isLoaded()) {
      const children = node.getChildren() ?? []
      const promiseList: Promise<boolean>[] = []
      for (let i = 0; i < children.length; i++) {
        const child = children[i]
        promiseList.push(lazyLoadChildren(child))
      }
      return Promise.all(promiseList).then(() => resolve(true))
    }

    // 先懒加载子级节点数据，再处理子级节点的懒加载
    node.load().done(async () => {
      const children = node.getChildren() ?? []
      for (let i = 0; i < children.length; i++) {
        const child = children[i]
        child.setSelected(node.isSelected())
        await lazyLoadChildren(child)
      }
      return resolve(true)
    })
  })
}

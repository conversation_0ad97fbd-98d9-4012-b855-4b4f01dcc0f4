import { defineAsyncComponent, ref, onBeforeMount, onBeforeUnmount, getCurrentInstance } from 'vue'
import { StrPubSub } from 'ypubsub'

const dialogModal = defineAsyncComponent(() => import('@components/modal/Modal.vue'))

export function useDialogModal(name?: string) {
  const visible = ref(false)
  const maximized = ref(false)

  const Instance = getCurrentInstance()
  const subject = `${name ?? Instance?.type.name ?? Instance?.type.__name}-visible`

  onBeforeMount(() => {
    StrPubSub.subscribe(subject, (value: boolean) => {
      visible.value = value
    })
  })
  onBeforeUnmount(() => {
    StrPubSub.unsubscribe(subject)
  })

  return {
    visible,
    maximized,
    dialogModal,
  }
}

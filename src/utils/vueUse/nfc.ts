import { onBeforeMount, onBeforeUnmount, ref, watch } from 'vue'
import log from '@utils/log'
import { Dialog } from 'quasar'
import { Deferred, deferred } from '@utils/common'
import { lib } from 'crypto-js'
import { encrypt } from '@utils/crypto'
import { i18n } from '@boot/i18n'

// 定义卡片类型
export enum NFCCardType {
  NDEF = 'ndef',
  MIFARE = 'mifare'
}

export enum NFCState {
  NO_NFC = 'NO_NFC',
  NFC_DISABLED = 'NFC_DISABLED',
}

export type DisableReasonType = keyof typeof NFCState

const NfcNotEnableReason: Record<DisableReasonType, () => string> = {
  NO_NFC: () => i18n.global.t('NFCPatrol.NO_NFC'),
  NFC_DISABLED: () => i18n.global.t('NFCPatrol.NFC_DISABLED'),
}

export interface NFC_NdefMessage {
  tnf: number
  type: Array<number>
  id: Array<number>
  payload: Array<number>
}

// https://github.com/EYALIN/community-cordova-plugin-nfc/tree/master?tab=readme-ov-file#ndef
export interface NFC_Tag {
  id: Array<number>
  techTypes: Array<string>
  type: string
  maxSize: number
  isWritable: boolean
  ndefMessage: Array<NFC_NdefMessage>
  canMakeReadOnly: boolean
}

export interface NFC_NdefEvent {
  isTrusted: boolean
  type: NFCCardType
  tag: NFC_Tag
}

const iv = 'bys_NFC_write_IV'
const key = 'bys_NFC_write_marker_KEY'

export const IV = lib.WordArray.create(getByteArray(iv)) // 十六位十六进制数作为密钥偏移量
export const KEY = lib.WordArray.create(getByteArray(key)) // 24 位秘钥密钥

export function useAndroidNFC() {
  const stream = ref<Deferred<NFC_Tag>>(deferred<NFC_Tag>())
  // 可以写入标志
  const isWrite = ref(false)
  const writeData = ref('')
  // 写入完成标志
  const writeFinish = ref<Deferred<boolean>>(deferred<boolean>())
  // 不支持NFC标志
  const noNFC = ref(true)

  // 监听读取数据
  function ndefListenerHandler(evt: NFC_NdefEvent) {
    stream.value.resolve(evt.tag)
    stream.value = deferred<NFC_Tag>()
  }

  // 检测 NFC 是否可用
  function autoDetectNfcEnable() {
    const res = deferred<boolean>()
    let isEnabled = false
    let detectCount = 0
    // NFC 可用
    const onEnabled = () => {
      log.info('NFC is enabled.')
      res.resolve(true)
      isEnabled = true
    }
    // NFC 不可用
    const onDisabled = (reason: string) => {
      log.error('NFC is not enabled.')
      isEnabled = false
      if (++detectCount > 1) {
        return
      }

      const message = NfcNotEnableReason[reason]?.() ?? reason
      if (reason === NFCState.NO_NFC) {
        Dialog.create({
          message,
        })

        return res.reject(reason)
      }

      Dialog.create({
        message,
        ok: {
          icon: 'nfc',
          flat: true,
          label: i18n.global.t('common.confirm')
        }
      })
        .onOk(() => {
          nfc.showSettings()
        })
    }

    const runDetect = () => {
      if (isEnabled) return

      nfc.enabled(onEnabled, onDisabled)
      window.setTimeout(() => {
        runDetect()
      }, 3000)
    }

    runDetect()

    return res
  }

  // 监听 NDEF 读取数据
  function tagDiscoveredListener(nfcEvent: NFC_NdefEvent) {
    const tag = nfcEvent.tag
    if (!tag || !tag.id || !tag.techTypes) {
      return
    }

    const tagId = nfc.bytesToHexString(tag.id)

    // 可以格式化为NDEF的标签，提示先写入数据
    if (tag.techTypes.includes('android.nfc.tech.MifareClassic') && tag.techTypes.includes('android.nfc.tech.NdefFormatable')) {
      Dialog.create({
        title: `${i18n.global.t('NFCPatrol.discoverNewTags')}: ${tagId}`,
        message: i18n.global.t('NFCPatrol.firstWriteMarkerData'),
      })
    } else {
      log.error('Detected tag is not a supported Mifare type for this example.', tag)
      Dialog.create({
        title: `${i18n.global.t('NFCPatrol.discoverNewTags')}: ${tagId}`,
        message: JSON.stringify(tag, null, 2)
      })
    }
  }

  function writeNFC() {
    const encryptData = encrypt(writeData.value, KEY, IV)
    const message = [
      ndef.textRecord(encryptData)
    ]

    nfc.write(
      message,
      () => {
        isWrite.value = false
        writeFinish.value.resolve(true)
        writeFinish.value = deferred<boolean>()
      },
      () => {
        isWrite.value = false
      }
    )
  }

  // 监听写入状态变化，切换监听读取、写入操作事件
  watch(isWrite, (val) => {
    if (val) {
      // 取消读取nfc标签事件
      nfc.removeNdefListener(ndefListenerHandler)
      nfc.removeTagDiscoveredListener(tagDiscoveredListener)
      // 添加写入nfc标签事件
      nfc.addNdefListener(writeNFC)
      nfc.addTagDiscoveredListener(writeNFC)
    } else {
      // 取消写入nfc标签事件
      nfc.removeNdefListener(writeNFC)
      nfc.removeTagDiscoveredListener(writeNFC)
      // 添加读取nfc标签事件
      nfc.addNdefListener(ndefListenerHandler)
      nfc.addTagDiscoveredListener(tagDiscoveredListener)
    }
  })

  onBeforeMount(() => {
    // 自动检测NFC是否开启
    autoDetectNfcEnable()
      .then(() => {
        // 开启后监听读取数据事件
        noNFC.value = false
        nfc.addNdefListener(ndefListenerHandler)
        nfc.addTagDiscoveredListener(tagDiscoveredListener)
      })
  })

  onBeforeUnmount(() => {
    // 解绑监听读卡事件
    nfc.removeNdefListener(ndefListenerHandler)
    nfc.removeTagDiscoveredListener(tagDiscoveredListener)
  })

  return {
    stream,
    isWrite,
    writeData,
    writeFinish,
    noNFC
  }
}

// 将NFC卡的ID字节序转换为16进制的字符串，20:46:3D:B2
export function readHexId(id: Array<number>, separator?: string): string {
  return Array.from(new Uint8Array(id))
    .map(num => num.toString(16).padStart(2, '0').toUpperCase())
    .join(separator)
}

function getByteArray(key: string): Uint8Array {
  const hexString = key.split('').map(char => char.charCodeAt(0).toString(16).padStart(2, '0')).join('');
  // @ts-ignore
  return new Uint8Array(hexString.match(/.{1,2}/g).map(byte => parseInt(byte, 16)));
}

import { computed, ComputedRef, ref, Ref } from 'vue'
import { useBysMarkerData } from '@utils/vueUse/bysMarkerData'
import { bysproto, doc } from '@ygen/bys.api'
import { ICallOption, TrpcMeta } from 'jsyrpc'
import { yrpcmsg } from 'yrpcmsg'
import log from '@utils/log'
import { RpcBysApi } from '@ygen/bys.api.yrpc'
import { GET_DATA, NS } from '@store/data/methodTypes'
import { DataName, Store } from '@src/store'
import { bysdb } from '@ygen/bysdb'
import { getGCJ02LngLat, setGCJ02LngLat } from '@utils/gcoord'
import { crud } from '@ygen/crud'
import { cloneDeep } from 'lodash'
import { BysMarker } from '@services/dataStore'
import { StrPubSub } from 'ypubsub'
import { BysMarkerForceUpdate, UpdateDbBysMarker } from '@utils/pubSubSubject'
import { PrpcDbBysMarker } from '@ygen/bysdb.rpc.yrpc'
import { MarkerType, outputDBError, rawObjectProps, checkIs4GMarker } from '@utils/common'
import globalConfig from '@src/config'
import maplibreGl from 'maplibre-gl'
import {
  genRangeRule,
  IDRangeRule,
  isLat,
  isLon,
  isValidTime,
  maxLength,
  required,
  isDomain,
  Sint32Limit, isIPValid, iccidReg, isValidDay,
  validateIMEI
} from '@utils/validation'
import { sysUtcTime, utcTime } from '@utils/dayjs'
import { BysMarkerAndUpdateInfo, FormRules } from '@utils/bysdb.type'
import { pakoDeflate, toBase64 } from '@utils/crypto'
import { i18n } from '@boot/i18n'
import { Notify, QInput } from 'quasar'
import { check4GMarkerIsNeedUpdateParamTime } from '@app/src/services/controller'

const bysMarkerError = computed(() => {
  return {
    controllerrid_fkey: i18n.global.t('dataBaseError.parControllerErr'),
    orgrid_fkey: i18n.global.t('dataBaseError.parUnitErr'),
    controllerrid_controllerchannel_markerqueueno_key: i18n.global.t('dataBaseError.queueNoErr'),
    markerhwid_key: i18n.global.t('dataBaseError.markerHwidErr'),
    markerno_key: i18n.global.t('dataBaseError.markerNoErr'),
    markerredlineno_key: i18n.global.t('dataBaseError.redLineNoErr'),
    'No operation permission': i18n.global.t('dataBaseError.permissionErr'),
  }
})

const { bysMarkerData } = useBysMarkerData()

export function useBysMarkerEdit(currentRow: Ref<BysMarkerAndUpdateInfo>) {
  const queueNoInput = ref<QInput>()
  const allMarkerICCID = computed(() => {
    let iccids: { RID: any; ICCID: any }[] = []
    bysMarkerData.value.forEach(item => {
      if (checkIs4GMarker(item.MarkerType) && item.ICCID) {
        iccids.push({
          RID: item.RID,
          ICCID: item.ICCID,
        })
      }
    })
    return iccids
  })

  const allMarkerIMEI = computed(() => {
    let iemis: { RID: string; IMEI: string }[] = []
    bysMarkerData.value.forEach((item: bysdb.DbBysMarker) => {
      if (checkIs4GMarker(item.MarkerType) && item.IMEI) {
        iemis.push({
          RID: item.RID,
          IMEI: item.IMEI,
        })
      }
    })
    return iemis
  })

  const MarkerTypeOptions = computed(() => {
    return [
      { label: i18n.global.t('form.normalMarkers'), value: MarkerType.Regular },
      { label: i18n.global.t('form.4GMarkers'), value: MarkerType.Net4G },
      { label: i18n.global.t('form.4GMarkersPro'), value: MarkerType.Net4GPro },
    ]
  })

  const MarkerModelOptions = computed(() => {
    return [
      { label: i18n.global.t('form.long'), value: 1 },
      { label: i18n.global.t('form.short'), value: 2 },
    ]
  })

  const controllerData = computed(() => {
    return Store.getters[`${NS}/${GET_DATA}`](DataName.Controller)
  })

  const AllMarkerOptions = computed(() => {
    return bysMarkerData.value.map(
      (data: bysdb.IDbBysMarker) => {
        return {
          label: data.MarkerNo,
          value: data.RID,
          MarkerHWID: data.MarkerHWID,
        }
      },
    )
  })

  const paramsWith4gAddrRules: ComputedRef<FormRules> = computed(() => {
    const _required = (val) => required(val) || i18n.global.t('rules.required')
    const _port = genRangeRule(1, 0xFFFF)
    return {
      addr: [
        val => _required(val),
        val => (isIPValid(val) || isDomain(val)) || i18n.global.t('rules.mustBeIpOrDomain')
      ],
      port: [
        val => _required(val),
        val => _port(val) || i18n.global.t('rules.availableRange', { min: 1, max: 0xFFFF }),
      ]
    }
  })

  const formRules: ComputedRef<Record<number, FormRules>> = computed(() => {
    const map = globalConfig.map as maplibreGl.Map
    const maxLevel = ~~map.getMaxZoom()
    const minLevel = ~~map.getMinZoom()
    const _required = (val) => required(val) || i18n.global.t('rules.required')
    const _maxLength = (val) => maxLength(val, 16) || i18n.global.t('rules.maxLength', { len: 16 })
    const slope = genRangeRule(15, 90)
    const distance = genRangeRule(10, 100)
    const vibrationAmplitude = genRangeRule(1, 10)
    const vibrationDuration = genRangeRule(1, 60)
    // const infraredLimlit = genRangeRule(1, 10)
    const deLaySlTime = genRangeRule(10, 60)
    const DebugModeTime = genRangeRule(120, 240)
    const AlarmInterval = genRangeRule(10, 60)
    const alarmNum = genRangeRule(10, 60)
    const queueNoInterval = genRangeRule(0, 0xFFFF)
    const markerQueueInterval = genRangeRule(20, 0xFF)
    const markerDayInterval = genRangeRule(1, 24)

    const sharedRules = {
      MarkerNo: [
        val => _required(val),
        val => _maxLength(val),
      ],
      OrgRID: [
        val => _required(val),
      ],
      MarkerHWID: [
        val => val !== null || i18n.global.t('rules.required'),
        val => IDRangeRule(val) || i18n.global.t('rules.availableRange', { min: 1, max: Sint32Limit }),
        val => !AllMarkerOptions.value.some(opt => {
          // 控制器ID唯一，如果在控制器中查找到相同的ID，且不是当前编辑的数据，则验证不通过
          if (currentRow.value) {
            return opt.MarkerHWID === val && opt.value !== currentRow.value.RID
          }
          return opt.MarkerHWID === val
        }) || i18n.global.t('rules.IDAlreadyExists'),
      ],
      MarkerModel: [
        val => val !== null || i18n.global.t('rules.required'),
      ],
      MarkerType: [
        val => val !== null || i18n.global.t('rules.required'),
      ],
      Lon: [
        val => _required(val),
        val => isLon(val) || i18n.global.t('rules.mustBeLon'),
      ],
      Lat: [
        val => _required(val),
        val => isLat(val) || i18n.global.t('rules.mustBeLat'),
      ],
      MapShowLevel: [
        val => _required(val),
        val => (val >= minLevel && val <= maxLevel) ||
          i18n.global.t('rules.availableRange', { min: minLevel, max: maxLevel }),
      ],
      MarkerDescription: [
        val => maxLength(val, 4096) || i18n.global.t('rules.maxLength', { len: 4096 }),
      ],
    }
    const regularRules = {
      ControllerRID: [
        val => _required(val),
      ],
      ControllerChannel: [
        val => _required(val),
      ],
      MarkerChannel: [
        val => _required(val),
      ],
      MarkerQueueNo: [
        val => _required(val),
        val => queueNoInterval(val) || i18n.global.t('rules.availableRange', { min: 0, max: 0xFFFF }),
      ],
      MarkerDayInterval: [
        val => _required(val),
        val => markerDayInterval(val) || i18n.global.t('rules.availableRange', { min: 1, max: 24 }),
      ],
      MarkerQueueInterval: [
        val => _required(val),
        val => markerQueueInterval(val) || i18n.global.t('rules.availableRange', { min: 20, max: 0xFF }),
      ],
      MarkerEmergentInterval: [
        val => _required(val),
        val => markerQueueInterval(val) || i18n.global.t('rules.availableRange', { min: 20, max: 0xFF }),
      ],
      MarkerWakeupBaseTime: [
        val => _required(val),
        val => isValidTime(val) || i18n.global.t('rules.invalidTime'),
      ],
    }
    const fourGRules = {
      timer: [
        val => _required(val),
        val => markerDayInterval(val) || i18n.global.t('rules.availableRange', { min: 1, max: 24 }),
      ],
      n1: [
        val => _required(val),
        val => alarmNum(val) || i18n.global.t('rules.availableRange', { min: 10, max: 60 }),
      ],
      t3: [
        val => _required(val),
        val => AlarmInterval(val) || i18n.global.t('rules.availableRange', { min: 10, max: 60 }),
      ],
      t2: [
        val => _required(val),
        val => DebugModeTime(val) || i18n.global.t('rules.availableRange', { min: 120, max: 240 }),
      ],
      t1: [
        val => _required(val),
        val => deLaySlTime(val) || i18n.global.t('rules.availableRange', { min: 10, max: 60 }),
      ],
      // infrared: [
      //   val => _required(val),
      //   val => infraredLimlit(val) || i18n.global.t('rules.availableRange', { min: 1, max: 10 }),
      // ],
      duration: [
        val => _required(val),
        val => vibrationDuration(val) || i18n.global.t('rules.availableRange', { min: 1, max: 60 }),
      ],
      amplitude: [
        val => _required(val),
        val => vibrationAmplitude(val) || i18n.global.t('rules.availableRange', { min: 1, max: 10 }),
      ],
      dirft: [
        val => _required(val),
        val => distance(val) || i18n.global.t('rules.availableRange', { min: 10, max: 100 }),
      ],
      attitude: [
        val => _required(val),
        val => slope(val) || i18n.global.t('rules.availableRange', { min: 15, max: 90 }),
      ],
      // 表单的域名/ip地址和端口分开配置处理，在this.paramsWith4gAddrRules中的addr和port进行校验
      // 此处的addr规则用于导入界桩数据时，需使用rules的key（addr）与界桩数据中的row.MarkerSettings.addr进行校验
      addr: [
        val => _required(val),
        val => {
          const addrRules = paramsWith4gAddrRules.value.addr
          const portRules = paramsWith4gAddrRules.value.port
          const addrAndPort = val.split(':')
          if (addrAndPort.length !== 2) {
            return i18n.global.t('form.addrRulesFail')
          }
          const [a, p] = addrAndPort
          for (let i = 0; i < addrRules.length; i++) {
            let rule = addrRules[i]
            let ruleRes = rule(a)
            if (ruleRes !== true) {
              return ruleRes
            }
          }
          for (let i = 0; i < portRules.length; i++) {
            let rule = portRules[i]
            let ruleRes = rule(Number(p))
            if (ruleRes !== true) {
              return ruleRes
            }
          }
          return true
        }
      ],
      ICCID: [
        val => !val || (val?.length ?? 0) === 19 || i18n.global.t('form.iccidRuleCheckFail'),
        val => !val || iccidReg(val) || i18n.global.t('form.iccidRuleCheckFail'),
        val => !val || !allMarkerICCID.value.some(item => {
          // iccid唯一，如果不是当前编辑的数据，则验证不通过
          if (currentRow.value) {
            return item.ICCID === val && item.RID !== currentRow.value.RID
          }
          return item.ICCID === val
        }) || i18n.global.t('form.iccidExists'),
      ],
      IMEI: [
        val => !val || (val?.length ?? 0) === 15 || i18n.global.t('rules.validIMEILength'),
        val => !val || validateIMEI(val) || i18n.global.t('rules.validIMEI'),
        val => !val || !allMarkerIMEI.value.some(item => {
          // imei唯一，如果不是当前编辑的数据，则验证不通过
          if (currentRow.value) {
            return item.IMEI === val && item.RID !== currentRow.value.RID
          }
          return item.IMEI === val
        }) || i18n.global.t('rules.IMEIExists'),
      ],
      ExpirationDate: expirationDateRules.value[0],
    }
    const RulesMap = {
      0: Object.assign(regularRules, sharedRules),
      1: Object.assign(fourGRules, sharedRules),
      2: Object.assign(fourGRules, sharedRules),
    }
    return RulesMap
  })

  const rules = computed<FormRules>(() => {
    return formRules.value[currentRow.value.MarkerType ?? 0]
  })

  const expirationDateRules: ComputedRef<FormRules> = computed(() => {
    const _required = (val) => required(val) || i18n.global.t('rules.required')
    return {
      0: [
        val => !val || isValidDay(val?.split(' ')[0] ?? '') || i18n.global.t('form.expirationDateFail'),
      ],
      1: [
        val => _required(val),
        val => !val || isValidDay(val?.split(' ')[0] ?? '') || i18n.global.t('form.expirationDateFail'),
      ]
    }
  })

  const redLineLinksRules = computed(() => {
    const _required = (val) => required(val) || i18n.global.t('rules.required')
    return [
      val => _required(val),
    ]
  })

  const updateData = (data: bysdb.IDbBysMarker): Promise<boolean> => {
    return new Promise((resolve, reject) => {
      const RID = data.RID as string
      const oldData = cloneDeep(BysMarker.getData(RID)) as BysMarkerAndUpdateInfo
      const needUpdateParamTime = check4GMarkerIsNeedUpdateParamTime(data, oldData)
      if (needUpdateParamTime) {
        // 更新界桩参数时间
        data.MarkerParamTime = sysUtcTime()
      }
      const d = bysdb.DbBysMarker.encode(oldData).finish()
      const meta = new TrpcMeta()
      meta.SetMeta('old-data', toBase64(pakoDeflate(d)))
      const options: ICallOption = {
        OnResult: (res: crud.IDMLResult, rpcCmd: yrpcmsg.Ymsg, meta?: yrpcmsg.IGrpcMeta) => {
          log.info('IDbBysMarker Update result', res, rpcCmd, meta)
          if (res.AffectedRow === 0) {
            reject('failed')
          } else {
            resolve(true)
            // 同步到数据容器对象
            const newMarker = new bysdb.DbBysMarker(data) as BysMarkerAndUpdateInfo
            const partdata = {
              isAlreadyRemoveAlarm: oldData.isAlreadyRemoveAlarm,
              removeAlarm: oldData.removeAlarm,
            }
            if (oldData.updateInfo) {
              Object.assign(partdata, {
                updateInfo: rawObjectProps(oldData.updateInfo)
              })
            }
            // 只有4g界桩才需要拷贝旧的数据
            if (newMarker.MarkerType === oldData.MarkerType && (oldData.MarkerType === MarkerType.Net4G || oldData.MarkerType === MarkerType.Net4GPro)) {
              Object.assign(partdata, {
                // 针对4g界桩新添加字段属性
                onlineInfo: oldData.onlineInfo,
                markerInfo: oldData.markerInfo,
              })
            }
            setGCJ02LngLat(newMarker, getGCJ02LngLat(data))
            BysMarker.setData(RID, newMarker)
              .setPartData(RID, partdata)
              .setDataIndex(newMarker.MarkerHWID + '', RID)
            // 发布更新数据通知
            StrPubSub.publish(UpdateDbBysMarker, newMarker, oldData)
            StrPubSub.publish(BysMarkerForceUpdate, newMarker.RID)
          }
        },
        OnServerErr: (errRpc: yrpcmsg.Ymsg) => {
          log.error('IDbBysMarker Update server error', errRpc)
          // 处理服务器响应的错误
          let reason = outputDBError(bysMarkerError.value, errRpc.Optstr)
          reject(reason)
        },
        OnLocalErr: (err: any) => {
          log.error('IDbBysMarker Update local error', err)
          reject('local')
        },
        OnTimeout: (v: any) => {
          log.warn('IDbBysMarker Update timeout', v)
          const options = {
            action: i18n.global.t('common.modify'),
            name: data.MarkerNo,
          }
          const reason = i18n.global.t('message.timeout', options)
          reject(reason)
        },
        rpcMeta: meta,
      }
      PrpcDbBysMarker.Update(data, options)
    })
  }
  const markerQueueNoChange = (val, isDefault = true) => {
    // 不在有效的范围内
    if (!(val >= 1 && val <= 256)) {
      return
    }
    const data: doc.IMarkerQueueNoInfo = {
      ControllerRID: currentRow.value.ControllerRID,
      ControllerChannelNo: currentRow.value.ControllerChannel,
      QueueNoAvailable: val,
    }
    if (!data.ControllerRID || !data.ControllerChannelNo || !data.QueueNoAvailable) {
      return
    }
    const options: ICallOption = {
      OnResult: (res: doc.IMarkerQueueNoInfo) => {
        // 过滤与请求参数不一致的结果
        if (res.ControllerRID !== currentRow.value.ControllerRID
          || res.ControllerChannelNo !== currentRow.value.ControllerChannel
        ) {
          return
        }
        if (currentRow.value.MarkerQueueNo !== res.Result && isDefault) {
          Notify.create({
            message: i18n.global.t('form.availableQueueNo') + res.Result + '',
            type: 'warning',
            position: 'top',
          })
        }
        currentRow.value.MarkerQueueNo = res.Result
        queueNoInput.value?.validate()
      },
      OnServerErr: (errRpc: yrpcmsg.Ymsg) => {
        log.error('query IMarkerQueueNoInfo server error', errRpc)
      },
      OnLocalErr: (err: any) => {
        log.error('query IMarkerQueueNoInfo local error', err)
      },
      OnTimeout: (v: any) => {
        log.warn('query IMarkerQueueNoInfo timeout', v)
      },
    }
    RpcBysApi.QueryMarkerQueueNo(data, options)
  }

  return {
    queueNoInput,
    allMarkerICCID,
    MarkerTypeOptions,
    MarkerModelOptions,
    controllerData,
    AllMarkerOptions,
    paramsWith4gAddrRules,
    formRules,
    rules,
    expirationDateRules,
    redLineLinksRules,
    allMarkerIMEI,
    updateData,
    markerQueueNoChange,
  }
}

function getDefAddr() {
  const addrInfo = window.location
  let addr = ''
  addr = addrInfo.hostname === 'localhost' ? '127.0.0.1' : addrInfo.hostname
  if (addrInfo.port === '') {
    addr += addrInfo.protocol === 'http:' ? ':80' : ':443'
  } else {
    addr += (':' + addrInfo.port)
  }
  return addr
}

  // 界桩运行的默认参数
export function getMarkerDefaultParams(): bysproto.IBDataUpdate {
  return {
    deviceID: 0,
    type: 1 << 1,
    dataVersion: utcTime(),
    addr: getDefAddr(),
    timerbase: '',
    timer: 24,
    attitude: 45,
    dirft: 10,
    vibration: {
      amplitude: 3,
      duration: 10,
    },
    infrared: 4,
    t1: 30,
    t2: 120,
    t3: 10,
    n1: 10,
  }
}

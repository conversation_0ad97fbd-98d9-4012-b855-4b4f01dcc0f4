import { computed } from 'vue'
import { DataName, Store } from '@src/store'
import { GET_DATA, NS } from '@store/data/methodTypes'
import { BysMarker } from '@services/dataStore'

export function useBysMarkerData() {
  const bysMarkerData = computed(() => {
    return Store.getters[`${NS}/${GET_DATA}`](DataName.BysMarker)
      .filter(item => item.RID !== Store.state.UserRID)
  })

  const bysMarkerDataSorted = computed(() => {
    return BysMarker.sortBy(bysMarkerData.value, { sortBy: 'MarkerHWID' })
  })

  return {
    bysMarkerData,
    bysMarkerDataSorted,
  }
}

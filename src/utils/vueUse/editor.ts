import { FormRuleItemFunc, FormRules } from '@utils/bysdb.type'
import { testPattern } from 'quasar/src/utils/patterns.js'

export default  function useEditor() {
  function checkDataByRules(data: { [key: string]: any }, rules: FormRules, getFieldValue?: (obj: Record<string, any>, key: string) => any): boolean | string {
    // 根据rule校验数据，兼容quasar内置的支持使用string来进行检验
    const keys = Object.keys(rules)
    const _getFieldValue = getFieldValue ?? ((obj, k) => obj[k])
    for (let k = 0; k < keys.length; k++) {
      const key = keys[k]
      let item = rules[key]
      for (let i = 0; i < item.length; i++) {
        let rule = item[i]
        // 使用字符串来匹配内置默认的规则，不匹配则不进行检验
        if (typeof rule === 'string') {
          if (!testPattern[rule]) {
            return true
          }
          rule = testPattern[rule] as FormRuleItemFunc
        }

        // @ts-ignore
        const valid = rule(_getFieldValue(data, key))
        if (valid !== true) {
          // @ts-ignore
          return valid
        }
      }
    }

    return true
  }

  return {
    checkDataByRules
  }
}

import { DefUuid } from '@src/config'
import {
  Bys<PERSON>ark<PERSON>,
  NFCPatrolLine,
  NFCPatrolLineAndRules,
  NFCPatrolLineDetail,
  NFCPatrolLineRules
} from '@services/dataStore'
import { DataName, Store } from '@src/store'
import { GET_DATA, NS } from '@store/data/methodTypes'
import { org } from '@src/ygen/org'
import { ComputedRef, computed, ref } from 'vue'
import { sortData } from '../permission'
import { i18n } from '@boot/i18n'
import { FormRules } from '../bysdb.type'
import { isValidTime, maxLength, required } from '../validation'
import { bysdb } from '@ygen/bysdb';

export function useNFCPatrolData(customCheckName?: Function) {

  const parentFilter = ref('')

  const NFCPatrolLines = computed(() => { return NFCPatrolLine.getDataList() })

  const NFCPatrolLineDetails = computed(() => { return NFCPatrolLineDetail.getDataList() })

  const NFCPatrolLineAndRulesData = computed(() => { return NFCPatrolLineAndRules.getDataList() })

  const NFCPatrolLineRulesData = computed(() => { return NFCPatrolLineRules.getDataList() })

  const bysMarkerData = computed(() => { return BysMarker.getDataList() })

  const unitData: ComputedRef<Array<org.IDbOrg>> = computed(() => {
    const dataList = Store.getters[`${NS}/${GET_DATA}`](DataName.Unit).filter(item => item.RID !== DefUuid)
    return sortData<org.IDbOrg>(dataList, {
      descending: Store.state.Settings.descending,
      prop: 'SortValue',
    })
  })

  const parentOptions: ComputedRef<Array<{ [key: string]: any }>> = computed(() => {
    return unitData.value
      .filter(item => item.RID !== DefUuid)
      .map(data => {
        return {
          label: data.RID === DefUuid ? i18n.global.t('form.default') : data.ShortName,
          value: data.RID,
        }
      })
      .filter(option => {
        const needle = parentFilter.value.toLowerCase()
        // @ts-ignore
        return option.label.toLowerCase().includes(needle)
      })
  })

  const rulesOptions = computed(() => {
    const customSort = (a, b) => {
      // 比较字符串长度
      if (a.label.length !== b.label.length) {
        return a.length - b.length
      }
      // 如果长度相等，比较结尾数字
      const regex = /\d+$/
      const numA = parseInt((a.label.match(regex) || [])[0]) || 0
      const numB = parseInt((b.label.match(regex) || [])[0]) || 0
      return numA - numB
    };
    return NFCPatrolLineRulesData.value.map(rule => {
      return {
        label: rule.Name,
        key: rule.RID
      }
    }).sort(customSort)
  })

  const rules: ComputedRef<FormRules> = computed(() => {
    const _required = (val) => required(val) || i18n.global.t('rules.required')
    const _maxLength = (val) => maxLength(val, 16) || i18n.global.t('rules.maxLength', { len: 16 })
    const validTime = [
      val => _required(val),
      val => isValidTime(val) || i18n.global.t('rules.invalidTime'),
    ]
    return {
      OrgRID: [
        val => _required(val),
      ],
      Name: [
        val => _required(val),
        val => _maxLength(val),
        val => checkName(val, NFCPatrolLineRulesData.value) ? true : i18n.global.t('NFCPatrol.isUseName'),
      ],
      Note: [
        val => maxLength(val, 4096) || i18n.global.t('rules.maxLength', { len: 4096 }),
      ],
      CheckCount: [
        val => _required(val),
        val => val > 0 || val < 99 || i18n.global.t('NFCPatrol.patrolCountExceed'),
      ],
      CheckStartTime: validTime,
      CheckEndTime: validTime,
    }
  })

  const lineRules: ComputedRef<FormRules> = computed(() => {
    const _required = (val) => required(val) || i18n.global.t('rules.required')
    const arrRequired = (val) => val.length > 0 || i18n.global.t('NFCPatrol.patrolRules') + i18n.global.t('rules.required')
    const _maxLength = (val) => maxLength(val, 16) || i18n.global.t('rules.maxLength', { len: 16 })
    return {
      OrgRID: [
        val => _required(val),
      ],
      Name: [
        val => _required(val),
        val => _maxLength(val),
        val => checkName(val, NFCPatrolLines.value) ? true : i18n.global.t('NFCPatrol.isUseName'),
      ],
      details: [
        val => val.length > 0 ? true : i18n.global.t('NFCPatrol.linePointIsNotNull'),
        val => val.length < 200 ? true : i18n.global.t('NFCPatrol.linePointIsNotExceed'),
      ],
      rules: [
        val => arrRequired(val),
        val => val.length < 10 ? true : i18n.global.t('NFCPatrol.rulesIsNotExceed'),
      ],
      Note: [
        val => maxLength(val, 4096) || i18n.global.t('rules.maxLength', { len: 4096 }),
      ],
    }
  })

  function filterParent(val: string, update: Function) {
    parentFilter.value = val
    update()
  }

  function getMarkerNo(rid: string) {
    const marker = BysMarker.getData(rid)
    return marker?.MarkerNo
  }

  function checkName(val, data: any[]) {
    if (customCheckName) return customCheckName(val)
    return data.findIndex(item => item.Name === val) === -1
  }

  return {
    NFCPatrolLines,
    NFCPatrolLineDetails,
    unitData,
    parentOptions,
    parentFilter,
    filterParent,
    NFCPatrolLineAndRulesData,
    NFCPatrolLineRulesData,
    rulesOptions,
    getMarkerNo,
    lineRules,
    rules,
    bysMarkerData,
  }
}


const NFCPatrolLines = computed(() => { return NFCPatrolLine.getDataList() })
const NFCPatrolLineDetails = computed(() => { return NFCPatrolLineDetail.getDataList() })
const NFCPatrolLineAndRulesData = computed(() => { return NFCPatrolLineAndRules.getDataList() })

export const NFCPatrolLineAndDetailsDataMap: ComputedRef<{[key: string]: bysdb.IDbNFCPatrolLineDetail[] }> = computed(() => {
  const lineAndDetailsMap = {}
  for (let i = 0; i < NFCPatrolLines.value.length; i++) {
    lineAndDetailsMap[NFCPatrolLines.value[i].RID as string] = NFCPatrolLineDetails.value.filter(v => NFCPatrolLines.value[i].RID === v.LineRID)
  }
  return lineAndDetailsMap
})

export const NFCPatrolLineAndRulesDataMap: ComputedRef<{ [key: string]: bysdb.IDbNFCPatrolLineAndRules[] }> = computed(() => {
  const lineAndRulesMap = {}
  for (let i = 0; i < NFCPatrolLines.value.length; i++) {
    lineAndRulesMap[NFCPatrolLines.value[i].RID as string] = NFCPatrolLineAndRulesData.value.filter(v => NFCPatrolLines.value[i].RID === v.LineRID)
  }
  return lineAndRulesMap
})

export const NFCPatrolLineRulesDataMap: ComputedRef<{[key: string]: bysdb.IDbNFCPatrolLineRules[]}> = computed(() => {
  const lineRulesMap = {}
  for (let i = 0; i < NFCPatrolLines.value.length; i++) {
    const rid = NFCPatrolLines.value[i].RID as string
    lineRulesMap[rid] = NFCPatrolLineAndRulesDataMap.value[rid].map(item => {
      return NFCPatrolLineRules.getData(item.RuleRID as string)
    })
  }
  return lineRulesMap
})

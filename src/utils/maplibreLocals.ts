// 详细需要查看maplibre-gl-dev.js模块下defaultLocale变更定义
export const enUSLocales = {
  'AttributionControl.ToggleAttribution': 'Toggle attribution',
  'AttributionControl.MapFeedback': 'Map feedback',
  'FullscreenControl.Enter': 'Enter fullscreen',
  'FullscreenControl.Exit': 'Exit fullscreen',
  'GeolocateControl.FindMyLocation': 'Find my location',
  'GeolocateControl.LocationNotAvailable': 'Location not available',
  'LogoControl.Title': 'MapLibre logo',
  'NavigationControl.ResetBearing': 'Reset bearing to north',
  'NavigationControl.ZoomIn': 'Zoom in',
  'NavigationControl.ZoomOut': 'Zoom out',
  'ScaleControl.Feet': 'ft',
  'ScaleControl.Meters': 'm',
  'ScaleControl.Kilometers': 'km',
  'ScaleControl.Miles': 'mi',
  'ScaleControl.NauticalMiles': 'nm',
  'TerrainControl.Enable': 'Enable terrain',
  'TerrainControl.Disable': 'Disable terrain',
  'CooperativeGesturesHandler.WindowsHelpText': 'Use Ctrl + scroll to zoom the map',
  'CooperativeGesturesHandler.MacHelpText': 'Use ⌘ + scroll to zoom the map',
  'CooperativeGesturesHandler.MobileHelpText': 'Use two fingers to move the map',
}

export const zhCNLocales = {
  'FullscreenControl.Enter': '全屏',
  'FullscreenControl.Exit': '退出全屏',
  'NavigationControl.ResetBearing': '重置方位',
  'NavigationControl.ZoomIn': '放大',
  'NavigationControl.ZoomOut': '缩小',
}

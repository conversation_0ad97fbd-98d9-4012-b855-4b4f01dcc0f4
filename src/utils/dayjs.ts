import dayjs, { ManipulateType, OpUnitType } from 'dayjs'
import utc from 'dayjs/plugin/utc'
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter'
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore'
import { Store } from '@src/store'

dayjs.extend(utc)
dayjs.extend(isSameOrAfter)
dayjs.extend(isSameOrBefore)

export { dayjs }
export const DefaultDataStr = '2001-01-01'
export const DateMask = 'YYYY-MM-DD'
export const TimeMask = 'HH:mm:ss'
export const Mask = `${DateMask} ${TimeMask}`
export const InvalidDate = 'Invalid Date'

export function dayjsFormat(t: dayjs.ConfigType, mask = Mask): string {
  return dayjs(t).format(mask)
}

export function localTime(t: dayjs.ConfigType = new Date(), mask = Mask): string {
  return dayjs(t).format(mask)
}

export function utcTime(t: dayjs.ConfigType = new Date(), mask = Mask): string {
  return dayjs.utc(t).format(mask)
}

// 得到系统的时间
export function getSysTime() {
  return Date.now() + Store.state.SysUTCTimeDiff
}

// 获取服务器的utc时间，本地时间加上服务器的时间差
export function sysUtcTime(): string {
  return dayjs.utc(getSysTime()).format(Mask)
}

export function toLocalTime(t: dayjs.ConfigType, mask = Mask): string {
  return dayjs.utc(t).local().format(mask)
}

export function toUtcTime(t: dayjs.ConfigType, mask = Mask): string {
  return dayjs(t).utc().format(mask)
}

// 包装可能出现的错误时间
export function wrapperInvalidDate(t: string, utc = false): string {
  if (!t) { return '' }

  const time: string = utc ? toUtcTime(t) : toLocalTime(t)
  if (time.includes(InvalidDate)) {
    return `${t}[${time}]`
  }

  return time
}

export interface dateDeff {
  days: number
  hours: number
  minutes: number
}

export function getDeff(start: dayjs.ConfigType, now: dayjs.ConfigType): dateDeff {
  const days = dayjs.utc(now).diff(dayjs.utc(start), 'day')
  const hours = dayjs.utc(now).diff(dayjs.utc(start), 'hour')
  const minutes = dayjs.utc(now).diff(dayjs.utc(start), 'minute')
  return {
    days,
    hours: hours - 24 * days,
    minutes: minutes - hours * 60,
  }
}

export function getDeffDay(start: dayjs.ConfigType, now: dayjs.ConfigType): number {
  return dayjs(now).diff(dayjs(start), 'day')
}

export function getDiffMin(start: dayjs.ConfigType, now = dayjs.utc(Date.now())): number {
  return dayjs(now).diff(dayjs.utc(start), 'minute')
}

export function getTimeString(t: dayjs.ConfigType = new Date(), isUtc = false): string {
  if (isUtc) {
    return dayjs.utc(t).format(TimeMask)
  }
  return dayjs(t).format(TimeMask)
}

export function getDateString(t: dayjs.ConfigType = new Date(), isUtc = false): string {
  if (isUtc) {
    return dayjs.utc(t).format(DateMask)
  }
  return dayjs(t).format(DateMask)
}

export function getSubtractTime(t: dayjs.ConfigType, value: number, unit: ManipulateType = 'day'): string {
  return dayjs(t).subtract(value, unit).format(Mask)
}

// 计算4g界桩到期日期还有多少天
export function getDiffFromExpirationDate(ExpirationDate, unit: OpUnitType = 'day') {
  return dayjs.utc(dayjs.utc(ExpirationDate)).diff(utcTime(), unit)
}

export function isBeforeOrSame(start: dayjs.ConfigType, end: dayjs.ConfigType, mask : OpUnitType = 'day') {
  return dayjs(start).isSameOrBefore(end, mask)
}

export function toLocalTimeFormat(val: string, mask = TimeMask) {
  return toLocalTime(`1949-10-01 ${val}`, mask)
}

export function toUtcTimeFormat(val: string, mask = TimeMask) {
  return toUtcTime(`1949-10-01 ${val}`, mask)
}

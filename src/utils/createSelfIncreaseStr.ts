/*
* 用于生成自增编号
* */

function createStr(res: string): string {
  return res.replace(/(\d+)$/g, (m: string) => {
    let len = m.length
    return ((parseInt(m) + 1).toString().padStart(len, '0'))
  })
}

export function createSelfIncreaseStr(res: string, existValues: string[]): string {
  if (!res) return ''
  //如果res中没有数字编号的，则追加1,否则自增数字部分
  if (!/(\d+)$/g.test(res)) {
    res = res + '1'
  } else {
    res = createStr(res)
  }

  //如果existValues中已经存在某一项，那就再自增编号，直到不重复为止
  while ((existValues).includes(res)) {
    res = createStr(res)
  }
  return res
}

export default createSelfIncreaseStr

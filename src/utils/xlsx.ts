import { read, utils, WorkBook, WorkSheet, write, writeFile } from 'xlsx'
import log from './log'
import { Dialog, exportFile, Notify } from 'quasar'
import { i18n } from '@boot/i18n'
import { isApp, webConfig } from '@src/config'
import { cloneDeep } from 'lodash'
import { AndroidFileEntry, createDirectory, requestAndroidFileSystem, resolveAndroidFileURL } from './path'

const { sheet_to_json, json_to_sheet, book_new, book_append_sheet, sheet_add_json } = utils

export function wrapCsvValue(val: any, row?: { [key: string]: any }, formatFn?: any) {
  let formatted = formatFn !== void 0 ? formatFn(val, row) : val
  formatted = formatted === void 0 || formatted === null ? '' : String(formatted)
  formatted = formatted.split('"').join('""')

  return `"${formatted}"`
}

export function encodeCsv(data: any[], columns: any[]): string {
  const header = [columns.map(col => wrapCsvValue(col.label))]
  const body = data.map(row => columns.map(col => wrapCsvValue(
    typeof col.field === 'function'
      ? col.field(row)
      : row[col.field === void 0 ? col.name : col.field],
    row,
    col.format,
  )).join(','))

  return header.concat(body).join('\r\n')
}

export function decodeCsv(csvStr: string): any {
  const book: WorkBook = read(csvStr, { type: 'string', raw: true })
  // 利用xlsx转换成对象数组
  return sheet_to_json(book.Sheets[book.SheetNames[0]])
}

export function formatCsvProps(list: any[], headerFields: { [key: string]: string }): any[] {
  return list.map((data) => {
    return Object.keys(data)
      .filter(k => {
        return Boolean(headerFields[k])
      })
      .map(k => {
        const key = headerFields[k]
        return { [key]: key ? data[k] : undefined }
      })
      .reduce((p, c) => Object.assign(p, c), {})
  })
}

export function exportCordovaData({ fileName, data, mimeType }) {
  // 使用 File Opener2 插件打开指定目录
  const openExportFile = (fileEntry: AndroidFileEntry) => {
    cordova.plugins.fileOpener2.open(
      fileEntry.nativeURL,
      'application/*',
      {
        error: function(error: any) {
          log.error('fileOpener2 openExportFile error', JSON.stringify(error))
        },
        success: function() {
          log.info('fileOpener2 openExportFile success')
        },
      }
    )
  }
  const errorHandle = (err: any) => {
    log.error('exportCordovaData error', JSON.stringify(err))
    Notify.create({
      message: JSON.stringify(err),
      type: 'error',
    })
  }
  const writeFile = (fileEntry: AndroidFileEntry) => {
    fileEntry.createWriter((fileWriter) => {
      fileWriter.onwriteend = () => {
        // 提示用户保存目录
        Dialog.create({
          title: i18n.global.t('message.exportSuccess') as string,
          message: i18n.global.t('message.exportPath', { path: fileEntry.fullPath }) as string,
          class: 'z-top file-path-alarm',
          ok: {
            label: i18n.global.t('export.openFile')
          },
          cancel: true,
        }).onOk(() => {
          openExportFile(fileEntry)
        })
      }
      fileWriter.onerror = errorHandle
      fileWriter.write(new Blob([data], { type: mimeType }))
    })
  }

  requestAndroidFileSystem()
    .then(() => {
      return resolveAndroidFileURL(cordova.file.externalApplicationStorageDirectory)
    })
    .then(entry => {
      return createDirectory(entry, webConfig.downloads || 'downloads')
    })
    .then(dirEntry => {
      dirEntry.getFile(
        fileName,
        { create: true, exclusive: false },
        writeFile,
        errorHandle,
      )
    })
}

export function exportData2CSV({ fileName, data, mimeType }) {
  if (isApp) {
    exportCordovaData({ fileName, data, mimeType })
  } else {
    exportFile(fileName, data, mimeType)
  }
}

export function createWorkSheet(data: Array<{ [key: string]: any }>, header: string[]): WorkSheet {
  return json_to_sheet(data, { header })
}

export function createWorkBook(sheet: WorkSheet): WorkBook {
  const wb = book_new()
  book_append_sheet(wb, sheet)
  return wb
}
export function columnsProcess(columns: Array<{ [key: string]: any }>,
  source: { [key: string]: any }, _row: { [key: string]: any }) {
  columns.forEach(col => {
    const val = typeof col.field === 'function'
      ? col.field(source)
      : source[col.field === void 0 ? col.name : col.field]
    _row[col.label] = typeof col.format === 'function' ? col.format(val, source) : val
    if (col.isTime && _row[col.label]) {
      // 时间有关的参数可能是空， 所有得判断_row(col.label)是否存在
      _row[col.label] = '\'' + _row[col.label]
    }
  })
  return _row
}

export function wrapExcelData(data: Array<{ [key: string]: any }>,
  columns: Array<{ [key: string]: any }>, extraFunc?: (...args: any[]) => any): Array<{ [key: string]: any }> {
  return data.map((row, index) => {
    // row可能被冻结，深拷贝一份
    // const source = (Object.isFrozen(row) || Object.isSealed(row)) ? cloneDeep(row) : row
    const source = cloneDeep(row)
    source.$index = index + 1
    let _row: { [key: string]: any } = {}
    _row = columnsProcess(columns, source, _row)
    if (extraFunc) {
      _row = extraFunc(source, _row)
    }
    return _row
  })
}

export function unWrapExcelData(data: ArrayBuffer,
  columns: Array<{ [key: string]: any }>, extraFunc?: (...args: any[]) => any): Array<{ [key: string]: any }> {
  const book: WorkBook = read(data, { type: 'array', raw: true })
  // 利用xlsx转换成对象数组
  const jsonData: Array<{ [key: string]: any }> = sheet_to_json(book.Sheets[book.SheetNames[0]])
  return jsonData.map(item => {
    let row: { [key: string]: any } = {}
    columns.forEach(col => {
      row[col.field] = item[col.label]
    })
    if (extraFunc) {
      row = extraFunc(row, item)
    }
    return row
  })
}


export function exportData2ExcelV2({ fileName, data, columns, childKey, childColumns}) {
  // 无数据
  if (!data.length) {
    Notify.create({
      message: i18n.global.t('export.noData') as string,
      type: 'warning',
    })
    return
  }

  // 生成主表数据
  const wrapData = wrapExcelData(data, columns)
  const header = columns.map(col => col.label)
  let baseSheet: WorkSheet = {}
  // 先创建第一行主表数据 在从主表数据下方添加子表数据
  for (let i = 0; i < wrapData.length; i++) {
    const item = wrapData[i]
    const childData = wrapExcelData(data[i][childKey], childColumns)
    if (i === 0) {
      baseSheet = createWorkSheet([item], header)
      baseSheet = sheet_add_json(baseSheet, childData, { origin: 'B3'})
    } else {
      // @ts-ignore  子表数据后方空一行在添加主表数据
      let origin = Number(baseSheet['!ref'].split(':')[1].match(/\d+/)[0]) + 2
      baseSheet = sheet_add_json(baseSheet, [item], { origin: 'A'+origin })
      // @ts-ignore 一行主表数据下空出第一列在添加子表数据
      origin = Number(baseSheet['!ref'].split(':')[1].match(/\d+/)[0]) + 1
      baseSheet = sheet_add_json(baseSheet, childData, { origin: 'B'+origin})
    }
  }

  const wb = createWorkBook(baseSheet)
  const bookType = 'xlsx'
  if (fileName.split('.').pop() !== bookType) { fileName += `.${bookType}` }

  if (isApp) {
    const output = write(wb, { bookType, bookSST: false, type: 'array' })
    exportCordovaData({ fileName, data: output, mimeType: 'application/octet-stream' })
  } else {
    writeFile(wb, fileName, { bookType })
  }

}

export function exportData2Excel({ fileName, data, columns }, extraFunc?: (...args: any[]) => any) {
  // 无数据
  if (!data.length) {
    Notify.create({
      message: i18n.global.t('export.noData') as string,
      type: 'warning',
    })
    return
  }

  const wrapData = wrapExcelData(data, columns, extraFunc)
  const header = columns.map(col => col.label)
  const sheet = createWorkSheet(wrapData, header)
  const wb = createWorkBook(sheet)
  const bookType = 'xlsx'
  if (fileName.split('.').pop() !== bookType) { fileName += `.${bookType}` }

  if (isApp) {
    const output = write(wb, { bookType, bookSST: false, type: 'array' })
    exportCordovaData({ fileName, data: output, mimeType: 'application/octet-stream' })
  } else {
    writeFile(wb, fileName, { bookType })
  }
}

const routes = [
  {
    path: '/login',
    name: 'login',
    component: () => import('@layouts/Login.vue'),
  },
  {
    path: '/',
    component: () => import('@layouts/MyLayout.vue'),
    // children: [
    //   { path: '', component: () => import('pages/Index.vue') }
    // ]
  },
  {
    path: '/ssl_update',
    name: 'sslUpdate',
    component: () => import('@layouts/SslUpdate.vue'),
  },
  // Always leave this as last one,
  // but you can also remove it
  {
    path: '/:catchAll(.*)*',
    component: () => import('pages/Error404.vue')
  }
]

export default routes

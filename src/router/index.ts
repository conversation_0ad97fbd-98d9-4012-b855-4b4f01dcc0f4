import { createRouter, createMemoryHistory, createWebHistory, createWebHashHistory } from 'vue-router'
import routes from './routes'
import { Store } from '@src/store'

export default function(/* { store, ssrContext } */) {
  const createHistory = process.env.SERVER
    ? createMemoryHistory
    : process.env.VUE_ROUTER_MODE === 'history' ? createWebHistory : createWebHashHistory

  const Router = createRouter({
    scrollBehavior: () => ({ left: 0, top: 0 }),
    routes,

    // Leave this as is and make changes in the quasar.config file instead!
    // quasar.config file -> build -> vueRouterMode
    // quasar.config file -> build -> publicPath
    history: createHistory(process.env.VUE_ROUTER_BASE)
  })

  Router.beforeEach(async (to, from, next) => {
    // 更新证书，不需要验证登录
    if (to.name === 'sslUpdate') {
      return next()
    }

    const alreadyLogin = Store.state.isLogin
    // 未登录
    if (!alreadyLogin && to.name !== 'login') {
      return next({ name: 'login' })
    }

    next()
  })

  return Router
}

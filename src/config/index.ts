import type { Map } from 'maplibre-gl'
import { v1 as uuidV1 } from 'uuid'
import { Loading, Platform } from 'quasar'

export const DefUuid = uuidV1().replace(/[^-]/ig, '0')

export enum appLang {
  zhCN = 'zh-CN',
  enUs = 'en-US'
}

export interface IWebConfig {
  [key: string]: any

  map: { [key: string]: any }
  language: string
  abnormalTime: number
  alarmDistance: number
  downloads: string
}

export const webConfig: IWebConfig = {
  map: {},
  // 首次进入系统时加载的语言，支持以下语言：['zh-hans','en-us']
  language: appLang.zhCN,
  // 异常打卡间隔，默认3天，即超出3天没有打卡的界桩才视为异常，需要维护
  abnormalTime: 3,
  // 界桩报警时，上传的定位数据与系统设置的经纬度相关多少米，同时显示两个图标，默认10米
  alarmDistance: 10,
  // App导出Excel数据目录
  downloads: 'downloads',
}

export function setWebConfig(config: IWebConfig) {
  Object.assign(webConfig, config)
}

export interface IGlobalConfig {
  [key: string]: any

  map: Map | undefined
  webConfig: IWebConfig
}

export const globalConfig: IGlobalConfig = {
  map: undefined,
  webConfig,
}

export default globalConfig

export const NoRows = 'no rows in result'
export const NoOptPerm = 'No operation permission'

export const globalLoading = {
  show(message) {
    Loading.show({
      message,
    })
  },
  hide() {
    Loading.hide()
  },
}

export const isApp = Platform.is.cordova || Platform.is.capacitor

const protocol = process.env.protocol ?? 'http:'
const hostname = process.env.hostname ?? '127.0.0.1'
const port = process.env.port ?? (protocol === 'https:' ? '443' : '80')
export const serverBaseUrl = `${protocol}//${hostname}:${port}`

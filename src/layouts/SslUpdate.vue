<template>
  <div class="flex flex-col items-center justify-center min-w-96 min-h-screen ssl-update gap-8">
    <q-banner dense rounded class="bg-orange text-white">
      <template v-slot:avatar>
        <q-icon name="warning"/>
      </template>

      {{ $t('sslUpdate.updateCertTips') }}
    </q-banner>

    <q-form @submit="onSubmit" class="min-w-96 flex flex-col gap-4">
      <q-file outlined v-model="crtFile"
              accept=".crt"
              :label="$t('sslUpdate.certificate')"
              :rules="[requiredRule]"/>

      <q-file outlined v-model="keyFile"
              accept=".key"
              :label="$t('sslUpdate.key')"
              :rules="[requiredRule]"/>

      <q-input
        outlined
        v-model="token"
        :label="$t('sslUpdate.token')"
        lazy-rules
        :rules="[requiredRule]"
      />

      <q-btn
        type="submit"
        :label="$t('sslUpdate.updateCertificate')"
        color="primary"
        unelevated
        no-caps
        icon="cloud_upload"
        :loading="isUploading"
        class="py-2"
      />
    </q-form>

    <q-banner dense rounded class="min-w-96 bg-slate-200" v-if="!!crtFile">
      <q-item dense class="flex items-center gap-4">
        <q-item-label>{{ $t('sslUpdate.domainName') }}</q-item-label>
        <q-item-label class="!mt-0">{{ certHosts.join(',') }}</q-item-label>
      </q-item>
      <q-item dense class="flex items-center gap-4">
        <q-item-label>{{ $t('sslUpdate.issueDate') }}</q-item-label>
        <q-item-label class="!mt-0" :class="{'text-red-600': validFrom >= new Date()}">{{ validFromStr }}</q-item-label>
      </q-item>
      <q-item dense class="flex items-center gap-4">
        <q-item-label>{{ $t('sslUpdate.expiredDate') }}</q-item-label>
        <q-item-label class="!mt-0" :class="{'text-red-600': validTo <= new Date()}">{{ valueToStr }}</q-item-label>
      </q-item>
    </q-banner>
  </div>
</template>

<script setup lang="ts">
  import { computed, ref, watch } from 'vue'
  import { useI18n } from 'vue-i18n'
  import { Notify } from 'quasar'
  import forge from 'node-forge'
  import type { Certificate } from 'node-forge'
  import { dayjsFormat } from '@utils/dayjs';
  import { waitDialogResult } from '@utils/common';

  const invalidDate = new Date('2000-01-01 00:00:00')
  const { t } = useI18n()
  const crtFile = ref<File | null>(null)
  const keyFile = ref<File | null>(null)
  const token = ref<string>('')
  const isUploading = ref(false)
  const validFrom = ref<Date>(invalidDate)
  const validTo = ref<Date>(invalidDate)
  const validFromStr = computed(() => dayjsFormat(validFrom.value))
  const valueToStr = computed(() => dayjsFormat(validTo.value))
  const certHosts = ref<Array<string>>([])


  // 获取证书的域名信息
  function getCertificateHosts(cert: Certificate) {
    const hosts: Array<string> = [];

    // 1. Check Subject Alternative Name (SAN) extension
    const altNames = cert.getExtension('subjectAltName');
    if (altNames) {
      for (const name of altNames.altNames) {
        if (name.type === 2) { // type 2 represents DNS name
          hosts.push(name.value);
        }
      }
    }

    // 2. Check Common Name (CN) in the subject (fallback)
    //    Note:  SAN is preferred over CN, which is considered deprecated for hostnames
    const commonName = cert.subject.getField('CN');
    if (commonName && !hosts.includes(commonName.value)) {
      hosts.push(commonName.value);
    }

    return hosts;
  }

  watch(crtFile, async () => {
    if (crtFile.value === null) {
      validFrom.value = invalidDate
      validTo.value = invalidDate
      return
    }

    const keyPem = await crtFile.value.text()
    const cert = forge.pki.certificateFromPem(keyPem)
    validFrom.value = cert.validity.notBefore
    validTo.value = cert.validity.notAfter
    certHosts.value = getCertificateHosts(cert);
  })

  const requiredRule = (val: any) => !!val || t('rules.required')

  async function onSubmit() {
    // 没有选择证书文件
    if (!crtFile.value || !keyFile.value) {
      Notify.create({
        type: 'negative',
        icon: 'warning',
        message: t('sslUpdate.noFileSelected'),
        position: 'top',
      })
      return
    }

    // 判断证书是否过期
    if (validTo.value < new Date()) {
      Notify.create({
        type: 'negative',
        icon: 'warning',
        message: `${t('sslUpdate.certExpired')}: ${dayjsFormat(validTo.value)}`,
        position: 'top',
      })
      return
    }

    // 二次确认更新证书
    const isOk = await waitDialogResult({
      message: t('sslUpdate.updateCertTips'),
      ok: {
        label: t('common.confirm'),
        color: 'orange',
        noCaps: true,
        rounded: true,
        unelevated: true,
        class: 'w-24',
      },
      cancel: {
        label: t('common.cancel'),
        color: 'primary',
        noCaps: true,
        flat: true,
        rounded: true,
        class: 'w-24',
      },
      persistent: true,
      focus: 'cancel',
    })
    if (!isOk) {
      return
    }

    isUploading.value = true
    const formData = new FormData()
    formData.append('token', token.value)
    const crtBytes = await crtFile.value.arrayBuffer()
    const crtBlob = new Blob([crtBytes])
    formData.append('crt', crtBlob)
    const keyBytes = await keyFile.value.arrayBuffer()
    const keyBlob = new Blob([keyBytes])
    formData.append('key', keyBlob)

    try {
      const response = await fetch('/sslupdate', {
        method: 'POST',
        body: formData,
      })

      if (!response.ok) {
        console.error('ssl update error: ', response.status, response.statusText)
        Notify.create({
          type: 'negative',
          icon: 'warning',
          message: `${response.status}: ${response.statusText}`,
          position: 'top',
        })
        return
      }

      Notify.create({
        type: 'positive',
        icon: 'success',
        message: t('sslUpdate.certUpdateSuccess'),
        position: 'top',
      })
    } catch (error) {
      console.error('ssl update catch error: ', error)
      Notify.create({
        type: 'negative',
        icon: 'warning',
        message: JSON.stringify(error),
        position: 'top',
      })
    } finally {
      isUploading.value = false
    }
  }
</script>

<style scoped lang="scss">

</style>

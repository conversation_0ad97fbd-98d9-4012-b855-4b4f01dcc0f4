<template>
  <q-layout view="hHh lpR fFf">
    <q-header elevated>
      <q-toolbar>
        <q-btn
          flat
          dense
          round
          @click="showMobileNav = !showMobileNav"
          icon="menu"
          aria-label="Menu"
          class="q-mr-sm lt-sm"
        />

        <q-avatar
          rounded
          class="q-mr-sm"
          size="38px"
        >
          <q-img
            :src="appLogo"
            height="38px"
            alt="logo"
          />
        </q-avatar>

        <!-- PC端导航菜单 -->
        <template v-if="$q.screen.gt.xs">
          <q-btn-dropdown
            v-for="(menu, index) in initMenuItems"
            :key="index"
            stretch
            flat
            auto-close
            dense
            :label="menu.label"
          >
            <q-list
              dense
              class="nav-menus"
              separator
            >
              <q-item
                v-for="(menuItem, idx) in menu.list"
                :key="idx"
                clickable
                v-close-popup
                @click="onMenuItemClick(menuItem)"
                :class="[menuItem.name]"
              >
                <q-item-section
                  avatar
                  v-if="menuItem.icon"
                >
                  <q-icon
                    :name="menuItem.icon"
                    :color="menuItem.iconColor"
                  />
                </q-item-section>
                <q-item-section>
                  <q-item-label v-text="menuItem.label" />
                </q-item-section>
              </q-item>
            </q-list>
          </q-btn-dropdown>
        </template>

        <q-toolbar-title
          ref="toolbarTitle"
          class="site-toolbar-title"
        >
          <div
            class="scroll-wrapper"
            ref="siteTitleWrapper"
          >
            <span
              ref="siteTitle"
              class="site-title"
              v-text="scrollTitle"
            ></span>
          </div>
        </q-toolbar-title>

        <!-- 与服务器连接标志 -->
        <q-icon
          :name="isConnected ? 'mdi-link-variant' : 'mdi-link-variant-off'"
          :color="isConnected ? 'positive' : 'negative'"
          size="large"
          class="q-ml-sm connect-flag"
          :class="{
            'connected': isConnected,
            'disconnect': !isConnected,
          }"
        />

        <!-- PC端细节按钮 -->

        <template v-if="$q.screen.gt.xs">
          <!-- 设备故障提示 -->
          <q-btn
            round
            unelevated
            dense
            :icon="hasAlarmTips ? 'notification_important' : 'notifications'"
            class="q-ml-sm device-alarm-tips"
            @click="openAbnormalMarkerStatisticPanel"
          >
            <q-badge
              v-if="haveError"
              color="red"
              rounded
              floating
            />
          </q-btn>

          <!-- 登录用户信息及快捷按钮 -->
          <q-btn-dropdown
            flat
            auto-close
            no-caps
            dense
            :label="userName"
            class="q-ml-sm"
          >
            <q-list dense>
              <q-item
                clickable
                @click="$q.fullscreen.toggle()"
              >
                <q-item-section>{{ $t('menus.toggleFullscreen') }}</q-item-section>
              </q-item>
              <q-item
                clickable
                @click="toggleScrollTitle"
              >
                <q-item-section v-if="!isScrollTitle">{{ $t('menus.enableScrollTitle') }}</q-item-section>
                <q-item-section v-else>{{ $t('menus.stopScrollTitle') }}</q-item-section>
              </q-item>
              <q-item clickable @click="changeLang(appLang.enUs)">
                <div class="items-center flex q-mr-xs">
                  <q-icon name="check_circle" :color="isEN ? 'primary' : 'transparent' " />
                </div>
                <q-item-section>{{ $t('languages.enUs') }}</q-item-section>
              </q-item>
              <q-item clickable @click="changeLang(appLang.zhCN)">
                <div class="items-center flex q-mr-xs">
                  <q-icon name="check_circle" :color="!isEN ? 'primary' : 'transparent' " />
                </div>
                <q-item-section>{{ $t('languages.zhCN') }}</q-item-section>
              </q-item>

              <q-separator />

              <q-item
                clickable
                @click="onClickSettings"
              >
                <q-item-section>{{ $t('menus.settings') }}</q-item-section>
              </q-item>
            </q-list>
          </q-btn-dropdown>
        </template>
        <div
          class="q-ml-sm header-data-time"
          v-if="$q.screen.gt.xs"
        >
          {{ dateTimeStr }}
        </div>

        <q-btn
          flat
          dense
          round
          @click="showDeviceTree = !showDeviceTree"
          icon="account_tree"
          aria-label="device tree"
          class="q-ml-sm"
        />
      </q-toolbar>
    </q-header>

    <!-- 左侧导航菜单，移动端可见 -->
    <q-drawer
      v-model="showMobileNav"
      show-if-above
      bordered
      overlay
      no-swipe-open
      no-swipe-close
      no-swipe-backdrop
      class="bg-white row column"
      v-if="$q.screen.lt.sm"
    >
      <!-- 顶部用户信息 -->
      <q-img
        :src="bgUserInfo"
        height="120px"
      >
        <div class="full-width full-height row column q-px-md q-py-sm bg-transparent">
          <!-- 登录用户信息及快捷按钮 -->
          <div class="user-info col">
            <div class="user-name relative-position q-mb-xs">
              <span>{{ $t('form.userName') }}: {{ userName }}</span>
            </div>
            <div class="user-name">
              <span>{{ $t('form.unit') }}: {{ userUnitName }}</span>
            </div>
          </div>
          <div class="header-data-time row justify-end text-caption">
            {{ dateTimeStr }}
          </div>
        </div>
      </q-img>

      <!-- 导航菜单 -->
      <q-scroll-area class="col nav-scroll-area">
        <q-list
          separator
          class=""
        >

          <template
            v-for="menu in initMenuItems"
            :key="menu.label"
          >
            <q-expansion-item
              group="mobileMenus"
              :label="menu.label"
              header-class="text-primary"
              expand-icon-class="text-primary"
            >
              <q-list
                class="nav-menus nav-menus-mobile bg-slate-100"
                separator
              >
                <q-item
                  v-for="(menuItem, idx) in menu.list"
                  :key="idx"
                  clickable
                  @click="onMenuItemClick(menuItem)"
                  :class="[menuItem.name]"
                >
                  <q-item-section
                    avatar
                    v-if="menuItem.icon"
                  >
                    <q-icon
                      :name="menuItem.icon"
                      :color="menuItem.iconColor"
                    />
                  </q-item-section>
                  <q-item-section>
                    <q-item-label v-text="menuItem.label" />
                  </q-item-section>
                </q-item>
              </q-list>
            </q-expansion-item>
          </template>
        </q-list>
      </q-scroll-area>

      <!-- NFC巡查相关 -->
      <template v-if="isApp && hasMarkerPatrol">
        <q-list
          bordered
          separator
        >
          <q-item
            clickable
            @click="nfcPatrol"
          >
            <q-item-section avatar>
              <q-icon
                name="mdi-nfc"
                color="purple"
              />
            </q-item-section>
            <q-item-section>
              <q-item-label>{{ $t('NFCPatrol.NFCPatrol') }}</q-item-label>
            </q-item-section>
          </q-item>
        </q-list>
      </template>

      <!-- 底部快捷按钮 -->
      <q-list
        padding
        class="bottom-quick-btn"
      >
        <q-checkbox
          :modelValue="isScrollTitle"
          :label="$t('menus.enableScrollTitle')"
          @update:model-value="toggleScrollTitle"
        />
        <div>
          <!-- 设备故障提示 -->
          <q-btn
            flat
            round
            :icon="hasAlarmTips ? 'notification_important' : 'notifications'"
            class="device-alarm-tips"
            @click="openAbnormalMarkerStatisticPanel"
            color="primary"
          >
          </q-btn>
          <q-btn
            round
            flat
            icon="settings"
            class="device-alarm-tips"
            @click="onClickSettingBtn"
            color="purple"
          >
          </q-btn>
          <!--切换语言-->
          <q-btn flat round icon="translate" color="primary" @click="showChangeLang"></q-btn>
        </div>
      </q-list>
    </q-drawer>

    <!-- 主页内容 -->
    <q-page-container class="layout-page-container">
      <mapbox></mapbox>
      <!-- <router-view></router-view> -->
    </q-page-container>

    <!-- 设备列表树 -->
    <q-drawer
      side="right"
      v-model="showDeviceTree"
      bordered
      overdlay
      behavior="desktop"
      :width="deviceTreeDrawerWidth"
    >
      <devices-tree />
    </q-drawer>

    <!-- 对话框 -->
    <component
      v-for="dialog in dialogs"
      :key="dialog.name"
      :is="dialog"
      :ref="dialog.name"
    ></component>

    <!-- 报警音频播放 -->
    <audio
      class="hidden"
      :src="alarmAudioSrc"
      ref="alarmAudio"
    ></audio>
  </q-layout>
</template>

<script
  setup
  lang="ts"
>
  import { isApp } from '@src/config'
  import { ComponentInternalInstance, ComponentPublicInstance, getCurrentInstance } from 'vue'

  interface ProxyProps {
    showMobileNav: boolean
    onOpenMenu: (path: string) => Promise<any>
  }

  const instance = getCurrentInstance() as ComponentInternalInstance
  const proxy = instance.proxy as ComponentPublicInstance<ProxyProps>

  function nfcPatrol() {
    proxy.showMobileNav = false
    proxy.onOpenMenu('NFCPatrol')
  }
</script>

<script lang="ts">
  import { defineComponent, DefineComponent, markRaw } from 'vue'
  import { StrPubSub } from 'ypubsub'
  import { DefUuid, globalLoading, appLang } from '@src/config'
  import { localTime, sysUtcTime, toLocalTime } from '@src/utils/dayjs'
  import DevicesTree from '@src/pages/DevicesTree.vue'
  import { GET_DATA, NS } from '@store/data/methodTypes'
  import { DataName, Store } from '@src/store'
  import { PermissionType } from '@utils/permission'
  import { cloneDeep } from 'lodash'
  import { OpenMenu, PlayAlarmAudio, StopAlarmAudio, ToggleI18nLang } from '@utils/pubSubSubject'
  import { user } from '@ygen/user'
  import { isHaveMaintainPerm } from '@utils/common'
  import { openPdf } from '@utils/documentViewer'
  import appLogoSrc from '@assets/app-logo.jpg'
  import bgUserInfoSrc from '@assets/bg-mobile-user-info.png'
  import Mapbox from '@pages/Mapbox.vue'
  import { BysMarkerAbnormalRidSet } from '@services/dataStore'
  import storage, { StoreLangKey } from '@utils/storage'

  let scrollTitleTimer: number | null = null
  let dateTimeTimer: number | null = null

  const _pages = import.meta.glob('@src/pages/*.vue')
  const pages = Object.keys(_pages)
    .map((key) => {
      let name = key.split('/').pop() ?? ''
      name = name.replace(/\.vue$/g, '')

      return {
        [name]: _pages[key],
      }
    })
    .reduce((p, c) => Object.assign(p, c), {})

  type VueComponent = DefineComponent<Record<string, unknown>, Record<string, unknown>, any>

  // 将各类菜单添加类数据包装，生成指定的格式
  enum sortArray {
    Data = 1, Organization, User, Role, Controller, BysMarker, MediaInfo,
    Query, ControllerOnlineHistory, MarkerHistory, MarkerUploadImageHistory, MarkerNFCPatrolHistory, OptionHistory,
    Help, SystemDoc, VersionInfo, CmdTest, MarkerStatistics
  }

  // NFC巡查历史权限字段
  const MarkerPatrolHistory = 'Query.DbMarkerPatrolHistory'

  // 添加必要的类型定义
  interface ImportedComponent {
    default: VueComponent
  }

  export default defineComponent({
    name: 'MyLayout',
    data() {
      return {
        showMobileNav: false,
        showDeviceTree: true,
        dialogs: {} as Record<string, VueComponent>,
        dateTimeStr: localTime(),
        // controller or bysMarker
        alarmTab: 'controller',
        bgUserInfo: bgUserInfoSrc,
        alarmAudioSrc: '/audio/emergency_cn.wav',
      }
    },
    computed: {
      isEN() {
        return this.$i18n.locale === appLang.enUs
      },
      abnormalBysMarkerSize() {
        return BysMarkerAbnormalRidSet.size
      },
      haveError() {
        return this.abnormalBysMarkerSize !== 0 || Store.getters[`${NS}/${GET_DATA}`](DataName.Controller).find(item => item.Error?.err)
      },
      systemSettings() {
        return this.$store.state.systemSettings
      },
      appLogo() {
        return this.systemSettings.appLogo?.ConfValue || appLogoSrc
      },
      userUnitName() {
        if (this.$store.state.UserOrgRID === DefUuid) {
          return this.$t('form.default')
        }
        return this.$store.getters[`${NS}/${GET_DATA}`](DataName.Unit, this.$store.state.UserOrgRID)?.ShortName
      },
      userData(): user.IDbUser | undefined {
        return this.$store.getters[`${NS}/${GET_DATA}`](DataName.User, this.$store.state.UserRID)
      },
      userName() {
        return this.userData?.UserName || ''
      },
      userLoginName() {
        return this.userData?.LoginName || ''
      },
      isScrollTitle() {
        return this.$store.state.isScrollTitle
      },
      scrollTitle() {
        return this.systemSettings.systemTitle?.ConfValue || this.$t('siteTitle')
      },
      hasAlarmTips() {
        return this.$store.state.hasAlarmTips
      },
      isConnected() {
        return this.$store.state.isConnected
      },
      deviceTreeDrawerWidth() {
        return this.$q.screen.lt.sm ? 200 : 300
      },
      // 所有菜单项对象集，以菜单权限从该对象中读取数据，动态生成菜单
      menuCenter() {
        return {
          Data: {
            label: this.$t('menus.data'),
            list: [],
          },
          DbOrg: {
            icon: 'iconfont bys-org',
            label: this.$t('menus.organization'),
            name: 'Organization',
          },
          DbUser: {
            icon: 'iconfont bys-user',
            label: this.$t('menus.user'),
            name: 'User',
          },
          DbRole: {
            icon: 'iconfont bys-role',
            label: this.$t('menus.role'),
            name: 'Role',
          },
          DbController: {
            icon: 'iconfont bys-base-station',
            label: this.$t('menus.controller'),
            name: 'Controller',
          },
          DbBysMarker: {
            icon: 'iconfont bys-jiezhuang',
            label: this.$t('menus.boundaryMarker'),
            name: 'BysMarker',
          },
          DbMediaInfo: {
            icon: 'iconfont bys-camera',
            label: this.$t('menus.images'),
            name: 'MediaInfo',
          },
          DbNFCPatrolLine: {
            icon: 'timeline',
            label: this.$t('menus.patrolLines'),
            name: 'PatrolLine',
          },
          DbNFCPatrolLineRules: {
            icon: 'design_services',
            label: this.$t('menus.patrolRules'),
            name: 'PatrolRule',
          },
          Query: {
            label: this.$t('menus.query'),
            list: [],
          },
          DbControllerOnlineHistory: {
            icon: 'iconfont bys-controller-history',
            label: this.$t('menus.controllerOnlineRecords'),
            name: 'ControllerOnlineRecords',
          },
          DbMarkerHistory: {
            icon: 'iconfont bys-marker-history',
            label: this.$t('menus.markerHistory'),
            name: 'MarkerHistory',
          },
          DbMarkerUploadImageHistory: {
            icon: 'upload_file',
            label: this.$t('menus.markerUploadImageHistory'),
            name: 'MarkerUploadImageHistory',
          },
          DbMarkerPatrolHistory: {
            icon: 'mdi-nfc',
            iconColor: 'purple',
            label: this.$t('menus.MarkerNFCPatrolHistory'),
            name: 'MarkerNFCPatrolHistory',
          },
          PatrolStatistics: {
            icon: 'trending_up',
            iconColor: 'lime',
            label: this.$t('NFCPatrol.patrolStatistics'),
            name: 'PatrolStatistics',
          },
          crudlog: {
            icon: 'iconfont bys-marker-history',
            label: this.$t('menus.optionHistory'),
            name: 'OptionHistory',
          },
          Help: {
            label: this.$t('menus.help'),
            list: [],
          },
          SystemDoc: {
            icon: 'iconfont bys-help',
            label: this.$t('menus.systemDoc'),
            name: 'systemDoc',
          },
          VersionInfo: {
            icon: 'iconfont bys-version',
            label: this.$t('menus.versionInfo'),
            name: 'VersionInfo',
          },
          CmdTest: {
            icon: 'iconfont bys-cmd-list',
            label: this.$t('menus.cmdTest'),
            name: 'CmdTest',
          },
          MarkerStatistics: {
            icon: 'addchart',
            label: this.$t('menus.markerStatistics'),
            name: 'MarkerStatistics',
          },
          DownLoad: {
            icon: 'iconfont bys-clouddownload',
            label: this.$t('menus.downLoad'),
            name: 'DownLoad',
          },
        }
      },
      // 类型为menu在权限集合
      menuPerms() {
        return this.$store.getters[`${NS}/${GET_DATA}`](DataName.Permission)
          .filter(v => v.PermissionType === PermissionType.menu)
      },
      initMenuItems() {
        // 缓存各类菜单，如数据类、查询类、命令类等
        const menus: { [key: string]: any[] } = {}
        // 遍历权限，动态生成菜单
        for (let item of this.menuPerms) {
          const res = this.splitPermValue(item.PermissionValue)
          // 该类菜单项不存在，则过滤不处理
          if (!this.menuCenter[res.name] || !this.menuCenter[res.perm]) {
            continue
          }
          // 该类菜单没有，则生成一个数组，如Data类菜单
          if (!menus[res.name]) {
            menus[res.name] = []
          }
          // 从所有菜单对象中读取菜单项数据，如单位、用户等
          const menuItem = cloneDeep(this.menuCenter[res.perm])
          menus[res.name].push(menuItem)
        }
        //添加帮助菜单
        menus.Help = [
          cloneDeep(this.menuCenter.SystemDoc),
          cloneDeep(this.menuCenter.VersionInfo),
          cloneDeep(this.menuCenter.MarkerStatistics),
        ]
        //如果有维修权限
        if (isHaveMaintainPerm()) {
          menus.Help.push(cloneDeep(this.menuCenter.CmdTest))
        }
        if (!isApp) {
          menus.Help.push(cloneDeep(this.menuCenter.DownLoad))
        }

        /*
        * 1.根菜单排序
        * 2.叶子菜单排序
        * */
        return Object.keys(menus).sort((a, b) => {
          return sortArray[a] - sortArray[b]
        })
          .map(k => {
            const typeMenu = cloneDeep(this.menuCenter[k])
            typeMenu.list = menus[k].sort((a, b) => {
              return sortArray[a.name as string] - sortArray[b.name as string]
            })
            return typeMenu
          })
      },
      hasMarkerPatrol() {
        return this.menuPerms.find(v => v.PermissionValue === MarkerPatrolHistory)
      }
    },
    methods: {
      showChangeLang() {
        this.$q.bottomSheet({
          class: 'change-lang-sheet',
          actions: [
            {
              id: appLang.zhCN,
              label: this.$t('languages.zhCN') as string,
              icon: this.$i18n.locale === appLang.zhCN ? 'check_circle' : undefined,
            },
            {},
            {
              id: appLang.enUs,
              label: this.$t('languages.enUs') as string,
              icon: this.$i18n.locale === appLang.enUs ? 'check_circle' : undefined,
            }
          ],
        }).onOk(action => {
          this.changeLang(action.id)
        })
      },
      changeLang(lang: string) {
        storage.save(StoreLangKey, lang)
        StrPubSub.publish(ToggleI18nLang, lang)
      },
      enableDateTime() {
        this.dateTimeStr = toLocalTime(sysUtcTime())
        dateTimeTimer = window.setInterval(() => {
          this.dateTimeStr = toLocalTime(sysUtcTime())
        }, 1000)
      },
      stopDateTime() {
        dateTimeTimer && clearInterval(dateTimeTimer)
      },
      splitPermValue(permValue: string) {
        let strArray = permValue.split('.')
        return { 'name': strArray[0], 'perm': strArray[1] }
      },
      async onClickSettings() {
        //进入到用户设置界面
        this.onOpenMenu('Settings')
      },
      onClickSettingBtn() {
        this.closeMobileNav()
        this.onClickSettings()
      },
      // 根据参数是否让标题滚动起来
      stopScrollSiteTitle() {
        scrollTitleTimer && clearInterval(scrollTitleTimer)
        const wrapper = this.$refs.siteTitleWrapper as HTMLElement
        if (wrapper) {
          wrapper.style.transform = 'none'
        }
      },
      onScrollSiteTitle() {
        // 每次启动，先清除旧的定时器
        this.stopScrollSiteTitle()

        // 未开启滚动
        if (!this.isScrollTitle) {
          return
        }

        // 滚动标题窗口和内容
        const toolbarTitle = (this.$refs.toolbarTitle as { $el: HTMLElement })?.$el
        const siteTitleWrapper = this.$refs.siteTitleWrapper as HTMLElement
        const siteTitle = this.$refs.siteTitle as HTMLElement
        if (!toolbarTitle || !siteTitleWrapper || !siteTitle) {
          return
        }

        // 设置滚动初始偏移
        const titleBoxWidth: number = toolbarTitle.offsetWidth
        siteTitleWrapper.style.transform = `translateX(${titleBoxWidth}px)`

        const Marquee = () => {
          const transform = siteTitleWrapper?.style.transform
          if (!transform) {
            this.stopScrollSiteTitle()
            return
          }

          let x = parseInt(/translateX\((-?\w+)px\)/ig.exec(transform)?.[1] || '0')
          const titleWidth = siteTitle?.offsetWidth
          // 当左移的距离小于标题的宽度时，重新开始从右往左移
          if (x <= -(titleWidth as number)) {
            x = titleBoxWidth
          } else {
            // 计算translate平移距离，左移值递减
            x -= 6
          }

          (siteTitleWrapper as HTMLElement).style.transform = `translateX(${x}px)`
        }

        // 启动定时器，循环滚动标题
        scrollTitleTimer = window.setInterval(Marquee, 150)
      },
      toggleScrollTitle() {
        const payload = { value: !this.isScrollTitle }
        this.$store.commit('isScrollTitle', payload)
        this.onScrollSiteTitle()
      },

      closeMobileNav() {
        if (this.$q.platform.is.mobile) {
          this.showMobileNav = false
        }
      },
      // 导航菜单事件
      onMenuItemClick(menuItem: { [key: string]: any }) {
        // App没有下载app页面功能
        if ('DownLoad' === menuItem.name) {
          !isApp && window.open('/app')
          return
        }

        if ('systemDoc' === menuItem.name) {
          if (isApp) {
            openPdf('doc/systemManual.pdf')
          } else {
            window.open('/doc/systemManual.pdf')
          }
          return
        }

        this.closeMobileNav()
        this.onOpenMenu(menuItem.name)
      },
      async onOpenMenu(path: string) {
        const dialog = await this.loadComponent(path)
        if (!dialog) {
          return
        }
        const name = dialog.name ?? dialog.type?.name ?? dialog.__name
        if (!this.dialogs[name]) {
          this.dialogs[name] = markRaw(dialog)
        }

        // 组件挂载后显示对话框
        this.$nextTick(() => {
          StrPubSub.publish(`${name}-visible`, true)
          // 权限组件显示最前
          // const vm = this.$refs[dialog.name]?.[0]
          // vm.$children[0]?.showToTop?.()
        })

        return dialog
      },
      async loadComponent(path: string) {
        const res = await pages[path]() as ImportedComponent
        return res && res.default
      },

      // 打开异常界庄的统计面板
      openAbnormalMarkerStatisticPanel() {
        this.closeMobileNav()
        this.onOpenMenu('AbnormalStatisticPanel')
      },
      resizePageHeight() {
        const el = document.querySelector('.layout-page-container') as HTMLElement
        el.style.height = window.innerHeight + 'px'
      },
    },
    watch: {
      // 如果是小屏，则不显示设备树
      '$q.screen.lt.sm': {
        immediate: true,
        handler(val) {
          this.showDeviceTree = !val
        },
      },
      // 移动端没有滚动标题
      '$q.platform.is.mobile': {
        immediate: true,
        handler(isMobile) {
          isMobile && this.stopScrollSiteTitle()
        },
      },
      '$store.state.isLogin'(val) {
        if (val) {
          globalLoading.show(
            `<span class="text-orange text-h5">
              ${this.$t('message.loginSuccess')}
                                                                                                <br>
                                                                                                ${this.$t('message.loadingData')}
            </span>`,
          )
        }
      },
      // 每次同步服务器时间差后，监听变化，同步更新头部时间文本
      '$store.state.SysUTCTimeDiff': {
        immediate: true,
        handler() {
          this.stopDateTime()
          this.enableDateTime()
        },
      },
    },
    components: {
      DevicesTree,
      Mapbox,
    },
    beforeMount(): void {
      StrPubSub.subscribe(OpenMenu, async (path: string, done: Function) => {
        const dialog = await this.onOpenMenu(path)
        this.$nextTick(() => {
          // 返回对应的vue实例
          const name = dialog?.name
          if (name) {
            done(this.$refs[name]?.[0])
          } else {
            done(undefined)
          }
        })
      })
      // // 启动时间定时器
      // this.enableDateTime()

      // 监听播放报警音频事件
      StrPubSub.subscribe(PlayAlarmAudio, () => {
        (this.$refs.alarmAudio as HTMLAudioElement)?.play()
      })
      StrPubSub.subscribe(StopAlarmAudio, () => {
        const audio = this.$refs.alarmAudio as HTMLAudioElement
        if (!audio) {
          return
        }
        audio.pause()
        audio.currentTime = 0
      })
    },
    beforeUnmount(): void {
      StrPubSub.unsubscribe(OpenMenu, this.onOpenMenu)
      StrPubSub.unsubscribe(PlayAlarmAudio)
      StrPubSub.unsubscribe(StopAlarmAudio)
      this.stopScrollSiteTitle()
      window.removeEventListener('resize', this.resizePageHeight)
    },
    mounted(): void {
      this.$nextTick(() => {
        this.onScrollSiteTitle()
      })
      this.resizePageHeight()
      window.addEventListener('resize', this.resizePageHeight)
    },
  })
</script>

<style lang="scss">
  @import "quasar/src/css/variables.sass";

  .layout-page-container {
    width: 100vw;
    height: 100vh;
    overflow: hidden;
  }

  .badge-dot {
    padding: 4px;
    border-radius: 4px;
    right: 0;
    top: 0;
  }

  .nav-menus.q-list {
    .q-item {
      .q-icon {
        color: inherit;
      }

      &.Organization .q-icon {
        color: $purple-8;
      }

      &.User .q-icon {
        color: $blue-8;
      }

      &.Role .q-icon {
        color: $orange-8;
      }

      &.Controller .q-icon {
        color: $green-8;
      }

      &.BysMarker .q-icon {
        color: $blue-8;
      }

      &.MediaInfo .q-icon {
        color: $indigo-8;
      }

      &.ControllerOnlineRecords .q-icon {
        color: $blue-8;
      }

      &.MarkerHistory .q-icon {
        color: $orange-8;
      }

      &.systemDoc .q-icon {
        color: $purple-8;
      }

      &.VersionInfo .q-icon {
        color: $cyan-8;
      }

      &.CmdTest .q-icon {
        color: $orange-8;
      }

      &.PatrolLine .q-icon {
        color: $lime-8;
      }

      &.PatrolRule .q-icon {
        color: $cyan-8;
      }
    }

    .q-item__section--avatar {
      min-width: unset;
    }
  }

  .online-tip {
    display: block;
    border-radius: 50%;
    width: 10px;
    height: 10px;
    position: absolute;
    left: 0;
  }

  .abnormal-marker-badge {
    position: absolute;
    top: 1px;
  }

  .connect-flag.q-icon {
    &.connected {
      color: #00ff00 !important;
    }
  }

  .nav-menus-mobile.q-list > .q-item-type + .q-item-type {
    border-top: 1px solid #fafafa !important;
  }

  .q-form .q-field--outlined .q-field__control:before {
    border: 1px solid #dcdee2;
  }

  .change-lang-sheet > div {
    font-size: 18px;

    .q-item {
      padding: 20px;
    }

    .q-icon {
      color: #027BE3;
    }
  }

  .bottom-quick-btn {
    padding-left: 10px;
  }
</style>

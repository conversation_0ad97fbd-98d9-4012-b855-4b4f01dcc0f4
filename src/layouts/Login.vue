<template>
  <section class="bf-login row justify-center items-center">
    <q-img
      v-if="bgImage"
      :src="bgImage"
      spinner-color="white"
      class="bf-login-bg-image"
    />

    <q-form
      @submit="onSubmit"
      class="bf-login-form col-xs-10 col-sm-6 col-md-4 col-lg-3 q-pa-md"
      :autofocus="!!$q.platform.is.desktop"
    >
      <q-item class="text-center text-h6 text-white">
        <q-item-section v-text="$t('login.title')" />
      </q-item>

      <q-input
        outlined
        dense
        v-model="login.system"
        :label="$t('login.system')"
        lazy-rules
        :rules="rules.system"
        class="login-input"
        @change="clearSessionRID"
      >
        <template v-slot:prepend>
          <i class="iconfont bys-number"></i>
        </template>
      </q-input>
      <q-input
        outlined
        dense
        v-model="login.name"
        :label="$t('login.loginName')"
        lazy-rules
        :rules="rules.name"
        maxlength="16"
        class="login-input"
        @change="clearSessionRID"
      >
        <template v-slot:prepend>
          <i class="iconfont bys-login-user"></i>
        </template>
      </q-input>
      <q-input
        outlined
        dense
        type="password"
        v-model="login.password"
        :label="$t('login.password')"
        lazy-rules
        :rules="rules.password"
        maxlength="16"
        class="login-input"
        @change="clearSessionRID"
      >
        <template v-slot:prepend>
          <i class="iconfont bys-password"></i>
        </template>
      </q-input>

      <q-checkbox
        keep-color
        v-model="remember"
        :label="$t('login.remember')"
        color="primary"
        class="text-white"
      />

      <div class="row justify-center">
        <q-btn
          :label="$t('login.submit')"
          type="submit"
          color="primary"
          unelevated
          no-caps
          icon="login"
          :disabled="!isConnected || isLoggingIn"
          :loading="isLoggingIn"
          class="q-px-lg login-button"
        >
          <template v-slot:loading>
            <q-spinner-facebook />
          </template>
        </q-btn>
      </div>
    </q-form>
  </section>
</template>

<script lang="ts" setup>
  import { reactive, ref, onBeforeMount, computed } from 'vue'
  import storage, { StoreLangKey, StoreLoginInfo } from '@utils/storage'
  import log from '@utils/log'
  import { user as userApi } from '@ygen/user.api'
  import { utcTime } from '@utils/dayjs'
  import { encodeLoginPassword } from '@utils/crypto'
  import { requestLogin } from '@utils/login'
  import { StrPubSub } from 'ypubsub'
  import { LoginFailed, LoginSuccess } from '@utils/pubSubSubject'
  import { useRouter } from 'vue-router'
  import { useI18n } from 'vue-i18n'
  import { useStore } from 'vuex'
  import { useIsConnected } from '@utils/vueUse/commonComputed'
  import bgImage from '@assets/bg-login.jpg'

  const router = useRouter()
  const i18n = useI18n()
  const store = useStore()

  const login = reactive({
    system: '',
    name: '',
    password: '',
  })
  const remember = ref(true)
  const isLoggingIn = ref(false)
  const SessionRID = ref('')

  const rules = computed(() => {
    const requiredFn = val => !!val || i18n.t('rules.required')
    return {
      system: [
        requiredFn,
      ],
      name: [
        requiredFn,
      ],
      password: [
        requiredFn,
      ],
    }
  })
  const isConnected = useIsConnected()

  // clear session, use password login way
  function clearSessionRID() {
    SessionRID.value = ''
  }

  function awaitRequestLogin(): Promise<userApi.IResUserLogin> {
    // 登录方式 0：username+userpass 1: session rid
    // session rid为uuid的格式，32位字符和4位连接符
    const LoginType: number = SessionRID.value.length === 36 ? 1 : 0
    const LoginName: string = LoginType ? SessionRID.value : login.name.trim()
    const LoginTimeStr: string = utcTime()
    const reqUserLogin: userApi.IReqUserLogin = {
      LoginName,
      LoginPass: encodeLoginPassword(LoginName, login.password.trim(), LoginTimeStr),
      LoginTimeStr,
      LoginType,
      System: login.system.trim(),
    }
    return requestLogin(reqUserLogin)
  }
  async function onSubmit() {
    isLoggingIn.value = true
    const resLogin = await awaitRequestLogin()
    isLoggingIn.value = false
    // Code:100 为正常登录，其他为异常
    if (resLogin.Code === 100) {
      // 发布全局登录成功事件，并更新登录状态
      store.commit('saveSystem', { value: login.system })
      // 缓存账号
      if (remember.value) {
        storage.save(StoreLoginInfo, {
          ...login,
          password: ''.padStart(login.password.length, '*'),
          SessionRID: resLogin.SessionRID,
        })
      }
      // 缓存当前使用的语言
      storage.save(StoreLangKey, i18n.locale.value)
      StrPubSub.publish(LoginSuccess, resLogin)
      // 路由跳转
      router.replace({ path: '/' })
    } else {
      // 登录失败处理
      StrPubSub.publish(LoginFailed, resLogin)
    }
  }

  onBeforeMount(() => {
    try {
      const loginCache: any = storage.fetch(StoreLoginInfo)
      if (loginCache) {
        SessionRID.value = loginCache.SessionRID
        login.system = loginCache.system
        login.name = loginCache.name
        login.password = loginCache.password
      }
    } catch (e) {
      log.error('get login info cache error:', e)
    }
  })
</script>

<style lang="scss">
  @import "quasar/src/css/variables";

  .bf-login {
    width: 100vw;
    height: 100vh;
    position: fixed;
    z-index: 5000;
    background-image: linear-gradient(135deg, $blue-12, $blue-4);

    &-bg-image {
      position: absolute;
      width: 100%;
      height: 100%;
    }

    &-form {
      background: linear-gradient(rgba(0, 0, 0, .25), rgba(0, 0, 0, .05));
      border-radius: 6px;

      .login-input .q-field__control {
        background-color: rgba(255, 255, 255, .6);
      }

      .login-button {
        font-size: 16px;

        .q-icon {
          margin-right: 6px;
        }
      }
    }
  }
</style>

import { createI18n } from 'vue-i18n'
import { StrPubSub } from 'ypubsub'
import { appLang, webConfig } from '@src/config'
import { Quasar, QuasarLanguage } from 'quasar'
import { ToggleI18nLang } from '@utils/pubSubSubject'
import storage, { StoreLangKey } from '@utils/storage'

// export type MessageLanguages = keyof typeof zhCN
// Type-define 'en-US' as the master schema for the resource
// export type MessageSchema = typeof zhCN

// See https://vue-i18n.intlify.dev/guide/advanced/typescript.html#global-resource-schema-type-definition
/* eslint-disable @typescript-eslint/no-empty-interface */
declare module 'vue-i18n' {
  // define the locale messages schema
  // export interface DefineLocaleMessage extends MessageSchema { }
  export interface DefineLocaleMessage { }

  // define the datetime format schema
  export interface DefineDateTimeFormat { }

  // define the number format schema
  export interface DefineNumberFormat { }
}
const appLangList: string[] = [appLang.zhCN, appLang.enUs]

const i18n = createI18n({
  legacy: false,
  globalInjection: true,
  locale: appLang.zhCN,
  fallbackLocale: appLang.zhCN,
  messages: {
    // [appLang.zhCN]: zhCN,
  },
})

// 已经加载过的语言
const ResolveLanguage: Set<string> = new Set()

// 修改系统网站标题
function changeSiteTitle(): void {
  document.title = i18n.global.t('siteTitle') as string
}

// 请求对应的语言文件
async function getLanguage(lang: string): Promise<{ [key: string]: any }> {
  const timestamp = Math.floor(Date.now() / 1000 / 60)
  const res: any = await fetch(`/locales/${lang}.json?t=${timestamp}`)
  return (await res.json()) ?? {}
}

// import.meta.glob，生成异步导入的模块，只支持字符串配置
// 路径配置规则 https://github.com/mrmlnc/fast-glob#pattern-syntax
// quasar语言包只加载支持的中文、英文两种
const _quasarLocales = import.meta.glob('@app/node_modules/quasar/lang/(zh-CN|en-US).mjs')
const quasarLocales = Object.keys(_quasarLocales)
  .filter((key) => appLangList.some((l) => key.includes(l)))
  .map((key) => {
    let name = key.split('/').pop() ?? ''
    name = name.replace(/\.mjs$/g, '')

    return {
      [name]: _quasarLocales[key],
    }
  })
  .reduce((p, c) => Object.assign(p, c), {})


// 按需要加载quasar框架语言包
async function getQuasarLang(lang: string): Promise<QuasarLanguage> {
  const res: any = await quasarLocales[lang]?.()
  return res?.default ?? {}
}

// 监听语言变化，自动切换对应语言
StrPubSub.subscribe(ToggleI18nLang, async (locale: string) => {
  // quasar lang package must be loading when locale is changed
  const quasarLanguagePack = await getQuasarLang(locale)
  // @ts-ignore
  Quasar.lang.set(quasarLanguagePack)

  // i18n lang package do not loading again
  if (ResolveLanguage.has(locale)) {
    i18n.global.locale.value = locale
    changeSiteTitle()
    return
  }

  // 没有加载过语言包，需要加载到i18n中
  const selfLanguagePack = await getLanguage(locale)
  // @ts-ignore
  i18n.global.setLocaleMessage(locale, selfLanguagePack)
  // @ts-ignore
  i18n.global.locale.value = locale
  ResolveLanguage.add(locale)
  changeSiteTitle()
})

// 初始化时，请求默认语言的语言包
function initLocale() {
  // 读取LocalStorage缓存的语言，如果没有则使用配置语言，最后才根据浏览器语言显示
  let locale = String(storage.fetch(StoreLangKey) || webConfig.language || window.navigator.language)
  locale = appLangList.includes(locale) ? locale :
    locale.startsWith('zh') ? appLang.zhCN : appLang.enUs
  StrPubSub.publish(ToggleI18nLang, locale)
}

export default ({ app }) => {
  initLocale()
  // Set i18n instance on app
  app.use(i18n)
}

export { i18n }

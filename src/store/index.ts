import { createStore } from 'vuex'

import state from './state'
import * as getters from './getters'
import * as mutations from './mutations'
import * as actions from './actions'

import data from './data'

export const Store = createStore({
  modules: {
    data,
  },

  state,
  getters,
  mutations,
  actions,

  // enable strict mode (adds overhead!)
  // for dev mode and --debug builds only
  strict: Boolean(process.env.DEBUGGING),
})

export default function(/* { ssrContext } */) {
  return Store
}

export * from './data'

export type TOneData = { [key: string]: any }

// 每个数据都有数据对象，索引对象，Select表单下拉菜单对象
export interface IData {
  data: { [key: string]: TOneData }
  index: { [key: string]: string }
}

export enum DataName {
  Unit = 'Unit',
  User = 'User',
  Config = 'Config',
  Image = 'Image',
  UserOrgPrivilege = 'UserOrgPrivilege',
  Permission = 'Permission',
  Role = 'Role',
  RolePermission = 'RolePermission',
  UserRole = 'UserRole',
  UserSession = 'UserSession',
  Controller = 'Controller',
  BysMarker = 'BysMarker',
  MediaInfo = 'MediaInfo',
  NFCPatrolLine = 'NFCPatrolLine',
  NFCPatrolLineDetail = 'NFCPatrolLineDetail',
  NFCPatrolLineAndRules = 'NFCPatrolLineAndRules',
  NFCPatrolLineRules = 'NFCPatrolLineRules',
}

// 创建各类数据管理容器
export function createDataState(): { [key: string]: IData } {
  const State: { [key: string]: IData } = {}
  const AllDataName = Object.entries(DataName)
  for (let i = 0; i < AllDataName.length; i++) {
    // [key,value]
    const [, v] = AllDataName[i]
    State[v] = {
      data: {},
      index: {},
    }
  }

  return State
}

export interface IDataState {
  [key: string]: any
}

export const DataState: IDataState = {
  ...createDataState(),
}

export default DataState

import * as types from './methodTypes'
import { createDataState } from './state'

export default {
  // set one data to Map
  [types.SET_DATA](state, { type, key, value }) {
    state[type].data[key] = value
  },
  [types.SET_PART_DATA](state, { type, key, value }) {
    Object.assign(state[type].data[key], value)
  },
  // delete one data from Map
  [types.DEL_DATA](state, { type, key }) {
    // delete data
    delete state[type].data[key]
    state[type].data = { ...state[type].data }
    // delete index,find the index value equal key
    const keyName: string[] = Object.keys(state[type].index)
      .filter(k => state[type].index[k] === key)
    keyName.forEach((key) => {
      delete state[type].index[key]
    })
    state[type].index = state[type].index
  },

  // set data index
  [types.SET_INDEX](state, { type, key, value }) {
    state[type].index[key] = value
  },

  // clean all data
  [types.CLEAN](state) {
    Object.assign(state, createDataState())
  },
}

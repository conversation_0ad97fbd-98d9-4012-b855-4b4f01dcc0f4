import * as types from './methodTypes'
import { DataName } from './state'

export default {
  // get one data or all
  [types.GET_DATA](state): any {
    return (type: string, key?: string) => {
      const data = state[type].data
      return key !== undefined? data[key]: Object.keys(data).map(key => data[key])
    }
  },

  // get data index
  [types.GET_INDEX](state): any {
    return (type: string, key: string) => {
      return state[type].index[key]
    }
  },

  // get one data by index
  [types.GET_DATA_BY_INDEX](state): any {
    return (type: string, key: string) => {
      return state[type].data[state[type].index[key]]
    }
  },

  // get data select option
  [types.GET_PARENT](state): any {
    return (key: string) => {
      return state[DataName.Unit].data[key]
    }
  },
}


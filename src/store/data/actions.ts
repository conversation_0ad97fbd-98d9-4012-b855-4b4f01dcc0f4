import * as types from './methodTypes'
import { DataName } from './state'
// import { StrPubSub } from 'ypubsub'

// [[key,value]...]
const AllDataName = Object.entries(DataName)

export default {
  // delete one data from Map
  deleteUnitData({ state, commit, dispatch }, { type, key }) {
    // delete Unit data
    commit(types.DEL_DATA, { type, key })

    // delete other data
    for (let i = 0; i < AllDataName.length; i++) {
      const [, name] = AllDataName[i]
      const _data = state[name].data
      const isUnit = name === DataName.Unit
      const prop = 'OrgRID'
      const dataList: Array<{ [key: string]: any }> = Object.keys(_data).map(key => _data[key])
        .filter((item: any) => item[prop] === key)

      for (let i = 0; i < dataList.length; i++) {
        const data = dataList[i]
        const payload = { type: name, key: data.RID }
        if (isUnit) {
          dispatch(types.DEL_DATA, payload)
        } else {
          commit(types.DEL_DATA, payload)
        }
      }
    }

    // 界桩数据管理被移出vuex管理，需要额外处理
    // StrPubSub.publish('deleteBysMarkerByDelUnit', key)
  },
}

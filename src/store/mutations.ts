export function updateLoginStatus(state, { value }) {
  state.isLogin = value
}

export function updateServerStatus(state, { status }) {
  state.isConnected = status
}

export function updateSessionRID(state, { value }) {
  state.SessionRID = value
}

export function saveUserOrgRID(state, { value }) {
  state.UserOrgRID = value
}

export function saveUserRID(state, { value }) {
  state.UserRID = value
}

export function saveSystem(state, { value }) {
  state.System = value
}

export function UserName(state, { value }) {
  state.UserName = value
}

export function isScrollTitle(state, { value }) {
  state.isScrollTitle = value
}

export function scrollTitle(state, { value }) {
  state.scrollTitle = value
}

export function hasAlarmTips(state, { value }) {
  state.hasAlarmTips = value
}

export function syncSettings(state, { value }) {
  state.Settings = { ...state.Settings, ...value }
}

// 缓存服务器时间
export function syncSysUTCTimeDiff(state, { value }) {
  let isoTime = new Date().toISOString()
  isoTime = isoTime.slice(0, 10) + ' ' + isoTime.slice(11, 19)
  state.SysUTCTimeDiff = new Date(value).getTime() - new Date(isoTime).getTime()
}

export function updateTopParentOrg(state, { value }) {
  state.topParentOrg = value
}

export function updateAppLogo(state, { value }) {
  state.systemSettings.appLogo = value
}

export function updateSystemTitle(state, { value }) {
  state.systemSettings.systemTitle = value
}

import { org } from '@ygen/org'
import { config } from '@ygen/config'

export interface UserSettings {
  [key: string]: any

  // true为降序
  descending?: boolean
  // true是控制器在前，界桩在后，false则相反
  controllerFirst?: boolean
  closePopupWind: boolean
  closeAlarmAudio: boolean
  showNotInstalledMarker: boolean
  // 新增：界桩巡查提醒
  patrolReminder?: boolean
}

const DefaultSettings: UserSettings = {
  descending: false,
  controllerFirst: false,
  closePopupWind: true,
  closeAlarmAudio: true,
  showNotInstalledMarker: false,
  // 新增：界桩巡查提醒，默认关闭
  patrolReminder: false,
}

export interface SystemSettings {
  appLogo?: config.IDbConfig
  systemTitle?: config.IDbConfig
}

export interface State {
  isLogin: boolean,
  isConnected: boolean,
  SessionRID: string,
  UserOrgRID: string,
  UserRID: string,
  System: string,
  UserName: string,
  isScrollTitle: boolean,
  scrollTitle: string,
  hasAlarmTips: boolean,
  Settings: UserSettings,
  SysUTCTimeDiff: number,
  topParentOrg: org.IDbOrg
  systemSettings: SystemSettings
}

export const DefaultOrg = new org.DbOrg({
  RID: '00000000-0000-0000-0000-000000000000',
  OrgRID: '00000000-0000-0000-0000-000000000000',
  OrgType: 0,
  SortValue: 100,
  ShortName: 'root',
  FullName: 'root',
  Setting: '{}',
})

export default function(): State {
  return {
    isLogin: false,
    isConnected: false,
    SessionRID: '',
    UserOrgRID: '',
    UserRID: '',
    System: '',
    UserName: '',
    isScrollTitle: true,
    scrollTitle: '',
    hasAlarmTips: false,
    Settings: DefaultSettings,
    SysUTCTimeDiff: 0,
    topParentOrg: DefaultOrg,
    systemSettings: {},
  }
}

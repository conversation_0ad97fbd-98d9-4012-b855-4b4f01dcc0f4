<template>
  <router-view></router-view>
</template>

<script lang="ts" setup>
  import { onBeforeMount, onMounted, onBeforeUnmount, watch } from 'vue'
  import { isApp } from '@src/config'
  import { useI18n } from 'vue-i18n'
  import { StrPubSub } from 'ypubsub'
  import { FullscreenIsActive } from '@utils/pubSubSubject'
  import { useQuasar, Dialog } from 'quasar'
  import { requirePerms } from '@utils/appPermissions'
  import { deferred } from '@utils/common'
  import { appName } from '@app/package.json'
  import { resolveServerPath } from '@utils/path'
  import log from '@utils/log'
  import semver from 'semver'

  const $i18n = useI18n()
  const $q = useQuasar()

  watch(() => $q.fullscreen.isActive, (isActive: boolean) => {
    StrPubSub.publish(FullscreenIsActive, isActive)
  })

  // 封装双击返回键，退出app
  function useDeviceready() {
    if (!isApp) {
      return
    }

    // 组件挂载完成Promise
    const mountedDeferred = deferred<boolean>()
    onMounted(() => {
      mountedDeferred.resolve(true)
    })

    // 封装检查升级app
    async function upgradeApp() {
      // 检查APK文件是否已存在并验证文件年龄
      async function checkApkExists(version: string): Promise<string | null> {
        const fileURL = `${cordova.file.externalApplicationStorageDirectory}cache/${appName}-${version}.apk`
        return new Promise((resolve) => {
          window.resolveLocalFileSystemURL(
            fileURL,
            (entry) => {
              if (entry.isFile) {
                // 获取文件修改时间
                entry.getMetadata((metadata) => {
                  const fileAge = Math.floor((Date.now() - metadata.modificationTime.getTime()) / (1000 * 60 * 60 * 24))
                  
                  // 如果文件超过7天，删除并重新下载
                  if (fileAge > 7) {
                    entry.remove(() => {
                      resolve(null) // 文件已删除，需要重新下载
                    }, () => {
                      log.error('Failed to remove old APK file')
                      resolve(null) // 删除失败，仍然需要重新下载
                    })
                  } else {
                    // 文件未过期，可以使用
                    resolve(fileURL)
                  }
                }, (error) => {
                  log.error('Failed to get file metadata:', error)
                  resolve(null) // 获取文件信息失败，需要重新下载
                })
              } else {
                resolve(null)
              }
            },
            () => resolve(null)
          )
        })
      }

      // 后台下载更新文件
      async function downloadInBackground(version: string): Promise<string> {
        const url = resolveServerPath(`/app?filename=${appName}-${version}.apk`)
        const fileURL = `${cordova.file.externalApplicationStorageDirectory}cache/${appName}-${version}.apk`
        const uri = encodeURI(url)

        // // 消息栏通知
        // scheduleLocalNotify({
        //   title: $i18n.t('upgradeApp.download') as string,
        //   text: `${appName}-${version}.apk`,
        // })
        // // 震动提示
        // navigator.vibrate([600, 10, 400])

        const fileTransfer = new FileTransfer()
        return new Promise((resolve, reject) => {
          fileTransfer.download(
            uri,
            fileURL,
            function() {
              resolve(fileURL)
            },
            function(error) {
              log.error('app upgrade download error ', JSON.stringify(error))
              Dialog.create({
                title: $i18n.t('upgradeApp.upgradeFailed') as string,
                message: $i18n.t('upgradeApp.errorMessage', { error: JSON.stringify(error) }) as string,
                class: 'file-path-alarm z-top',
              })
              reject(error)
            },
            false,
            {},
          )
        })
      }

      // 安装更新
      function installApp(fileURL: string) {
        cordova.plugins.fileOpener2.open(
          fileURL,
          'application/vnd.android.package-archive',
          {
            error: (error) => {
              log.error('useUpgradeApp fileOpener2 error', JSON.stringify(error))
              Dialog.create({
                title: $i18n.t('upgradeApp.upgradeFailed') as string,
                message: $i18n.t('upgradeApp.errorMessage', { error: JSON.stringify(error) }) as string,
                class: 'file-path-alarm z-top',
              })
            },
            success: () => {
              // 安装成功，无需返回值
            }
          }
        )
      }

      // 检查更新
      async function checkUpdate() {
        const url = resolveServerPath('/app?filename=versionnumber.txt')
        const response = await fetch(url, {
          headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, PATCH, OPTIONS',
            'Access-Control-Allow-Headers': 'Accept, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization',
          },
        })

        const data = await response.text()
        return data.trim()
      }

      async function run() {
        const version = await checkUpdate()
        if (semver.lte(version, CLIENT_VERSION)) return

        try {
          // 先检查是否已下载
          const existingFileURL = await checkApkExists(version)
          const fileURL = existingFileURL || await downloadInBackground(version)
          Dialog.create({
            message: $i18n.t('upgradeApp.isInstallNewApp', { newVersion: version }) as string,
            class: 'file-path-alarm z-top',
            ok: $i18n.t('upgradeApp.install') as string,
            cancel: $i18n.t('common.cancel') as string,
            persistent: true,
          })
            .onOk(() => {
              installApp(fileURL)
            })
            .onCancel(() => {
              // 用户取消安装，无需处理
            })
            .onDismiss(() => {
              // 对话框关闭时的处理
            })
        } catch (error) {
          log.error('upgrade error', error)
        }
      }

      return run()
    }

    let backButtonLastTime = 0

    // 模拟浏览器历史记录
    function mockHistory() {
      while (window.history.length < 6) {
        window.history.pushState(null, '', '/')
      }
    }

    function exitApp() {
      // 每次取消退出App，重新向history添加记录
      mockHistory()
      // 两次点击返回键时间小于2s，则退出app
      if (Date.now() - backButtonLastTime <= 2000) {
        // 添加到后台运行
        cordova.plugins?.backgroundMode?.moveToBackground()
        navigator.app?.exitApp()
        return
      }

      backButtonLastTime = Date.now()
      $q.notify({
        message: $i18n.t('common.clickAgainExitApp') as string,
        position: 'bottom',
        type: 'info',
        timeout: 2100,
      })
    }

    function backbuttonFn(ev: any) {
      ev.preventDefault()
      ev.stopPropagation()
      exitApp()
    }

    async function deviceready() {
      // 向history添加模拟记录，防止点击返回键浏览器后退，导致路由重载
      mockHistory()
      document.addEventListener('backbutton', backbuttonFn, false)

      await requirePerms()
      await upgradeApp()
    }

    onBeforeMount(() => {
      document.addEventListener('deviceready', deviceready, false)
    })

    onBeforeUnmount(() => {
      document.removeEventListener('backbutton', backbuttonFn)
      document.removeEventListener('deviceready', deviceready)
    })
  }

  useDeviceready()
</script>

<style lang="scss">
  #q-app {
    width: 100vw;
    height: 100vh;
    overflow: hidden;
  }

  .q-dialog.app-exit-dialog {
    font-size: 18px;
    text-align: center;

    .q-card .q-card__actions {
      justify-content: center;
    }
  }

  .file-path-alarm .q-dialog__message {
    word-break: break-word;
  }
</style>

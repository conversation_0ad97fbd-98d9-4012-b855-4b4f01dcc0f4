declare namespace NodeJS {
  interface ProcessEnv {
    NODE_ENV: string;
    VUE_ROUTER_MODE: 'hash' | 'history' | 'abstract' | undefined;
    VUE_ROUTER_BASE: string | undefined;

    // 自定义环境参数
    protocol: string
    hostname: string
    port: string
  }
}

declare const CLIENT_VERSION: string
declare const CLIENT_BUILD_TIME: string
declare const appAvailability: any
declare const startApp: any
declare const resolveLocalFileSystemURL: any
declare const requestFileSystem: any
declare const LocalFileSystem: any
declare const Camera: any
declare const FileTransfer: any
declare const fileOpener2: any
declare const nfc: any
declare const ndef: any

interface Cordova {
  file?: any
}

interface Navigator {
  app?: any
  camera?: any
}

interface CordovaPlugins {
  notification?: any
  backgroundMode?: any
  permissions?: any
  fileOpener2?: any
  SitewaertsDocumentViewer?: any
}

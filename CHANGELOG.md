## [2.2.18](https://git.kicad99.com/gzdev/bys/web/compare/v2.2.17...v2.2.18) (2025-06-23)


### Bug Fixes

* 历史查询页面，空数据时禁用导出等按钮 ([a300235](https://git.kicad99.com/gzdev/bys/web/commit/a300235e5c4a77dc9062fefafe13977cb367dd6f))
* 界桩地图Popup更多下拉菜单，添加对应菜单的权限判断 ([978c8e9](https://git.kicad99.com/gzdev/bys/web/commit/978c8e9f7b22f4e42643cf79467d043e6482df2c))

## [2.2.17](https://git.kicad99.com/gzdev/bys/web/compare/v2.2.16...v2.2.17) (2025-06-20)


### Bug Fixes

* queryDataRet方法添加"$index"自定义属性 ([dbdd639](https://git.kicad99.com/gzdev/bys/web/commit/dbdd6395017b665008e0670b434f83488f44d596))
* 修改历史查询记录中，索引值显示错误 ([3faeaba](https://git.kicad99.com/gzdev/bys/web/commit/3faeaba9d165efb80aabd3cc7f00996382c61b00))
* 图像上传历史的图像预览显示全部历史的图片，以懒加载的方式进行处理 ([a2f3ee6](https://git.kicad99.com/gzdev/bys/web/commit/a2f3ee699c611a6c43a7a3c1c3581bd13afc16b6))
* 将历史表格的排序事件向父组件传递，以便原始数据排序后，lightbox的imgs顺序是一致的 ([0e05d80](https://git.kicad99.com/gzdev/bys/web/commit/0e05d80622f271db93623c17c916c3b142eacbf6))

## [2.2.16](https://git.kicad99.com/gzdev/bys/web/compare/v2.2.15...v2.2.16) (2025-06-19)


### Bug Fixes

* 修改app升级流程，改为先后台自动下载，然后再提示安装 ([5c3d69b](https://git.kicad99.com/gzdev/bys/web/commit/5c3d69b7b258525a729b98092f853ae9fce4751f))
* 先等待app权限申请完成，再执行后续升级操作 ([f3befd2](https://git.kicad99.com/gzdev/bys/web/commit/f3befd28844b11cc371b976551452ade5add9af6))
* 安装semver库，进行版本比较 ([50c2e77](https://git.kicad99.com/gzdev/bys/web/commit/50c2e779b13867117e67dcca6b4c7080f34ee9da))
* 更新cordova目录下的package-lock.json文件 ([31af317](https://git.kicad99.com/gzdev/bys/web/commit/31af317f27a1664f9d8bbbac79d1a01ea49177a2))

## [2.2.15](https://git.kicad99.com/gzdev/bys/web/compare/v2.2.14...v2.2.15) (2025-06-13)


### Bug Fixes

* 修复下发遥晕、遥活指令后更新界桩数据没有拷贝附加属性的错误 ([7790987](https://git.kicad99.com/gzdev/bys/web/commit/77909871c25630ccdb6ad87ef153d1a16f3be583))
* 界桩Popup的编辑按钮，文本修改为"数据编辑" ([422495e](https://git.kicad99.com/gzdev/bys/web/commit/422495e6ff5721f447969c08fb01495a5f29178a))
* 界桩地图Popup快速查找界桩历史时，要执行一次markerChange来赋值searchMarkerType ([0cb0dd0](https://git.kicad99.com/gzdev/bys/web/commit/0cb0dd0707f9db751cff584b603688e49bfa147c))

## [2.2.14](https://git.kicad99.com/gzdev/bys/web/compare/v2.2.13...v2.2.14) (2025-06-13)


### Bug Fixes

* lint ([c365ddc](https://git.kicad99.com/gzdev/bys/web/commit/c365ddca3d1650f8832a6e3959ad2b166b761ef0))
* 在leficlick页面添加一个更多的下拉按钮，下拉显示跳转界桩历史和NFC巡查历史快捷按钮 ([02936ad](https://git.kicad99.com/gzdev/bys/web/commit/02936ada9d300acad9ac27ccbe05d0fff41bd71e))
* 在解除遥晕时添加二次确认对话框以防止误操作 ([81ea62d](https://git.kicad99.com/gzdev/bys/web/commit/81ea62d9d7e3e9e2b3b5f425b00a36e0c00ef8a1))
* 异步函数添加.catch处理 ([9741998](https://git.kicad99.com/gzdev/bys/web/commit/97419980e780d62aa7d30544160b856110f492b0))
* 指令日志-"拍照上传"删除前面的冒号 ([d847a71](https://git.kicad99.com/gzdev/bys/web/commit/d847a718bcf5f3ba19f17166bea9271549cd10c5))
* 添加重新登录数据查询开始事件，重置查询状态并优化数据订阅逻辑 ([84d7618](https://git.kicad99.com/gzdev/bys/web/commit/84d761846a433515c4d24b96e53805d8012edaa5))
* 界桩现在通过二次确认后直接下发遥毙遥晕命令，不需要检测 ([c7c0acc](https://git.kicad99.com/gzdev/bys/web/commit/c7c0acc49de3a26342faa4fe70193a55688fded9))
* 解除报警后需要将captureImageInfo清空 ([1ce15be](https://git.kicad99.com/gzdev/bys/web/commit/1ce15bef3cddc58f9746778c936d54f490b29214))
* 调整自定义地图弹窗的最小宽度，从260px修改为300px ([f39cf43](https://git.kicad99.com/gzdev/bys/web/commit/f39cf433625adecd99266ff95e6d16543cb7cb66))

## [2.2.13](https://git.kicad99.com/gzdev/bys/web/compare/v2.2.12...v2.2.13) (2025-06-12)


### Bug Fixes

* lint ([b23071b](https://git.kicad99.com/gzdev/bys/web/commit/b23071b400e67e8cb1429c8f5a22657f66b45590))
* 优化设置时间逻辑，仅在遥晕遥毙下更新CameraDisabledTime和MarkerDisabledTime ([a9e7442](https://git.kicad99.com/gzdev/bys/web/commit/a9e744202f62f0d982c55437e50baa5610752a7a))
* 删除未使用的导入 ([7d6e1c0](https://git.kicad99.com/gzdev/bys/web/commit/7d6e1c070702d4d3b4d3973d86e54d198641e717))
* 在表单同步数据时深拷贝对象以避免引用问题 ([5aec505](https://git.kicad99.com/gzdev/bys/web/commit/5aec5059ad01a53b157f06d65a8201699d878ec6))
* 在解除遥晕时同步标记数据并更新设置 ([8f24c40](https://git.kicad99.com/gzdev/bys/web/commit/8f24c405db0acfb9b26256286e6fd84aabe83823))
* 添加对Marker表单的实时同步功能，通过发布/订阅模式更新界桩遥晕遥毙数据 ([d379a6d](https://git.kicad99.com/gzdev/bys/web/commit/d379a6d02b1d94a0e9b241c0c479145fb300147e))
* 组件销毁时需要取消订阅事件 ([18fc2a8](https://git.kicad99.com/gzdev/bys/web/commit/18fc2a8aa1125b1595ff869e42fc49098cc0282c))

## [2.2.12](https://git.kicad99.com/gzdev/bys/web/compare/v2.2.11...v2.2.12) (2025-06-06)


### Bug Fixes

* 4g界桩报警如果是红外报警添加“红外报警阈值： XX” ([8bb249b](https://git.kicad99.com/gzdev/bys/web/commit/8bb249beb158ff4e5049ab5303e868c352906d61))
* 4g界桩遥晕修改为界桩遥晕 ([fbd3105](https://git.kicad99.com/gzdev/bys/web/commit/fbd31053f97905e427e874fc14fa57141005c058))
* build proto ([2fc16f6](https://git.kicad99.com/gzdev/bys/web/commit/2fc16f607100a7c970c2d2c38ff5d052c931e85d))
* build proto ([7d3580b](https://git.kicad99.com/gzdev/bys/web/commit/7d3580b5a221de815447161297cc08f36151a6bd))
* fix unused ([db1954f](https://git.kicad99.com/gzdev/bys/web/commit/db1954fa80f65e3f309b412f4758305906189795))
* infrared不显示format ([4ce5964](https://git.kicad99.com/gzdev/bys/web/commit/4ce5964bb2090c747b1513b70cd6607723ff08e1))
* lint ([e9e3580](https://git.kicad99.com/gzdev/bys/web/commit/e9e35801d7d3365a624cce490c27e40b3bb9f0b7))
* lint ([c79d8a4](https://git.kicad99.com/gzdev/bys/web/commit/c79d8a4c9b57d53894aa6dec577236ae096b9e1b))
* MarkerType为Net4GPro也是4G界桩 ([1e3afb2](https://git.kicad99.com/gzdev/bys/web/commit/1e3afb2133f6bd1c5412a05522bf34a1da59b0b0))
* 修复setting字段写成了settings字段 ([5f45018](https://git.kicad99.com/gzdev/bys/web/commit/5f45018330273ed169d31cb7b5bebc50b9bed533))
* 修改获取版本信息的函数 ([6c2a950](https://git.kicad99.com/gzdev/bys/web/commit/6c2a950072be0af46d894d32bccaca49c0a9fc52))
* 删除没有使用的变量 ([2fed75c](https://git.kicad99.com/gzdev/bys/web/commit/2fed75c292985c9e67a682067e5f09c3587ca96d))
* 同步数据库数据到本地界桩数据并更新setting 时间 ([3b6ad6c](https://git.kicad99.com/gzdev/bys/web/commit/3b6ad6ce75cb8b432128e4a0156a5c6c89f0fffe))
* 在界桩表单添加摄像机遥毙和遥晕功能 ([b1cb110](https://git.kicad99.com/gzdev/bys/web/commit/b1cb1106d463b163a5657f2a4bbb1201d84ab1b1))
* 在设备树中显示摄像机遥晕遥活 ([45d2e75](https://git.kicad99.com/gzdev/bys/web/commit/45d2e75ef108d5ac2210e284b0af30f992e44fe3))
* 将项目中使用使用MarkerType.Net4G的都替换为使用函数去判断 ([4fda786](https://git.kicad99.com/gzdev/bys/web/commit/4fda7867354a4befa51e4f1277d448e5f037fecb))
* 异常界桩label要返回一个默认的“” ([93564a1](https://git.kicad99.com/gzdev/bys/web/commit/93564a150f631cb6c5f7fc9cf5547369baafb74b))
* 指令日志添加界桩的类型 ([21acb65](https://git.kicad99.com/gzdev/bys/web/commit/21acb65bf282bccbcd9d8d46e1d5466f423bd13f))
* 摄像机遥晕也是异常界桩，也可以解除遥晕 ([1c9af0a](https://git.kicad99.com/gzdev/bys/web/commit/1c9af0acd5789409d8cf1dccf5da6b1ff86f9b20))
* 更新界桩setting 时间的时候传入时间 ([32f865b](https://git.kicad99.com/gzdev/bys/web/commit/32f865ba906530a7326585148a8827faf714243b))
* 格式化语言包 ([96b3f91](https://git.kicad99.com/gzdev/bys/web/commit/96b3f91369382bc2898e70352ea5eee9f4151c15))
* 添加4G界桩Pro类型 ([e665e42](https://git.kicad99.com/gzdev/bys/web/commit/e665e42fd44674461bee94b790afba57d2fed1bd))
* 添加红外报警阈值并限制范围为0-10，0为关闭 ([6d78b1a](https://git.kicad99.com/gzdev/bys/web/commit/6d78b1aaa5d183c606cd777f474868c960779024))
* 添加缺失的英文翻译 ([d14720e](https://git.kicad99.com/gzdev/bys/web/commit/d14720e64a3121b47cbcb5b6f777b91fa8ad1f1d))
* 红外报警阈值0显示为关闭 ([5f0761a](https://git.kicad99.com/gzdev/bys/web/commit/5f0761a0b49215beaedb3ae197f568e7b9b58f03))
* 红外报警阈值范围1-10,再往下调就是关闭 ([7fb9b66](https://git.kicad99.com/gzdev/bys/web/commit/7fb9b66ceb7e190cdb5400c5bdf927a21c26492d))
* 表单底部checkbox添加gap ([3c82f53](https://git.kicad99.com/gzdev/bys/web/commit/3c82f53caddf91a98128c7e02fb75c71f8de57c8))
* 调整指令日志的颜色 ([31820f7](https://git.kicad99.com/gzdev/bys/web/commit/31820f78bb1011546cbb8a7268bf5b7e0ad653d4))
* 调整界桩表单底部checkbox布局 ([0dd0b04](https://git.kicad99.com/gzdev/bys/web/commit/0dd0b04b41de03f8c366729add13d10a583656df))
* 调整表单checkbox为三列 ([35f7d7c](https://git.kicad99.com/gzdev/bys/web/commit/35f7d7c6361d003b3b82d7f8b3d2ac5377b1d044))
* 调整表单底部checkbox布局 ([66be81c](https://git.kicad99.com/gzdev/bys/web/commit/66be81cb9ce3f46b5755ceb1f490a491bf0820a2))

## [2.2.11](https://git.kicad99.com/gzdev/bys/web/compare/v2.2.10...v2.2.11) (2025-05-22)


### Bug Fixes

* 优化判断界桩参数变更逻辑 ([9fa6adb](https://git.kicad99.com/gzdev/bys/web/commit/9fa6adbfb39f9b74718428d81bd343f33f860a20))

## [2.2.10](https://git.kicad99.com/gzdev/bys/web/compare/v2.2.9...v2.2.10) (2025-05-22)


### Bug Fixes

* 修复4g界桩唤醒时间和唤醒间隔参数设置错误，修改为使用同一个参数属性，在最后确定更新时进行同步 ([833581c](https://git.kicad99.com/gzdev/bys/web/commit/833581ceeddc7c563c79f62956017949b4c12297))
* 修正判断4g界桩参数变更判断，对象类型参数需要递归处理 ([88c4065](https://git.kicad99.com/gzdev/bys/web/commit/88c4065fd0e95f4cd7844985a8671e86fb7739c4))

## [2.2.9](https://git.kicad99.com/gzdev/bys/web/compare/v2.2.8...v2.2.9) (2025-05-21)


### Bug Fixes

* 修复唤醒间隔参数在各类型下同步的异常 ([89a617b](https://git.kicad99.com/gzdev/bys/web/commit/89a617b106f587e56161ff9767eded824ddcc810))
* 修复界桩历史报警轨迹过滤条件不正确导致只有一个轨迹点的问题 ([64eaa5e](https://git.kicad99.com/gzdev/bys/web/commit/64eaa5e7d44a3a457be351afae1ccf6f81a91e3e))

## [2.2.8](https://git.kicad99.com/gzdev/bys/web/compare/v2.2.7...v2.2.8) (2025-05-21)


### Bug Fixes

* 修复界桩参数变更时，因为监听器重置了参数更新时间，导致界桩参数同步判断不正确错误 ([d4d09f9](https://git.kicad99.com/gzdev/bys/web/commit/d4d09f9334053c71cf5e56485c3140e6d491d6a2))

## [2.2.7](https://git.kicad99.com/gzdev/bys/web/compare/v2.2.6...v2.2.7) (2025-05-19)


### Bug Fixes

* 删除图像上传历史AI生成的列配置 ([0790c53](https://git.kicad99.com/gzdev/bys/web/commit/0790c53146df811c678bddca5c56af30e8cba7be))

## [2.2.6](https://git.kicad99.com/gzdev/bys/web/compare/v2.2.5...v2.2.6) (2025-05-19)


### Bug Fixes

* 修正界桩图像上传历史时间显示不正确问题 ([b1f945d](https://git.kicad99.com/gzdev/bys/web/commit/b1f945d83c723d047ae6693c3e29aa1051802418))

## [2.2.5](https://git.kicad99.com/gzdev/bys/web/compare/v2.2.4...v2.2.5) (2025-04-27)


### Bug Fixes

* 修改生成app图标和启动背景图标命令，使用profile files来更灵活配置 ([1c1e24f](https://git.kicad99.com/gzdev/bys/web/commit/1c1e24fdb96051a3fdfbb52909ae9a5ee118b822))
* 修正部署4g客户端ci依赖关系 ([106d30b](https://git.kicad99.com/gzdev/bys/web/commit/106d30b3759a973f5bec9ddb3068e0f2b62c243a))
* 生成新的app图标 ([d75ddda](https://git.kicad99.com/gzdev/bys/web/commit/d75ddda66df0e9f52c36fd9437ced567b5898706))

## [2.2.4](https://git.kicad99.com/gzdev/bys/web/compare/v2.2.3...v2.2.4) (2025-04-25)


### Bug Fixes

* 修正4g界桩客户端和app自动部署ci ([acb670d](https://git.kicad99.com/gzdev/bys/web/commit/acb670dca153b9ddff05f7c83de8fa0e2b2da455))

## [2.2.3](https://git.kicad99.com/gzdev/bys/web/compare/v2.2.2...v2.2.3) (2025-04-25)


### Bug Fixes

* 修正切换语言调用的bottomSheet用法错误，quasar版本升级后用法变更 ([3fd8fd4](https://git.kicad99.com/gzdev/bys/web/commit/3fd8fd44e434acde964f88cb28132db3886cb220))

## [2.2.2](https://git.kicad99.com/gzdev/bys/web/compare/v2.2.1...v2.2.2) (2025-04-25)


### Bug Fixes

* 优化图像上传历史下载图片缓存逻辑，限制缓存数量，避免内存使用过大 ([8b9bfda](https://git.kicad99.com/gzdev/bys/web/commit/8b9bfdab029919517e7b32f36b82039be68b5652))
* 优化图像上传历史照片显示大小 ([a52b59d](https://git.kicad99.com/gzdev/bys/web/commit/a52b59d3309c816d0bcc7ca89737b38f953e0f36))
* 优化界桩图像上传历史图片加载逻辑，使用懒加载方式避免一次性加载所有图片 ([da8e793](https://git.kicad99.com/gzdev/bys/web/commit/da8e7939baff9339c060bfd5c1c28461ca567fc0))
* 使用addTagDiscoveredListener发现非NDEF标签，和写入NDEF格式数据 ([9dccd63](https://git.kicad99.com/gzdev/bys/web/commit/9dccd63287c2d949ec56305a016a72bc652b6030))
* 使用community-cordova-plugin-nfc替换phonegap-nfc-api31 ([cd544fe](https://git.kicad99.com/gzdev/bys/web/commit/cd544febad7970f74ecf2fe02e8cff2bc80b700a))
* 修改清除日志方法，只保留最后500条日志，并检查和释放被清除日志的URL资源，避免内存泄漏 ([bc57cf2](https://git.kicad99.com/gzdev/bys/web/commit/bc57cf245bcb7a65176c5040657d5b7cff4c2d84))
* 取消移动端导航菜单默认展开菜单行为 ([482a63f](https://git.kicad99.com/gzdev/bys/web/commit/482a63fa0068f0c4d68890be9ab4dd7a960207b9))
* 报警时，界桩标记弹窗显示抓拍到的照片 ([ff7139f](https://git.kicad99.com/gzdev/bys/web/commit/ff7139fe94c3280df874136c02f0541a971b1cc7))
* 添加after_prepare.sh，修改gradle代理地址 ([edd62bf](https://git.kicad99.com/gzdev/bys/web/commit/edd62bf7862da3eadb07a674b1e02811c54be8d0))
* 添加报警类型红外报警的状态解析和日志 ([6262d21](https://git.kicad99.com/gzdev/bys/web/commit/6262d21dcaac45874e5795c8d373ecfe17af56b5))

## [2.2.1](https://git.kicad99.com/gzdev/bys/web/compare/v2.2.0...v2.2.1) (2025-01-14)


### Bug Fixes

* DeviceTree组件销毁时，Fancytree实例也要销毁；Devicetree挂载时， 判断是否存在异常界桩集合，存在就等待Fancytree初始化完成在更新节点状态 ([a715745](https://git.kicad99.com/gzdev/bys/web/commit/a715745c6db55af0f8da82f8d4a992ecb477256b))
* disabled时禁用事件执行 ([6b60b90](https://git.kicad99.com/gzdev/bys/web/commit/6b60b90dda786f4f1c5337b10a158424c289d0a6))
* v2.2.1 ([22b14ca](https://git.kicad99.com/gzdev/bys/web/commit/22b14cac44961b2956ece9e7cdb1104d73bb7bb4))
* 二次提示弹窗css统一处理 ([a136908](https://git.kicad99.com/gzdev/bys/web/commit/a1369089a2dab703931c4220c3a09ae754dc9a05))
* 图像上传历史显示为自定义的拍照类型；上传日志根据hwid没有找到界桩时，直接跳过该日志 ([e9f51f8](https://git.kicad99.com/gzdev/bys/web/commit/e9f51f80b904da48868b71ddc58a621b603b45b7))
* 在遥晕遥毙checkbox上添加一层遮罩层 ([324f093](https://git.kicad99.com/gzdev/bys/web/commit/324f093872ff7b89e1cdd58c0437c3952565ddf2))
* 拍照上传后, 控制台打印出相关数据 ([90cf230](https://git.kicad99.com/gzdev/bys/web/commit/90cf230642ffb46927095382f02cb6870415df93))
* 更新协议 ([416b960](https://git.kicad99.com/gzdev/bys/web/commit/416b9608fb5e99772d28d0055936f30eaf83c9d0))
* 解出自定义的上传图片的信息, 并在日志中展示 ([85a90a0](https://git.kicad99.com/gzdev/bys/web/commit/85a90a01f072b5269c37032c40d1aa369d4031c9))


### Reverts

* Revert "fix: v2.1.4" ([4dc117a](https://git.kicad99.com/gzdev/bys/web/commit/4dc117ac9a93b834925299587ee16b546da6788e))

## [2.2.0](https://git.kicad99.com/gzdev/bys/web/compare/v2.1.3...v2.2.0) (2025-01-07)


### Features

* 添加上传证书页面 ([804dd38](https://git.kicad99.com/gzdev/bys/web/commit/804dd380cafde6db78e6324d6fe48e24da973842))

## [2.1.3](https://git.kicad99.com/gzdev/bys/web/compare/v2.1.2...v2.1.3) (2024-12-16)


### Bug Fixes

* 4g界桩上线, 不需要同步iccid, 服务器端已经处理 ([ca98372](https://git.kicad99.com/gzdev/bys/web/commit/ca983721242df1813d4ea29d92be5e0e5d5fed49))
* 4g界桩定时上报时, 判断是否同步参数, 并在指令日志中展示 ([50c9c42](https://git.kicad99.com/gzdev/bys/web/commit/50c9c4289f6060281ce8ee6418f8ed246d430618))
* iccid和imei冲突弹窗按钮等宽 ([5421a35](https://git.kicad99.com/gzdev/bys/web/commit/5421a353ef907ac22219fdf508ea49b54e0bf663))
* log 修改 ([91044ad](https://git.kicad99.com/gzdev/bys/web/commit/91044ad3024a670e55600f7b1a6c7973cd48edaf))
* v2.1.3 ([0174938](https://git.kicad99.com/gzdev/bys/web/commit/0174938b31fd786a2478e31108337cb5a3305cf6))
* 上线界桩的iccid在系统内已存在其他界桩上时， 弹窗提示， 可以直接选在弹窗的更新按钮来更新两个界桩的iccid ([c3a77f7](https://git.kicad99.com/gzdev/bys/web/commit/c3a77f7b89aa2042726ac839e5fb4d7687f1bb69))
* 上线界桩的iccid或imei存在冲突的情况， 弹窗提示且在指令日志中展示 ([2a986b8](https://git.kicad99.com/gzdev/bys/web/commit/2a986b800ee7d3fffe744bada009824b44bf03da))
* 优化日志上下边距, 添加一个函数, 将对象属性名带有下划线的转化为小驼峰 ([ca641eb](https://git.kicad99.com/gzdev/bys/web/commit/ca641eb5aa6562d81d4efcb812e54024883c9d88))
* 修改iccid和imei冲突提示 ([aadac11](https://git.kicad99.com/gzdev/bys/web/commit/aadac11ec3354af973d5d5c2a19bd3842141c4d2))
* 删除测试的代码 ([658f99e](https://git.kicad99.com/gzdev/bys/web/commit/658f99ee3ee139fa289a00cd9d8eb8b97a9d8446))
* 删除预览teleport预览图片处多于的/>, 该问题导致打开含有预览图的teleport出现滚动条 ([02520e1](https://git.kicad99.com/gzdev/bys/web/commit/02520e166c6d7b88b5fb1f3c354649561bf64e5f))
* 勾选遥毙/遥晕后, 提示是否设置遥晕/遥毙状态?取消则恢复为原来的状态 ([397986e](https://git.kicad99.com/gzdev/bys/web/commit/397986e3f6c760f75b15e56e728ec56eb52e2d96))
* 取消和勾选遥晕遥毙都要二次确认后直接更新 ([4c4303d](https://git.kicad99.com/gzdev/bys/web/commit/4c4303dd7c20b057ff560e5c4abed5f4aeaad55a))
* 国际化翻译调整 ([d47cd1f](https://git.kicad99.com/gzdev/bys/web/commit/d47cd1f91360c6b70956aa50694db625e7ffe518))
* 处理bysImage组件, 在指令日志中展示上传的图片 ([c111cb4](https://git.kicad99.com/gzdev/bys/web/commit/c111cb47efc1fd3489c507a66e73def6a3ee8982))
* 封装有关界桩遥毙遥晕的相关方法 ([ff638df](https://git.kicad99.com/gzdev/bys/web/commit/ff638df276c128fb78265a07a3ffaef952c9ddc2))
* 拍照类型翻译 ([8cfbf75](https://git.kicad99.com/gzdev/bys/web/commit/8cfbf75a137b92b65cfcf84a46cd57efb9c50284))
* 指令日志使用q-img ([9a5c934](https://git.kicad99.com/gzdev/bys/web/commit/9a5c934f0ebac46cd8c6b62dea262118e3deafa7))
* 收到4g界桩上线后, 同步一次本地的iccid卡号到本地数据 ([d1fc2b8](https://git.kicad99.com/gzdev/bys/web/commit/d1fc2b8b502deb9af9e20649fbd30c07e4acb618))
* 日志提示的国际化翻译修改 ([81ec552](https://git.kicad99.com/gzdev/bys/web/commit/81ec5524d8360e2c8bceae99511dc330a90f30db))
* 添加上报界桩iccid在系统内出现重复时， 弹窗提示用户 ([880aecd](https://git.kicad99.com/gzdev/bys/web/commit/880aecdda8ba32f61d1dd653a75e68366df36c34))
* 添加拍照上传的日志的title颜色 ([83c6e52](https://git.kicad99.com/gzdev/bys/web/commit/83c6e52de4a4b04571a3d1cd9d6a7e2db96ac3c9))
* 添加缺少的界桩巡查历史的翻译 ([0175270](https://git.kicad99.com/gzdev/bys/web/commit/0175270416357f416298aa5e4da48e46958590f5))
* 添加翻译语句 ([e5f1a5a](https://git.kicad99.com/gzdev/bys/web/commit/e5f1a5a305ef27ebcd8d0fbf33c18cca86e13c99))
* 监听服务器发送的1003,界桩拍照上传, 同步展示到指令日志中 ([f0dc3d7](https://git.kicad99.com/gzdev/bys/web/commit/f0dc3d71af8b69e10ff4b83a3f54d1e0f62ae560))
* 角色权限的数据选择中，历史类数据不展示增删改 ([1a603ce](https://git.kicad99.com/gzdev/bys/web/commit/1a603cea431d2b5f86fd7e67f1b57125907791dc))
* 调整各个dialog的底部按钮宽度 ([933763e](https://git.kicad99.com/gzdev/bys/web/commit/933763e437bce169f8a63ffd6541a5ce6aff5424))

## [2.1.2](https://git.kicad99.com/gzdev/bys/web/compare/v2.1.1...v2.1.2) (2024-12-05)


### Bug Fixes

* lint ([36da4e7](https://git.kicad99.com/gzdev/bys/web/commit/36da4e75e6103f686ae1b9b1597c9baa97448916))
* v2.1.2 ([0862027](https://git.kicad99.com/gzdev/bys/web/commit/08620277d69b749e18a088d96e29cc083018be24))
* 报警轨迹回放, 展示报警详情原因和报警时间 ([1908474](https://git.kicad99.com/gzdev/bys/web/commit/190847499d715229a60160abf2217a3c28fe758d))
* 界桩历史展示估计轨迹回放按钮, 只有报警才可用, 增加提示 ([17e8be9](https://git.kicad99.com/gzdev/bys/web/commit/17e8be93d80f3b41e6ca40b19ffa3dd69dc58b63))
* 界桩历史的报警历史添加轨迹回放 ([53717eb](https://git.kicad99.com/gzdev/bys/web/commit/53717eb508e222e9220fd3a70372e8f27d0ec6f3))
* 超过6000米固定按100个点绘制线段, 轨迹播放暂停和完成有通知提示 ([ab39dc3](https://git.kicad99.com/gzdev/bys/web/commit/ab39dc33dd4df41bc88cbb82e06e79db8a1f611b))
* 连续重复的一个位置,只在地图上绘制一个点 ([ce3ecb8](https://git.kicad99.com/gzdev/bys/web/commit/ce3ecb870a7c471672522df1835b21d5d340ed4d))

## [2.1.1](https://git.kicad99.com/gzdev/bys/web/compare/v2.1.0...v2.1.1) (2024-12-05)


### Bug Fixes

*  在请求对应的语言文件时, 添加一个分钟的时间戳, 防止语言文件更新但还是从磁盘获取资源 ([0559883](https://git.kicad99.com/gzdev/bys/web/commit/055988353b900dc8711eac4676adadd45c8adb34))
* lint ([8b3b8f5](https://git.kicad99.com/gzdev/bys/web/commit/8b3b8f53cd1d5a4347f81626fb5a9f81d262d8e5))
* v2.0.6 ([30a5364](https://git.kicad99.com/gzdev/bys/web/commit/30a536484a90be059483b59413ca858c4868b9e5))
* v2.1.1 ([23227ca](https://git.kicad99.com/gzdev/bys/web/commit/23227cacf6dd6a80329b0f46ee6e03ee903fd780))
* 删除";" ([fcefb01](https://git.kicad99.com/gzdev/bys/web/commit/fcefb0180527e7aac18ad5d649b0daeec77b9490))
* 导入的依赖处理 ([8d639c9](https://git.kicad99.com/gzdev/bys/web/commit/8d639c9eef82cd326eda0f97f1fdc4f3eaf4319f))
* 封装遥晕遥毙和解除事件, 异步返回布尔值判断是否成功 ([d8189b3](https://git.kicad99.com/gzdev/bys/web/commit/d8189b35fc7a97a808f703269cba5ffdc865942d))
* 封装遥晕遥毙和解除的方法 ([93bb733](https://git.kicad99.com/gzdev/bys/web/commit/93bb7334c4fcdc7c88e6cefe13d51fbf7c783cab))
* 屏蔽控制器历史中4g界桩的相关内容 ([fa0a558](https://git.kicad99.com/gzdev/bys/web/commit/fa0a558b7b47c7f822d4e7f7f9e7451d8bd1067b))
* 异常统计的表格中的数据可以双击跳转至地图坐标 ([f211e56](https://git.kicad99.com/gzdev/bys/web/commit/f211e56d3f1d074752a28298483e6267a2b707a5))
* 异常统计的遥晕/遥毙添加一列处理按钮(解除遥晕) ([7c8b0bc](https://git.kicad99.com/gzdev/bys/web/commit/7c8b0bc6043a37a24d90aeb4d5930474b28c4fe4))
* 控制器上线历史查询条件屏蔽4g界桩的控制器编号 ([45ce3a2](https://git.kicad99.com/gzdev/bys/web/commit/45ce3a2c3e1b60cc3707be996544ede8b4bcae0a))
* 控制器历史筛选控制器编号屏蔽4g界桩 ([6f157bc](https://git.kicad99.com/gzdev/bys/web/commit/6f157bce03a83260d7a3cf86d849485c0038f9e6))
* 查看控制器历史添加默认过滤条件: 只查控制器 ([b5a494d](https://git.kicad99.com/gzdev/bys/web/commit/b5a494d40456f22ae492e0c6b01e6b98e021b176))
* 没有权限也可以看到界桩安装统计 ([2683476](https://git.kicad99.com/gzdev/bys/web/commit/2683476a2cfbb329af750f29ad0d8c2d8951f1cb))
* 没有维修权限的只能看到4g卡到期异常 ([edfa2d4](https://git.kicad99.com/gzdev/bys/web/commit/edfa2d4e37ef4c3d3fbecefdfd2b71fd542a628f))
* 没有维护权限就看不到界桩安装统计 ([2865f40](https://git.kicad99.com/gzdev/bys/web/commit/2865f40de3600dcea104339bec3265803a5d4470))
* 添加解除遥晕翻译 ([b22df8f](https://git.kicad99.com/gzdev/bys/web/commit/b22df8f8b4c11024acc89d3670a3dd5478238a7e))
* 清除未使用导入 ([774df0f](https://git.kicad99.com/gzdev/bys/web/commit/774df0f2218e456606491e5299b0b2f4b7972702))
* 界桩编辑表单, 在没有维护权限的情况下, 不能编辑界桩的重要数据 ([b83083e](https://git.kicad99.com/gzdev/bys/web/commit/b83083ef36ff00ec697e023597208ce2d8cf229c))
* 界桩详情表添加IMEI ([7e03a3f](https://git.kicad99.com/gzdev/bys/web/commit/7e03a3f7243f3ed843d026498d3eaec766806b65))
* 订阅的事件需要解绑 ([467091b](https://git.kicad99.com/gzdev/bys/web/commit/467091b557e33d5ba705e0cc0a10f0df7c7dba2b))
* 遥毙异常的最后一列留空 只有遥晕才有解除 ([3b82c46](https://git.kicad99.com/gzdev/bys/web/commit/3b82c4637da6d154d67cbf960f833493d0ed9d1c))

## [2.1.0](https://git.kicad99.com/gzdev/bys/web/compare/v2.0.5...v2.1.0) (2024-12-04)


### Features

* 添加图像上传历史查询功能 ([b797cb2](https://git.kicad99.com/gzdev/bys/web/commit/b797cb2fbb03d65e1ac3e8d4c07992a52212a017))


### Bug Fixes

* lint code styles ([84eec00](https://git.kicad99.com/gzdev/bys/web/commit/84eec00cbe6614b23afafc50f0e2f94d6cff90f8))
* 历史查询按钮设置最小宽度"min-w-48" ([a0c6838](https://git.kicad99.com/gzdev/bys/web/commit/a0c6838fd71b193cdee7f7d516154f12737d5c94))
* 历史表格tbody最后一行添加底部边框 ([e231dba](https://git.kicad99.com/gzdev/bys/web/commit/e231dbae2dc8c02b99eb307d6d73353e1e9757cb))
* 更新bys/doc子模块 ([e13cc5f](https://git.kicad99.com/gzdev/bys/web/commit/e13cc5fc0185257c149f7f5399783e8c0ec88bed))
* 更新bys/doc子模块 ([babe6a3](https://git.kicad99.com/gzdev/bys/web/commit/babe6a327db86ca0a1d99815ae2179977874e68b))

## [2.0.5](https://git.kicad99.com/gzdev/bys/web/compare/v2.0.4...v2.0.5) (2024-11-29)


### Bug Fixes

* add commit ([9acdca5](https://git.kicad99.com/gzdev/bys/web/commit/9acdca5de6196553f9f53634136d8316948d00c0))
* v2.0.5 ([2060d9a](https://git.kicad99.com/gzdev/bys/web/commit/2060d9a79bac9dd8944e12a72a342b3203339201))
* 代码格式化 ([5ba8bd3](https://git.kicad99.com/gzdev/bys/web/commit/5ba8bd30b4836b3bc8f57a65482691cd0d8a08ba))
* 代码格式化 ([bb60b5c](https://git.kicad99.com/gzdev/bys/web/commit/bb60b5c883c0677695a260f7c433e8ed9fa42650))
* 修改界桩位置渲染界桩icon时,判断界桩状态来渲染对应的颜色 ([6b079e9](https://git.kicad99.com/gzdev/bys/web/commit/6b079e9f320d5eba86f75201e2ea01ba10a7481b))
* 修改经纬度地图load时,加载图片或渲染界桩marker ([3448fd4](https://git.kicad99.com/gzdev/bys/web/commit/3448fd4750af85f189b93045c2e31cc36712d8a2))
* 切换经纬度地图先添加feature ([e32d317](https://git.kicad99.com/gzdev/bys/web/commit/e32d31763f780b3f56603f39dde5c42061fbea0e))
* 删除为使用导入 ([a386722](https://git.kicad99.com/gzdev/bys/web/commit/a38672299833e5a425b82cbc955cfd8ba6aefcad))
* 判断控制器是基站还是中继 ([7eefbde](https://git.kicad99.com/gzdev/bys/web/commit/7eefbde3de24e43ea47cbe72e6a6fa9dc787e156))
* 同步控制器编辑 ([fb3fe11](https://git.kicad99.com/gzdev/bys/web/commit/fb3fe11ac63c587c6a3fdd12bd464e6c356c4a22))
* 导入文件方式修改 ([cd50ec4](https://git.kicad99.com/gzdev/bys/web/commit/cd50ec4a487cdb41259118cf498d5cfe8f4081ad))
* 打开界桩或者控制器编辑表单组件在watch中同步数据时,判断是否存在GCJ02LngLat,不存在则设置 ([2019273](https://git.kicad99.com/gzdev/bys/web/commit/20192738996ff0091070ac4173ffd427e3ceb992))
* 控制器和界桩获取到的默认数据的经纬度转化为WGS84 ([fdd999c](https://git.kicad99.com/gzdev/bys/web/commit/fdd999c2a793f7ffd3ad7ddd5b4b67ed0cd03ea6))
* 控制器选择定位时将当前图标渲染在选择定位地图上 ([153a8d2](https://git.kicad99.com/gzdev/bys/web/commit/153a8d27ccd2f29d28793d2f8eb17a556f1034cf))
* 首次添加界桩使用地图中心的定位时,直接使用GCJ02赋值 ([d8f5ea3](https://git.kicad99.com/gzdev/bys/web/commit/d8f5ea3e7196816039da386c2bb980e1d44c0421))

## [2.0.4](https://git.kicad99.com/gzdev/bys/web/compare/v2.0.3...v2.0.4) (2024-11-15)


### Bug Fixes

* 修改cordova-plugin-splashscreen地址，当前为7.0.0-dev ([81c79b2](https://git.kicad99.com/gzdev/bys/web/commit/81c79b28cf019d720e00e0a25114203788dfe2f5))
* 配置release指定分支为main ([adf3587](https://git.kicad99.com/gzdev/bys/web/commit/adf3587addcd6c61ceaaf44f2c2c45407a69c8de))

## [0.13.0](https://git.kicad99.com/gzdev/bys/web/compare/v0.12.5...v0.13.0) (2024-01-20)


### Features

* add update_modules.sh, and used on hooks ([9316e70](https://git.kicad99.com/gzdev/bys/web/commit/9316e7048435fb318b3a1f541e53886597af8f27))
* update add android hooks, and add mirrors ([a71840c](https://git.kicad99.com/gzdev/bys/web/commit/a71840c592fd63be0788f71f7911121c034b1834))
* upgrade android plugin version ([8a0cfb1](https://git.kicad99.com/gzdev/bys/web/commit/8a0cfb19667a9de5f7566896215df0051f05a312))
* upgrade cordova platform android@12 ([42264de](https://git.kicad99.com/gzdev/bys/web/commit/42264de8aff2622c58186df676df7bfe5b397e29))
* 删除cordova-android@12版本不再支持的插件 ([92e7018](https://git.kicad99.com/gzdev/bys/web/commit/92e7018a3fc7d7f4f8beba3d02a7f0f1b840084a))
* 更新编译app参数，指定编译为apk ([75ee2a7](https://git.kicad99.com/gzdev/bys/web/commit/75ee2a75b43fc16ecd956bec6f56461ea6f34f86))


### Bug Fixes

* set node engines to 16.0.0 ([46165c8](https://git.kicad99.com/gzdev/bys/web/commit/46165c809cc7720f3673d4eaee96ce911d9c789e))
* upgrade android deploy image, use "linfulong/quasar-builder:node18-gradle7.6-jdk11" ([61584fe](https://git.kicad99.com/gzdev/bys/web/commit/61584fe1c4f691d6b5c53c64054db592d76f5eff))

## [0.12.5](https://git.kicad99.com/gzdev/bys/web/compare/v0.12.4...v0.12.5) (2024-01-16)


### Bug Fixes

* delete unused css code ([938c97d](https://git.kicad99.com/gzdev/bys/web/commit/938c97d608b474e7ecf3fb8bc95c301bc5af841e))
* fix some eslint error ([5e674f9](https://git.kicad99.com/gzdev/bys/web/commit/5e674f967c0cd3b687ae14e6e483f91aa0a0b5e7))
* fix ts can not resolve "Position" ([5689e29](https://git.kicad99.com/gzdev/bys/web/commit/5689e293086f9dbe1d983c77b616a4c1a461b166))
* use node:16-bullseye image for ci ([2e55580](https://git.kicad99.com/gzdev/bys/web/commit/2e55580cb730cfe9e441020ed9638f5eb320f0f4))
* 升级quasar及其相关依赖版本 ([2a17755](https://git.kicad99.com/gzdev/bys/web/commit/2a177558bd6067cfe1aba8b61d8ff582619db66c))

## [0.12.4](https://git.kicad99.com/gzdev/bys/web/compare/v0.12.3...v0.12.4) (2023-12-28)


### Bug Fixes

* fix query unknow user filter with empty creator ([ad10e9d](https://git.kicad99.com/gzdev/bys/web/commit/ad10e9d5469595151e0985253b605e20ef6f1990))
* fix query unknown user filter error ([01ddf2d](https://git.kicad99.com/gzdev/bys/web/commit/01ddf2d0ccc92024a21d57544c6ae74e613cdcef))
* fix query unknown user filter error ([73a1ffc](https://git.kicad99.com/gzdev/bys/web/commit/73a1ffc775adc9dda57e167f0b2caccbb68c770d))

## [0.12.3](https://git.kicad99.com/gzdev/bys/web/compare/v0.12.2...v0.12.3) (2023-12-27)


### Bug Fixes

* 修复界桩报警指令的相关订阅事件参数错误 ([d74e2b2](https://git.kicad99.com/gzdev/bys/web/commit/d74e2b2320176c594ce802164494324648fa292d))

## [0.12.2](https://git.kicad99.com/gzdev/bys/web/compare/v0.12.1...v0.12.2) (2023-08-08)


### Bug Fixes

* 修复重复加载了未安装的界桩的树节点问题 ([320d707](https://git.kicad99.com/gzdev/bys/web/commit/320d707b49c5655b32da82047ff2a94edc0f19b9))

### [0.12.1](https://git.kicad99.com/bys/web/compare/v0.12.0...v0.12.1) (2022-10-19)


### Bug Fixes

* **permission:** 修复界桩、控制器在没有数据编辑权限下可以从地图marker打开数据编辑窗口错误 ([8fe6d25](https://git.kicad99.com/bys/web/commit/8fe6d259d5b7f2d62b3d17f120b8705667893093))

## [0.12.0](https://git.kicad99.com/bys/web/compare/v0.11.27...v0.12.0) (2022-09-21)


### Features

* **Map:** 界桩架构树形节点更新，同步地图上显示或隐藏。使用map.setFilter方法 ([d0a422f](https://git.kicad99.com/bys/web/commit/d0a422f0fa19e21d4fea62c0bd31d5d4966cb807))
* **Settings:** 个人设置的"显示未安装界桩”参数变更，发布自定义事件 ([e65eea6](https://git.kicad99.com/bys/web/commit/e65eea6955022ba9be4138a670015b07d3d4d482))
* **Tree:** 根据"显示未安装界桩"设置，同步更新树形导航 ([1cb5bfd](https://git.kicad99.com/bys/web/commit/1cb5bfd458ccce8649752e8d13afa77b693612a6))


### Bug Fixes

* **ci:** update package mirrors ([9784fca](https://git.kicad99.com/bys/web/commit/9784fcaf3b7bd0adeedca589438fcb5a99daa157))

### [0.11.27](https://git.kicad99.com/bys/web/compare/v0.11.26...v0.11.27) (2022-05-26)


### Bug Fixes

* 修复跳转地图的经纬度坐标系统错误 ([c7ef743](https://git.kicad99.com/bys/web/commit/c7ef743e2a887fb49f53ed4c3fc11a92f802e4ad))

### [0.11.26](https://git.kicad99.com/bys/web/compare/v0.11.25...v0.11.26) (2022-05-18)


### Bug Fixes

* 修复rawObjectProps方法拷贝原型链上值为null/undefined数据时toString方法错误 ([6e1e376](https://git.kicad99.com/bys/web/commit/6e1e376f147e3188754e583be820af231740ac24))

### [0.11.25](https://git.kicad99.com/bys/web/compare/v0.11.24...v0.11.25) (2022-03-15)


### Bug Fixes

* 初始化界桩树形统计界桩和石桩数量 ([fb9728c](https://git.kicad99.com/bys/web/commit/fb9728c2764839a7ad1bcfdb3c15afabcf911343))
* 单位、界桩节点变更、删除时重新统计安装信息 ([7f22fa6](https://git.kicad99.com/bys/web/commit/7f22fa6af028b24e6efc438b580a96b863c5e153))
* 界桩安装统计页面添加上级单位和界桩类型显示 ([c8aa430](https://git.kicad99.com/bys/web/commit/c8aa430599827508f41c9db44fba3b4c9c1d90d1))
* 界桩树形单位节点添加tooltip显示统计信息 ([f77165b](https://git.kicad99.com/bys/web/commit/f77165b82e91318b3285a6a558a9e62ef09119e6))

### [0.11.24](https://git.kicad99.com/bys/web/compare/v0.11.23...v0.11.24) (2022-03-08)


### Bug Fixes

* 取消石桩安装，则设备安装也取消 ([f58f747](https://git.kicad99.com/bys/web/commit/f58f74715fc7d66ee32e3ec7c8faa91429296694))
* 异常界桩统计，过滤没有安装石桩和设备的数据 ([feb35d7](https://git.kicad99.com/bys/web/commit/feb35d7d35491a021ac32146543c5145f6bde4ab))

### [0.11.23](https://git.kicad99.com/bys/web/compare/v0.11.22...v0.11.23) (2021-11-25)


### Bug Fixes

* 全景图组件在destroy前先将pannellum插件尝试destroy ([3a5979a](https://git.kicad99.com/bys/web/commit/3a5979a14c22c922c38d6916bb70398c642e7fe7))

### [0.11.22](https://git.kicad99.com/bys/web/compare/v0.11.21...v0.11.22) (2021-11-17)


### Bug Fixes

* 取消全景图资源url变更监听，父组件滚动列表项添加"key"属性，以便vue能正确处理组件的变更 ([223c89f](https://git.kicad99.com/bys/web/commit/223c89ff35c6511848c74581b62489b0ad2b808b))
* 界桩列表树已安装的界桩图标颜色改为绿色，与地图的标记颜色保持同步 ([9541513](https://git.kicad99.com/bys/web/commit/9541513bb2730b12bde77908d8c280fce6c5d8ac))

### [0.11.21](https://git.kicad99.com/bys/web/compare/v0.11.20...v0.11.21) (2021-11-16)


### Bug Fixes

* 修复图影像在搜索界桩全景图时，第一个全景图组件被vue复用，在url变更时pannellum插件没有重新渲染全景问题 ([b133bf4](https://git.kicad99.com/bys/web/commit/b133bf448bddf91be4e1cfcb7543798fb5bc20b4))

### [0.11.20](https://git.kicad99.com/bys/web/compare/v0.11.19...v0.11.20) (2021-11-01)


### Bug Fixes

* 替换已安装设备的界桩标记图标，改为亮绿色 ([63aa15b](https://git.kicad99.com/bys/web/commit/63aa15bb7a50309cad5a60a676407a50b97e563f))

### [0.11.19](https://git.kicad99.com/bys/web/compare/v0.11.18...v0.11.19) (2021-10-29)


### Bug Fixes

* format code style ([b8bc358](https://git.kicad99.com/bys/web/commit/b8bc35887145c1bcad5764f845c95a411647fd09))
* use node:14.18.1 image, fix node-sass not supported node:16.x.x ([c953816](https://git.kicad99.com/bys/web/commit/c953816814234adf1bf4aedb6ce34ec9ecc5bbf0))

### [0.11.18](https://git.kicad99.com/bys/web/compare/v0.11.17...v0.11.18) (2021-10-29)


### Bug Fixes

* 修复缺失的本地化字体资源，使用node-fontnik生成 ([e335f87](https://git.kicad99.com/bys/web/commit/e335f8753f1a7dad249de63ceb851fbca768b2d7))

### [0.11.17](https://git.kicad99.com/bys/web/compare/v0.11.16...v0.11.17) (2021-10-26)


### Bug Fixes

* 修复更新界桩排队发射间隔、唤醒基准时间参数时没有同步本地同控制器下其他界桩参数错误 ([cb51522](https://git.kicad99.com/bys/web/commit/cb5152250f1ef9e91ea96eb897ace317f8d900e2))

### [0.11.16](https://git.kicad99.com/bys/web/compare/v0.11.15...v0.11.16) (2021-10-26)


### Bug Fixes

* 如果已经安装界桩设备，石桩默认已安装 ([2bc4af1](https://git.kicad99.com/bys/web/commit/2bc4af1ab63554c472bab18e92c52922c0298611))

### [0.11.15](https://git.kicad99.com/bys/web/compare/v0.11.14...v0.11.15) (2021-10-25)


### Bug Fixes

* 断开服务器15分钟后，重新请求数据时，清空当前的界桩报警状态 ([ba90a2d](https://git.kicad99.com/bys/web/commit/ba90a2d9c95b41583db9b3a4ea6f588057d07302))

### [0.11.14](https://git.kicad99.com/bys/web/compare/v0.11.13...v0.11.14) (2021-10-22)


### Bug Fixes

* 屏蔽界桩树形电量显示 ([65f841e](https://git.kicad99.com/bys/web/commit/65f841efedd173525c751651556ec0458c167cbc))

### [0.11.13](https://git.kicad99.com/bys/web/compare/v0.11.12...v0.11.13) (2021-10-20)


### Bug Fixes

* 因界桩电池记忆原因，修正界桩电量百分比显示 ([52c689f](https://git.kicad99.com/bys/web/commit/52c689febc725f338089dcccdf34a414afbc459a))

### [0.11.12](https://git.kicad99.com/bys/web/compare/v0.11.11...v0.11.12) (2021-10-19)


### Bug Fixes

* 修复部分方法中读取GCJO2坐标错误 ([d9b24e6](https://git.kicad99.com/bys/web/commit/d9b24e6a55d76c5641328bfc682b763c0e8d7183))

### [0.11.11](https://git.kicad99.com/bys/web/compare/v0.11.10...v0.11.11) (2021-09-07)


### Bug Fixes

* add req_login param to publish param ([8e73543](https://git.kicad99.com/bys/web/commit/8e73543f542897453e278a66c31d9b8572ed242e))
* marker update append oldData attr into newData ([7964d3a](https://git.kicad99.com/bys/web/commit/7964d3a7561c3303a1483819182481e1b765b617))
* remove gitlab.ci sharp dep version ([97767fd](https://git.kicad99.com/bys/web/commit/97767fd662fb5782099dd1999b2cfb9a4010a07c))
* update gitlab.ci sharp dep version ([fc0086f](https://git.kicad99.com/bys/web/commit/fc0086fbf672a4d98f0c22a65ada3f983010bdf3))
* update gitlab.ci sharp dep version ([6f190ec](https://git.kicad99.com/bys/web/commit/6f190ec5972df87603886e25f276e538986e9ccc))
* update gitlab.ci sharp dep version ([ab8e74e](https://git.kicad99.com/bys/web/commit/ab8e74e82aa0f60ca20cf72f0b46ba5bfa9dd08f))

### [0.11.10](https://git.kicad99.com/bys/web/compare/v0.11.9...v0.11.10) (2021-08-03)


### Bug Fixes

* map box touch-start evt on mobile bug ([2af6639](https://git.kicad99.com/bys/web/commit/2af6639fa6d5bbfc72c2297d500639e635f7d449))

### [0.11.9](https://git.kicad99.com/bys/web/compare/v0.11.8...v0.11.9) (2021-08-02)


### Bug Fixes

* 修复删除单位时，该单位下的界桩数据管理没有同步错误 ([482bf75](https://git.kicad99.com/bys/web/commit/482bf754a3d3171f19f0afd7301541229893ebbc))

### [0.11.8](https://git.kicad99.com/bys/web/compare/v0.11.7...v0.11.8) (2021-07-27)


### Bug Fixes

* fix QueryAllUnknownUnit return type error ([b78ffa1](https://git.kicad99.com/bys/web/commit/b78ffa19554b9acb1330b6e5fcb82665f9abb419))
* fix QueryAllUnknownUnit return type error ([2a8d7f1](https://git.kicad99.com/bys/web/commit/2a8d7f1737aaa6a8d1ea7bf2d488682636e942e9))
* 修复界桩、控制器数据在同步时没有更新地图图层错误 ([98d799b](https://git.kicad99.com/bys/web/commit/98d799bfadd25057f52dd500636729862042d8b5))
* 修改更新App的提示操作按钮，屏蔽取消按钮，强制更新 ([cc11e23](https://git.kicad99.com/bys/web/commit/cc11e23040dff531bfe31c149ec830f9498111fc))

### [0.11.7](https://git.kicad99.com/bys/web/compare/v0.11.6...v0.11.7) (2021-07-26)


### Bug Fixes

* rebuild proto ([6014a05](https://git.kicad99.com/bys/web/commit/6014a05d8d7e5f9bf3c4e4288d4b3fc6d02fe386))
* 修复界桩开栈时，上报d1指令时因本地数据isAlreadyRemoveAlarm属性没有初始化导致的解除报警消息提示 ([f1fbf06](https://git.kicad99.com/bys/web/commit/f1fbf065892ccc7b90cb5ac9265496c4bf760761))
* 修改控制器数据，同一基站下控制器通道不能重复 ([e125bab](https://git.kicad99.com/bys/web/commit/e125bab87ad4c9054ad0a35f38b480a1c796755e))
* 修改控制器数据，如果改了名称，则提示同步修改硬件配置 ([5136c64](https://git.kicad99.com/bys/web/commit/5136c64d6d42c08048d6e63dd22ee8f4b54b1e7a))
* 控制器管理，基站没有上级控制器，禁用且置空 ([6dd3bd6](https://git.kicad99.com/bys/web/commit/6dd3bd6d67a4661ee9af236e090e3bac913eb585))
* 界桩历史，添加显示实际接收中继的信息 ([3bd95a1](https://git.kicad99.com/bys/web/commit/3bd95a13f4a49c08b6827663fbeeaef4b98406fb))

### [0.11.6](https://git.kicad99.com/bys/web/compare/v0.11.5...v0.11.6) (2021-07-26)


### Bug Fixes

* 界桩排队发射间隔默认值改为最小值20 ([34525c4](https://git.kicad99.com/bys/web/commit/34525c4beb01a6d47464d5145a43c4ac8199ff1e))

### [0.11.5](https://git.kicad99.com/bys/web/compare/v0.11.4...v0.11.5) (2021-07-26)


### Bug Fixes

* rebuild proto ([3157119](https://git.kicad99.com/bys/web/commit/3157119f65675f6131d0c25364f643a0975f2d8b))
* 修改删除单位的警告语，明确告知删除单位的后果 ([1f81f68](https://git.kicad99.com/bys/web/commit/1f81f68cf29d52789db4f10f9647596a41e8ff72))
* 屏蔽界桩旧的红线图规则字段 ([1ace3f2](https://git.kicad99.com/bys/web/commit/1ace3f299b8df4fe8e7adfcf08a84af6a11f8698))

### [0.11.4](https://git.kicad99.com/bys/web/compare/v0.11.3...v0.11.4) (2021-07-23)


### Bug Fixes

* (PM BUG [#18117](https://git.kicad99.com/bys/web/issues/18117))修改排队、报警发射间隔最小参数范围检验参数错误 ([b70d45e](https://git.kicad99.com/bys/web/commit/b70d45ecc89d361d947479281a76b84b9df6c2ea))

### [0.11.3](https://git.kicad99.com/bys/web/compare/v0.11.2...v0.11.3) (2021-07-22)


### Bug Fixes

* (PM BUG [#18117](https://git.kicad99.com/bys/web/issues/18117))修改排队、报警发射间隔最小参数范围 ([b0456fd](https://git.kicad99.com/bys/web/commit/b0456fd4996771171e8e8ced5ad2978d4f4cfac7))

### [0.11.2](https://git.kicad99.com/bys/web/compare/v0.11.1...v0.11.2) (2021-07-15)


### Bug Fixes

* 使用pako压缩数据，将旧的界桩生成base64添加到rpcMeta中 ([76889ec](https://git.kicad99.com/bys/web/commit/76889ec8b752f9f78d71383d158b7f2a71365955))

### [0.11.1](https://git.kicad99.com/bys/web/compare/v0.11.0...v0.11.1) (2021-06-28)


### Bug Fixes

* 删除界桩更新触发的重绘红线图逻辑，直接重绘整个红线图 ([d97b7ae](https://git.kicad99.com/bys/web/commit/d97b7aed88dd5204c198528499850f4c5704e789))
* 更新说明文档 ([dc6f5f7](https://git.kicad99.com/bys/web/commit/dc6f5f76b783f223195b183b374f9acbba86dc8d))

## [0.11.0](https://git.kicad99.com/bys/web/compare/v0.10.19...v0.11.0) (2021-06-28)


### Features

* 修改界桩红线图有配置的连线逻辑，与配置的界桩两两连线 ([e2691f9](https://git.kicad99.com/bys/web/commit/e2691f98908381c44675ca33c45344f4419bb072))
* 修改界桩红线图设置规则 ([4983953](https://git.kicad99.com/bys/web/commit/498395325b72533ad9c9db2fabbc0bae25ce3f34))
* 修改界桩红线图设置规则，设置默认红线图连接暂未实现 ([2cfa760](https://git.kicad99.com/bys/web/commit/2cfa760522c2bcc6bb35dcd6b6af05c6107f01b7))
* 完成红线图默认规则初始化计算逻辑 ([9669599](https://git.kicad99.com/bys/web/commit/9669599551b09fdba4506e13c57680459358aa65))


### Bug Fixes

* 修复界桩从popup更新数据后，重新打开，没有更新数据问题 ([e50146c](https://git.kicad99.com/bys/web/commit/e50146cba676a39aba0d96b68afd158942c1d883))
* 修改界桩、控制器图层数据源存储结构，使用Map以优化数组查找耗时过长问题导致图层加载缓慢问题 ([d5736f2](https://git.kicad99.com/bys/web/commit/d5736f2897f33efb8426163cf3c9e48301305d5d))
* 修改红线图规则JSON字段名称，删除名称长度以减少网络传输字节 ([a84300c](https://git.kicad99.com/bys/web/commit/a84300c4d8cb61e59764b708b988f1f70edd0116))
* 双击控制器、界桩图层，禁用默认缩放地图行为 ([ab7e70f](https://git.kicad99.com/bys/web/commit/ab7e70fdf9a9b63bacce71b93f9dad447ea372b4))
* 屏蔽旧的红线图配置 ([e54198b](https://git.kicad99.com/bys/web/commit/e54198bde6def2530b2c7aeb3206a3eedac45959))
* 更新quasar框架版本 ([11ffa7b](https://git.kicad99.com/bys/web/commit/11ffa7b6dea05bd11c893c1a531e251f8e9d0382))

### [0.10.19](https://git.kicad99.com/bys/web/compare/v0.10.18...v0.10.19) (2021-06-24)


### Bug Fixes

* 修复界桩和控制器添加数据时，坐标处理错误 ([eb05b7c](https://git.kicad99.com/bys/web/commit/eb05b7c85c7c5495f6e0d66a6d5499137a98a5eb))

### [0.10.18](https://git.kicad99.com/bys/web/compare/v0.10.17...v0.10.18) (2021-06-23)


### Bug Fixes

* add i18n items ([e36b810](https://git.kicad99.com/bys/web/commit/e36b81017f8f9df145549d95d166e2af5ecb1246))
* add page for user query markers-install status ([944e102](https://git.kicad99.com/bys/web/commit/944e102e7e75f5248b4faf89da8688d8b38a95b6))
* rm un-used code ([6560009](https://git.kicad99.com/bys/web/commit/6560009729e1c36d2cfc74df6677cda2ade6ea43))

### [0.10.17](https://git.kicad99.com/bys/web/compare/v0.10.16...v0.10.17) (2021-06-23)


### Bug Fixes

* fix 'Layer' is defined but never used ([8c197c1](https://git.kicad99.com/bys/web/commit/8c197c18649071cf11edcd9f8ca74fb662863c54))
* 修复安装石桩缺失的翻译 ([84dbde0](https://git.kicad99.com/bys/web/commit/84dbde016e2a18c7660f539397fd665f1a962729))

### [0.10.16](https://git.kicad99.com/bys/web/compare/v0.10.15...v0.10.16) (2021-06-22)


### Bug Fixes

* fix build project ci error ([a4fb267](https://git.kicad99.com/bys/web/commit/a4fb2675cda0917319b84eae01e5646e5ed0dd29))

### [0.10.15](https://git.kicad99.com/bys/web/compare/v0.10.14...v0.10.15) (2021-06-22)


### Bug Fixes

* fix ci dependencies on deploy-spa ([64b92b8](https://git.kicad99.com/bys/web/commit/64b92b8e638156d031d92fd8dfa826b79fb2e687))

### [0.10.14](https://git.kicad99.com/bys/web/compare/v0.10.13...v0.10.14) (2021-06-22)


### Bug Fixes

* add resource for marker icon ([ae38b66](https://git.kicad99.com/bys/web/commit/ae38b663d6e132b977039312eaa4f0791f390689))
* fix quasar-ui-qmediaplayer "Null" type error ([82aaa47](https://git.kicad99.com/bys/web/commit/82aaa47750b971d7f8aa3099e79b9666e5fac726))
* rm yarn.lock ([9b78d94](https://git.kicad99.com/bys/web/commit/9b78d94a48ac145b663003cdd554ff144d32c016))
* scripts path err ([76d4162](https://git.kicad99.com/bys/web/commit/76d416279e856bf6960215ec52422b4d8ca9da74))
* set sharp url ([d571b60](https://git.kicad99.com/bys/web/commit/d571b605767ae6b7b40a39d0188f78c6f135e955))
* sync yarn lock ([bbf98f3](https://git.kicad99.com/bys/web/commit/bbf98f3ae1172b3e2dcb0f67a3b046e348c9b570))
* update dependence for qmediaplayer ([8fed062](https://git.kicad99.com/bys/web/commit/8fed06262804df3640038dd7393221ae613f0fe0))
* update marker ([608f3b1](https://git.kicad99.com/bys/web/commit/608f3b1fd2ae9a3091a3ba0f9bce4ede4f896ab0))
* update yarn sharp_dist_base_url ([401b001](https://git.kicad99.com/bys/web/commit/401b001abf1977c9ea2fa8691942fec69e96ca4a))
* 脚本添加当前工作目录修正 ([774e08a](https://git.kicad99.com/bys/web/commit/774e08a832145c3d3d977240d578d25ac1948feb))

### [0.10.13](https://git.kicad99.com/bys/web/compare/v0.10.12...v0.10.13) (2021-06-08)


### Bug Fixes

* 修复设备列表树切换选中状态，对应的地图标记没有重新显示问题(PM: BUG [#17789](https://git.kicad99.com/bys/web/issues/17789)) ([0a2d72d](https://git.kicad99.com/bys/web/commit/0a2d72ded441ca72e4d678836b3500fac8435c02))

### [0.10.12](https://git.kicad99.com/bys/web/compare/v0.10.11...v0.10.12) (2021-06-04)


### Bug Fixes

* check if notify when got controller online/offline ([5e9cfd7](https://git.kicad99.com/bys/web/commit/5e9cfd7bae1f0e2c41248d7791e930e536a3d7d0))
* use const enum ([87c196a](https://git.kicad99.com/bys/web/commit/87c196a5da0f972561d475c610dd730e1c8658c2))

### [0.10.11](https://git.kicad99.com/bys/web/compare/v0.10.10...v0.10.11) (2021-06-03)


### Bug Fixes

* **bysMarker:** 修复界桩解除报警后，重新报警时同一个数据rid导致popup没有同步问题(DZ141-2项目BUG [#17709](https://git.kicad99.com/bys/web/issues/17709)) ([3e0b6b8](https://git.kicad99.com/bys/web/commit/3e0b6b8e3368fedd9e3d7c7cbd8dc58a3c863c09))

### [0.10.10](https://git.kicad99.com/bys/web/compare/v0.10.9...v0.10.10) (2021-05-27)


### Bug Fixes

* **Controller:** 修复界桩上指令时，更新控制器最后状态错误(登录时间，最后数据时间) ([6079682](https://git.kicad99.com/bys/web/commit/6079682c1094864e68c51de2ad2f1e9a4a70b6d6))

### [0.10.9](https://git.kicad99.com/bys/web/compare/v0.10.8...v0.10.9) (2021-05-27)


### Bug Fixes

* **BysMarker:** 修复界桩在报警时，出现原位置的marker被清除问题 ([b7e6000](https://git.kicad99.com/bys/web/commit/b7e6000e6ee1f08f812c7742a9cb1cf5686ba36a))
* **BysMarker:** 修复界桩在报警时，非下发指令的客户端界面没有同步解除报警状态问题 ([f107737](https://git.kicad99.com/bys/web/commit/f10773788bc21c3ab4d1eacfbff17c5c1e0b9735))

### [0.10.8](https://git.kicad99.com/bys/web/compare/v0.10.7...v0.10.8) (2021-05-26)


### Bug Fixes

* **map:** 修复更新界桩、控制器位置后，没有同步更新对应popup位置问题 ([9572d2e](https://git.kicad99.com/bys/web/commit/9572d2e0f16f2b5cf57fa500dc4cfa07ed059c91))

### [0.10.7](https://git.kicad99.com/bys/web/compare/v0.10.6...v0.10.7) (2021-05-25)


### Bug Fixes

* **map:** 修改报警铃铛列表内容不换行 ([56526a0](https://git.kicad99.com/bys/web/commit/56526a0b44b840660d0565f687bbd7f855872c01))
* **map:** 将原始的wgs84转换成gcj02坐标缓存到数据的"GCJ02LngLat"属性中，不覆盖原有坐标 ([b1c0d8c](https://git.kicad99.com/bys/web/commit/b1c0d8caf171c54b89d7780bbbc0c8394e69739b))

### [0.10.6](https://git.kicad99.com/bys/web/compare/v0.10.5...v0.10.6) (2021-05-25)


### Bug Fixes

* dev tree dbClick marker lon let need check gps lon lat ([b1448ef](https://git.kicad99.com/bys/web/commit/b1448ef641675e62b6f7609db048ffe5d8df5d35))
* marker mapbox quick edit data write back need check rid equally ([619093e](https://git.kicad99.com/bys/web/commit/619093e4ae29958ad95be7f8d0cacf49c409bc69))
* remove logs ([8590cea](https://git.kicad99.com/bys/web/commit/8590cea31b4edc2ee454581d9a1810b53cb2d965))

### [0.10.5](https://git.kicad99.com/bys/web/compare/v0.10.4...v0.10.5) (2021-05-24)


### Bug Fixes

* **map:** 统一地图跳转方法入口 ([a2f3d99](https://git.kicad99.com/bys/web/commit/a2f3d9982a9b4c48f414740b415cd17495b79cbc))
* check controller network type for all type of controller ([5486f2a](https://git.kicad99.com/bys/web/commit/5486f2a549ded1f478b9474e3a670a5a062bf9b0))

### [0.10.4](https://git.kicad99.com/bys/web/compare/v0.10.3...v0.10.4) (2021-05-24)


### Bug Fixes

* **BysMarker:** 修复界桩禁止弹框判断逻辑 ([cd60e5c](https://git.kicad99.com/bys/web/commit/cd60e5c18954ef3ca43c858c6b5c8204a3726902))

### [0.10.3](https://git.kicad99.com/bys/web/compare/v0.10.2...v0.10.3) (2021-05-24)


### Bug Fixes

* 如果处于显示界桩历史轨迹时，禁止界桩报警弹框 ([0675763](https://git.kicad99.com/bys/web/commit/0675763836c132539b60c0e56f256173075f77b8))

### [0.10.2](https://git.kicad99.com/bys/web/compare/v0.10.1...v0.10.2) (2021-05-24)


### Bug Fixes

* **BysMarker:** 更新界桩popup弹框强制更新逻辑执行时机 ([a4fce2b](https://git.kicad99.com/bys/web/commit/a4fce2bc98874bed1464a24a9363db3d892ee9b1))

### [0.10.1](https://git.kicad99.com/bys/web/compare/v0.10.0...v0.10.1) (2021-05-14)


### Bug Fixes

* **controller:** 修复界桩上传指令触发更新中继状态时,中继数据查找错误 ([9507c40](https://git.kicad99.com/bys/web/commit/9507c40f5d6d2fa92d161a1c07719b069534a38d))

## [0.10.0](https://git.kicad99.com/bys/web/compare/v0.9.13...v0.10.0) (2021-05-11)


### Features

* **controller:** 添加控制器报警指令处理 ([0d8077f](https://git.kicad99.com/bys/web/commit/0d8077f8dc971ace53dbad1dec2cd3ef6104a35a))


### Bug Fixes

* **controller:** 添加界桩解除报警成功消息提示 ([55d0dd9](https://git.kicad99.com/bys/web/commit/55d0dd9af7093f7627869806776c1d9769ced17d))
* **proto:** 更新协议，添加控制器状态参数和控制器报警 ([11be7fb](https://git.kicad99.com/bys/web/commit/11be7fb5ffd53274bc3291071d74b6eac8e1cf4f))

### [0.9.13](https://git.kicad99.com/bys/web/compare/v0.9.12...v0.9.13) (2021-05-10)


### Bug Fixes

* **controller:** 修复判断界桩时间同步的条件的时间错误，需要使用服务器的时间 ([d0f2a17](https://git.kicad99.com/bys/web/commit/d0f2a1740e30ed01769fa442cb56f7f76adfd3cc))

### [0.9.12](https://git.kicad99.com/bys/web/compare/v0.9.11...v0.9.12) (2021-05-08)


### Bug Fixes

* **controller:** 修改控制器心跳指令处理网络类型 ([5c47a7c](https://git.kicad99.com/bys/web/commit/5c47a7cb2f716b1e405295d3524d898c7832eacf))

### [0.9.11](https://git.kicad99.com/bys/web/compare/v0.9.10...v0.9.11) (2021-05-08)


### Bug Fixes

* abnormal controller add power row ([efd067b](https://git.kicad99.com/bys/web/commit/efd067bace6bda4270042c3dd8fb9e696fb821e8))
* controller power err in heartbeet ([eeb13ed](https://git.kicad99.com/bys/web/commit/eeb13eddbdc50846743f5aeed76139fcc6e8d5eb))
* controller power err in heartbeet ([c54ed25](https://git.kicad99.com/bys/web/commit/c54ed25fa6ac84336adc2e94c0c1fcb3c2022cea))
* controller power err in heartbeet ([39b53c4](https://git.kicad99.com/bys/web/commit/39b53c4861903f2f6c2cff624668c8ef3244a766))

### [0.9.10](https://git.kicad99.com/bys/web/compare/v0.9.9...v0.9.10) (2021-05-08)


### Bug Fixes

* **controller:** 使用rawObjectProps方法将原型链上属性拷贝到对象自身中，中继不登录，在上心跳时检测网络类型，默认为fsk ([c30cdc8](https://git.kicad99.com/bys/web/commit/c30cdc84749c20645f7ddfa5eb51bb06d31ec036))

### [0.9.9](https://git.kicad99.com/bys/web/compare/v0.9.8...v0.9.9) (2021-05-06)


### Bug Fixes

* **controller:** 修复界桩参数时间判断错误 ([b7e8067](https://git.kicad99.com/bys/web/commit/b7e80677ac1941d5443fc4fdc589abf4566b0e81))

### [0.9.8](https://git.kicad99.com/bys/web/compare/v0.9.7...v0.9.8) (2021-05-06)


### Bug Fixes

* **package:** 修复jsyrpc模块最低版本要求错误 ([cbf8ae9](https://git.kicad99.com/bys/web/commit/cbf8ae9ca5bb76a336b3ed25f489659d1f7f72c3))

### [0.9.7](https://git.kicad99.com/bys/web/compare/v0.9.6...v0.9.7) (2021-05-06)


### Bug Fixes

* 重新排序 异常控制器列 ([fff605f](https://git.kicad99.com/bys/web/commit/fff605f493f15dffbc9aeb7fb07262b9dcd80ea9))

### [0.9.6](https://git.kicad99.com/bys/web/compare/v0.9.5...v0.9.6) (2021-05-06)


### Bug Fixes

* cmd test.fix time is sync check ([7acad2d](https://git.kicad99.com/bys/web/commit/7acad2da4785ebd1ef5b5183334006d818e9c8e6))
* compile error ([7e8221e](https://git.kicad99.com/bys/web/commit/7e8221ea629de104ffde67cd483ba975f089a683))
* controller update merge attrs ([628d247](https://git.kicad99.com/bys/web/commit/628d2474773ca4f227bb443a03a0eb2f54314d46))
* fix conflict with jsyrpc ([9d3ffd4](https://git.kicad99.com/bys/web/commit/9d3ffd4e32fff8ac9ec5454365f3cbfaf24fcd59))
* impl marker exteact search ([8b7f6bc](https://git.kicad99.com/bys/web/commit/8b7f6bc421f5c9e941b94d5662bdd9730ecf7533))
* merge object attribute save old object attrs ([6d412be](https://git.kicad99.com/bys/web/commit/6d412be74474f658e430e8c92ffbfef2dc292b31))
* ordered controller and marker ([ca2a669](https://git.kicad99.com/bys/web/commit/ca2a66914c54fbbfc704afb3d5917067448fce9f))
* sleep func add true arg for resolve ([ec1e697](https://git.kicad99.com/bys/web/commit/ec1e69724408cf4ec94be5bffe8b947b9829f7e5))
* time compare ([7913f08](https://git.kicad99.com/bys/web/commit/7913f08702f9f22d62b979df86db626549a3397a))
* update dep in package.json ([3b1ca82](https://git.kicad99.com/bys/web/commit/3b1ca82315f1037d877a9e32fa31035c607fc58b))
* v0.9.6 ([d9a5bb7](https://git.kicad99.com/bys/web/commit/d9a5bb7ccfc155afd79b5961c7fa947f814a0864))
* v0.9.7 ([69c22c4](https://git.kicad99.com/bys/web/commit/69c22c4bbde1c5aebae88e4bad9c8770329aa492))
* yarn lock update ([7ec3889](https://git.kicad99.com/bys/web/commit/7ec38894b563977fa6e9331ab791e244dd3055a1))

### [0.9.5](https://git.kicad99.com/bys/web/compare/v0.9.4...v0.9.5) (2021-04-30)


### Bug Fixes

* **controller:** 查询控制器状态，添加3s超时处理，累计3次则视为下线 ([852a03e](https://git.kicad99.com/bys/web/commit/852a03eb4fad7b2b99ccc67b0917f8642523ab67))
* **controller:** 添加中继的通道号与基站的关系索引 ([2e39ac8](https://git.kicad99.com/bys/web/commit/2e39ac88989e9637dbe4cda37616376b4376600f))
* **Controller:** 接收到界桩指令时，更新控制器的最后数据时间和在线状态等参数 ([f761ae7](https://git.kicad99.com/bys/web/commit/f761ae703a819010390682bf983dadfbbd2be0eb))
* **i18n:** 添加缺失的翻译文本 ([8be41bf](https://git.kicad99.com/bys/web/commit/8be41bfd82720912eabb6ace8f8d0163dd6b571b))
* **Mapbox:** 优化界桩和控制器的地图popup按钮组，添加边框样式 ([1da1c46](https://git.kicad99.com/bys/web/commit/1da1c4626b2365676292334b0233222e9d3dccd2))

### [0.9.4](https://git.kicad99.com/bys/web/compare/v0.9.3...v0.9.4) (2021-04-29)


### Bug Fixes

* **DataEdit:** fix "filter-method" filter logic ([ad702a0](https://git.kicad99.com/bys/web/commit/ad702a0f62627201084fbddda6676c1f86ea2cee))
* **DataEdit:** fix const error ([f38d310](https://git.kicad99.com/bys/web/commit/f38d31059e8040a3ffdce1b38b578b72d28ebc1d))
* **DataEdit:** fix const error ([a17a27e](https://git.kicad99.com/bys/web/commit/a17a27e09f7ca49f112cb0e9dd7bac8b19b054a6))
* **DataEdit:** modify table's filter-method ([dc50d83](https://git.kicad99.com/bys/web/commit/dc50d83ff3523b09442437423550163298e65103))
* impl data table filter method ([4bf2cb4](https://git.kicad99.com/bys/web/commit/4bf2cb4d2e6167d4749a45de7ba4f3195826d008))
* impl history table filter method ([f15b723](https://git.kicad99.com/bys/web/commit/f15b723cb50a41ee07554b193f8b5c8bff13d569))
* merge network type ([3f10bf5](https://git.kicad99.com/bys/web/commit/3f10bf51873db427fccaa5978d2d4183ec1bdc58))
* remoev console log ([62abae2](https://git.kicad99.com/bys/web/commit/62abae2b38a623f3ca42feb76754f35d535db758))
* update current icon ([601539a](https://git.kicad99.com/bys/web/commit/601539afda0147a50f933d9aeda0db20cd1c3734))

### [0.9.3](https://git.kicad99.com/bys/web/compare/v0.9.2...v0.9.3) (2021-04-19)


### Bug Fixes

* impl tree upper unit marker count-init ([8bd121d](https://git.kicad99.com/bys/web/commit/8bd121d1de8c12b5f1390f126aad92bded34bdd2))
* impl tree upper unit marker count-init ([2f5b28e](https://git.kicad99.com/bys/web/commit/2f5b28efe9ccbbd16d7ec236caf76d80fe3e5614))

### [0.9.2](https://git.kicad99.com/bys/web/compare/v0.9.1...v0.9.2) (2021-04-19)


### Bug Fixes

* modify marker history page translate ([19fcbe6](https://git.kicad99.com/bys/web/commit/19fcbe64eae3eff7932f505f97c5a54252b8b599))

### [0.9.1](https://git.kicad99.com/bys/web/compare/v0.9.0...v0.9.1) (2021-04-09)


### Bug Fixes

* **user-login:** add error code for no such system ([9e34939](https://git.kicad99.com/bys/web/commit/9e349391a1e2a1ce3bda4767c499817fe9465e10))

## [0.9.0](https://git.kicad99.com/bys/web/compare/v0.8.5...v0.9.0) (2021-04-08)


### Features

* **Controller:** 只有登录到系统的基站和中继才能修改服务器地址 ([8631058](https://git.kicad99.com/bys/web/commit/8631058d2cbcd60461b04dce193445b62de48f59))
* **Controller:** 添加修改控制器注册服务器地址功能 ([40247e7](https://git.kicad99.com/bys/web/commit/40247e7427772c9cf1562c10c415f4a50edf0e42))
* **Controller:** 设备树控制器节点添加修改服务器地址右键菜单 ([2c8773d](https://git.kicad99.com/bys/web/commit/2c8773d53cead5e8e96dee86ec5e1ce0756e31f6))


### Bug Fixes

* **Controller:** 修复控制器地图标记popup组件可能存在的undefined访问错误 ([01f6317](https://git.kicad99.com/bys/web/commit/01f6317557b09e39321ca189d28469b913cf49d8))
* **Controller:** 修改操作历史修改控制器的服务器地址日志信息 ([50e8f63](https://git.kicad99.com/bys/web/commit/50e8f63efc7409cce7014319055a10753aea6ca2))
* **Controller:** 添加修改控制器服务器地址协议 ([ffb4791](https://git.kicad99.com/bys/web/commit/ffb4791a684f538e2aaa2dd44c98910d7e446163))
* **DialogPlugin:** 修复dialog插件在多层dialog下被遮住问题 ([2d9e5b3](https://git.kicad99.com/bys/web/commit/2d9e5b358982e92cfa0e1e71b0c0b2f2d60a2676))
* **Mapbox:** 修改控制器、界桩标记弹框公用样式 ([ead85de](https://git.kicad99.com/bys/web/commit/ead85de199bebaed660cf1b27f350604b798228e))

### [0.8.5](https://git.kicad99.com/bys/web/compare/v0.8.4...v0.8.5) (2021-04-07)


### Bug Fixes

* **controller-heartbeat:** judge timeout after query online controllers ([19b7673](https://git.kicad99.com/bys/web/commit/19b7673c9c4d9b14799b5080b70def412cdf777b))

### [0.8.4](https://git.kicad99.com/bys/web/compare/v0.8.3...v0.8.4) (2021-04-07)


### Bug Fixes

* **cmdTest:** cmd test page.virtual scroll sticky footer ([df16fe9](https://git.kicad99.com/bys/web/commit/df16fe96e39e9b0623c26795ec972b08341ce7b7))

### [0.8.3](https://git.kicad99.com/bys/web/compare/v0.8.2...v0.8.3) (2021-04-06)


### Bug Fixes

* **controller-heartbeat:** add diff time out for bs_base & bs_relay ([f9dba60](https://git.kicad99.com/bys/web/commit/f9dba60f6f724e1f2cc0f73c812bebfd6e834247))

### [0.8.2](https://git.kicad99.com/bys/web/compare/v0.8.1...v0.8.2) (2021-04-02)


### Bug Fixes

* **Mapbox:** 修复地图搜索控制器跳转功能没有正确打开上级节点bug ([af113ad](https://git.kicad99.com/bys/web/commit/af113ad7401a6ef9b54498de8c6c844450b0c877))

### [0.8.1](https://git.kicad99.com/bys/web/compare/v0.8.0...v0.8.1) (2021-02-03)


### Bug Fixes

* **system config:** 修改系统网站标题名称 ([fc80b80](https://git.kicad99.com/bys/web/commit/fc80b802e7a7029db2d6fd22e4c3ede9bd914f9a))
* **user:** 取消用户电话表单校验规则，最大输入32字符 ([d44e9d9](https://git.kicad99.com/bys/web/commit/d44e9d983438fecf5057f803351b90a610ac6eff))
* **user:** 用户管理单位下拉列表添加排序处理 ([37cd08a](https://git.kicad99.com/bys/web/commit/37cd08ac402946129ce3385f3dff32b159a6fee6))

## [0.8.0](https://git.kicad99.com/bys/web/compare/v0.7.29...v0.8.0) (2021-02-02)


### Features

* **tree:** 单位节点添加界桩数量统计标记 ([0cc8b3a](https://git.kicad99.com/bys/web/commit/0cc8b3a275119a3003cf60ff98d9ea16e7e7011f))


### Bug Fixes

* **org:** 所属单位的下拉列表按树的排序顺序进行排序 ([7a900b8](https://git.kicad99.com/bys/web/commit/7a900b8827e7d5aa330a304ca7938e45de291912))

### [0.7.29](https://git.kicad99.com/bys/web/compare/v0.7.28...v0.7.29) (2021-02-02)


### Bug Fixes

* **deviceTree:** 修复设备树单位节点排序不正确问题 ([302ad91](https://git.kicad99.com/bys/web/commit/302ad91d8cfd1541419914487502227cf8abd115))
* **deviceTree:** 没有维护权限的用户，树形不显示控制器节点 ([1cee755](https://git.kicad99.com/bys/web/commit/1cee75523575842c3970feb32e2c72bdcf026184))

### [0.7.28](https://git.kicad99.com/bys/web/compare/v0.7.27...v0.7.28) (2021-01-07)


### Bug Fixes

* **crudLog:** 修复操作历史在app下表格头部被遮挡问题 ([1ac42ce](https://git.kicad99.com/bys/web/commit/1ac42ce739ba04b596fe897633e9df4aa7406ab9))

### [0.7.27](https://git.kicad99.com/bys/web/compare/v0.7.26...v0.7.27) (2021-01-05)


### Bug Fixes

* in StoreSyncMethods notify client data changed need throttle by 10 second ([36788a8](https://git.kicad99.com/bys/web/commit/36788a88308cde2292ddea84e90f063f6888319c))
* Sync client user org privilege fancy tree ([1b51318](https://git.kicad99.com/bys/web/commit/1b513189efeca002958ef842b548fe953021e65d))
* User page.ref roleDataEdit rename to userDataEdit ([3963895](https://git.kicad99.com/bys/web/commit/39638951629a9220470b123e3dcd8b581e309c1e))

### [0.7.26](https://git.kicad99.com/bys/web/compare/v0.7.25...v0.7.26) (2021-01-04)


### Bug Fixes

* **menu center:** 只有用户拥有历史记录菜单的权限才可以看到菜单 ([9ee2ab7](https://git.kicad99.com/bys/web/commit/9ee2ab7ccb5f2227651a015a2c5c25fe2a5fe32e))
* update submodule ([7ac45bb](https://git.kicad99.com/bys/web/commit/7ac45bb9ccdcfb4d4b3dd27cca34724185202aa1))

### [0.7.25](https://git.kicad99.com/bys/web/compare/v0.7.24...v0.7.25) (2021-01-04)


### Bug Fixes

* **App:** 修复app无法打开系统文档(pdf)问题 ([7281dda](https://git.kicad99.com/bys/web/commit/7281dda1af6c9b5bd3a440f7a2db6e79364e996e))
* **App:** 修复历史数据表在android@8.1.0平台上表格顶部按钮被屏蔽问题(flex样式兼容) ([25f4474](https://git.kicad99.com/bys/web/commit/25f44745d79f4b2777922e5dcf89ca57e5aea836))
* **Data import:** 修复单位编号没有限制长度规则 ([fe605ca](https://git.kicad99.com/bys/web/commit/fe605ca244cda7766c502f34d2f74ccf71eaf17c))
* **Data import:** 修复长度校验规则，空内容不进行校验 ([255dc17](https://git.kicad99.com/bys/web/commit/255dc173676e1f4b3356a588a2c046a0fad37729))
* **Data import:** 界桩上级控制器通道数和信道号自动修正为可用范围内参数 ([8eb1c19](https://git.kicad99.com/bys/web/commit/8eb1c1916536abe77999d2deae10aa219f1e3e59))
* **Role:** 角色名称在同一单位下不能重复 ([a24fa09](https://git.kicad99.com/bys/web/commit/a24fa09d9227e80ad6f4d4383b1cb328d66e1ebd))

### [0.7.24](https://git.kicad99.com/bys/web/compare/v0.7.23...v0.7.24) (2020-12-31)


### Bug Fixes

* **Data export:** 修复导出的数据出现额外的$index等列数据 ([5cb3a93](https://git.kicad99.com/bys/web/commit/5cb3a9320879f9fb6e7fcff679c0c8d2cc34acc0))
* **Data import:** 修复数据导入时表单检验规则查找不到和检验错误 ([d20f831](https://git.kicad99.com/bys/web/commit/d20f831b93b1d6c50042a626d8a0753be4c17df0))
* **syncLocalStore:** 数据同步，如果没有数据则停止继续同步，并打印错误信息 ([9c38840](https://git.kicad99.com/bys/web/commit/9c3884014f8bbee7fc208f048dca90c725740a6c))

### [0.7.23](https://git.kicad99.com/bys/web/compare/v0.7.22...v0.7.23) (2020-12-31)


### Bug Fixes

* **User:** 修复用户数据的角色标签没有过滤基础角色问题 ([fe16ad6](https://git.kicad99.com/bys/web/commit/fe16ad6c53669fa8f7979a9911eef382f9149afa))

### [0.7.22](https://git.kicad99.com/bys/web/compare/v0.7.21...v0.7.22) (2020-12-30)


### Bug Fixes

* **Android:** 修改编译android app脚本，添加hooks解决gradle不支持"com.android.support:support-v4:27.+"的问题 ([cd89c97](https://git.kicad99.com/bys/web/commit/cd89c97695bf7aab2b7dc61bd0f665e9747ae36e))
* **DataEdit:** 将角色、用户的操作列使用slot传递，在各自的组件中进行逻辑处理 ([889b435](https://git.kicad99.com/bys/web/commit/889b435ef8374836ee2ca75f98454212194e7df0))
* **i18n:** 修复缺失、重复的翻译文本 ([a88f547](https://git.kicad99.com/bys/web/commit/a88f5474d0ed29f1fc1e633cceef6ed5d995010b))
* **Org:** fix parent option duplicate ([d619c09](https://git.kicad99.com/bys/web/commit/d619c093160f743bbbe7cf64e40085b496e60a63))
* **Permission:** 将用户的权限与数据管理按钮关联起来，禁用相关的操作按钮 ([6033b28](https://git.kicad99.com/bys/web/commit/6033b2830a163737eb166e77d64457272b310d96))
* **Proto:** build proto file ([2a17f4c](https://git.kicad99.com/bys/web/commit/2a17f4c8e22886ccb0a0805f4817900d19187c08))
* **Proto:** 重新编译协议，添加查询角色权限API ([77eb3a6](https://git.kicad99.com/bys/web/commit/77eb3a60456389d0d8b06b93b491f6b3a4788b92))
* **Query data:** 修改登录后请求数据逻辑中，添加权限判断，如果没有权限则不请求数据；修改请求角色数据逻辑 ([c3bbea8](https://git.kicad99.com/bys/web/commit/c3bbea896a466cd163cdd3d1440a825ed84f982e))
* **Role:** 修改角色数据及权限操作提示消息 ([03a449e](https://git.kicad99.com/bys/web/commit/03a449e4f6667ddb67cba55430b09f0575d98f79))
* **Role:** 修改角色数据操作列控件禁用逻辑，和显示处理无权限的数据的上级单位名称 ([bc5152a](https://git.kicad99.com/bys/web/commit/bc5152a2d9bb5fe3de8044fdb045322557b6d4e5))
* **Role:** 修改角色数据管理逻辑，角色权限允许查看，没有权限的则禁用操作 ([ef91484](https://git.kicad99.com/bys/web/commit/ef914848152511a6fc799b63cad2ea6ecc8e6870))
* **Role perm:** fix types define error ([44c256b](https://git.kicad99.com/bys/web/commit/44c256b908f4aa37cf6a0235134ec7d1af3044d7))
* **RolePermission:** 修改角色权限禁用与互斥及数据操作同步逻辑 ([ecc48b4](https://git.kicad99.com/bys/web/commit/ecc48b4a0b6f781a4ebc3dc4425a70f82da96f23))
* **UserPermission:** 修改用户权限禁用与互斥及数据操作同步逻辑 ([ecf8608](https://git.kicad99.com/bys/web/commit/ecf8608c4ea4e31929e6183c026f647b47d1c1df))

### [0.7.21](https://git.kicad99.com/bys/web/compare/v0.7.20...v0.7.21) (2020-12-25)


### Bug Fixes

* **ci:**  fix builder image name error ([34f9e64](https://git.kicad99.com/bys/web/commit/34f9e6426be69b945b70fd9229aefffff19a3c43))

### [0.7.20](https://git.kicad99.com/bys/web/compare/v0.7.19...v0.7.20) (2020-12-25)


### Bug Fixes

* **Android:**  fix android hooks pwd err ([e6f1096](https://git.kicad99.com/bys/web/commit/e6f10966b5b364b3801e63cac3af5678a8fc3be3))

### [0.7.19](https://git.kicad99.com/bys/web/compare/v0.7.18...v0.7.19) (2020-12-25)


### Bug Fixes

* **Android:** 修改编译android app脚本，添加hooks解决gradle不支持"com.android.support:support-v4:27.+"的问题 ([d2509a7](https://git.kicad99.com/bys/web/commit/d2509a768903bc331f8c992896e6e02e016f0252))

### [0.7.18](https://git.kicad99.com/bys/web/compare/v0.7.17...v0.7.18) (2020-12-23)


### Bug Fixes

* **DbTab.vue:** 修复PermRow类型错误 ([742e927](https://git.kicad99.com/bys/web/commit/742e9271edd7ded2314cd7c236ffc6702e2725a7))
* **i18n:** 修复缺失、重复的翻译文本 ([b1fb109](https://git.kicad99.com/bys/web/commit/b1fb109ecc847afe05820adf923a8ebd52d774d3))

### [0.7.17](https://git.kicad99.com/bys/web/compare/v0.7.16...v0.7.17) (2020-12-17)


### Bug Fixes

* cmdtest add controller state panel ([68c1f6d](https://git.kicad99.com/bys/web/commit/68c1f6d8d0ea1fc840cbb4d2970fb0f3bb3ecbb0))

### [0.7.16](https://git.kicad99.com/bys/web/compare/v0.7.15...v0.7.16) (2020-12-14)


### Bug Fixes

* **Marker:** 修复界桩历史指定控制器条件查询时，条件参数错误导致查询失败问题 ([f8999c7](https://git.kicad99.com/bys/web/commit/f8999c734baf9c0666631398c03266c78363853f))
* **Store:** 修改数据管理类的数据类型，使用泛型来定义，并添加getDataList方法 ([1c0482a](https://git.kicad99.com/bys/web/commit/1c0482a9bbf34a8648e23351ee6d1fb9bca86031))

### [0.7.15](https://git.kicad99.com/bys/web/compare/v0.7.14...v0.7.15) (2020-12-11)


### Bug Fixes

* **android:** modify android platform version ([03b755f](https://git.kicad99.com/bys/web/commit/03b755ff1cbcf575ba58f49a182885b1d7f0cffe))

### [0.7.14](https://git.kicad99.com/bys/web/compare/v0.7.13...v0.7.14) (2020-12-10)


### Bug Fixes

* **android app:** 取消cordova hooks ([3f54cc4](https://git.kicad99.com/bys/web/commit/3f54cc42f612164f6e3e7526ecace75db5028068))
* **ci:** cancel release version generate ([806e12e](https://git.kicad99.com/bys/web/commit/806e12e14ac50e0ef391dfede34dd889b652052e))
* **ci:** disable gradle daemon ([4c77bcf](https://git.kicad99.com/bys/web/commit/4c77bcf428a79e00e61233aacc1c4affc7567fa6))

### [0.7.13](https://git.kicad99.com/bys/web/compare/v0.7.12...v0.7.13) (2020-12-08)


### Bug Fixes

* controller set channel no 调整链路层 ([de700ec](https://git.kicad99.com/bys/web/commit/de700ec9395273a5547043a3315bf3fdd04fa22a))
* 指令测试界面增加控制器心条说明 ([7b2d191](https://git.kicad99.com/bys/web/commit/7b2d191c3398522c4865f4cafc5deb4a56161277))

### [0.7.12](https://git.kicad99.com/bys/web/compare/v0.7.11...v0.7.12) (2020-12-07)


### Bug Fixes

* **Controller:** 修复控制器下线指令日志中，上线时间参数处理不正确的问题 ([7cf1f32](https://git.kicad99.com/bys/web/commit/7cf1f32856c4e20eb25ebb726f8403172e6c2b2e))

### [0.7.11](https://git.kicad99.com/bys/web/compare/v0.7.10...v0.7.11) (2020-12-04)


### Bug Fixes

* **BysMarker:** 修改排队号和打卡间隔参数范围 ([37408ff](https://git.kicad99.com/bys/web/commit/37408ff285cc72a5ab31913900932cbdd06b5a4d))

### [0.7.10](https://git.kicad99.com/bys/web/compare/v0.7.9...v0.7.10) (2020-12-03)


### Bug Fixes

* **BysMarker:** 修复界桩popup弹框的解除报警按钮显示不正确问题，D1指令中也会包含报警状态 ([91f0f7f](https://git.kicad99.com/bys/web/commit/91f0f7f574722065cceba5c7221dbd1cf6424800))

### [0.7.9](https://git.kicad99.com/bys/web/compare/v0.7.8...v0.7.9) (2020-12-01)


### Bug Fixes

* **BysMarker:** 修改界桩报警发射间隔为12~255s ([f45a380](https://git.kicad99.com/bys/web/commit/f45a38092a56f593ae195aa7e4e5898de6568653))
* **BysMarker:** 报警中更新参数后，界桩上传的d1指令状态中还是报警状态，需要过滤掉 ([bd4b358](https://git.kicad99.com/bys/web/commit/bd4b358822b1b0cf60e4ee06cf19170bb89b4f3a))

### [0.7.8](https://git.kicad99.com/bys/web/compare/v0.7.7...v0.7.8) (2020-11-27)


### Bug Fixes

* **BysMarker:** 修复界桩参数更新时间判断api使用错误，isAfter->isSameOrAfter ([ad19f58](https://git.kicad99.com/bys/web/commit/ad19f581c1ebc9be6143448c115f6cc4903f768a))

### [0.7.7](https://git.kicad99.com/bys/web/compare/v0.7.6...v0.7.7) (2020-11-21)


### Bug Fixes

* **BysMarker:** 修复界桩被遥毙参数更新时，没有下发D0指令错误 ([e499d88](https://git.kicad99.com/bys/web/commit/e499d8857ef21b956d7384464a42de9a50c1771e))
* **Controller:** 修复控制器下线通知缓存状态参数错误，导致异常控制器统计错误 ([30703e0](https://git.kicad99.com/bys/web/commit/30703e043fea047193ee18fdda2b23e2a21c3d49))

### [0.7.6](https://git.kicad99.com/bys/web/compare/v0.7.5...v0.7.6) (2020-11-18)


### Bug Fixes

* **controller:** 修复控制器统计异常的属性没有正确赋值，导致控制器异常统计无法响应错误 ([06d02dc](https://git.kicad99.com/bys/web/commit/06d02dc2bf6291e82d2b8647dc9cb6b911eb2f71))
* **controller abnormal:** 调整异常控制器表格列顺序 ([b46dc87](https://git.kicad99.com/bys/web/commit/b46dc870ea913dd34204869b4a0a3f4a8729558f))
* **lngLat:** 统一经纬度格式化处理，最大保留7位小数 ([1998836](https://git.kicad99.com/bys/web/commit/199883651e5c4a2d513c2ff2667bee7b84fc75da))

### [0.7.5](https://git.kicad99.com/bys/web/compare/v0.7.4...v0.7.5) (2020-11-16)


### Bug Fixes

* **data editor:** 修复数据编辑的对象没有原型链上的默认零值问题，表单元素上不处理原型链上的属性 ([3c13ac3](https://git.kicad99.com/bys/web/commit/3c13ac338cb23f10de2f3821ec710f0f2cf856e5))
* **org:** 修复上级单位下拉列表二次打开时过滤错误 ([5a50a22](https://git.kicad99.com/bys/web/commit/5a50a22a67e3eca7c7cdee8d6c5590106cf50e0c))
* **sint32 limit:** 修复sint32类型最大值限制错误，客户端限制为0xFFFFFFF ([1c75e8f](https://git.kicad99.com/bys/web/commit/1c75e8fe56aa81cbba513aa5402dc7aa621782c0))
* **sint32 limit:** 修改解析界桩状态公共函数逻辑，添加0xD3指令的处理 ([84907df](https://git.kicad99.com/bys/web/commit/84907dfcb694249edb4d810ddd82fc0a8df9c09c))

### [0.7.4](https://git.kicad99.com/bys/web/compare/v0.7.3...v0.7.4) (2020-11-11)


### Bug Fixes

* **reLogin:** 修复重新登录后没有重新订阅nats事件错误，导致无法接收控制器指令等问题 ([65a56c9](https://git.kicad99.com/bys/web/commit/65a56c91be1f9d32ae00295ad18de3fdcc24e033))

### [0.7.3](https://git.kicad99.com/bys/web/compare/v0.7.2...v0.7.3) (2020-11-03)


### Bug Fixes

* **cordova:** add 'gradle --stop' cmd ([d790162](https://git.kicad99.com/bys/web/commit/d790162aa47510bf01d4c61beab2f1e8adfbc749))

### [0.7.2](https://git.kicad99.com/bys/web/compare/v0.7.1...v0.7.2) (2020-11-03)


### Bug Fixes

* **controller:** 修复控制器心跳指令丢失原有状态数据问题 ([da0f622](https://git.kicad99.com/bys/web/commit/da0f622bc03412974ea9faa6fe747a311f17f720))

### [0.7.1](https://git.kicad99.com/bys/web/compare/v0.7.0...v0.7.1) (2020-11-03)


### Bug Fixes

* delay controller check err time ([174d194](https://git.kicad99.com/bys/web/commit/174d1943001454d666d62830db0c4d26d394ee28))
* delay controller check err time ([8410424](https://git.kicad99.com/bys/web/commit/841042481c7f30cdc8d047a69e8a2e54d4c60813))
* **abnormal controller:** 修改控制器异常原因分隔符 ([cdb4853](https://git.kicad99.com/bys/web/commit/cdb4853188e8c5515f0567493ce44b8e72e3d9c5))
* 控制器异常字段拼接方法 ([934db9c](https://git.kicad99.com/bys/web/commit/934db9cda78f1714d10468953b6a8367a0e3090b))

## [0.7.0](https://git.kicad99.com/bys/web/compare/v0.6.0...v0.7.0) (2020-10-30)


### Features

* **ts:** use custom bysdb tyeps for bysMarker and controller ([e9b1f5d](https://git.kicad99.com/bys/web/commit/e9b1f5d2a81f1765ddd0c8b05dfc50cae2ed8d58))


### Bug Fixes

* **cmd:** fix bysMarker calcPower argv(batteryPower) err ([ef643dd](https://git.kicad99.com/bys/web/commit/ef643ddebc65287fb6a98696a37fb943ed5b45a4))
* **map:** cancel 'delete' keyword ([bd06722](https://git.kicad99.com/bys/web/commit/bd067223207c024103d11eed0fb87e2cee517c23))
* **map:** 界桩报警图层判断界桩的位置逻辑，删除对倾倒标记位的判断 ([7c29047](https://git.kicad99.com/bys/web/commit/7c29047dda780a90c539fb880ed44c46ce5210d1))
* **package:** delete patch and minor script ([312f92a](https://git.kicad99.com/bys/web/commit/312f92a28c46ae8e017bb3a61c5b07cd72f5afa9))
* **ts:** add custom bysdb types ([9409048](https://git.kicad99.com/bys/web/commit/9409048809d078fea67fa6d3497623dff62564c5))

## [0.6.0](https://git.kicad99.com/bys/web/compare/v0.5.1...v0.6.0) (2020-10-30)


### Features

* **sync client store data:** 同步用户客户端数据，当数据库修改同步修改用户界面数据。同步设置某个用户的系统配置 ([92d3ab3](https://git.kicad99.com/bys/web/commit/92d3ab3fbeaaf96f9e16e13bdd499c64fe29a0f3))

### [0.5.1](https://git.kicad99.com/bys/web/compare/v0.5.0...v0.5.1) (2020-10-30)


### Bug Fixes

* **jsyrpc:** fix not auto reconnect socket err ([3c46678](https://git.kicad99.com/bys/web/commit/3c466781c9e38fb474158ad2160b8249a26ca4d9))
* **jsyrpc:** update jsyrpc ([d632c1d](https://git.kicad99.com/bys/web/commit/d632c1d34504e06c44f18a1f909c6498eafec0e3))

### [0.4.1](https://git.kicad99.com/bys/web/compare/v0.4.0...v0.4.1) (2020-10-27)


### Bug Fixes

* **ci:** fix deploy rules ([41c81bc](https://git.kicad99.com/bys/web/commit/41c81bc7c9cc6ac8adb3fc649aa78c8f720ff591))

## [0.4.0](https://git.kicad99.com/bys/web/compare/v0.3.7...v0.4.0) (2020-10-27)


### Features

* **ci:** add semantic-release, auto release ([7069213](https://git.kicad99.com/bys/web/commit/706921394613da0b70d461322bbc2c2d6a517a11))


### Bug Fixes

* **ci:** add missing release config and files ([f69f41f](https://git.kicad99.com/bys/web/commit/f69f41f74389c87c96fc90ae5845d2c0b28a7c54))
* **ci:** add semantic-release introduction ([7729996](https://git.kicad99.com/bys/web/commit/77299963e2ea521e04fb494f7aea1c04bcb12a54))
